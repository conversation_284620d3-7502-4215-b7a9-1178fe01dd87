<?xml version="1.0" encoding="UTF-8"?>
<serviceCatalog
        xmlns="http://service.sankuai.com/1.0.0"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:schemaLocation="http://service.sankuai.com/1.0.0
            http://pixel.sankuai.com/repository/releases/com/meituan/apidoc/servicecatalog/1.0.0/servicecatalog-1.0.0.xsd">

    <!--
        1. 每个项目必须有一个 SERVICE.DESCRIPTION.xml 文件，并且放在代码仓库的根目录下面。
        2. 该文件会有两部分组成，<serviceDescs> 用于描述 service 和 interface 之间的关联关系，是必须要填写的部分；
           <services> 用于不适合在代码中编写文档的场景，可以通过 <services> 编写一个完整的接口文档，本文件不展示这种
           用法，只阐述可以在代码中进行文档编写的场景。
        3. 具体的 SERVICE.DESCRIPTION.xml 文件格式可以参考：https://km.sankuai.com/page/59144013
    -->


    <!--
        <serviceDescs> 标签用于描述 service 和 interface 的关联关系，即一个 service 包含了哪些 interface。
    -->
    <serviceDescs>



        <!--
            该示例描述的是使用 Restful 服务框架的情况下如何进行文档编写。该类型的编写方式和普通 java 的一样，都是通过注解的方式在
            代码中进行相关文档的描述。接入文档可以参考：https://km.sankuai.com/page/60715770
            可以将 <serviceDesc> 中 name, description, scenarios 等信息以 @ServiceDoc 的注解在代码中标注，也可以直接
            按下面这种方式进行编写。
        -->
        <serviceDesc>
            <appkey>mapi-pay-promo-web</appkey>
            <name>mapi-pay-promo-web服务文档</name>
            <description>mapi-pay-promo-web服务文档</description>
            <scenarios>优惠券-领券组件</scenarios>
            <interfaceDescs>
                <interfaceDesc>
                    <type>restful</type>
                    <class>com.dianping.api.picasso.controller.UnifiedIssueCouponAction</class>
                </interfaceDesc>
            </interfaceDescs>
        </serviceDesc>

        <serviceDesc>
            <name>beautyissuecouponcomponent.bin</name>
            <description>mapi-shell框架的丽人领券组件展示接口</description>
            <appkey>mapi-pay-promo-web</appkey>
            <scenarios>mapi-shell框架的丽人领券组件展示接口</scenarios>
            <interfaceDescs>
                <interfaceDesc>
                    <type>restful</type>
                    <class>com.dianping.api.picasso.controller.BeautyIssueCouponPicassoAction</class>
                </interfaceDesc>
            </interfaceDescs>
        </serviceDesc>
        <serviceDesc>
            <name>issuecoupon.bin</name>
            <description>mapi-shell框架的丽人领券组件领券接口</description>
            <appkey>mapi-pay-promo-web</appkey>
            <scenarios>mapi-shell框架的丽人领券组件领券接口</scenarios>
            <interfaceDescs>
                <interfaceDesc>
                    <type>restful</type>
                    <class>com.dianping.api.picasso.controller.IssueCouponPicassoAction</class>
                </interfaceDesc>
            </interfaceDescs>
        </serviceDesc>
        <serviceDesc>
            <name>unifiedissuecouponcomponent.bin</name>
            <description>统一领券组件</description>
            <appkey>mapi-pay-promo-web</appkey>
            <scenarios>统一领券组件</scenarios>
            <interfaceDescs>
                <interfaceDesc>
                    <type>restful</type>
                    <class>com.dianping.api.picasso.controller.UnifiedIssueCouponComponentAction</class>
                </interfaceDesc>
            </interfaceDescs>
        </serviceDesc>


    </serviceDescs>

</serviceCatalog>