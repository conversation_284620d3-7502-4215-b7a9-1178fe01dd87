# 国补功能实现方案设计

## 1. 需求分析

### 1.1 业务目标
在商详页（包括老poi页和一致性poi页）的领券栏和领券浮层中实现国补（国家补贴）功能，为用户提供国家补贴资格的展示和领取入口。

### 1.2 功能要求
- **触发条件**：商详页必须有国补门店标识，对所有用户展示国补氛围，无需校验GPS定位
- **领券栏展示**：根据用户国补资格状态展示不同的标签和文案
- **领券浮层展示**：提供详细的国补信息卡片和交互按钮
- **优先级控制**：国补标签位次为最高优先级（国补 > 神券包 > 神券 > 其他优惠券）

### 1.3 技术约束
- 基于现有 `IssueCouponComponentAction#execute` 接口架构
- 保持与现有领券栏和浮层功能的兼容性
- 遵循现有的处理器链模式和数据流转机制

## 2. 技术实现方案

### 2.1 整体架构设计

基于现有的处理器链架构，新增国补处理器 `NationalSubsidyShopProcessor`，集成到现有的优惠券查询流程中。

```mermaid
graph TD
    A[IssueCouponComponentAction] --> B[参数校验]
    B --> C[用户身份识别]
    C --> D[券查询处理模块]
    D --> E[国补处理器]
    E --> F[数据转换组装]
    F --> G[响应返回]
    
    E --> E1[ZDC门店标识查询]
    E --> E2[用户国补资格查询]
    E --> E3[补贴比例查询]
    
    F --> F1[领券栏标签组装]
    F --> F2[浮层卡片组装]
```

### 2.2 核心组件设计

#### 2.2.1 新增处理器：NationalSubsidyShopProcessor

**职责：**
- 查询门店是否具有国补标识
- 获取用户国补资格状态
- 查询各类目的补贴比例信息

**关键逻辑：**
```java
@Component("nationalSubsidyShopProcessor")
public class NationalSubsidyShopProcessor extends AbstractPromoProcessor {
    
    @Override
    public void prePare(CouponActivityContext promoCtx, IMobileContext iMobileContext) {
        // 1. 校验基础条件（用户登录、门店ID、城市ID等）
        // 2. 异步查询国补信息
        Future<NationalSubsidyInfoDO> future = ExecutorService.submit(() -> {
            return queryNationalSubsidyInfo(promoCtx);
        });
        promoCtx.setNationalSubsidyInfoDOFuture(future);
    }
    
    private NationalSubsidyInfoDO queryNationalSubsidyInfo(CouponActivityContext promoCtx) {
        // 1. 调用ZDC查询门店国补标识
        // 2. 调用闪购服务查询用户资格状态
        // 3. 查询各类目补贴比例（Lion配置或供应链接口）
        // 4. 组装返回结果
    }
}
```

#### 2.2.2 数据模型设计

**NationalSubsidyInfoDO（内部数据传输对象）：**
```java
@Data
public class NationalSubsidyInfoDO implements Serializable {
    // 用户国补资格信息映射：类目ID -> 资格状态（0未领取，1已领取）
    private Map<Integer, Integer> userQualMap;
    
    // 闪购类目到补贴比例映射：类目ID -> 补贴比例
    private Map<Integer, Integer> category2promoMap;
}
```

**NationalSubsidyInfo（前端展示对象）：**
```java
@MobileDo(id = 0xb934)
@Data
public class NationalSubsidyInfo implements Serializable {
    private String jumpUrl;        // 按钮跳链
    private String buttonPic;      // 按钮图片
    private String promo;          // 国补优惠比例
    private String subTitle;       // 副标题
    private String title;          // 标题
    private String icon;           // 角标
    private int issueStatus;       // 领取状态 0未领取，1已领取
}
```

### 2.3 数据流程设计

```mermaid
sequenceDiagram
    participant Client as 客户端
    participant Action as IssueCouponComponentAction
    participant Processor as NationalSubsidyShopProcessor
    participant ZDC as ZDC门店标识服务
    participant Shangou as 闪购资格服务
    participant Supply as 供应链服务
    participant Biz as UnifiedIssueCouponBiz
    
    Client->>Action: 请求券信息(neednationalsubsidy=1)
    Action->>Processor: 执行国补处理器
    
    par 并行查询
        Processor->>ZDC: 查询门店国补标识
        ZDC-->>Processor: 返回标识结果
    and
        Processor->>Shangou: 查询用户资格状态
        Shangou-->>Processor: 返回资格信息
    and
        Processor->>Supply: 查询补贴比例
        Supply-->>Processor: 返回比例信息
    end
    
    Processor-->>Action: 返回国补信息
    Action->>Biz: 数据转换组装
    Biz-->>Action: 组装后的响应
    Action-->>Client: 返回完整券信息
```

### 2.4 关键实现点

#### 2.4.1 处理器链集成

在 `CouponIssueActivityQueryService` 中添加国补处理器：

```java
private List<AbstractPromoProcessor> getChainForPromoProcessors() {
    return Lists.newArrayList(
        // ... 现有处理器
        nationalSubsidyShopProcessor  // 新增国补处理器
    );
}
```

#### 2.4.2 请求参数扩展

在 `IssuecouponcomponentRequest` 中新增参数：

```java
/**
 * 是否需要国家补贴，1查，0&其他不查
 */
@MobileRequest.Param(name = "neednationalsubsidy")
private Integer neednationalsubsidy;
```

#### 2.4.3 上下文对象扩展

在 `CouponActivityContext` 中添加国补相关字段：

```java
// 是否返回国家补贴
private boolean needNationalSubsidy;

// 异步查询Future
private Future<NationalSubsidyInfoDO> nationalSubsidyInfoDOFuture;

// 查询结果
private NationalSubsidyInfoDO nationalSubsidyInfoDO;
```

#### 2.4.4 业务组装逻辑

在 `UnifiedIssueCouponBiz` 中实现国补信息的组装：

**领券栏标签组装：**
```java
// 国补标签优先级最高，插入到列表首位
if (Objects.nonNull(nationalSubsidyInfoDO)) {
    maxCount++;
    couponSimpleListResult.add(0, buildNationalSubsidyQualTag(isNationalSubsidyQualBound, nationalSubsidyMaxPromo));
}
```

**浮层卡片组装：**
```java
// 拼装国补资格浮层卡片信息
if (Objects.nonNull(nationalSubsidyInfoDO)) {
    NationalSubsidyInfo nationalSubsidyInfo = buildNationalSubsidyInfo(couponActivityContext.isMt(), isNationalSubsidyQualBound, nationalSubsidyMaxPromo);
    couponListInfo.setNationalSubsidyInfo(Lists.newArrayList(nationalSubsidyInfo));
}
```

### 2.5 配置管理

通过Lion配置管理各种参数：

```java
// 是否支持点评侧查询
NATIONAL_SUBSIDY_IS_SUPPORT_DP = "mapi-pay-promo-web.nationalSubsidy.isSupportDp"

// 查询超时时间
QUERY_NATIONAL_SUBSIDY_INFO_TIMEOUT = "mapi-pay-promo-web.queryNationalSubsidyInfo.timeout"

// 补贴比例映射
NATIONAL_SUBSIDY_SHANGOU_PRODUCT_CAT_2_PROMO_MAP = "mapi-pay-promo-web.nationalSubsidy.shangouProductCat2PromoMap"

// 跳转链接配置
NATIONAL_SUBSIDY_JUMP_URL_MAP = "mapi-pay-promo-web.nationalSubsidy.jumpUrlMap"

// 卡片文案配置
NATIONAL_SUBSIDY_CARD_SUBTITLE = "mapi-pay-promo-web.nationalSubsidy.cardSubtitle"
NATIONAL_SUBSIDY_CARD_BUTTON_PIC_MAP = "mapi-pay-promo-web.nationalSubsidy.cardButtonPicMap"
```

## 3. 代码改动点

### 3.1 新增文件

1. **NationalSubsidyShopProcessor.java** - 国补处理器
2. **NationalSubsidyInfoDO.java** - 内部数据传输对象
3. **NationalSubsidyInfo.java** - 前端展示对象
4. **NationalSubsidyShopWrapper.java** - 外部服务调用封装

### 3.2 修改文件

1. **IssuecouponcomponentRequest.java** - 添加neednationalsubsidy参数
2. **CouponActivityContext.java** - 添加国补相关字段
3. **CouponIssueActivityQueryService.java** - 集成国补处理器
4. **UnifiedIssueCouponBiz.java** - 实现国补信息组装逻辑
5. **IssueCouponComponentDetail.java** - 添加nationalSubsidyInfo字段
6. **LionConstants.java** - 添加配置常量
7. **PromotionRequestContext.java** - 添加needNationalSubsidy字段映射
8. **IssueActivityMapper.java** - 添加参数映射逻辑

### 3.3 配置文件修改

1. **appcontext-service-remote.xml** - 添加外部服务依赖配置

## 4. 风险评估与问题识别

### 4.1 技术风险

#### 4.1.1 性能风险
- **问题**：新增多个外部服务调用（ZDC、闪购、供应链），可能影响接口响应时间
- **解决方案**：
  - 采用异步并行查询，设置合理的超时时间（500ms）
  - 实现降级机制，外部服务异常时不影响其他券信息展示
  - 添加熔断和限流保护

#### 4.1.2 兼容性风险
- **问题**：新增字段可能影响现有客户端解析
- **解决方案**：
  - 使用MobileDo注解确保向后兼容
  - 新增字段设置为可选，不影响现有逻辑
  - 通过neednationalsubsidy参数控制功能开关

### 4.2 业务风险

#### 4.2.1 数据一致性风险
- **问题**：多个数据源（ZDC、闪购、供应链）可能存在数据不一致
- **解决方案**：
  - 实现数据校验逻辑，异常数据时使用默认值
  - 添加详细的日志记录，便于问题排查
  - 设置监控告警，及时发现数据异常

#### 4.2.2 用户体验风险
- **问题**：国补信息查询失败可能导致用户看不到相关信息
- **解决方案**：
  - 实现优雅降级，查询失败时不展示国补信息但不影响其他券
  - 设置重试机制，提高成功率
  - 添加用户友好的错误提示

### 4.3 运维风险

#### 4.3.1 配置管理风险
- **问题**：多个Lion配置项，配置错误可能导致功能异常
- **解决方案**：
  - 设置合理的默认值
  - 实现配置校验逻辑
  - 建立配置变更审核流程

#### 4.3.2 监控告警风险
- **问题**：新增功能缺乏有效监控，问题发现不及时
- **解决方案**：
  - 添加关键指标监控（成功率、响应时间、异常率）
  - 设置分级告警机制
  - 建立问题处理SOP

## 5. 实施建议

### 5.1 分阶段实施

**第一阶段：基础功能开发**
- 实现NationalSubsidyShopProcessor处理器
- 完成数据模型定义
- 集成到现有处理器链

**第二阶段：业务逻辑完善**
- 实现领券栏和浮层的展示逻辑
- 完善异常处理和降级机制
- 添加配置管理

**第三阶段：测试和优化**
- 完善单元测试和集成测试
- 性能测试和优化
- 监控告警配置

### 5.2 测试策略

1. **单元测试**：覆盖核心业务逻辑
2. **集成测试**：验证与外部服务的交互
3. **性能测试**：验证接口响应时间
4. **兼容性测试**：确保不影响现有功能

### 5.3 上线策略

1. **灰度发布**：先在部分城市或用户群体试点
2. **监控观察**：密切关注关键指标
3. **逐步放量**：根据监控结果逐步扩大范围
4. **应急预案**：准备快速回滚方案

## 6. 总结

本方案基于现有的IssueCouponComponentAction接口架构，通过新增NationalSubsidyShopProcessor处理器的方式实现国补功能，保持了与现有系统的良好兼容性。方案采用异步并行查询提高性能，通过配置管理提供灵活性，并考虑了各种风险和应对措施。

实施过程中需要重点关注性能优化、异常处理和监控告警，确保功能稳定可靠地为用户提供国补服务。
