# 国补功能实现方案设计

## 1. 需求分析

### 1.1 业务目标
在商详页（包括老poi页和一致性poi页）的领券栏和领券浮层中实现国补（国家补贴）功能，为用户提供国家补贴资格的展示和领取入口。

### 1.2 功能要求详解

#### 1.2.1 触发条件
- **门店标识要求**：商详页必须有国补门店标识（通过ZDC接口查询，标签值ID：21857）
- **用户范围**：对所有用户展示国补氛围，无需校验GPS定位
- **展位配置**：使用 `MT_MERCHANT_DETAILS_BANNER` 标识查询门店国补标签

#### 1.2.2 领券栏展示要求
- **展示逻辑**：根据不同类目对应的补贴比例展示15%或20%的国补标签
- **优先级**：国补 > 神券包 > 神券 > 其他优惠券（最高优先级）
- **文案规则**：
  - 未完全领取：【国补 | 最高补贴xx% | 领取】（xx=未领取类目的最高补贴比例）
  - 已全部领取：【国补 | 最高补贴xx% | 已领】（xx=已领取类目的最高补贴比例）
- **交互行为**：点击唤起领券浮层，需要按压动效承接

#### 1.2.3 领券浮层展示要求
- **UI元素**：
  - icon：【国家补贴】
  - 优惠比例：【15%】或【20%】
  - 注释：【最高补贴】
  - 主标题：【国家补贴】
  - 副标题：【领后使用 美团支付、云闪付可享】
- **按钮逻辑**：
  - 有未领取类目或闪购接口失效：【立即领取】
  - 全部类目已领取：【已领取】
  - 所有按钮均跳转至国补到店分会场

### 1.3 技术约束
- 基于现有 `IssueCouponComponentAction#execute` 接口架构
- 保持与现有领券栏和浮层功能的兼容性
- 遵循现有的处理器链模式和数据流转机制
- 需要集成ZDC门店标识接口和闪购用户资格接口

## 2. 技术实现方案

### 2.1 整体架构设计

基于现有的处理器链架构，新增国补处理器 `NationalSubsidyShopProcessor`，集成到现有的优惠券查询流程中。

```mermaid
graph TD
    A[IssueCouponComponentAction] --> B[参数校验]
    B --> C[用户身份识别]
    C --> D[券查询处理模块]
    D --> E[国补处理器]
    E --> F[数据转换组装]
    F --> G[响应返回]
    
    E --> E1[ZDC门店标识查询]
    E --> E2[闪购用户资格查询]
    E --> E3[补贴比例查询]
    
    F --> F1[领券栏标签组装]
    F --> F2[浮层卡片组装]
```

### 2.2 核心组件设计

#### 2.2.1 新增处理器：NationalSubsidyShopProcessor

**职责：**
- 查询门店是否具有国补标识（ZDC接口，标签ID：21857）
- 获取用户国补资格状态（闪购接口）
- 查询各类目的补贴比例信息（Lion配置或供应链接口）

**关键实现逻辑：**
```java
@Component("nationalSubsidyShopProcessor")
public class NationalSubsidyShopProcessor extends AbstractPromoProcessor {
    
    private static final long NATIONAL_SUBSIDY_TAG_ID = 21857;
    
    @Override
    public void prePare(CouponActivityContext promoCtx, IMobileContext iMobileContext) {
        // 1. 校验基础条件（用户登录、门店ID、城市ID等）
        // 2. 异步查询国补信息
        Future<NationalSubsidyInfoDO> future = ExecutorService.submit(() -> {
            return queryNationalSubsidyInfo(promoCtx);
        });
        promoCtx.setNationalSubsidyInfoDOFuture(future);
    }
    
    private NationalSubsidyInfoDO queryNationalSubsidyInfo(CouponActivityContext promoCtx) {
        // 1. 调用ZDC查询门店国补标识（MT_MERCHANT_DETAILS_BANNER + 标签ID 21857）
        // 2. 调用闪购服务查询用户资格状态
        // 3. 查询各类目补贴比例（Lion配置或供应链接口）
        // 4. 组装返回结果
    }
}
```

#### 2.2.2 数据模型设计

**NationalSubsidyInfoDO（内部数据传输对象）：**
```java
@Data
public class NationalSubsidyInfoDO implements Serializable {
    // 用户国补资格信息映射：类目ID -> 资格状态（0未领取，1已领取）
    private Map<Integer, Integer> userQualMap;
    
    // 闪购类目到补贴比例映射：类目ID -> 补贴比例
    private Map<Integer, Integer> category2promoMap;
}
```

**NationalSubsidyInfo（前端展示对象）：**
```java
@MobileDo(id = 0xb934)
@Data
public class NationalSubsidyInfo implements Serializable {
    private String jumpUrl;        // 按钮跳链（国补到店分会场）
    private String buttonPic;      // 按钮图片
    private String promo;          // 国补优惠比例（15%或20%）
    private String subTitle;       // 副标题（领后使用 美团支付、云闪付可享）
    private String title;          // 标题（国家补贴）
    private String icon;           // 角标（国家补贴图标）
    private int issueStatus;       // 领取状态 0未领取，1已领取
}
```

### 2.3 数据流程设计

```mermaid
sequenceDiagram
    participant Client as 客户端
    participant Action as IssueCouponComponentAction
    participant Processor as NationalSubsidyShopProcessor
    participant ZDC as ZDC门店标识服务
    participant Shangou as 闪购资格服务
    participant Supply as 供应链服务
    participant Biz as UnifiedIssueCouponBiz
    
    Client->>Action: 请求券信息(neednationalsubsidy=1)
    Action->>Processor: 执行国补处理器
    
    par 并行查询
        Processor->>ZDC: 查询门店国补标识(MT_MERCHANT_DETAILS_BANNER, 标签ID:21857)
        ZDC-->>Processor: 返回标识结果
    and
        Processor->>Shangou: 查询用户资格状态
        Shangou-->>Processor: 返回资格信息
    and
        Processor->>Supply: 查询补贴比例
        Supply-->>Processor: 返回比例信息
    end
    
    Processor-->>Action: 返回国补信息
    Action->>Biz: 数据转换组装
    
    Note over Biz: 领券栏：国补标签最高优先级
    Note over Biz: 浮层：国补卡片信息
    
    Biz-->>Action: 组装后的响应
    Action-->>Client: 返回完整券信息
```
