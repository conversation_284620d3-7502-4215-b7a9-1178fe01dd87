package com.dianping.pay.api.service.impl;

import com.dianping.cat.Cat;
import com.dianping.pay.api.entity.issuecoupon.GovernmentSubsidyInfo;
import com.dianping.pay.api.entity.issuecoupon.GovernmentSubsidyModule;
import com.dianping.pay.api.entity.issuecoupon.GovernmentSubsidyTag;
import com.dianping.pay.api.service.ZdcShopIdentityService;
import com.dianping.pay.api.util.LionConstants;
import com.sankuai.meituan.lion.client.Lion;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * ZDC门店标识服务实现
 * <AUTHOR>
 */
@Service
@Slf4j
public class ZdcShopIdentityServiceImpl implements ZdcShopIdentityService {

    private static final String GOVERNMENT_SUBSIDY_SWITCH = "mapi-pay-promo-web.government.subsidy.switch";
    private static final String GOVERNMENT_SUBSIDY_JUMP_URL = "mapi-pay-promo-web.government.subsidy.jump.url";
    private static final String DEFAULT_JUMP_URL = "https://i.meituan.com/awp/hfe/block/dd8b8c9b/index.html";

    @Override
    public boolean hasGovernmentSubsidyIdentity(Long shopId, boolean isMt) {
        try {
            // 检查开关
            if (!Lion.getBooleanValue(GOVERNMENT_SUBSIDY_SWITCH, false)) {
                return false;
            }

            // TODO: 调用ZDC门店标识接口
            // 这里需要根据实际的ZDC接口文档实现
            // 示例：调用ZDC接口查询门店是否有国补标识
            return callZdcApi(shopId, isMt);

        } catch (Exception e) {
            log.error("查询门店国补标识失败, shopId: {}, isMt: {}", shopId, isMt, e);
            Cat.logError("ZdcShopIdentityService.hasGovernmentSubsidyIdentity", e);
            return false;
        }
    }

    @Override
    public GovernmentSubsidyInfo getGovernmentSubsidyInfo(Long shopId, Long userId, boolean isMt) {
        try {
            if (!hasGovernmentSubsidyIdentity(shopId, isMt)) {
                return null;
            }

            GovernmentSubsidyInfo subsidyInfo = new GovernmentSubsidyInfo();
            subsidyInfo.setShowGovernmentSubsidy(true);

            // 构建标签信息
            GovernmentSubsidyTag subsidyTag = buildSubsidyTag(shopId, userId, isMt);
            subsidyInfo.setSubsidyTag(subsidyTag);

            // 构建浮层模块信息
            GovernmentSubsidyModule subsidyModule = buildSubsidyModule(shopId, userId, isMt);
            subsidyInfo.setSubsidyModule(subsidyModule);

            return subsidyInfo;

        } catch (Exception e) {
            log.error("获取门店国补信息失败, shopId: {}, userId: {}, isMt: {}", shopId, userId, isMt, e);
            Cat.logError("ZdcShopIdentityService.getGovernmentSubsidyInfo", e);
            return null;
        }
    }

    /**
     * 调用ZDC接口查询门店标识
     */
    private boolean callZdcApi(Long shopId, boolean isMt) {
        // TODO: 实现ZDC接口调用
        // 根据ZDC门店标识接口文档实现具体调用逻辑
        // 这里先返回模拟数据
        return shopId != null && shopId > 0;
    }

    /**
     * 构建国补标签信息
     */
    private GovernmentSubsidyTag buildSubsidyTag(Long shopId, Long userId, boolean isMt) {
        GovernmentSubsidyTag tag = new GovernmentSubsidyTag();
        
        // 查询用户国补资格状态
        SubsidyQualificationInfo qualificationInfo = queryUserSubsidyQualification(shopId, userId, isMt);
        
        if (qualificationInfo.hasUnclaimedCategory()) {
            // 有未领取的类目
            tag.setTagText(String.format("国补 | 最高补贴%s | 领取", qualificationInfo.getMaxUnclaimedRate()));
            tag.setTagStatus(0); // 可领取
            tag.setSubsidyRate(qualificationInfo.getMaxUnclaimedRate());
        } else {
            // 所有类目都已领取
            tag.setTagText(String.format("国补 | 最高补贴%s | 已领", qualificationInfo.getMaxClaimedRate()));
            tag.setTagStatus(1); // 已领取
            tag.setSubsidyRate(qualificationInfo.getMaxClaimedRate());
        }
        
        return tag;
    }

    /**
     * 构建国补浮层模块信息
     */
    private GovernmentSubsidyModule buildSubsidyModule(Long shopId, Long userId, boolean isMt) {
        GovernmentSubsidyModule module = new GovernmentSubsidyModule();
        
        // 查询用户国补资格状态
        SubsidyQualificationInfo qualificationInfo = queryUserSubsidyQualification(shopId, userId, isMt);
        
        // 设置优惠信息
        String maxRate = qualificationInfo.hasUnclaimedCategory() ? 
            qualificationInfo.getMaxUnclaimedRate() : qualificationInfo.getMaxClaimedRate();
        module.setDiscountInfo(maxRate);
        
        // 设置按钮状态和文案
        if (qualificationInfo.hasUnclaimedCategory()) {
            module.setButtonText("立即领取");
            module.setButtonStatus(0);
        } else {
            module.setButtonText("已领取");
            module.setButtonStatus(1);
        }
        
        // 设置跳转链接
        String jumpUrl = Lion.getStringValue(GOVERNMENT_SUBSIDY_JUMP_URL, DEFAULT_JUMP_URL);
        module.setJumpUrl(jumpUrl);
        
        return module;
    }

    /**
     * 查询用户国补资格信息
     */
    private SubsidyQualificationInfo queryUserSubsidyQualification(Long shopId, Long userId, boolean isMt) {
        // TODO: 实现闪购接口调用，查询用户国补资格
        // 这里先返回模拟数据
        SubsidyQualificationInfo info = new SubsidyQualificationInfo();
        info.setMaxUnclaimedRate("20%");
        info.setMaxClaimedRate("15%");
        info.setHasUnclaimedCategory(true);
        return info;
    }

    /**
     * 国补资格信息内部类
     */
    private static class SubsidyQualificationInfo {
        private String maxUnclaimedRate;
        private String maxClaimedRate;
        private boolean hasUnclaimedCategory;

        public String getMaxUnclaimedRate() {
            return maxUnclaimedRate;
        }

        public void setMaxUnclaimedRate(String maxUnclaimedRate) {
            this.maxUnclaimedRate = maxUnclaimedRate;
        }

        public String getMaxClaimedRate() {
            return maxClaimedRate;
        }

        public void setMaxClaimedRate(String maxClaimedRate) {
            this.maxClaimedRate = maxClaimedRate;
        }

        public boolean hasUnclaimedCategory() {
            return hasUnclaimedCategory;
        }

        public void setHasUnclaimedCategory(boolean hasUnclaimedCategory) {
            this.hasUnclaimedCategory = hasUnclaimedCategory;
        }
    }
}
