
package com.dianping.pay.api.service.impl;

import java.util.List;

import com.dianping.cat.Cat;
import com.google.common.collect.Lists;

public class LazyPager<T> {

	public interface DataProvider<T> {
		int getCount();

		List<T> getData(int startIndex, int limit);
	}

	List<DataProvider<T>> providers = Lists.newArrayList();

	public LazyPager() {

	}

	public void addDataProvider(DataProvider<T> provider) {
		Cat.logEvent("INVALID_METHOD_5", "com.dianping.pay.api.service.impl.LazyPager.addDataProvider(com.dianping.pay.api.service.impl.LazyPager$DataProvider)");
		this.providers.add(provider);
	}

	public List<T> getData(int startIndex, int limit) {
		Cat.logEvent("INVALID_METHOD_4", "com.dianping.pay.api.service.impl.LazyPager.getData(int,int)");
		List<T> list = Lists.newArrayList();
		for (DataProvider<T> src : this.providers) {
			if (limit > 0) {
				int count = src.getCount();
				if (startIndex < count) {
					list.addAll(src.getData(startIndex, limit));
					startIndex = 0;
					limit -= (count - startIndex);
				} else {
					startIndex -= (count);
				}
			}
		}
		return list;
	}

}
