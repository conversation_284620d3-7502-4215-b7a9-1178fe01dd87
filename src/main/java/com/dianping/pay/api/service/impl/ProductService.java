package com.dianping.pay.api.service.impl;

import com.dianping.deal.base.DealGroupBaseService;
import com.dianping.deal.base.dto.DealGroupBaseDTO;
import com.dianping.deal.idmapper.api.DealIdMapperService;
import com.dianping.deal.idmapper.api.dto.IdMapper;
import com.dianping.deal.publishcategory.DealGroupPublishCategoryQueryService;
import com.dianping.deal.publishcategory.dto.DealGroupChannelDTO;
import com.dianping.deal.voucher.query.api.DealGroupVoucherQueryService;
import com.dianping.deal.voucher.query.api.dto.VoucherDTO;
import com.dianping.lion.client.Lion;
import com.dianping.pay.api.beans.DealDetailDO;
import com.dianping.pay.api.beans.ProductDetailDO;
import com.dianping.pay.api.beans.ProductItemDetailDO;
import com.dianping.pay.api.util.LionConstants;
import com.dianping.tpfun.product.api.sku.aggregate.ItemSettleService;
import com.dianping.tpfun.product.api.sku.aggregate.ProductDetailService;
import com.dianping.tpfun.product.api.sku.aggregate.dto.ItemProfitDTO;
import com.dianping.tpfun.product.api.sku.aggregate.dto.ProductDetailDTO;
import com.dianping.tpfun.product.api.sku.aggregate.dto.SimpleItemDTO;
import com.dianping.tpfun.product.api.sku.aggregate.request.GetProductDetailRequest;
import com.dianping.tpfun.product.api.sku.common.enums.SPUTypeEnum;
import com.dianping.tpfun.product.api.sku.model.Product;
import com.dianping.tpfun.product.api.sku.model.ProductItem;
import com.dianping.tpfun.product.api.sku.request.GetProductItemRequest;
import com.dianping.ts.settle.common.api.util.SettleUtil;
import com.dianping.zsts.commission.unify.api.UnifiedCommissionQueryService;
import com.dianping.zsts.commission.unify.dto.DealGroupCommissionDto;
import com.dianping.zsts.commission.unify.dto.ItemCommissionDto;
import com.dianping.zsts.commission.unify.request.DealGroupCommissionRequest;
import com.dianping.zsts.commission.unify.request.ItemCommissionRequest;
import com.dianping.zsts.commission.unify.request.LatestItemProfitRequest;
import com.dianping.zsts.commission.unify.request.ShopSkuIdsMapping;
import com.google.common.base.Function;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.google.common.primitives.Ints;
import com.meituan.mtrace.Tracer;
import com.sankuai.general.product.query.center.client.builder.model.DealBasicInfoBuilder;
import com.sankuai.general.product.query.center.client.builder.model.DealGroupCategoryBuilder;
import com.sankuai.general.product.query.center.client.builder.model.DealGroupRuleBuilder;
import com.sankuai.general.product.query.center.client.builder.requset.QueryByDealGroupIdRequestBuilder;
import com.sankuai.general.product.query.center.client.dto.AttrDTO;
import com.sankuai.general.product.query.center.client.dto.DealGroupCategoryDTO;
import com.sankuai.general.product.query.center.client.dto.DealGroupDTO;
import com.sankuai.general.product.query.center.client.dto.deal.DealGroupDealDTO;
import com.sankuai.general.product.query.center.client.dto.rule.use.ReceiptEffectiveDateDTO;
import com.sankuai.general.product.query.center.client.enums.AttrSubjectEnum;
import com.sankuai.general.product.query.center.client.enums.IdTypeEnum;
import com.sankuai.general.product.query.center.client.enums.ResponseCodeEnum;
import com.sankuai.general.product.query.center.client.request.QueryByDealGroupIdRequest;
import com.sankuai.general.product.query.center.client.response.QueryDealGroupListResponse;
import com.sankuai.general.product.query.center.client.service.DealGroupQueryService;
import com.sankuai.general.product.query.center.client.util.DealGroupUtils;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

@Component
@Slf4j
public class ProductService {

    @Resource
    private DealGroupVoucherQueryService voucherQueryService;
    @Autowired
    private DealIdMapperService dealIdMapperService;
    @Resource
    private DealGroupPublishCategoryQueryService dealGroupPublishCategoryQueryServiceFuture;
    @Resource
    private DealGroupBaseService dealGroupBaseService;
    @Resource
    private ProductDetailService productDetailService;
    @Resource(name = "skuProductService")
    private com.dianping.tpfun.product.api.sku.ProductService productService;
    @Resource
    private ItemSettleService itemSettleService;
    @Resource(name = "unifiedCommissionQueryService")
    private UnifiedCommissionQueryService unifiedCommissionQueryService;
    @Autowired
    private DealGroupQueryService dealGroupQueryService;

    public DealGroupBaseDTO getDealGroup(int dpDealGroupId) {
        try {
            return dealGroupBaseService.getDealGroup(dpDealGroupId);
        } catch (Exception e) {
            log.error("getDealGroup error. dealGroupId: {}", dpDealGroupId, e);
            return null;
        }
    }

    public DealDetailDO getDealGroupDetailInfo(long dpDealGroupId) {
        DealDetailDO dealDetailDO = new DealDetailDO();
        QueryByDealGroupIdRequest request = QueryByDealGroupIdRequestBuilder.builder()
            .dealGroupIds(Sets.newHashSet(dpDealGroupId),IdTypeEnum.DP)
            .rule(DealGroupRuleBuilder.builder().useRule())
            .category(DealGroupCategoryBuilder.builder().categoryId())
            .dealBasicInfo(DealBasicInfoBuilder.builder().status())
            .attrsByKey(AttrSubjectEnum.DEAL,"sku_receipt_type")
            .build();
        try {
            QueryDealGroupListResponse queryDealGroupListResponse = dealGroupQueryService.queryByDealGroupIds(request);
            if(queryDealGroupListResponse != null
                && queryDealGroupListResponse.getCode() == ResponseCodeEnum.SUCCESS.getCode()
                && queryDealGroupListResponse.getData() != null
                && CollectionUtils.isNotEmpty(queryDealGroupListResponse.getData().getList())) {
                DealGroupDTO dealGroupDTO = queryDealGroupListResponse.getData().getList().get(0);
                //团购可用开始结束时间
                if(dealGroupDTO.getRule() != null
                    && dealGroupDTO.getRule().getUseRule() != null
                    && dealGroupDTO.getRule().getUseRule().getReceiptEffectiveDate() != null) {
                    ReceiptEffectiveDateDTO receiptEffectiveDateDTO = dealGroupDTO.getRule().getUseRule().getReceiptEffectiveDate();
                    String startTimestamp = String.valueOf(DealGroupUtils.convertString2Date(receiptEffectiveDateDTO.getReceiptBeginDate()).getTime());
                    String endTimestamp = String.valueOf(DealGroupUtils.convertString2Date(receiptEffectiveDateDTO.getReceiptEndDate()).getTime());
                    dealDetailDO.setStartTimestamp(startTimestamp);
                    dealDetailDO.setEndTimestamp(endTimestamp);
                }
                //团购二级类目
                DealGroupCategoryDTO category = dealGroupDTO.getCategory();
                if(category != null && category.getCategoryId() != null) {
                    dealDetailDO.setDealCategoryId(category.getCategoryId().toString());
                }
                //团购三方判断
                List<DealGroupDealDTO> validDeals = dealGroupDTO.getDeals().stream().filter(x -> x.getBasic().getStatus() == 1).collect(Collectors.toList());
                if(CollectionUtils.isNotEmpty(validDeals)
                    && CollectionUtils.isNotEmpty(validDeals.get(0).getAttrs())) {
                    List<AttrDTO> attrs = validDeals.get(0).getAttrs().stream().filter(x -> x.getName().equals("sku_receipt_type")).collect(Collectors.toList());
                    if(CollectionUtils.isNotEmpty(attrs) && CollectionUtils.isNotEmpty(attrs.get(0).getValue())) {
                        //查询只有一个属性，取第一个属性就行
                        dealDetailDO.setThirdPartyCode(attrs.get(0).getValue().get(0));
                    }
                }
            }
            return dealDetailDO;
        } catch (Exception e) {
            log.error("getDealGroup error. dealGroupId: {}", dpDealGroupId);
            return null;
        }
    }

    /**
     * 查询团单对应的佣金率，注意入参是点评团单id，如果佣金率是5%，返回的是5
     *
     * @param dpDealGroupId
     * @return
     */
    public BigDecimal getDealGroupCommissionRate(int dpDealGroupId, boolean dealSwitch) {
        try {
            if (dealSwitch) {
                DealGroupCommissionRequest dealGroupCommissionRequest = new DealGroupCommissionRequest();
                dealGroupCommissionRequest.setDealGroupID((long) dpDealGroupId);
                Map<Long, DealGroupCommissionDto> dealGroupCommissionDtoMap = unifiedCommissionQueryService.batchQueryDealGroupCommission(Lists.newArrayList(dealGroupCommissionRequest));
                if (MapUtils.isEmpty(dealGroupCommissionDtoMap) || dealGroupCommissionDtoMap.get((long) dpDealGroupId) == null) {
                    log.error("getSkuCommissionRate empty. dealGroupId: {}", dpDealGroupId);
                    return null;
                }
                return new BigDecimal(dealGroupCommissionDtoMap.get((long) dpDealGroupId).getCommissionRate()).multiply(new BigDecimal(100));
            } else {
                List<VoucherDTO> vouchers = voucherQueryService.queryDealGroupVoucher(dpDealGroupId);
                if (CollectionUtils.isNotEmpty(vouchers)) {
                    VoucherDTO voucherDTO = vouchers.get(0);
                    BigDecimal commissionRate = SettleUtil.getCommission4TG(voucherDTO.getDealCost(), voucherDTO.getDealPrice());
                    if (commissionRate != null) {
                        return commissionRate.multiply(BigDecimal.valueOf(100));
                    }
                    log.error("getDealGroupCommissionRate empty. dealGroupId: {}", dpDealGroupId);
                    return null;
                }
                log.error("getDealGroupCommissionRate empty. dealGroupId: {}", dpDealGroupId);
                return null;
            }
        } catch (Exception e) {
            log.error("getDealGroupCommissionRate error. dealGroupId: {}", dpDealGroupId);
            return null;
        }
    }

    /**
     * 查询sku对应的佣金率，如果佣金率是5%，返回的是5
     *
     * @param skuId
     * @return
     */
    public BigDecimal getSkuCommissionRate(int skuId, boolean skuSwitch) {
        try {
            if (skuSwitch) {
                ItemCommissionRequest itemCommissionRequest = new ItemCommissionRequest();
                itemCommissionRequest.setItemId((long) skuId);
                Map<Long, ItemCommissionDto> commissionDtoMap = unifiedCommissionQueryService.batchQueryItemCommission(Lists.newArrayList(itemCommissionRequest));
                if (MapUtils.isEmpty(commissionDtoMap) || commissionDtoMap.get((long) skuId) == null) {
                    log.error("getSkuCommissionRate empty. skuId: {}", skuId);
                    return null;
                }
                return new BigDecimal(commissionDtoMap.get((long) skuId).getCommissionRate()).multiply(new BigDecimal(100));
            } else {
                Map<Integer, ItemProfitDTO> itemProfitDTOMap = itemSettleService.mGetItemProfitDto(Lists.newArrayList(skuId));
                if (MapUtils.isEmpty(itemProfitDTOMap) || itemProfitDTOMap.get(skuId) == null) {
                    return null;
                }
                return itemProfitDTOMap.get(skuId).getProfit();
            }
        } catch (Exception e) {
            log.error("getSkuCommissionRate error. skuId: {}", skuId, e);
        }
        return null;
    }

    public int getDpDealGroupIdByMtId(int mtDealGroupId) {
        try {
            IdMapper idMapper = dealIdMapperService.queryByMtDealGroupId(mtDealGroupId);
            if (idMapper != null) {
                return idMapper.getDpDealGroupID();
            }
            return 0;
        } catch (Exception e) {
            log.error("getDpDealGroupIdByMtId error. mtDealGroupId: {}", mtDealGroupId);
            return 0;
        }
    }

    public DealGroupChannelDTO loadDealGroupChannelByDpId(int dpDealGroupId) {
        try {
            return dealGroupPublishCategoryQueryServiceFuture.getDealGroupChannelById(dpDealGroupId);
        } catch (Exception e) {
            log.error("loadDealGroupChannelByDpId error. dpDealGroupId: {}", dpDealGroupId, e);
        }
        return null;
    }


    public ProductDetailDO loadProductDetail(int skuId) {
        try {
            if (skuId <= 0) {
                return null;
            }
            int productId = loadProductIdBySkuId(skuId);
            if (productId <= 0) {
                return null;
            }
            GetProductDetailRequest request = new GetProductDetailRequest();
            request.setProductId(productId);
            ProductDetailDTO productDetailDTO = productDetailService.getProductDetail(request);
            if (productDetailDTO == null) {
                return null;
            }
            Product product = productDetailDTO.getProduct();

            ProductDetailDO productDetailDO = new ProductDetailDO();
            productDetailDO.setName(product.getName());
            productDetailDO.setProductType(product.getProductType());
            productDetailDO.setSpuType(product.getSpuType());

            List<SimpleItemDTO> productItemDTOs = productDetailDTO.getProductItemDTOs();
            if (org.apache.commons.collections.CollectionUtils.isNotEmpty(productItemDTOs)) {
                //多sku处理 填充list
                List<ProductItemDetailDO> items = Lists.newArrayList();
                for (SimpleItemDTO productItemDTO : productItemDTOs) {
                    //过滤小于0的
                    if (productItemDTO.getStatus() < 0) {
                        continue;
                    }
                    ProductItemDetailDO productItemDetailDO = new ProductItemDetailDO();
                    productItemDetailDO.setId(productItemDTO.getItemId());
                    productItemDetailDO.setName(productItemDTO.getName());
                    productItemDetailDO.setPrePayPrice(productItemDTO.getAttrFirstValue("prePayPrice"));
                    productItemDetailDO.setFinalPayPrice(productItemDTO.getAttrFirstValue("finalPayPrice"));

                    //获取价格，普通医美预付商品通过sku price获取，医美商家小程序商品通过sku属性"total price"获取
                    BigDecimal itemTotalPrice = null;
                    if (productItemDTO.getProductItemSaleDTO() != null) {
                        if (productDetailDTO.getProduct().getSpuType() == SPUTypeEnum.MedicalPrepayMerchantMiniProgram.getCode()) {
                            itemTotalPrice = new BigDecimal(productItemDTO.getAttrFirstValue("totalPrice"));
                        } else {
                            itemTotalPrice = productItemDTO.getProductItemSaleDTO().getPrice();
                        }
                        productItemDetailDO.setMarketPrice(productItemDTO.getProductItemSaleDTO().getMarketPrice());
                        //医美小程序用totalPrice
                        productItemDetailDO.setPrice(itemTotalPrice);
                    }
                    //是否为多次sku
                    String countStr = productItemDTO.getAttrFirstValue("count");
                    //默认为1，且不过滤属性为空的情况，医美默认都有count但是齿科没有
                    Integer count = 1;
                    if (StringUtils.isNotBlank(countStr)) {
                        count = Ints.tryParse(countStr);
                        if (count == null) {
                            continue;
                        }
                    }
                    productItemDetailDO.setCount(String.valueOf(count));
                    if (count > 1) {
                        String singlePriceStr = productItemDTO.getAttrFirstValue("unitPrice");
                        if (StringUtils.isNotBlank(singlePriceStr)) {
                            productItemDetailDO.setSinglePrice(new BigDecimal(singlePriceStr));
                        } else {
                            //医美小程序totalPrice
                            if (itemTotalPrice != null) {
                                BigDecimal singlePrice = itemTotalPrice.divide(new BigDecimal(count), 0, BigDecimal.ROUND_CEILING);
                                productItemDetailDO.setSinglePrice(singlePrice);
                            }
                        }
                        productItemDetailDO.setSpecial(true);
                    } else {
                        productItemDetailDO.setSpecial(false);
                        //兼容老逻辑，正常的sku需要被填充到productDetailDO下
                        productDetailDO.setProductItemId(productItemDTO.getItemId().intValue());
                        productDetailDO.setPrePayPrice(productItemDTO.getAttrFirstValue("prePayPrice"));
                        productDetailDO.setFinalPayPrice(productItemDTO.getAttrFirstValue("finalPayPrice"));
                        if (productItemDTO.getProductItemSaleDTO() != null) {
                            productDetailDO.setMarketPrice(productItemDTO.getProductItemSaleDTO().getMarketPrice());
                            //医美小程序用totalPrice
                            productDetailDO.setPrice(itemTotalPrice);
                        }
                    }
                    productItemDetailDO.setOriginalPrice(productItemDetailDO.getPrice());
                    items.add(productItemDetailDO);
                }
                productDetailDO.setProductItems(items);
            }
            return productDetailDO;
        } catch (Exception e) {
            log.error("loadProductIdBySkuId error. skuId: {}", skuId, e);
        }
        return null;
    }

    private int loadProductIdBySkuId(int skuId) {
        try {
            GetProductItemRequest getProductItemRequest = new GetProductItemRequest();
            getProductItemRequest.setItemIds(Lists.newArrayList(skuId));
            getProductItemRequest.setNeedIemAttr(false);

            Map<Integer, ProductItem> productItemMap = productService.getItemsById(getProductItemRequest);
            if (MapUtils.isEmpty(productItemMap) || productItemMap.get(skuId) == null) {
                return 0;
            }
            return productItemMap.get(skuId).getProductId();
        } catch (Exception e) {
            log.error("loadProductIdBySkuId error. skuId: {}", skuId, e);
        }
        return 0;
    }

}
