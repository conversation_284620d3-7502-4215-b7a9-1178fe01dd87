package com.dianping.pay.api.service.impl;

import com.dianping.api.framework.BaseService;
import com.dianping.cat.Cat;
import com.dianping.deal.base.DealBaseService;
import com.dianping.deal.base.DealGroupBaseService;
import com.dianping.deal.base.dto.DealBaseDTO;
import com.dianping.deal.base.dto.DealGroupBaseDTO;
import com.dianping.pay.api.entity.DealGroupSelect;
import com.dianping.pay.api.entity.DealSelect;
import com.dianping.pay.api.service.DealService;

import javax.annotation.Resource;

public class DealServiceImpl extends BaseService implements DealService {

    @Resource
    private DealBaseService dealBaseService;
    @Resource
    private DealGroupBaseService dealGroupBaseService;

    @Override
    public DealSelect getDealSelectById(int dealId) {
        Cat.logEvent("INVALID_METHOD_4", "com.dianping.pay.api.service.impl.DealServiceImpl.getDealSelectById(int)");
        DealBaseDTO baseDTO = dealBaseService.getDeal(dealId);
        return formatDealSelect(baseDTO);
    }

    private DealSelect formatDealSelect(DealBaseDTO baseDTO) {
        Cat.logEvent("INVALID_METHOD_4", "com.dianping.pay.api.service.impl.DealServiceImpl.formatDealSelect(com.dianping.deal.base.dto.DealBaseDTO)");
        if(baseDTO==null){
            return null;
        }else{
            DealSelect dealSelect = new DealSelect();
            dealSelect.setId(baseDTO.getDealId());
            dealSelect.setPrice(baseDTO.getPrice());
            dealSelect.setTitle(baseDTO.getShortTitle());
            dealSelect.setDealGroupId(baseDTO.getDealGroupId());
            dealSelect.setDelivery(baseDTO.getDeliverType()==1);
            return dealSelect;
        }
    }

    @Override
    public DealGroupSelect getDealGroupSelect(int dealGroupId) {
        Cat.logEvent("INVALID_METHOD_5", "com.dianping.pay.api.service.impl.DealServiceImpl.getDealGroupSelect(int)");
        return formatDealGroupSelect(dealGroupBaseService.getDealGroup(dealGroupId));
    }

    private DealGroupSelect formatDealGroupSelect(DealGroupBaseDTO dealGroup) {
        Cat.logEvent("INVALID_METHOD_1", "com.dianping.pay.api.service.impl.DealServiceImpl.formatDealGroupSelect(com.dianping.deal.base.dto.DealGroupBaseDTO)");
        if(dealGroup==null){
            return null;
        }else{
            DealGroupSelect select = new DealGroupSelect();
            select.setDealGroupId(dealGroup.getDealGroupId());
            select.setAutoRefundSwitch(dealGroup.getAutoRefundSwitch());
            select.setBeginDate(dealGroup.getBeginDate());
            select.setDealGroupShortTitle(dealGroup.getDealGroupShortTitle());
            select.setDealGroupTitleDesc(dealGroup.getDealGroupTitleDesc());
            select.setDealGroupTypeId(dealGroup.getDealGroupType());
            select.setDiscountRuleId(dealGroup.getDiscountRuleID());
            select.setEndDate(dealGroup.getEndDate());
            select.setBlockStock(dealGroup.isBlockStock());
            select.setMaxPerUser(dealGroup.getMaxPerUser());
            select.setMinPerUser(dealGroup.getMinPerUser());
            select.setPayChannelIdAllowed(dealGroup.getPayChannelIDAllowed());
            select.setPublishStatus(dealGroup.getPublishStatus());
            select.setSaleChannel(dealGroup.getSaleChannel());
            select.setStatus(dealGroup.getStatus());
            select.setThirdPartVerify(dealGroup.isThirdPartVerify());
            select.setCanUseCoupon(dealGroup.isCanUseCoupon());
            return select;
        }
    }

}
