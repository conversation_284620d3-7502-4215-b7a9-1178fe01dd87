package com.dianping.pay.api.service.impl;

import com.dianping.api.util.LionQueryUtils;
import com.dianping.pay.api.biz.activity.newloader.dto.AbTestConfig;
import com.dianping.pay.api.biz.activity.newloader.dto.AbTestInfo;
import com.dianping.pay.api.biz.activity.newloader.dto.AbTestRequest;
import com.dianping.pay.api.service.AbTestService;
import com.dianping.pay.api.util.LogUtils;
import com.sankuai.douhu.absdk.bean.DouHuRequest;
import com.sankuai.douhu.absdk.bean.DouHuResponse;
import com.sankuai.douhu.absdk.client.DouHuClient;
import com.sankuai.douhu.absdk.enums.ErrorCode;
import com.sankuai.douhu.absdk.enums.PlatformEnum;
import com.sankuai.douhu.absdk.util.DouHuUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2025/3/21
 */
@Component
@Slf4j
public class AbTestServiceImpl implements AbTestService {

    @Resource
    private DouHuClient douHuClient;

    @Override
    public AbTestInfo tryAb(AbTestRequest request) {
        if (request == null || StringUtils.isBlank(request.getAbCode())) {
            return null;
        }
        AbTestConfig abConfig = getAbConfig(request);
        if (abConfig == null) {
            return null;
        }
        DouHuRequest douHuRequest = buildDouhuRequest(request, abConfig);
        DouHuResponse douHuResponse = tryDouHuAbImpl(douHuRequest);
        return convertAbTestInfo(douHuResponse, abConfig);
    }

    private AbTestInfo convertAbTestInfo(DouHuResponse response, AbTestConfig abConfig) {
        if (response != null && ErrorCode.SUCCESS.getCode().equalsIgnoreCase(response.getCode())&&
                !StringUtils.isEmpty(response.getSk())) {
            AbTestInfo abTestInfo = new AbTestInfo();
            abTestInfo.setModuleAbInfo4Front(DouHuUtil.extractExpABInfo4Front(response));
            abTestInfo.setAbCode(abConfig.getAbCode());
            abTestInfo.setHitStrategy(abConfig.getExpSk2StrategyKey().get(response.getSk()));
            return abTestInfo;
        }
        return null;
    }

    private DouHuResponse tryDouHuAbImpl(DouHuRequest douHuRequest) {
        try {
            return douHuClient.tryAb(douHuRequest);
        }catch (Exception e) {
            log.error("douhu ab test error, msg:{}", e.getMessage(), e);
        }
        return null;
    }

    private DouHuRequest buildDouhuRequest(AbTestRequest request, AbTestConfig abConfig) {
        DouHuRequest douHuRequest = new DouHuRequest();
        douHuRequest.setExpId(abConfig.getExpId());
        if (Objects.equals(PlatformEnum.DP.getCode(), abConfig.getPlatform())) {
            douHuRequest.setDpid(request.getDpId());
        }else {
            douHuRequest.setUuid(request.getUuid());
        }
        douHuRequest.setAppId(request.getAppId());
        douHuRequest.setUserId(request.getUserId());
        if (request.getCityId() != null) {
            douHuRequest.setCityId(String.valueOf(request.getCityId()));
        }
        douHuRequest.setOs(request.getOs());
        douHuRequest.setAppClient(request.getAppVersion());
        return douHuRequest;
    }

    private AbTestConfig getAbConfig(AbTestRequest request) {
        List<AbTestConfig> abTestConfig = LionQueryUtils.getAbTestConfig(request.getAbCode());
        if (CollectionUtils.isEmpty(abTestConfig)) {
            return null;
        }
        for (AbTestConfig config : abTestConfig) {
            if (config == null) {
                continue;
            }
            if (config.getPlatform() != null && !config.getPlatform().equals(request.getPlatform())) {
                continue;
            }
            if (CollectionUtils.isNotEmpty(config.getExcludeCityIds())
                    && config.getExcludeCityIds().contains(request.getCityId())) {
                continue;
            }
            if (request.isMiniProgramFlag() && !config.isIncludeMiniProgram()) {
                continue;
            }
            return config;
        }
        return null;
    }
}
