
package com.dianping.pay.api.service.impl;

import com.dianping.avatar.log.AvatarLogger;
import com.dianping.avatar.log.AvatarLoggerFactory;
import com.dianping.cat.Cat;
import com.google.common.collect.Sets;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;

import java.util.Set;

/**
 * NOTE: ALWAYS ADD AN ADDITIONAL SET METHOD WHICH RECEIVES AN STRING PARAMETER
 * 
 * <AUTHOR>
 */
public class LionConfigService {

	private static final AvatarLogger LOG = AvatarLoggerFactory.getLogger(LionConfigService.class);

	/**
	 * 是否允许app端手机重绑
	 */
	private boolean isRebindEnabled;

	/**
	 * 银联无卡支付开关 0关1主2团3全开
	 */
	private int unipayStatus;

	/**
	 * 联动优势支付开关 0关1主2团3全开
	 */
	private int umpayStatus;

	/**
	 * 财付通支付开关 0关1主2团3全开
	 */
	private int tenpayStatus;

	/**
	 * 微信支付开关 0关1主2团3全开
	 */
	private int weixinStatus;

	/**
	 * 当银联/联动同时可用时的选择 1银联2联动
	 */
	private int creditCardPayChannel;

	private int isHotRegionEnabled;

	/**
	 * 是否允许退款至余额
	 */
	private int isRefundToBalanceEnabled;

	/**
	 * 订单详情页晒单入口开关
	 */
	private int isShaiDanEnabled;

	/**
	 * 是否禁用网页登陆
	 */
	private int isWebLoginEnabled;

	/**
	 * 是否启用批量验券
	 */
	private int isBatchTicketEnabled;

	/**
	 * 酒店单五元直减
	 */
	private int fiveYuanOffer;

	private String voucherOfferCityIds;
	private Set<Integer> voucherOfferCityIdSet = Sets.newHashSet();

	public boolean isRebindEnabled() {
        Cat.logEvent("INVALID_METHOD_2", "com.dianping.pay.api.service.impl.LionConfigService.isRebindEnabled()");
        return isRebindEnabled;
	}

	public void setIsRebindEnabled(boolean isRebindEnabled) {
		this.isRebindEnabled = isRebindEnabled;
	}

	public int getUnipayStatus() {
		return unipayStatus;
	}

	public void setUnipayStatus(int unipayStatus) {
		this.unipayStatus = unipayStatus;
	}

	public void setUnipayStatus(String unipayStatus) {
        Cat.logEvent("INVALID_METHOD_1", "com.dianping.pay.api.service.impl.LionConfigService.setUnipayStatus(java.lang.String)");
        this.unipayStatus = Integer.parseInt(unipayStatus);
	}

	public int getUmpayStatus() {
		return umpayStatus;
	}

	public void setUmpayStatus(int umpayStatus) {
		this.umpayStatus = umpayStatus;
	}

	public void setUmpayStatus(String umpayStatus) {
        Cat.logEvent("INVALID_METHOD_2", "com.dianping.pay.api.service.impl.LionConfigService.setUmpayStatus(java.lang.String)");
        this.umpayStatus = Integer.parseInt(umpayStatus);
	}

	public int getTenpayStatus() {
		return tenpayStatus;
	}

	public void setTenpayStatus(int tenpayStatus) {
		this.tenpayStatus = tenpayStatus;
	}

	public void setTenpayStatus(String tenpayStatus) {
        Cat.logEvent("INVALID_METHOD_5", "com.dianping.pay.api.service.impl.LionConfigService.setTenpayStatus(java.lang.String)");
        this.tenpayStatus = Integer.parseInt(tenpayStatus);
	}

	public int getIsHotRegionEnabled() {
		return isHotRegionEnabled;
	}

	public void setIsHotRegionEnabled(int isHotRegionEnabled) {
		this.isHotRegionEnabled = isHotRegionEnabled;
	}

	public void setIsHotRegionEnabled(String isHotRegionEnabled) {
        Cat.logEvent("INVALID_METHOD_3", "com.dianping.pay.api.service.impl.LionConfigService.setIsHotRegionEnabled(java.lang.String)");
        this.isHotRegionEnabled = Integer.parseInt(isHotRegionEnabled);
	}

	public int getIsRefundToBalanceEnabled() {
		return isRefundToBalanceEnabled;
	}

	public void setIsRefundToBalanceEnabled(int isRefundToBalanceEnabled) {
		this.isRefundToBalanceEnabled = isRefundToBalanceEnabled;
	}

	public void setIsRefundToBalanceEnabled(String isRefundToBalanceEnabled) {
        Cat.logEvent("INVALID_METHOD_5", "com.dianping.pay.api.service.impl.LionConfigService.setIsRefundToBalanceEnabled(java.lang.String)");
        this.isRefundToBalanceEnabled = Integer.parseInt(isRefundToBalanceEnabled);
	}

	public int getIsShaiDanEnabled() {
		return isShaiDanEnabled;
	}

	public void setIsShaiDanEnabled(int isShaiDanEnabled) {
		this.isShaiDanEnabled = isShaiDanEnabled;
	}

	public void setIsShaiDanEnabled(String isShaiDanEnabled) {
		this.isShaiDanEnabled = Integer.parseInt(isShaiDanEnabled);
	}

	public int getCreditCardPayChannel() {
		return creditCardPayChannel;
	}

	public void setCreditCardPayChannel(int creditCardPayChannel) {
		this.creditCardPayChannel = creditCardPayChannel;
	}

	public void setCreditCardPayChannel(String creditCardPayChannel) {
        Cat.logEvent("INVALID_METHOD_2", "com.dianping.pay.api.service.impl.LionConfigService.setCreditCardPayChannel(java.lang.String)");
        this.creditCardPayChannel = Integer.parseInt(creditCardPayChannel);
	}

	public int getIsWebLoginEnabled() {
		return isWebLoginEnabled;
	}

	public void setIsWebLoginEnabled(int isWebLoginEnabled) {
		this.isWebLoginEnabled = isWebLoginEnabled;
	}

	public void setIsWebLoginEnabled(String isWebLoginEnabled) {
        Cat.logEvent("INVALID_METHOD_3", "com.dianping.pay.api.service.impl.LionConfigService.setIsWebLoginEnabled(java.lang.String)");
        this.isWebLoginEnabled = Integer.parseInt(isWebLoginEnabled);
	}

	public int getIsBatchTicketEnabled() {
		return isBatchTicketEnabled;
	}

	public void setIsBatchTicketEnabled(int isBatchTicketEnabled) {
		this.isBatchTicketEnabled = isBatchTicketEnabled;
	}

	public void setIsBatchTicketEnabled(String isBatchTicketEnabled) {
        Cat.logEvent("INVALID_METHOD_1", "com.dianping.pay.api.service.impl.LionConfigService.setIsBatchTicketEnabled(java.lang.String)");
        this.isBatchTicketEnabled = Integer.parseInt(isBatchTicketEnabled);
	}

	public int getFiveYuanOffer() {
		return fiveYuanOffer;
	}

	public void setFiveYuanOffer(int fiveYuanOffer) {
		this.fiveYuanOffer = fiveYuanOffer;
	}

	public void setFiveYuanOffer(String fiveYuanOffer) {
        Cat.logEvent("INVALID_METHOD_1", "com.dianping.pay.api.service.impl.LionConfigService.setFiveYuanOffer(java.lang.String)");
        this.fiveYuanOffer = Integer.parseInt(fiveYuanOffer);
	}

	public int getWeixinStatus() {
		return weixinStatus;
	}

	public void setWeixinStatus(int weixinStatus) {
		this.weixinStatus = weixinStatus;
	}

	public void setWeixinStatus(String weixinStatus) {
        Cat.logEvent("INVALID_METHOD_1", "com.dianping.pay.api.service.impl.LionConfigService.setWeixinStatus(java.lang.String)");
        this.weixinStatus = Integer.parseInt(weixinStatus);
	}

	public boolean isVoucherOfferCity(int cityId) {
        Cat.logEvent("INVALID_METHOD_1", "com.dianping.pay.api.service.impl.LionConfigService.isVoucherOfferCity(int)");
        return voucherOfferCityIdSet.contains(cityId);
	}

	public String getVoucherOfferCityIds() {
		return voucherOfferCityIds;
	}

	public void setVoucherOfferCityIds(String voucherOfferCityIds) {
		this.voucherOfferCityIds = voucherOfferCityIds;
		Set<Integer> s = Sets.newHashSet();
		for (String c : StringUtils.split(voucherOfferCityIds, ',')) {
			int cityId = NumberUtils.toInt(c, 0);
			if (cityId > 0) {
				s.add(cityId);
			}
		}
		this.voucherOfferCityIdSet = s;
	}

	public Set<Integer> getVoucherOfferCityIdSet() {
		return voucherOfferCityIdSet;
	}

	public void setVoucherOfferCityIdSet(Set<Integer> voucherOfferCityIdSet) {
		this.voucherOfferCityIdSet = voucherOfferCityIdSet;
	}
	public boolean isWeiFangCinemaOffer() {
		return weiFangCinemaOffer;
	}

	public void setWeiFangCinemaOffer(boolean weiFangCinemaOffer) {
		this.weiFangCinemaOffer = weiFangCinemaOffer;
	}

	public String getWeiFangOfferDealGroupIds() {
		return weiFangOfferDealGroupIds;
	}

	public void setWeiFangOfferDealGroupIds(String weiFangOfferDealGroupIds) {
		this.weiFangOfferDealGroupIds = weiFangOfferDealGroupIds;
		
		Set<Integer> s = Sets.newHashSet();
		if (weiFangOfferDealGroupIds != null) {
			for (String id : StringUtils.split(weiFangOfferDealGroupIds, ',')) {
				int i = NumberUtils.toInt(id, 0);
				if (i > 0) { s.add(i); }
			}
		}
		WEIFANG_OFFER_DEALGROUPIDS = s;
	}

	private boolean weiFangCinemaOffer;
	private String weiFangOfferDealGroupIds;
	
	public static Set<Integer> WEIFANG_OFFER_DEALGROUPIDS = Sets.newHashSet();

}
