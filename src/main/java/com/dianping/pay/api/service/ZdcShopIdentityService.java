package com.dianping.pay.api.service;

import com.dianping.pay.api.entity.issuecoupon.GovernmentSubsidyInfo;

/**
 * ZDC门店标识服务接口
 * <AUTHOR>
 */
public interface ZdcShopIdentityService {

    /**
     * 查询门店是否有国补标识
     * @param shopId 门店ID
     * @param isMt 是否美团端
     * @return 是否有国补标识
     */
    boolean hasGovernmentSubsidyIdentity(Long shopId, boolean isMt);

    /**
     * 获取门店国补信息
     * @param shopId 门店ID
     * @param userId 用户ID
     * @param isMt 是否美团端
     * @return 国补信息
     */
    GovernmentSubsidyInfo getGovernmentSubsidyInfo(Long shopId, Long userId, boolean isMt);

}
