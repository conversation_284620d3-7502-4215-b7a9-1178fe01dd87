package com.dianping.pay.api.service.impl;

import com.dianping.pay.api.util.CollUtils;
import com.dianping.poi.relation.service.api.PoiRelationService;
import com.dianping.poi.shopcateprop.api.dto.dpcategory.PoiCategoryInfoDTO;
import com.dianping.poi.shopcateprop.api.service.PoiShopCategoryQueryService;
import com.google.common.collect.ImmutableList;
import com.google.common.collect.Lists;
import com.sankuai.sinai.data.api.dto.*;
import com.sankuai.sinai.data.api.service.DpPoiService;
import com.sankuai.sinai.data.api.service.MtPoiService;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Arrays;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2023/8/10
 */
@Service
public class ShopWrapperService {

    public static final Logger logger = LoggerFactory.getLogger(ShopWrapperService.class);

    private static final List<String> fields = ImmutableList.of(
            "backMainCategoryPath"
    );

    private static final List<String> mtFields = Arrays.asList(
            "typeHierarchy"
    );

    @Autowired
    private DpPoiService sinaiDpPoiService;

    @Autowired
    private MtPoiService mtPoiService;

    @Autowired
    private PoiRelationService poiRelationCacheService;

    public Pair<List<Integer>, List<Integer>> loadShopCategory(long dpShopId) {
        if (dpShopId <= 0) {
            return null;
        }
        DpPoiRequest dpPoiRequest = new DpPoiRequest();
        dpPoiRequest.setShopIds(Lists.newArrayList(dpShopId));
        dpPoiRequest.setFields(fields);
        List<DpPoiBackCategoryDTO> categoryInfoDTOS = Lists.newArrayList();

        try {
            List<DpPoiDTO> result = sinaiDpPoiService.findShopsByShopIds(dpPoiRequest);
            for(DpPoiDTO dpPoiDTO : result) {
                categoryInfoDTOS.addAll(dpPoiDTO.getBackMainCategoryPath());
            }
        } catch(Exception e) {
            logger.error("loadShopCategory exception. dpShopId: {}", dpShopId, e);
        }

        if (CollectionUtils.isEmpty(categoryInfoDTOS)) {
            return null;
        }
        List<Integer> firstCategoryList = Lists.newArrayList();
        List<Integer> secondCategoryList = Lists.newArrayList();
        for (DpPoiBackCategoryDTO infoDTO : categoryInfoDTOS) {
            if (null == infoDTO.getCategoryLevel()) {
                continue;
            }
            if (infoDTO.getCategoryLevel() == 1) {
                firstCategoryList.add(infoDTO.getCategoryId());
            } else if (infoDTO.getCategoryLevel() == 2) {
                secondCategoryList.add(infoDTO.getCategoryId());
            }
        }
        return Pair.of(firstCategoryList, secondCategoryList);
    }

    public Pair<Integer, Integer> loadMtShopFirstCategory(long mtShopId) {
        if (mtShopId <= 0) {
            return null;
        }
        List<TypeHierarchyView> typeHierarchyViews = Lists.newArrayList();

        try {
            Map<Long, MtPoiDTO> result = mtPoiService.findPoisById(Arrays.asList(mtShopId), mtFields);
            if (MapUtils.isEmpty(result) || !result.containsKey(mtShopId)) {
                return null;
            }
            MtPoiDTO mtPoiDTO = result.get(mtShopId);
            if (mtPoiDTO == null || CollectionUtils.isEmpty(mtPoiDTO.getTypeHierarchy())) {
                return null;
            }
            typeHierarchyViews = mtPoiDTO.getTypeHierarchy();
        } catch(Exception e) {
            logger.error("loadShopCategory exception. mtShopId: {}", mtShopId, e);
            return null;
        }
        if (CollectionUtils.isEmpty(typeHierarchyViews)) {
            return null;
        }
        Integer firstCategory = typeHierarchyViews.get(typeHierarchyViews.size() - 1).getId();
        Integer secondCategory = null;
        if (typeHierarchyViews.size() > 1) {
            secondCategory = typeHierarchyViews.get(typeHierarchyViews.size() - 2).getId();
        }

        return Pair.of(firstCategory, secondCategory);
    }

    public Long getMtShopId(long dpShopId) {
        try {
            List<Long> mtShopIds = poiRelationCacheService.queryMtByDpIdL(dpShopId);
            return CollUtils.getFirst(mtShopIds, 0);
        } catch (Exception e) {
            logger.error("getMtShopId exception!", e);
        }
        return null;
    }

}
