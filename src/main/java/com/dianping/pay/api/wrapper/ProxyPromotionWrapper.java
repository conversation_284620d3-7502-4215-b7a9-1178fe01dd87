package com.dianping.pay.api.wrapper;

import com.dianping.cat.Cat;
import com.dianping.deal.base.dto.DealGroupBaseDTO;
import com.dianping.lion.client.Lion;
import com.dianping.mobile.framework.datatypes.IMobileContext;
import com.dianping.pay.api.beans.ProductDetailDO;
import com.dianping.pay.api.beans.ProductItemDetailDO;
import com.dianping.pay.api.biz.activity.newloader.dto.CouponActivityContext;
import com.dianping.pay.api.biz.activity.newloader.dto.ProxyPromoContext;
import com.dianping.pay.api.constants.ProxyConstant;
import com.dianping.pay.api.enums.QueryShopCouponTypeEnum;
import com.dianping.pay.api.service.impl.ProductService;
import com.dianping.pay.api.util.*;
import com.google.common.collect.Lists;
import com.sankuai.nib.mkt.common.base.enums.RulePropertyTypeEnum;
import com.sankuai.nib.mkt.common.base.enums.propertyValueType.MeiDianSourceEnum;
import com.sankuai.nibmktproxy.queryclient.proxy.*;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2021/11/10
 */
@Service
public class ProxyPromotionWrapper {

    public static final Logger logger = LoggerFactory.getLogger(ProxyPromotionWrapper.class);

    @Autowired
    private PromotionProxyService.Iface promotionProxyService;

    @Autowired
    private LogUtils logUtils;

    @Resource
    private ProductService productService;

    public PromotionDTOResult getProxyPromotions(CouponActivityContext promoCtx, IMobileContext iMobileContext, final ProxyPromoContext proxyPromoContext) {
        Cat.logEvent("INVALID_METHOD_5", "com.dianping.pay.api.wrapper.ProxyPromotionWrapper.getProxyPromotions(CouponActivityContext,IMobileContext,ProxyPromoContext)");
        try {
            PromotionRequest request = buildPromoProxyRequest(promoCtx, iMobileContext, proxyPromoContext);
            if (request == null) {
                return null;
            }
            PromotionResponse response = promotionProxyService.getPromotions(request);
            logUtils.logInfo("ProxyCouponProcessor# call getPromotions. request: {}, response: {}", JsonUtils.toJson(request), JsonUtils.toJson(response));
            if (response == null) {
                DotUtils.dotForPromoCallFail(proxyPromoContext.getPromoName(), "getPromotions", promoCtx.isMt());
                logger.warn("getPromotions failed. request: {}, response: {}", request, response);
                return null;
            }
            if (MapUtils.isEmpty(response.getPromotionMap())) {
                DotUtils.dotForPromoHit(proxyPromoContext.getPromoName(), promoCtx.isMt(), false);
                return null;
            }

            int productId;
            if (promoCtx.getQueryShopCouponType() == QueryShopCouponTypeEnum.BY_DEAL.getCode()) {
                if (promoCtx.isMt()) {
                    productId = promoCtx.getMtDealGroupId();
                } else {
                    productId = promoCtx.getDpDealGroupId();
                }
            } else {
                productId = promoCtx.getSkuId();
            }
            PromotionDTOResult promotionDTOResult = response.getPromotionMap().get(String.valueOf(productId));
            if (promotionDTOResult == null || CollectionUtils.isEmpty(promotionDTOResult.getGetPromotionDTO())) {
                DotUtils.dotForPromoHit(proxyPromoContext.getPromoName(), promoCtx.isMt(), false);
            } else {
                DotUtils.dotForPromoHit(proxyPromoContext.getPromoName(), promoCtx.isMt(), true);
            }
            return promotionDTOResult;
        } catch (Exception e) {
            logger.error("getProxyPromotions error. ctx: {}", promoCtx, e);
        }
        return null;
    }

    // 请求参数拼接
    private PromotionRequest buildPromoProxyRequest(CouponActivityContext ctx, IMobileContext iMobileContext, ProxyPromoContext proxyPromoContext) {
        Cat.logEvent("INVALID_METHOD_3", "com.dianping.pay.api.wrapper.ProxyPromotionWrapper.buildPromoProxyRequest(CouponActivityContext,IMobileContext,ProxyPromoContext)");
        PromotionRequest promotionRequest = new PromotionRequest();

        promotionRequest.setSceneId(proxyPromoContext.getSceneId());
        promotionRequest.setBizCode(proxyPromoContext.getBizCode());
        promotionRequest.setRequestStrategy(RequestStrategy.NORMAL);

        //商品信息
        RequestItem requestItem = buildRequestItem(ctx, proxyPromoContext);
        if (requestItem == null) {
            return null;
        }
        promotionRequest.setRequestItemList(Lists.newArrayList(requestItem));
        //用户信息
        promotionRequest.setUserInfo(buildUserInfo(ctx));
        //通用属性
        promotionRequest.setCommonProperties(buildCommonInfo(ctx, proxyPromoContext));

        return promotionRequest;
    }

    private RequestItem buildRequestItem(CouponActivityContext ctx, ProxyPromoContext proxyPromoContext) {
        Cat.logEvent("INVALID_METHOD_3", "com.dianping.pay.api.wrapper.ProxyPromotionWrapper.buildRequestItem(CouponActivityContext,ProxyPromoContext)");
        RequestItem requestItem = new RequestItem();

        List<Property> properties = Lists.newArrayListWithExpectedSize(3);

        BigDecimal price = getPrice(ctx);
        long productId = getProductId(ctx);

        if (productId <= 0 || price == null) {
            return null;
        }
        requestItem.setItemId(String.valueOf(productId));

        //价格
        properties.add(new Property(RulePropertyTypeEnum.price.getCode(), price.multiply(new BigDecimal(100)).toString()));
        //商品ID
        properties.add(new Property(RulePropertyTypeEnum.productId.getCode(), String.valueOf(productId)));

        long shopId;
        if (ctx.isMt()) {
            shopId = ctx.getMtShopIdL();
        } else {
            shopId = ctx.getDpShopIdL();
        }
        //门店
        properties.add(new Property(RulePropertyTypeEnum.poiId.getCode(), String.valueOf(shopId)));

        if (proxyPromoContext.isCouponQueryScene()) {
            BigDecimal commissionRate = null;
            if (ctx.getQueryShopCouponType() == QueryShopCouponTypeEnum.BY_DEAL.getCode()) {
                boolean dealSwitch = Lion.getBoolean(LionConstants.APP_KEY, LionConstants.DEAL_COMMISSION_MOVE_SWITCH, true);
                commissionRate = productService.getDealGroupCommissionRate(ctx.getDpDealGroupId(), dealSwitch);
                if (commissionRate != null) {
                    Cat.logEvent(ProxyConstant.COMMISSION_CAT, "commission_" + ctx.getQueryShopCouponType());
                } else {
                    Cat.logEvent(ProxyConstant.COMMISSION_CAT, "commission_" + ctx.getQueryShopCouponType(), "1", "");
                }
            }
            //佣金
            if (commissionRate != null) {
                properties.add(new Property(RulePropertyTypeEnum.commissionRate.getCode(), commissionRate.toString()));
            }
        }

        requestItem.setProperties(properties);
        return requestItem;
    }

    private UserInfo buildUserInfo(CouponActivityContext ctx) {
        Cat.logEvent("INVALID_METHOD_1", "com.dianping.pay.api.wrapper.ProxyPromotionWrapper.buildUserInfo(com.dianping.pay.api.biz.activity.newloader.dto.CouponActivityContext)");
        UserInfo userInfo = new UserInfo();
        long userId;
        if (ctx.isMt()) {
            userId = ctx.getMtUserId();
        } else {
            userId = ctx.getDpUserId();
        }
        userInfo.setUserId(userId);
        userInfo.setDeviceId(ctx.getDpid());
        return userInfo;
    }

    private List<Property> buildCommonInfo(CouponActivityContext ctx, ProxyPromoContext proxyPromoContext) {
        Cat.logEvent("INVALID_METHOD_1", "com.dianping.pay.api.wrapper.ProxyPromotionWrapper.buildCommonInfo(CouponActivityContext,ProxyPromoContext)");
        List<Property> commonProperties = Lists.newArrayList();

        Property templateIdProperty = new Property();
        templateIdProperty.setCode(RulePropertyTypeEnum.templateId.getCode());
        templateIdProperty.setValue(String.valueOf(ProxyConstant.PROMO_TEMP_ID));
        commonProperties.add(templateIdProperty);

        //城市id
        commonProperties.add(new Property(RulePropertyTypeEnum.browseMtCityId.getCode(), String.valueOf(ctx.getCityId())));
        //客户端
        commonProperties.add(new Property(RulePropertyTypeEnum.clientTp.getCode(), ClientTypeUtils.getClientTypeByPayPlatform(ctx.getCouponPlatform(), ctx.isMt())));
        //点评or美团
        commonProperties.add(new Property(RulePropertyTypeEnum.MeiDianSource.getCode(), ctx.isMt() ? MeiDianSourceEnum.MT.getValue() : MeiDianSourceEnum.DP.getValue()));
        //ReturnControl
        commonProperties.add(new Property(RulePropertyTypeEnum.returnComposition.getCode(), "true"));
        //retAll
        commonProperties.add(new Property(RulePropertyTypeEnum.retAll.getCode(), "true"));

        return commonProperties;
    }

    private long getProductId(CouponActivityContext ctx) {
        Cat.logEvent("INVALID_METHOD_2", "com.dianping.pay.api.wrapper.ProxyPromotionWrapper.getProductId(com.dianping.pay.api.biz.activity.newloader.dto.CouponActivityContext)");
        long productId = 0L;
        if (ctx.getQueryShopCouponType() == QueryShopCouponTypeEnum.BY_DEAL.getCode()) {
            DealGroupBaseDTO dealGroup = ctx.getDealGroupBaseDTO();
            if (dealGroup != null) {
                if (ctx.isMt()) {
                    productId = ctx.getMtDealGroupId();
                } else {
                    productId = ctx.getDpDealGroupId();
                }
            }
        }
        // 除此之外都是异常情况
        return productId;
    }

    private BigDecimal getPrice(CouponActivityContext ctx) {
        Cat.logEvent("INVALID_METHOD_5", "com.dianping.pay.api.wrapper.ProxyPromotionWrapper.getPrice(com.dianping.pay.api.biz.activity.newloader.dto.CouponActivityContext)");
        BigDecimal price = null;
        if (ctx.getQueryShopCouponType() == QueryShopCouponTypeEnum.BY_DEAL.getCode()) {
            DealGroupBaseDTO dealGroup = ctx.getDealGroupBaseDTO();
            if (dealGroup != null) {
                price = dealGroup.getDealGroupPrice();
            }
        } else {
            ProductDetailDO productDetailDO = ctx.getProductDetailDO();
            if (productDetailDO != null) {
                List<ProductItemDetailDO> itemDetailDOS = productDetailDO.getProductItems();
                if (CollectionUtils.isNotEmpty(itemDetailDOS)) {
                    for (ProductItemDetailDO productItemDetailDO : itemDetailDOS) {
                        if (productItemDetailDO.getId() == ctx.getSkuId()) {
                            price = productItemDetailDO.getPrice();
                            break;
                        }
                    }
                    if (price == null) {
                        price = itemDetailDOS.get(0).getPrice();
                    }
                }
            }
        }
        return price;
    }
}
