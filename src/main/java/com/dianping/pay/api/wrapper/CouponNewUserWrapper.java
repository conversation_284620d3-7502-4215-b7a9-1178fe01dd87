package com.dianping.pay.api.wrapper;

import com.dianping.cat.Cat;
import com.dianping.gmkt.coupon.common.api.enums.CouponGroupUserRuleDimension;
import com.dianping.pay.api.beans.MerchantNewUserValidateResult;
import com.dianping.pay.promo.common.enums.coupon.UnifiedCouponProductType;
import com.dianping.unified.coupon.manage.api.response.UnifiedCouponManageResponse;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.sankuai.mpmkt.coupon.execute.api.UnifiedCouponExecuteValidateService;
import com.sankuai.mpmkt.coupon.execute.api.dto.ProductNewUserValidateResp;
import com.sankuai.mpmkt.coupon.execute.api.dto.UCEGeneralSkuInfo;
import com.sankuai.mpmkt.coupon.execute.api.dto.UCEQueryContext;
import com.sankuai.mpmkt.coupon.execute.api.response.CouponGroupURValidateResp;
import com.sankuai.mpmkt.coupon.execute.api.response.MerchantUserRuleValidateResp;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2022/7/21
 */
@Service
public class CouponNewUserWrapper {

    public static final Logger logger = LoggerFactory.getLogger(CouponNewUserWrapper.class);

    @Resource
    private UnifiedCouponExecuteValidateService unifiedCouponExecuteValidateService;

    public MerchantNewUserValidateResult getMerchantNewUserResult(List<Integer> couponGroupIds, long userId, int userType, int productCode, List<Long> productIds, int type) {
        MerchantNewUserValidateResult merchantNewUserValidateResult = new MerchantNewUserValidateResult();
        Map<Integer, Boolean> brandNewUserValidateResult = Maps.newHashMap();
        Map<Long, List<Integer>> productUnPassActivityMap = Maps.newHashMap();


        MerchantUserRuleValidateResp merchantUserRuleValidateResp = validateMerchantNewUser(couponGroupIds, userId, userType, productCode, productIds, type);
        if (merchantUserRuleValidateResp != null && MapUtils.isNotEmpty(merchantUserRuleValidateResp.getValidateRespMap())) {
            Map<Integer, CouponGroupURValidateResp> validateRespMap = merchantUserRuleValidateResp.getValidateRespMap();
            for (Integer couponGroupId : couponGroupIds) {
                if (!validateRespMap.containsKey(couponGroupId)) {
                    continue;
                }
                CouponGroupURValidateResp couponGroupURValidateResp = validateRespMap.get(couponGroupId);
                if (couponGroupURValidateResp.getMerchantUserRule() == CouponGroupUserRuleDimension.BRAND_NEW_USER.getValue() &&
                        couponGroupURValidateResp.getBrandNewUserValidateResp() != null) {
                    brandNewUserValidateResult.put(couponGroupId, couponGroupURValidateResp.getBrandNewUserValidateResp().isValidatePass());
                }
                if (couponGroupURValidateResp.getMerchantUserRule() == CouponGroupUserRuleDimension.PRODUCT_NEW_USER.getValue() &&
                        CollectionUtils.isNotEmpty(couponGroupURValidateResp.getDetailProductValidateInfos())) {
                    for (ProductNewUserValidateResp productNewUserValidateResp : couponGroupURValidateResp.getDetailProductValidateInfos()) {
                        if (productNewUserValidateResp.isValidatePass()) {
                            continue;
                        }
                        Long key = productNewUserValidateResp.getProductId();
                        if (productUnPassActivityMap.containsKey(key)) {
                            productUnPassActivityMap.get(key).add(couponGroupId);
                        } else {
                            productUnPassActivityMap.put(key, Lists.newArrayList(couponGroupId));
                        }
                    }
                }
            }
        }
        merchantNewUserValidateResult.setBrandNewUserValidateResult(brandNewUserValidateResult);
        merchantNewUserValidateResult.setProductUnPassActivityMap(productUnPassActivityMap);
        return merchantNewUserValidateResult;
    }

    public MerchantUserRuleValidateResp validateMerchantNewUser(List<Integer> couponGroupIds, long userId, int userType, int productCode, List<Long> productIds, int type) {
        try {
            if (userId <= 0) {
                Cat.logEvent("CouponNewUser", "userIdEmpty", "-1", "");
                return null;
            }
            UCEQueryContext uceQueryContext = new UCEQueryContext();
            uceQueryContext.setUserId(userId);
            uceQueryContext.setUserType(userType);
            uceQueryContext.setProductCode(productCode);
            if (CollectionUtils.isNotEmpty(productIds)) {
                List<UCEGeneralSkuInfo> generalSkuInfoList = Lists.newArrayListWithExpectedSize(productIds.size());
                for (Long productId : productIds) {
                    UCEGeneralSkuInfo generalSkuInfo = new UCEGeneralSkuInfo();
                    if (type == UnifiedCouponProductType.PRODUCTGROUPID.code) {
                        generalSkuInfo.setProductId(productId);
                    } else {
                        generalSkuInfo.setSkuId(productId);
                    }
                    generalSkuInfoList.add(generalSkuInfo);
                }
                uceQueryContext.setGeneralSkuInfoList(generalSkuInfoList);
            }
            Cat.logEvent("CouponNewUser", "successRequest");
            UnifiedCouponManageResponse<MerchantUserRuleValidateResp> response = unifiedCouponExecuteValidateService.batchValidateMerchantUserRule(couponGroupIds, uceQueryContext);
            if (response == null || !response.isSuccess() || response.getResult() == null) {
                logger.error("batchValidateMerchantUserRule fail, couponGroupIds:{}, userId:{}, userType:{}, productCode:{}, productIds:{}, response:{}",
                        couponGroupIds, userId, userType, productCode, productIds, response);
                return null;
            }
            return response.getResult();
        } catch (Exception e) {
            logger.error("batchValidateMerchantUserRule ex, couponGroupIds:{}, userId:{}, userType:{}, productCode:{}, productIds:{}, response:{}, ",
                    couponGroupIds, userId, userType, productCode, productIds, e);
        }
        return null;
    }

}