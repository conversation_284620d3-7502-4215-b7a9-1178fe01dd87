package com.dianping.pay.api.biz.promodesk;

import com.dianping.api.util.StringUtil;
import com.dianping.cat.Cat;
import com.dianping.pay.account.enums.AccountCatalog;
import com.dianping.pay.account.model.UserAccountStatisticsDTO;
import com.dianping.pay.api.beans.DiscountRules;
import com.dianping.pay.api.beans.GetPromoDeskRequest;
import com.dianping.pay.api.biz.user.UserAccountBiz;
import com.dianping.pay.api.entity.promodesk.GiftCardPromoTool;
import com.dianping.pay.api.entity.promodesk.HongBaoPromoTool;
import com.dianping.pay.api.entity.promodesk.PointPromoTool;
import com.dianping.pay.common.enums.ProductCode;
import com.dianping.pay.promo.execute.service.dto.PromoToolDTO;
import com.dianping.pay.promo.execute.service.dto.response.PromoQueryResponse;
import com.dianping.pay.promo.execute.service.enums.PromoToolType;
import com.google.common.collect.Lists;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

/**
 * 团购优惠台加载策略
 * 返回：多个立减、抵用券、商户抵用券、礼品卡
 * 不返回：红包、积分、小黄条
 */
public class TuangouPcPromoDeskStrategy extends CommonPromoDeskStrategy implements PromoDeskStrategy {

    @Resource
    protected UserAccountBiz userAccountBiz;

    @Override
    public GiftCardPromoTool assembleGiftCardPromoTool(GetPromoDeskRequest getPromoDeskRequest, DiscountRules discountRules) {
        Cat.logEvent("INVALID_METHOD_1", "com.dianping.pay.api.biz.promodesk.TuangouPcPromoDeskStrategy.assembleGiftCardPromoTool(com.dianping.pay.api.beans.GetPromoDeskRequest,com.dianping.pay.api.beans.DiscountRules)");
        if (getPromoDeskRequest.getUserId() > 0) {
            UserAccountStatisticsDTO accountStatistics = userAccountBiz.getAccountStatistics(getPromoDeskRequest.getUserId(), AccountCatalog.GIFT_CARD);
            if (accountStatistics != null) {
                BigDecimal hbAmount = accountStatistics.getCatalogBalance(AccountCatalog.GIFT_CARD.getCode());
                if (hbAmount != null && hbAmount.signum() == 1) {
                    GiftCardPromoTool giftCardPromoTool = new GiftCardPromoTool();
                    giftCardPromoTool.setCanUse(true);
                    giftCardPromoTool.setBalance(hbAmount.doubleValue());
                    giftCardPromoTool.setProductCodes(Lists.newArrayList(ProductCode.TUANGOU.getCode()));

                    PromoToolDTO promoToolDTO = new PromoToolDTO();
                    promoToolDTO.setNewId(String.valueOf(getPromoDeskRequest.getUserId()));
                    promoToolDTO.setType(PromoToolType.GIFT_CARD.getCode());
                    promoToolDTO.setProductCodeList(giftCardPromoTool.getProductCodes());
                    promoToolDTO.setUserId(getPromoDeskRequest.getUserId());
                    PromoQueryResponse<List<String>> response = payPromoDeskQueryService.encryptPromoTool(Lists.newArrayList(promoToolDTO), null);
                    if (response.isSuccess() && CollectionUtils.isNotEmpty(response.getResult())) {
                        giftCardPromoTool.setPromoCipher(response.getResult().get(0));
                        return giftCardPromoTool;
                    }
                }
            }
        }
        return null;
    }

    @Override
    public HongBaoPromoTool assembleHongBaoPromoTool(GetPromoDeskRequest getPromoDeskRequest, DiscountRules discountRules, Map<Integer, DiscountRules> productCodeDiscountRule) {
        Cat.logEvent("INVALID_METHOD_2", "com.dianping.pay.api.biz.promodesk.TuangouPcPromoDeskStrategy.assembleHongBaoPromoTool(com.dianping.pay.api.beans.GetPromoDeskRequest,com.dianping.pay.api.beans.DiscountRules,java.util.Map)");
        return null;
    }
}
