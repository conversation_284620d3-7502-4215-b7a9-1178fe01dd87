package com.dianping.pay.api.biz.activity.newloader;

import com.dianping.cat.Cat;
import com.dianping.gmkt.activity.enums.AppPlatform;
import com.dianping.gmkt.data.base.api.dto.CouponInfoDTO;
import com.dianping.gmkt.data.base.api.enums.BaseErrorCode;
import com.dianping.gmkt.data.base.api.request.PlatformCouponByShopRequest;
import com.dianping.gmkt.data.base.api.request.PlatformCouponRequest;
import com.dianping.gmkt.data.base.api.response.PlatformCouponResponse;
import com.dianping.gmkt.data.base.api.service.PlatformCouponService;
import com.dianping.gmkt.wave.api.dto.response.UnifiedCouponProductLimitInfo;
import com.dianping.gmkt.wave.api.enums.ActivityChannelEnum;
import com.dianping.gmkt.wave.api.enums.CouponQuerySource;
import com.dianping.pay.api.util.BusinessLineCheckUtils;
import com.dianping.pay.api.beans.PlatformCoupon;
import com.dianping.pay.api.biz.activity.newloader.dto.CouponIssueActivityQueryContext;
import com.dianping.pay.api.util.JsonUtils;
import com.dianping.pay.api.util.PoiIdUtils;
import com.dianping.pay.promo.common.enums.coupon.UnifiedCouponProductType;
import org.apache.commons.collections.CollectionUtils;
import com.google.common.collect.Lists;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang.Validate;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

/**
 * Created by ivan on 2018/12/26.
 */
@Service("platformCouponQuerySerivce")
public class PlatformCouponQuerySerivce {
    private static final Logger log = LoggerFactory.getLogger(PlatformCouponQuerySerivce.class);
    //设置分页查询
    private static final int PAGINATE_QUERY = 1;
    //todo 每页记录数 最大不超过40，否则会截取 待产品确认
    private static final int MAX_PLATFORM_COUPON_TO_QUERY = 20;

    @Autowired
    private PlatformCouponService platformCouponService;

    public List<PlatformCoupon> queryPlatformCoupons(CouponIssueActivityQueryContext context) {
        List<PlatformCoupon> result = Lists.newArrayList();
        log.info("[PlatformCouponQueryService][queryPlatformCoupons],context={}", context);

        try {
            PlatformCouponResponse response = new PlatformCouponResponse();
            if (context.getBusinessLine() > 0) {
                Validate.isTrue(context.getIssueChannel() > 0, "invalid issueChannel!");
                Validate.isTrue(BusinessLineCheckUtils.businessLine2ActivityChannel(context.getBusinessLine(), context.getIssueChannel()), "businessLine not matched with issueChannel!");
                PlatformCouponRequest request = generatePlatformCouponQueryRequest(context);
                response = platformCouponService.getCouponNew(request);
            } else {
                PlatformCouponByShopRequest request = generateQueryPlatformCouponByShopRequest(context);
                response = platformCouponService.getCouponByShopId(request);
            }
            if (!StringUtils.equals(response.getResult(), BaseErrorCode.SUCCESS.getCode())) {
                log.warn("query platform coupon failed, context={}, msg={}", context, response.getResultMessage());
                return result;
            }
            if (CollectionUtils.isEmpty(response.getCouponDTOList())) {
                log.info("no available platform coupons", context);
                return result;
            }
            filterCouponByShopId(response, context.getShopIdL());
            for (CouponInfoDTO couponInfoDTO : response.getCouponDTOList()) {
                result.add(toPlatformCouponQueryResponse(couponInfoDTO, context.isDpClient()));
            }

        } catch (Exception e) {
            log.error("[PlatformCouponQueryService][queryPlatformCoupons][query platform coupons error]", e);
        }
        return result;
    }

    private PlatformCouponRequest generatePlatformCouponQueryRequest(CouponIssueActivityQueryContext context) {
        Cat.logEvent("INVALID_METHOD_2", "com.dianping.pay.api.biz.activity.newloader.PlatformCouponQuerySerivce.generatePlatformCouponQueryRequest(com.dianping.pay.api.biz.activity.newloader.dto.CouponIssueActivityQueryContext)");
        PlatformCouponRequest platformCouponRequest = new PlatformCouponRequest();
        platformCouponRequest.setUserIdL(context.getUserId());
        platformCouponRequest.setBusinessLine(context.getBusinessLine());
        platformCouponRequest.setActivityChannel(context.getIssueChannel());
        platformCouponRequest.setPlatform(context.isDpClient() ? AppPlatform.DP.getCode() : AppPlatform.MT.getCode());
        platformCouponRequest.setOffset(PAGINATE_QUERY);
        platformCouponRequest.setLimit(MAX_PLATFORM_COUPON_TO_QUERY);
        platformCouponRequest.setUuid(context.getUuid());
        platformCouponRequest.setDpid(context.getDpid());
        platformCouponRequest.setMobileNo(context.getMobileNo());
        platformCouponRequest.setCouponPlatforms(context.getCouponPlatforms());
        platformCouponRequest.setCouponQuerySource(CouponQuerySource.COUPON_COMPONENT.getCode());
        return platformCouponRequest;
    }

    private void filterCouponByShopId(PlatformCouponResponse response, long shopId){
        Cat.logEvent("INVALID_METHOD_3", "com.dianping.pay.api.biz.activity.newloader.PlatformCouponQuerySerivce.filterCouponByShopId(com.dianping.gmkt.data.base.api.response.PlatformCouponResponse,long)");
        List<CouponInfoDTO> couponInfoDTOs = new ArrayList<>();

        for(CouponInfoDTO couponInfoDTO : response.getCouponDTOList()){
            if( ! needFilterByShopId(couponInfoDTO.getUnifiedCouponProductLimitInfoList(),shopId)){
                couponInfoDTOs.add(couponInfoDTO);
            }
        }
        response.setCouponDTOList(couponInfoDTOs);
    }

    private boolean needFilterByShopId(List<UnifiedCouponProductLimitInfo> unifiedCouponProductLimitInfoList, long shopId) {
        Cat.logEvent("INVALID_METHOD_1", "com.dianping.pay.api.biz.activity.newloader.PlatformCouponQuerySerivce.needFilterByShopId(java.util.List,long)");
        List<Long> shopIdLimitList = Lists.newArrayList();
        if (CollectionUtils.isEmpty(unifiedCouponProductLimitInfoList)) {
            //无限制条件的不需要过滤
            return false;
        } else {
            //获取优惠券限制的所有shopId，限制为其他类型的不进行过滤
            for (UnifiedCouponProductLimitInfo unifiedCouponProductLimitInfo : unifiedCouponProductLimitInfoList) {
                if (unifiedCouponProductLimitInfo.getType() == UnifiedCouponProductType.SHOPID.code) {
                    shopIdLimitList.add(unifiedCouponProductLimitInfo.getProductIdL());
                }
            }
            //shopIdLimitList为空表示限制维度不是门店，不进行过滤
            //shopIdLimitList不为空且不包含当前门店时，需要过滤
            return CollectionUtils.isNotEmpty(shopIdLimitList) && !shopIdLimitList.contains(shopId);
        }
    }
    private PlatformCouponByShopRequest generateQueryPlatformCouponByShopRequest(CouponIssueActivityQueryContext context) {
        PlatformCouponByShopRequest platformCouponByShopRequest = new PlatformCouponByShopRequest();
        platformCouponByShopRequest.setUserIdL(context.getUserId());
        //native 活动渠道设置为通用领券组件
        platformCouponByShopRequest.setActivityChannel(ActivityChannelEnum.COMMON.getCode());
        platformCouponByShopRequest.setPlatform(context.isDpClient() ? AppPlatform.DP.getCode() : AppPlatform.MT.getCode());
        platformCouponByShopRequest.setOffset(PAGINATE_QUERY);
        platformCouponByShopRequest.setLimit(MAX_PLATFORM_COUPON_TO_QUERY);
        platformCouponByShopRequest.setUuid(context.getUuid());
        platformCouponByShopRequest.setDpid(context.getDpid());
        platformCouponByShopRequest.setMobileNo(context.getMobileNo());
        platformCouponByShopRequest.setCouponPlatforms(context.getCouponPlatforms());
        platformCouponByShopRequest.setCouponQuerySource(CouponQuerySource.COUPON_COMPONENT.getCode());
        platformCouponByShopRequest.setShopId(PoiIdUtils.poiIdLongToInt(context.getShopIdL()));
        platformCouponByShopRequest.setShopIdL(context.getShopIdL());
        return platformCouponByShopRequest;
    }

    private PlatformCoupon toPlatformCouponQueryResponse(CouponInfoDTO couponInfoDTO, boolean isDpClient) {
        Cat.logEvent("INVALID_METHOD_5", "com.dianping.pay.api.biz.activity.newloader.PlatformCouponQuerySerivce.toPlatformCouponQueryResponse(com.dianping.gmkt.data.base.api.dto.CouponInfoDTO,boolean)");
        PlatformCoupon platformCoupon = new PlatformCoupon();
        platformCoupon.setUnifiedCouponGroupId(couponInfoDTO.getCouponId());
        platformCoupon.setUnifiedCouponId(couponInfoDTO.getUnifiedCouponId());
        platformCoupon.setAmount(couponInfoDTO.getCouponAmountValue());
        platformCoupon.setPriceLimit(couponInfoDTO.getCouponLimitValue() == null ? 0 : Double.valueOf(couponInfoDTO.getCouponLimitValue()));
        platformCoupon.setCouponTitle(couponInfoDTO.getCouponTitle());
        platformCoupon.setStatus(couponInfoDTO.getCouponStatus());
        platformCoupon.setToUseUrl(couponInfoDTO.getToUseUrl());
        platformCoupon.setDesc(couponInfoDTO.getCouponRule());
        platformCoupon.setDpClient(isDpClient);
        platformCoupon.setProductCodes(couponInfoDTO.getProductCodes());
        platformCoupon.setStartTime(couponInfoDTO.getStartTime());
        platformCoupon.setEndTime(couponInfoDTO.getEndTime());
        platformCoupon.setExpireType(couponInfoDTO.getExpireType());
        platformCoupon.setFloatDay(couponInfoDTO.getFloatDay());
        platformCoupon.setMaxPerUser(couponInfoDTO.getMaxPerUser());
        platformCoupon.setCouponIssued(couponInfoDTO.getCouponIssued());
        platformCoupon.setCouponAvailable(couponInfoDTO.getCouponAvailable());
        platformCoupon.setCouponUsed(couponInfoDTO.getCouponUsed());
        platformCoupon.setCouponExpired(couponInfoDTO.getCouponExpired());

        return platformCoupon;
    }
}
