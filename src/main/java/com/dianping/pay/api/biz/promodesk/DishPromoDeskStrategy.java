package com.dianping.pay.api.biz.promodesk;

import com.dianping.cat.Cat;
import com.dianping.pay.api.beans.DiscountRules;
import com.dianping.pay.api.beans.GetPromoDeskRequest;
import com.dianping.pay.api.entity.promodesk.HongBaoPromoTool;

import java.util.Map;

/**
 * 点菜优惠台加载策略
 * 返回：多个立减、抵用券、商户抵用券
 * 不返回：红包、积分、小黄条
 */
public class DishPromoDeskStrategy extends CommonPromoDeskStrategy implements PromoDeskStrategy {

    @Override
    public HongBaoPromoTool assembleHongBaoPromoTool(GetPromoDeskRequest getPromoDeskRequest, DiscountRules discountRules, Map<Integer, DiscountRules> productCodeDiscountRule) {
        Cat.logEvent("INVALID_METHOD_2", "com.dianping.pay.api.biz.promodesk.DishPromoDeskStrategy.assembleHongBaoPromoTool(com.dianping.pay.api.beans.GetPromoDeskRequest,com.dianping.pay.api.beans.DiscountRules,java.util.Map)");
        return null;
    }
}
