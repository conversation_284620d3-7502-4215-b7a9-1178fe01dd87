package com.dianping.pay.api.biz.activity.newloader.executors;

import com.dianping.cat.Cat;
import com.dianping.pay.api.beans.IssueCouponActivity;
import com.dianping.pay.api.biz.activity.newloader.AbstractCouponIssueActivityQueryExecutor;
import com.dianping.pay.api.entity.issuecoupon.IssueCouponRequest;
import com.dianping.poi.relation.service.api.PoiRelationService;
import com.dianping.spc.common.Response;
import com.dianping.tgc.open.SpuActivityQueryRemoteService;
import com.dianping.tgc.open.entity.ActivityDTO;
import com.dianping.tgc.open.entity.ActivityQueryRequest;
import com.dianping.tgc.open.entity.QueryResponseDTO;
import com.dianping.tgc.open.enums.QuerySourceEnum;
import com.google.common.collect.Lists;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class SpuShopActivityQueryExecutor extends AbstractCouponIssueActivityQueryExecutor {

    private SpuActivityQueryRemoteService spuActivityQueryRemoteService;
    private PoiRelationService poiRelationCacheService;

    public SpuShopActivityQueryExecutor(IssueCouponRequest request) {
        super(request);
    }

    @Override
    protected List<IssueCouponActivity> loadFromRemote() {
        if (request.getShopIdL() <= 0) {
            return Lists.newArrayList();
        }
        List<Long> dpShopIdList = Lists.newArrayList();
        if (request.isDpClient()) {
            dpShopIdList = Lists.newArrayList(request.getShopIdL());
        } else {
            try {
                dpShopIdList = poiRelationCacheService.queryDpByMtIdL(request.getShopIdL());
            } catch (Exception e) {
                Cat.logError("queryDpByMtId", e);
            }
            if (CollectionUtils.isEmpty(dpShopIdList)) {
                return Lists.newArrayList();
            }
        }
        Map<Long, List<ActivityDTO>> activityMap = new HashMap<Long, List<ActivityDTO>>();
        for(Long shopId : dpShopIdList){
            ActivityQueryRequest remoteRequest = new ActivityQueryRequest();
            remoteRequest.setShopIdL(shopId);
            remoteRequest.setSource(request.isDpClient() ? QuerySourceEnum.DP.getValue() : QuerySourceEnum.MT.getValue());
            Response<QueryResponseDTO> response = null;
            try {
                response = spuActivityQueryRemoteService.queryActivityDTOsByShopId(remoteRequest);
            } catch (Exception e){
                Cat.logError("queryActivityDTOsByShopId", e);
            }
            if(response == null || !response.isSuccess() || response.getResult() == null){
                continue;
            }
            activityMap.put(shopId, response.getResult().getActivityDTOs());
        }
        if (MapUtils.isEmpty(activityMap)) {
            return Lists.newArrayList();
        }
        return toIssueCouponActivitiesL(activityMap);
    }

    public SpuShopActivityQueryExecutor setSpuActivityQueryRemoteService(SpuActivityQueryRemoteService spuActivityQueryRemoteService) {
        this.spuActivityQueryRemoteService = spuActivityQueryRemoteService;
        return this;
    }

    public SpuShopActivityQueryExecutor setPoiRelationCacheService(PoiRelationService poiRelationCacheService) {
        this.poiRelationCacheService = poiRelationCacheService;
        return this;
    }


}
