package com.dianping.pay.api.biz.activity.newloader.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @date 2025/3/21
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class AbTestRequest {

    /**
     * 编码
     */
    private String abCode;

    /**
     * 用户ID
     */
    private String userId;

    /**
     * 设备ID
     */
    private String uuid;

    private String dpId;

    /**
     * app版本
     */
    private String appVersion;

    private Integer appId;

    /**
     * 系统
     */
    private String os;

    /**
     * 城市ID
     */
    private Integer cityId;

    /**
     * 平台,美团 点评
     */
    private String platform;

    /**
     * 是否小程序
     */
    private boolean miniProgramFlag;
}
