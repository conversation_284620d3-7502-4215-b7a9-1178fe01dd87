package com.dianping.pay.api.biz.activity.newloader.dto;

import com.dianping.deal.base.dto.DealGroupBaseDTO;
import com.dianping.mobile.framework.datatypes.IMobileContext;
import com.dianping.pay.api.beans.IssueCouponActivity;
import com.dianping.pay.api.entity.issuecoupon.ReturnPromotionDisplayDto;
import com.dianping.pay.promo.common.enums.User;
import com.dianping.pay.promo.display.api.dto.PromoDisplayDTO;
import com.sankuai.mkt.activity.api.merchantsharecoupon.model.MerchantShareCouponExposeDTO;
import com.sankuai.nibmktproxy.queryclient.proxy.PromotionDTOResult;
import com.sankuai.nibmktproxy.queryclient.proxy.PromotionResponse;
import lombok.Data;
import org.apache.commons.lang3.tuple.Pair;

import java.util.List;
import java.util.concurrent.Future;

@Data
public class PromoCtx {

    private IMobileContext iMobileContext;

    private int cityId;

    private long dpUserId;
    private long mtUserId;


    private long dpShopIdL;

    private long mtShopIdL;

    //0:点评 1:美团
    private User userType;

    private int skuId;
    private int dpDealGroupId;
    private int mtDealGroupId;

    //0：根据门店查询对应 1：团购 2：商品
    private int queryShopCouponType;
    private int payPlatform;

    private boolean isDealPromoProxy;

    //是否需要返回返礼信息
    private boolean needReturnPromotion;
    //是否需要返回分享券信息
    private boolean needShareCouponPromotion;
    //是否需要返回立减信息
    private boolean needReductionPromotion;




    //存放future信息
    private Future<List<IssueCouponActivity>> tgcCouponPromotionFuture;
    private Future<ReturnPromotionDisplayDto> returnPromotionFuture;
    private Future<List<MerchantShareCouponExposeDTO>> shareCouponPromotionFuture;
    private Future<PromotionDTOResult> proxyPromotionFuture;
    private Future<Pair<List<PromoDisplayDTO>, List<PromoDisplayDTO>>> reductionPromotionFuture;


    //存放future get后的信息
    private List<IssueCouponActivity> issueCouponActivities;
    private ReturnPromotionDisplayDto returnPromotionDisplayDto;
    private List<MerchantShareCouponExposeDTO> shareCouponExposes;
    private PromotionDTOResult promotionDTOResult;
    private Pair<List<PromoDisplayDTO>, List<PromoDisplayDTO>> reductionPromotions;


    //一些基本信息
    private DealGroupBaseDTO dealGroupBaseDTO;


    public boolean isMt(){
        return userType == User.MT;
    }


    public long getUserId(){
        return isMt()?mtUserId:dpUserId;
    }

    public long getShopIdL(){
        return isMt() ? mtShopIdL : dpShopIdL;
    }

    public int getDealGroupId(){
        return isMt()?mtDealGroupId:dpDealGroupId;
    }

}
