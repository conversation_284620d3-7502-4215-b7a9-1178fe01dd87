package com.dianping.pay.api.biz.activity.newloader.dto;

import lombok.Data;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2025/3/20
 */
@Data
public class AbTestConfig {

    /**
     * 实验id
     */
    private String expId;

    /**
     * 实验名称
     */
    private String expName;

    /**
     * 实验编码
     */
    private String abCode;

    /**
     * 实验组命中执行策略
     */
    private Map<String, String> expSk2StrategyKey;

    /**
     * 平台
     */
    private String platform;

    /**
     * 排除城市
     */
    private List<Integer> excludeCityIds;

    /**
     * 是否包含小程序
     */
    private boolean includeMiniProgram;
}
