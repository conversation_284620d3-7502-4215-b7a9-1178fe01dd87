package com.dianping.pay.api.biz.promodesk;

import com.dianping.cat.Cat;
import com.dianping.pay.api.beans.GetPromoDeskRequest;
import com.dianping.pay.api.beans.PromoProduct;
import com.dianping.pay.api.entity.promodesk.DiscountPromoEventGroup;
import com.dianping.pay.api.entity.promodesk.DiscountPromoTool;
import com.dianping.pay.common.enums.ProductCode;
import com.google.common.base.Predicate;
import org.apache.commons.collections.CollectionUtils;

import java.util.ArrayList;

/**
 * 闪惠优惠台加载策略
 * 返回：一个立减、抵用券、商户抵用券、红包
 * 不返回：积分、小黄条
 */
public class ShanhuiPromoDeskStrategy extends CommonPromoDeskStrategy implements PromoDeskStrategy {


    private Predicate<PromoProduct> discountProductPredicate = new Predicate<PromoProduct>() {
        @Override
        public boolean apply(PromoProduct promoProduct) {
            return promoProduct.getProductCode() == ProductCode.MO2O2PAY.getCode();
        }
    };

    @Override
    public DiscountPromoTool assembleDiscountPromoTool(GetPromoDeskRequest getPromoDeskRequest) {
        Cat.logEvent("INVALID_METHOD_1", "com.dianping.pay.api.biz.promodesk.ShanhuiPromoDeskStrategy.assembleDiscountPromoTool(com.dianping.pay.api.beans.GetPromoDeskRequest)");
        DiscountPromoTool discountPromoTool = reductionBiz.queryPromoDTOGroup(getPromoDeskRequest, discountProductPredicate);
        if (CollectionUtils.isNotEmpty(discountPromoTool.getDiscountPromoEventGroups())) {
            ArrayList<DiscountPromoEventGroup> discountPromoEventGroups = new ArrayList<DiscountPromoEventGroup>();
            discountPromoEventGroups.add(discountPromoTool.getDiscountPromoEventGroups().get(0));
            discountPromoTool.setDiscountPromoEventGroups(discountPromoEventGroups);
            return discountPromoTool;
        }
        return new DiscountPromoTool();
    }

}
