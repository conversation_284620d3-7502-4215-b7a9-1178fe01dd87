package com.dianping.pay.api.biz.activity.newloader.dto;

import org.apache.commons.lang.builder.ToStringBuilder;

import java.util.List;

/**
 * Created by drin<PERSON> on 18/6/7.
 */

public class CouponIssueActivityQueryContext {
    private boolean dpClient;
    private long shopIdL;
    private int productId;
    private int productType;

    private long userId ;
    @Deprecated
    private int couponGroupId;

    private String unifiedCouponGroupId;
    private String cx;
    //支付渠道
    private List<Integer> couponPlatforms;
    private int issueChannel;
    private int businessLine;
    private String uuid;
    private String dpid;
    private String mobileNo;
    private String serialno;

    public int getCouponGroupId() {
        return couponGroupId;
    }

    public void setCouponGroupId(int couponGroupId) {
        this.couponGroupId = couponGroupId;
    }

    public long getUserId() {
        return userId;
    }

    public void setUserId(long userId) {
        this.userId = userId;
    }

    public boolean isDpClient() {
        return dpClient;
    }

    public void setDpClient(boolean dpClient) {
        this.dpClient = dpClient;
    }

    public long getShopIdL() {
        return shopIdL;
    }

    public void setShopIdL(long shopIdL) {
        this.shopIdL = shopIdL;
    }

    public int getProductId() {
        return productId;
    }

    public void setProductId(int productId) {
        this.productId = productId;
    }

    public int getProductType() {
        return productType;
    }

    public void setProductType(int productType) {
        this.productType = productType;
    }

    public String getUnifiedCouponGroupId() {
        return unifiedCouponGroupId;
    }

    public void setUnifiedCouponGroupId(String unifiedCouponGroupId) {
        this.unifiedCouponGroupId = unifiedCouponGroupId;
    }

    public int getBusinessLine() {
        return businessLine;
    }

    public void setBusinessLine(int businessLine) {
        this.businessLine = businessLine;
    }

    public int getIssueChannel() {
        return issueChannel;
    }

    public void setIssueChannel(int issueChannel) {
        this.issueChannel = issueChannel;
    }

    public String getUuid() {
        return uuid;
    }

    public void setUuid(String uuid) {
        this.uuid = uuid;
    }

    public String getDpid() {
        return dpid;
    }

    public void setDpid(String dpid) {
        this.dpid = dpid;
    }

    public String getMobileNo() {
        return mobileNo;
    }

    public void setMobileNo(String mobileNo) {
        this.mobileNo = mobileNo;
    }

    public List<Integer> getCouponPlatforms() {
        return couponPlatforms;
    }

    public void setCouponPlatforms(List<Integer> couponPlatforms) {
        this.couponPlatforms = couponPlatforms;
    }

    public String getCx() {
        return cx;
    }

    public void setCx(String cx) {
        this.cx = cx;
    }

    public String getSerialno() {
        return serialno;
    }

    public void setSerialno(String serialno) {
        this.serialno = serialno;
    }

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this);
    }
}
