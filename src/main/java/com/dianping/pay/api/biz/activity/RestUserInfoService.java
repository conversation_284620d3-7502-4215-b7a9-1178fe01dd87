package com.dianping.pay.api.biz.activity;

import com.dianping.account.UserAccountService;
import com.dianping.account.dto.VirtualBindUserInfoDTO;
import com.dianping.account.utils.constants.CommonWebConstants;
import com.dianping.account.utils.util.HttpUtils;
import com.dianping.mobile.framework.datatypes.IMobileContext;
import com.dianping.mobile.framework.datatypes.token.UserStatusResult;
import com.dianping.pay.api.biz.activity.newloader.dto.RestUserInfo;
import com.dianping.pay.api.util.LogUtils;
import com.dianping.pay.framework.utils.account.http.MtIsidParseUtils;
import com.dianping.pay.promo.common.enums.User;
import org.apache.commons.lang.ArrayUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang.math.NumberUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service("restUserInfoService")
public class RestUserInfoService {

    private static final Logger LOGGER = LogManager.getLogger(RestUserInfoService.class);

    @Autowired
    private LogUtils logUtils;

    @Autowired
    private UserAccountService userAccountService;

    private static String getCookieToken(IMobileContext context, String key) {
        String cookieToken = HttpUtils.getCookie(context.getRequest(), key);
        if (isValidToken(cookieToken)) {
            return cookieToken;
        }
        return null;
    }

    private static boolean isValidToken(String token) {
        return StringUtils.isNotBlank(token) && token.length() >= 10;
    }

    public RestUserInfo handleMobileContext(IMobileContext mobileContext){
        RestUserInfo restUserInfo = new RestUserInfo();
        // 不传就按点评处理
        int userCode = User.DP.getCode();
        String userCodeStr = mobileContext.getParameter("usercode");
        if (StringUtils.isNotBlank(userCodeStr)) {
            userCode = NumberUtils.toInt(mobileContext.getParameter("usercode"));
        } else {
            // 新框架头解析方式
            userCodeStr =  mobileContext.getRequest().getHeader("appName");
            if (StringUtils.isNotBlank(userCodeStr)) {
                userCode = "meituan".equals(userCodeStr) ? User.MT.getCode() : User.DP.getCode();
            }
        }

        if(!ArrayUtils.contains(new int[]{User.DP.getCode(), User.MT.getCode()}, userCode)){
            return null;
        }
        restUserInfo.setUserCode(userCode);
        String token = getToken(mobileContext, userCode);
        String oops = getCookieToken(mobileContext, "oops");
        if(StringUtils.isBlank(token) && StringUtils.isBlank(oops)){
            LOGGER.info(String.format("token and oops is null, mobileContext :%s", mobileContext));
            return restUserInfo;
        }
        try {
            if (StringUtils.isNotBlank(token)) {
                VirtualBindUserInfoDTO virtualBindUserInfoDTO = userAccountService.loadUserByToken(token, HttpUtils.getUserIp(mobileContext.getRequest()), null);
                logUtils.logInfo("loadUserByToken: token:{}, result:{}", token, virtualBindUserInfoDTO);
                if(virtualBindUserInfoDTO != null){
                    if(userCode == User.MT.getCode()){
                        if(virtualBindUserInfoDTO.getMtid() != null){
                            restUserInfo.setUserId(virtualBindUserInfoDTO.getMtid());
                        }
                    }else{
                        if(virtualBindUserInfoDTO.getDpid() != null) {
                            restUserInfo.setUserId(virtualBindUserInfoDTO.getDpid());
                        }
                    }
                }
            } else if (StringUtils.isNotBlank(oops) && userCode == User.MT.getCode()){
                // 美团老i版没有token，为oops，解析方式和token一致
                VirtualBindUserInfoDTO virtualBindUserInfoDTO = userAccountService.loadUserByToken(oops, HttpUtils.getUserIp(mobileContext.getRequest()), null);
                logUtils.logInfo("loadUserByOops: result:{}", virtualBindUserInfoDTO);
                if(virtualBindUserInfoDTO != null){
                    if(virtualBindUserInfoDTO.getMtid() != null){
                        restUserInfo.setUserId(virtualBindUserInfoDTO.getMtid());
                    }
                }
            }
        }catch (Exception e){
            LOGGER.error(String.format("loadUserByToken: %s exception!", token),  e);
        }

        return restUserInfo;
    }

    public RestUserInfo getDPUserInfoByMtToken(IMobileContext mobileContext){
        UserStatusResult userStatus = mobileContext.getUserStatus();
        if(userStatus == null || userStatus.getMtUserId() <= 0){
            return null;
        }
        String token = getToken(mobileContext, User.MT.getCode());
        if(StringUtils.isBlank(token)){
            return null;
        }
        RestUserInfo restUserInfo = null;
        try {
            VirtualBindUserInfoDTO virtualBindUserInfoDTO = userAccountService.loadUserByToken(token, HttpUtils.getUserIp(mobileContext.getRequest()), null);
            logUtils.logInfo("loadUserByToken[DP]: result:{}", virtualBindUserInfoDTO);
            if(virtualBindUserInfoDTO != null && virtualBindUserInfoDTO.getDpid() != null){
                restUserInfo = new RestUserInfo();
                restUserInfo.setUserCode(User.DP.getCode());
                restUserInfo.setUserId(virtualBindUserInfoDTO.getDpid());
            }
        }catch (Exception e){
            LOGGER.error(String.format("loadUserByToken:%s exception!", token),  e);
        }

        return restUserInfo;
    }

    private String getToken(IMobileContext iMobileContext, int userCode) {
        if(iMobileContext == null){
            return "";
        }

        String token = "";

        token = iMobileContext.getHeader().getToken();
        if(StringUtils.isNotBlank(token)){
            return token;
        }

        token = iMobileContext.getParameter("token");
        if(StringUtils.isNotBlank(token)){
            return token;
        }

        if(userCode == User.DP.getCode()){
            token = iMobileContext.getHeader().getNewToken();
            if(StringUtils.isNotBlank(token)){
                return token;
            }

            token = iMobileContext.getParameter("newtoken");
            if(StringUtils.isNotBlank(token)){
                return token;
            }

            token = HttpUtils.getCookie(iMobileContext.getRequest(), CommonWebConstants.COOKIE_KEY_DPER);
            if(StringUtils.isNotBlank(token)){
                return token;
            }
        }

        token = HttpUtils.getCookie(iMobileContext.getRequest(), CommonWebConstants.COOKIE_KEY_TOKEN);
        if(StringUtils.isNotBlank(token)){
            return token;
        }

        token = iMobileContext.getRequest().getHeader("token");
        if(StringUtils.isNotBlank(token)){
            return token;
        }

        return "";
    }




}
