package com.dianping.pay.api.biz.discount;

import com.alibaba.fastjson.JSON;
import com.dianping.api.common.enums.FeatureSwitchEnum;
import com.dianping.api.common.enums.PageSourceEnum;
import com.dianping.api.constans.CommonConstants;
import com.dianping.api.framework.FeatureContingency;
import com.dianping.api.util.LionQueryUtils;
import com.dianping.cat.Cat;
import com.dianping.combiz.spring.util.PropertiesLoaderSupportUtils;
import com.dianping.gmkt.coupon.common.api.dto.CouponPushTemplate;
import com.dianping.gmkt.coupon.common.api.enums.CouponPushTemplateEnum;
import com.dianping.gmkt.coupon.common.api.enums.UnifiedCouponExtraKeyEnum;
import com.dianping.gmkt.coupon.common.api.enums.UserSource;
import com.dianping.pay.api.beans.GetPromoDeskRequest;
import com.dianping.pay.api.beans.PromoProduct;
import com.dianping.pay.api.biz.activity.newloader.dto.CouponIssueActivityQueryContext;
import com.dianping.pay.api.entity.issuecoupon.IssueCouponRequest;
import com.dianping.pay.api.entity.promodesk.PromoDeskCoupon;
import com.dianping.pay.api.util.CouponComparator;
import com.dianping.pay.api.util.IssueCouponUtils;
import com.dianping.pay.promo.common.enums.UnifiedCouponTargetEnum;
import com.dianping.pay.promo.common.enums.User;
import com.dianping.pay.promo.common.enums.coupon.UnifiedCouponGroupType;
import com.dianping.pay.promo.execute.service.PayPromoDeskQueryService;
import com.dianping.pay.promo.execute.service.dto.PromoToolDTO;
import com.dianping.pay.promo.execute.service.dto.response.PromoQueryResponse;
import com.dianping.pay.promo.execute.service.enums.PromoToolType;
import com.dianping.tgc.process.enums.PlatformEnum;
import com.dianping.unified.coupon.issue.api.response.UnifiedCouponIssueResponse;
import com.dianping.unified.coupon.manage.api.UnifiedCouponExtendService;
import com.dianping.unified.coupon.manage.api.UnifiedCouponGroupQueryService;
import com.dianping.unified.coupon.manage.api.UnifiedCouponListService;
import com.dianping.unified.coupon.manage.api.dto.*;
import com.dianping.unified.coupon.manage.api.request.BatchLoadCouponGroupRequest;
import com.dianping.unified.coupon.manage.api.request.TempCountCouponRequest;
import com.dianping.unified.coupon.manage.api.request.UnifiedCouponQueryByCouponGroupIdRequest;
import com.dianping.unified.coupon.manage.api.response.UnifiedCouponManageResponse;
import com.google.common.base.Predicate;
import com.google.common.collect.*;
import com.sankuai.mpmkt.coupon.issue.api.IssueCouponService;
import com.sankuai.mpmkt.coupon.issue.api.request.CouponIssueInfo;
import com.sankuai.mpmkt.coupon.issue.api.request.CouponIssueRequest;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang.Validate;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;

/**
 * Created by huawei.li on 15/3/16.
 */
public class CouponBiz {
    private static final Logger log = LoggerFactory.getLogger(CouponBiz.class);
    public static final int OPTIMALDISCOUNTSIZE = PropertiesLoaderSupportUtils.getIntProperty("pay-api-mobile.checkout.optimaldiscount.size", 5);
    @Resource
    private PayPromoDeskQueryService payPromoDeskQueryService;
    @Resource
    private UnifiedCouponListService unifiedCouponListService;
    @Resource
    private UnifiedCouponGroupQueryService unifiedCouponGroupQueryService;
    @Resource
    private UnifiedCouponExtendService unifiedCouponExtendService;
    @Resource
    private IssueCouponService issueCouponService;
    
    public List<PromoDeskCoupon> getOptimalCoupons(GetPromoDeskRequest request, boolean isShopCoupon) {
        Cat.logEvent("INVALID_METHOD_2", "com.dianping.pay.api.biz.discount.CouponBiz.getOptimalCoupons(com.dianping.pay.api.beans.GetPromoDeskRequest,boolean)");
        PromoProduct promoProduct = request.getPromoProductList().get(0);
        if (promoProduct.getPrice() == null || promoProduct.getPrice().signum() <= 0) {
            return Collections.emptyList();
        }

        List<UnifiedCouponDTO> optimalCoupons = getOptimalCoupons(request, promoProduct, isShopCoupon);
        if (CollectionUtils.isNotEmpty(optimalCoupons)) {
            List<PromoDeskCoupon> promoDeskCoupons = CouponMapper.toPromoDeskCouponList(promoProduct.getProductCode(), optimalCoupons);
            assembleCipher(promoDeskCoupons, request.getUserId());
            return promoDeskCoupons;
        } else {
            return Collections.emptyList();
        }
    }

    public List<UnifiedCouponDTO> getOptimalCoupons(GetPromoDeskRequest request, final PromoProduct product, boolean isShopCoupon) {
        Cat.logEvent("INVALID_METHOD_4", "com.dianping.pay.api.biz.discount.CouponBiz.getOptimalCoupons(com.dianping.pay.api.beans.GetPromoDeskRequest,com.dianping.pay.api.beans.PromoProduct,boolean)");
        if (FeatureContingency.isSwitchOff(FeatureSwitchEnum.COUPON)) {
            return Collections.emptyList();
        }
        final UnifiedCouponQueryContext couponQueryContext = CouponMapper.toCouponQueryContext(request, product, isShopCoupon);
        UnifiedCouponManageResponse<List<UnifiedCouponDTO>> listResponse = unifiedCouponListService.queryCouponByContext(couponQueryContext, null, new UnifiedCouponListOption(5, 0, 0, 25));
        if (listResponse.isSuccess()) {
            return Ordering.from(new CouponComparator()).sortedCopy(Collections2.filter(listResponse.getResult(), new Predicate<UnifiedCouponDTO>() {
                @Override
                public boolean apply(UnifiedCouponDTO coupon) {
                    return coupon.isAvailable()
                            && CollectionUtils.isEmpty(coupon.getCouponGroupDTO().getPayChannelList()) // 渠道抵用券不参加最优选择
                            && couponQueryContext.getAmount().compareTo(coupon.getCouponGroupDTO().getDiscountAmount()) > 0;
                }
            }));
        }
        return Collections.emptyList();
    }

    public void assembleCipher(Collection<PromoDeskCoupon> couponCollection, long userId) {
        Cat.logEvent("INVALID_METHOD_2", "com.dianping.pay.api.biz.discount.CouponBiz.assembleCipher(java.util.Collection,long)");
        if (CollectionUtils.isEmpty(couponCollection)) {
            return;
        }
        List<PromoToolDTO> promoToolDTOList = new ArrayList<PromoToolDTO>();
        for (PromoDeskCoupon promoDeskCoupon : couponCollection) {
            PromoToolDTO promoToolDTO = new PromoToolDTO();
            promoToolDTO.setId(promoDeskCoupon.getId());
            promoToolDTO.setNewId(promoDeskCoupon.getNewId());
            promoToolDTO.setType(couponGroupType2PromoToolType(promoDeskCoupon.getCouponType()));
            promoToolDTO.setProductCodeList(Lists.newArrayList(promoDeskCoupon.getProductCode()));
            promoToolDTO.setUserId(userId);
            promoToolDTO.setAmount(promoDeskCoupon.getPriceValue() != null ? promoDeskCoupon.getPriceValue() : BigDecimal.ZERO);
            promoToolDTOList.add(promoToolDTO);
        }
        PromoQueryResponse<List<String>> response = payPromoDeskQueryService.encryptPromoTool(promoToolDTOList, null);
        if (response.isSuccess()) {
            int i = 0;
            Iterator<PromoDeskCoupon> iterator = couponCollection.iterator();
            while (iterator.hasNext()) {
                try {
                    PromoDeskCoupon coupon = iterator.next();
                    String cipher = response.getResult().get(i);
                    Validate.notEmpty(cipher);
                    coupon.setPromoCipher(cipher);
                } catch (Exception e) {
                    iterator.remove();
                }
                i++;
            }
        }
    }

    public static PromoDeskCoupon getOptimalCoupon(BigDecimal amount, List<PromoDeskCoupon> optimalCoupons) {
        Cat.logEvent("INVALID_METHOD_2", "com.dianping.pay.api.biz.discount.CouponBiz.getOptimalCoupon(java.math.BigDecimal,java.util.List)");
        if (CollectionUtils.isNotEmpty(optimalCoupons)) {
            for (PromoDeskCoupon d : optimalCoupons) {
                if (d.getOrderPriceLimit() - amount.doubleValue() <= 0.001) {
                    return d;
                }
            }
        }
        return null;
    }

    public static int couponGroupType2PromoToolType(int couponGroupType) {
        if (couponGroupType == UnifiedCouponGroupType.code.value) { // 优惠代码
            return PromoToolType.CODE.getCode();
        }
        if ( couponGroupType == UnifiedCouponGroupType.coupon.value || couponGroupType == UnifiedCouponGroupType.magicCard.value) {
            return PromoToolType.COUPON.getCode();
        }
        if (couponGroupType == UnifiedCouponGroupType.shopCoupon.value
                || couponGroupType == UnifiedCouponGroupType.mtShopCoupon.value
                || couponGroupType == UnifiedCouponGroupType.huiYuanKa.value
                || couponGroupType == UnifiedCouponGroupType.shopCoupon2.value) {
            return PromoToolType.SHOP_COUPON.getCode();
        }
        return 0;
    }

    public int queryIssuedCount(long userId, int couponGroupId,boolean isDpClient) {
        Cat.logEvent("INVALID_METHOD_3", "com.dianping.pay.api.biz.discount.CouponBiz.queryIssuedCount(long,int,boolean)");
        if (userId == 0) {
            return 0;
        }
        try {
            UnifiedCouponGroupDTO couponGroupDTO2 = queryCouponGroup(couponGroupId);
            if (couponGroupDTO2 == null) {
                return Integer.MAX_VALUE;
            }

            UnifiedCouponManageResponse<Integer> response;
            if (couponGroupDTO2.getTarget() == UnifiedCouponTargetEnum.meituan.getCode()) {
                response = unifiedCouponListService.countMTCouponByCampaignId((int) userId, Lists.newArrayList(couponGroupDTO2.getMtCampaignID()));
            } else {
                TempCountCouponRequest tempCountCouponRequest = new TempCountCouponRequest();
                tempCountCouponRequest.setUserId(userId);
                tempCountCouponRequest.setUserType(isDpClient ? User.DP.getFlag() : User.MT.getFlag());
                tempCountCouponRequest.setCouponGroupIds(Lists.newArrayList(couponGroupId));
                tempCountCouponRequest.setFilterType(0);
                response = unifiedCouponExtendService.tempCountCoupon(tempCountCouponRequest);
            }
            return response.isSuccess() ? response.getResult() : Integer.MAX_VALUE;
        } catch (Exception e) {
            log.error("queryIssuedCount error", e);
        }
        return Integer.MAX_VALUE;
    }

    public List<UnifiedCouponDTO> queryIssuedCouponList(long userId, int couponGroupId,boolean isDpClient) {
        return batchQueryIssuedCouponList(userId, Lists.newArrayList(couponGroupId) ,isDpClient);
    }
    public List<UnifiedCouponDTO> batchQueryIssuedCouponList(long userId,List<Integer> couponGroupIds,boolean isDpClient){
        List<UnifiedCouponDTO> resultList = new ArrayList<UnifiedCouponDTO>();
        if (CollectionUtils.isEmpty(couponGroupIds) || userId<=0) {
            return resultList;
        }
        try {
            UnifiedCouponQueryByCouponGroupIdRequest request = new UnifiedCouponQueryByCouponGroupIdRequest();
            request.setUserId(userId);
            request.setUserType(isDpClient ? User.DP.getCode() : User.MT.getCode());
            request.setListOption(new UnifiedCouponListOption(0, 0));
            request.setCouponGroupIdList(couponGroupIds);
            UnifiedCouponManageResponse<List<UnifiedCouponDTO>> response = unifiedCouponListService.queryCouponByCouponGroupId(request);
            if (response.isSuccess()) {
                resultList = response.getResult();
            }
        } catch (Exception e) {
            log.error("getIssuedCouponList error", e);
        }
        return resultList;
    }
    
    public UnifiedCouponGroupDTO queryCouponGroup(int couponGroupId) {
        try {
            UnifiedCouponManageResponse<UnifiedCouponGroupDTO> response = unifiedCouponGroupQueryService.loadCouponGroup(couponGroupId, null);
            if (response.isSuccess() && response.getResult() != null) {
                return response.getResult();
            }
        } catch (Exception e) {
            log.error("queryCouponGroup error", e);
        }
        return null;
    }

    public Map<Integer, UnifiedCouponGroupDTO> batchQueryCouponGroup(List<Integer> couponGroupIds){
        if (CollectionUtils.isEmpty(couponGroupIds)) {
            return Maps.newHashMap();
        }
        try {
            Map<Integer, UnifiedCouponGroupDTO> result = Maps.newHashMap();
            List<List<Integer>> couponGroupIdsList = Lists.partition(couponGroupIds, 30);
            UnifiedCouponQueryOption queryOption = new UnifiedCouponQueryOption();
            queryOption.setFormatSubDisplayTitle(true);
            for (List<Integer> partIDS : couponGroupIdsList) {
                UnifiedCouponManageResponse<Map<Integer, UnifiedCouponGroupDTO>> response = unifiedCouponGroupQueryService.batchLoadCouponGroup(Sets.newHashSet(partIDS), queryOption);
                if (response.isSuccess() && MapUtils.isNotEmpty(response.getResult())) {
                    Map<Integer, UnifiedCouponGroupDTO> map = response.getResult();
                    result.putAll(map);
                }
            }
            return result;
        } catch (Exception e) {
            log.error("queryCouponGroup error", e);
        }
        return Maps.newHashMap();
    }

    public Map<Integer, UnifiedCouponGroupDTO> batchQueryCouponGroupWithStock(List<Integer> couponGroupIds){
        if (CollectionUtils.isEmpty(couponGroupIds)) {
            return Maps.newHashMap();
        }
        try {
            Map<Integer, UnifiedCouponGroupDTO> result = Maps.newHashMap();
            List<List<Integer>> couponGroupIdsList = Lists.partition(couponGroupIds, 30);
            UnifiedCouponQueryOption queryOption = new UnifiedCouponQueryOption();
            queryOption.setFormatSubDisplayTitle(true);
            queryOption.setFillDailyStockInfo(true);
            queryOption.setFillStockInfo(true);
            for (List<Integer> partIDS : couponGroupIdsList) {
                UnifiedCouponManageResponse<Map<Integer, UnifiedCouponGroupDTO>> response = unifiedCouponGroupQueryService.batchLoadCouponGroup(Sets.newHashSet(partIDS), queryOption);
                if (response.isSuccess() && MapUtils.isNotEmpty(response.getResult())) {
                    Map<Integer, UnifiedCouponGroupDTO> map = response.getResult();
                    result.putAll(map);
                }
            }
            return result;
        } catch (Exception e) {
            log.error("queryCouponGroup error", e);
        }
        return Maps.newHashMap();
    }

    public List<String> getIssueCouponMsgSuccessContent(int couponGroupId) {
        try {
            UnifiedCouponManageResponse<UnifiedCouponGroupDTO> response = unifiedCouponGroupQueryService.loadCouponGroup(couponGroupId, null);
            if (response.isSuccess() && response.getResult() != null) {
                return IssueCouponUtils.formatIssueCouponMsgSuccessContent(response.getResult());
            }
        } catch (Exception e) {
            log.error("getIssueCouponMsgSuccessContent error", e);
        }
        return Collections.emptyList();
    }

    private final static String LQG_ISSUE_SOURCE = "DAO_DIAN_LQG";

    public String issueLQGCoupon(String unifiedCouponGroupId, IssueCouponRequest issueCouponRequest) {
        Cat.logEvent("CouponIssue", "LQG");
        CouponIssueRequest req = new CouponIssueRequest();
        req.setUserId(issueCouponRequest.getUserId());
        req.setUserType(issueCouponRequest.isDpClient() ? User.DP.getFlag() : User.MT.getFlag());
        CouponIssueInfo couponIssueInfo = new CouponIssueInfo();
        couponIssueInfo.setCouponGroupId(unifiedCouponGroupId);
        req.setCouponGroupInfo(Lists.newArrayList(couponIssueInfo));
        req.setOperator("mapi-pay-promo-web");
        //美团端才发横幅，点评端暂不支持
        if (LionQueryUtils.PREPAY_GOD_COUPON_BANNER_PUSH_SWITCH && !issueCouponRequest.isDpClient() && (PageSourceEnum.BeautyMedicalProductDetail.code.equals(issueCouponRequest.getCouponPageSource()) || PageSourceEnum.BeautyMedicalPOIDetail.code.equals(issueCouponRequest.getCouponPageSource()))) {
            Map<String, String> extraParams = Maps.newHashMap();
            CouponPushTemplate couponPushTemplate = new CouponPushTemplate();
            couponPushTemplate.setTemplateId(CouponPushTemplateEnum.YUFU_GOD_COUPON_BANNER_PUSH.getId());
            extraParams.put(UnifiedCouponExtraKeyEnum.pushTemplate.name(), JSON.toJSONString(couponPushTemplate));
            couponIssueInfo.setExtraParams(extraParams);
        }
        Map<String, String> extraParams = Optional.ofNullable(couponIssueInfo.getExtraParams()).orElse(Maps.newHashMap());
        if (MapUtils.isNotEmpty(issueCouponRequest.getExtraParams())) {
            extraParams.putAll(issueCouponRequest.getExtraParams());
        }
        couponIssueInfo.setExtraParams(extraParams);

        req.setCommonParams(issueCouponRequest.getCommonParams());
        req.setIssueSource(LQG_ISSUE_SOURCE);
        req.setApParams(issueCouponRequest.getApParams());
        try {
            UnifiedCouponIssueResponse response = issueCouponService.issueCouponsToUser(req, null);
            if (response.isSuccess() && response.getResult() != null && response.getResult().getIssueSuccessQuantity() > 0) {
                return response.getResult().getResult().get(0).getUnifiedCouponId();
            }
        } catch (Exception e) {
            log.error("issueCouponsToUser error", e);
        }
        return null;
    }

}
