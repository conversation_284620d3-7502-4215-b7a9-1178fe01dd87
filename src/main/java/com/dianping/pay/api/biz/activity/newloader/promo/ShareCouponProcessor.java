package com.dianping.pay.api.biz.activity.newloader.promo;

import com.dianping.lion.client.Lion;
import com.dianping.mobile.framework.datatypes.IMobileContext;
import com.dianping.pay.api.biz.activity.newloader.dto.CouponActivityContext;
import com.dianping.pay.api.util.DotUtils;
import com.dianping.pay.api.util.LionConstants;
import com.dianping.pay.api.util.PoiIdUtils;
import com.sankuai.mkt.activity.api.enums.ActivityErrorCode;
import com.sankuai.mkt.activity.api.merchantsharecoupon.model.MerchantShareCouponExposeDTO;
import com.sankuai.mkt.activity.api.merchantsharecoupon.model.PoiExposeCouponReqDTO;
import com.sankuai.mkt.activity.api.merchantsharecoupon.service.MerchantShareCouponActivityCService;
import com.sankuai.mkt.activity.api.response.RpcResult;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.concurrent.Callable;
import java.util.concurrent.Future;
import java.util.concurrent.TimeUnit;

@Component("shareCouponProcessor")
@Slf4j
public class ShareCouponProcessor extends AbstractPromoProcessor {

    @Autowired
    private MerchantShareCouponActivityCService merchantShareCouponActivityCService;

    @Override
    public void prePare(final CouponActivityContext promoCtx, IMobileContext iMobileContext) {
        try {
            if (!Lion.getBooleanValue(LionConstants.SHARE_COUPON_QUERY_SWITCH, true) || !promoCtx.isNeedShareCouponPromotion()) {
                return;
            }
            DotUtils.dotForPromoProcessor(promoCtx, getPromoName());
            Future<List<MerchantShareCouponExposeDTO>> future = ExecutorService.submit(new Callable<List<MerchantShareCouponExposeDTO>>() {
                @Override
                public List<MerchantShareCouponExposeDTO> call() throws Exception {
                    return queryShareCoupon(promoCtx);
                }
            });
            promoCtx.setShareCouponPromotionFuture(future);
        } catch (Exception e) {
            log.error("ShareCouponProcessor# prepare error. ctx: {}", promoCtx, e);
        }

    }

    @Override
    public void loadPromo(CouponActivityContext promoCtx) {
        try {
            Future<List<MerchantShareCouponExposeDTO>> shareCouponPromotionFuture = promoCtx.getShareCouponPromotionFuture();
            if (shareCouponPromotionFuture != null) {
                promoCtx.setCouponExposeDTOS(shareCouponPromotionFuture.get(500, TimeUnit.MILLISECONDS));
            }
        } catch (Exception e) {
            log.error("ShareCouponProcessor# loadPromo error. ctx: {}", promoCtx, e);
        }
    }

    @Override
    public String getPromoName() {
        return "shareCoupon";
    }

    private List<MerchantShareCouponExposeDTO> queryShareCoupon(CouponActivityContext promoCtx) {
        List<MerchantShareCouponExposeDTO> couponExposeDTOS = null;
        try {
            long userId;
            if (promoCtx.isMt()) {
                userId = promoCtx.getMtUserId();
            } else {
                userId = promoCtx.getDpUserId();
            }
            PoiExposeCouponReqDTO poiExposeCouponReqDTO = new PoiExposeCouponReqDTO();
            poiExposeCouponReqDTO.setUserId(userId);
            if (promoCtx.isMt()) {
                poiExposeCouponReqDTO.setPlatform(com.sankuai.mkt.activity.api.enums.AppPlatform.MT.getCode());
                poiExposeCouponReqDTO.setMtShopIdL(promoCtx.getMtShopIdL());
                poiExposeCouponReqDTO.setMtShopId(PoiIdUtils.poiIdLongToInt(promoCtx.getMtShopIdL()));
                poiExposeCouponReqDTO.setUserId(promoCtx.getMtUserId());
            } else {
                poiExposeCouponReqDTO.setPlatform(com.sankuai.mkt.activity.api.enums.AppPlatform.DP.getCode());
                poiExposeCouponReqDTO.setDpShopIdL(promoCtx.getDpShopIdL());
                poiExposeCouponReqDTO.setDpShopId(PoiIdUtils.poiIdLongToInt(promoCtx.getDpShopIdL()));
                poiExposeCouponReqDTO.setUserId(promoCtx.getDpUserId());
            }
            RpcResult<List<MerchantShareCouponExposeDTO>> result = merchantShareCouponActivityCService.poiExposeCoupon(poiExposeCouponReqDTO);
            if (result != null && ActivityErrorCode.SUCCESS.getCode().equals(result.getCode())) {
                couponExposeDTOS = result.getData();
                boolean isHit = CollectionUtils.isNotEmpty(couponExposeDTOS);
                DotUtils.dotForPromoHit(getPromoName(), promoCtx.isMt(), isHit);
            } else {
                log.warn("call poiExposeCoupon failed. request: {}, result: {}", poiExposeCouponReqDTO, result);
                // userid逻辑暂时不短路，先把这部分失败打点
                if (userId > 0) {
                    DotUtils.dotForPromoCallFail(getPromoName(), "poiExposeCoupon", promoCtx.isMt());
                }
            }
        } catch (Exception e) {
            log.error("queryShareCoupon exception!", e);
        }
        return couponExposeDTOS;
    }
}
