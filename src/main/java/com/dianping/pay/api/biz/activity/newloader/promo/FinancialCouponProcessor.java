package com.dianping.pay.api.biz.activity.newloader.promo;

import com.alibaba.fastjson.JSON;
import com.dianping.gmkt.coupon.common.api.enums.CouponPayPlatform;
import com.dianping.gmkt.coupon.common.api.enums.MtCouponPlatform;
import com.dianping.lion.client.Lion;
import com.dianping.mobile.framework.datatypes.IMobileContext;
import com.dianping.pay.api.biz.activity.newloader.dto.CouponActivityContext;
import com.dianping.pay.api.util.ClientTypeUtils;
import com.dianping.pay.api.util.DotUtils;
import com.dianping.pay.api.util.LionConstants;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.sankuai.pay.access.client.predisplay.IPreDisplayThrift;
import com.sankuai.pay.access.client.predisplay.dto.PrePromoDTO;
import com.sankuai.pay.access.client.predisplay.request.PreDecisionReqVO;
import com.sankuai.pay.access.client.predisplay.response.PreDecisionRespVO;
import com.sankuai.pay.access.trade.sdk.protocol.NbParams;
import com.sankuai.pay.access.trade.sdk.utils.TradeOrderSignUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;
import java.util.concurrent.Callable;
import java.util.concurrent.Future;
import java.util.concurrent.TimeUnit;

@Component("financialCouponProcessor")
@Slf4j
public class FinancialCouponProcessor extends AbstractPromoProcessor {

    @Autowired
    private IPreDisplayThrift preDisplayThrift;

    private static final List<Long> whiteUserIdList = Lists.newArrayList();

    @Override
    public void prePare(CouponActivityContext promoCtx, IMobileContext iMobileContext) {
        long userId;//美团userid
        if (promoCtx.isMt()) {
            userId = promoCtx.getMtUserId();
        } else {
            userId = promoCtx.getMtRealUserIdFromDp() == null ? 0 : promoCtx.getMtRealUserIdFromDp();
        }
        if (!promoCtx.isNeedFinancialPromo() || userId <= 0) {
            return;
        }
        List<Long> whiteUserIds = Lion.getList("mapi-pay-promo-web.financialCoupon.userid.white.list", Long.class, whiteUserIdList);
        long userIdPercent = userId % 100;
        if (!whiteUserIds.contains(userId) && userIdPercent >= Lion.getIntValue("mapi-pay-promo-web.financialCoupon.userid.gray", 0)) {
            return;
        }
        //支付券只需要适配native，H5 小程序等等不支持美团金融支付
        if (promoCtx.isMt() && promoCtx.getCouponPlatform() != MtCouponPlatform.APP.getCode()) {
            return;
        }
        if (!promoCtx.isMt() && !String.valueOf(promoCtx.getCouponPlatform()).startsWith("2")) {
            return;
        }
        DotUtils.dotForPromoProcessor(promoCtx, getPromoName());
        try {
            Future<PrePromoDTO> future = ExecutorService.submit(new Callable<PrePromoDTO>() {
                @Override
                public PrePromoDTO call() throws Exception {
                    return queryFinancialCoupons(promoCtx, iMobileContext);
                }
            });
            promoCtx.setFinancialCouponPromotionFuture(future);
        } catch (Exception e) {
            log.error("TgcCouponProcessor# prepare error. ctx: {}", promoCtx, e);
        }
    }

    private PrePromoDTO queryFinancialCoupons(CouponActivityContext promoCtx, IMobileContext iMobileContext) {
        PreDecisionReqVO req = new PreDecisionReqVO();
        req.setSignType("MD5");
        req.setCharset("UTF-8");
        req.setSellerId(Lion.getLongValue(LionConstants.FINANCIAL_INVOKE_SELLERID, 0));
        if (promoCtx.isMt()) {
            req.setUserId(promoCtx.getMtUserId());
        } else {
            req.setUserId(promoCtx.getMtRealUserIdFromDp());
        }
        req.setUserIdType(1);
        Map<String, String> dealInfo = Maps.newHashMap();
        dealInfo.put("poiId", String.valueOf(promoCtx.getDpShopIdL()));
        req.setDealInfo(JSON.toJSONString(dealInfo));
        req.setRiskParam("{}");
        NbParams nbParams = new NbParams();
        nbParams.setUuid(promoCtx.getDpid());
        if (promoCtx.isMt()) {
            nbParams.setApp("group");
            nbParams.setPlatform("android");
        }else{
            CouponPayPlatform couponPayPlatform = CouponPayPlatform.getByCode(promoCtx.getCouponPlatform());
            if (couponPayPlatform == null) {
                return null;
            }
            nbParams.setApp("dianping-nova");
            String platform = couponPayPlatform.name().contains("iphone") ? "iphone" : "android";
            nbParams.setPlatform(platform);
        }
        if (iMobileContext != null) {
            nbParams.setAppversion(iMobileContext.getVersion());
        }
        //与fengtingfan沟通，传用户左上角选择城市，美团传即可，点评端不传就行
        if (promoCtx.isMt()) {
            nbParams.setCity(String.valueOf(promoCtx.getCityId()));
        }
        if (iMobileContext != null) {
            nbParams.setVersion(iMobileContext.getVersion());
        }
        req.setNbParams(nbParams);
        req.setDecisionScene(31);
        String sign = TradeOrderSignUtils.tradeOrderSign(req, Lion.get(LionConstants.FINANCIAL_INVOKE_SECRET, ""));
        req.setSign(sign);
        try {
            PreDecisionRespVO resp = preDisplayThrift.preDecision(req);
            if (resp == null) {
                Exception e = new RuntimeException("queryFinancialCoupons resp fail,msg:null");
                log.error("queryFinancialCoupons fail. req: {}", req, e);
            }else if (resp.getStatus() == null || resp.getStatus() != 0 || resp.getData() == null) {
                Exception e = new RuntimeException("queryFinancialCoupons resp fail,msg:" + resp.getMsg() != null ? resp.getMsg() : "null");
                log.error("queryFinancialCoupons fail. req: {}", req, e);
            } else {
                if (CollectionUtils.isEmpty(resp.getData().getProductList()) || resp.getData().getProductList().get(0) == null || resp.getData().getProductList().get(0).getPrePromoInfo() == null) {
                    return null;
                }
                return resp.getData().getProductList().get(0).getPrePromoInfo();
            }
        } catch (Exception e) {
            log.error("queryFinancialCoupons  error. req: {}", req, e);
        }
        return null;
    }

    @Override
    public void loadPromo(CouponActivityContext promoCtx) {
        try {
            Future<PrePromoDTO> financialCouponPromotionFuture = promoCtx.getFinancialCouponPromotionFuture();
            if (financialCouponPromotionFuture != null) {
                PrePromoDTO prePromoDTO = financialCouponPromotionFuture.get(500, TimeUnit.MILLISECONDS);
                DotUtils.dotForPromoHit(getPromoName(), promoCtx.isMt(), prePromoDTO != null);
                promoCtx.setFinancialCouponPromotion(prePromoDTO);
            }
        } catch (Exception e) {
            log.error("financialCouponPromo# loadPromo error. ctx: {}", promoCtx, e);
        }
    }

    @Override
    public String getPromoName() {
        return "financialCoupon";
    }
}
