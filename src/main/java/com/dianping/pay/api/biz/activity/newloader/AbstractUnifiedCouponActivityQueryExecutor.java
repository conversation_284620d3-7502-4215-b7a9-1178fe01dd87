package com.dianping.pay.api.biz.activity.newloader;

import com.dianping.cat.Cat;
import com.dianping.cat.message.Transaction;
import com.dianping.pay.api.beans.IssueCouponActivity;
import com.dianping.pay.api.biz.activity.newloader.dto.CouponIssueActivityQueryContext;
import com.dianping.pay.api.biz.discount.CouponBiz;
import com.dianping.pay.api.util.IssueCouponUtils;
import com.dianping.unified.coupon.manage.api.dto.UnifiedCouponDTO;
import com.dianping.unified.coupon.manage.api.dto.UnifiedCouponGroupDTO;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import org.apache.commons.collections.CollectionUtils;
import org.jsoup.helper.Validate;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.concurrent.Callable;

/**
 * Created by drintu on 18/6/7.
 */

public abstract class AbstractUnifiedCouponActivityQueryExecutor implements Callable<List<IssueCouponActivity>> {
    private static final Logger LOGGER = LoggerFactory.getLogger(AbstractUnifiedCouponActivityQueryExecutor.class);

    private CouponBiz couponBiz;

    protected CouponIssueActivityQueryContext queryContext;

    @Override
    public List<IssueCouponActivity> call() throws Exception {
        Transaction t = Cat.newTransaction("UnifiedCouponIssueActivity", this.getClass().getName());
        try {
            if (queryContext.getUserId() <= 0) {
                Cat.logEvent("InvalidUserId", this.getClass().getName());
            }
            List<IssueCouponActivity> activities = loadFromRemote();
            fillRelatedInfo(activities);
            t.setStatus(Transaction.SUCCESS);
            return activities;
        } catch (Exception e) {
            LOGGER.error("couponIssueActivityQueryExecutor error", e);
            t.setStatus(e.getClass().getSimpleName());
            return Collections.emptyList();
        } finally {
            t.complete();
        }
    }

    private void fillRelatedInfo(List<IssueCouponActivity> activities) {
        if (CollectionUtils.isEmpty(activities)) {
            return;
        }
        List<Integer> couponGroupIds = Lists.newArrayList();
        for (IssueCouponActivity activity : activities){
            if(activity == null || activity.getCouponGroupId() <= 0){
                continue;
            }
            couponGroupIds.add(activity.getCouponGroupId());
        }

        Map<Integer,UnifiedCouponGroupDTO> couponGroupMap = couponBiz.batchQueryCouponGroup(couponGroupIds);
        List<UnifiedCouponDTO> unifiedCouponDTOs = couponBiz.batchQueryIssuedCouponList(queryContext.getUserId(), couponGroupIds, queryContext.isDpClient());
        Map<Integer,List<UnifiedCouponDTO>> issuedCouponMap = Maps.newHashMap();
        for(UnifiedCouponDTO unifiedCouponDTO : unifiedCouponDTOs){
            if(issuedCouponMap.get(unifiedCouponDTO.getCouponGroupDTO().getCouponGroupId()) == null){
                issuedCouponMap.put(unifiedCouponDTO.getCouponGroupDTO().getCouponGroupId(), new ArrayList<UnifiedCouponDTO>());
            }
            issuedCouponMap.get(unifiedCouponDTO.getCouponGroupDTO().getCouponGroupId()).add(unifiedCouponDTO);
        }

        for (IssueCouponActivity activity : activities) {
            Validate.notNull(activity,"activity is null");
            Validate.notNull(couponGroupMap.get(activity.getCouponGroupId()), "couponGroup not exists");
            activity.setCouponGroup(couponGroupMap.get(activity.getCouponGroupId()));
            activity.setDpClient(queryContext.isDpClient());

            List<UnifiedCouponDTO> issuedCoupon = issuedCouponMap.get(activity.getCouponGroupId());
            activity.setIssuedCoupon(issuedCoupon);
            activity.setIssuedUnUseUnExpireCoupon(IssueCouponUtils.buildUnUseUnExpiredCoupons(issuedCoupon));
        }

    }

    protected AbstractUnifiedCouponActivityQueryExecutor(CouponIssueActivityQueryContext context){
        this.queryContext = context;
    }
    public AbstractUnifiedCouponActivityQueryExecutor setCouponBiz(CouponBiz couponBiz) {
        this.couponBiz = couponBiz;
        return this;
    }
    protected abstract List<IssueCouponActivity> loadFromRemote() ;

}
