package com.dianping.pay.api.biz.activity.newloader.promo;


import com.dianping.cat.Cat;
import com.dianping.gmkt.coupon.common.api.enums.CouponBusiness;
import com.dianping.lion.client.Lion;
import com.dianping.mobile.framework.datatypes.IMobileContext;
import com.dianping.pay.api.beans.IssueCouponActivity;
import com.dianping.pay.api.beans.MerchantNewUserValidateResult;
import com.dianping.pay.api.biz.activity.newloader.dto.CouponActivityContext;
import com.dianping.pay.api.biz.discount.CouponBiz;
import com.dianping.pay.api.enums.IssueCouponOptionType;
import com.dianping.pay.api.enums.QueryShopCouponTypeEnum;
import com.dianping.pay.api.util.*;
import com.dianping.pay.api.wrapper.CouponNewUserWrapper;
import com.dianping.pay.promo.common.enums.User;
import com.dianping.pay.promo.common.enums.coupon.UnifiedCouponProductType;
import com.dianping.spc.common.Response;
import com.dianping.tgc.enums.CouponUserTypeEnum;
import com.dianping.tgc.open.entity.*;
import com.dianping.tgc.open.v2.TGCActivityShopQueryService;
import com.dianping.tgc.open.v2.TGCGetCouponComponentQueryService;
import com.dianping.unified.coupon.manage.api.dto.UnifiedCouponDTO;
import com.dianping.unified.coupon.manage.api.dto.UnifiedCouponGroupDTO;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.sankuai.mpmkt.coupon.execute.api.dto.ProductNewUserValidateResp;
import com.sankuai.mpmkt.coupon.execute.api.response.CouponGroupURValidateResp;
import com.sankuai.mpmkt.coupon.execute.api.response.MerchantUserRuleValidateResp;
import com.sankuai.nibmktproxy.queryclient.proxy.GetPromotionDTO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.jsoup.helper.Validate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.concurrent.Callable;
import java.util.concurrent.Future;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

@Component("tgcCouponProcessor")
@Slf4j
public class TgcCouponProcessor extends AbstractPromoProcessor {

    @Autowired
    private TGCGetCouponComponentQueryService tgcGetCouponComponentQueryService;

    @Autowired
    private CouponBiz couponBiz;

    @Autowired
    private LogUtils logUtils;

    @Autowired
    private TGCActivityShopQueryService tgcActivityShopQueryService;

    @Autowired
    private CouponNewUserWrapper couponNewUserWrapper;

    @Override
    public void prePare(final CouponActivityContext promoCtx, IMobileContext iMobileContext) {
        try {
            Future<List<IssueCouponActivity>> future = ExecutorService.submit(new Callable<List<IssueCouponActivity>>() {
                @Override
                public List<IssueCouponActivity> call() throws Exception {
                    return queryIssueCouponActivity(promoCtx);
                }
            });
            promoCtx.setTgcCouponPromotionFuture(future);
        } catch (Exception e) {
            log.error("TgcCouponProcessor# prepare error. ctx: {}", promoCtx, e);
        }
    }

    @Override
    public void loadPromo(CouponActivityContext promoCtx) {
        try {
            if (promoCtx.getIssueCouponActivities() != null) {
                return;
            }
            Future<List<IssueCouponActivity>> tgcCouponPromotionFuture = promoCtx.getTgcCouponPromotionFuture();
            if (tgcCouponPromotionFuture != null) {
                int timeout = Lion.getIntValue(LionConstants.TGC_COUPON_PROMOTION_TIMEOUT, 500);
                promoCtx.setIssueCouponActivities(tgcCouponPromotionFuture.get(timeout, TimeUnit.MILLISECONDS));
            }
        } catch (Exception e) {
            log.error("TgcCouponProcessor# loadPromo error. ctx: {}", promoCtx, e);
        }
    }

    @Override
    public String getPromoName() {
        return "tgcCoupon";
    }

    private List<ActivityDTO> getActivityDTOS(CouponActivityContext promoCtx) {
        List<ActivityDTO> activityDTOs = Lists.newArrayList();
        long shopId;
        if (promoCtx.isMt()) {
            shopId = promoCtx.getMtShopIdL();
        } else {
            shopId = promoCtx.getDpShopIdL();
        }
        int queryType = promoCtx.getQueryShopCouponType();
        String methodName;
        if (queryType == QueryShopCouponTypeEnum.BY_SHOP.getCode()) {
            // 根据门店查询券批次场景
            if (shopId <= 0) {
                return null;
            }
            DotUtils.dotForPromoProcessor(promoCtx, getPromoName());
            BatchActivityQueryRequest batchActivityQueryRequest = new BatchActivityQueryRequest();
            ActivityQueryOption activityQueryOption = new ActivityQueryOption();
            activityQueryOption.setFillDiscountCoupon(Boolean.TRUE.equals(promoCtx.getNeedDiscountCoupon()));

            batchActivityQueryRequest.setActivityQueryOption(activityQueryOption);
            batchActivityQueryRequest.setShopLongIds(Lists.newArrayList(promoCtx.getDpShopIdL()));
            //activityShopQueryRequest.setUserId(promoCtx.getUserId());
            if (promoCtx.isMt()) {
                batchActivityQueryRequest.setSource(2);
            } else {
                batchActivityQueryRequest.setSource(1);
            }
            Response<BatchQueryResponseDTO> batchQueryResponseDTOResponse = tgcActivityShopQueryService.batchQueryActivityByShopIds(batchActivityQueryRequest);
            if (batchQueryResponseDTOResponse == null || !batchQueryResponseDTOResponse.isSuccess() || batchQueryResponseDTOResponse.getResult() == null ||
                    MapUtils.isEmpty(batchQueryResponseDTOResponse.getResult().getShopCouponMap()) ||
                    !batchQueryResponseDTOResponse.getResult().getShopCouponMap().containsKey(promoCtx.getDpShopIdL())) {
                DotUtils.dotForPromoCallFail(getPromoName(), "batchQueryActivityByShopIds", promoCtx.isMt());
                log.warn("TgcCouponProcessor# batchQueryResponseDTOResponse error. promoCtx: {}, queryResponseDTOResponse: {}", promoCtx, batchQueryResponseDTOResponse);
                return null;
            }
            activityDTOs = batchQueryResponseDTOResponse.getResult().getShopCouponMap().get(promoCtx.getDpShopIdL());
        } else if (queryType == QueryShopCouponTypeEnum.BY_DEAL.getCode() && !promoCtx.isDealPromoProxy()) {
            // 团单场景
            int dealGroupId = promoCtx.getDpDealGroupId();
            if (dealGroupId <= 0) {
                return null;
            }
            DotUtils.dotForPromoProcessor(promoCtx, getPromoName());
            Response<QueryResponseDTO> queryResponseDTOResponse = queryAllKindCouponListByBizId(promoCtx.isMt(), dealGroupId, BizIdType.DEAL_GROUP_ID.getCode());
            if (queryResponseDTOResponse == null || !queryResponseDTOResponse.isSuccess() || queryResponseDTOResponse.getResult() == null) {
                DotUtils.dotForPromoCallFail(getPromoName(), "queryAllKindCouponListByDealId", promoCtx.isMt());
                log.warn("TgcCouponProcessor# getActivityDTOS error. promoCtx: {}, queryResponseDTOResponse: {}", promoCtx, queryResponseDTOResponse);
                return null;
            }
            activityDTOs = queryResponseDTOResponse.getResult().getActivityDTOs();
        } else if (queryType == QueryShopCouponTypeEnum.BY_SPU.getCode()) {
            // 泛商品spu场景
            long spuGroupId = promoCtx.getSpuGroupId();
            if (spuGroupId <= 0) {
                return null;
            }
            DotUtils.dotForPromoProcessor(promoCtx, getPromoName());
            Response<QueryResponseDTO> queryResponseDTOResponse = queryAllKindCouponListByBizId(promoCtx.isMt(), spuGroupId, BizIdType.SPU_ID.getCode());
            if (queryResponseDTOResponse == null || !queryResponseDTOResponse.isSuccess() || queryResponseDTOResponse.getResult() == null) {
                DotUtils.dotForPromoCallFail(getPromoName(), "queryAllKindCouponListBySpuId", promoCtx.isMt());
                log.warn("TgcCouponProcessor# getActivityDTOS error. promoCtx: {}, queryResponseDTOResponse: {}", promoCtx, queryResponseDTOResponse);
                return null;
            }
            activityDTOs = queryResponseDTOResponse.getResult().getActivityDTOs();
        } else {
            return null;
        }
        boolean hit = CollectionUtils.isNotEmpty(activityDTOs);
        DotUtils.dotForPromoHit(getPromoName(), promoCtx.isMt(), hit);
        return activityDTOs;
    }

    private Response<QueryResponseDTO> queryAllKindCouponListByBizId(boolean isMt, long bizId, Integer bizIdType) {
        CouponBizIdQueryRequest couponBizIdQueryRequest = new CouponBizIdQueryRequest();
        couponBizIdQueryRequest.setMt(isMt);
        couponBizIdQueryRequest.setBizId(bizId);
        couponBizIdQueryRequest.setBizIdType(bizIdType);

        ActivityQueryOption activityQueryOption = new ActivityQueryOption();
        activityQueryOption.setFillDiscountCoupon(true);
        couponBizIdQueryRequest.setActivityQueryOption(activityQueryOption);
        Response<QueryResponseDTO> queryResponseDTOResponse = tgcGetCouponComponentQueryService.queryAllKindCouponListByBizId(couponBizIdQueryRequest);
        logUtils.logInfo("queryAllKindCouponListByBizId# req: {}, result: {}", couponBizIdQueryRequest, queryResponseDTOResponse);
        return queryResponseDTOResponse;
    }


    private List<IssueCouponActivity> queryIssueCouponActivity(CouponActivityContext promoCtx) {
        List<IssueCouponActivity> issueCouponActivities = Lists.newArrayList();
        try {
            List<ActivityDTO> activityDTOs = getActivityDTOS(promoCtx);
            logUtils.logInfo("queryIssueCouponActivity#. ctx: {}, activityDTOs: {}", promoCtx, activityDTOs);
            if (CollectionUtils.isNotEmpty(activityDTOs)) {
                for (ActivityDTO activityDTO : activityDTOs) {
                    IssueCouponActivity issueCouponActivity = null;
                    try {
                        //测试环境存在String的券批次 忽略
                        if (activityDTO.getCouponDTO().isUpDailyLimit()) {
                            continue;
                        }
                        issueCouponActivity = new IssueCouponActivity(activityDTO,
                                promoCtx.isMt() ? IssueCouponOptionType.MT_SHOP_EVENT.getCode() :
                                        IssueCouponOptionType.DP_SHOP_EVENT.getCode());
                        issueCouponActivities.add(issueCouponActivity);
                    } catch (Exception e) {
                        log.error("IssueCouponActivity instance exception!", e);
                        Cat.logEvent("BuildIssueCouponActivityException", "Ex");
                    }
                }

                //过新客校验
                filterNewUserActivity(issueCouponActivities, promoCtx);

                //填充券批次信息和用户领券信息
                fillRelatedInfo(issueCouponActivities, promoCtx);
            }
        } catch (Exception e) {
            log.error("queryAllKindCouponListByShopId exception!", e);
        }
        logUtils.logInfo("queryIssueCouponActivity#. ctx: {}, result: {}", promoCtx, issueCouponActivities);


        if (CollectionUtils.isEmpty(issueCouponActivities)) {
            return issueCouponActivities;
        }

        List<IssueCouponActivity> filterIssueCouponActivities = Lists.newArrayListWithExpectedSize(issueCouponActivities.size());
        for (IssueCouponActivity issueCouponActivity : issueCouponActivities) {
            if (!filterActivity(String.valueOf(issueCouponActivity.getActivityId()), promoCtx)) {
                filterIssueCouponActivities.add(issueCouponActivity);
            }
        }

        return filterIssueCouponActivities;
    }

    private void filterNewUserActivity(List<IssueCouponActivity> activities, CouponActivityContext promoCtx) {
        //品牌新客校验结果
        Map<Integer, Boolean> brandNewUserValidateResult = Maps.newHashMap();
        //商品限制活动映射
        Map<Long, List<Integer>> productUnPassActivityMap = Maps.newHashMap();

        List<Integer> couponGroupIds = Lists.newArrayList();
        for (IssueCouponActivity activity : activities) {
            if (activity == null || activity.getCouponGroupId() <= 0) {
                continue;
            }
            couponGroupIds.add(activity.getCouponGroupId());
        }
        if (CollectionUtils.isEmpty(couponGroupIds)) {
            return;
        }

        Pair<Map<Integer, Boolean>, Map<Long, List<Integer>>> pair = validateAndFillResult(couponGroupIds, promoCtx);
        if (MapUtils.isNotEmpty(pair.getLeft())) {
            brandNewUserValidateResult.putAll(pair.getLeft());
        }
        if (MapUtils.isNotEmpty(pair.getRight())) {
            productUnPassActivityMap.putAll(pair.getRight());
        }

        int queryType = promoCtx.getQueryShopCouponType();
        long productId = queryType == QueryShopCouponTypeEnum.BY_DEAL.getCode() ?
                promoCtx.getUserType() == User.DP ? (long) promoCtx.getDpDealGroupId() : (long) promoCtx.getMtDealGroupId() :
                queryType == QueryShopCouponTypeEnum.BY_SPU.getCode() ? promoCtx.getSpuGroupId() : -1;
        Iterator<IssueCouponActivity> iterator = activities.iterator();
        while (iterator.hasNext()) {
            IssueCouponActivity issueCouponActivity = iterator.next();
            boolean isBrandNewUserCoupon = issueCouponActivity.getCouponUserType() != null && issueCouponActivity.getCouponUserType() == CouponUserTypeEnum.BRAND_NEW_USER.getCode();
            if (isBrandNewUserCoupon) {
                if (!Boolean.TRUE.equals(brandNewUserValidateResult.get(issueCouponActivity.getCouponGroupId()))) {
                    iterator.remove();
                    continue;
                }
            }
            if (productId < 0) {
                continue;
            }
            boolean isProductNewUserCoupon = issueCouponActivity.getCouponUserType() != null && issueCouponActivity.getCouponUserType() == CouponUserTypeEnum.PRODUCT_NEW_USER.getCode();
            if (isProductNewUserCoupon) {
                if (productUnPassActivityMap.containsKey(productId) && productUnPassActivityMap.get(productId).contains(issueCouponActivity.getCouponGroupId())) {
                    iterator.remove();
                }
            }
        }
    }

    private Pair<Map<Integer, Boolean>, Map<Long, List<Integer>>> validateAndFillResult(List<Integer> couponGroupIds, CouponActivityContext promoCtx) {
        Map<Integer, Boolean> brandNewUserValidateResult = Maps.newHashMap();
        Map<Long, List<Integer>> productUnPassActivityMap = Maps.newHashMap();
        int queryType = promoCtx.getQueryShopCouponType();
        int productCode = 0;
        List<Long> productIds = Lists.newArrayList();
        int type;
        if (queryType == QueryShopCouponTypeEnum.BY_SHOP.getCode()) {
            type = UnifiedCouponProductType.SHOPID.code;
        } else if (queryType == QueryShopCouponTypeEnum.BY_DEAL.getCode()) {
            productCode = CouponBusinessUtils.getDzTuangouProductCode();
            productIds = promoCtx.getUserType() == User.DP ?
                    Lists.newArrayList((long) promoCtx.getDpDealGroupId()) :
                    Lists.newArrayList((long) promoCtx.getMtDealGroupId());
            type = UnifiedCouponProductType.DEALGROUPID.code;
        } else if (queryType == QueryShopCouponTypeEnum.BY_SPU.getCode()) {
            productCode = promoCtx.getUserType() == User.DP ?
                    CouponBusiness.GENERAL_BOOK.getCode() :
                    CouponBusiness.CARD_BUSINESS_GENERAL_BOOK.getCode();
            productIds = Lists.newArrayList(promoCtx.getSpuGroupId());
            type = UnifiedCouponProductType.PRODUCTGROUPID.code;
        } else {
            return Pair.of(brandNewUserValidateResult, productUnPassActivityMap);
        }

        long userId = promoCtx.isMt() ? promoCtx.getMtUserId() : promoCtx.getDpUserId();
        MerchantNewUserValidateResult merchantNewUserValidateResult = couponNewUserWrapper.getMerchantNewUserResult(couponGroupIds, userId, promoCtx.getUserType().getCode(), productCode, productIds, type);
        if (merchantNewUserValidateResult != null) {
            if (MapUtils.isNotEmpty(merchantNewUserValidateResult.getBrandNewUserValidateResult())){
                brandNewUserValidateResult = merchantNewUserValidateResult.getBrandNewUserValidateResult();
            }
            if (MapUtils.isNotEmpty(merchantNewUserValidateResult.getProductUnPassActivityMap())) {
                productUnPassActivityMap = merchantNewUserValidateResult.getProductUnPassActivityMap();
            }
        }
        return Pair.of(brandNewUserValidateResult, productUnPassActivityMap);
    }

    private void fillRelatedInfo(List<IssueCouponActivity> activities, CouponActivityContext promotionRequestContext) {
        if (CollectionUtils.isEmpty(activities)) {
            return;
        }
        List<Integer> couponGroupIds = Lists.newArrayList();
        for (IssueCouponActivity activity : activities) {
            if (activity == null || activity.getCouponGroupId() <= 0) {
                continue;
            }
            couponGroupIds.add(activity.getCouponGroupId());
        }

        long userId;
        if (promotionRequestContext.isMt()) {
            userId = promotionRequestContext.getMtUserId();
        } else {
            userId = promotionRequestContext.getDpUserId();
        }

        Map<Integer, UnifiedCouponGroupDTO> couponGroupMap = couponBiz.batchQueryCouponGroup(couponGroupIds);
        List<UnifiedCouponDTO> unifiedCouponDTOs = couponBiz.batchQueryIssuedCouponList(userId, couponGroupIds, !promotionRequestContext.isMt());
        Map<Integer, List<UnifiedCouponDTO>> issuedCouponMap = Maps.newHashMap();
        for (UnifiedCouponDTO unifiedCouponDTO : unifiedCouponDTOs) {
            if (!issuedCouponMap.containsKey(unifiedCouponDTO.getCouponGroupDTO().getCouponGroupId())) {
                issuedCouponMap.put(unifiedCouponDTO.getCouponGroupDTO().getCouponGroupId(), Lists.<UnifiedCouponDTO>newArrayList());
            }
            issuedCouponMap.get(unifiedCouponDTO.getCouponGroupDTO().getCouponGroupId()).add(unifiedCouponDTO);
        }

        for (IssueCouponActivity activity : activities) {
            Validate.notNull(activity, "activity is null");
            Validate.notNull(couponGroupMap.get(activity.getCouponGroupId()), "couponGroup not exists");
            activity.setCouponGroup(couponGroupMap.get(activity.getCouponGroupId()));
            activity.setDpClient(!promotionRequestContext.isMt());
            List<UnifiedCouponDTO> issuedCoupon = issuedCouponMap.get(activity.getCouponGroupId());
            activity.setIssuedCoupon(issuedCoupon);
            activity.setIssuedUnUseUnExpireCoupon(IssueCouponUtils.buildUnUseUnExpiredCoupons(issuedCoupon));

        }
    }


}
