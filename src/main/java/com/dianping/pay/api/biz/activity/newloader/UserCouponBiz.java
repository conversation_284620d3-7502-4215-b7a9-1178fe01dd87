package com.dianping.pay.api.biz.activity.newloader;

import com.dianping.cat.Cat;
import com.dianping.unified.coupon.manage.api.response.UnifiedCouponManageResponse;
import com.sankuai.mpmkt.coupon.userinstance.api.UnifiedCouponInfoQueryService;
import com.sankuai.mpmkt.coupon.userinstance.api.enums.UnifiedCouponUserSource;
import com.sankuai.mpmkt.coupon.userinstance.api.request.UnifiedCouponUserIssueNumQueryRequest;
import org.apache.commons.collections.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Component
public class UserCouponBiz {

    private static final Logger logger = LoggerFactory.getLogger(UserCouponBiz.class);

    @Autowired
    private UnifiedCouponInfoQueryService unifiedCouponInfoQueryService;

    public Map<Integer, Integer> queryUserIssuedNum(long userId, int userType, List<Integer> couponGroupIds) {
        Cat.logEvent("INVALID_METHOD_1", "com.dianping.pay.api.biz.activity.newloader.UserCouponBiz.queryUserIssuedNum(long,int,java.util.List)");
        try {
            if (CollectionUtils.isEmpty(couponGroupIds)) {
                return new HashMap<Integer, Integer>();
            }
            UnifiedCouponUserIssueNumQueryRequest request = new UnifiedCouponUserIssueNumQueryRequest();
            request.setUserId(userId);
            request.setCouponGroupIdList(couponGroupIds);
            request.setStatus(0);
            request.setUserType(userType);
            UnifiedCouponManageResponse<Map<Integer, Integer>> response = unifiedCouponInfoQueryService.queryUserIssuedNum(request);
            if (null == response || !response.isSuccess() || null == response.getResult()) {
                Cat.logEvent("DC_Query_Fail", "queryUserIssuedNum_Fail");
                return new HashMap<Integer, Integer>();
            }
            return response.getResult();
        } catch (Exception e) {
            Cat.logEvent("DC_Query_Fail", "queryUserIssuedNum_Fail");
            logger.error("queryUserIssuedNum fails with msg[{}], userId[{}], couponGroupIds[{}]", e.getMessage(), userId, couponGroupIds, e);
            return new HashMap<Integer, Integer>();
        }
    }

}
