package com.dianping.pay.api.biz.promodeskcoupon;

import com.dianping.cat.Cat;
import com.dianping.pay.api.beans.GetPromoDeskCouponRequest;
import com.dianping.pay.api.beans.GetPromoDeskRequest;
import com.dianping.pay.api.beans.PromoProduct;
import com.dianping.pay.api.biz.discount.CouponMapper;
import com.dianping.pay.api.entity.promodesk.PromoDeskCoupon;
import com.dianping.pay.coupon.service.dto.CouponDTO;
import com.dianping.pay.coupon.service.dto.CouponProductDTO;
import com.dianping.pay.coupon.service.enums.CouponProductType;
import com.dianping.unified.coupon.manage.api.dto.*;
import com.dianping.unified.coupon.manage.api.response.UnifiedCouponManageResponse;

import java.util.List;
import java.util.Set;

public class ShanhuiPromoDeskCouponStrategy extends CommonPromoDeskCouponStrategy {

    private static final UnifiedCouponQueryOption queryOption = new UnifiedCouponQueryOption();
    static {
        queryOption.setTemplateId(2); // short time format
        queryOption.setFormatTitle(true);
        queryOption.setFormatDesc(true);
    }

    @Override
    public boolean assembleShopCouponSet(Set<PromoDeskCoupon> resultSet,
                                      Set<PromoDeskCoupon> unavailableSet,
                                      GetPromoDeskCouponRequest request,
                                      PromoProduct promoProduct) {
        Cat.logEvent("INVALID_METHOD_1", "com.dianping.pay.api.biz.promodeskcoupon.ShanhuiPromoDeskCouponStrategy.assembleShopCouponSet(Set,Set,GetPromoDeskCouponRequest,PromoProduct)");
        if (request.getStart() != 0) { // 暂时不翻页
            return true;
        }
        UnifiedCouponQueryContext context = CouponMapper.toCouponQueryContext(request, promoProduct, true);
        UnifiedCouponManageResponse<List<UnifiedCouponDTO>> listResponse = unifiedCouponListService.queryCouponByContext(context, queryOption, new UnifiedCouponListOption(5, 0, 0, 100));// limit 0,100
        if (listResponse.isSuccess()) {
            for (UnifiedCouponDTO couponDTO : listResponse.getResult()) {
                PromoDeskCoupon promoDeskCoupon = CouponMapper.toPromoDeskCoupon(promoProduct.getProductCode(), couponDTO);
                if (promoDeskCoupon.isCanUse()) {
                    resultSet.add(promoDeskCoupon);
                } else if (isCouponOfShop(couponDTO, request)) {
                    unavailableSet.add(promoDeskCoupon);
                }
            }
        }
        return true;
    }

    @Override
    public boolean isCouponOfShop(UnifiedCouponDTO couponDTO, GetPromoDeskRequest request) {
        Cat.logEvent("INVALID_METHOD_2", "com.dianping.pay.api.biz.promodeskcoupon.ShanhuiPromoDeskCouponStrategy.isCouponOfShop(com.dianping.unified.coupon.manage.api.dto.UnifiedCouponDTO,com.dianping.pay.api.beans.GetPromoDeskRequest)");
        long shopId = request.getShopIdL();
        if (shopId > 0) {
            for (UnifiedCouponProductDTO couponProductDTO : couponDTO.getCouponGroupDTO().getCouponProductList()) {
                if (couponProductDTO.getType() == CouponProductType.SHOPID.code
                        && couponProductDTO.getProductId() == shopId) {
                    return true;
                }
            }
        }
        return false;
    }
}
