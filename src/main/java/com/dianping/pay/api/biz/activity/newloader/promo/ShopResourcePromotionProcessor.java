package com.dianping.pay.api.biz.activity.newloader.promo;

import com.alibaba.fastjson.JSON;
import com.dianping.api.common.enums.PageSourceEnum;
import com.dianping.api.constans.CommonConstants;
import com.dianping.api.util.LionQueryUtils;
import com.dianping.cat.Cat;
import com.dianping.gmkt.scene.api.delivery.dto.req.GodMemberContextDTO;
import com.dianping.gmkt.scene.api.delivery.dto.req.QueryExposureResourcesReqDTO;
import com.dianping.gmkt.scene.api.delivery.dto.req.RiskContextDTO;
import com.dianping.gmkt.scene.api.delivery.dto.res.CouponDetailDTO;
import com.dianping.gmkt.scene.api.delivery.dto.res.DeliveryCommonResponse;
import com.dianping.gmkt.scene.api.delivery.dto.res.LocationMaterialsDTO;
import com.dianping.gmkt.scene.api.delivery.dto.res.ResourceExposureResponseDTO;
import com.dianping.gmkt.scene.api.delivery.enums.BizTypeEnum;
import com.dianping.gmkt.scene.api.delivery.enums.DeliveryErrorCode;
import com.dianping.gmkt.scene.api.delivery.enums.RecallTypeEnum;
import com.dianping.gmkt.scene.api.delivery.service.ResourcesExposureService;
import com.dianping.lion.client.Lion;
import com.dianping.mobile.framework.datatypes.IMobileContext;
import com.dianping.pay.api.beans.ResourceMaterialsDO;
import com.dianping.pay.api.beans.ShopResourcePromotionDO;
import com.dianping.pay.api.biz.activity.newloader.dto.CouponActivityContext;
import com.dianping.pay.api.enums.QueryShopCouponTypeEnum;
import com.dianping.pay.api.service.impl.ShopWrapperService;
import com.dianping.pay.api.util.ClientTypeUtils;
import com.dianping.pay.api.util.DotUtils;
import com.dianping.pay.api.util.LionConstants;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;

import com.meituan.mtrace.Tracer;
import com.sankuai.meituan.waimai.thrift.tools.cache.RegionSetConfigCache;
import com.sankuai.meituan.waimai.thrift.tools.enums.SetProxyTypeEnum;
import com.sankuai.meituan.waimai.thrift.tools.utils.MTraceRouterInfoUtil;
import com.sankuai.nib.mkt.common.base.util.JsonUtils;
import java.util.*;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.concurrent.Callable;
import java.util.concurrent.Future;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @date 2023/8/9
 */
@Component("shopResourcePromotionProcessor")
@Slf4j
public class ShopResourcePromotionProcessor extends AbstractPromoProcessor {

    @Autowired
    private ResourcesExposureService resourcesExposureService;

    @Autowired
    private ShopWrapperService shopWrapperService;

    public static final Set<Integer> HANDLE_RECALL_TYPES = Sets.newHashSet(RecallTypeEnum.RECALL_PRE_DRAW_COUPON.getCode(),
        RecallTypeEnum.RECALL_MULTI_COUPON.getCode(), RecallTypeEnum.RECALL_DIRECT_DRAW_COUPON.getCode(), RecallTypeEnum.ALREADY_DRAW_COUPON.getCode());

    @Override
    public void prePare(CouponActivityContext promoCtx, IMobileContext iMobileContext) {
        try {
            if (!promoCtx.isNeedShopResourcesPromotion()) {
                return;
            }
            if (promoCtx.isMt() && promoCtx.getMtUserId() <= 0) {
                return;
            }
            if (!promoCtx.isMt() && promoCtx.getDpUserId() <= 0) {
                return;
            }
            if (promoCtx.getCityId() <= 0 || promoCtx.getDpShopIdL() <= 0) {
                return;
            }
            if (PageSourceEnum.BeautyMedicalPOIDetail.code.equals(promoCtx.getCouponPageSource()) &&
                    LionQueryUtils.PREPAY_LQG_RAINBOW_DEGRADE_SWITCH) {
                return;
            }

            DotUtils.dotForPromoProcessor(promoCtx, getPromoName());
            Future<ShopResourcePromotionDO> future = ExecutorService.submit(new Callable<ShopResourcePromotionDO>() {
                @Override
                public ShopResourcePromotionDO call() throws Exception {
                    return loadResources(promoCtx, iMobileContext);
                }
            });
            promoCtx.setShopResourceExposureFuture(future);
        } catch (Exception e) {
            log.error("shopResourcePromotionProcessor# prepare error. ctx: {}", promoCtx, e);
        }
    }

    @Override
    public void loadPromo(CouponActivityContext promoCtx) {
        try {
            Future<ShopResourcePromotionDO> shopResourceExposureFuture = promoCtx.getShopResourceExposureFuture();
            if (shopResourceExposureFuture != null) {
                promoCtx.setShopResourcePromotionDO(shopResourceExposureFuture.get(500, TimeUnit.MILLISECONDS));
            }
        } catch (Exception e) {
            log.error("shopResourcePromotionProcessor# loadPromo error. ctx: {}", promoCtx, e);
        }
    }

    @Override
    public String getPromoName() {
        return "shopResourcePromo";
    }

    private ShopResourcePromotionDO loadResources(CouponActivityContext promoCtx, IMobileContext iMobileContext) {
        try {
            // regionId埋入trace, 背景见https://km.sankuai.com/collabpage/**********
            String regionId = promoCtx.getRegionId();
            if (StringUtils.isNotBlank(regionId)) {
                String setName = RegionSetConfigCache.getCellNameByRegionId(regionId);
                MTraceRouterInfoUtil.setTraceRoutingInfo4Api(SetProxyTypeEnum.BY_USER_ID, promoCtx.getMtUserId(), regionId, setName);
            }

            long userId;
            if (promoCtx.isMt()) {
                userId = promoCtx.getMtUserId();
            } else {
                userId = promoCtx.getDpUserId();
            }

            Long locationId = getLocationId(promoCtx);
            if (locationId == null || locationId <= 0) {
                log.error("ShopResourcePromotionProcessor LocationId illegal, locationId:{}, promoCtx:{}", locationId, promoCtx);
                return null;
            }

            QueryExposureResourcesReqDTO queryExposureResourcesReqDTO = new QueryExposureResourcesReqDTO();
            queryExposureResourcesReqDTO.setResourceLocationId(locationId);
            queryExposureResourcesReqDTO.setCityId(promoCtx.getCityId());
            queryExposureResourcesReqDTO.setLatitude(promoCtx.getLatitude());
            queryExposureResourcesReqDTO.setLongitude(promoCtx.getLongitude());
            queryExposureResourcesReqDTO.setUserId(userId);
            if (StringUtils.isNotBlank(promoCtx.getUuid())) {
                queryExposureResourcesReqDTO.setUuid(promoCtx.getUuid());
            } else {
                queryExposureResourcesReqDTO.setUuid(promoCtx.getDpid());
            }
            queryExposureResourcesReqDTO.setPageSource(promoCtx.getPageSource());

            RiskContextDTO riskContextDTO = new RiskContextDTO();
            riskContextDTO.setIp(iMobileContext.getUserIp());
            riskContextDTO.setUserAgent(iMobileContext.getUserAgent());
            riskContextDTO.setVersion(iMobileContext.getVersion());
            riskContextDTO.setRequestUrl(iMobileContext.getRequest().getRequestURI());
            if (StringUtils.isNotBlank(promoCtx.getUuid())) {
                riskContextDTO.setUuid(promoCtx.getUuid());
            } else {
                riskContextDTO.setUuid(promoCtx.getDpid());
            }
            if (!promoCtx.isMt() && StringUtils.isNotBlank(promoCtx.getDpid())) {
                riskContextDTO.setDpid(promoCtx.getDpid());
            }
            riskContextDTO.setPlatform(promoCtx.getCPlatform() == 1 ? CommonConstants.RAINBOW_IOS : CommonConstants.RAINBOW_ANDROID);
            int payPlatform = promoCtx.getCouponPlatform();
            if (ClientTypeUtils.isMainWX(payPlatform, promoCtx.isMt())) {
                riskContextDTO.setWechatFingerprint(promoCtx.getCx());
            } else if (ClientTypeUtils.isMainWeb(payPlatform, promoCtx.isMt())) {
                riskContextDTO.setH5Fingerprint(promoCtx.getCx());
            }
            if (StringUtils.isNotBlank(promoCtx.getMtFingerprint())){
                riskContextDTO.setFingerprint(promoCtx.getMtFingerprint());
            } else {
                riskContextDTO.setFingerprint(promoCtx.getCx());
            }
            riskContextDTO.setPhone(promoCtx.getPhone());
            riskContextDTO.setClientType(ClientTypeUtils.getRiskClientType(iMobileContext));
            riskContextDTO.setMtgsig(promoCtx.getMtgsig());
            queryExposureResourcesReqDTO.setRiskContextDTO(riskContextDTO);

            queryExposureResourcesReqDTO.setPlatform(promoCtx.isMt() ? 2 : 1);
            Pair<List<Integer>, List<Integer>> categoryPair = shopWrapperService.loadShopCategory(promoCtx.getDpShopIdL());
            if (categoryPair == null) {
                return null;
            }
            queryExposureResourcesReqDTO.setFirstCategoryList(categoryPair.getLeft());
            queryExposureResourcesReqDTO.setSecondCategoryList(categoryPair.getRight());

            long shopId = promoCtx.isMt() ? promoCtx.getMtShopIdL() : promoCtx.getDpShopIdL();
            if (Lion.getBoolean(LionConstants.APP_KEY, LionConstants.SHOP_RESOURCE_PARAM_CHANGE_SWITCH, true)) {
                queryExposureResourcesReqDTO.setShopId((int) shopId);
            } else {
                queryExposureResourcesReqDTO.setBizIds(Lists.newArrayList(String.valueOf(shopId)));
                queryExposureResourcesReqDTO.setBizIdsType(BizTypeEnum.POI_ID.getCode());
            }
            queryExposureResourcesReqDTO.setShopIdL(shopId);
            queryExposureResourcesReqDTO.setLocationCityId(promoCtx.getActualCityId());
            fillGodMemberContext(queryExposureResourcesReqDTO, promoCtx);
            DeliveryCommonResponse<List<ResourceExposureResponseDTO>> response = resourcesExposureService.queryExposureResources(queryExposureResourcesReqDTO);
            log.info("ShopResourcePromotionProcessor call queryExposureResources# request: {}, resp: {}", JsonUtils.toJSONString(queryExposureResourcesReqDTO), JsonUtils.toJSONString(response));
            if (response == null || !DeliveryErrorCode.SUCESS.getErrorCode().equals(response.getCode())) {
                DotUtils.dotForPromoCallFail(getPromoName(), "queryExposureResources", promoCtx.isMt());
                log.warn("ShopResourcePromotionProcessor call queryExposureResources failed. request: {}, resp: {}", queryExposureResourcesReqDTO, response);
                return null;
            }
            if (CollectionUtils.isEmpty(response.getData())) {
                DotUtils.dotForPromoHit(getPromoName(), promoCtx.isMt(), false);
                return null;
            }
            DotUtils.dotForPromoHit(getPromoName(), promoCtx.isMt(), true);

            ResourceExposureResponseDTO exposureResponseDTO = response.getData().get(0);
            if (CollectionUtils.isEmpty(exposureResponseDTO.getMaterialsDTOList()) || MapUtils.isEmpty(exposureResponseDTO.getStaticMaterialContext())) {
                return null;
            }
            Map<String, String> staticMaterialContext = exposureResponseDTO.getStaticMaterialContext();
            //仅支持领券模块样式的投放活动
            if (!staticMaterialContext.containsKey(CommonConstants.SEND_COUPON_TYPE) || !CommonConstants.SUPPORT_SEND_TYPE.contains(staticMaterialContext.get(CommonConstants.SEND_COUPON_TYPE))) {
                return null;
            }

            ShopResourcePromotionDO shopResourcePromotionDO = buildResourcePromotionDo(exposureResponseDTO);
            if (shopResourcePromotionDO == null) {
                log.warn("buildResourcePromotionDo null, exposureResponseDTO:{}", exposureResponseDTO);
                return null;
            }
            //打点已领券、待领券、新塞券的占比信息
            DotUtils.dotForPoiRainbowCoupon(shopResourcePromotionDO);
            return shopResourcePromotionDO;
        } catch (Exception e) {
            log.warn("ShopResourcePromotionProcessor failed. promoCtx: {}", promoCtx, e);
        }
        return null;
    }

    private void fillGodMemberContext(QueryExposureResourcesReqDTO queryExposureResourcesReqDTO, CouponActivityContext promoCtx) {
        GodMemberContextDTO godMemberContextDTO = new GodMemberContextDTO();
        if (PageSourceEnum.BeautyMedicalPOIDetail.code.equals(promoCtx.getCouponPageSource())) {
            godMemberContextDTO.setTransactionType(3);
            godMemberContextDTO.setBizCategoryType(12);
        }
        if (!promoCtx.isMt() && promoCtx.getMtUserId() >= 0 && promoCtx.getMtShopIdL() >= 0) {
            godMemberContextDTO.setMtRealUserId(promoCtx.getMtUserId());
            godMemberContextDTO.setMtShopId(promoCtx.getMtShopIdL());
            godMemberContextDTO.setMtCityId(promoCtx.getMtCityId());
            godMemberContextDTO.setMtLocationCityId(promoCtx.getMtActualCityId());
            godMemberContextDTO.setMtVirtualUserId(promoCtx.getMtVirtualUserId());
        }
        queryExposureResourcesReqDTO.setGodMemberContextDTO(godMemberContextDTO);
    }

    private Long getLocationId(CouponActivityContext promoCtx) {
        try {
            Map<String, String> locationMap = Lion.getMap(LionConstants.APP_KEY, LionConstants.SHOP_RESOURCE_LOCATION_CONFIG, String.class);
            int queryShopCouponType = PageSourceEnum.BeautyMedicalPOIDetail.code.equals(promoCtx.getCouponPageSource()) ? QueryShopCouponTypeEnum.BY_SKU.getCode() : promoCtx.getQueryShopCouponType();
            String key = promoCtx.isMt() ? "MT_" + queryShopCouponType : "DP_" + queryShopCouponType;
            return Long.valueOf(locationMap.getOrDefault(key, "-1"));
        } catch (Exception e) {
            log.error("ShopResourcePromotionProcessor getLocationId ex, promoCtx:{}", promoCtx, e);
            return -1L;
        }
    }

    private ShopResourcePromotionDO buildResourcePromotionDo(ResourceExposureResponseDTO resourceExposureResponseDTO) {
        ShopResourcePromotionDO shopResourcePromotionDO = new ShopResourcePromotionDO();
        shopResourcePromotionDO.setActivityId(resourceExposureResponseDTO.getActivityId());
        shopResourcePromotionDO.setResourceLocationId(resourceExposureResponseDTO.getResourceLocationId());
        shopResourcePromotionDO.setFlowId(resourceExposureResponseDTO.getFlowId());
        shopResourcePromotionDO.setRowKey(resourceExposureResponseDTO.getRowKey());
        shopResourcePromotionDO.setStaticMaterialContext(resourceExposureResponseDTO.getStaticMaterialContext());
        List<ResourceMaterialsDO> resourceMaterialsDOS = Lists.newArrayList();
        Set<Integer> drawStatuses = Sets.newHashSet();
        boolean result = false;
        for (LocationMaterialsDTO materialsDTO : resourceExposureResponseDTO.getMaterialsDTOList()) {
            if (!HANDLE_RECALL_TYPES.contains(materialsDTO.getRecallType())) {
                continue;
            }
            if (CollectionUtils.isEmpty(materialsDTO.getCouponDetailDTOS())) {
                continue;
            }

            drawStatuses.add(materialsDTO.getDrawStatus());
            ResourceMaterialsDO resourceMaterialsDO = new ResourceMaterialsDO();
            resourceMaterialsDO.setCouponDetailDTOS(filterCouponDetail(materialsDTO.getCouponDetailDTOS()));
            resourceMaterialsDO.setActionType(materialsDTO.getActionType());
            resourceMaterialsDO.setMaterialId(materialsDTO.getMaterialId());
            resourceMaterialsDO.setMaterialType(materialsDTO.getMaterialType());
            resourceMaterialsDO.setPlatform(materialsDTO.getPlatform());
            resourceMaterialsDO.setRecallType(materialsDTO.getRecallType());
            resourceMaterialsDO.setPrizeCouponAmount(materialsDTO.getPrizeCouponAmount());
            resourceMaterialsDO.setCouponUserTagDescription(materialsDTO.getCouponUserTagDescription());
            RecallTypeEnum recallTypeEnum = RecallTypeEnum.getByCode(materialsDTO.getRecallType());
            if (recallTypeEnum == RecallTypeEnum.RECALL_DIRECT_DRAW_COUPON || recallTypeEnum == RecallTypeEnum.ALREADY_DRAW_COUPON) {
                if (Objects.equals(materialsDTO.getDrawStatus(), CommonConstants.CAN_DRAW)) {
                    Cat.logEvent("SHOP_RESOURCE_PROMOTION", "recallTypeDraw-statusError");
                }
            }
            if (!result) {
                result = recallTypeEnum == RecallTypeEnum.RECALL_DIRECT_DRAW_COUPON;
            }
            resourceMaterialsDOS.add(resourceMaterialsDO);
        }
        shopResourcePromotionDO.setDisplayBannerFlag(result);
        //存在不同领取状态，不展示
        if (drawStatuses.size() > 1) {
            shopResourcePromotionDO.setDisplayBannerFlag(Boolean.FALSE);
            return null;
        }
        if (CollectionUtils.isEmpty(resourceMaterialsDOS)) {
            return null;
        }
        shopResourcePromotionDO.setResourceMaterialsDOS(resourceMaterialsDOS);
        //1-已领取  2-可领取
        if (drawStatuses.contains(CommonConstants.DRAWED)) {
            shopResourcePromotionDO.setDrawStatus(CommonConstants.DRAWED);
        } else if (drawStatuses.contains(CommonConstants.CAN_DRAW)) {
            shopResourcePromotionDO.setDrawStatus(CommonConstants.CAN_DRAW);
        } else {
            return null;
        }
        return shopResourcePromotionDO;
    }

    private List<CouponDetailDTO> filterCouponDetail(List<CouponDetailDTO> resource) {
        List<CouponDetailDTO> couponDetailDTOS = new ArrayList<>();
        for (CouponDetailDTO couponDetailDTO : resource) {
            if (couponDetailDTO != null && couponDetailDTO.getAmountPrice() != null) {
                couponDetailDTOS.add(couponDetailDTO);
            }
        }
        return couponDetailDTOS;
    }

}
