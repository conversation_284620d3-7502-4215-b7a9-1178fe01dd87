package com.dianping.pay.api.biz.activity.newloader;

import com.dianping.cat.Cat;
import com.dianping.cat.message.Transaction;
import com.dianping.pay.api.beans.IssueCouponActivity;
import com.dianping.pay.api.biz.discount.CouponBiz;
import com.dianping.pay.api.entity.issuecoupon.IssueCouponRequest;
import com.dianping.pay.api.enums.IssueCouponOptionType;
import com.dianping.pay.api.util.IssueCouponUtils;
import com.dianping.pay.promo.common.enums.UnifiedCouponTargetEnum;
import com.dianping.tgc.open.entity.ActivityDTO;
import com.dianping.unified.coupon.manage.api.dto.UnifiedCouponDTO;
import com.dianping.unified.coupon.manage.api.dto.UnifiedCouponGroupDTO;
import com.dianping.violet.enums.BeautyConfigEnum;
import com.dianping.violet.enums.CouponRequestTypeEnum;
import com.dianping.violet.service.BeautyCouponService;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import org.jsoup.helper.Validate;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.*;
import java.util.concurrent.Callable;
@Deprecated
public abstract class AbstractCouponIssueActivityQueryExecutor implements Callable<List<IssueCouponActivity>> {

    private static final Logger LOGGER = LoggerFactory.getLogger(AbstractCouponIssueActivityQueryExecutor.class);

    private CouponBiz couponBiz;

    protected IssueCouponRequest request;

    protected BeautyCouponService beautyCouponService;

    protected AbstractCouponIssueActivityQueryExecutor(IssueCouponRequest request) {
        this.request = request;
    }

    @Override
    public List<IssueCouponActivity> call() throws Exception {
        Transaction t = Cat.newTransaction("CouponIssueActivity", this.getClass().getName());
        try {
            if (request.getUserId() <= 0) {
                Cat.logEvent("InvalidUserId", this.getClass().getName());
            }
            List<IssueCouponActivity> activities = loadFromRemote();
            Iterator<IssueCouponActivity> iterator = activities.iterator();
            while (iterator.hasNext()) {
                IssueCouponActivity activity = iterator.next();
                try {
                    fillRelatedInfo(activity);
                } catch (Exception e) {
                    iterator.remove();
                }
            }
            t.setStatus(Transaction.SUCCESS);
            return activities;
        } catch (Exception e) {
            LOGGER.error("couponIssueActivityQueryExecutor error", e);
            t.setStatus(e.getClass().getSimpleName());
            return Collections.emptyList();
        } finally {
            t.complete();
        }
    }

    protected abstract List<IssueCouponActivity> loadFromRemote();

    private void fillRelatedInfo(IssueCouponActivity activity) {
        UnifiedCouponGroupDTO couponGroup = couponBiz.queryCouponGroup(activity.getCouponGroupId());
        Validate.notNull(couponGroup, "couponGroup not exists");
        activity.setCouponGroup(couponGroup);
        activity.setDpClient(request.isDpClient());

        if (couponGroup.getTarget() == UnifiedCouponTargetEnum.meituan.getCode()) {
            int issuedCount = couponBiz.queryIssuedCount(request.getUserId(), activity.getCouponGroupId(), request.isDpClient());
            if (issuedCount > 0) {
                activity.setMtHongbaoIssued(true);
            }
        } else {
            List<UnifiedCouponDTO> unifiedCouponDTOs = couponBiz.queryIssuedCouponList(request.getUserId(), activity.getCouponGroupId(), request.isDpClient());
            activity.setIssuedCoupon(unifiedCouponDTOs);
            activity.setIssuedUnUseUnExpireCoupon(IssueCouponUtils.buildUnUseUnExpiredCoupons(unifiedCouponDTOs));

        }

        // 丽人领券组件
        if (request.getRequestType() == CouponRequestTypeEnum.DISPLAY.getCode()) {
            if (activity.getIcon() == null) {
                Map<String, String> beautyConfig = beautyCouponService.getBeautyConfig(request.isDpClient() ? 1 : 2);
                activity.setIcon(beautyConfig.get(activity.getType() == IssueCouponOptionType.BEAUTY_EVENT.getCode() ? BeautyConfigEnum.BEAUTY_ICON.key : BeautyConfigEnum.SHOP_ICON.key));
            }
        }
    }


    public AbstractCouponIssueActivityQueryExecutor setCouponBiz(CouponBiz couponBiz) {
        this.couponBiz = couponBiz;
        return this;
    }

    public AbstractCouponIssueActivityQueryExecutor setBeautyCouponService(BeautyCouponService beautyCouponService) {
        this.beautyCouponService = beautyCouponService;
        return this;
    }

    public List<IssueCouponActivity> toIssueCouponActivities(Map<Integer, List<ActivityDTO>> activityMap) {
        List<ActivityDTO> activityDTOs = Lists.newArrayList();
        Set<Integer> activityIdSet = Sets.newHashSet();
        for (List<ActivityDTO> activityDTOList : activityMap.values()) {
            for (ActivityDTO activityDTO : activityDTOList) {
                if (activityIdSet.add(activityDTO.getActivityID())) {
                    activityDTOs.add(activityDTO);
                }
            }
        }

        List<IssueCouponActivity> resultList = Lists.newArrayList();
        for (ActivityDTO activityDTO : activityDTOs) {
            try {
                if (activityDTO.getCouponDTO().isUpDailyLimit()) {
                    continue;
                }
                resultList.add(new IssueCouponActivity(
                        activityDTO,
                        request.isDpClient() ? IssueCouponOptionType.DP_SHOP_EVENT.getCode() : IssueCouponOptionType.MT_SHOP_EVENT.getCode()
                ));
            } catch (IllegalArgumentException e) {

            }
        }
        return resultList;
    }

    public List<IssueCouponActivity> toIssueCouponActivitiesL(Map<Long, List<ActivityDTO>> activityMap) {
        List<ActivityDTO> activityDTOs = Lists.newArrayList();
        Set<Integer> activityIdSet = Sets.newHashSet();
        for (List<ActivityDTO> activityDTOList : activityMap.values()) {
            for (ActivityDTO activityDTO : activityDTOList) {
                if (activityIdSet.add(activityDTO.getActivityID())) {
                    activityDTOs.add(activityDTO);
                }
            }
        }

        List<IssueCouponActivity> resultList = Lists.newArrayList();
        for (ActivityDTO activityDTO : activityDTOs) {
            try {
                if (activityDTO.getCouponDTO().isUpDailyLimit()) {
                    continue;
                }
                resultList.add(new IssueCouponActivity(
                        activityDTO,
                        request.isDpClient() ? IssueCouponOptionType.DP_SHOP_EVENT.getCode() : IssueCouponOptionType.MT_SHOP_EVENT.getCode()
                ));
            } catch (IllegalArgumentException e) {
                LOGGER.error("toIssueCouponActivitiesL IllegalArgumentException", e);
            }
        }
        return resultList;
    }
}
