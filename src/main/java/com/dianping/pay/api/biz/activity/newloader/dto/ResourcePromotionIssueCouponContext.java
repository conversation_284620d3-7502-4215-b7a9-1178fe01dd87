package com.dianping.pay.api.biz.activity.newloader.dto;

import com.dianping.pay.api.entity.issuecoupon.IssueCouponRequest;
import lombok.Data;

/**
 * 彩虹发劵上下文
 */
@Data
public class ResourcePromotionIssueCouponContext {

    /* ==========origin value ========== **/

    private IssueCouponRequest originIssueCouponRequest;

    /* ===========query value ==============*/

    /**
     * 美团实id
     */
    private Long mtRealUserId;

    /**
     * 美团虚id
     */
    private Long mtVirtualUserId;

    /**
     * 门店id-美团体系
     */
    private Long mtShopId;

    /**
     * 首页城市id 美团体系
     */
    private Integer mtCityId;

    /**
     * 定位城市id 美团体系
     */
    private Integer mtActualCityId;

    /**
     * 用户手机号
     */
    private String mobilePhone;

    public ResourcePromotionIssueCouponContext(IssueCouponRequest originIssueCouponRequest) {
        this.originIssueCouponRequest = originIssueCouponRequest;
    }
}
