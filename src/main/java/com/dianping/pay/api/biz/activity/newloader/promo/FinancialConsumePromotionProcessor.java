package com.dianping.pay.api.biz.activity.newloader.promo;

import com.alibaba.fastjson.JSONObject;
import com.dianping.cat.Cat;
import com.dianping.gmkt.scene.api.delivery.enums.DeliveryErrorCode;
import com.dianping.mobile.framework.datatypes.IMobileContext;
import com.dianping.pay.api.biz.activity.newloader.dto.CouponIssueActivityQueryContext;
import com.dianping.pay.api.entity.issuecoupon.IssueCouponRequest;
import com.dianping.pay.api.entity.issuecoupon.UnifiedIssueResult;
import com.dianping.pay.api.exception.IssueCouponException;
import com.dianping.pay.api.util.JsonUtils;
import com.meituan.pay.paycoupon.sdk.AssignCouponReqVo;
import com.meituan.pay.paycoupon.sdk.PayCouponExternalService;
import com.meituan.pay.paycoupon.sdk.SyncAssignCouponResTo;
import com.sankuai.wpt.user.merge.query.thrift.message.BindRelationResp;
import com.sankuai.wpt.user.merge.query.thrift.message.UserMergeQueryService;
import java.util.Date;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import static com.dianping.pay.api.enums.GetCouponStatus.GET_COUPON_FAIL;

@Component("financialConsumePromotionProcessor")
@Slf4j
public class FinancialConsumePromotionProcessor {

    @Autowired
    private PayCouponExternalService.Iface payCouponExternalClient;

    @Autowired
    private UserMergeQueryService.Iface rpcUserService;

    private static final int COUPON_COMPONENT_SENDER = 1054;

    private static final String FINANCIAL_COUPON_ISSUE_EVENT = "FinancialConsumeCouponIssueEvent";

    public UnifiedIssueResult doIssueGovConsumeCoupon(IssueCouponRequest issueCouponRequest, CouponIssueActivityQueryContext context) {
        try {
            if(StringUtils.isEmpty(context.getSerialno())) {
                Cat.logEvent(FINANCIAL_COUPON_ISSUE_EVENT, "getSerialNOEmpty");
                throw new IssueCouponException("领取失败", GET_COUPON_FAIL);
            }
            AssignCouponReqVo request = buildAssignCouponReqVo(issueCouponRequest, context);
            if(request == null) {
                Cat.logEvent(FINANCIAL_COUPON_ISSUE_EVENT, "buildRemoteRequestError");
                throw new IssueCouponException("领取失败", GET_COUPON_FAIL);
            }
            SyncAssignCouponResTo response = payCouponExternalClient.assignPayCoupon(request);
            log.info("request:{}, response:{}", JsonUtils.toJson(request), JsonUtils.toJson(response));
            if (response == null
                || response.status == null
                || !response.status.equals("success")
                || response.getData() == null) {
                Cat.logEvent(FINANCIAL_COUPON_ISSUE_EVENT, "callResponseError");
                throw new IssueCouponException("领取失败", GET_COUPON_FAIL);
            }
            UnifiedIssueResult result = new UnifiedIssueResult();
            result.setCouponGroupName(response.getData().getName());
            result.setUnifiedCouponId(context.getUnifiedCouponGroupId());
            return result;
        } catch (Exception e) {
            Cat.logEvent(FINANCIAL_COUPON_ISSUE_EVENT, "callException");
            log.error("issue doIssueGovConsume coupon exception. request: {}, context:{}", JsonUtils.toJson(issueCouponRequest), JsonUtils.toJson(context), e);
            throw new IssueCouponException("领取失败", GET_COUPON_FAIL);
        }
    }

    private AssignCouponReqVo buildAssignCouponReqVo(IssueCouponRequest issueCouponRequest, CouponIssueActivityQueryContext context) {
        AssignCouponReqVo assignCouponReqVo = new AssignCouponReqVo();
        assignCouponReqVo.setSerialNo(Long.valueOf(context.getSerialno()));
        assignCouponReqVo.setSign("");
        assignCouponReqVo.setSender(COUPON_COMPONENT_SENDER);
        assignCouponReqVo.setApplyId(Long.valueOf(context.getUnifiedCouponGroupId()));
        assignCouponReqVo.setMarketingId(100L);
        long mtUserId = 0;
        if(context.isDpClient()) {
            mtUserId = getMtRealUserId(issueCouponRequest.getUserId());
        } else {
            mtUserId = issueCouponRequest.getUserId();
        }
        if(mtUserId <= 0) {
            return null;
        }
        assignCouponReqVo.setUserId(mtUserId);
        if (StringUtils.isNotBlank(issueCouponRequest.getPackagesecretkey())){
            assignCouponReqVo.setCouponPackage(issueCouponRequest.getPackagesecretkey());
        } else {
            assignCouponReqVo.setCouponPackage(StringUtils.EMPTY);
        }
        return assignCouponReqVo;
    }

    private Long getMtRealUserId(long dpUserId) {
        try {
            BindRelationResp realBind = rpcUserService.getRealBindByDpUserId(dpUserId);
            if (realBind != null && realBind.isSuccess()) {
                return realBind.getData() == null ? null : (realBind.getData().getMtUserId() == null ? null : realBind.getData().getMtUserId().getId());
            }else{
                log.info("doIssueGovConsumeCoupon getMtRealUserId fail,dpUserId:" + dpUserId);
            }
        } catch (Exception e) {
            log.error("doIssueGovConsumeCoupon getMtRealUserId error, dpUserId:" + dpUserId, e);
        }
        return null;
    }
}
