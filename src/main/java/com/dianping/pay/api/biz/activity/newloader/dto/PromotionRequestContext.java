package com.dianping.pay.api.biz.activity.newloader.dto;

import com.dianping.pay.api.biz.activity.newloader.promo.MagicalMemberCouponProcessor;
import com.dianping.pay.promo.common.enums.User;
import lombok.Data;

import java.util.List;
import java.util.Map;

/**
 * @Author: clark
 * Created At: 2020-04-09 21:05
 */
@Data
public class PromotionRequestContext {

    private String cx;
    private Double latitude;
    private Double longitude;
    private String dpid;

    private int cityId;
    private int actualCityId;

    private long dpUserId;
    private long mtUserId;

    private long dpShopIdL;
    private long mtShopIdL;

    private int couponPlatform;
    private int payPlatform;

    //0:点评 1:美团
    private User userType;
    private String version;

    private String uuid;
    private int cPlatform;
    private String mtFingerprint;
    private String mtgsig;
    private String regionId;

    //渠道标识
    private String channel;

    //是否需要返回返礼信息
    private boolean needReturnPromotion;
    //是否要分享券信息
    private boolean needShareCouponPromotion;
    //是否要立减信息
    private boolean needReductionPromotion;
    //是否要投放资源信息
    private boolean needResourcesPromotion;
    //是否要用户券
    private boolean needUserPromotion;
    //是否要品牌券活动
    private boolean needBrandPromotion;
    //是否要考虑会员价
    private boolean needMemberPromotion;
    //是否需要金融券
    private boolean needFinalcialCouponPromotion;
    //是否通过商品价格服务获取优惠信息
    private boolean needQueryPricePromotion;
    //是否要返回门店投放资源信息
    private boolean needShopResourcesPromotion;
    //是否返回商家营销账户平台券信息
    private boolean needMerchantAccountPromotion;
    //是否返回top广告投放资源信息
    private boolean needTopAdResourcePromotion;
    //是否返回神会员券
    private boolean needMagicalMemberCoupon;



    private int skuId;
    private long spuGroupId;
    private int dpDealGroupId;
    private int mtDealGroupId;

    //-1: 不查商家券 0：根据门店查询对应 1：团购 2：商品 3:根据批量商品查
    private int queryShopCouponType = 0;

    /**
     * @see com.dianping.pay.api.enums.ProductTypeEnum
     */
    private int productType;

    private List<Integer> skuIds;
    private List<Integer> dpDealGroupIds;
    private List<Integer> mtDealGroupIds;
    private long brandId;

    private Boolean beautyDealNewType;
    private Boolean needDiscountCoupon;

    private Map<String, String> extParam;

    private String pageSource;

    private String couponPageSource;

    private String trafficSource;

    private String business;

    private boolean needResetUserId;

    // poi新老页面标识, 目前仅用于打点判断
    private Integer poiPageSource;

    private Integer miniProgramFlag;

    private String appId;

    private String openId;

    private Integer cposition;


    /**
     * 神券是否可膨，透传给优惠台
     */
    private String mmcInflate;

    /**
     * 神券是否可用，透传给优惠台
     */
    private String mmcUse;

    /**
     * 券包是否可买，透传给优惠台
     */
    private String mmcBuy;

    /**
     * 神券是否可领塞，透传给优惠台
     */
    private String mmcFree;

    /*
     * 神券组件版本
     */
    private String magicMemberComponentVersion;

    public boolean isMt(){
        return userType == User.MT;
    }


    public long getUserId(){
        return isMt()?mtUserId:dpUserId;
    }

    public long getShopIdL() {
        return isMt() ? mtShopIdL : dpShopIdL;
    }

    public int getCpositionOrDefault() {
        if (cposition != null && cposition != 0) {
            return cposition;
        }
       return isMt() ? MagicalMemberCouponProcessor.POI_POSITION : MagicalMemberCouponProcessor.DP_POI_POSITION;
    }

    public boolean isMiniProgram() {
        return miniProgramFlag != null && miniProgramFlag == 1;
    }
}
