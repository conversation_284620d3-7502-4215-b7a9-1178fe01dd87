package com.dianping.pay.api.biz.activity.newloader.promo;

import com.dianping.cat.Cat;
import com.dianping.lion.client.Lion;
import com.dianping.mobile.framework.datatypes.IMobileContext;
import com.dianping.pay.api.biz.activity.PreIssueValidateBiz;
import com.dianping.pay.api.biz.activity.newloader.dto.CouponActivityContext;
import com.dianping.pay.api.biz.discount.CouponBiz;
import com.dianping.pay.api.entity.issuecoupon.BrandActivityIssueCouponRequest;
import com.dianping.pay.api.entity.issuecoupon.UnifiedIssueDetailResult;
import com.dianping.pay.api.enums.CouponClientTypeEnum;
import com.dianping.pay.api.exception.IssueCouponException;
import com.dianping.pay.api.util.*;
import com.dianping.pay.coupon.service.enums.CouponOperationResultCode;
import com.dianping.pay.promo.common.enums.User;
import com.dianping.tgc.open.CouponActivityQueryService;
import com.dianping.tgc.open.entity.BrandActivityDTO;
import com.dianping.tgc.open.entity.BrandActivityQueryRequest;
import com.dianping.tgc.open.enums.BActivityQuerySceneEnum;
import com.dianping.tgc.open.enums.QuerySourceEnum;
import com.dianping.tgc.open.util.ResultResponse;
import com.dianping.unified.coupon.issue.api.UnifiedCouponIssueTrustService;
import com.dianping.unified.coupon.issue.api.dto.UnifiedCouponIssueDetail;
import com.dianping.unified.coupon.issue.api.dto.UnifiedCouponIssueOption;
import com.dianping.unified.coupon.issue.api.response.UnifiedCouponIssueResponse;
import com.dianping.unified.coupon.manage.api.dto.UnifiedCouponDTO;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.sankuai.mpmctbrand.brandc.api.brand.BrandThriftService;
import com.sankuai.mpmctbrand.brandc.dto.brand.BatchQryBrandInfoByBrandIdReqDTO;
import com.sankuai.mpmctbrand.brandc.dto.brand.BatchQryBrandInfoByBrandIdRespDTO;
import com.sankuai.mpmctbrand.brandc.dto.brand.BrandInfoDTO;
import com.sankuai.mpmkt.coupon.issue.api.IssueCouponService;
import com.sankuai.mpmkt.coupon.issue.api.request.CouponIssueInfo;
import com.sankuai.mpmkt.coupon.issue.api.request.CouponIssueRequest;
import org.apache.commons.collections.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;
import org.tuckey.web.filters.urlrewrite.utils.StringUtils;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.Future;
import java.util.concurrent.TimeUnit;

import static com.dianping.pay.api.enums.GetCouponStatus.GET_COUPON_FAIL;

@Component("brandActivityPromotionProcessor")
public class BrandActivityPromotionProcessor extends AbstractPromoProcessor {

    @Resource
    private CouponActivityQueryService couponActivityQueryService;

    @Resource
    private PreIssueValidateBiz preIssueValidateBiz;

    @Resource
    private CouponBiz couponBiz;

    @Resource
    private BrandThriftService brandThriftService;

    @Resource
    private UnifiedCouponIssueTrustService unifiedCouponIssueTrustService;

    @Resource
    private IssueCouponService issueCouponService;

    private static final String ISSUE_SOURCE = "DZ_BRAND_COUPON";

    @Resource
    private LogUtils logUtils;

    public static final Logger log = LoggerFactory.getLogger(BrandActivityPromotionProcessor.class);

    @Override
    public void prePare(final CouponActivityContext promoCtx, IMobileContext iMobileContext) {
        try {
            if (!promoCtx.isNeedBrandPromotion()) {
                return;
            }
            DotUtils.dotForPromoProcessor(promoCtx, getPromoName());
            queryBrandActivity(promoCtx);
        } catch (Exception e) {
            log.error("BrandActivityPromotionProcessor# prepare error. ctx: {}", promoCtx, e);
        }
    }

    private List<BrandActivityDTO> queryBrandActivities(long brandId, List<Integer> activcityIds, boolean isMt) {
        BrandActivityQueryRequest request = new BrandActivityQueryRequest();
        request.setBrandId(brandId);
        request.setBrandActivityIds(activcityIds);
        request.setScene(BActivityQuerySceneEnum.BRAND_DETAIL_PAGE.getValue());
        request.setSource(isMt ? QuerySourceEnum.MT.getValue() : QuerySourceEnum.DP.getValue());
        ResultResponse<List<BrandActivityDTO>> resultResponse = couponActivityQueryService.batchQueryOnlineBrandActivities(request);
        logUtils.logInfo("BrandActivityPromotionProcessor# call batchQueryOnlineBrandActivities. request: {}, response: {}", JsonUtils.toJson(request), JsonUtils.toJson(resultResponse));
        if (resultResponse == null || !resultResponse.isSuccess()) {
            log.warn("BrandActivityPromotionProcessor# call batchQueryOnlineBrandActivities error. request: {}, response: {}", JsonUtils.toJson(request), JsonUtils.toJson(resultResponse));
            return null;
        }
        return resultResponse.getResult();
    }

    private Map<Integer, List<UnifiedCouponDTO>> getUserIssuedCoupon(List<BrandActivityDTO> brandActivityDTOS, CouponActivityContext promoCtx) {
        if (CollectionUtils.isEmpty(brandActivityDTOS)) {
            return Maps.newHashMap();
        }
        List<Integer> couponGroupIdList = Lists.newArrayListWithExpectedSize(brandActivityDTOS.size());
        for (BrandActivityDTO brandActivityDTO : brandActivityDTOS) {
            if (brandActivityDTO == null) {
                continue;
            }
            if (brandActivityDTO.getCouponGroupDTO() != null) {
                couponGroupIdList.add(brandActivityDTO.getCouponGroupDTO().getCouponGroupId());
            }
        }
        // 未登录的情况下，就默认无领取记录
        long userId;
        if (promoCtx.isMt()) {
            userId = promoCtx.getMtUserId();
        } else {
            userId = promoCtx.getDpUserId();
        }
        if (userId <= 0 || CollectionUtils.isEmpty(couponGroupIdList)) {
            return Maps.newHashMap();
        }
        try {
            List<UnifiedCouponDTO> unifiedCouponDTOS = couponBiz.batchQueryIssuedCouponList(userId, couponGroupIdList, !promoCtx.isMt());
            logUtils.logInfo("BrandActivityPromotionProcessor# call batchQueryIssuedCouponList. userId: {}, couponGroupIdList: {}, unifiedCouponDtos: {}", userId, couponGroupIdList, unifiedCouponDTOS);
            if (CollectionUtils.isEmpty(unifiedCouponDTOS)) {
                return Maps.newHashMap();
            }
            Map<Integer, List<UnifiedCouponDTO>> result = Maps.newHashMapWithExpectedSize(couponGroupIdList.size());
            for (UnifiedCouponDTO unifiedCouponDTO : unifiedCouponDTOS) {
                if (unifiedCouponDTO == null || unifiedCouponDTO.getCouponGroupDTO() == null) {
                    continue;
                }
                int couponGroupId = unifiedCouponDTO.getCouponGroupDTO().getCouponGroupId();
                if (!result.containsKey(couponGroupId)) {
                    result.put(couponGroupId, Lists.newArrayList());
                }
                result.get(couponGroupId).add(unifiedCouponDTO);
            }
            return result;
        } catch (Exception e) {
            log.error("BrandActivityPromotionProcessor# call batchQueryIssuedCouponList error. userId: {}, isMt: {}, couponGroupIds: {}", userId, promoCtx.isMt(), couponGroupIdList, e);
            return Maps.newHashMap();
        }
    }

    private Map<Long, BrandInfoDTO> batchQueryBrandInfo(List<BrandActivityDTO> brandActivityDTOS) {
        if (CollectionUtils.isEmpty(brandActivityDTOS)) {
            return Maps.newHashMap();
        }
        List<Long> brandIdList = Lists.newArrayListWithExpectedSize(brandActivityDTOS.size());
        for (BrandActivityDTO brandActivityDTO : brandActivityDTOS) {
            if (brandActivityDTO == null) {
                continue;
            }
            if (brandActivityDTO.getBrandID() > 0) {
                brandIdList.add(brandActivityDTO.getBrandID());
            }
        }
        try {
            BatchQryBrandInfoByBrandIdReqDTO reqDTO = new BatchQryBrandInfoByBrandIdReqDTO();
            reqDTO.setBrandIdList(brandIdList);
            BatchQryBrandInfoByBrandIdRespDTO respDTO = brandThriftService.batchQryBrandInfoByBrandId(reqDTO);
            logUtils.logInfo("BrandActivityPromotionProcessor# call batchQryBrandInfoByBrandId. req: {}, resp: {}", reqDTO, respDTO);
            if (respDTO == null || respDTO.getCommonRespDTO() == null || respDTO.getCommonRespDTO().getCode() != 200) {
                log.warn("BrandActivityPromotionProcessor# call batchQryBrandInfoByBrandId failed. req: {}, resp: {}", reqDTO, respDTO);
                return Maps.newHashMap();
            }
            return respDTO.getBrandInfoDTOMap();
        } catch (Exception e) {
            log.warn("BrandActivityPromotionProcessor# call batchQueryBrandInfo error. req: {}", brandIdList, e);
        }
        return Maps.newHashMap();

    }

    private void queryBrandActivity(CouponActivityContext promoCtx) {
        Cat.logEvent("INVALID_METHOD_1", "com.dianping.pay.api.biz.activity.newloader.promo.BrandActivityPromotionProcessor.queryBrandActivity(com.dianping.pay.api.biz.activity.newloader.dto.CouponActivityContext)");
        try {
            CompletableFuture<List<BrandActivityDTO>> brandActivityDtoFutureFuture = CompletableFuture.supplyAsync(() -> queryBrandActivities(promoCtx.getBrandId(), null, promoCtx.isMt()), ExecutorService);
            CompletableFuture<Map<Integer, List<UnifiedCouponDTO>>> userIssuedCouponFuture = brandActivityDtoFutureFuture.thenApplyAsync(brandActivityDTOS -> getUserIssuedCoupon(brandActivityDTOS, promoCtx), ExecutorService);
            CompletableFuture<Map<Long, BrandInfoDTO>> brandInfoFuture = brandActivityDtoFutureFuture.thenApplyAsync(this::batchQueryBrandInfo, ExecutorService);

            promoCtx.setBrandActivityFuture(brandActivityDtoFutureFuture);
            promoCtx.setUserIssuedCouponFuture(userIssuedCouponFuture);
            promoCtx.setBrandInfoFuture(brandInfoFuture);
        } catch (Exception e) {
            log.error("queryBrandActivity exception!", e);
        }
    }

    @Override
    public void loadPromo(CouponActivityContext promoCtx) {
        try {
            Future<List<BrandActivityDTO>> brandActivityFuture = promoCtx.getBrandActivityFuture();
            if (brandActivityFuture != null) {
                promoCtx.setBrandActivityDTOS(brandActivityFuture.get(500, TimeUnit.MILLISECONDS));
            }

            Future<Map<Integer, List<UnifiedCouponDTO>>> userIssuedCouponFuture = promoCtx.getUserIssuedCouponFuture();
            if (brandActivityFuture != null) {
                promoCtx.setUserIssuedCoupon(userIssuedCouponFuture.get(500, TimeUnit.MILLISECONDS));
            }

            Future<Map<Long, BrandInfoDTO>> brandInfoFuture = promoCtx.getBrandInfoFuture();
            if (brandActivityFuture != null) {
                promoCtx.setBrandInfo(brandInfoFuture.get(500, TimeUnit.MILLISECONDS));
            }
        } catch (Exception e) {
            log.error("BrandActivityPromotionProcessor# loadPromo error. ctx: {}", promoCtx, e);
        }
    }

    @Override
    public String getPromoName() {
        Cat.logEvent("INVALID_METHOD_4", "com.dianping.pay.api.biz.activity.newloader.promo.BrandActivityPromotionProcessor.getPromoName()");
        return "brandActivity";
    }

    private String getApp(Integer clienttype) {
        Cat.logEvent("INVALID_METHOD_5", "com.dianping.pay.api.biz.activity.newloader.promo.BrandActivityPromotionProcessor.getApp(java.lang.Integer)");
        try {
            if (Integer.valueOf(CouponClientTypeEnum.NATIVE.getCode()).equals(clienttype)) {
                return  "app";
            }
            if (Integer.valueOf(CouponClientTypeEnum.MINIPROGRAM.getCode()).equals(clienttype)) {
                return  "miniprogram";
            }
            return "mobile";
        } catch (Exception ignore) {
            return "app";
        }
    }

    private String getSourceAppkey() {
        Cat.logEvent("INVALID_METHOD_4", "com.dianping.pay.api.biz.activity.newloader.promo.BrandActivityPromotionProcessor.getSourceAppkey()");
        String sourceAppkey = AppkeyUtils.getSourceAppkey();
        if (StringUtils.isBlank(sourceAppkey)) {
            sourceAppkey = "mapi-pay-promo-web";
        }
        return sourceAppkey;
    }

    // 发券逻辑写法
    public UnifiedIssueDetailResult doIssueCoupon(BrandActivityIssueCouponRequest issueCouponRequest, IMobileContext context) {
        Cat.logEvent("INVALID_METHOD_5", "com.dianping.pay.api.biz.activity.newloader.promo.BrandActivityPromotionProcessor.doIssueCoupon(BrandActivityIssueCouponRequest,IMobileContext)");
        try {
            // 1 鉴权 根据brandId + brandActivityId 去查询对应的券批次id
            BrandActivityDTO brandActivityDTO = getRelatedBrandActivity(issueCouponRequest);
            if (brandActivityDTO == null || brandActivityDTO.getCouponGroupDTO() == null) {
                throw new IssueCouponException("没有对应的券活动", GET_COUPON_FAIL);
            }
            // 2 根据券批次id调用券侧接口进行发券，需要过风控
            CouponIssueRequest couponIssueRequest = new CouponIssueRequest();
            CouponIssueInfo couponIssueInfo = new CouponIssueInfo();
            couponIssueInfo.setCouponGroupId(String.valueOf(brandActivityDTO.getCouponGroupDTO().getCouponGroupId()));
            List<CouponIssueInfo> couponIssueInfoList = Lists.newArrayList(couponIssueInfo);
            couponIssueRequest.setCouponGroupInfo(couponIssueInfoList);
            couponIssueRequest.setIssueSource(ISSUE_SOURCE);
            couponIssueRequest.setIssueType(1);
            couponIssueRequest.setOperator(getSourceAppkey());
            couponIssueRequest.setUserId(issueCouponRequest.getUserId());
            String userType = issueCouponRequest.getUsercode() == User.MT.getCode() ? User.MT.getFlag() : User.DP.getFlag();
            couponIssueRequest.setUserType(userType);

            Map<String, String> creditContext = Maps.newHashMap();
            creditContext.put("cx", issueCouponRequest.getCx());
            creditContext.put("requesturl", context.getRequest().getRequestURI());
            creditContext.put("envArray", "[app,mobile,miniprogram]");
            creditContext.put("ip", context.getUserIp());
            creditContext.put("env", getApp(issueCouponRequest.getClienttype()));
            creditContext.put("userid", String.valueOf(issueCouponRequest.getUserId()));
            String couponFrom = issueCouponRequest.getUsercode() == User.MT.getCode() ? "mt" : "dp";
            creditContext.put("couponFrom", couponFrom);
            creditContext.put("event", "[" + AppkeyUtils.getSourceAppkey() + "][" + issueCouponRequest.getBActivityId() + couponFrom + "]");
            couponIssueRequest.setCreditContext(creditContext);
            UnifiedCouponIssueOption issueOption = new UnifiedCouponIssueOption();
            boolean needCredit = Lion.getBooleanValue(LionConstants.BRNAD_COUPON_ISSUE_NEED_CREDIT, false);
            if (needCredit) {
                issueOption.setCreditType(0);
            } else {
                issueOption.setCreditType(-1);
            }

            UnifiedCouponIssueResponse response = issueCouponService.issueCouponsToUser(couponIssueRequest, issueOption);
            if (response == null || !response.isSuccess() || response.getResultCode() != CouponOperationResultCode.SUCCESS.code) {
                log.warn("BrandActivityPromotionProcessor# call issueCouponsToUser failed. request: {}, response: {}", couponIssueRequest, response);
                throw new IssueCouponException("领取失败", GET_COUPON_FAIL);
            }

            List<UnifiedCouponIssueDetail> result = response.getResult().getResult();
            UnifiedCouponIssueDetail issueDetail = result.get(0);

            UnifiedIssueDetailResult resp = new UnifiedIssueDetailResult();
            resp.setSuccess(true);
            resp.setResultMessage("领取成功");
            resp.setBeginTime(issueDetail.getBeginTime());
            resp.setEndTime(issueDetail.getEndTime());
            resp.setCouponGroupName(issueDetail.getCouponGroupName());
            resp.setUnifiedCouponId(issueDetail.getUnifiedCouponId());
            return resp;
        } catch (IssueCouponException e) {
            Cat.logEvent("IssueCoupons", "BrandCoupon", "-1", String.valueOf(issueCouponRequest.getBActivityId()));
            log.error("BrandActivityPromotionProcessor# doIssueCoupon error. request: {}", issueCouponRequest, e);
            UnifiedIssueDetailResult resp = new UnifiedIssueDetailResult();
            resp.setSuccess(false);
            resp.setToastMsg(e.getMessage());
            return resp;
        }
        // 其余错误全部上抛
    }


    private BrandActivityDTO getRelatedBrandActivity(BrandActivityIssueCouponRequest issueCouponRequest) {
        Cat.logEvent("INVALID_METHOD_4", "com.dianping.pay.api.biz.activity.newloader.promo.BrandActivityPromotionProcessor.getRelatedBrandActivity(com.dianping.pay.api.entity.issuecoupon.BrandActivityIssueCouponRequest)");
        try {
            List<BrandActivityDTO> brandActivityDTOS = queryBrandActivities(issueCouponRequest.getBrandId(), Lists.newArrayList(issueCouponRequest.getBActivityId()), issueCouponRequest.getUsercode() == User.MT.getCode());
            if (CollectionUtils.isEmpty(brandActivityDTOS)) {
                return null;
            }
            return brandActivityDTOS.get(0);
        } catch (Exception e) {
            log.error("BrandActivityPromotionProcessor# getRelatedBrandActivity failed. request: {}", issueCouponRequest, e);
            return null;
        }
    }


}
