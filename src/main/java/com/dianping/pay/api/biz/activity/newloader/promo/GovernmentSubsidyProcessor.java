package com.dianping.pay.api.biz.activity.newloader.promo;

import com.dianping.cat.Cat;
import com.dianping.mobile.framework.datatypes.IMobileContext;
import com.dianping.pay.api.biz.activity.newloader.dto.CouponActivityContext;
import com.dianping.pay.api.entity.issuecoupon.GovernmentSubsidyInfo;
import com.dianping.pay.api.service.ZdcShopIdentityService;
import com.sankuai.meituan.lion.client.Lion;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * 国补氛围处理器
 * <AUTHOR>
 */
@Component
@Slf4j
public class GovernmentSubsidyProcessor extends AbstractPromoProcessor {

    @Autowired
    private ZdcShopIdentityService zdcShopIdentityService;

    private static final String GOVERNMENT_SUBSIDY_SWITCH = "mapi-pay-promo-web.government.subsidy.switch";

    @Override
    public String getPromoName() {
        return "GovernmentSubsidyProcessor";
    }

    @Override
    public CouponActivityContext process(CouponActivityContext promoCtx, IMobileContext iMobileContext) {
        try {
            // 检查开关
            if (!Lion.getBooleanValue(GOVERNMENT_SUBSIDY_SWITCH, false)) {
                log.debug("国补氛围开关未开启");
                return promoCtx;
            }

            // 检查必要参数
            if (promoCtx.getShopIdL() == null || promoCtx.getShopIdL() <= 0) {
                log.debug("shopId无效，跳过国补氛围处理");
                return promoCtx;
            }

            // 查询国补信息
            GovernmentSubsidyInfo subsidyInfo = zdcShopIdentityService.getGovernmentSubsidyInfo(
                promoCtx.getShopIdL(), 
                promoCtx.getUserId(), 
                promoCtx.isMt()
            );

            if (subsidyInfo != null && subsidyInfo.isShowGovernmentSubsidy()) {
                promoCtx.setGovernmentSubsidyInfo(subsidyInfo);
                Cat.logEvent("GovernmentSubsidy", "Show");
                log.info("成功获取国补氛围信息, shopId: {}, userId: {}", 
                    promoCtx.getShopIdL(), promoCtx.getUserId());
            } else {
                log.debug("门店无国补标识或用户无国补资格, shopId: {}", promoCtx.getShopIdL());
            }

        } catch (Exception e) {
            log.error("处理国补氛围失败, shopId: {}, userId: {}", 
                promoCtx.getShopIdL(), promoCtx.getUserId(), e);
            Cat.logError("GovernmentSubsidyProcessor.process", e);
        }

        return promoCtx;
    }
}
