package com.dianping.pay.api.biz.discount;

import com.dianping.cat.Cat;
import com.dianping.combiz.spring.util.PropertiesLoaderSupportUtils;
import com.dianping.pay.api.beans.GetPromoDeskRequest;
import com.dianping.pay.api.beans.PromoDTOGroup;
import com.dianping.pay.api.beans.PromoProduct;
import com.dianping.pay.api.entity.promodesk.DiscountPromoEvent;
import com.dianping.pay.api.entity.promodesk.DiscountPromoEventGroup;
import com.dianping.pay.api.entity.promodesk.DiscountPromoTool;
import com.dianping.pay.common.enums.ProductCode;
import com.dianping.pay.promo.common.enums.ProductType;
import com.dianping.pay.promo.common.enums.PromoType;
import com.dianping.pay.promo.execute.service.dto.PromoExecuteDetailDTO;
import com.dianping.pay.promo.execute.service.dto.request.PromoExecuteQueryRequest;
import com.google.common.base.Function;
import com.google.common.collect.Lists;
import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;

import java.lang.reflect.Type;
import java.math.BigDecimal;
import java.util.*;

public class PayPromoQueryMapper {

    private static final Map<Integer, List<Integer>> mutexProductCodesConfig = new HashMap<Integer, List<Integer>>();

    private static Map<Integer, Integer> templateIdMap = new HashMap<Integer, Integer>();
    static {
        Gson gson = new Gson();
        Type typeOfMap = new TypeToken<HashMap<Integer, Integer>>(){}.getType();
        templateIdMap = gson.fromJson(PropertiesLoaderSupportUtils.getProperty("mapi-pay-promo-web.queryAvailable.templateIdMap"), typeOfMap);
        mutexProductCodesConfig.put(ProductCode.MO2O2PAY.getCode(), Lists.newArrayList(ProductCode.TUANGOU.getCode()));
    }

    public static List<PromoExecuteQueryRequest> buildRequest(GetPromoDeskRequest request, Collection<PromoProduct> promoProductList) {
        Cat.logEvent("INVALID_METHOD_1", "com.dianping.pay.api.biz.discount.PayPromoQueryMapper.buildRequest(com.dianping.pay.api.beans.GetPromoDeskRequest,java.util.Collection)");
        List<PromoExecuteQueryRequest> resultList = new ArrayList<PromoExecuteQueryRequest>();
        for (PromoProduct product : promoProductList) {
            ProductType productType = ProductType.fromProductCode(product.getProductCode(), request.isMtClient());
            if (productType == null) {
                continue;
            }
            PromoExecuteQueryRequest promoQueryRequest = new PromoExecuteQueryRequest();
            promoQueryRequest.setUserId(request.getUserId());
            promoQueryRequest.setMobileNo(request.getMobileNo());
            promoQueryRequest.setDpId(request.getClientInfo().getDpId());
            promoQueryRequest.setProductId(product.getProductId());
            promoQueryRequest.setProductType(productType.value);
            promoQueryRequest.setTemplateId(MapUtils.getIntValue(templateIdMap, product.getProductCode(), 0));
            promoQueryRequest.setClientVersion(request.getClientInfo().getVersion().getVersion());
            promoQueryRequest.setCityId(request.getCityId());
            promoQueryRequest.setCx(request.getClientInfo().getCx());
            promoQueryRequest.setIp(request.getClientInfo().getIp());
            promoQueryRequest.setPlatform(request.getClientInfo().getPlatform());
            promoQueryRequest.setQuantity(product.getQuantity());
            promoQueryRequest.setPrice(product.getPrice());
            resultList.add(promoQueryRequest);
        }
        return resultList;
    }

    public static int toProductCode(int productType) {
        Cat.logEvent("INVALID_METHOD_5", "com.dianping.pay.api.biz.discount.PayPromoQueryMapper.toProductCode(int)");
        if (productType == ProductType.tuangou.value || productType == ProductType.mt_tuangou.value) {
            return ProductCode.TUANGOU.getCode();
        } else if (productType == ProductType.movie.value) {
            return ProductCode.MOVIE.getCode();
        } else if (productType == ProductType.takeaway.value) {
            return ProductCode.TAKEAWAY.getCode();
        } else if (productType == ProductType.shanhui.value || productType == ProductType.mt_shanhui.value) {
            return ProductCode.MO2O2PAY.getCode();
        } else if (productType == ProductType.toHome.value) {
            return ProductCode.TOHOME.getCode();
        } else if (productType == ProductType.ktv.value || productType == ProductType.mt_ktv.value) {
            return ProductCode.KTV.getCode();
        } else if (productType == ProductType.joy.value || productType == ProductType.mt_joy.value) {
            return ProductCode.JOY.getCode();
        } else {
            throw new IllegalArgumentException("unsupported promo productType");
        }
    }

    public static List<Integer> toMutexProductCodes(int productCode) {
        Cat.logEvent("INVALID_METHOD_3", "com.dianping.pay.api.biz.discount.PayPromoQueryMapper.toMutexProductCodes(int)");
        return (List<Integer>) MapUtils.getObject(mutexProductCodesConfig, productCode, Collections.emptyList());
    }


    public static DiscountPromoTool toDiscountPromoTool(Collection<PromoDTOGroup> promoDTOGroupList) {
        Cat.logEvent("INVALID_METHOD_5", "com.dianping.pay.api.biz.discount.PayPromoQueryMapper.toDiscountPromoTool(java.util.Collection)");
        DiscountPromoTool discountPromoTool = new DiscountPromoTool();
        BigDecimal maxPromoValue = BigDecimal.ZERO;
        for (PromoDTOGroup promoDTOGroup : promoDTOGroupList) {
            int promoDetailSize = promoDTOGroup.getPromoDTOList().size();
            if (promoDetailSize == 0) {
                continue;
            }

            DiscountPromoEventGroup discountPromoEventGroup = new DiscountPromoEventGroup();
            discountPromoEventGroup.setDiscountPromoEvents(new ArrayList<DiscountPromoEvent>());
            discountPromoEventGroup.setIcon(PropertiesLoaderSupportUtils.getProperty("pay-api-mobile.promodesk.discount.icon"));
            discountPromoEventGroup.setMutexTools(promoDTOGroup.getMutexTools());
            discountPromoEventGroup.setMutexProductCodes(promoDTOGroup.getMutexProductCodes());
            discountPromoEventGroup.setProductCode(promoDTOGroup.getProductCode());

            DiscountPromoEvent maxDiscountPromoEvent = null;
            for (PromoExecuteDetailDTO promoExecuteDetailDTO : promoDTOGroup.getPromoDTOList()) {
                if (StringUtils.isBlank(promoExecuteDetailDTO.getPromoTextDetailDTO().getDisplayTitle())) {
                    continue;
                }
                if(!promoExecuteDetailDTO.isAvailable()
                        || promoExecuteDetailDTO.getPromoType() != PromoType.reduce.value){//只返回可享优惠
                    continue;
                }
                DiscountPromoEvent discountPromoEvent = new DiscountPromoEvent(discountPromoEventGroup);
                discountPromoEvent.setId(promoExecuteDetailDTO.getPromoId());
                discountPromoEvent.setTitle(promoExecuteDetailDTO.getPromoTextDetailDTO().getDisplayTitle());
                discountPromoEvent.setDesc(promoExecuteDetailDTO.getPromoTextDetailDTO().getDisplayDesc());
                discountPromoEvent.setCanUse(promoExecuteDetailDTO.isAvailable());
                discountPromoEvent.setOrderPriceLimit(getOrderPriceLimit(promoExecuteDetailDTO));
                discountPromoEvent.setPromoAmount(promoExecuteDetailDTO.getTotalPromoAmount().doubleValue());
                discountPromoEvent.setPromoCipher(promoExecuteDetailDTO.getPromoCipher());
                discountPromoEventGroup.getDiscountPromoEvents().add(discountPromoEvent);
                if(maxDiscountPromoEvent == null || maxDiscountPromoEvent.getPromoAmount() < discountPromoEvent.getPromoAmount()){
                    maxDiscountPromoEvent = discountPromoEvent;
                }
            }
            if (maxDiscountPromoEvent == null || CollectionUtils.isEmpty(discountPromoEventGroup.getDiscountPromoEvents())) {
                continue;
            }
            //团购返回最高优惠
            if(promoDTOGroup.getProductCode() == ProductCode.TUANGOU.getCode()){
                discountPromoEventGroup.getDiscountPromoEvents().clear();
                discountPromoEventGroup.getDiscountPromoEvents().add(maxDiscountPromoEvent);
                if(maxPromoValue.doubleValue() < maxDiscountPromoEvent.getPromoAmount()){
                    maxPromoValue = new BigDecimal(maxDiscountPromoEvent.getPromoAmount());
                    discountPromoTool.getDiscountPromoEventGroups().clear();
                    discountPromoTool.getDiscountPromoEventGroups().add(discountPromoEventGroup);
                }
                continue;
            }
            discountPromoTool.getDiscountPromoEventGroups().add(discountPromoEventGroup);
        }
        return discountPromoTool;
    }

    private static double getOrderPriceLimit(PromoExecuteDetailDTO promoExecuteDTO) {
        Cat.logEvent("INVALID_METHOD_3", "com.dianping.pay.api.biz.discount.PayPromoQueryMapper.getOrderPriceLimit(com.dianping.pay.promo.execute.service.dto.PromoExecuteDetailDTO)");
        if (promoExecuteDTO.getPromoRuleDetailDTO().getProductPrice() != null) {
            return promoExecuteDTO.getPromoRuleDetailDTO().getProductPrice().doubleValue();
        }
        if (promoExecuteDTO.getPromoRuleDetailDTO().getOrderPromoLimit() != null) {
            return promoExecuteDTO.getPromoRuleDetailDTO().getOrderPromoLimit().doubleValue();
        }
        return 0;
    }
}
