package com.dianping.pay.api.biz.activity.newloader.promo;


import com.dianping.api.common.enums.PageSourceEnum;
import com.dianping.cat.Cat;
import com.dianping.deal.base.dto.DealGroupBaseDTO;
import com.dianping.lion.client.Lion;
import com.dianping.mobile.framework.datatypes.IMobileContext;
import com.dianping.pay.api.beans.IssueCouponActivity;
import com.dianping.pay.api.beans.ProductDetailDO;
import com.dianping.pay.api.beans.ProductItemDetailDO;
import com.dianping.pay.api.biz.activity.newloader.dto.CouponActivityContext;
import com.dianping.pay.api.constants.ProxyConstant;
import com.dianping.pay.api.enums.QueryShopCouponTypeEnum;
import com.dianping.pay.api.service.impl.ProductService;
import com.dianping.pay.api.util.*;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.sankuai.nib.mkt.common.base.enums.RulePropertyTypeEnum;
import com.sankuai.nib.mkt.common.base.enums.TrafficFlagEnum;
import com.sankuai.nib.mkt.common.base.enums.daozong.DzPriceSceneEnum;
import com.sankuai.nib.mkt.common.base.enums.daozong.GoodsInfoEnum;
import com.sankuai.nib.mkt.common.base.enums.propertyValueType.MeiDianSourceEnum;
import com.sankuai.nibmktproxy.queryclient.proxy.*;
import java.util.HashMap;
import java.util.Map;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.Iterator;
import java.util.List;
import java.util.concurrent.Callable;
import java.util.concurrent.Future;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

@Component("proxyCouponProcessor")
@Slf4j
public class ProxyCouponProcessor extends AbstractPromoProcessor {


    private static final int OLD_SCENE_ID = 3017;

    private static final int SCENE_ID = 3405;
    //全部立减
    private static final int PROMO_TEMP_ID = 239;

    private static final String BIZ_CODE = "nib.general.groupbuy";
    // 目前商品只有医美预付bizCode
    private static final String PRAPAY_BIZCODE = "nib.general.medical_prepay";

    @Resource
    private ProductService productService;

    @Autowired
    private PromotionProxyService.Iface promotionProxyService;

    @Autowired
    private LogUtils logUtils;


    private int getSceneIdInGray(long userId) {
        String newProxyGrayUserIdPattern = Lion.getStringValue(LionConstants.NEW_PROXY_SCENEID_GRAYUSERID_PATTERN, ".*");
        if (String.valueOf(userId).matches(newProxyGrayUserIdPattern)) {
            return SCENE_ID;
        }
        return OLD_SCENE_ID;
    }

    @Override
    public void prePare(final CouponActivityContext promoCtx, final IMobileContext iMobileContext) {
        try {
            if (promoCtx.getQueryShopCouponType() != QueryShopCouponTypeEnum.BY_DEAL.getCode() && promoCtx.getQueryShopCouponType() != QueryShopCouponTypeEnum.BY_SKU.getCode()) {
                return;
            }
            DotUtils.dotForPromoProcessor(promoCtx, getPromoName());
            Future<PromotionDTOResult> future = ExecutorService.submit(new Callable<PromotionDTOResult>() {
                @Override
                public PromotionDTOResult call() throws Exception {
                    return getProxyPromotions(promoCtx, iMobileContext);
                }
            });
            promoCtx.setProxyPromotionFuture(future);
        } catch (Exception e) {
            log.error("ProxyCouponProcessor# prepare error. ctx: {}", promoCtx, e);
        }
    }

    @Override
    public void loadPromo(CouponActivityContext promoCtx) {
        try {
            Future<PromotionDTOResult> proxyPromotionFuture = promoCtx.getProxyPromotionFuture();
            int timeout = Lion.getIntValue(LionConstants.PROXY_PROMOTION_TIMEOUT, 500);
            if (proxyPromotionFuture != null) {
                promoCtx.setPromotionDTOResult(proxyPromotionFuture.get(timeout, TimeUnit.MILLISECONDS));
            }
        } catch (Exception e) {
            log.error("ProxyCouponProcessor# loadPromo error. ctx: {}", promoCtx, e);
        }
    }

    @Override
    public String getPromoName() {
        return "proxyCoupon";
    }

    private PromotionDTOResult getProxyPromotions(CouponActivityContext promoCtx, IMobileContext iMobileContext) {
        try {
            PromotionRequest request = buildPromoProxyRequest(promoCtx, iMobileContext);
            if (request == null) {
                return null;
            }
            PromotionResponse response = promotionProxyService.getPromotions(request);
            logUtils.logInfo("ProxyCouponProrcessor# call getPromotions. request: {}, response: {}", JsonUtils.toJson(request), JsonUtils.toJson(response));
            if (response == null) {
                DotUtils.dotForPromoCallFail(getPromoName(), "getPromotions", promoCtx.isMt());
                log.warn("getPromotions failed. request: {}, response: {}", request, response);
                return null;
            }
            if (MapUtils.isEmpty(response.getPromotionMap())) {
                DotUtils.dotForPromoHit(getPromoName(), promoCtx.isMt(), false);
                return null;
            }

            int productId;
            if (promoCtx.getQueryShopCouponType() == QueryShopCouponTypeEnum.BY_DEAL.getCode()) {
                if (promoCtx.isMt()) {
                    productId = promoCtx.getMtDealGroupId();
                } else {
                    productId = promoCtx.getDpDealGroupId();
                }
            } else {
                productId = promoCtx.getSkuId();
            }
            PromotionDTOResult promotionDTOResult = response.getPromotionMap().get(String.valueOf(productId));
            if (promotionDTOResult == null || CollectionUtils.isEmpty(promotionDTOResult.getGetPromotionDTO())) {
                DotUtils.dotForPromoHit(getPromoName(), promoCtx.isMt(), false);
            } else {
                DotUtils.dotForPromoHit(getPromoName(), promoCtx.isMt(), true);
            }

            if (promotionDTOResult == null ||CollectionUtils.isEmpty(promotionDTOResult.getGetPromotionDTO())) {
                return promotionDTOResult;
            }

            Iterator<GetPromotionDTO> iterator = promotionDTOResult.getGetPromotionDTO().iterator();

            try {
                while (iterator.hasNext()) {
                    GetPromotionDTO getPromotionDTO = iterator.next();
                    if (!ProxyPromoInfoHelper.isCouponPromo(getPromotionDTO)) {
                        continue;
                    }
                    CouponDTO couponDTO = getPromotionDTO.getPromotionDTO().getCouponDTO();
                    if (couponDTO == null) {
                        continue;
                    }
                    // 目前只过滤可领券
                    if (!couponDTO.canAssign) {
                        continue;
                    }
                    if (filterActivity(couponDTO.getCouponGroupId(), promoCtx)) {
                        iterator.remove();
                    }
                }
            } catch (Exception e) {
                log.error("filter getPromotion dto failed", e);
            }

            return promotionDTOResult;
        } catch (Exception e) {
            log.error("getProxyPromotions error. ctx: {}", promoCtx, e);
        }
        return null;
    }

    // 请求参数拼接
    private PromotionRequest buildPromoProxyRequest(CouponActivityContext ctx, IMobileContext iMobileContext) {
        PromotionRequest promotionRequest = new PromotionRequest();
        long userId;
        if (ctx.isMt()) {
            userId = ctx.getMtUserId();
        } else {
            userId = ctx.getDpUserId();
        }

        promotionRequest.setSceneId(getSceneIdInGray(userId));
        promotionRequest.setUseCouponType(0);
        if (ctx.getQueryShopCouponType() == QueryShopCouponTypeEnum.BY_DEAL.getCode()) {
            promotionRequest.setBizCode(BIZ_CODE);
        } else {
            // 目前商品类型只有预付业务
            promotionRequest.setBizCode(PRAPAY_BIZCODE);
        }
        promotionRequest.setRequestStrategy(RequestStrategy.NORMAL);

        List<RequestItem> requestItemList = Lists.newArrayList();

        List<Property> properties = Lists.newArrayListWithExpectedSize(3);
        RequestItem requestItem = new RequestItem();

        BigDecimal price = getPrice(ctx);
        long productId = getProductId(ctx);

        if (productId <= 0 || price == null) {
            return null;
        }
        requestItem.setItemId(String.valueOf(productId));
        Property priceProperty = new Property();
        priceProperty.setCode(RulePropertyTypeEnum.price.getCode());
        priceProperty.setValue(price.multiply(new BigDecimal(100)).toString());
        properties.add(priceProperty);

        Property productIdProperty = new Property();
        productIdProperty.setCode(RulePropertyTypeEnum.productId.getCode());
        productIdProperty.setValue(String.valueOf(productId));
        properties.add(productIdProperty);

        Property poiProperty = new Property();
        poiProperty.setCode(RulePropertyTypeEnum.poiId.getCode());

        long shopId;
        if (ctx.isMt()) {
            shopId = ctx.getMtShopIdL();
        } else {
            shopId = ctx.getDpShopIdL();
        }
        poiProperty.setValue(String.valueOf(shopId));
        properties.add(poiProperty);

        BigDecimal commissionRate = null;
        boolean dealSwitch = Lion.getBoolean(LionConstants.APP_KEY, LionConstants.DEAL_COMMISSION_MOVE_SWITCH, true);
        boolean skuSwitch = Lion.getBoolean(LionConstants.APP_KEY, LionConstants.SKU_COMMISSION_MOVE_SWITCH, true);
        if (ctx.getQueryShopCouponType() == QueryShopCouponTypeEnum.BY_DEAL.getCode()) {
            commissionRate = productService.getDealGroupCommissionRate(ctx.getDpDealGroupId(), dealSwitch);
        } else {
            commissionRate = productService.getSkuCommissionRate(ctx.getSkuId(), skuSwitch);
        }
        if (commissionRate != null) {
            Cat.logEvent(ProxyConstant.COMMISSION_CAT, "commission_" + ctx.getQueryShopCouponType());
            Property profitProperty = new Property();
            profitProperty.setCode(RulePropertyTypeEnum.commissionRate.getCode());
            profitProperty.setValue(commissionRate.toString());
            properties.add(profitProperty);
        } else {
            Cat.logEvent(ProxyConstant.COMMISSION_CAT, "commission_" + ctx.getQueryShopCouponType(), "1", "");
        }

        if(ctx.getDealDetailDO() != null) {
            Map<String, String> goodsInfoMap = new HashMap<>(4);
            goodsInfoMap.put(GoodsInfoEnum.DEAL_TYPE.name(), ctx.getDealDetailDO().getDealCategoryId());
            goodsInfoMap.put(GoodsInfoEnum.GROUP_ORDER_CAN_USE_THE_START_TIME.name(), ctx.getDealDetailDO().getStartTimestamp());
            goodsInfoMap.put(GoodsInfoEnum.GROUP_ORDER_CAN_USE_THE_END_TIME.name(), ctx.getDealDetailDO().getEndTimestamp());
            goodsInfoMap.put(GoodsInfoEnum.THIRD_PARTY_CODE.name(), ctx.getDealDetailDO().getThirdPartyCode());
            Property goodsInfoProperty = new Property();
            goodsInfoProperty.setCode(RulePropertyTypeEnum.goodsInfo.getCode());
            goodsInfoProperty.setValue(JsonUtils.toJson(goodsInfoMap));
            properties.add(goodsInfoProperty);
        }

        requestItem.setProperties(properties);
        requestItemList.add(requestItem);
        promotionRequest.setRequestItemList(requestItemList);

        UserInfo userInfo = new UserInfo();
        userInfo.setUserId(userId);
        userInfo.setDeviceId(ctx.getDpid());
        promotionRequest.setUserInfo(userInfo);
        List<Property> commonProperties = buildCommonInfo(ctx, iMobileContext);
        //券查询策略
        Property retAllProperty = new Property();
        retAllProperty.setCode(RulePropertyTypeEnum.retAll.getCode());
        retAllProperty.setValue("true");
        commonProperties.add(retAllProperty);
        if (PageSourceEnum.BeautyMedicalProductDetail.code.equals(ctx.getCouponPageSource())) {
            Property priceSceneProperty = new Property();
            priceSceneProperty.setCode(RulePropertyTypeEnum.priceScene.getCode());
            priceSceneProperty.setValue("6");
            commonProperties.add(priceSceneProperty);
        }
        promotionRequest.setCommonProperties(commonProperties);

        return promotionRequest;
    }

    private List<Property> buildCommonInfo(CouponActivityContext ctx, IMobileContext iMobileContext) {
        List<Property> commonProperties = Lists.newArrayList();

        //增加经纬度
        if(ctx.getLongitude() != null && ctx.getLatitude() != null) {
            Property latitudeProperty = new Property();
            latitudeProperty.setCode(RulePropertyTypeEnum.latitude.getCode());
            latitudeProperty.setValue(ctx.getLatitude().toString());
            Property longitudeProperty = new Property();
            longitudeProperty.setCode(RulePropertyTypeEnum.longitude.getCode());
            longitudeProperty.setValue(ctx.getLongitude().toString());
            commonProperties.add(longitudeProperty);
            commonProperties.add(latitudeProperty);
        }

        // 这是个新字段，先加个开关，避免出现异常情况，观察稳定后可以去除
        if (Lion.getBoolean(LionConstants.APP_KEY, LionConstants.PROXY_VERSION_PARAM_SWITCH, true)) {
            if (StringUtils.isNotBlank(iMobileContext.getVersion())) {
                Property versionProperty = new Property();
                versionProperty.setCode(RulePropertyTypeEnum.version.getCode());
                versionProperty.setValue(iMobileContext.getVersion());
                commonProperties.add(versionProperty);
            }
        }

        Property templateIdProperty = new Property();
        templateIdProperty.setCode(RulePropertyTypeEnum.templateId.getCode());
        templateIdProperty.setValue(String.valueOf(PROMO_TEMP_ID));
        commonProperties.add(templateIdProperty);

        //城市id
        Property cityIdProperty = new Property();
        cityIdProperty.setCode(RulePropertyTypeEnum.browseMtCityId.getCode());
        //TODO 待确认是否是美团是美团，点评是点评
        cityIdProperty.setValue(String.valueOf(ctx.getCityId()));
        commonProperties.add(cityIdProperty);

        Property clientTpProperty = new Property();
        clientTpProperty.setCode(RulePropertyTypeEnum.clientTp.getCode());
        clientTpProperty.setValue(ClientTypeUtils.getClientTypeByPayPlatform(ctx.getCouponPlatform(), ctx.isMt()));
        commonProperties.add(clientTpProperty);

        //点评or美团
        Property meiDianSource = new Property();
        meiDianSource.setCode(RulePropertyTypeEnum.MeiDianSource.getCode());
        if (ctx.isMt()) {
            meiDianSource.setValue(MeiDianSourceEnum.MT.getValue());
        } else {
            meiDianSource.setValue(MeiDianSourceEnum.DP.getValue());
        }
        commonProperties.add(meiDianSource);
        //ReturnControl
        Property returnControlProperty = new Property();
        returnControlProperty.setCode(RulePropertyTypeEnum.returnComposition.getCode());
        returnControlProperty.setValue("true");
        commonProperties.add(returnControlProperty);
        //流量来源，目前仅支持点评百度小程序
        if(StringUtils.isNotEmpty(ctx.getTrafficSource())
            && ctx.getTrafficSource().equals("baiduMapApp")) {
            Property trafficSourceProperty = new Property();
            trafficSourceProperty.setCode(RulePropertyTypeEnum.trafficFlag.getCode());
            if(ctx.isMt()) {
                trafficSourceProperty.setValue(TrafficFlagEnum.MT_BAIDU.getCode());
            } else {
                trafficSourceProperty.setValue(TrafficFlagEnum.DP_BAIDU.getCode());
            }
            commonProperties.add(trafficSourceProperty);
        }
        return commonProperties;
    }

    private long getProductId(CouponActivityContext ctx) {
        long productId = 0L;
        if (ctx.getQueryShopCouponType() == QueryShopCouponTypeEnum.BY_DEAL.getCode()) {
            DealGroupBaseDTO dealGroup = ctx.getDealGroupBaseDTO();
            if (dealGroup != null) {
                if (ctx.isMt()) {
                    productId = ctx.getMtDealGroupId();
                } else {
                    productId = ctx.getDpDealGroupId();
                }
            }
        } else {
            ProductDetailDO productDetailDO = ctx.getProductDetailDO();
            if (productDetailDO != null) {
                productId = ctx.getSkuId();
            }
        }
        // 除此之外都是异常情况
        return productId;
    }

    private BigDecimal getPrice(CouponActivityContext ctx) {
        BigDecimal price = null;
        if (ctx.getQueryShopCouponType() == QueryShopCouponTypeEnum.BY_DEAL.getCode()) {
            DealGroupBaseDTO dealGroup = ctx.getDealGroupBaseDTO();
            if (dealGroup != null) {
                price = dealGroup.getDealGroupPrice();
            }
        } else {
            if (ctx.getQueryShopCouponType() == QueryShopCouponTypeEnum.BY_SKU.getCode() &&
                    !Lion.getBooleanValue(LionConstants.SKU_SKIP_PRICE_VALIDATE_SWITCH, true)) {
                return BigDecimal.valueOf(Integer.MAX_VALUE);
            }

            ProductDetailDO productDetailDO = ctx.getProductDetailDO();
            if (productDetailDO != null) {
                List<ProductItemDetailDO> itemDetailDOS = productDetailDO.getProductItems();
                if (CollectionUtils.isNotEmpty(itemDetailDOS)) {
                    for (ProductItemDetailDO productItemDetailDO : itemDetailDOS) {
                        if (productItemDetailDO.getId() == ctx.getSkuId()) {
                            price = productItemDetailDO.getPrice();
                            break;
                        }
                    }
                    if (price == null) {
                        price = itemDetailDOS.get(0).getPrice();
                    }
                }
            }
        }
        return price;
    }
}
