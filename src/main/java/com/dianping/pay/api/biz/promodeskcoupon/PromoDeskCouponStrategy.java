package com.dianping.pay.api.biz.promodeskcoupon;

import com.dianping.pay.api.beans.GetPromoDeskCouponRequest;
import com.dianping.pay.api.beans.GetPromoDeskRequest;
import com.dianping.pay.api.beans.PromoProduct;
import com.dianping.pay.api.entity.promodesk.PromoDeskCoupon;
import com.dianping.unified.coupon.manage.api.dto.UnifiedCouponDTO;

import java.util.Set;

public interface PromoDeskCouponStrategy {

    /**
     * @return 是否已经取完所有结果
     */
    boolean assembleCouponSet(Set<PromoDeskCoupon> resultSet,
                                      Set<PromoDeskCoupon> unavailableSet,
                                      GetPromoDeskCouponRequest request,
                                      PromoProduct promoProduct,
                                      int start,
                                      int limit);

    /**
     * @return 是否已经取完所有结果
     */
    boolean assembleShopCouponSet(Set<PromoDeskCoupon> resultSet,
                                       Set<PromoDeskCoupon> unavailableSet,
                                       GetPromoDeskCouponRequest request,
                                       PromoProduct promoProduct);

    /**
     * @return 是否是这家店/商品的抵用券
     */
    boolean isCouponOfShop(UnifiedCouponDTO couponDTO, GetPromoDeskRequest request);
}
