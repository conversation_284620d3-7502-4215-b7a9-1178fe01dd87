package com.dianping.pay.api.biz.activity.newloader.dto;

import com.dianping.deal.base.dto.DealGroupBaseDTO;
import com.dianping.pay.api.beans.*;
import com.dianping.pay.api.entity.issuecoupon.GovernmentSubsidyInfo;
import com.dianping.pay.api.entity.issuecoupon.ReturnPromotionDisplayDto;
import com.dianping.pay.promo.common.enums.User;
import com.dianping.pay.promo.display.api.dto.PromoDisplayDTO;
import com.dianping.shopremote.remote.dto.ShopDTO;
import com.dianping.tgc.open.entity.BrandActivityDTO;
import com.dianping.unified.coupon.manage.api.dto.UnifiedCouponDTO;
import com.sankuai.dealuser.price.display.api.model.PriceDisplayDTO;
import com.sankuai.mkt.activity.api.merchantsharecoupon.model.MerchantShareCouponExposeDTO;
import com.sankuai.mpmctbrand.brandc.dto.brand.BrandInfoDTO;
import com.sankuai.mpmkt.coupon.search.api.dto.CouponDesc;
import com.sankuai.nibmktproxy.queryclient.proxy.PromotionDTOResult;
import com.sankuai.pay.access.client.predisplay.dto.PrePromoDTO;
import lombok.Data;
import org.apache.commons.lang3.tuple.Pair;

import java.util.List;
import java.util.Map;
import java.util.concurrent.Future;

@Data
public class CouponActivityContext {

    private String cx;
    private Double latitude;
    private Double longitude;
    private String dpid;
    private String version;

    private int cityId;
    private int actualCityId;
    private int mtCityId;
    private int mtActualCityId;
    private long dpUserId;
    private long mtUserId;
    private long mtVirtualUserId;

    private long dpShopIdL;
    private long mtShopIdL;

    private String dpShopUuid;

    private ShopDTO shopDTO;

    private Pair<Integer, Integer> dpShopCategoryPair;

    //0:点评 1:美团
    private User userType;
    private long userId;
    private String phone;

    private String uuid;
    private int cPlatform;
    private String mtFingerprint;
    private String mtgsig;
    private String regionId;

    private int skuId;
    private int dpDealGroupId;
    private int mtDealGroupId;
    private long spuGroupId;

    private List<Integer> skuIds;
    private List<Integer> dpDealGroupIds;
    private List<Integer> mtDealGroupIds;
    private Map<Integer, Integer> mtDpDealGroupMap;
    private long brandId;
    private Long mtRealUserIdFromDp;

    // 丽人团详新样式，详见：https://km.sankuai.com/page/952322502
    private Boolean beautyDealNewType;
    private Boolean needDiscountCoupon;


    //0：根据门店查询对应 1：团购 2：商品 3:根据批量商品查 5：标品
    private int queryShopCouponType;
    // 查询平台：点评为com.dianping.pay.common.enums.PayPlatform，美团为com.dianping.gmkt.coupon.common.api.enums.MtCouponPlatform
    private int couponPlatform;
    // 查询平台，点评美团均为com.dianping.pay.common.enums.PayPlatform
    private int payPlatform;
    //渠道标识
    private String channel;

    /**
     * @see com.dianping.pay.api.enums.ProductTypeEnum
     */
    private int productType;

    private boolean isDealPromoProxy;

    //是否需要返回返礼信息
    private boolean needReturnPromotion;
    //是否需要返回分享券信息
    private boolean needShareCouponPromotion;
    //是否需要返回立减信息
    private boolean needReductionPromotion;
    //是否要返回投放资源信息
    private boolean needResourcesPromotion;
    //是否要返回门店投放资源信息
    private boolean needShopResourcesPromotion;
    //是否要用户券
    private boolean needUserPromotion;
    //是否要品牌券活动
    private boolean needBrandPromotion;

    private boolean beMemberPriceProxy;
    //是否需要金融券
    private boolean needFinancialPromo;
    //是否通过商品价格服务获取优惠信息
    private boolean needQueryPricePromotion;
    //是否查询商家账户权益券
    private boolean needMerchantAccountPromotion;
    //是否查top广告投放资源信息
    private boolean needTopAdResourcePromotion;
    //是否返回神会员券
    private boolean needMagicalMemberCoupon;

    //点评神劵灰度结果
    private boolean dpMmcGrayControlPaas;





    //存放future信息
    private Future<List<IssueCouponActivity>> tgcCouponPromotionFuture;
    private Future<ReturnPromotionDisplayDto> returnPromotionFuture;
    private Future<List<MerchantShareCouponExposeDTO>> shareCouponPromotionFuture;
    private Future<PromotionDTOResult> proxyPromotionFuture;
    private Future<Pair<List<PromoDisplayDTO>, List<PromoDisplayDTO>>> reductionPromotionFuture;
    private Future<ResourcePromotionDo> resourceExposureFuture;
    private Future<ShopResourcePromotionDO> shopResourceExposureFuture;
    private Future<TopAdResourcePromotionDO> topAdResourceExposureFuture;
    private Future<List<UnifiedCouponDTO>> userCouponPromotionFuture;
    private Future<Pair<Map<Integer, Map<String, List<Integer>>>, List<IssueCouponActivity>>> tgcBatchCouponPromotionFuture;
    private Future<List<BrandActivityDTO>> brandActivityFuture;
    private Future<Map<Integer, List<UnifiedCouponDTO>>> userIssuedCouponFuture;
    private Future<Map<Long, BrandInfoDTO>> brandInfoFuture;
    private Future<PromotionDTOResult> proxyReducePromotionFuture;
    private Future<PromotionDTOResult> proxyMemberCardPromotionFuture;
    private Future<PromotionDTOResult> proxyJoyCardPromotionFuture;
    private Future<PrePromoDTO> financialCouponPromotionFuture;
    private Future<PriceDisplayDTO> priceDisplayPromotionFuture;
    private Future<MagicalMemberTagRawDO> magicalMemberTagRawDOFuture;
    //left:未领，right：已领
    private Future<Pair<List<CouponDesc>, List<CouponDesc>>> merchantAccountCouponPromotionFuture;


    //存放future get后的信息
    private List<IssueCouponActivity> issueCouponActivities;
    private ReturnPromotionDisplayDto returnPromotionDisplayDto;
    private List<MerchantShareCouponExposeDTO> couponExposeDTOS;
    private PromotionDTOResult promotionDTOResult;
    private Pair<List<PromoDisplayDTO>, List<PromoDisplayDTO>> reductionPromotions;
    private List<PromoDisplayDTO> promoDisplayDTOS;
    private List<PromoDisplayDTO> discountPromoDisplayDTOS;
    private ResourcePromotionDo resourceExposure;
    private ShopResourcePromotionDO shopResourcePromotionDO;
    private TopAdResourcePromotionDO topAdResourcePromotionDO;
    private List<UnifiedCouponDTO> unifiedCouponDTOS;
    private Pair<Map<Integer, Map<String, List<Integer>>>, List<IssueCouponActivity>> batchIssueCouponActivitiesPair;
    private List<BrandActivityDTO> brandActivityDTOS;
    private Map<Integer, List<UnifiedCouponDTO>> userIssuedCoupon;
    private Map<Long, BrandInfoDTO> brandInfo;
    private PrePromoDTO financialCouponPromotion;
    private PriceDisplayDTO priceDisplayDTO;
    private MagicalMemberTagRawDO magicalMemberTagRawDO;
    private Pair<List<CouponDesc>, List<CouponDesc>> merchantAccountCouponPromotion;

    //价格力优化--三个优惠请求
    private PromotionDTOResult proxyReducePromotionResult;
    private PromotionDTOResult proxyMemberCardPromotionResult;
    private PromotionDTOResult proxyJoyCardPromotionResult;

    //根据三种优惠模式，计算出最佳的优惠
    private PromotionDTOResult bestProxyPromotionResult;


    //一些基本信息
    private DealGroupBaseDTO dealGroupBaseDTO;
    // 商品详情
    private ProductDetailDO productDetailDO;
    //团单详情，如团单使用时间、团单二级类目、团单三方信息
    private DealDetailDO dealDetailDO;

    private Map<String, String> extParam;

    private String pageSource;

    private String couponPageSource;

    private String trafficSource;

    private String business;

    private Integer miniProgramFlag;

    private String appId;

    private String openId;

    private Integer cposition;

    /**
     * 神券是否可膨，透传给优惠台
     */
    private String mmcInflate;

    /**
     * 神券是否可用，透传给优惠台
     */
    private String mmcUse;

    /**
     * 券包是否可买，透传给优惠台
     */
    private String mmcBuy;

    /**
     * 神券是否可领塞，透传给优惠台
     */
    private String mmcFree;

    /**
     * 神券组件版本
     */
    private String magicMemberComponentVersion;

    private Map<String, AbTestInfo> abResult;

    /**
     * 国补氛围信息
     */
    private GovernmentSubsidyInfo governmentSubsidyInfo;

    public boolean isMiniProgram() {
        return miniProgramFlag != null && miniProgramFlag == 1;
    }

    public boolean isMt(){
        return userType == User.MT;
    }

    public boolean isMmcGrayControlPaas() {
        if (this.userType != User.DP) {
            return true;
        }
        return dpMmcGrayControlPaas;
    }
}
