package com.dianping.pay.api.biz.discount;

import com.dianping.api.domain.Version;
import com.dianping.cat.Cat;
import com.dianping.combiz.spring.util.PropertiesLoaderSupportUtils;
import com.dianping.pay.api.beans.GetPromoDeskRequest;
import com.dianping.pay.api.beans.PromoDTOGroup;
import com.dianping.pay.api.beans.PromoProduct;
import com.dianping.pay.api.entity.promodesk.DiscountPromoEvent;
import com.dianping.pay.api.entity.promodesk.DiscountPromoEventGroup;
import com.dianping.pay.api.entity.promodesk.DiscountPromoTool;
import com.dianping.pay.cashier.biz.enums.PaymentRule;
import com.dianping.pay.common.enums.ProductCode;
import com.dianping.pay.promo.common.enums.PromoSource;
import com.dianping.pay.promo.common.enums.PromoType;
import com.dianping.pay.promo.execute.service.MtCampaignQueryService;
import com.dianping.pay.promo.execute.service.dto.*;
import com.dianping.pay.promo.execute.service.dto.request.MtCampaignQueryRequest;
import com.dianping.pay.promo.execute.service.dto.request.MtMaitonCampaignQueryRequest;
import com.dianping.pay.promo.execute.service.dto.response.PromoQueryResponse;
import com.google.common.collect.Lists;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;

import java.math.BigDecimal;
import java.util.*;

/**
 * Created by lixiang on 2016/12/13.
 */
public class MTDiscountPromoQuery {
    @Autowired
    private MtCampaignQueryService mtCampaignQueryService;

    public DiscountPromoTool queryPromo(GetPromoDeskRequest getPromoDeskRequest, List<PromoProduct> promoProductList) {
        Cat.logEvent("INVALID_METHOD_2", "com.dianping.pay.api.biz.discount.MTDiscountPromoQuery.queryPromo(com.dianping.pay.api.beans.GetPromoDeskRequest,java.util.List)");
        DiscountPromoTool discountPromoTool = new DiscountPromoTool();
        if (CollectionUtils.isEmpty(promoProductList)){
            return discountPromoTool;
        }

        Version version = new Version("8.0.8");
        List<MtCampaignQueryRequest> requestList = MTPayPromoQueryMapper.buildRequest(
                getPromoDeskRequest,
                promoProductList);

        for (MtCampaignQueryRequest campaignQueryRequest : requestList){
            PromoQueryResponse<List<MtCampaignDetailDTO>> response = mtCampaignQueryService.queryMtCampaignDetail(campaignQueryRequest);
            if (response.isSuccess() && response.getResult() != null) {
                for (MtCampaignDetailDTO campaignDetailDTO : response.getResult()) {
                    if (Version.compareTo(getPromoDeskRequest.getClientInfo().getVersion(), version) < 0) {
                        continue;
                    }
                    DiscountPromoEventGroup discountPromoEventGroup = new DiscountPromoEventGroup();
                    discountPromoEventGroup.setDiscountPromoEvents(new ArrayList<DiscountPromoEvent>());
                    discountPromoEventGroup.setIcon(PropertiesLoaderSupportUtils.getProperty("pay-api-mobile.promodesk.discount.icon"));
                    discountPromoEventGroup.setMutexTools(PaymentRule.getMutexPaymentRuleCode(PaymentRule.REDUCTION, campaignDetailDTO.getPaymentRuleId()));
                    discountPromoEventGroup.setMutexProductCodes(PayPromoQueryMapper.toMutexProductCodes(ProductCode.TUANGOU.getCode()));
                    discountPromoEventGroup.setProductCode(ProductCode.TUANGOU.getCode());

                    DiscountPromoEvent discountPromoEvent = new DiscountPromoEvent(discountPromoEventGroup);
                    discountPromoEvent.setId(campaignDetailDTO.getCampaignId());
                    discountPromoEvent.setTitle(campaignDetailDTO.getShortTitle());
                    discountPromoEvent.setDesc(campaignDetailDTO.getLongTitle());
                    discountPromoEvent.setCanUse(true);
                    discountPromoEvent.setPromoAmount(campaignDetailDTO.getReduceDTOs().get(0).getReduce().doubleValue());
                    discountPromoEvent.setPromoCipher(campaignDetailDTO.getCipher());
                    discountPromoEventGroup.getDiscountPromoEvents().add(discountPromoEvent);
                    discountPromoTool.getDiscountPromoEventGroups().add(discountPromoEventGroup);
                }
            }
        }
        return discountPromoTool;
    }

    public DiscountPromoTool queryShanhuiPromo(GetPromoDeskRequest getPromoDeskRequest, List<PromoProduct> promoProductList) {
        Cat.logEvent("INVALID_METHOD_3", "com.dianping.pay.api.biz.discount.MTDiscountPromoQuery.queryShanhuiPromo(com.dianping.pay.api.beans.GetPromoDeskRequest,java.util.List)");
        Map<Integer, PromoDTOGroup> promoDTOGroupMap = new HashMap<Integer, PromoDTOGroup>();
        Version version = new Version("8.0.8");

        List<MtMaitonCampaignQueryRequest> requestList = MTPayPromoQueryMapper.buildShanhuiRequest(
                getPromoDeskRequest,
                promoProductList);
        for (MtMaitonCampaignQueryRequest requset : requestList) {
            PromoQueryResponse<List<MtMaitonCampaignDetailDTO>> response = mtCampaignQueryService.queryMtMaitonCampaignDetail(requset);
            if (response.isSuccess() && response.getResult() != null) {
                BigDecimal maxPromoValue = null, minOrderAmountLimit = null;
                for (MtMaitonCampaignDetailDTO campaignDetailDTO : response.getResult()) {
                    if (maxPromoValue == null
                            || campaignDetailDTO.getCampDiscount() != null
                            && campaignDetailDTO.getCampDiscount().compareTo(maxPromoValue) > 0) {
                        maxPromoValue = campaignDetailDTO.getCampDiscount();
                    }
                    if (minOrderAmountLimit == null
                            || campaignDetailDTO.getOrderAmountLimit() != null
                            && campaignDetailDTO.getOrderAmountLimit().compareTo(minOrderAmountLimit) < 0) {
                        minOrderAmountLimit = campaignDetailDTO.getOrderAmountLimit();
                    }
                }
                for (MtMaitonCampaignDetailDTO campaignDetailDTO : response.getResult()) {
                    if (Version.compareTo(getPromoDeskRequest.getClientInfo().getVersion(), version) < 0
                            && campaignDetailDTO.getPromoSource() == PromoSource.shopSource.getCode()) {
                        continue;
                    }
                    int promoGroupId = campaignDetailDTO.getCampaignId();
                    PromoDTOGroup promoDTOGroup = promoDTOGroupMap.get(promoGroupId);
                    if (promoDTOGroup == null) {
                        promoDTOGroup = new PromoDTOGroup();
                        promoDTOGroup.setPromoGroupId(campaignDetailDTO.getCampaignId());
                        promoDTOGroup.setProductCode(ProductCode.MO2O2PAY.getCode());
                        promoDTOGroup.setMutexProductCodes(PayPromoQueryMapper.toMutexProductCodes(ProductCode.MO2O2PAY.getCode()));
                        promoDTOGroup.setMutexTools(PaymentRule.getMutexPaymentRuleCode(PaymentRule.REDUCTION, campaignDetailDTO.getPaymentRuleId()));
                        promoDTOGroup.setMaxPromoValue(campaignDetailDTO.getCampDiscount());
                        promoDTOGroupMap.put(promoGroupId, promoDTOGroup);
                    }
                    promoDTOGroup.getPromoDTOList().add(toPromoExecuteDetailDTO(campaignDetailDTO, minOrderAmountLimit));
                    promoDTOGroup.setMaxPromoValue(maxPromoValue != null ? maxPromoValue : BigDecimal.ZERO);
                }
            }
        }
        List<PromoDTOGroup> promoDTOGroupList = Lists.newArrayList(promoDTOGroupMap.values());
        Collections.sort(promoDTOGroupList);
        return PayPromoQueryMapper.toDiscountPromoTool(promoDTOGroupList);
    }

    private PromoExecuteDetailDTO toPromoExecuteDetailDTO(MtMaitonCampaignDetailDTO input, BigDecimal minOrderAmountLimit){
        Cat.logEvent("INVALID_METHOD_5", "com.dianping.pay.api.biz.discount.MTDiscountPromoQuery.toPromoExecuteDetailDTO(com.dianping.pay.promo.execute.service.dto.MtMaitonCampaignDetailDTO,java.math.BigDecimal)");
        PromoExecuteDetailDTO output = new PromoExecuteDetailDTO();
        output.setValidateResult(new ArrayList<Integer>());
        output.setPromoGroupId(input.getCampaignId());
        output.setPromoId(input.getComCampId());
        output.setPaymentRuleId(input.getPaymentRuleId());
        output.setPromoAmount(input.getCampDiscount());
        output.setTotalPromoAmount(input.getCampDiscount());
        output.setPromoCipher(input.getCipher());
        output.setPromoSource(input.getPromoSource());
        output.setPromoType(PromoType.reduce.value);

        PromoRuleDetailDTO ruleDetail = new PromoRuleDetailDTO();
        ruleDetail.setOrderPromoLimit(input.getOrderAmountLimit());
        ruleDetail.setProductCode(ProductCode.MO2O2PAY.getCode());
        ruleDetail.setUserBuyTimes(input.getMaxNum());
        output.setPromoRuleDetailDTO(ruleDetail);

        PromoTextDetailDTO textDetail = new PromoTextDetailDTO();
        textDetail.setDisplayTitle(input.getShortTitle());
        textDetail.setDisplayDesc(String.format(
                "折后满%s元可享",
                minOrderAmountLimit != null ? minOrderAmountLimit.stripTrailingZeros().toPlainString() : "0"
        ));
        output.setPromoTextDetailDTO(textDetail);
        return output;
    }
}