package com.dianping.pay.api.biz.activity.newloader.promo;

import com.dianping.cat.Cat;
import com.dianping.lion.client.Lion;
import com.dianping.mobile.framework.datatypes.IMobileContext;
import com.dianping.pay.api.beans.MagicalMemberTagRawDO;
import com.dianping.pay.api.biz.activity.newloader.dto.AbTestInfo;
import com.dianping.pay.api.biz.activity.newloader.dto.CouponActivityContext;
import com.dianping.pay.api.constants.AbTestConstant;
import com.dianping.pay.api.constants.CatEventConstants;
import com.dianping.pay.api.service.impl.ShopWrapperService;
import com.dianping.pay.api.util.CouponAggrGrayUtils;
import com.dianping.pay.api.util.DotUtils;
import com.dianping.pay.api.util.JsonUtils;
import com.dianping.pay.api.util.LionConstants;
import com.sankuai.meituan.waimai.thrift.tools.cache.RegionSetConfigCache;
import com.sankuai.meituan.waimai.thrift.tools.enums.SetProxyTypeEnum;
import com.sankuai.meituan.waimai.thrift.tools.utils.MTraceRouterInfoUtil;
import com.sankuai.nib.magic.member.base.enums.BizEnum;
import com.sankuai.nib.mkt.common.base.enums.PageSourceEnum;
import com.sankuai.nib.mkt.common.base.enums.RealGpsCoordTypeEnum;
import com.sankuai.nib.mkt.common.base.enums.RulePropertyTypeEnum;
import com.sankuai.nib.mkt.common.base.enums.TrafficFlagEnum;
import com.sankuai.nib.mkt.common.base.enums.activity.AggregateStrategyEnum;
import com.sankuai.nib.mkt.common.base.enums.ExperimentContentEnum;
import com.sankuai.nib.mkt.common.base.model.Property;
import com.sankuai.nibmkt.promotion.api.query.model.UserInfoDTO;
import com.sankuai.nibmkt.promotion.api.query.model.magicalmember.request.QueryMagicalMemberTagRequest;
import com.sankuai.nibmkt.promotion.api.query.model.magicalmember.response.MagicalMemberCouponDTO;
import com.sankuai.nibmkt.promotion.api.query.model.magicalmember.response.MagicalMemberTagTextDTO;
import com.sankuai.nibmkt.promotion.api.query.model.magicalmember.response.QueryMagicalMemberTagResponse;
import com.sankuai.nibmkt.promotion.api.query.model.poi.PoiDTO;
import com.sankuai.nibmkt.promotion.api.service.PromotionQueryService;
import com.sankuai.nib.mkt.common.base.enums.propertyValueType.ClientTypeEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.concurrent.Callable;
import java.util.concurrent.Future;
import java.util.concurrent.TimeUnit;

import static com.dianping.api.common.enums.TrafficSourceEnum.NewYouHuiMa;


@Component("magicalMemberCouponProcessor")
@Slf4j
public class MagicalMemberCouponProcessor extends AbstractPromoProcessor {

    public static final String FORMAT_SMALL = "yyyy/MM/dd";
    public static final int DAO_JIA_COUPON = 1;
    public static final int DAO_DIAN_COUPON = 2;
    public static final int HAS_INFLATED = 1;       // 已膨
    public static final int NOT_YET_INFLATED = 0;   // 未膨
    public static final String couponButtonText = "免费膨胀";
    public static final int GO_TO_INFLATE = 1;
    public static final int PAID_MMC = 1;
    public static final int FREE_MMC = 2;
    public static final int AVAILABLE_COUPON = 1;
    public static final int VALID_COUPON_STATUS = 1;
    public static final int CAN_INFLATE = 1;       // 可膨
    public static final int CAN_NOT_INFLATE = 0;   // 不可膨
    public static final int MINI_PROGRAM_POI_POSITION = 1102;
    public static final int POI_POSITION = 2102;   // POI详情页-领券栏
    public static final int DP_POI_POSITION = 3102;

    @Autowired
    private PromotionQueryService promotionQueryService;

    @Autowired
    private ShopWrapperService shopWrapperService;

    @Override
    public void prePare(CouponActivityContext promoCtx, IMobileContext iMobileContext) {
        try {
            // 降级开关
            if (!Lion.getBooleanValue(LionConstants.MMC_COUPON_DEGRADE_SWITCH, false)) {
                return;
            }
            if (!promoCtx.isNeedMagicalMemberCoupon()) {
                return;
            }
            if (promoCtx.getMtUserId() <= 0) {
                return;
            }
            if (promoCtx.isMiniProgram() && !Lion.getBooleanValue(LionConstants.MINIPROGRAM_MMC_COUPON_DEGRADE_SWITCH, false)) {
                return;
            }
            // 点评端降级开关
            if (!promoCtx.isMt() && (!Lion.getBooleanValue(LionConstants.DP_MMC_COUPON_DEGRADE_SWITCH, false)
                    || !promoCtx.isMmcGrayControlPaas())) {
                return;
            }
            if (promoCtx.getMtShopIdL() <= 0) {
                return;
            }
            if (promoCtx.getMtCityId() <= 0) {
                return;
            }
            DotUtils.dotForPromoProcessor(promoCtx, getPromoName());
            Future<MagicalMemberTagRawDO> future = ExecutorService.submit(new Callable<MagicalMemberTagRawDO>() {
                @Override
                public MagicalMemberTagRawDO call() throws Exception {
                    return callMagicalMemberTag(promoCtx, iMobileContext);
                }
            });
            promoCtx.setMagicalMemberTagRawDOFuture(future);
        } catch (Exception e) {
            log.error("MagicalMemberCouponProcessor# prepare error. ctx: {}", promoCtx, e);
        }
    }

    @Override
    public void loadPromo(CouponActivityContext promoCtx) {
        try {
            Future<MagicalMemberTagRawDO> magicalMemberTagDORawFuture = promoCtx.getMagicalMemberTagRawDOFuture();
            if (magicalMemberTagDORawFuture != null) {
                int timeout = Lion.getIntValue(LionConstants.MAGICAL_MEMBER_PROMOTION_TIMEOUT, 500);
                promoCtx.setMagicalMemberTagRawDO(magicalMemberTagDORawFuture.get(timeout, TimeUnit.MILLISECONDS));
            }
        } catch (Exception e) {
            log.error("MagicalMemberCouponProcessor# loadPromo error. ctx: {}", promoCtx, e);
        }
    }

    @Override
    public String getPromoName() {
        Cat.logEvent("INVALID_METHOD_5", "com.dianping.pay.api.biz.activity.newloader.promo.MagicalMemberCouponProcessor.getPromoName()");
        return "magicalMemberCouponProcessor";
    }



    private MagicalMemberTagRawDO callMagicalMemberTag(CouponActivityContext promoCtx, IMobileContext iMobileContext) {
        try {
            // regionId埋入trace, 背景见https://km.sankuai.com/collabpage/**********
            String regionId = promoCtx.getRegionId();
            if (StringUtils.isNotBlank(regionId)) {
                String setName = RegionSetConfigCache.getCellNameByRegionId(regionId);
                MTraceRouterInfoUtil.setTraceRoutingInfo4Api(SetProxyTypeEnum.BY_USER_ID, promoCtx.getMtUserId(), regionId, setName);
            }

            QueryMagicalMemberTagRequest request = buildQueryMagicalMemberTagRequest(promoCtx, iMobileContext);
            if (request == null) {
                return null;
            }
            log.info("MagicalMemberCouponProcessor#callMagicalMemberTag request: {}", JsonUtils.toJson(request));
            QueryMagicalMemberTagResponse response = promotionQueryService.queryMagicalMemberTag(request);
            MagicalMemberTagRawDO magicalMemberTagRawDO = buildMagicalMemberTagDO(response, promoCtx.getMtShopIdL());
            log.info("MagicalMemberCouponProcessor#callMagicalMemberTag request: {}, QueryMagicalMemberTagResponse:{}", JsonUtils.toJson(request), JsonUtils.toJson(response));
            return magicalMemberTagRawDO;
        } catch (Exception e) {
            log.error("MagicalMemberCouponProcessor#callMagicalMemberTag error,  ctx: {}", promoCtx, e);
        }
        return null;
    }

    public QueryMagicalMemberTagRequest buildQueryMagicalMemberTagRequest(CouponActivityContext promoCtx, IMobileContext iMobileContext) {
        QueryMagicalMemberTagRequest request = new QueryMagicalMemberTagRequest();
        if (com.dianping.api.common.enums.PageSourceEnum.BeautyMedicalPOIDetail.code.equals(promoCtx.getCouponPageSource())) {
            request.setNibBiz(BizEnum.GENERAL_PREPAY.name());
        } else {
            request.setNibBiz(BizEnum.GENERAL_GROUPBUY.name());
        }
        UserInfoDTO userInfoDTO = new UserInfoDTO();
        userInfoDTO.setUserId(promoCtx.getMtUserId());
        if (StringUtils.isNotBlank(promoCtx.getUuid())) {
            userInfoDTO.setDeviceId(promoCtx.getUuid());
        } else {
            userInfoDTO.setDeviceId(promoCtx.getDpid());
        }
        if (!promoCtx.isMt()) {
            userInfoDTO.setDpId(promoCtx.getDpid());
        }
        request.setUserInfoDTO(userInfoDTO);

        List<PoiDTO> poiDTOList = new ArrayList<>();
        PoiDTO poiDTO = new PoiDTO();
        poiDTO.setPoiId(promoCtx.getMtShopIdL());
        Pair<Integer, Integer> categoryList = promoCtx.isMt() ?  shopWrapperService.loadMtShopFirstCategory(promoCtx.getMtShopIdL())
                : promoCtx.getDpShopCategoryPair();
        if (categoryList == null) {
            Cat.logEvent(CatEventConstants.QUERY_SHOP_CATEGORY, promoCtx.getMtShopIdL() + "_null");
            return null;
        }
        Integer firstCategory = categoryList.getLeft();
        Integer secondCategory = categoryList.getRight();
        if (firstCategory == null) {
            Cat.logEvent(CatEventConstants.QUERY_SHOP_CATEGORY, promoCtx.getMtShopIdL() + "_firstCategory_null");
            return null;
        }
        List<Property> propertyList = new ArrayList<>();
        Property poiFirstCategory = new Property();
        poiFirstCategory.setCode(RulePropertyTypeEnum.poiFirstCategory.getCode());
        poiFirstCategory.setValue(String.valueOf(firstCategory));
        propertyList.add(poiFirstCategory);
        if (secondCategory != null) {
            Property poiSecondCategory = new Property();
            poiSecondCategory.setCode(RulePropertyTypeEnum.poiSecondCategory.getCode());
            poiSecondCategory.setValue(String.valueOf(secondCategory));
            propertyList.add(poiSecondCategory);
            Cat.logEvent(CatEventConstants.QUERY_SHOP_CATEGORY, "success");
        } else {
            Cat.logEvent(CatEventConstants.QUERY_SHOP_CATEGORY, promoCtx.getMtShopIdL() + "_secondCategory_null");
        }

        if (!promoCtx.isMt()) {
            Property nibBizProperty = new Property();
            nibBizProperty.setCode(RulePropertyTypeEnum.nibBiz.getCode());
            nibBizProperty.setValue(BizEnum.GENERAL_GROUPBUY.name());
            propertyList.add(nibBizProperty);
        }
        poiDTO.setPropertyList(propertyList);
        poiDTOList.add(poiDTO);
        request.setPoiIdDTOList(poiDTOList);

        request.setPageSource(PageSourceEnum.poiDetailPage.getCode());

        List<Property> commonProperties = new ArrayList<>();
        Property clientTp = new Property();
        clientTp.setCode(RulePropertyTypeEnum.clientTp.getCode());
        if (promoCtx.isMiniProgram()) {
            clientTp.setValue(ClientTypeEnum.MT_WE_CHAT_APPLET.getValue());
        } else if (promoCtx.isMt()) {
            clientTp.setValue(promoCtx.getCPlatform() == 1 ? String.valueOf(ClientTypeEnum.IPHONE.getValue()) : String.valueOf(ClientTypeEnum.ANDROID.getValue()));
        }else {
            clientTp.setValue(promoCtx.getCPlatform() == 1 ? String.valueOf(ClientTypeEnum.DP_IPHONE.getValue()) : String.valueOf(ClientTypeEnum.DP_ANDROID.getValue()));
        }
        commonProperties.add(clientTp);
        Property gpsMtCityId = new Property();
        gpsMtCityId.setCode(RulePropertyTypeEnum.gpsMtCityId.getCode());
        gpsMtCityId.setValue(String.valueOf(promoCtx.getMtActualCityId()));
        commonProperties.add(gpsMtCityId);
        Property version = new Property();
        version.setCode(RulePropertyTypeEnum.version.getCode());
        version.setValue(iMobileContext.getVersion());
        commonProperties.add(version);
        Property latitude = new Property();
        latitude.setCode(RulePropertyTypeEnum.latitude.getCode());
        latitude.setValue(promoCtx.getLatitude() == null ? null : String.valueOf(promoCtx.getLatitude()));
        commonProperties.add(latitude);
        Property longitude = new Property();
        longitude.setCode(RulePropertyTypeEnum.longitude.getCode());
        longitude.setValue(promoCtx.getLongitude() == null ? null : String.valueOf(promoCtx.getLongitude()));
        commonProperties.add(longitude);
        Property browseMtCityId = new Property();
        browseMtCityId.setCode(RulePropertyTypeEnum.browseMtCityId.getCode());
        browseMtCityId.setValue(String.valueOf(promoCtx.getMtCityId()));
        commonProperties.add(browseMtCityId);
        Property position = new Property();
        position.setCode(RulePropertyTypeEnum.position.getCode());
        Integer cposition = promoCtx.getCposition();
        if (cposition == null) {
            if (promoCtx.isMiniProgram()) {
                cposition = MINI_PROGRAM_POI_POSITION;
            } else if (promoCtx.isMt()) {
                cposition = POI_POSITION;
            }else {
                cposition = DP_POI_POSITION;
            }
        }
        position.setValue(String.valueOf(cposition));
        commonProperties.add(position);
        if (com.dianping.api.common.enums.PageSourceEnum.BeautyMedicalPOIDetail.code.equals(promoCtx.getCouponPageSource())) {
            Property realGpsCoordType = new Property();
            realGpsCoordType.setCode(RulePropertyTypeEnum.realGpsCoordType.getCode());
            realGpsCoordType.setValue(RealGpsCoordTypeEnum.GCJ02.name());
            commonProperties.add(realGpsCoordType);
        }
        fillMiniProgramProperties(commonProperties, promoCtx);
        Property couponAggrStrategy = new Property();
        couponAggrStrategy.setCode(RulePropertyTypeEnum.couponAggregateStrategy.getCode());
        if (CouponAggrGrayUtils.hitCouponAggrGrayRule(promoCtx.isMt() ? promoCtx.getMtUserId() : promoCtx.getDpUserId())) {
            couponAggrStrategy.setValue(String.valueOf(AggregateStrategyEnum.MAGIC_MEMBER_COUPON_AGGREGATION.getCode()));
        } else {
            couponAggrStrategy.setValue(String.valueOf(AggregateStrategyEnum.NO_AGGREGATION.getCode()));
        }
        commonProperties.add(couponAggrStrategy);

        Property componentVersion=new Property();
        componentVersion.setCode(RulePropertyTypeEnum.magicMemberComponentVersion.getCode());
        componentVersion.setValue(promoCtx.getMagicMemberComponentVersion());
        commonProperties.add(componentVersion);

        fillInflateTrafficSourceProperty(commonProperties, promoCtx);
        fillAbTestStrategy(commonProperties, promoCtx);
        request.setCommonProperties(commonProperties);

        return request;
    }

    private void fillAbTestStrategy(List<Property> commonProperties, CouponActivityContext promoCtx) {
        List<Integer> inflateExperimentIdList = new ArrayList<>();
        AbTestInfo poiAbTest = MapUtils.getObject(promoCtx.getAbResult(), AbTestConstant.POI_MMC_ISSUE_COUPON_AB);
        if (poiAbTest != null && AbTestConstant.SHOW_NEW_MMC_TEXT_WITH_ANIME.equals(poiAbTest.getHitStrategy())) {
            inflateExperimentIdList.add(ExperimentContentEnum.ENHANCE_PERCEPTION.getCode());
        }
        if (CollectionUtils.isNotEmpty(inflateExperimentIdList)) {
            Property abTestStrategy = new Property();
            abTestStrategy.setCode(RulePropertyTypeEnum.inflateExperimentId.getCode());
            abTestStrategy.setValue(JsonUtils.toJson(inflateExperimentIdList));
            commonProperties.add(abTestStrategy);
        }
    }

    private void fillMiniProgramProperties(List<Property> commonProperties, CouponActivityContext promoCtx) {
        if (!promoCtx.isMiniProgram()) {
            return;
        }

        Property gpsCoordProperty = new Property();
        gpsCoordProperty.setCode(RulePropertyTypeEnum.realGpsCoordType.getCode());
        gpsCoordProperty.setValue(RealGpsCoordTypeEnum.GCJ02.name());
        commonProperties.add(gpsCoordProperty);

        Property openIdProperty = new Property();
        openIdProperty.setCode(RulePropertyTypeEnum.wxOpenId.getCode());
        openIdProperty.setValue(promoCtx.getOpenId());
        commonProperties.add(openIdProperty);

        Property appIdProperty = new Property();
        appIdProperty.setCode(RulePropertyTypeEnum.wxAppId.getCode());
        appIdProperty.setValue(promoCtx.getAppId());
        commonProperties.add(appIdProperty);

        Property wxVersionProperty = new Property();
        wxVersionProperty.setCode(RulePropertyTypeEnum.wxVersion.getCode());
        wxVersionProperty.setValue(promoCtx.getVersion());
        commonProperties.add(wxVersionProperty);

        Property mtgsigProperty = new Property();
        mtgsigProperty.setCode(RulePropertyTypeEnum.mtgsig.getCode());
        mtgsigProperty.setValue(promoCtx.getMtgsig());
        commonProperties.add(mtgsigProperty);

        Property fingerprintProperty = new Property();
        fingerprintProperty.setCode(RulePropertyTypeEnum.fingerprint.getCode());
        fingerprintProperty.setValue(promoCtx.getMtFingerprint() != null ? promoCtx.getMtFingerprint() : promoCtx.getCx());
        commonProperties.add(fingerprintProperty);
    }
    private MagicalMemberTagRawDO buildMagicalMemberTagDO(QueryMagicalMemberTagResponse response, Long poiId) {
        if (response == null) {
            return null;
        }
        MagicalMemberTagRawDO magicalMemberTagRawDO = new MagicalMemberTagRawDO();
        // 标签信息
        Map<Long, MagicalMemberTagTextDTO> poiMagicMemberTagTextMap = response.getPoiMagicMemberTagTextMap();
        if (MapUtils.isNotEmpty(poiMagicMemberTagTextMap) && poiMagicMemberTagTextMap.containsKey(poiId)) {
            magicalMemberTagRawDO.setMagicalMemberTagText(poiMagicMemberTagTextMap.get(poiId));
        }
        // 券包id信息
        Map<Long, List<String>> poiCouponPackageIdMap = response.getPoiCouponPackageIdMap();
        if (MapUtils.isNotEmpty(poiCouponPackageIdMap) && poiCouponPackageIdMap.containsKey(poiId)) {
            magicalMemberTagRawDO.setMagicalMemberCouponPackageIds(poiCouponPackageIdMap.get(poiId));
        }
        // 已购神券列表
        Map<Long, List<MagicalMemberCouponDTO>> poiMagicalMemberCouponDTOMap = response.getPoiMagicalMemberCouponDTOMap();
        if (MapUtils.isNotEmpty(poiMagicalMemberCouponDTOMap) && poiMagicalMemberCouponDTOMap.containsKey(poiId)) {
            List<MagicalMemberCouponDTO> magicalMemberCouponDTOS = poiMagicalMemberCouponDTOMap.get(poiId);
            if (CollectionUtils.isNotEmpty(magicalMemberCouponDTOS)) {
                magicalMemberTagRawDO.setMagicMemberCouponInfoList(magicalMemberCouponDTOS);
            }
        }
        // extendedFieldsMap扩展字段
        Map<Long, Map<String, String>> poiId2ExtendFieldMap = response.getPoiId2ExtendFieldMap();

        if (MapUtils.isNotEmpty(poiId2ExtendFieldMap) && poiId2ExtendFieldMap.containsKey(poiId)) {
            Map<String, String> extendedFieldsMap = poiId2ExtendFieldMap.get(poiId);
            if (MapUtils.isNotEmpty(extendedFieldsMap)) {
                magicalMemberTagRawDO.setExtendedFieldsMap(extendedFieldsMap);
            }
        }
        return magicalMemberTagRawDO;
    }

    private void fillInflateTrafficSourceProperty(List<Property> commonProperties, CouponActivityContext promoCtx) {
        if (promoCtx.getTrafficSource() != null && NewYouHuiMa.code.equals(promoCtx.getTrafficSource())) {
            Property inflateTrafficSourceProperty = new Property();
            inflateTrafficSourceProperty.setCode(RulePropertyTypeEnum.trafficFlag.getCode());
            inflateTrafficSourceProperty.setValue(TrafficFlagEnum.NEW_YOUHUIMA_MINI.getCode());
            commonProperties.add(inflateTrafficSourceProperty);
        }
        if (StringUtils.isNotBlank(promoCtx.getMmcInflate())) {
            Property property = new Property();
            property.setCode(RulePropertyTypeEnum.queryInflateType.getCode());
            property.setValue(promoCtx.getMmcInflate());
            commonProperties.add(property);
        }
        if (StringUtils.isNotBlank(promoCtx.getMmcBuy())) {
            Property property = new Property();
            property.setCode(RulePropertyTypeEnum.queryMMCPackageType.getCode());
            property.setValue(promoCtx.getMmcBuy());
            commonProperties.add(property);
        }
        if (StringUtils.isNotBlank(promoCtx.getMmcUse())) {
            Property property = new Property();
            property.setCode(RulePropertyTypeEnum.queryMMCType.getCode());
            property.setValue(promoCtx.getMmcUse());
            commonProperties.add(property);
        }
    }

}
