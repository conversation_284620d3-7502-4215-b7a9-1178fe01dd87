package com.dianping.pay.api.biz.activity.newloader.dto;

import lombok.Data;

import java.util.List;

@Data
public class ActivityFilterPatternDto {

    /**
     * 美团还是点评，0点评，1美团
     */
    private int userType;

    /**
     * 匹配的活动id，只有活动id在目标列表中才进行拦截（活动id定义由各processor定义，具体参考代码实现）
     */
    private List<String> matchedActivityIds;

    /**
     * 匹配的userid，点评为点评，美团为美团，只有userId在目标列表中才进行拦截 （为空为非法参数，包含-1表示全部拦截）
     */
    private List<String> matchedUserIds;

    /**
     * 匹配的shopId，点评为点评，美团为美团，只有shopId在目标列表中才进行拦截 （为空为非法参数，包含-1表示全部拦截）
     */
    private List<String> matchedShopIds;

    /**
     * 匹配的dealGroupId，点评为点评，美团为美团，只有dealGroupId在目标列表中才进行拦截 （为空为非法参数，包含-1表示全部拦截）
     */
    private List<String> matchedDealGroupIds;

    /**
     * 匹配的skuId，只有skuId在目标列表中才进行拦截 （为空为非法参数，包含-1表示全部拦截）
     */
    private List<String> matchedSkuIds;

}
