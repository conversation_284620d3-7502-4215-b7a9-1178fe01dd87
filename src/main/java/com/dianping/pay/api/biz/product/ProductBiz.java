package com.dianping.pay.api.biz.product;

import com.alibaba.fastjson.JSON;
import com.dianping.cat.Cat;
import com.dianping.pay.api.beans.DiscountRules;
import com.dianping.pay.api.beans.PromoProduct;
import com.dianping.pay.cashier.biz.enums.PaymentRule;
import com.dianping.pay.cashier.service.ProductPaymentRuleService;
import com.dianping.pay.promo.common.enums.ProductType;
import com.dianping.tp.promo.api.dto.BizPaymentRuleDTO;
import com.dianping.tp.promo.api.enums.PromoBizKeyType;
import com.dianping.tp.promo.api.query.BizPaymentRuleRequest;
import com.dianping.tp.promo.api.service.IPromoBizPaymentService;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang.math.NumberUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

/**
 * Created by huawei.li on 15/3/12.
 */
public class ProductBiz {
    private static final Logger log = LoggerFactory.getLogger(ProductBiz.class);

    @Resource
    private IPromoBizPaymentService promoBizPaymentService;

    public DiscountRules getDiscountRules(PromoProduct promoProduct) {
        Cat.logEvent("INVALID_METHOD_2", "com.dianping.pay.api.biz.product.ProductBiz.getDiscountRules(com.dianping.pay.api.beans.PromoProduct)");
        DiscountRules discountRules = new DiscountRules();
        discountRules.clear();
        discountRules.mergeRules(getPaymentRule(promoProduct.getProductId(), promoProduct.getProductCode(), false));
        return discountRules;
    }

    public List<Integer> getPaymentRule(int productId, int productCode, boolean isMtClient) {
        Cat.logEvent("INVALID_METHOD_3", "com.dianping.pay.api.biz.product.ProductBiz.getPaymentRule(int,int,boolean)");
        try {
            BizPaymentRuleRequest bizPaymentRuleRequest = new BizPaymentRuleRequest();
            bizPaymentRuleRequest.setPromoBizType(ProductType.fromProductCode(productCode, isMtClient).getValue());
            bizPaymentRuleRequest.setBizId(productId);
            bizPaymentRuleRequest.setPromoBizKeyType(PromoBizKeyType.PAYMENT_RULE.getValue());
            log.info( "ProductBiz.getPaymentRule.bizPaymentRuleRequest: {}" , JSON.toJSONString(bizPaymentRuleRequest));
            BizPaymentRuleDTO bizPaymentRuleDTO = promoBizPaymentService.queryBizPaymentRuleInfo(bizPaymentRuleRequest).getResult();
            log.info( "ProductBiz.getPaymentRule.bizPaymentRuleDTO: {}" , JSON.toJSONString(bizPaymentRuleDTO));
            if (bizPaymentRuleDTO != null && StringUtils.isNotBlank(bizPaymentRuleDTO.getBizValue())) {
                return toPaymentRules(NumberUtils.toInt(bizPaymentRuleDTO.getBizValue()));
            }
        } catch (Exception e) {
            log.error("getPaymentRule error", e);
        }
        return toPaymentRules(PaymentRule.ALL.value);
    }

    private List<Integer> toPaymentRules(int paymentRuleSummarize) {
        Cat.logEvent("INVALID_METHOD_5", "com.dianping.pay.api.biz.product.ProductBiz.toPaymentRules(int)");
        if (paymentRuleSummarize < 0) {
            return new ArrayList<Integer>();
        }
        List<Integer> ruleCodeList = new ArrayList<Integer>();
        for (PaymentRule rule : PaymentRule.values()) {
            if (rule == PaymentRule.ALL) {
                continue;
            }
            if (paymentRuleSummarize == 0) {
                ruleCodeList.add(rule.code);
            } else if ((paymentRuleSummarize & rule.value) == rule.value) {
                ruleCodeList.add(rule.code);
            }
        }
        return ruleCodeList;
    }
}
