package com.dianping.pay.api.biz.product;

import com.dianping.cat.Cat;
import com.dianping.combiz.spring.util.PropertiesLoaderSupportUtils;
import com.dianping.deal.base.DealGroupBaseService;
import com.dianping.deal.base.dto.DealGroupBaseDTO;
import com.dianping.pay.api.beans.PromoProduct;
import com.dianping.pay.promo.common.enums.ProductType;
import com.google.common.collect.Lists;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Created by lixiang on 2016/12/7.
 */
public class ProductJudgeBiz {
    private static final Logger log = LoggerFactory.getLogger(ProductJudgeBiz.class);


    @Autowired
    private DealGroupBaseService dealGroupBaseService;

    public Pair<List<PromoProduct>, List<PromoProduct>> judgeProductDeal(List<PromoProduct> promoProductList, boolean isMtClient){
        Cat.logEvent("INVALID_METHOD_5", "com.dianping.pay.api.biz.product.ProductJudgeBiz.judgeProductDeal(java.util.List,boolean)");

        final List<PromoProduct> foodPromoProductList = new ArrayList<PromoProduct>();
        final List<PromoProduct> otherPromoProductList = new ArrayList<PromoProduct>();
        Pair<List<PromoProduct>, List<PromoProduct>> pair = new Pair<List<PromoProduct>, List<PromoProduct>>() {
            @Override
            public List<PromoProduct> setValue(List<PromoProduct> value) {

                return null;
            }

            @Override
            public List<PromoProduct> getLeft() {
                return foodPromoProductList;
            }

            @Override
            public List<PromoProduct> getRight() {
                return otherPromoProductList;
            }
        };

        HashMap<Integer, PromoProduct> needJudgeMap = new HashMap<Integer, PromoProduct>();
        for (PromoProduct promoProduct : promoProductList){
            if (!PropertiesLoaderSupportUtils.getBoolProperty("pay-promo-execute-service.food.tuangou.promo.switch", false)
                    || ProductType.tuangou != ProductType.fromProductCode(promoProduct.getProductCode(), isMtClient)){
                otherPromoProductList.add(promoProduct);
            } else {
                needJudgeMap.put(promoProduct.getProductId(), promoProduct);
            }
        }
        if (needJudgeMap.size() <= 0){
            return pair;
        }
        Map<Integer, DealGroupBaseDTO> categoryMap;
        try {
            categoryMap = dealGroupBaseService.multiGetDealGroup(Lists.newArrayList(needJudgeMap.keySet()));
        } catch (Exception e){
            log.error(String.format("GetPublishCategory Error, Keys:%s, Msg:%s", needJudgeMap.keySet(), e.getMessage()), e);
            categoryMap = new HashMap<Integer, DealGroupBaseDTO>();
        }
        if (null == categoryMap){
            categoryMap = new HashMap<Integer, DealGroupBaseDTO>();
        }
        for (Map.Entry<Integer, PromoProduct> entry: needJudgeMap.entrySet()){
            int productId = entry.getKey();
            DealGroupBaseDTO dealGroup = categoryMap.get(productId);
            if (null != dealGroup && CollectionUtils.isNotEmpty(dealGroup.getDeals())
                    && dealGroup.getDeals().get(0).getReceiptType() == 9){
                foodPromoProductList.add(entry.getValue());
            } else {
                otherPromoProductList.add(entry.getValue());
            }
        }
        return pair;
    }
}
