package com.dianping.pay.api.biz.activity;

import com.dianping.cat.Cat;
import com.dianping.pay.api.util.AppkeyUtils;
import com.dianping.pay.unified.coupon.issue.core.api.CoreCouponIssueValidateService;
import com.dianping.pay.unified.coupon.issue.core.api.dto.common.response.CoreCouponIssueBaseResponse;
import com.dianping.pay.unified.coupon.issue.core.api.dto.enums.CoreCouponIssueDetailResultCode;
import com.dianping.pay.unified.coupon.issue.core.api.dto.request.CoreCouponSingleUserPreValidateRequest;
import com.dianping.pay.unified.coupon.issue.core.api.dto.response.CoreCouponPreValidateResultDTO;
import com.dianping.pay.unified.coupon.issue.core.api.dto.response.CoreCouponPreValidateSummaryResponse;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import org.apache.commons.collections.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

@Component
public class PreIssueValidateBiz {

    private static final Logger logger = LoggerFactory.getLogger(PreIssueValidateBiz.class);

    @Resource
    private CoreCouponIssueValidateService issueValidateService;

    public Map<Integer, Boolean> validatePreIssue(Set<Integer> couponGroupIdList, long userId, int userType, String issueSource) {
        Cat.logEvent("INVALID_METHOD_4", "com.dianping.pay.api.biz.activity.PreIssueValidateBiz.validatePreIssue(java.util.Set,long,int,java.lang.String)");
        try {
            if (CollectionUtils.isEmpty(couponGroupIdList)) {
                return Maps.newHashMap();
            }

            Map<Integer, Boolean> result = buildErrorPreIssueMap(couponGroupIdList);
            CoreCouponSingleUserPreValidateRequest validateRequest = new CoreCouponSingleUserPreValidateRequest();
            validateRequest.setCouponGroupIdList(Lists.newArrayList(couponGroupIdList));
            validateRequest.setUserId(userId);
            validateRequest.setUserType(userType);
            validateRequest.setClientAppKey(AppkeyUtils.getClientAppkey());
            validateRequest.setUseDailyStockCache(true);
            validateRequest.setIssueSource(issueSource);

            CoreCouponIssueBaseResponse<CoreCouponPreValidateSummaryResponse> response = issueValidateService.preValidateToSingleUser(validateRequest);
            if (response == null || !response.isSuccess() || response.getResult() == null) {
                Cat.logEvent("CallPreIssue", "Error");
                logger.error("call validatePreIssue failed. request: {}, resp: {}", validateRequest, response);
                return result;
            }
            result.putAll(response.getResult().getValidateResults().stream().collect(Collectors.toMap(CoreCouponPreValidateResultDTO::getCouponGroupId,
                    e -> e.getResultCode() == CoreCouponIssueDetailResultCode.SUCCESS.getCode(), (v1, v2) -> v1)));
            return result;
        } catch (Exception e) {
            logger.error("call validatePreIssue failed. couponGroupIdList: {}", couponGroupIdList, e);
            return Maps.newHashMap();
        }
    }


    private Map<Integer, Boolean> buildSuccessPreIssueMap(Set<Integer> couponGroupIdList) {
        Cat.logEvent("INVALID_METHOD_5", "com.dianping.pay.api.biz.activity.PreIssueValidateBiz.buildSuccessPreIssueMap(java.util.Set)");
        return couponGroupIdList.stream().collect(Collectors.toMap(e -> e, e -> Boolean.TRUE));
    }

    private Map<Integer, Boolean> buildErrorPreIssueMap(Set<Integer> couponGroupIdList) {
        Cat.logEvent("INVALID_METHOD_1", "com.dianping.pay.api.biz.activity.PreIssueValidateBiz.buildErrorPreIssueMap(java.util.Set)");
        return couponGroupIdList.stream().collect(Collectors.toMap(e -> e, e -> Boolean.FALSE));
    }



}
