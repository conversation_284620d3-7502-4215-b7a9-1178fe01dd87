package com.dianping.pay.api.biz.activity.newloader.promo;

import com.alibaba.fastjson.JSONObject;
import com.dianping.api.common.entity.BoothId;
import com.dianping.api.common.enums.CouponPageSourceEnum;
import com.dianping.cat.Cat;
import com.dianping.gmkt.coupon.common.api.enums.MtCouponPlatform;
import com.dianping.lion.client.Lion;
import com.dianping.mobile.framework.datatypes.IMobileContext;
import com.dianping.pay.api.beans.TopAdResourcePromotionDO;
import com.dianping.pay.api.beans.VoucherDetailDO;
import com.dianping.pay.api.biz.activity.newloader.CityBiz;
import com.dianping.pay.api.biz.activity.newloader.dto.CouponActivityContext;
import com.dianping.pay.api.biz.user.UserAccountBiz;
import com.dianping.pay.api.util.DotUtils;
import com.dianping.pay.api.util.JsonUtils;
import com.dianping.pay.api.util.LionConstants;
import com.dianping.pay.common.enums.PayPlatform;
import com.google.common.collect.Lists;
import com.meituan.service.hotel.noah.thrift.AppVersion;
import com.meituan.service.hotel.noah.thrift.TopAdRequest;
import com.meituan.service.hotel.noah.thrift.TopAdResponse;
import com.meituan.service.hotel.noah.thrift.VoucherDetail;
import com.meituan.service.hotel.noah.thrift.VoucherResult;
import com.meituan.service.hotel.noah.thrift.ad.TopAdThriftService;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.concurrent.Callable;
import java.util.concurrent.Future;
import java.util.concurrent.TimeUnit;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Component("topAdResourcePromotionProcessor")
@Slf4j
public class TopAdResourcePromotionProcessor extends AbstractPromoProcessor {

    @Autowired
    private TopAdThriftService.Iface topAdThriftService;

    @Resource
    private UserAccountBiz userAccountBiz;

    @Autowired
    private CityBiz cityBiz;

    private static final int UNKNOWN_ID = -1;
    private static final int MT_DZ_BIZ = 19;
    private static final int DP_DZ_BIZ = 20;
    private static final int IOS_OS = 1;
    private static final int ANDROID_OS = 2;
    private static final int I_OS = 999;
    private static final int MT_APP = 1;
    private static final int DP_APP = 4;
    private static final int POI_PRODUCT_PROPERTY_TYPE = 1;

    @Override public void prePare(CouponActivityContext promoCtx, IMobileContext iMobileContext) {
        try {
            if (!promoCtx.isNeedTopAdResourcePromotion()) {
                return;
            }
            Optional<Integer> boothIdOp = getBoothId(promoCtx);
            if(!boothIdOp.isPresent()) {
                return;
            }
            if (promoCtx.isMt() && promoCtx.getMtUserId() <= 0) {
                return;
            }
            //点评侧由于前端未打通top手动发券，点评未验证，先关闭扣子
            if(!promoCtx.isMt()) {
                return;
            }
            if (!promoCtx.isMt() && promoCtx.getDpUserId() <= 0) {
                return;
            }
            DotUtils.dotForPromoProcessor(promoCtx, getPromoName());
            Future<TopAdResourcePromotionDO> future = ExecutorService.submit(new Callable<TopAdResourcePromotionDO>() {
                @Override
                public TopAdResourcePromotionDO call() throws Exception {
                   return callTopAdResource(promoCtx, iMobileContext, boothIdOp.get());
                }
            });
            promoCtx.setTopAdResourceExposureFuture(future);
        } catch (Exception e) {
            log.error("TopAdResourcePromotionProcessor# prepare error. ctx: {}", promoCtx, e);
        }
    }

    @Override
    public void loadPromo(CouponActivityContext promoCtx) {
        try {
            Future<TopAdResourcePromotionDO> topAdResourceExposureFuture = promoCtx.getTopAdResourceExposureFuture();
            if (topAdResourceExposureFuture != null) {
                promoCtx.setTopAdResourcePromotionDO(topAdResourceExposureFuture.get(500, TimeUnit.MILLISECONDS));
            }
        } catch (Exception e) {
            log.error("TopAdResourcePromotionProcessor# loadPromo error. ctx: {}", promoCtx, e);
        }
    }

    @Override
    public String getPromoName() {
        Cat.logEvent("INVALID_METHOD_5", "com.dianping.pay.api.biz.activity.newloader.promo.TopAdResourcePromotionProcessor.getPromoName()");
        return "topAdResourcePromo";
    }

    private TopAdResourcePromotionDO callTopAdResource(CouponActivityContext promoCtx, IMobileContext iMobileContext, Integer boothId) {
        try {
            TopAdRequest request = buildTopAdRequest(promoCtx, iMobileContext, boothId);
            log.info("callTopAdResource request: {}", JsonUtils.toJson(request));
            TopAdResponse response = topAdThriftService.loadAdFromTOP(request);
            TopAdResourcePromotionDO topAdResourcePromotionDO = buildTopAdResourcePromotionDO(response);
            log.info("callTopAdResource response: {}, topAdResourcePromotionDO:{}", JsonUtils.toJson(response), JsonUtils.toJson(topAdResourcePromotionDO));
            return topAdResourcePromotionDO;
        } catch(Exception e) {
            log.error("callTopAdResource error,  ctx: {}", promoCtx, e);
        }
        return null;
    }

    private TopAdRequest buildTopAdRequest(CouponActivityContext promoCtx, IMobileContext iMobileContext, Integer boothId) {
        TopAdRequest request = new TopAdRequest();
        //美团侧传美团实userid，点评侧传美团虚userid，未登录传-1（未登录用户发券活动无法发券）
        request.setUserId(buildUserId(promoCtx.getMtUserId(), promoCtx.getDpUserId(), promoCtx.isMt()));
        //展位(广告位)的 id，注意不同环境id可能不一样
        request.setBoothId(boothId);
        //投放平台 1-美团app 4-点评app 5-i版(小程序) 6-美团旅行app 7-美团门票商家app
        request.setPlatform(buildPlatform(promoCtx.getCouponPlatform(), promoCtx.isMt()));
        //业务线：19:美团到综 20:点评到综
        request.setBiz(buildBiz(promoCtx.isMt()));
        request.setAppVersion(buildAppVersion(promoCtx.getPayPlatform(), iMobileContext.getVersion()));
        //美团城市id！！！如果是点评城市id需要转一下
        request.setCityId(buildCityId(promoCtx.getCityId(), promoCtx.isMt()));
        request.setUuid(promoCtx.getDpid());
        //点评侧必传, 点评用户实id，用于选人过滤和发券，未登录和美团侧传-1
        if(!promoCtx.isMt() && promoCtx.getDpUserId() > 0) {
            request.setDpUserId(promoCtx.getDpUserId());
        }
        //不知道填-1， 用户选择的地标id，比如地铁站还是高校等的id，这个id可能来自于group-geo等，
        request.setGisItemId(UNKNOWN_ID);
        //不知道填-1，上面的gisItemId的类型 1=商圈 2=机场 3=火车站 4=地铁站 5=高校 6=热门景点 7=医院 8=商场 9=考点
        request.setGisItemType(UNKNOWN_ID);
        //不知道填-1
        request.setLbsDataSource(UNKNOWN_ID);
        //纬度，无法获取就置空,建议传过来，如果要接点评广告这个必传
        if(promoCtx.getLatitude() != null) {
            request.setLatitude(promoCtx.getLatitude());
        }
        //经度，无法获取就置空,建议传过来，如果要接点评广告这个必传
        if(promoCtx.getLongitude() != null) {
            request.setLongitude(promoCtx.getLongitude());
        }

        //跟广告沟通，点评传点评，美团传美团
        long shopId = promoCtx.isMt() ? promoCtx.getMtShopIdL() : promoCtx.getDpShopIdL();
        if(shopId > 0) {
            request.setPropertyIdLong(shopId);
            request.setProductPropertyType(POI_PRODUCT_PROPERTY_TYPE);
        }
        return request;
    }

    private TopAdResourcePromotionDO buildTopAdResourcePromotionDO(TopAdResponse response) {
        TopAdResourcePromotionDO topAdResourcePromotionDO = new TopAdResourcePromotionDO();
        //只返回需要手动领取（autoType控制） + 已经领取成功的券
        if(response == null
            || CollectionUtils.isEmpty(response.getAdvertisementList())
            || response.getAdvertisementList().get(0).getVoucherResult() == null
            || response.getAdvertisementList().get(0).getVoucherResult().getAutoType() != 0
            || CollectionUtils.isEmpty(response.getAdvertisementList().get(0).getVoucherResult().getVoucherDetails())) {
            return null;
        }
        VoucherResult voucherResult = response.getAdvertisementList().get(0).getVoucherResult();
        topAdResourcePromotionDO.setAdId(Long.valueOf(response.getAdvertisementList().get(0).getAdId()));
        topAdResourcePromotionDO.setVoucherAutoType(voucherResult.getAutoType());
        List<VoucherDetailDO> voucherDetailDOList = Lists.newArrayList();
        for(VoucherDetail voucherDetail : response.getAdvertisementList().get(0).getVoucherResult().getVoucherDetails()) {
            //只关注已经领取成功的券 + 普通券/折扣券
            //TODO 测试用（201后续去掉，方便测试验证）
            if(voucherDetail.getStatusCode() == 422) {
                if(voucherDetail.getVoucherUseType() == 0 || voucherDetail.getVoucherUseType() == 2) {
                    VoucherDetailDO voucherDetailDO = new VoucherDetailDO();
                    voucherDetailDO.setIssueStatus(1);
                    voucherDetailDO.setVoucherValueType(voucherDetail.getVoucherUseType());
                    if(voucherDetail.getVoucherUseType() == 0) {
                        voucherDetailDO.setPriceAmount(voucherDetail.getValue());
                    } else {
                        //8折使用80
                        voucherDetailDO.setDiscount(voucherDetail.getValue());
                    }
                    voucherDetailDO.setPriceLimit(voucherDetail.getMinmoney());
                    voucherDetailDO.setUseBeginTime(voucherDetail.getUseStartTime() * 1000);
                    voucherDetailDO.setUseEndTime(voucherDetail.getUseEndTime() * 1000);
                    voucherDetailDO.setTitle(voucherDetail.getTitle());
                    voucherDetailDO.setCouponId(String.valueOf(voucherDetail.getCode()));
                    voucherDetailDOList.add(voucherDetailDO);
                }
            }
        }
        if(CollectionUtils.isEmpty(voucherDetailDOList)) {
            return null;
        }
        topAdResourcePromotionDO.setVoucherDetailDOList(voucherDetailDOList);
        return topAdResourcePromotionDO;
    }

    private Optional<Integer> getBoothId(CouponActivityContext promoCtx) {
        try {
            Map<String, BoothId> boothMap = Lion.getMap(LionConstants.APP_KEY, LionConstants.TOP_AD_RESOURCE_BOOTH_ID_CONFIG_BY_BIZ, BoothId.class);
            String couponPageSource = promoCtx.getCouponPageSource();
            // 兼容之前休娱poi场景 没有传couponPageSource
            if (StringUtils.isBlank(couponPageSource)) {
                couponPageSource = CouponPageSourceEnum.BEAUTY_POI_DETAIL.name();
            }
            BoothId boothId = boothMap.get(couponPageSource);
            if (boothId == null) {
                return Optional.empty();
            }
            if (promoCtx.isMt()) {
                return Optional.of(boothId.getMt());
            } else {
                return Optional.of(boothId.getDp());
            }
        } catch (Exception e) {
            log.error("ShopResourcePromotionProcessor getLocationId ex, promoCtx:{}", promoCtx, e);
            return Optional.empty();
        }
    }

    private Long buildUserId(long mtUserId, long dpUserId, boolean isMt) {
        if(isMt) {
            if(mtUserId > 0) {
                return mtUserId;
            }
        }
        if(dpUserId > 0) {
            long virtualMtUserId = userAccountBiz.getVirtualMtUserIdByDpUserId(dpUserId);
            if(virtualMtUserId > 0) {
                return virtualMtUserId;
            }
        }
        return Long.valueOf(UNKNOWN_ID);
    }

    private int buildPlatform(int couponPlatform, boolean isMt) {
        if(isMt) {
            if(couponPlatform == MtCouponPlatform.APP.getCode()) {
                return MT_APP;
            }
        } else {
            if(couponPlatform == PayPlatform.dp_iphone_native.getCode() || couponPlatform == PayPlatform.dp_android_native.getCode()) {
                return DP_APP;
            }
        }
        return UNKNOWN_ID;
    }

    private int buildBiz(boolean isMt) {
        if(isMt) {
            return MT_DZ_BIZ;
        } else {
            return DP_DZ_BIZ;
        }
    }

    private AppVersion buildAppVersion(int payPlatformCode, String version) {
        PayPlatform payPlatform = PayPlatform.getByCode(payPlatformCode);
        //1-iOS,2-Android, 999=i版
        int os = I_OS;
        if(payPlatform != null) {
            if (payPlatform == PayPlatform.dp_iphone_native || payPlatform == PayPlatform.mt_iphone_native) {
                os = IOS_OS;
            }
            if (payPlatform == PayPlatform.dp_android_native || payPlatform == PayPlatform.mt_android_native) {
                os = ANDROID_OS;
            }
        }
        AppVersion appVersion = new AppVersion();
        appVersion.setOs(os);
        //TODO 联调确认是否正确
        appVersion.setVersion(version);
        appVersion.setVersionName(version);
        return appVersion;
    }

    private Integer buildCityId(int cityId, boolean isMt) {
        if(!isMt) {
            return cityBiz.transferDpCity2MtCity(cityId);
        }
        return cityId;
    }
}
