package com.dianping.pay.api.biz.activity.newloader.mapper;

import com.dianping.api.constans.HeaderConstants;
import com.dianping.api.picasso.CouponPlatformUtils;
import com.dianping.cat.Cat;
import com.dianping.lion.client.Lion;
import com.dianping.mobile.framework.datatypes.IMobileContext;
import com.dianping.mobile.framework.datatypes.token.UserStatusResult;
import com.dianping.pay.api.beans.IssueCouponActivity;
import com.dianping.pay.api.biz.activity.newloader.dto.CouponIssueActivityQueryContext;
import com.dianping.pay.api.biz.activity.newloader.dto.PromotionRequestContext;
import com.dianping.pay.api.biz.activity.newloader.dto.RestUserInfo;
import com.dianping.pay.api.entity.issuecoupon.*;
import com.dianping.pay.api.enums.*;
import com.dianping.pay.api.util.*;
import com.dianping.pay.api.util.ClientTypeUtils;
import com.dianping.pay.api.util.IssueCouponTypeUtils;
import com.dianping.pay.api.util.IssueCouponUtils;
import com.dianping.pay.api.util.LionConstants;
import com.dianping.pay.promo.common.enums.User;
import com.dianping.tgc.open.entity.ActivityDTO;
import com.dianping.unified.coupon.manage.api.dto.UnifiedCouponDTO;
import com.dianping.unified.coupon.manage.api.dto.UnifiedCouponGroupDTO;
import com.fasterxml.jackson.core.type.TypeReference;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import com.meituan.mars.common.constant.Conversions;
import com.meituan.mars.common.domain.Coordinate;
import com.meituan.mars.common.util.CoordinateUtil;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import java.io.UnsupportedEncodingException;
import java.math.BigDecimal;
import java.net.URLDecoder;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;

/**
 * Created by drintu on 18/6/7.
 */

public class IssueActivityMapper {
    private static final Logger logger = LogManager.getLogger(IssueActivityMapper.class);

    public static List<IssueCouponActivity> activities2IssueActivities(Map<Integer, List<ActivityDTO>> activityMap, boolean isDpClient) {
        List<ActivityDTO> activityDTOs = Lists.newArrayList();
        Set<Integer> activityIdSet = Sets.newHashSet();
        for (List<ActivityDTO> activityDTOList : activityMap.values()) {
            for (ActivityDTO activityDTO : activityDTOList) {
                if (activityIdSet.add(activityDTO.getActivityID())) {
                    activityDTOs.add(activityDTO);
                }
            }
        }
        return activities2IssueActivities(activityDTOs, isDpClient);
    }

    public static List<IssueCouponActivity> activities2IssueActivitiesL(Map<Long, List<ActivityDTO>> activityMap, boolean isDpClient) {
        List<ActivityDTO> activityDTOs = Lists.newArrayList();
        Set<Integer> activityIdSet = Sets.newHashSet();
        for (List<ActivityDTO> activityDTOList : activityMap.values()) {
            for (ActivityDTO activityDTO : activityDTOList) {
                if (activityIdSet.add(activityDTO.getActivityID())) {
                    activityDTOs.add(activityDTO);
                }
            }
        }
        return activities2IssueActivities(activityDTOs, isDpClient);
    }

    public static List<IssueCouponActivity> activities2IssueActivities(List<ActivityDTO> activityDTOs, boolean isDpClient) {

        List<IssueCouponActivity> resultList = Lists.newArrayList();
        for (ActivityDTO activityDTO : activityDTOs) {
            try {
                if (activityDTO.getCouponDTO().isUpDailyLimit()) {
                    continue;
                }
                resultList.add(new IssueCouponActivity(
                        activityDTO,
                        isDpClient ? IssueCouponOptionType.DP_SHOP_EVENT.getCode() : IssueCouponOptionType.MT_SHOP_EVENT.getCode()
                ));
            } catch (IllegalArgumentException e) {
                logger.warn(String.format("activities2IssueActivities IllegalArgumentException,e[%s],activityDTO:[%s]", e.getMessage(), activityDTO));
            }
        }
        return resultList;
    }

    public static CouponIssueActivityQueryContext unifiedIssueComponentRequest2QueryActivityContext(UnifiedissuecouponcomponentRequest request, IMobileContext iMobileContext) {
        CouponIssueActivityQueryContext context = new CouponIssueActivityQueryContext();
        if (iMobileContext.isMtClient() && iMobileContext.getUserStatus() != null) {
            context.setUserId(iMobileContext.getUserStatus().getMtUserId());
        } else {
            context.setUserId(iMobileContext.getUserId());
        }
        context.setDpClient(!iMobileContext.isMtClient());
        context.setProductId(request.getProductid() == null ? 0 : request.getProductid());
        context.setShopIdL(request.getShopidL() == null ? 0 : request.getShopidL());
        context.setProductType(request.getProducttype() == null ? 0 : request.getProducttype());
        context.setIssueChannel(request.getIssueChannel() == null ? 0 : request.getIssueChannel());
        context.setBusinessLine(request.getBusinessLine() == null ? 0 : request.getBusinessLine());
        context.setMobileNo(request.getMobileNo());
        if (iMobileContext.getHeader() != null) {
            context.setDpid(iMobileContext.getHeader().getDpid());
            context.setUuid(iMobileContext.getHeader().getUuid());
        }
        return context;
    }

    public static PromotionRequestContext buildPromotionRequestContext(PromotionDisplayActionRequest request, IMobileContext iMobileContext) {
        PromotionRequestContext context = new PromotionRequestContext();
        UserStatusResult userStatus = iMobileContext.getUserStatus();

        if (request.getUsertype() != null && request.getUsertype() == User.MT.getCode()) {
            context.setUserType(User.MT);
            context.setMtShopIdL(request.getShopIdL());
            if (userStatus != null) {
                context.setMtUserId(userStatus.getMtUserId());
            }
        } else {
            context.setUserType(User.DP);
            context.setDpShopIdL(request.getShopIdL());
            if (userStatus != null) {
                context.setDpUserId(userStatus.getUserId());
            }
        }
        context.setNeedReturnPromotion(true);
        context.setQueryShopCouponType(QueryShopCouponTypeEnum.BY_SHOP.getCode());
        return context;
    }

    public static PromotionRequestContext buildPromotionRequestContext(IssuecouponcomponentRequest request, IMobileContext iMobileContext) {
        PromotionRequestContext context = new PromotionRequestContext();
        UserStatusResult userStatus = iMobileContext.getUserStatus();

        if (request.getUsertype() != null && request.getUsertype() == User.MT.getCode()) {
            context.setUserType(User.MT);
            context.setMtShopIdL(request.getShopIdL());
            if (userStatus != null) {
                context.setMtUserId(userStatus.getMtUserId());
            }
        } else {
            context.setUserType(User.DP);
            context.setDpShopIdL(request.getShopIdL());
            if (userStatus != null) {
                context.setDpUserId(userStatus.getUserId());
            }
        }
        //点评端直接取header.dpId
        if (!iMobileContext.isMtClient()) {
            context.setDpid(iMobileContext.getHeader().getDpid());
        }else {
            context.setDpid(request.getDpid());
        }
        if (StringUtils.isBlank(context.getDpid()) && iMobileContext.isMtClient()) {
            context.setDpid(iMobileContext.getHeader().getUuid());
        }

        context.setNeedShareCouponPromotion(request.getNeedsharecouponpromotion() != null && request.getNeedsharecouponpromotion() == 1);
        context.setQueryShopCouponType(QueryShopCouponTypeEnum.BY_SHOP.getCode());
        context.setNeedDiscountCoupon(true);
        context.setNeedShopResourcesPromotion(request.getNeedshopresourcespromotion() != null && request.getNeedshopresourcespromotion() == 1);
        context.setNeedMerchantAccountPromotion(request.getNeedmerchantaccountcoupon() != null && request.getNeedmerchantaccountcoupon() == 1);
        context.setNeedTopAdResourcePromotion(request.getNeedtopadresourcepromotion() != null && request.getNeedtopadresourcepromotion() == 1);
        context.setNeedMagicalMemberCoupon(request.getNeedmagicalmembercoupon() != null && request.getNeedmagicalmembercoupon() == 1);
        context.setCx(request.getCx());
        context.setPageSource(request.getPagesource());
        context.setCouponPageSource(request.getCouponPageSource());
        context.setLatitude(request.getLatitude());
        context.setLongitude(request.getLongitude());
        if (request.getCityid() != null) {
            context.setCityId(request.getCityid());
        }
        if (request.getActualcityid() != null) {
            context.setActualCityId(request.getActualcityid());
        }
        if (request.getCplatform() != null) {
            context.setCPlatform(request.getCplatform());
        }
        if (StringUtils.isNotBlank(request.getMtfingerprint())) {
            context.setMtFingerprint(request.getMtfingerprint());
        }
        if (StringUtils.isNotBlank(request.getUuid())) {
            context.setUuid(request.getUuid());
        }
        if (iMobileContext.getRequest() != null) {
            context.setMtgsig(iMobileContext.getRequest().getHeader("mtgsig"));
            context.setRegionId(iMobileContext.getRequest().getParameter("wtt_region_id"));
        }
        context.setCouponPlatform(CouponPlatformUtils.getCouponPlatform(iMobileContext));
        context.setPayPlatform(CouponPlatformUtils.getPayPlatform(iMobileContext));
        context.setPoiPageSource(request.getPoiPageSource());
        context.setMiniProgramFlag(request.getMiniProgramFlag());
        context.setTrafficSource(request.getTrafficsource());
        transferMmcParam(request, context);
        return context;
    }

    public static PromotionRequestContext buildNativePromotionRequestContext(ProductcouponpromoRequest request, IMobileContext iMobileContext) {
        PromotionRequestContext context = new PromotionRequestContext();
        UserStatusResult userStatus = iMobileContext.getUserStatus();
        context.setLatitude(request.getLatitude());
        context.setCx(request.getCx());
        context.setLongitude(request.getLongitude());
        context.setBeautyDealNewType(request.getBeautyDealNewType());
        context.setPageSource(request.getPagesource());
        context.setCouponPageSource(request.getCouponPageSource());
        context.setNeedDiscountCoupon(request.getNeeddiscountcoupon());
        context.setTrafficSource(request.getTrafficsource());
        context.setBusiness(request.getBusiness());

        if (request.getCityid() != null) {
            context.setCityId(request.getCityid());
        }
        if (iMobileContext.isMtClient()) {
            context.setUserType(User.MT);
            if (request.getShopIdL() != null) {
                context.setMtShopIdL(request.getShopIdL());
            }
            if (userStatus != null) {
                context.setMtUserId(userStatus.getMtUserId());
            }
            context.setDpid(iMobileContext.getHeader().getUuid());
        } else {
            context.setUserType(User.DP);
            if (request.getShopIdL() != null) {
                context.setDpShopIdL(request.getShopIdL());
            }
            if (userStatus != null) {
                context.setDpUserId(userStatus.getUserId());
            }
            context.setDpid(iMobileContext.getHeader().getDpid());
        }
        if (StringUtils.isNotBlank(request.getDpid())) {
            context.setDpid(request.getDpid());
        }

        context.setCouponPlatform(CouponPlatformUtils.getCouponPlatform(iMobileContext));
        context.setPayPlatform(CouponPlatformUtils.getPayPlatform(iMobileContext));
        if (request.getProducttype() == 0) {
            context.setQueryShopCouponType(QueryShopCouponTypeEnum.BY_DEAL.getCode());
            if (iMobileContext.isMtClient()) {
                if (request.getProductid() != null) {
                    context.setMtDealGroupId((request.getProductid().intValue()));
                }
            } else {
                if (request.getProductid() != null) {
                    context.setDpDealGroupId(request.getProductid().intValue());
                }
            }
            // 目前只有团购场景会有返礼 & 立减
            context.setNeedReturnPromotion(true);
            context.setNeedReductionPromotion(true);
            context.setNeedMemberPromotion(true);
            context.setNeedFinalcialCouponPromotion(true);
        } else if (request.getProducttype() == 1) {
            context.setQueryShopCouponType(QueryShopCouponTypeEnum.BY_SKU.getCode());
            if (request.getProductid() != null) {
                context.setSkuId(request.getProductid().intValue());
            }
            context.setNeedReductionPromotion(true);
        } else if (request.getProducttype() == 4) {
            context.setQueryShopCouponType(QueryShopCouponTypeEnum.BY_SPU.getCode());
            if (request.getProductid() != null) {
                context.setSpuGroupId(request.getProductid());
            }
        } else {
            context.setQueryShopCouponType(QueryShopCouponTypeEnum.BY_SPU_PRODUCT.getCode());
            context.setNeedReductionPromotion(true);
            if (request.getProductid() != null) {
                context.setSpuGroupId(request.getProductid());
            }
        }
        if (request.getProducttype() != 4) {
            context.setNeedResourcesPromotion(true);
        }
        return context;
    }

    public static PromotionRequestContext buildPromotionRequestContext(ExternalLinkIssueCouponRequest request, IMobileContext iMobileContext) {
        Cat.logEvent("INVALID_METHOD_5", "com.dianping.pay.api.biz.activity.newloader.mapper.IssueActivityMapper.buildPromotionRequestContext(ExternalLinkIssueCouponRequest,IMobileContext)");
        PromotionRequestContext context = new PromotionRequestContext();
        UserStatusResult userStatus = iMobileContext.getUserStatus();

        if(request.getUsercode() != null && request.getUsercode()==User.MT.getCode()){
            context.setUserType(User.MT);
            if(userStatus != null){
                context.setMtUserId(userStatus.getMtUserId());
            }
        }else{
            context.setUserType(User.DP);
            if(userStatus != null){
                context.setDpUserId(userStatus.getUserId());
            }
        }
        return context;
    }

    public static int parsePlatform(IMobileContext iMobileContext,  ProductcouponpromoRequest request) {
        String mpSource = iMobileContext.getRequest().getHeader("platform");
        if ("Android".equals(mpSource)) {
            return CouponPlatformTypeEnum.ANDROID.getCode();
        } else if  ("iPhone".equals(mpSource)) {
            return CouponPlatformTypeEnum.IPHONE.getCode();
        }
        return request.getPlatform() == null ? CouponPlatformTypeEnum.ANDROID.getCode() : request.getPlatform();
    }

    public static int parsePlatform(IMobileContext iMobileContext,  ChannelProductCouponPromoRequest request) {
        String mpSource = iMobileContext.getRequest().getHeader("platform");
        if ("Android".equals(mpSource)) {
            return CouponPlatformTypeEnum.ANDROID.getCode();
        } else if  ("iPhone".equals(mpSource)) {
            return CouponPlatformTypeEnum.IPHONE.getCode();
        }
        return request.getPlatform() == null ? CouponPlatformTypeEnum.ANDROID.getCode() : request.getPlatform();
    }


    public static int parseClientType(IMobileContext iMobileContext, ProductcouponpromoRequest request) {
        String mpSource = iMobileContext.getRequest().getHeader("mpSource");
        if ("wechat".equals(mpSource)) {
            return CouponClientTypeEnum.MINIPROGRAM.getCode();
        }
        return request.getClienttype() == null ? CouponClientTypeEnum.NATIVE.getCode() : request.getClienttype();
    }

    public static int parseClientType(IMobileContext iMobileContext, ChannelProductCouponPromoRequest request) {
        String mpSource = iMobileContext.getRequest().getHeader("mpSource");
        if ("wechat".equals(mpSource)) {
            return CouponClientTypeEnum.MINIPROGRAM.getCode();
        }
        return request.getClienttype() == null ? CouponClientTypeEnum.NATIVE.getCode() : request.getClienttype();
    }



    public static PromotionRequestContext buildHttpPromotionRequestContext(ProductcouponpromoRequest request, IMobileContext iMobileContext, RestUserInfo restUserInfo) {
        PromotionRequestContext context = new PromotionRequestContext();
        boolean isDpClient = restUserInfo.getUserCode() != User.MT.getCode();
        context.setDpid(request.getDpid());
        if (StringUtils.isBlank(context.getDpid())) {
            if (iMobileContext.isMtClient()) {
                context.setDpid(iMobileContext.getHeader().getUuid());
            } else {
                context.setDpid(iMobileContext.getHeader().getDpid());
            }
        }
        context.setNeedDiscountCoupon(request.getNeeddiscountcoupon());
        context.setLatitude(request.getLatitude());
        context.setLongitude(request.getLongitude());
        context.setBeautyDealNewType(request.getBeautyDealNewType());
        context.setPageSource(request.getPagesource());
        context.setCouponPageSource(request.getCouponPageSource());
        context.setTrafficSource(request.getTrafficsource());
        context.setBusiness(request.getBusiness());
        if (request.getCityid() != null) {
            context.setCityId(request.getCityid());
        }
        context.setCx(request.getCx());
        if (!isDpClient) {
            context.setUserType(User.MT);
            if (request.getShopIdL() != null) {
                context.setMtShopIdL(request.getShopIdL());
            }
            context.setMtUserId(restUserInfo.getUserId());
        } else {
            context.setUserType(User.DP);
            if (request.getShopIdL() != null) {
                context.setDpShopIdL(request.getShopIdL());
            }
            context.setDpUserId(restUserInfo.getUserId());
        }

        int clientType = parseClientType(iMobileContext, request);
        int platform = parsePlatform(iMobileContext, request);

        context.setCouponPlatform(ClientTypeUtils.getCouponPlatform(clientType, platform, !isDpClient));
        context.setPayPlatform(ClientTypeUtils.getPayPlatform(clientType, platform, !isDpClient));

        if (request.getProducttype() == ProductTypeEnum.DEAL_GROUP.getCode()) {
            context.setQueryShopCouponType(QueryShopCouponTypeEnum.BY_DEAL.getCode());
            if (!isDpClient) {
                context.setMtDealGroupId(request.getProductid().intValue());
            } else {
                context.setDpDealGroupId(request.getProductid().intValue());
            }
            // 目前只有团购场景会有返礼 & 立减，不过团购也没有返礼
            if (clientType != CouponClientTypeEnum.MINIPROGRAM.getCode()) {
                context.setNeedReturnPromotion(true);
            }
            context.setNeedReductionPromotion(true);
            context.setNeedMemberPromotion(true);
        } else if (request.getProducttype() == ProductTypeEnum.SKU.getCode()) {
            context.setQueryShopCouponType(QueryShopCouponTypeEnum.BY_SKU.getCode());
            context.setSkuId(request.getProductid().intValue());
            context.setNeedReductionPromotion(true);
        } else if (request.getProducttype() == ProductTypeEnum.SPU.getCode()) {
            context.setQueryShopCouponType(QueryShopCouponTypeEnum.BY_SPU.getCode());
            context.setSpuGroupId(request.getProductid());
        } else {
            context.setQueryShopCouponType(QueryShopCouponTypeEnum.BY_SPU_PRODUCT.getCode());
            context.setNeedReductionPromotion(true);
            context.setSpuGroupId(request.getProductid());
        }
        if (clientType != CouponClientTypeEnum.MINIPROGRAM.getCode() && request.getProducttype() != ProductTypeEnum.SPU.getCode()) {
            context.setNeedResourcesPromotion(true);
        }
        return context;
    }

    public static PromotionRequestContext buildHttpPromotionRequestContext(ShopCartPromoDisplayActionRequest request, RestUserInfo restUserInfo, List<ShopCartBrandProduct> shopCartBrandProducts) {
        PromotionRequestContext context = new PromotionRequestContext();
        boolean isDpClient = restUserInfo.getUserCode() != User.MT.getCode();

        if (!isDpClient) {
            context.setUserType(User.MT);
            context.setMtUserId(restUserInfo.getUserId());
        } else {
            context.setUserType(User.DP);
            context.setDpUserId(restUserInfo.getUserId());
        }

        if (CollectionUtils.isNotEmpty(shopCartBrandProducts)) {
            List<Integer> skuIds = Lists.newArrayList();
            List<Integer> dpDealGroupIds = Lists.newArrayList();
            List<Integer> mtDealGroupIds = Lists.newArrayList();
            int queryShopCouponType = QueryShopCouponTypeEnum.BY_SKUS.getCode();
            for (ShopCartBrandProduct shopCartBrandProduct : shopCartBrandProducts) {
                if (CollectionUtils.isEmpty(shopCartBrandProduct.getShopcartproducts())) {
                    continue;
                }
                for (ShopCartProduct shopCartProduct : shopCartBrandProduct.getShopcartproducts()) {
                    if (shopCartProduct.getProducttype() == ProductTypeEnum.DEAL_GROUP.getCode()) {
                        if (isDpClient) {
                            dpDealGroupIds.add(shopCartProduct.getProductid());
                        } else {
                            mtDealGroupIds.add(shopCartProduct.getProductid());
                        }
                        queryShopCouponType = QueryShopCouponTypeEnum.BY_DEALS.getCode();
                    } else if (shopCartProduct.getProducttype() == ProductTypeEnum.SKU.getCode()) {
                        skuIds.add(shopCartProduct.getProductid());
                        queryShopCouponType = QueryShopCouponTypeEnum.BY_SKUS.getCode();
                    }
                }
            }
            context.setDpDealGroupIds(dpDealGroupIds);
            context.setMtDealGroupIds(mtDealGroupIds);
            context.setSkuIds(skuIds);
            context.setQueryShopCouponType(queryShopCouponType);
            context.setNeedUserPromotion(queryShopCouponType == QueryShopCouponTypeEnum.BY_SKUS.getCode());
        }
        return context;
    }

    public static PromotionRequestContext buildHttpCouponPromoRequestContext(ProductcouponpromoRequest request, IMobileContext iMobileContext, RestUserInfo restUserInfo) {
        PromotionRequestContext context = new PromotionRequestContext();
        boolean isDpClient = restUserInfo.getUserCode() != User.MT.getCode();
        context.setDpid(request.getDpid());
        if (StringUtils.isBlank(context.getDpid())) {
            if (iMobileContext.isMtClient()) {
                context.setDpid(iMobileContext.getHeader().getUuid());
            } else {
                context.setDpid(iMobileContext.getHeader().getDpid());
            }
        }
        context.setLatitude(request.getLatitude());
        context.setLongitude(request.getLongitude());
        if (request.getCityid() != null) {
            context.setCityId(request.getCityid());
        }
        context.setCx(request.getCx());
        if (!isDpClient) {
            context.setUserType(User.MT);
            if (request.getShopIdL() != null) {
                context.setMtShopIdL(request.getShopIdL());
            }
            context.setMtUserId(restUserInfo.getUserId());
        } else {
            context.setUserType(User.DP);
            if (request.getShopIdL() != null) {
                context.setDpShopIdL(request.getShopIdL());
            }
            context.setDpUserId(restUserInfo.getUserId());
        }

        int clientType = request.getClienttype() == null ? CouponClientTypeEnum.NATIVE.getCode() : request.getClienttype();
        int platform = request.getPlatform() == null ? CouponPlatformTypeEnum.ANDROID.getCode() : request.getPlatform();

        context.setCouponPlatform(ClientTypeUtils.getCouponPlatform(clientType, platform, !isDpClient));
        context.setPayPlatform(ClientTypeUtils.getPayPlatform(clientType, platform, !isDpClient));

        if (request.getProducttype() == 0) {
            context.setQueryShopCouponType(QueryShopCouponTypeEnum.BY_DEAL.getCode());
            if (!isDpClient) {
                context.setMtDealGroupId(request.getProductid().intValue());
            } else {
                context.setDpDealGroupId(request.getProductid().intValue());
            }
            context.setNeedReductionPromotion(true);
        } else if (request.getProducttype() == 1) {
            context.setQueryShopCouponType(QueryShopCouponTypeEnum.BY_SKU.getCode());
            context.setSkuId(request.getProductid().intValue());
        } else {
            context.setQueryShopCouponType(QueryShopCouponTypeEnum.BY_SPU_PRODUCT.getCode());
            context.setNeedReductionPromotion(true);
            context.setSpuGroupId(request.getProductid());
        }
        return context;
    }

    public static PromotionRequestContext buildNativePromotionRequestContext(ShopCartPromoDisplayActionRequest request, IMobileContext iMobileContext, List<ShopCartBrandProduct> shopCartBrandProducts) {
        PromotionRequestContext context = new PromotionRequestContext();
        UserStatusResult userStatus = iMobileContext.getUserStatus();
        if (iMobileContext.isMtClient()) {
            context.setUserType(User.MT);
            if (userStatus != null) {
                context.setMtUserId(userStatus.getMtUserId());
            }
            context.setDpid(iMobileContext.getHeader().getUuid());
        } else {
            context.setUserType(User.DP);
            if (userStatus != null) {
                context.setDpUserId(userStatus.getUserId());
            }
            context.setDpid(iMobileContext.getHeader().getDpid());
        }
        context.setPayPlatform(CouponPlatformUtils.getCouponPlatform(iMobileContext));
        int queryShopCouponType = QueryShopCouponTypeEnum.BY_SKUS.getCode();
        if (CollectionUtils.isNotEmpty(shopCartBrandProducts)) {
            List<Integer> skuIds = Lists.newArrayList();
            List<Integer> dpDealGroupIds = Lists.newArrayList();
            List<Integer> mtDealGroupIds = Lists.newArrayList();
            for (ShopCartBrandProduct shopCartBrandProduct : shopCartBrandProducts) {
                if (CollectionUtils.isEmpty(shopCartBrandProduct.getShopcartproducts())) {
                    continue;
                }
                for (ShopCartProduct shopCartProduct : shopCartBrandProduct.getShopcartproducts()) {
                    if (shopCartProduct.getProducttype() == ProductTypeEnum.DEAL_GROUP.getCode()) {
                        if (iMobileContext.isMtClient()) {
                            mtDealGroupIds.add(shopCartProduct.getProductid());
                        } else {
                            dpDealGroupIds.add(shopCartProduct.getProductid());
                        }
                        queryShopCouponType = QueryShopCouponTypeEnum.BY_DEALS.getCode();
                    } else if (shopCartProduct.getProducttype() == ProductTypeEnum.SKU.getCode()) {
                        skuIds.add(shopCartProduct.getProductid());
                        queryShopCouponType = QueryShopCouponTypeEnum.BY_SKUS.getCode();
                    }
                }
            }
            context.setDpDealGroupIds(dpDealGroupIds);
            context.setMtDealGroupIds(mtDealGroupIds);
            context.setSkuIds(skuIds);
            context.setQueryShopCouponType(queryShopCouponType);
            context.setNeedUserPromotion(queryShopCouponType == QueryShopCouponTypeEnum.BY_SKUS.getCode());
        }
        return context;
    }

    public static ShopCartIssueMultiCouponContext buildHttpPromotionRequestContext(ShopCartIssueMultiCouponRequest request, RestUserInfo restUserInfo, List<ShopCartProduct> shopCartProducts) {
        ShopCartIssueMultiCouponContext context = new ShopCartIssueMultiCouponContext();
        boolean isDpClient = restUserInfo.getUserCode() != User.MT.getCode();

        List<String> couponGroupIds = Lists.newArrayList(StringUtils.split(request.getUnifiedcoupongroupids(), ","));
        context.setCouponGroupIds(couponGroupIds);
        if (!isDpClient) {
            context.setUserType(User.MT);
            context.setMtUserId(restUserInfo.getUserId());
        } else {
            context.setUserType(User.DP);
            context.setDpUserId(restUserInfo.getUserId());
        }

        List<Integer> skuIds = Lists.newArrayList();
        List<Integer> dpDealGroupIds = Lists.newArrayList();
        List<Integer> mtDealGroupIds = Lists.newArrayList();
        for (ShopCartProduct shopCartProduct : shopCartProducts) {
            if (shopCartProduct.getProducttype() == ProductTypeEnum.DEAL_GROUP.getCode()) {
                if (isDpClient) {
                    dpDealGroupIds.add(shopCartProduct.getProductid());
                } else {
                    mtDealGroupIds.add(shopCartProduct.getProductid());
                }

            } else if (shopCartProduct.getProducttype() == ProductTypeEnum.SKU.getCode()) {
                skuIds.add(shopCartProduct.getProductid());
            }
        }
        context.setDpDealGroupIds(dpDealGroupIds);
        context.setMtDealGroupIds(mtDealGroupIds);
        context.setSkuIds(skuIds);
        context.setIssueDetailSourceCode(request.getIssuedetailsourcecode());
        return context;
    }

    public static NewIssueMultiCouponContext buildHttpPromotionRequestContext(NewBatchIssueCouponRequest request, RestUserInfo restUserInfo) {
        Cat.logEvent("INVALID_METHOD_2", "com.dianping.pay.api.biz.activity.newloader.mapper.IssueActivityMapper.buildHttpPromotionRequestContext(NewBatchIssueCouponRequest,RestUserInfo)");
        NewIssueMultiCouponContext context = new NewIssueMultiCouponContext();
        boolean isDpClient = restUserInfo.getUserCode() != User.MT.getCode();
        if (!isDpClient) {
            context.setUserType(User.MT);
            context.setMtUserId(restUserInfo.getUserId());
        } else {
            context.setUserType(User.DP);
            context.setDpUserId(restUserInfo.getUserId());
        }
        context.setIssueDetailSourceCode(request.getIssueDetailSourceCode());
        context.setNewIssueCouponUnitList(request.getNewIssueCouponUnitList());
        return context;
    }

    public static NewIssueMultiCouponContext buildNativePromotionRequestContext(NewBatchIssueCouponRequest request, IMobileContext iMobileContext) {
        NewIssueMultiCouponContext context = new NewIssueMultiCouponContext();
        UserStatusResult userStatus = iMobileContext.getUserStatus();
        if (iMobileContext.isMtClient()) {
            context.setUserType(User.MT);
            if (userStatus != null) {
                context.setMtUserId(userStatus.getMtUserId());
            }
        } else {
            context.setUserType(User.DP);
            if (userStatus != null) {
                context.setDpUserId(userStatus.getUserId());
            }
        }
        context.setNewIssueCouponUnitList(request.getNewIssueCouponUnitList());
        context.setIssueDetailSourceCode(request.getIssueDetailSourceCode());
        return context;
    }

    public static ShopCartIssueMultiCouponContext buildNativePromotionRequestContext(ShopCartIssueMultiCouponRequest request, IMobileContext iMobileContext, List<ShopCartProduct> shopCartProducts) {
        ShopCartIssueMultiCouponContext context = new ShopCartIssueMultiCouponContext();
        UserStatusResult userStatus = iMobileContext.getUserStatus();

        List<String> couponGroupIds = Lists.newArrayList(StringUtils.split(request.getUnifiedcoupongroupids(), ","));
        context.setCouponGroupIds(couponGroupIds);
        if (iMobileContext.isMtClient()) {
            context.setUserType(User.MT);
            if (userStatus != null) {
                context.setMtUserId(userStatus.getMtUserId());
            }
            context.setDpid(iMobileContext.getHeader().getUuid());
        } else {
            context.setUserType(User.DP);
            if (userStatus != null) {
                context.setDpUserId(userStatus.getUserId());
            }
            context.setDpid(iMobileContext.getHeader().getDpid());
        }
        context.setPayPlatform(CouponPlatformUtils.getCouponPlatform(iMobileContext));
        List<Integer> skuIds = Lists.newArrayList();
        List<Integer> dpDealGroupIds = Lists.newArrayList();
        List<Integer> mtDealGroupIds = Lists.newArrayList();

        for (ShopCartProduct shopCartProduct : shopCartProducts) {
            if (shopCartProduct.getProducttype() == ProductTypeEnum.DEAL_GROUP.getCode()) {
                if (iMobileContext.isMtClient()) {
                    mtDealGroupIds.add(shopCartProduct.getProductid());
                } else {
                    dpDealGroupIds.add(shopCartProduct.getProductid());
                }
            } else if (shopCartProduct.getProducttype() == ProductTypeEnum.SKU.getCode()) {
                skuIds.add(shopCartProduct.getProductid());
            }
        }

        context.setDpDealGroupIds(dpDealGroupIds);
        context.setMtDealGroupIds(mtDealGroupIds);
        context.setSkuIds(skuIds);
        context.setIssueDetailSourceCode(request.getIssuedetailsourcecode());
        return context;
    }


    public static PromotionRequestContext buildHttpPromotionRequestContext(BrandPromoDisplayActionRequest request, RestUserInfo restUserInfo) {
        PromotionRequestContext context = new PromotionRequestContext();
        boolean isDpClient = restUserInfo.getUserCode() != User.MT.getCode();

        if (!isDpClient) {
            context.setUserType(User.MT);
            context.setMtUserId(restUserInfo.getUserId());
        } else {
            context.setUserType(User.DP);
            context.setDpUserId(restUserInfo.getUserId());
        }
        context.setBrandId(request.getBrandId());
        context.setNeedBrandPromotion(true);
        context.setQueryShopCouponType(-1);
        return context;
    }

    public static PromotionRequestContext buildNativePromotionRequestContext(BrandPromoDisplayActionRequest request, IMobileContext iMobileContext) {
        Cat.logEvent("INVALID_METHOD_3", "com.dianping.pay.api.biz.activity.newloader.mapper.IssueActivityMapper.buildNativePromotionRequestContext(BrandPromoDisplayActionRequest,IMobileContext)");
        PromotionRequestContext context = new PromotionRequestContext();
        UserStatusResult userStatus = iMobileContext.getUserStatus();
        if (iMobileContext.isMtClient()) {
            context.setUserType(User.MT);
            if (userStatus != null) {
                context.setMtUserId(userStatus.getMtUserId());
            }
            context.setDpid(iMobileContext.getHeader().getUuid());
        } else {
            context.setUserType(User.DP);
            if (userStatus != null) {
                context.setDpUserId(userStatus.getUserId());
            }
            context.setDpid(iMobileContext.getHeader().getDpid());
        }
        context.setBrandId(request.getBrandId());
        context.setNeedBrandPromotion(true);
        context.setQueryShopCouponType(-1);
        return context;
    }

    public static CouponDetailInfo genCouponDetailItem(IssueCouponActivity issueCouponActivity) {
        CouponDetailInfo item = new CouponDetailInfo();
        UnifiedCouponGroupDTO couponGroup = issueCouponActivity.getCouponGroup();
        String couponGroupId = StringUtils.isNotBlank(couponGroup.getUnifiedCouponGroupId()) ? couponGroup.getUnifiedCouponGroupId() : String.valueOf(issueCouponActivity.getCouponGroupId());
        item.setCouponGroupId(couponGroupId);
        item.setAmount(couponGroup.getDiscountAmount().stripTrailingZeros().toPlainString());
        item.setDiscountAmount(couponGroup.getDiscountAmount().multiply(new BigDecimal(100)));
        item.setPriceLimit(couponGroup.getPriceLimit().multiply(new BigDecimal(100)));

        if (!IssueCouponTypeUtils.isIssued(issueCouponActivity)) {
            item.setCanAssign(true);
            item.setCouponSuitDesc(IssueCouponUtils.formatCouponTimePeriod(couponGroup));
        } else {
            item.setCanAssign(false);
            UnifiedCouponDTO unifiedCouponDTO = IssueCouponTypeUtils.getIssuedUnifiedCouponDto(issueCouponActivity);
            item.setCouponSuitDesc(IssueCouponUtils.formatTimePeriod(unifiedCouponDTO.getBeginTime(), unifiedCouponDTO.getEndTime()));
        }

        int merchantProductType = IssueCouponUtils.getMerchantCouponProductType(couponGroup);
        item.setTitle(IssueCouponUtils.getMerchantCouponTitle(merchantProductType));

        if (IssueCouponUtils.isBrandNewUserCoupon(couponGroup)) {
            item.setFreshExclusive(true);
            item.setCornerMarkType(CornerMarkTypeEnum.NEW_USER_MARK.getCode());
            item.setMerchantUserType(MerchantUserTypeEnum.BRAND_NEW_USER.getCode());
        } else if (IssueCouponUtils.isProductNewUserCoupon(couponGroup)) {
            item.setFreshExclusive(true);
            item.setCornerMarkType(CornerMarkTypeEnum.OTHER_MARK.getCode());
            item.setCornerMarkUrl(Lion.getString(LionConstants.APP_KEY, LionConstants.PRODUCT_NEW_USER_CORNER_MARK_URL));
            item.setMerchantUserType(MerchantUserTypeEnum.PRODUCT_NEW_USER.getCode());
        } else {
            item.setCornerMarkType(CornerMarkTypeEnum.NON_MARK.getCode());
            item.setMerchantUserType(MerchantUserTypeEnum.ALL_USER.getCode());
        }
        item.setCouponThresholdDesc(IssueCouponUtils.formatTitle(couponGroup));
        item.setCouponSrc(1);
        item.setMerchantProductType(merchantProductType);
        item.setBizAndTimeLimitDesc(issueCouponActivity.getBizAndTimeLimitDesc());
        if (IssueCouponUtils.isDzTimeLimitRule(couponGroup)) {
            item.setDzTimeLimitRule(IssueCouponUtils.formatDzTimeLimitRule(couponGroup));
        }

        return item;
    }

    public static PromotionRequestContext buildHttpPromotionRequestContext(IssuecouponcomponentRequest request, RestUserInfo restUserInfo, IMobileContext iMobileContext) {
        PromotionRequestContext context = new PromotionRequestContext();
        if (restUserInfo != null) {
            if (restUserInfo.getUserCode() == User.MT.getCode()) {
                context.setUserType(User.MT);
                context.setMtShopIdL(request.getShopIdL());
                context.setMtUserId(restUserInfo.getUserId());
            } else {
                context.setUserType(User.DP);
                context.setDpShopIdL(request.getShopIdL());
                context.setDpUserId(restUserInfo.getUserId());
            }
        }

        context.setDpid(request.getDpid());
        if (StringUtils.isBlank(context.getDpid()) && iMobileContext != null) {
            if (iMobileContext.isMtClient()) {
                context.setDpid(iMobileContext.getHeader().getUuid());
            } else {
                context.setDpid(iMobileContext.getHeader().getDpid());
            }
        }

        context.setNeedShareCouponPromotion(request.getNeedsharecouponpromotion() != null && request.getNeedsharecouponpromotion() == 1);
        context.setQueryShopCouponType(QueryShopCouponTypeEnum.BY_SHOP.getCode());
        context.setNeedDiscountCoupon(true);
        context.setNeedResourcesPromotion(true);
        context.setNeedShopResourcesPromotion(request.getNeedshopresourcespromotion() != null && request.getNeedshopresourcespromotion() == 1);
        context.setNeedMerchantAccountPromotion(request.getNeedmerchantaccountcoupon() != null && request.getNeedmerchantaccountcoupon() == 1);
        context.setNeedTopAdResourcePromotion(request.getNeedtopadresourcepromotion() != null && request.getNeedtopadresourcepromotion() == 1);
        context.setNeedMagicalMemberCoupon(request.getNeedmagicalmembercoupon() != null && request.getNeedmagicalmembercoupon() == 1);
        context.setCx(request.getCx());
        context.setPageSource(request.getPagesource());
        context.setCouponPageSource(request.getCouponPageSource());
        if (request.getCityid() != null) {
            context.setCityId(request.getCityid());
        }
        if (request.getActualcityid() != null) {
            context.setActualCityId(request.getActualcityid());
        }
        if (request.getCplatform() != null) {
            context.setCPlatform(request.getCplatform());
        }
        if (StringUtils.isNotBlank(request.getMtfingerprint())) {
           context.setMtFingerprint(request.getMtfingerprint());
        }
        if (StringUtils.isNotBlank(request.getUuid())) {
            context.setUuid(request.getUuid());
        }
        if (iMobileContext != null && iMobileContext.getRequest() != null) {
            context.setMtgsig(iMobileContext.getRequest().getHeader("mtgsig"));
            context.setRegionId(iMobileContext.getRequest().getParameter("wtt_region_id"));
        }

        context.setCouponPlatform(CouponPlatformUtils.getCouponPlatform(iMobileContext));
        context.setPayPlatform(CouponPlatformUtils.getPayPlatform(iMobileContext));
        context.setPoiPageSource(request.getPoiPageSource());
        context.setMiniProgramFlag(request.getMiniProgramFlag());

        context.setVersion(request.getAppversion());
        context.setAppId(request.getAppid());
        context.setCposition(request.getCposition());
        Pair<Double, Double> userLocation = convertLocation(request.getGpsCoord(), request.getLatitude(), request.getLongitude());
        context.setLatitude(userLocation.getLeft());
        context.setLongitude(userLocation.getRight());

        // get parameters from header
        if (request.getMiniProgramFlag() != null && request.getMiniProgramFlag() == 1) {
            if (StringUtils.isBlank(context.getUuid())) {
                context.setUuid(iMobileContext.getRequest().getHeader(HeaderConstants.UUID));
            }
            if (StringUtils.isBlank(context.getAppId())) {
                context.setAppId(iMobileContext.getRequest().getHeader(HeaderConstants.APP_ID));
            }
            context.setOpenId(iMobileContext.getRequest().getHeader(HeaderConstants.OPEN_ID));
            if (StringUtils.isBlank(context.getVersion())) {
                context.setVersion(iMobileContext.getParameter(HeaderConstants.APP_VERSION));
            }
        }
        context.setTrafficSource(request.getTrafficsource());
        transferMmcParam(request, context);

        return context;
    }

    public static PromotionRequestContext buildHttpPromotionRequestContext(ChannelProductCouponPromoRequest request, IMobileContext iMobileContext, RestUserInfo restUserInfo) {
        PromotionRequestContext context = new PromotionRequestContext();
        boolean isDpClient = restUserInfo.getUserCode() != User.MT.getCode();
        context.setDpid(request.getDpid());
        if (StringUtils.isBlank(context.getDpid())) {
            if (iMobileContext.isMtClient()) {
                context.setDpid(iMobileContext.getHeader().getUuid());
            } else {
                context.setDpid(iMobileContext.getHeader().getDpid());
            }
        }
        if (StringUtils.isBlank(context.getDpid())) {
            //适配微信小程序
            context.setDpid(iMobileContext.getRequest().getHeader("openId"));
        }
        //适配微信小程序
        context.setVersion(iMobileContext.getRequest().getHeader("hostAppVersion"));
        if (StringUtils.isBlank(context.getVersion())) {
            context.setVersion(iMobileContext.getVersion());
        }
        if (request.getLatitude() != null) {
            context.setLatitude(request.getLatitude());
        }
        if (request.getLongitude() != null) {
            context.setLongitude(request.getLongitude());
        }
        context.setCityId(request.getCityid());
        context.setCx(request.getCx());
        if (!isDpClient) {
            context.setUserType(User.MT);
            if (request.getShopid() != null) {
                context.setMtShopIdL(request.getShopid());
            }
            context.setMtUserId(restUserInfo.getUserId());
        } else {
            context.setUserType(User.DP);
            if (request.getShopid() != null) {
                context.setDpShopIdL(request.getShopid());
            }
            context.setDpUserId(restUserInfo.getUserId());
        }

        int clientType = parseClientType(iMobileContext, request);
        int platform = parsePlatform(iMobileContext, request);
        context.setCouponPlatform(ClientTypeUtils.getCouponPlatform(clientType, platform, !isDpClient));
        context.setPayPlatform(ClientTypeUtils.getPayPlatform(clientType, platform, !isDpClient));

        context.setProductType(request.getProducttype());
        context.setQueryShopCouponType(-1);
        if (request.getProducttype() == ProductTypeEnum.DEAL_GROUP.getCode()) {
            if (!isDpClient) {
                context.setMtDealGroupId(request.getProductid().intValue());
            } else {
                context.setDpDealGroupId(request.getProductid().intValue());
            }
        } else if (request.getProducttype() == ProductTypeEnum.SKU.getCode()) {
            context.setSkuId(request.getProductid().intValue());
        } else {
            context.setSpuGroupId(request.getProductid());
        }
        context.setChannel(request.getChannel());
        Map<String, String> dealExtParam = null;
        try {
            dealExtParam = JsonUtils.toObject(URLDecoder.decode(request.getDealextparam(), "utf-8"), new TypeReference<Map<String, String>>(){});
        } catch (UnsupportedEncodingException e) {
            Cat.logEvent("ChannelProductPromoDisplayAction", "dealExtParamError");
            logger.error("ChannelProductPromoDisplayAction dealExtParam parse Error, request={}", JsonUtils.toJson(request), e);
        }
        if (MapUtils.isNotEmpty(dealExtParam)) {
            context.setExtParam(dealExtParam);
        }
        if(ChannelProductEnum.getByDesc(context.getChannel()) == ChannelProductEnum.ODP) {
            context.setNeedQueryPricePromotion(true);
        }
        return context;
    }

    public static PromotionRequestContext buildNativePromotionRequestContext(ChannelProductCouponPromoRequest request, IMobileContext iMobileContext) {
        Cat.logEvent("INVALID_METHOD_4", "com.dianping.pay.api.biz.activity.newloader.mapper.IssueActivityMapper.buildNativePromotionRequestContext(ChannelProductCouponPromoRequest,IMobileContext)");
        PromotionRequestContext context = new PromotionRequestContext();
        UserStatusResult userStatus = iMobileContext.getUserStatus();
        context.setCx(request.getCx());

        if (request.getLatitude() != null) {
            context.setLatitude(request.getLatitude());
        }
        if (request.getLongitude() != null) {
            context.setLongitude(request.getLongitude());
        }
        context.setCityId(request.getCityid());

        if (iMobileContext.isMtClient()) {
            context.setUserType(User.MT);
            if (request.getShopid() != null) {
                context.setMtShopIdL(request.getShopid());
            }
            if (userStatus != null) {
                context.setMtUserId(userStatus.getMtUserId());
            }
            context.setDpid(iMobileContext.getHeader().getUuid());
        } else {
            context.setUserType(User.DP);
            if (request.getShopid() != null) {
                context.setDpShopIdL(request.getShopid());
            }
            if (userStatus != null) {
                context.setDpUserId(userStatus.getUserId());
            }
            context.setDpid(iMobileContext.getHeader().getDpid());
        }
        if (StringUtils.isNotBlank(request.getDpid())) {
            context.setDpid(request.getDpid());
        }
        context.setVersion(iMobileContext.getVersion());

        context.setCouponPlatform(CouponPlatformUtils.getCouponPlatform(iMobileContext));
        context.setPayPlatform(CouponPlatformUtils.getPayPlatform(iMobileContext));

        context.setQueryShopCouponType(-1);
        context.setProductType(request.getProducttype());
        if (request.getProducttype() == ProductTypeEnum.DEAL_GROUP.getCode()) {
            if (iMobileContext.isMtClient()) {
                if (request.getProductid() != null) {
                    context.setMtDealGroupId((request.getProductid().intValue()));
                }
            } else {
                if (request.getProductid() != null) {
                    context.setDpDealGroupId(request.getProductid().intValue());
                }
            }
        } else if (request.getProducttype() == ProductTypeEnum.SKU.getCode()) {
            if (request.getProductid() != null) {
                context.setSkuId(request.getProductid().intValue());
            }
        } else {
            if (request.getProductid() != null) {
                context.setSpuGroupId(request.getProductid());
            }
        }
        context.setChannel(request.getChannel());
        Map<String, String> dealExtParam = null;
        try {
            dealExtParam = JsonUtils.toObject(URLDecoder.decode(request.getDealextparam(), "utf-8"), new TypeReference<Map<String, String>>(){});
        } catch (UnsupportedEncodingException e) {
            Cat.logEvent("ChannelProductPromoDisplayAction", "dealExtParamError");
            logger.error("ChannelProductPromoDisplayAction dealExtParam parse Error, request={}", JsonUtils.toJson(request), e);
        }
        if (MapUtils.isNotEmpty(dealExtParam)) {
            context.setExtParam(dealExtParam);
        }
        if(ChannelProductEnum.getByDesc(context.getChannel()) == ChannelProductEnum.ODP) {
            context.setNeedQueryPricePromotion(true);
        }
        return context;
    }

    public static Pair<Double, Double> convertLocation(Integer gpsCoord, Double latitude, Double longitude) {
        if (gpsCoord == null || latitude == null || longitude == null) {
            return Pair.of(latitude, longitude);
        }
        if (gpsCoord == GPSCoordEnum.GPS.getCode()) {
            Coordinate coordinate = CoordinateUtil.transform(latitude, longitude, Conversions.GPS2MARS);
            return Pair.of(coordinate.getLatitude(), coordinate.getLongitude());
        }

        return Pair.of(latitude, longitude);
    }

    /**
     * 透传神券参数，目前主要是优惠台使用
     */
    public static void transferMmcParam(IssuecouponcomponentRequest request, PromotionRequestContext context) {
        if (Objects.isNull(request) || Objects.isNull(context)) {
            return;
        }
        context.setMmcInflate(request.getMmcinflate());
        context.setMmcUse(request.getMmcuse());
        context.setMmcBuy(request.getMmcbuy());
        context.setMmcFree(request.getMmcfree());
    }
}
