package com.dianping.pay.api.biz.activity.newloader.unified.executor;

import com.dianping.deal.idmapper.api.DealIdMapperService;
import com.dianping.pay.api.beans.IssueCouponActivity;
import com.dianping.pay.api.biz.activity.newloader.AbstractUnifiedCouponActivityQueryExecutor;
import com.dianping.pay.api.biz.activity.newloader.dto.CouponIssueActivityQueryContext;
import com.dianping.pay.api.biz.activity.newloader.mapper.IssueActivityMapper;
import com.dianping.spc.common.Response;
import com.dianping.tgc.open.SkuActivityQueryRemoteService;
import com.dianping.tgc.open.entity.ActivityDTO;
import com.dianping.tgc.open.entity.ActivityQueryOption;
import com.dianping.tgc.open.entity.SkuActivityBatchQueryRequest;
import com.dianping.tgc.open.entity.SkuActivityQueryResponseDTO;
import com.google.common.collect.Lists;
import org.apache.commons.collections.MapUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * Created by drintu on 18/6/14.
 */

public class UnifiedSkuActivityQueryExecutor extends AbstractUnifiedCouponActivityQueryExecutor{
    private DealIdMapperService dealIdMapperService;
    private SkuActivityQueryRemoteService skuActivityQueryRemoteService;

    public UnifiedSkuActivityQueryExecutor(CouponIssueActivityQueryContext context) {
        super(context);
    }

    @Override
    protected List<IssueCouponActivity> loadFromRemote() {
        if (queryContext.getProductId() == 0) {
            return Lists.newArrayList();
        }
        SkuActivityBatchQueryRequest remoteRequest = new SkuActivityBatchQueryRequest();
        long skuid = queryContext.getProductId();
        remoteRequest.setSkuIds(Lists.newArrayList(skuid));
        if (queryContext.isDpClient()) {
            remoteRequest.setSource(1);
        } else {
            remoteRequest.setSource(2);
        }

        ActivityQueryOption queryOption = new ActivityQueryOption();
        queryOption.setFillDiscountCoupon(true);
        remoteRequest.setQueryOption(queryOption);

        Response<SkuActivityQueryResponseDTO> response = skuActivityQueryRemoteService.queryActivityDTOsBySkuIds(remoteRequest);
        if (!response.isSuccess() || response.getResult() == null) {
            return Lists.newArrayList();
        }
        Map<Long, List<ActivityDTO>> skuActivityMap = response.getResult().getSkuActivityMap();
        if (MapUtils.isEmpty(skuActivityMap)) {
            return Lists.newArrayList();
        }
        List<ActivityDTO> activities = new ArrayList<>();
        for (Map.Entry<Long, List<ActivityDTO>> activity : skuActivityMap.entrySet()) {
            activities.addAll(activity.getValue());
        }
        return IssueActivityMapper.activities2IssueActivities(activities,queryContext.isDpClient());
    }

    public UnifiedSkuActivityQueryExecutor setDealIdMapperService(DealIdMapperService dealIdMapperService) {
        this.dealIdMapperService = dealIdMapperService;
        return this;
    }

    public UnifiedSkuActivityQueryExecutor setSkuActivityQueryRemoteService(SkuActivityQueryRemoteService skuActivityQueryRemoteService) {
        this.skuActivityQueryRemoteService = skuActivityQueryRemoteService;
        return this;
    }
}
