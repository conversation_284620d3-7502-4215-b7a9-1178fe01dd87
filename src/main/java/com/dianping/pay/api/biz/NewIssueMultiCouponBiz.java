package com.dianping.pay.api.biz;

import com.alibaba.fastjson.JSONObject;
import com.dianping.api.util.ApiUtil;
import com.dianping.api.util.CrossDomainUtils;
import com.dianping.api.util.DotUtils;
import com.dianping.cat.Cat;
import com.dianping.gmkt.coupon.common.api.enums.CouponGroupExtraKeyEnum;
import com.dianping.gmkt.coupon.common.api.enums.IssueDetailSourceEnum;
import com.dianping.lion.client.Lion;
import com.dianping.mobile.framework.datatypes.CommonMobileResponse;
import com.dianping.mobile.framework.datatypes.IMobileContext;
import com.dianping.mobile.framework.datatypes.IMobileResponse;
import com.dianping.pay.api.biz.activity.CouponInfoBiz;
import com.dianping.pay.api.biz.activity.IssueCouponBiz;
import com.dianping.pay.api.biz.activity.RestUserInfoService;
import com.dianping.pay.api.biz.activity.newloader.dto.RestUserInfo;
import com.dianping.pay.api.biz.activity.newloader.mapper.IssueActivityMapper;
import com.dianping.pay.api.constants.CatEventConstants;
import com.dianping.pay.api.entity.issuecoupon.IssueCouponMsg;
import com.dianping.pay.api.entity.issuecoupon.NewBatchIssueCouponRequest;
import com.dianping.pay.api.entity.issuecoupon.NewIssueCouponUnit;
import com.dianping.pay.api.entity.issuecoupon.NewIssueDetailResult;
import com.dianping.pay.api.entity.issuecoupon.NewIssueMultiCouponContext;
import com.dianping.pay.api.entity.issuecoupon.NewIssueMultiCouponRequest;
import com.dianping.pay.api.entity.issuecoupon.NewIssueMultiCouponResp;
import com.dianping.pay.api.exception.IssueCouponException;
import com.dianping.pay.api.util.JsonUtils;
import com.dianping.pay.api.util.LionConstants;
import com.dianping.pay.promo.common.enums.coupon.UnifiedCouponGroupType;
import com.dianping.unified.coupon.manage.api.dto.UnifiedCouponGroupDTO;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import java.util.List;
import java.util.Map;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang.Validate;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;

@Service
@Slf4j
public class NewIssueMultiCouponBiz {

    public static final String CAT_TYPE = "NewIssueMultiCouponAction";

    @Autowired
    private RestUserInfoService restUserInfoService;

    @Autowired
    private CouponInfoBiz couponInfoBiz;

    @Autowired
    private IssueCouponBiz issueCouponBiz;

    public IMobileResponse validate(NewIssueMultiCouponRequest request, IMobileContext iMobileContext) {
        try {
            if (iMobileContext.getRequest() != null) {
                DotUtils.addRefererDot(iMobileContext.getRequest());
            }
            if (ApiUtil.isMapiRequest(iMobileContext)) {
                Validate.isTrue(iMobileContext.getUserId() > 0 || (iMobileContext.isMtClient() && iMobileContext.getUserStatus() != null && iMobileContext.getUserStatus().getMtUserId() > 0), "用户未登录");
            }
            validateRequest(request);
        } catch (IllegalArgumentException e) {
            log.error("invalid request:{}", request, e);
            return new CommonMobileResponse(new IssueCouponMsg(401, e.getMessage(), e.getMessage()));
        }
        return null;
    }

    public NewIssueMultiCouponResp doIssueMultiCoupon(NewIssueMultiCouponRequest request, IMobileContext iMobileContext) {
        CrossDomainUtils.setEnableCrossDomain(iMobileContext.getRequest(), iMobileContext.getResponse());
        try {
            log.info("NewIssueMultiCouponAction request:{}", JSONObject.toJSONString(request));
            validateRequest(request);
            NewBatchIssueCouponRequest newBatchIssueCouponRequest = JsonUtils.toObject(request.getIssuemulticouponrequestbody(), NewBatchIssueCouponRequest.class);
            NewIssueMultiCouponContext context;
            // 如果是h5场景
            if (!ApiUtil.isMapiRequest(iMobileContext)) {
                RestUserInfo restUserInfo = restUserInfoService.handleMobileContext(iMobileContext);
                context = IssueActivityMapper.buildHttpPromotionRequestContext(newBatchIssueCouponRequest, restUserInfo);
            } else {
                context = IssueActivityMapper.buildNativePromotionRequestContext(newBatchIssueCouponRequest, iMobileContext);
            }
            log.info("NewIssueMultiCouponAction context:{}", JSONObject.toJSONString(context));
            Assert.isTrue(context.getUserId() > 0, "用户未登录");
            Assert.isTrue(isValidUserId(context.isMt(), context.getUserId()), "用户非法");
            //目前批量发券接口只支持opt券
            doIssueOptCoupon(context);
            List<NewIssueDetailResult> issueDetailResultList = generateResultFromContext(context);
            dotIssueDetailResult(issueDetailResultList);
            NewIssueMultiCouponResp newIssueMultiCouponResp = new NewIssueMultiCouponResp();
            newIssueMultiCouponResp.setBeSuccess(true);
            newIssueMultiCouponResp.setIssueDetailResultList(issueDetailResultList);
            log.info("NewIssueMultiCouponAction, newIssueMultiCouponResp:{}", newIssueMultiCouponResp);
            return newIssueMultiCouponResp;
        } catch(IllegalArgumentException ie) {
            Cat.logEvent(CAT_TYPE, "invalidRequest");
            log.error("NewIssueMultiCouponAction invalid request:{}", request, ie);
            NewIssueMultiCouponResp newIssueMultiCouponResp = new NewIssueMultiCouponResp();
            newIssueMultiCouponResp.setBeSuccess(false);
            newIssueMultiCouponResp.setErrorMsg(ie.getMessage());
            return newIssueMultiCouponResp;
        } catch (IssueCouponException e) {
            Cat.logEvent(CAT_TYPE, "issueCouponException");
            log.error("NewIssueMultiCouponAction request:{}", request, e);
            NewIssueMultiCouponResp newIssueMultiCouponResp = new NewIssueMultiCouponResp();
            newIssueMultiCouponResp.setBeSuccess(false);
            newIssueMultiCouponResp.setErrorMsg(e.getMessage());
            return newIssueMultiCouponResp;
        }
    }

    private void dotIssueDetailResult(List<NewIssueDetailResult> issueDetailResultList) {
        int successCount = 0;
        int failCount = 0;
        for(NewIssueDetailResult newIssueDetailResult : issueDetailResultList) {
            if(newIssueDetailResult.isBeSuccess()) {
                successCount++;
            } else {
                failCount++;
            }
        }
        Cat.logEvent(CatEventConstants.ISSUE_MULITY_RESULT, successCount + "_" + failCount);
    }

    private List<NewIssueDetailResult> generateResultFromContext(NewIssueMultiCouponContext context) {
        List<NewIssueDetailResult> newIssueDetailResultList = Lists.newArrayList();
        Map<String, NewIssueDetailResult> unitIssueResultMap = context.getUnitIssueResultMap();
        for(NewIssueCouponUnit newIssueCouponUnit : context.getNewIssueCouponUnitList()) {
            if(unitIssueResultMap.containsKey(newIssueCouponUnit.getUnitId())) {
                newIssueDetailResultList.add(unitIssueResultMap.get(newIssueCouponUnit.getUnitId()));
            } else {
                NewIssueDetailResult newIssueDetailResult = new NewIssueDetailResult(newIssueCouponUnit.getUnitId(), false, "券不存在");
                newIssueDetailResult.setUnifiedCouponGroupId(newIssueCouponUnit.getUnifiedCouponGroupId());
                newIssueDetailResultList.add(newIssueDetailResult);
            }
        }
        return newIssueDetailResultList;
    }

    private void validateRequest(NewIssueMultiCouponRequest request) {
        Validate.isTrue(request != null && StringUtils.isNotEmpty(request.getIssuemulticouponrequestbody()), "null request");
        NewBatchIssueCouponRequest newBatchIssueCouponRequest = JsonUtils.toObject(request.getIssuemulticouponrequestbody(), NewBatchIssueCouponRequest.class);
        Validate.notNull(newBatchIssueCouponRequest, "请求json转换失败");
        Validate.isTrue(newBatchIssueCouponRequest.getIssueDetailSourceCode() != null && IssueDetailSourceEnum.getByCode(newBatchIssueCouponRequest.getIssueDetailSourceCode()) != null, "issueDetailSourceCode非法");
        Validate.isTrue(newBatchIssueCouponRequest.getProductType() != null && newBatchIssueCouponRequest.getProductType() == 0, "仅支持团单业务");
        Validate.isTrue(CollectionUtils.isNotEmpty(newBatchIssueCouponRequest.getNewIssueCouponUnitList()), "请求单元列表不能为空");
        for(NewIssueCouponUnit newIssueCouponUnit : newBatchIssueCouponRequest.getNewIssueCouponUnitList()) {
            Validate.isTrue(newIssueCouponUnit.getCouponType() != null && newIssueCouponUnit.getCouponType() == 0, "批量发券仅支持opt券");
        }
    }

    public void doIssueOptCoupon(NewIssueMultiCouponContext context) {
        Map<String, NewIssueCouponUnit> optUnitIdCouponMap = collectOptUnitMap(context.getNewIssueCouponUnitList());
        if(MapUtils.isEmpty(optUnitIdCouponMap)) {
            return;
        }
        Map<String, List<String>> optCouponGroupIdUnitIdListMap = collectOptCouponGroupIdUnitIdListMap(optUnitIdCouponMap);
        dotIssueOptCount(optCouponGroupIdUnitIdListMap);
        Map<String, UnifiedCouponGroupDTO> couponGroupDTOMap = couponInfoBiz.batchLoadCouponGroup(Lists.newArrayList(optCouponGroupIdUnitIdListMap.keySet()));
        Pair<List<String>, List<String>> shopCouponAndLQGCouponPair = collectShopCouponAndLQGCouponPair(couponGroupDTOMap);
        context.setOptUnitIdCouponMap(optUnitIdCouponMap);
        context.setShopCouponGroupIds(shopCouponAndLQGCouponPair.getLeft());
        context.setLqgCouponGroupIds(shopCouponAndLQGCouponPair.getRight());
        context.setOptCouponGroupIdUnitIdListMap(optCouponGroupIdUnitIdListMap);
        issueCouponBiz.doIssueMultiCouponNew(context);
    }

    private void dotIssueOptCount(Map<String, List<String>> optCouponGroupIdUnitIdListMap) {
        if(MapUtils.isNotEmpty(optCouponGroupIdUnitIdListMap)) {
            int allOptCount = optCouponGroupIdUnitIdListMap.keySet().size();
            int singleOptMaxCount = 0;
            for(Map.Entry<String, List<String>> entry : optCouponGroupIdUnitIdListMap.entrySet()) {
                if(entry.getValue().size() > singleOptMaxCount) {
                    singleOptMaxCount = entry.getValue().size();
                }
            }
            Cat.logEvent(CatEventConstants.ISSUE_MULITY_COUPON_QUANTITY, allOptCount + "_" + singleOptMaxCount);
        }
    }

    private static Map<String, NewIssueCouponUnit> collectOptUnitMap(List<NewIssueCouponUnit> newIssueCouponUnitList) {
        Map<String, NewIssueCouponUnit> optUnitIdCouponMap = Maps.newHashMap();
        for(NewIssueCouponUnit newIssueCouponUnit : newIssueCouponUnitList) {
            if(newIssueCouponUnit.getCouponType() == 0) {
                optUnitIdCouponMap.put(newIssueCouponUnit.getUnitId(), newIssueCouponUnit);
            }
        }
        return optUnitIdCouponMap;
    }

    private Map<String, List<String>> collectOptCouponGroupIdUnitIdListMap(Map<String, NewIssueCouponUnit> optUnitIdCouponMap) {
        Map<String, List<String>> optCouponGroupIdUnitIdListMap = Maps.newHashMap();
        for(Map.Entry<String, NewIssueCouponUnit> entry : optUnitIdCouponMap.entrySet()) {
            String unifiedCouponGroupId = entry.getValue().getUnifiedCouponGroupId();
            if(optCouponGroupIdUnitIdListMap.containsKey(unifiedCouponGroupId)) {
                optCouponGroupIdUnitIdListMap.get(unifiedCouponGroupId).add(entry.getKey());
            } else {
                optCouponGroupIdUnitIdListMap.put(unifiedCouponGroupId, Lists.newArrayList(entry.getKey()));
            }
        }
        return optCouponGroupIdUnitIdListMap;
    }

    private Pair<List<String>, List<String>> collectShopCouponAndLQGCouponPair(Map<String, UnifiedCouponGroupDTO> couponGroupDTOMap ) {
        //待发送商家券列表
        List<String> shopCouponGroupIds = Lists.newArrayList();
        //待发送领券购券列表
        List<String> lqgCouponGroupIds = Lists.newArrayList();
        if(MapUtils.isNotEmpty(couponGroupDTOMap)) {
            for (Map.Entry<String, UnifiedCouponGroupDTO> entry : couponGroupDTOMap.entrySet()) {
                int couponGroupType = entry.getValue().getCouponGroupType();
                String couponGroupId = String.valueOf(entry.getValue().getCouponGroupId());
                if (couponGroupType == UnifiedCouponGroupType.shopCoupon.value || couponGroupType == UnifiedCouponGroupType.mtShopCoupon.value) {
                    shopCouponGroupIds.add(couponGroupId);
                } else if (MapUtils.isNotEmpty(entry.getValue().getExtraInfoMap()) && MapUtils.getBooleanValue(entry.getValue().getExtraInfoMap(), CouponGroupExtraKeyEnum.lingquangou.name(), false)) {
                    lqgCouponGroupIds.add(couponGroupId);
                }
            }
        }
        return Pair.of(shopCouponGroupIds, lqgCouponGroupIds);
    }

    private boolean isValidUserId(boolean isMt, long userId) {
        List<Long> illegalUserIdList = null;
        if(isMt) {
            illegalUserIdList = Lion.getList(LionConstants.APP_KEY, LionConstants.ISSUE_MULTI_MT_USERID_BLACK_LIST, Long.class);
        } else {
            illegalUserIdList = Lion.getList(LionConstants.APP_KEY, LionConstants.ISSUE_MULTI_DP_USERID_BLACK_LIST, Long.class);
        }
        if(CollectionUtils.isEmpty(illegalUserIdList) || !illegalUserIdList.contains(userId)) {
            return true;
        }
        return false;
    }

}
