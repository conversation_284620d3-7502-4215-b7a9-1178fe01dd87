package com.dianping.pay.api.biz.activity.newloader.promo;

import com.dianping.cat.Cat;
import com.dianping.cat.thread.pool.CatExecutorServiceTraceWrapper;
import com.dianping.lion.client.ConfigEvent;
import com.dianping.lion.client.ConfigListener;
import com.dianping.lion.client.Lion;
import com.dianping.mobile.framework.datatypes.IMobileContext;
import com.dianping.pay.api.biz.activity.newloader.dto.ActivityFilterPatternDto;
import com.dianping.pay.api.biz.activity.newloader.dto.CouponActivityContext;
import com.dianping.pay.api.util.JsonUtils;
import com.dianping.pay.api.util.LionConstants;
import com.fasterxml.jackson.core.type.TypeReference;
import com.google.common.collect.ImmutableMap;
import com.meituan.mtrace.thread.pool.ExecutorServiceTraceWrapper;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import java.util.List;
import java.util.Map;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.SynchronousQueue;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;


public abstract class AbstractPromoProcessor {

    private static final Logger LOGGER = LogManager.getLogger(AbstractPromoProcessor.class);

    public static final ExecutorService ExecutorService = new ExecutorServiceTraceWrapper(new CatExecutorServiceTraceWrapper(new ThreadPoolExecutor(40, 120,
            60L, TimeUnit.SECONDS,
            new SynchronousQueue<Runnable>(), new ThreadPoolExecutor.CallerRunsPolicy())));

    public abstract void prePare(final CouponActivityContext promoCtx, IMobileContext iMobileContext);

    public abstract void loadPromo(CouponActivityContext promoCtx);

    public abstract String getPromoName();


    private volatile static Map<String, List<ActivityFilterPatternDto>> activityFilterPatternMap;

    private static boolean activityFilterSwitch;


    static {
        activityFilterSwitch = Lion.getBooleanValue(LionConstants.ACTIVITY_FILTER_SWITCH, true);
        Lion.addConfigListener(LionConstants.ACTIVITY_FILTER_SWITCH, new ConfigListener() {
            @Override
            public void configChanged(ConfigEvent configEvent) {
                activityFilterSwitch = Lion.getBooleanValue(LionConstants.ACTIVITY_FILTER_SWITCH, true);
            }
        });

        activityFilterPatternMap = ImmutableMap.copyOf((Map<String, List<ActivityFilterPatternDto>>)JsonUtils.toObject(Lion.getStringValue(LionConstants.ACTIVITY_FILTER_PATTERN_MAP), new TypeReference<Map<String, List<ActivityFilterPatternDto>>>() {
        }));
        Lion.addConfigListener(LionConstants.ACTIVITY_FILTER_PATTERN_MAP, new ConfigListener() {
            @Override
            public void configChanged(ConfigEvent configEvent) {
                activityFilterPatternMap = ImmutableMap.copyOf((Map<String, List<ActivityFilterPatternDto>>) JsonUtils.toObject(Lion.getStringValue(LionConstants.ACTIVITY_FILTER_PATTERN_MAP), new TypeReference<Map<String, List<ActivityFilterPatternDto>>>() {
                }));
            }
        });
    }

    private static final String ALL_MATCHED_PATTERN = "-1";

    /**
     * 黑名单接口Map<String, List<ActivityFilterPatternDto>>，其中key值为promoName，value值为需要过滤的pattern dto，满足任意一个规则及对优惠进行过滤
     * 使用黑名单需注意 & 评估产生的影响。不建议大量配置，另外只覆盖使用promoprocessor的场景
     * 为trues时需屏蔽盖优惠，配置方式具体参考：https://km.sankuai.com/page/1222503883
     *
     * @return
     */
    public boolean filterActivity(String activityId, CouponActivityContext promoCtx) {
        try {
            if (!activityFilterSwitch) {
                return false;
            }
            if (MapUtils.isEmpty(activityFilterPatternMap)) {
                return false;
            }
            List<ActivityFilterPatternDto> activityFilterPatternDtos = activityFilterPatternMap.get(getPromoName());
            if (CollectionUtils.isEmpty(activityFilterPatternDtos)) {
                return false;
            }
            for (ActivityFilterPatternDto patternDto : activityFilterPatternDtos) {

                //用户身份不匹配，返回
                if (promoCtx.getUserType().getCode() != patternDto.getUserType()) {
                    continue;
                }

                // 入参校验：activityIds为空或者 activityId列表不包含全部且没有目标activityId也返回
                if (!hitCollection(patternDto.getMatchedActivityIds(), activityId)) {
                    continue;
                }

                // 入参校验：dealGroupIds为空或者 dealGroupId列表不包含全部且没有目标dealGroupId也返回
                String dealGroupId = promoCtx.isMt() ? String.valueOf(promoCtx.getMtDealGroupId()) : String.valueOf(promoCtx.getDpDealGroupId());
                if (!hitCollection(patternDto.getMatchedDealGroupIds(), dealGroupId)) {
                    continue;
                }

                // 入参校验：skuIds为空或者 skuId列表不包含全部且没有目标skuId也返回
                // 因为购物车理论上添加的商品都是有效商品，不会曝光错误商品，可以暂时不考虑购物车场景
                if (!hitCollection(patternDto.getMatchedSkuIds(), String.valueOf(promoCtx.getSkuId()))) {
                    continue;
                }

                // 入参校验：shopIds为空或者 shopId列表不包含全部且没有目标shopId也返回
                String shopId = promoCtx.isMt() ? String.valueOf(promoCtx.getMtShopIdL()) : String.valueOf(promoCtx.getDpShopIdL());
                if (!hitCollection(patternDto.getMatchedShopIds(), shopId)) {
                    continue;
                }

                // 都匹配了就返回true
                Cat.logMetricForCount("HitBlack_" + getPromoName());
                return true;
            }
            return false;
        } catch (Exception e) {
            Cat.logEvent("HitBlackErr", getPromoName());
            LOGGER.error("filterActivity error. promoName: {}, activityId: {}", getPromoName(), activityId);
        }
        return false;
    }

    private boolean hitCollection(List<String> checkIdList, String targetId) {
        Cat.logEvent("INVALID_METHOD_4", "com.dianping.pay.api.biz.activity.newloader.promo.AbstractPromoProcessor.hitCollection(java.util.List,java.lang.String)");
        if (CollectionUtils.isEmpty(checkIdList)) {
            return false;
        }
        return checkIdList.contains(ALL_MATCHED_PATTERN) || checkIdList.contains(targetId);
    }
}
