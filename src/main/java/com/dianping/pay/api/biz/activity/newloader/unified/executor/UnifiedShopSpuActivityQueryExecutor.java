package com.dianping.pay.api.biz.activity.newloader.unified.executor;

import com.dianping.cat.Cat;
import com.dianping.pay.api.beans.IssueCouponActivity;
import com.dianping.pay.api.biz.activity.newloader.AbstractUnifiedCouponActivityQueryExecutor;
import com.dianping.pay.api.biz.activity.newloader.dto.CouponIssueActivityQueryContext;
import com.dianping.pay.api.biz.activity.newloader.mapper.IssueActivityMapper;
import com.dianping.poi.relation.service.api.PoiRelationService;
import com.dianping.spc.common.Response;
import com.dianping.tgc.open.SpuActivityQueryRemoteService;
import com.dianping.tgc.open.entity.ActivityDTO;
import com.dianping.tgc.open.entity.ActivityQueryRequest;
import com.dianping.tgc.open.entity.QueryResponseDTO;
import com.dianping.tgc.open.enums.QuerySourceEnum;
import com.google.common.collect.Lists;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Created by drintu on 18/6/14.
 */

public class UnifiedShopSpuActivityQueryExecutor extends AbstractUnifiedCouponActivityQueryExecutor {
    private PoiRelationService poiRelationCacheService;
    private SpuActivityQueryRemoteService spuActivityQueryRemoteService;

    public UnifiedShopSpuActivityQueryExecutor(CouponIssueActivityQueryContext context) {
        super(context);
    }

    public UnifiedShopSpuActivityQueryExecutor setPoiRelationCacheService(PoiRelationService poiRelationCacheService) {
        this.poiRelationCacheService = poiRelationCacheService;
        return this;
    }

    public UnifiedShopSpuActivityQueryExecutor setSpuQueryRemoteService(SpuActivityQueryRemoteService spuActivityQueryRemoteService) {
        this.spuActivityQueryRemoteService = spuActivityQueryRemoteService;
        return this;
    }

    @Override
    protected List<IssueCouponActivity> loadFromRemote() {
        if (queryContext.getShopIdL() <= 0) {
            return Lists.newArrayList();
        }
        List<Long> dpShopIdList = Lists.newArrayList();
        if (queryContext.isDpClient()) {
            dpShopIdList = Lists.newArrayList(queryContext.getShopIdL());
        } else {
            try {
                dpShopIdList = poiRelationCacheService.queryDpByMtIdL(queryContext.getShopIdL());
            } catch (Exception e) {
                Cat.logError("queryDpByMtId", e);
            }
            if (CollectionUtils.isEmpty(dpShopIdList)) {
                return Lists.newArrayList();
            }
        }
        Map<Long, List<ActivityDTO>> activityMap = new HashMap<Long, List<ActivityDTO>>();
        for(Long shopId : dpShopIdList){
            ActivityQueryRequest remoteRequest = new ActivityQueryRequest();
            remoteRequest.setShopIdL(shopId);
            remoteRequest.setSource(queryContext.isDpClient() ? QuerySourceEnum.DP.getValue() : QuerySourceEnum.MT.getValue());
            Response<QueryResponseDTO> response = null;
            try {
                response = spuActivityQueryRemoteService.queryActivityDTOsByShopId(remoteRequest);
            } catch (Exception e){
                Cat.logError("queryActivityDTOsByShopId", e);
            }
            if(response == null || !response.isSuccess() || response.getResult() == null){
                continue;
            }
            activityMap.put(shopId, response.getResult().getActivityDTOs());
        }
        if (MapUtils.isEmpty(activityMap)) {
            return Lists.newArrayList();
        }
        return IssueActivityMapper.activities2IssueActivitiesL(activityMap,queryContext.isDpClient());
    }
}
