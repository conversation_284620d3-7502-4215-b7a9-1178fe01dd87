package com.dianping.pay.api.biz.activity.newloader.promo;


import com.dianping.api.campaign.utils.ThreadPoolUtils;
import com.dianping.api.constans.CommonConstants;
import com.dianping.api.domain.ClientInfo;
import com.dianping.api.service.UserWrapperService;
import com.dianping.gmkt.scene.api.delivery.dto.req.*;
import com.dianping.gmkt.scene.api.delivery.dto.res.*;
import com.dianping.gmkt.scene.api.delivery.enums.ActionTypeEnum;
import com.dianping.gmkt.scene.api.delivery.enums.DeliveryErrorCode;
import com.dianping.gmkt.scene.api.delivery.enums.RecallTypeEnum;
import com.dianping.gmkt.scene.api.delivery.service.ResourcesActionService;
import com.dianping.gmkt.scene.api.delivery.service.ResourcesExposureService;
import com.dianping.lion.client.Lion;
import com.dianping.mobile.framework.datatypes.IMobileContext;
import com.dianping.pay.api.beans.ResourcePromotionDo;
import com.dianping.pay.api.biz.AsyncDpInfoLoader;
import com.dianping.pay.api.biz.activity.newloader.dto.CouponActivityContext;
import com.dianping.pay.api.biz.activity.newloader.dto.ResourcePromotionIssueCouponContext;
import com.dianping.pay.api.biz.user.UserAccountBiz;
import com.dianping.pay.api.entity.issuecoupon.IssueCouponRequest;
import com.dianping.pay.api.entity.issuecoupon.UnifiedIssueResult;
import com.dianping.pay.api.enums.QueryShopCouponTypeEnum;
import com.dianping.pay.api.exception.IssueCouponException;
import com.dianping.pay.api.service.impl.ShopWrapperService;
import com.dianping.pay.api.util.ClientTypeUtils;
import com.dianping.pay.api.util.DotUtils;
import com.dianping.pay.api.util.LionConstants;
import com.google.common.collect.Lists;
import com.sankuai.meituan.waimai.thrift.tools.cache.RegionSetConfigCache;
import com.sankuai.meituan.waimai.thrift.tools.enums.SetProxyTypeEnum;
import com.sankuai.meituan.waimai.thrift.tools.utils.MTraceRouterInfoUtil;
import com.sankuai.wpt.user.retrieve.thrift.message.UserModel;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.concurrent.Callable;
import java.util.concurrent.Future;
import java.util.concurrent.TimeUnit;

import static com.dianping.pay.api.enums.GetCouponStatus.GET_COUPON_FAIL;

@Component("resourcePromotionProcessor")
@Slf4j
public class ResourcePromotionProcessor extends AbstractPromoProcessor {

    @Autowired
    private ResourcesExposureService resourcesExposureService;

    @Autowired
    private ResourcesActionService resourcesActionService;

    @Autowired
    private ShopWrapperService shopWrapperService;

    @Autowired
    private UserWrapperService userWrapperService;

    @Autowired
    private UserAccountBiz userAccountBiz;

    @Override
    public void prePare(final CouponActivityContext promoCtx, final IMobileContext iMobileContext) {
        try {
            if (!promoCtx.isNeedResourcesPromotion()) {
                return;
            }
            if (promoCtx.isMt() && promoCtx.getMtUserId() <= 0) {
                return;
            }
            if (!promoCtx.isMt() && promoCtx.getDpUserId() <= 0) {
                return;
            }
            if (promoCtx.getCityId() <= 0 || promoCtx.getDpShopIdL() <= 0) {
                return;
            }
            DotUtils.dotForPromoProcessor(promoCtx, getPromoName());
            Future<ResourcePromotionDo> future = ExecutorService.submit(new Callable<ResourcePromotionDo>() {
                @Override
                public ResourcePromotionDo call() throws Exception {
                    return loadResources(promoCtx, iMobileContext);
                }
            });
            promoCtx.setResourceExposureFuture(future);
        } catch (Exception e) {
            log.error("resourcePromotionProcessor# prepare error. ctx: {}", promoCtx, e);
        }
    }

    @Override
    public void loadPromo(CouponActivityContext promoCtx) {
        try {
            Future<ResourcePromotionDo> resourceExposureFuture = promoCtx.getResourceExposureFuture();
            if (resourceExposureFuture != null) {
                promoCtx.setResourceExposure(resourceExposureFuture.get(500, TimeUnit.MILLISECONDS));
            }
        } catch (Exception e) {
            log.error("resourcePromotionProcessor# loadPromo error. ctx: {}", promoCtx, e);
        }
    }

    @Override
    public String getPromoName() {
        return "resourcePromo";
    }

    private ResourcePromotionDo loadResources(CouponActivityContext promoCtx, IMobileContext iMobileContext) {
        try {
            long userId;
            if (promoCtx.isMt()) {
                userId = promoCtx.getMtUserId();
            } else {
                userId = promoCtx.getDpUserId();
            }

            QueryExposureResourcesReqDTO queryExposureResourcesReqDTO = new QueryExposureResourcesReqDTO();
            queryExposureResourcesReqDTO.setResourceLocationId(getLocationId(promoCtx));
            queryExposureResourcesReqDTO.setCityId(promoCtx.getCityId());
            queryExposureResourcesReqDTO.setLatitude(promoCtx.getLatitude());
            queryExposureResourcesReqDTO.setLongitude(promoCtx.getLongitude());
            queryExposureResourcesReqDTO.setUserId(userId);
            if (StringUtils.isNotBlank(promoCtx.getUuid())) {
                queryExposureResourcesReqDTO.setUuid(promoCtx.getUuid());
            } else {
                queryExposureResourcesReqDTO.setUuid(promoCtx.getDpid());
            }
            queryExposureResourcesReqDTO.setPageSource(promoCtx.getPageSource());

            RiskContextDTO riskContextDTO = new RiskContextDTO();
            riskContextDTO.setIp(iMobileContext.getUserIp());
            riskContextDTO.setUserAgent(iMobileContext.getUserAgent());
            riskContextDTO.setVersion(iMobileContext.getVersion());
            riskContextDTO.setRequestUrl(iMobileContext.getRequest().getRequestURI());
            if (StringUtils.isNotBlank(promoCtx.getUuid())) {
                riskContextDTO.setUuid(promoCtx.getUuid());
            } else {
                riskContextDTO.setUuid(promoCtx.getDpid());
            }
            riskContextDTO.setPlatform(promoCtx.getCPlatform() == 1 ? CommonConstants.RAINBOW_IOS : CommonConstants.RAINBOW_ANDROID);
            int payPlatform = promoCtx.getCouponPlatform();
            if (ClientTypeUtils.isMainWX(payPlatform, promoCtx.isMt())) {
                riskContextDTO.setWechatFingerprint(promoCtx.getCx());
            } else if (ClientTypeUtils.isMainWeb(payPlatform, promoCtx.isMt())) {
                riskContextDTO.setH5Fingerprint(promoCtx.getCx());
            }
            if (StringUtils.isNotBlank(promoCtx.getMtFingerprint())){
                riskContextDTO.setFingerprint(promoCtx.getMtFingerprint());
            } else {
                riskContextDTO.setFingerprint(promoCtx.getCx());
            }
            riskContextDTO.setPhone(promoCtx.getPhone());
            riskContextDTO.setClientType(ClientTypeUtils.getRiskClientType(iMobileContext));
            riskContextDTO.setMtgsig(promoCtx.getMtgsig());
            queryExposureResourcesReqDTO.setRiskContextDTO(riskContextDTO);

            queryExposureResourcesReqDTO.setPlatform(promoCtx.isMt() ? 2 : 1);
            Pair<List<Integer>, List<Integer>> categoryPair = shopWrapperService.loadShopCategory(promoCtx.getDpShopIdL());
            if (categoryPair == null) {
                return null;
            }
            queryExposureResourcesReqDTO.setFirstCategoryList(categoryPair.getLeft());
            queryExposureResourcesReqDTO.setSecondCategoryList(categoryPair.getRight());
            if (Lion.getBooleanValue(LionConstants.RESOURCE_SIEVE_SWITCH, true) && promoCtx.getQueryShopCouponType() == QueryShopCouponTypeEnum.BY_DEAL.getCode()) {
                int dealGroupId = promoCtx.isMt() ? promoCtx.getMtDealGroupId() : promoCtx.getDpDealGroupId();
                queryExposureResourcesReqDTO.setBizIds(Lists.newArrayList(String.valueOf(String.valueOf(dealGroupId))));
                queryExposureResourcesReqDTO.setBizIdsType(1);
            }
            queryExposureResourcesReqDTO.setLocationCityId(promoCtx.getActualCityId());

            DeliveryCommonResponse<List<ResourceExposureResponseDTO>> response = resourcesExposureService.queryExposureResources(queryExposureResourcesReqDTO);
            log.info("call queryExposureResources# request: {}, resp: {}", queryExposureResourcesReqDTO, response);
            if (response == null || !DeliveryErrorCode.SUCESS.getErrorCode().equals(response.getCode())) {
                DotUtils.dotForPromoCallFail(getPromoName(), "queryExposureResources", promoCtx.isMt());
                log.warn("call queryExposureResources failed. request: {}, resp: {}", queryExposureResourcesReqDTO, response);
                return null;
            }
            if (CollectionUtils.isEmpty(response.getData())) {
                DotUtils.dotForPromoHit(getPromoName(), promoCtx.isMt(), false);
                return null;
            }
            DotUtils.dotForPromoHit(getPromoName(), promoCtx.isMt(), true);

            ResourceExposureResponseDTO exposureResponseDTO = response.getData().get(0);
            if (CollectionUtils.isEmpty(exposureResponseDTO.getMaterialsDTOList())) {
                return null;
            }

            ResourcePromotionDo resourcePromotionDo = buildResourcePromotionDo(exposureResponseDTO);
            if (resourcePromotionDo == null) {
                return null;
            }
            // 已领取直接返回
            if (resourcePromotionDo.getDrawStatus() == 1) {
                return resourcePromotionDo;
            }
            // 无法领取
            if (resourcePromotionDo.getDrawStatus() != 2) {
                return null;
            }


            // 如果是领券的就返回
            // 如果不是发券形式的，就直接返回了
            if (!ActionTypeEnum.ACTION_DRAW_COUPON.getCode().equals(resourcePromotionDo.getActionType())) {
                return null;
            }
            // 预发券的就直接返回了
            if (RecallTypeEnum.RECALL_PRE_DRAW_COUPON.getCode().equals(resourcePromotionDo.getRecallType())) {
                return resourcePromotionDo;
            }
            if (!RecallTypeEnum.RECALL_DRAW_COUPON.getCode().equals(resourcePromotionDo.getRecallType())) {
                return null;
            }

            // 剩下的都是主动发券的了
            ResourceActionRequestDTO requestDTO = new ResourceActionRequestDTO();
            requestDTO.setActivityId(exposureResponseDTO.getActivityId());
            ActionMaterial actionMaterial = new ActionMaterial();
            actionMaterial.setActionType(ActionTypeEnum.ACTION_DRAW_COUPON.getCode());
            actionMaterial.setMaterialType(resourcePromotionDo.getMaterialType());
            actionMaterial.setMaterialId(resourcePromotionDo.getMaterialId());
            actionMaterial.setPlatform(promoCtx.isMt() ? 2 : 1);

            requestDTO.setActionMaterial(actionMaterial);
            requestDTO.setUserId(userId);
            requestDTO.setRowKey(exposureResponseDTO.getRowKey());
            requestDTO.setRiskContextDTO(riskContextDTO);
            requestDTO.setPlatform(promoCtx.isMt() ? 2 : 1);
            requestDTO.setFlowId(exposureResponseDTO.getFlowId());
            requestDTO.setResourceLocationId(exposureResponseDTO.getResourceLocationId());
            requestDTO.setCityId(promoCtx.getCityId());
            requestDTO.setLatitude(promoCtx.getLatitude());
            requestDTO.setLongitude(promoCtx.getLongitude());
            requestDTO.setLocationCityId(promoCtx.getActualCityId());

            DeliveryCommonResponse<ResourceActionResponseDTO> actionResponseDTODeliveryCommonResponse =
                    resourcesActionService.doResourcesAction(requestDTO);
            log.info("call doResourcesAction(auto)# request: {}, resp: {}", requestDTO, actionResponseDTODeliveryCommonResponse);
            if (actionResponseDTODeliveryCommonResponse == null || !DeliveryErrorCode.SUCESS.getErrorCode().equals(actionResponseDTODeliveryCommonResponse.getCode())) {
                return null;
            }
            ResourceActionResponseDTO resourceActionResponseDTO = actionResponseDTODeliveryCommonResponse.getData();
            if (CollectionUtils.isEmpty(resourceActionResponseDTO.getCouponDetailDTOList())) {
                return null;
            }
            resourcePromotionDo.setDetailDTO(resourceActionResponseDTO.getCouponDetailDTOList().get(0));
            resourcePromotionDo.setDrawStatus(1);
            resourcePromotionDo.setCurrentIssue(true);
            return resourcePromotionDo;
        } catch (Exception e) {
            log.warn("ResourcePromotionProcessor failed. promoCtx: {}", promoCtx, e);
        }
        return null;
    }


    private ResourcePromotionDo buildResourcePromotionDo(ResourceExposureResponseDTO resourceExposureResponseDTO) {
        LocationMaterialsDTO materialsDTO = resourceExposureResponseDTO.getMaterialsDTOList().get(0);
        if (materialsDTO == null || CollectionUtils.isEmpty(materialsDTO.getCouponDetailDTOS())) {
            return null;
        }
        ResourcePromotionDo result = new ResourcePromotionDo();
        result.setActivityId(resourceExposureResponseDTO.getActivityId());
        result.setFlowId(resourceExposureResponseDTO.getFlowId());
        result.setRecallType(materialsDTO.getRecallType());
        result.setActionType(materialsDTO.getActionType());
        result.setMaterialType(materialsDTO.getMaterialType());
        result.setPlatform(materialsDTO.getPlatform());
        result.setMaterialId(materialsDTO.getMaterialId());
        result.setRowKey(resourceExposureResponseDTO.getRowKey());
        result.setStaticMaterialContext(resourceExposureResponseDTO.getStaticMaterialContext());
        result.setDrawStatus(materialsDTO.getDrawStatus());
        result.setDetailDTO(materialsDTO.getCouponDetailDTOS().get(0));
        return result;
    }

    // TODO
    private Long getLocationId(CouponActivityContext promoCtx) {
        if (promoCtx.isMt()) {
            // 如果是团详的
            if (promoCtx.getQueryShopCouponType() == QueryShopCouponTypeEnum.BY_DEAL.getCode()) {
                return Lion.getLongValue("mapi-pay-promo-web.exposure.mt.deal.locationid", 0L);
            } else {
                return Lion.getLongValue("mapi-pay-promo-web.exposure.mt.sku.locationid", 1007L);
            }
        } else {
            if (promoCtx.getQueryShopCouponType() == QueryShopCouponTypeEnum.BY_DEAL.getCode()) {
                return Lion.getLongValue("mapi-pay-promo-web.exposure.dp.deal.locationid", 1006L);
            } else {
                return Lion.getLongValue("mapi-pay-promo-web.exposure.dp.sku.locationid", 1005L);
            }
        }
    }

    public UnifiedIssueResult doIssueCoupon(IssueCouponRequest issueCouponRequest, IMobileContext context) {
        try {
            //创建 & 初始化上下文(主要初始化部分美团体系数据)
            ResourcePromotionIssueCouponContext issueCouponContext = createAndInitContext(issueCouponRequest, context);

            //涉及家店交互需要设置reginId
            if (issueCouponContext.getMtRealUserId() != null && issueCouponContext.getMtRealUserId() > 0 &&
                    StringUtils.isNotBlank(issueCouponRequest.getRegionId())) {
                // regionId埋入trace, 背景见https://km.sankuai.com/collabpage/**********
                String regionId = issueCouponRequest.getRegionId();
                String setName = RegionSetConfigCache.getCellNameByRegionId(regionId);
                MTraceRouterInfoUtil.setTraceRoutingInfo4Api(SetProxyTypeEnum.BY_USER_ID, issueCouponContext.getMtRealUserId(), regionId, setName);
            }

            ResourceActionRequestDTO requestDTO = new ResourceActionRequestDTO();
            requestDTO.setActivityId(issueCouponRequest.getActivityid());
            ActionMaterial actionMaterial = new ActionMaterial();
            if (StringUtils.isNotBlank(issueCouponRequest.getMaterialid()) && issueCouponRequest.getMaterialid().split(",").length > 1) {
                actionMaterial.setActionType(ActionTypeEnum.ACTION_DRAW_MULTI_COUPON.getCode());
            } else {
                actionMaterial.setActionType(ActionTypeEnum.ACTION_DRAW_COUPON.getCode());
            }
            actionMaterial.setMaterialType(2);
            actionMaterial.setMaterialId(issueCouponRequest.getMaterialid());
            actionMaterial.setPlatform(issueCouponRequest.isDpClient() ? 1 : 2);

            RiskContextDTO riskContextDTO = new RiskContextDTO();
            riskContextDTO.setIp(context.getUserIp());
            riskContextDTO.setUserAgent(context.getUserAgent());
            riskContextDTO.setVersion(context.getVersion());
            riskContextDTO.setRequestUrl(context.getRequest().getRequestURI());
            if (issueCouponRequest.getCPlatform() != null) {
                riskContextDTO.setPlatform(issueCouponRequest.getCPlatform() == 1 ? CommonConstants.RAINBOW_IOS : CommonConstants.RAINBOW_ANDROID);
            } else {
                riskContextDTO.setPlatform(0);
            }
            riskContextDTO.setUuid(issueCouponRequest.getUuid());
            String dpId = context.getHeader().getDpid();
            if (issueCouponContext.getOriginIssueCouponRequest().isDpClient() && StringUtils.isNotBlank(dpId)) {
                riskContextDTO.setDpid(dpId);
            }
            riskContextDTO.setPhone(issueCouponRequest.isDpClient() ? issueCouponContext.getMobilePhone() : getPhoneByUserId(issueCouponRequest.getUserId(), true));
            if (StringUtils.isNotBlank(issueCouponRequest.getMtFingerprint())){
                riskContextDTO.setFingerprint(issueCouponRequest.getMtFingerprint());
            } else {
                if (issueCouponRequest.getClientInfo() != null){
                    riskContextDTO.setFingerprint(issueCouponRequest.getClientInfo().getCx());
                }
            }
            riskContextDTO.setClientType(ClientTypeUtils.getRiskClientType(context));
            if (context.getRequest() != null) {
                riskContextDTO.setMtgsig(context.getRequest().getHeader("mtgsig"));
            }

            requestDTO.setActionMaterial(actionMaterial);
            requestDTO.setUserId(issueCouponRequest.getUserId());
            requestDTO.setRowKey(issueCouponRequest.getRowkey());
            requestDTO.setRiskContextDTO(riskContextDTO);
            requestDTO.setPlatform(issueCouponRequest.isDpClient() ? 1 : 2);
            requestDTO.setFlowId(issueCouponRequest.getFlowid());
            requestDTO.setResourceLocationId(issueCouponRequest.getResourceactivityid());
            requestDTO.setCityId(issueCouponRequest.getCityId());
            requestDTO.setLongitude(issueCouponRequest.getLongitude());
            requestDTO.setLatitude(issueCouponRequest.getLatitude());
            requestDTO.setLocationCityId(issueCouponRequest.getActualCityId());

            //点评端增加神会员相关上下文
            fillGodMemberContext(issueCouponContext, requestDTO);
            DeliveryCommonResponse<ResourceActionResponseDTO> actionResponseDTODeliveryCommonResponse =
                        resourcesActionService.doResourcesAction(requestDTO);
            log.info("call doResourcesAction# request: {}, resp: {}", requestDTO, actionResponseDTODeliveryCommonResponse);
            if (actionResponseDTODeliveryCommonResponse == null || !DeliveryErrorCode.SUCESS.getErrorCode().equals(actionResponseDTODeliveryCommonResponse.getCode()) ||
                    actionResponseDTODeliveryCommonResponse.getData() == null) {
                log.warn("issue resource coupon failed. request: {}, result: {}", requestDTO, actionResponseDTODeliveryCommonResponse);
                throw new IssueCouponException("领取失败", GET_COUPON_FAIL);
            }
            ResourceActionResponseDTO resourceActionResponseDTO = actionResponseDTODeliveryCommonResponse.getData();
            if (CollectionUtils.isEmpty(resourceActionResponseDTO.getCouponDetailDTOList())) {
                log.warn("issue resource coupon failed. request: {}, result: {}", requestDTO, actionResponseDTODeliveryCommonResponse);
                throw new IssueCouponException("领取失败", GET_COUPON_FAIL);
            }
            CouponDetailDTO detailDTO = resourceActionResponseDTO.getCouponDetailDTOList().get(0);

            UnifiedIssueResult result = new UnifiedIssueResult();
            result.setBeginTime(new Date(detailDTO.getUseBeginTime()));
            result.setEndTime(new Date(detailDTO.getUseEndTime()));
            result.setCouponGroupName(detailDTO.getCouponGroupName());
            return result;
        } catch (Exception e) {
            log.error("issue resource coupon failed. request: {}", issueCouponRequest, e);
            throw new IssueCouponException("领取失败", GET_COUPON_FAIL);
        }
    }

    private void fillGodMemberContext(ResourcePromotionIssueCouponContext issueCouponContext, ResourceActionRequestDTO requestDTO) {
        //非点评端 or 没有美团实id端场景，不需要神会员上下文
        if (!issueCouponContext.getOriginIssueCouponRequest().isDpClient() || issueCouponContext.getMtRealUserId() == null ||
                issueCouponContext.getMtRealUserId() <= 0) {
            return;
        }
        GodMemberContextDTO contextDTO = new GodMemberContextDTO();
        contextDTO.setMtRealUserId(issueCouponContext.getMtRealUserId());
        contextDTO.setMtVirtualUserId(issueCouponContext.getMtVirtualUserId());
        contextDTO.setMtCityId(issueCouponContext.getMtCityId());
        contextDTO.setMtLocationCityId(issueCouponContext.getMtActualCityId());
        requestDTO.setGodMemberContextDTO(contextDTO);
    }

    private ResourcePromotionIssueCouponContext createAndInitContext(IssueCouponRequest issueCouponRequest, IMobileContext context) {
        ResourcePromotionIssueCouponContext issueCouponContext = new ResourcePromotionIssueCouponContext(issueCouponRequest);
        if (!issueCouponContext.getOriginIssueCouponRequest().isDpClient()) {
            issueCouponContext.setMtRealUserId(issueCouponRequest.getUserId());
            issueCouponContext.setMtShopId(issueCouponRequest.getShopIdL());
            issueCouponContext.setMtCityId(issueCouponRequest.getCityId());
            issueCouponContext.setMtActualCityId(issueCouponRequest.getActualCityId());
            return issueCouponContext;
        }

        // 点评端降级开关
        if (!Lion.getBooleanValue(LionConstants.DP_MMC_COUPON_DEGRADE_SWITCH, false)) {
            return issueCouponContext;
        }

        AsyncDpInfoLoader dpIdConverter = buildConverter(issueCouponContext);
        issueCouponContext.setMtShopId(dpIdConverter.getMtShopId());
        Pair<Long, Long> mtUserPairFuture = dpIdConverter.getMtUserPairFuture();
        if (mtUserPairFuture != null) {
            issueCouponContext.setMtRealUserId(mtUserPairFuture.getLeft());
            issueCouponContext.setMtVirtualUserId(mtUserPairFuture.getRight());
        }
        issueCouponContext.setMobilePhone(dpIdConverter.getUserMobile());
        Map<Integer, Integer> dpCityId2MtCityIdMap = dpIdConverter.getDpCityId2MtCityIdMap();
        issueCouponContext.setMtCityId(MapUtils.getInteger(dpCityId2MtCityIdMap, issueCouponContext.getOriginIssueCouponRequest().getCityId()));
        issueCouponContext.setMtActualCityId(MapUtils.getInteger(dpCityId2MtCityIdMap, issueCouponContext.getOriginIssueCouponRequest().getActualCityId()));

        return issueCouponContext;
    }

    private AsyncDpInfoLoader buildConverter(ResourcePromotionIssueCouponContext issueCouponContext) {
        AsyncDpInfoLoader dpIdConverter = new AsyncDpInfoLoader(shopWrapperService, userWrapperService, ThreadPoolUtils.issueCouponPreExecutor.getExecutor());
        return dpIdConverter.loadMtShopId(issueCouponContext.getOriginIssueCouponRequest().getShopIdL())
                .loadUserMobile(issueCouponContext.getOriginIssueCouponRequest().getUserId())
                .loadMtUserId(issueCouponContext.getOriginIssueCouponRequest().getUserId())
                .batchLoadMtCityId(issueCouponContext.getOriginIssueCouponRequest().getCityId(),
                        issueCouponContext.getOriginIssueCouponRequest().getActualCityId());
    }


    private String getPhoneByUserId(long userId, boolean isMt) {
        if (isMt) {
            UserModel userModel = userWrapperService.getUserByIdWithMsg(userId);
            if (userModel != null) {
                return userModel.getMobile();
            }
        }
        return null;
    }
}
