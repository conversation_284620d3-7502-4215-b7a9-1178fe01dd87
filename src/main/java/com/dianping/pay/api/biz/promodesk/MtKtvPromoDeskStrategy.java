package com.dianping.pay.api.biz.promodesk;

import com.dianping.cat.Cat;
import com.dianping.pay.api.beans.DiscountRules;
import com.dianping.pay.api.beans.GetPromoDeskRequest;
import com.dianping.pay.api.entity.promodesk.CouponPromoTool;

/**
 * KTV优惠台加载策略
 * 返回：多个立减
 * 不返回：红包、积分、小黄条、抵用券、商户抵用券
 */
public class MtKtvPromoDeskStrategy extends KtvPromoDeskStrategy implements PromoDeskStrategy {

    @Override
    public CouponPromoTool assembleCouponPromoTool(GetPromoDeskRequest getPromoDeskRequest, DiscountRules discountRules) {
        Cat.logEvent("INVALID_METHOD_3", "com.dianping.pay.api.biz.promodesk.MtKtvPromoDeskStrategy.assembleCouponPromoTool(com.dianping.pay.api.beans.GetPromoDeskRequest,com.dianping.pay.api.beans.DiscountRules)");
        return null;
    }

    @Override
    public CouponPromoTool assembleShopCouponPromoTool(GetPromoDeskRequest getPromoDeskRequest, DiscountRules discountRules) {
        Cat.logEvent("INVALID_METHOD_1", "com.dianping.pay.api.biz.promodesk.MtKtvPromoDeskStrategy.assembleShopCouponPromoTool(com.dianping.pay.api.beans.GetPromoDeskRequest,com.dianping.pay.api.beans.DiscountRules)");
        return null;
    }
}
