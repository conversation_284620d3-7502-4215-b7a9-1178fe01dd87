package com.dianping.pay.api.biz.activity.newloader;

import com.dianping.cat.Cat;
import com.dianping.poi.areacommon.AreaCommonService;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

@Component
@Slf4j
public class CityBiz {

    @Resource(name = "areaCommonService")
    private AreaCommonService areaCommonService;

    public int transferDpCity2MtCity(int dpCityId){
        Cat.logEvent("INVALID_METHOD_5", "com.dianping.pay.api.biz.activity.newloader.CityBiz.transferDpCity2MtCity(int)");
        if(dpCityId <= 0){
            return 0;
        }
        try {
            Integer mtCityId = areaCommonService.getMtCityByDpCity(dpCityId);
            if (mtCityId != null) {
                return mtCityId;
            }
        } catch(Exception e) {
            log.error("getMtCityByDpCity error, dpCityId:{}", dpCityId, e);
        }
        return 0;
    }
}
