package com.dianping.pay.api.biz.activity;

import com.dianping.cat.Cat;
import com.dianping.pay.api.biz.activity.newloader.dto.CouponIssueActivityQueryContext;
import com.dianping.pay.api.exception.IssueCouponException;
import com.dianping.pay.api.util.JsonUtils;
import com.dianping.pay.promo.common.enums.coupon.UnifiedCouponGroupType;
import com.dianping.unified.coupon.manage.api.UnifiedCouponGroupQueryService;
import com.dianping.unified.coupon.manage.api.dto.UnifiedCouponGroupDTO;
import com.dianping.unified.coupon.manage.api.enums.UnifiedCouponManageResultCodeEnum;
import com.dianping.unified.coupon.manage.api.request.BatchLoadCouponGroupRequest;
import com.dianping.unified.coupon.manage.api.request.LoadCouponGroupRequest;
import com.dianping.unified.coupon.manage.api.response.UnifiedCouponManageResponse;
import com.google.common.collect.Sets;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.MapUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

import static com.dianping.pay.api.enums.GetCouponStatus.GET_COUPON_FAIL;

/**
 * Created by ivan on 2019/3/4.
 */
@Service("couponInfoBiz")
@Slf4j
public class CouponInfoBiz {

    @Autowired
    UnifiedCouponGroupQueryService unifiedCouponGroupQueryService;

    private static final int UNDEFINED_COUPON_GROUP_TYPE = 0;

    public boolean isShopCoupon(CouponIssueActivityQueryContext context) {
        String unifiedCouponGroupId = context.getUnifiedCouponGroupId() != null ? context.getUnifiedCouponGroupId() : String.valueOf(context.getCouponGroupId());
        int couponGroupType = getCouponGroupType(unifiedCouponGroupId);
        return couponGroupType == UnifiedCouponGroupType.shopCoupon.value || couponGroupType == UnifiedCouponGroupType.mtShopCoupon.value;
    }

    private int getCouponGroupType(String unifiedCouponGroupId) {
        LoadCouponGroupRequest loadCouponGroupRequest = new LoadCouponGroupRequest();
        loadCouponGroupRequest.setUnifiedCouponGroupId(unifiedCouponGroupId);
        try {
            UnifiedCouponManageResponse<UnifiedCouponGroupDTO> response = unifiedCouponGroupQueryService.loadCouponGroup(loadCouponGroupRequest);
            if (response.isSuccess() && response.getResultCode() == UnifiedCouponManageResultCodeEnum.Success.getCode() && response.getResult() != null) {
                return response.getResult().getCouponGroupType();
            }
            return UNDEFINED_COUPON_GROUP_TYPE;
        } catch (Exception e) {
            throw new IssueCouponException("unknown coupon type", GET_COUPON_FAIL);
        }
    }

    public boolean isAllShopCoupon(List<String> unifiedCouponGroupIds) {
        Cat.logEvent("INVALID_METHOD_1", "com.dianping.pay.api.biz.activity.CouponInfoBiz.isAllShopCoupon(java.util.List)");
        Map<String, UnifiedCouponGroupDTO> unifiedCouponGroupDTOMap = batchLoadCouponGroup(unifiedCouponGroupIds);
        if (MapUtils.isEmpty(unifiedCouponGroupDTOMap)) {
            return false;
        }
        for (Map.Entry<String, UnifiedCouponGroupDTO> entry : unifiedCouponGroupDTOMap.entrySet()) {
            int couponGroupType = entry.getValue().getCouponGroupType();
            if (couponGroupType != UnifiedCouponGroupType.shopCoupon.value && couponGroupType != UnifiedCouponGroupType.mtShopCoupon.value) {
                return false;
            }
        }
        return true;
    }

    public Map<String, UnifiedCouponGroupDTO> batchLoadCouponGroup(List<String> unifiedCouponGroupIds){
        BatchLoadCouponGroupRequest request = new BatchLoadCouponGroupRequest();
        request.setUnifiedCouponGroupIds(Sets.newHashSet(unifiedCouponGroupIds));
        UnifiedCouponManageResponse<Map<String, UnifiedCouponGroupDTO>> resp = unifiedCouponGroupQueryService.batchLoadCouponGroup(request);
        if (resp == null || !resp.isSuccess()) {
            log.warn("batchLoadCouponGroup failed. request: {}, resp: {}", JsonUtils.toJson(request), JsonUtils.toJson(resp));
            throw new IssueCouponException("batch loadCouponGroup fail", GET_COUPON_FAIL);
        }
        return resp.getResult();
    }
}
