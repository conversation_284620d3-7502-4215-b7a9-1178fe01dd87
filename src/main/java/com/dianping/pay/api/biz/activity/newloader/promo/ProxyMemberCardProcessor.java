package com.dianping.pay.api.biz.activity.newloader.promo;

import com.dianping.cat.Cat;
import com.dianping.lion.client.Lion;
import com.dianping.mobile.framework.datatypes.IMobileContext;
import com.dianping.pay.api.biz.activity.newloader.dto.CouponActivityContext;
import com.dianping.pay.api.biz.activity.newloader.dto.ProxyPromoContext;
import com.dianping.pay.api.constants.ProxyConstant;
import com.dianping.pay.api.enums.QueryShopCouponTypeEnum;
import com.dianping.pay.api.util.DotUtils;
import com.dianping.pay.api.util.LionConstants;
import com.dianping.pay.api.wrapper.ProxyPromotionWrapper;
import com.sankuai.nibmktproxy.queryclient.proxy.PromotionDTOResult;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.concurrent.Callable;
import java.util.concurrent.Future;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @date 2021/11/10
 */
@Component("proxyMemberCardProcessor")
@Slf4j
public class ProxyMemberCardProcessor extends AbstractPromoProcessor {

    @Autowired
    private ProxyPromotionWrapper proxyPromotionWrapper;

    @Override
    public void prePare(CouponActivityContext promoCtx, IMobileContext iMobileContext) {
        try {
            if (promoCtx.getQueryShopCouponType() != QueryShopCouponTypeEnum.BY_DEAL.getCode()) {
                return;
            }
            if (!promoCtx.isBeMemberPriceProxy()) {
                return;
            }
            DotUtils.dotForPromoProcessor(promoCtx, getPromoName());
            Future<PromotionDTOResult> future = ExecutorService.submit(new Callable<PromotionDTOResult>() {
                @Override
                public PromotionDTOResult call() throws Exception {
                    return getProxyPromotions(promoCtx, iMobileContext);
                }
            });
            promoCtx.setProxyMemberCardPromotionFuture(future);
        } catch (Exception e) {
            log.error("ProxyMemberCardProcessor# prepare error. ctx: {}", promoCtx, e);
        }
    }

    @Override
    public void loadPromo(CouponActivityContext promoCtx) {
        try {
            Future<PromotionDTOResult> proxyPromotionFuture = promoCtx.getProxyMemberCardPromotionFuture();
            int timeout = Lion.getIntValue(LionConstants.PROXY_PROMOTION_TIMEOUT, 500);
            if (proxyPromotionFuture != null) {
                promoCtx.setProxyMemberCardPromotionResult(proxyPromotionFuture.get(timeout, TimeUnit.MILLISECONDS));
            }
        } catch (Exception e) {
            log.error("ProxyMemberCardProcessor# loadPromo error. ctx: {}", promoCtx, e);
        }
    }

    private PromotionDTOResult getProxyPromotions(CouponActivityContext promoCtx, IMobileContext iMobileContext) {
        Cat.logEvent("INVALID_METHOD_1", "com.dianping.pay.api.biz.activity.newloader.promo.ProxyMemberCardProcessor.getProxyPromotions(CouponActivityContext,IMobileContext)");
        final ProxyPromoContext proxyPromoContext = new ProxyPromoContext();
        if (promoCtx.getQueryShopCouponType() == QueryShopCouponTypeEnum.BY_DEAL.getCode()) {
            proxyPromoContext.setBizCode(ProxyConstant.DEAL_BIZ_CODE);
        } else {
            proxyPromoContext.setBizCode(ProxyConstant.PEDICURE_BOOK_BIZCODE);
        }
        proxyPromoContext.setSceneId(ProxyConstant.MEMBER_CARD_SCENE_ID);
        proxyPromoContext.setPromoName(getPromoName());
        proxyPromoContext.setCouponQueryScene(false);
        return proxyPromotionWrapper.getProxyPromotions(promoCtx, iMobileContext, proxyPromoContext);
    }

    @Override
    public String getPromoName() {
        Cat.logEvent("INVALID_METHOD_1", "com.dianping.pay.api.biz.activity.newloader.promo.ProxyMemberCardProcessor.getPromoName()");
        return "proxyMemberCard";
    }
}
