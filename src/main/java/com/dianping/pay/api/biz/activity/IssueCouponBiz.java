package com.dianping.pay.api.biz.activity;

import com.alibaba.fastjson.JSON;
import com.dianping.api.common.enums.PageSourceEnum;
import com.dianping.api.common.util.DistributeLockUtils;
import com.dianping.api.constans.ApParamConstants;
import com.dianping.api.constans.CommonConstants;
import com.dianping.api.domain.ClientInfo;
import com.dianping.cat.Cat;
import com.dianping.cat.message.Transaction;
import com.dianping.cat.thread.pool.CatExecutorServiceTraceWrapper;
import com.dianping.combiz.spring.util.PropertiesLoaderSupportUtils;
import com.dianping.deal.idmapper.api.DealIdMapperService;
import com.dianping.deal.idmapper.api.dto.IdMapper;
import com.dianping.gmkt.coupon.common.api.dto.dynamicbindrule.CouponDynamicBindProduct;
import com.dianping.gmkt.coupon.common.api.dto.dynamicbindrule.CouponDynamicBindRuleInfo;
import com.dianping.gmkt.coupon.common.api.dto.dynamicbindrule.GroupDynamicBindRuleDTO;
import com.dianping.gmkt.coupon.common.api.dto.dynamicbindrule.GroupDynamicBindRuleInfo;
import com.dianping.gmkt.coupon.common.api.enums.CouponBusiness;
import com.dianping.gmkt.coupon.common.api.enums.CouponCommonTagEnum;
import com.dianping.gmkt.coupon.common.api.enums.CouponGroupExtraKeyEnum;
import com.dianping.gmkt.coupon.common.api.enums.IssueDetailSourceEnum;
import com.dianping.gmkt.coupon.common.api.enums.MerchantCouponProductType;
import com.dianping.gmkt.wave.api.dto.request.GetCouponRequest;
import com.dianping.gmkt.wave.api.dto.response.DrawResult;
import com.dianping.gmkt.wave.api.service.CouponCenterService;
import com.dianping.lion.client.Lion;
import com.dianping.pay.api.beans.IssueCouponActivity;
import com.dianping.pay.api.biz.activity.newloader.CouponIssueActivityQueryService;
import com.dianping.pay.api.biz.activity.newloader.dto.CouponIssueActivityQueryContext;
import com.dianping.pay.api.biz.activity.newloader.dto.CouponIssueMultiActivityQueryContext;
import com.dianping.pay.api.biz.discount.CouponBiz;
import com.dianping.pay.api.constants.CatEventConstants;
import com.dianping.pay.api.controller.IssueCouponController;
import com.dianping.pay.api.entity.issuecoupon.BeautyIssueCouponComponent;
import com.dianping.pay.api.entity.issuecoupon.BeautyIssueCouponTag;
import com.dianping.pay.api.entity.issuecoupon.IssueCouponComponent;
import com.dianping.pay.api.entity.issuecoupon.IssueCouponOption;
import com.dianping.pay.api.entity.issuecoupon.IssueCouponRequest;
import com.dianping.pay.api.entity.issuecoupon.IssueCouponTag;
import com.dianping.pay.api.entity.issuecoupon.IssueMultiCouponRequest;
import com.dianping.pay.api.entity.issuecoupon.NewIssueCouponUnit;
import com.dianping.pay.api.entity.issuecoupon.NewIssueDetailResult;
import com.dianping.pay.api.entity.issuecoupon.NewIssueMultiCouponContext;
import com.dianping.pay.api.entity.issuecoupon.ShopCartIssueMultiCouponContext;
import com.dianping.pay.api.entity.issuecoupon.UnifiedIssueDetailResult;
import com.dianping.pay.api.entity.issuecoupon.UnifiedIssueResult;
import com.dianping.pay.api.enums.IssueCouponOptionType;
import com.dianping.pay.api.enums.QualificationType;
import com.dianping.pay.api.exception.IssueCouponException;
import com.dianping.pay.api.util.BeautyIssueCouponUtils;
import com.dianping.pay.api.util.IssueCouponActivityComparator;
import com.dianping.pay.api.util.IssueCouponTypeUtils;
import com.dianping.pay.api.util.IssueCouponUtils;
import com.dianping.pay.api.util.JsonUtils;
import com.dianping.pay.api.util.LionConstants;
import com.dianping.pay.coupon.service.enums.CouponOperationResultCode;
import com.dianping.pay.promo.common.enums.UnifiedCouponTargetEnum;
import com.dianping.pay.promo.common.enums.User;
import com.dianping.pay.promo.common.enums.coupon.UnifiedCouponProductType;
import com.dianping.tgc.open.entity.ActivitySimpleDTO;
import com.dianping.tgc.open.entity.QuerySimpleResponseDTO;
import com.dianping.tgc.process.MerchantCouponIssueService;
import com.dianping.tgc.process.entity.CouponIssueResult;
import com.dianping.tgc.process.entity.PResponse;
import com.dianping.tgc.process.enums.IssueSourceChannel;
import com.dianping.tgc.process.enums.PlatformEnum;
import com.dianping.unified.coupon.issue.api.UnifiedCouponIssueTrustService;
import com.dianping.unified.coupon.issue.api.dto.UnifiedCouponIssueDetail;
import com.dianping.unified.coupon.issue.api.enums.ExtraParamEnum;
import com.dianping.unified.coupon.issue.api.request.UnifiedCouponIssueRequest;
import com.dianping.unified.coupon.issue.api.response.UnifiedCouponIssueResponse;
import com.dianping.unified.coupon.manage.api.dto.UnifiedCouponDTO;
import com.dianping.unified.coupon.manage.api.dto.UnifiedCouponGroupDTO;
import com.dianping.violet.enums.CouponRequestTypeEnum;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.meituan.mtrace.thread.pool.ExecutorServiceTraceWrapper;
import com.sankuai.mpmkt.coupon.issue.api.IssueCouponService;
import com.sankuai.mpmkt.coupon.issue.api.request.CouponIssueInfo;
import com.sankuai.mpmkt.coupon.issue.api.request.CouponIssueRequest;
import dianping.gmkt.wave.api.BizCode;
import dianping.gmkt.wave.api.CommonResponse;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang.Validate;
import org.apache.commons.lang3.math.NumberUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.concurrent.Callable;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Future;
import java.util.concurrent.SynchronousQueue;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;

import static com.dianping.pay.api.enums.GetCouponStatus.GET_COUPON_FAIL;

/**
 * Created by zhaochen on 2018/4/17.
 */
public class IssueCouponBiz {
    private static final Logger log = LoggerFactory.getLogger(IssueCouponController.class);

    //商家券记录领券来源的幂等前缀
    private static final String PREFIX = "mapi";

    //商家券外链投放
    private static final String EXTERNAL_LINK_PREFIX = "elc";

    @Autowired
    private CouponIssueActivityQueryService couponIssueActivityQueryService;
    @Autowired
    private CouponBiz couponBiz;
    @Autowired
    private UnifiedCouponIssueTrustService unifiedCouponIssueTrustService;
    @Autowired
    private MerchantCouponIssueService merchantCouponIssueService;
    @Autowired
    private CouponCenterService couponCenterService;
    @Autowired
    private DealIdMapperService dealIdMapperService;
    @Resource
    private IssueCouponService issueCouponService;

    public IssueCouponComponent toIssueCouponComponent(List<IssueCouponActivity> activities) {
        IssueCouponComponent component = new IssueCouponComponent();
        if (CollectionUtils.isNotEmpty(activities)) {
            Collections.sort(activities, new IssueCouponActivityComparator());

            for (IssueCouponActivity activity : activities) {
                IssueCouponOption couponOption = IssueCouponTypeUtils.getIssueCouponOption(activity);
                component.getCouponOptionList().add(couponOption);
                component.getPromotionTagList().add(activity.getPromotionTag());
            }
            fillCouponTags(component, activities);
        }
        return component;
    }

    public BeautyIssueCouponComponent toBeautyIssueCouponComponent(List<IssueCouponActivity> activities) {
        BeautyIssueCouponComponent component = new BeautyIssueCouponComponent();
        Transaction t = Cat.newTransaction("IssueCouponBiz", "toBeautyIssueCouponComponent");
        try {
            if (CollectionUtils.isNotEmpty(activities)) {
                List<IssueCouponActivity> issueCouponActivities = Lists.newArrayListWithExpectedSize(activities.size());
                for (IssueCouponActivity activity : activities) {
                    int merchantProductType = IssueCouponUtils.getMerchantCouponProductType(activity.getCouponGroup());
                    boolean isNewUserCoupon = IssueCouponUtils.isNewUserCoupon(activity.getCouponGroup());
                    if (isNewUserCoupon || merchantProductType == MerchantCouponProductType.CERTAIN_LABLE.getValue()) {
                        continue;
                    }
                    issueCouponActivities.add(activity);
                }
                Collections.sort(issueCouponActivities, new IssueCouponActivityComparator());
                component.setBeautyShopCouponTag("店铺抵用券");
                String secondTag = Lion.getStringValue("mapi-pay-promo-web.beautyissuecoupon.prepaid.text", "优先抵用尾款，尾款全部抵用后，可继续抵用预付金");
                component.setBeautyShopSecondTag(secondTag);
                for (IssueCouponActivity activity : issueCouponActivities) {
                    if (activity.getType() == IssueCouponOptionType.BEAUTY_EVENT.getCode()) {
                        component.getBeautyPlatformCouponOptionList().add(IssueCouponTypeUtils.getBeautyIssueCouponOption(activity));
                    } else {
                        component.getBeautyShopCouponOptionList().add(IssueCouponTypeUtils.getBeautyIssueCouponOption(activity));
                    }
                }
                fillBeautyCouponTags(component, issueCouponActivities);
            }
            t.setStatus(Transaction.SUCCESS);
        } catch (Exception e) {
            log.error("beauty issue coupon component build fail", e);
            t.setStatus(e);
        } finally {
            t.complete();
        }
        return component;
    }

    private void fillBeautyCouponTags(BeautyIssueCouponComponent component, List<IssueCouponActivity> activities) {
        List<IssueCouponActivity> platformActivities = new ArrayList<>(), shopActivities = new ArrayList<>();
        for (IssueCouponActivity activity : activities) {
            if (activity.getType() == IssueCouponOptionType.BEAUTY_EVENT.getCode()) {
                platformActivities.add(activity);
            } else {
                shopActivities.add(activity);
            }
        }
        if (CollectionUtils.isNotEmpty(platformActivities)) {
            String tagText = BeautyIssueCouponUtils.fillCouponTagText(platformActivities, true);
            if (StringUtils.isNotBlank(tagText)) {
                IssueCouponActivity issueCouponActivity = platformActivities.get(0);
                BeautyIssueCouponTag tag = new BeautyIssueCouponTag();
                tag.setIcon(issueCouponActivity.getIcon());
                tag.setTag(tagText);
                component.getBeautyCouponTagList().add(tag);
                component.setBeautyPlatformCouponTag(StringUtils.isNotEmpty(issueCouponActivity.getBeautyConfigStr()) ?
                        issueCouponActivity.getBeautyConfigStr() + "团购抵用券" : "丽人团购抵用券");
            }
        }
        if (CollectionUtils.isNotEmpty(shopActivities)) {
            String tagText = BeautyIssueCouponUtils.fillCouponTagText(shopActivities, false);
            if (StringUtils.isNotBlank(tagText)) {
                BeautyIssueCouponTag tag = new BeautyIssueCouponTag();
                tag.setIcon(shopActivities.get(0).getIcon());
                tag.setTag(tagText);
                component.getBeautyCouponTagList().add(tag);
            }
        }
    }

    /**
     * 最多展示两个
     * 券种类大于一种，按优先级每种类型券各展示一张（展示优先级：团购>预订>闪惠）
     */
    private void fillCouponTags(IssueCouponComponent component, List<IssueCouponActivity> activities) {
        int couponTagLimit = 0;
        int formerProductCode = 0;
        UnifiedCouponGroupDTO temp = null;
        for (IssueCouponActivity activity : activities) {
            UnifiedCouponGroupDTO couponGroup = activity.getCouponGroup();
            if (couponTagLimit == 2) {
                break;
            }
            if (formerProductCode == 0) {
                component.getCouponTagList().add(IssueCouponUtils.fillCouponTagText(couponGroup));
                formerProductCode = couponGroup.getProductCodeList().get(0);
                couponTagLimit++;
                continue;
            }
            if (formerProductCode == couponGroup.getProductCodeList().get(0)) {
                if (temp == null) {
                    temp = couponGroup;
                }
                continue;
            }
            component.getCouponTagList().add(IssueCouponUtils.fillCouponTagText(couponGroup));
            formerProductCode = couponGroup.getProductCodeList().get(0);
            couponTagLimit++;
        }
        if (couponTagLimit < 2 && temp != null) {
            component.getCouponTagList().add(IssueCouponUtils.fillCouponTagText(temp));
        }

        List<String> couponTagText = new ArrayList<String>();
        for (IssueCouponTag couponTag : component.getCouponTagList()) {
            couponTagText.add(couponTag.getCategory() + couponTag.getTag());
        }
        component.setTitle(StringUtils.join(couponTagText, "; "));
    }

    private IssueCouponRequest copyIssueCouponRequest(IssueCouponRequest input) {
        IssueCouponRequest result = new IssueCouponRequest();
        BeanUtils.copyProperties(input, result);
        return result;
    }

    public static final ExecutorService asyncExecutor=new ExecutorServiceTraceWrapper(new CatExecutorServiceTraceWrapper(new ThreadPoolExecutor(20, 100,
            60L, TimeUnit.SECONDS,
            new SynchronousQueue<Runnable>(),new ThreadPoolExecutor.CallerRunsPolicy())));

    private List<UnifiedIssueDetailResult> issueMultiCoupon(IssueMultiCouponRequest issueMultiCouponRequest, Map<Integer, IssueCouponActivity>  activitiesMap) {
        Set<String> unifiedCouponGroupIds = Sets.newHashSet(issueMultiCouponRequest.getUnifiedCouponGroupIds());
        Map<Integer, Future<UnifiedIssueDetailResult>> futureMap = Maps.newHashMapWithExpectedSize(unifiedCouponGroupIds.size());
        List<UnifiedIssueDetailResult> result = Lists.newArrayListWithExpectedSize(unifiedCouponGroupIds.size());

        for (String unifiedCouponGroupId : unifiedCouponGroupIds) {
            int couponOptionId = NumberUtils.toInt(unifiedCouponGroupId);
            final IssueCouponActivity activity = MapUtils.isNotEmpty(activitiesMap) ? activitiesMap.get(couponOptionId) : null;
            final IssueCouponRequest issueCouponRequest = copyIssueCouponRequest(issueMultiCouponRequest.getIssueCouponRequest());
            issueCouponRequest.setCouponOptionId(couponOptionId);
            issueCouponRequest.setStringCouponOptionId(unifiedCouponGroupId);
            // TODO 先简单实现
            futureMap.put(couponOptionId, asyncExecutor.submit(new Callable<UnifiedIssueDetailResult>() {
                @Override
                public UnifiedIssueDetailResult call() throws Exception {
                    return issueCouponWithWrapResponse(issueCouponRequest, activity);
                }
            }));
        }
        for (Map.Entry<Integer, Future<UnifiedIssueDetailResult>> entry : futureMap.entrySet()) {
            try {
                UnifiedIssueDetailResult detailResult = entry.getValue().get(1000, TimeUnit.MILLISECONDS);
                result.add(detailResult);
            } catch (Exception e) {
                UnifiedIssueDetailResult detailResult = new UnifiedIssueDetailResult();
                detailResult.setSuccess(false);
                detailResult.setResultMessage("系统异常");
                detailResult.setUnifiedCouponGroupId(String.valueOf(entry.getKey()));
                result.add(detailResult);
            }
        }
        return result;
    }

    private UnifiedIssueDetailResult issueCouponWithWrapResponse(IssueCouponRequest issueCouponRequest, IssueCouponActivity activity) {
        UnifiedIssueDetailResult detailResult =  new UnifiedIssueDetailResult();
        try {
            UnifiedIssueResult result = issueCoupon(issueCouponRequest, activity, null);
            detailResult.setSuccess(true);
            detailResult.setBeginTime(result.getBeginTime());
            detailResult.setEndTime(result.getEndTime());
            detailResult.setCouponGroupName(result.getCouponGroupName());
            detailResult.setUnifiedCouponId(result.getUnifiedCouponId());
            detailResult.setUnifiedCouponGroupId(String.valueOf(result.getCouponGroupId()));
            detailResult.setToastMsg(result.getToastMsg());
        } catch (IssueCouponException e) {
            detailResult.setSuccess(false);
            detailResult.setUnifiedCouponId(String.valueOf(issueCouponRequest.getCouponOptionId()));
            detailResult.setResultMessage(e.getMessage());
        } catch (Exception e) {
            detailResult.setSuccess(false);
            detailResult.setUnifiedCouponId(String.valueOf(issueCouponRequest.getCouponOptionId()));
            detailResult.setResultMessage("系统异常");
        }
        return detailResult;
    }

    private NewIssueDetailResult issueShopCouponWithWrapResponse(NewIssueMultiCouponContext context, String unitId, String shopCouponGroupId) {
        try {
            UnifiedIssueResult result = issueShopCoupon(context, unitId, shopCouponGroupId);
            NewIssueDetailResult detailResult =  new NewIssueDetailResult(unitId, true, null);
            detailResult.setCouponGroupName(result.getCouponGroupName());
            detailResult.setUnifiedCouponId(result.getUnifiedCouponId());
            detailResult.setUnifiedCouponGroupId(shopCouponGroupId);
            return detailResult;
        } catch (IssueCouponException e) {
            Cat.logEvent(CatEventConstants.ISSUE_MULITY_FAIL_DETAIL, "shop-" + (e.getCode() == 2 ? "reachUserMaxLimt" : "bizFail"));
            log.error("issueShopCouponWithWrapResponse IssueCouponException, context:{}, unitId:{}, shopCouponGroupId:{}", JsonUtils.toJson(context), unitId, shopCouponGroupId, e);
            NewIssueDetailResult newIssueDetailResult = new NewIssueDetailResult(unitId, false, e.getMessage());
            newIssueDetailResult.setUnifiedCouponId(shopCouponGroupId);
            return newIssueDetailResult;
        } catch (Exception e) {
            Cat.logEvent(CatEventConstants.ISSUE_MULITY_FAIL_DETAIL, "shop-systemException");
            log.error("issueShopCouponWithWrapResponse Exception, context:{}, unitId:{}, shopCouponGroupId:{}", JsonUtils.toJson(context), unitId, shopCouponGroupId, e);
            NewIssueDetailResult newIssueDetailResult = new NewIssueDetailResult(unitId, false, "系统异常");
            newIssueDetailResult.setUnifiedCouponId(shopCouponGroupId);
            return newIssueDetailResult;
        }
    }

    private NewIssueDetailResult issueLqgCouponWithWrapResponse(NewIssueMultiCouponContext context, String unitId, String lqgCouponGroupId) {
        Cat.logEvent("INVALID_METHOD_4", "com.dianping.pay.api.biz.activity.IssueCouponBiz.issueLqgCouponWithWrapResponse(com.dianping.pay.api.entity.issuecoupon.NewIssueMultiCouponContext,java.lang.String,java.lang.String)");
        try {
            UnifiedIssueResult result = issueLqgCoupon(context, unitId, lqgCouponGroupId);
            NewIssueDetailResult detailResult =  new NewIssueDetailResult(unitId, true, null);
            detailResult.setCouponGroupName(result.getCouponGroupName());
            detailResult.setUnifiedCouponId(result.getUnifiedCouponId());
            detailResult.setUnifiedCouponGroupId(lqgCouponGroupId);
            return detailResult;
        } catch (IssueCouponException e) {
            Cat.logEvent(CatEventConstants.ISSUE_MULITY_FAIL_DETAIL, "lqg-" + (e.getCode() == 2 ? "reachUserMaxLimt" : "bizFail"));
            log.error("issueLqgCouponWithWrapResponse Exception, context:{}, unitId:{}, lqgCouponGroupId:{}", JsonUtils.toJson(context), unitId, lqgCouponGroupId, e);
            NewIssueDetailResult newIssueDetailResult = new NewIssueDetailResult(unitId, false, e.getMessage());
            newIssueDetailResult.setUnifiedCouponId(lqgCouponGroupId);
            return newIssueDetailResult;
        } catch (Exception e) {
            Cat.logEvent(CatEventConstants.ISSUE_MULITY_FAIL_DETAIL, "lqg-systemException");
            log.error("issueLqgCouponWithWrapResponse Exception, context:{}, unitId:{}, lqgCouponGroupId:{}", JsonUtils.toJson(context), unitId, lqgCouponGroupId, e);
            NewIssueDetailResult newIssueDetailResult = new NewIssueDetailResult(unitId, false, "系统异常");
            newIssueDetailResult.setUnifiedCouponId(lqgCouponGroupId);
            return newIssueDetailResult;
        }
    }

    @Resource
    private DistributeLockUtils distributeLockUtils;

    private UnifiedIssueResult issueCoupon(IssueCouponRequest issueCouponRequest, IssueCouponActivity activity, Map<String, String> riskMap) {
        try {
            UnifiedCouponGroupDTO couponGroupDTO2 = couponBiz.queryCouponGroup(issueCouponRequest.getCouponOptionId());
            if (couponGroupDTO2 == null) {
                throw new IssueCouponException("券配置不存在", 1);
            }
            boolean isLQG = MapUtils.getBooleanValue(couponGroupDTO2.getExtraInfoMap(), CouponGroupExtraKeyEnum.lingquangou.name(), false);
            if (activity == null && !isLQG) {
                throw new IssueCouponException("活动不存在", 1);
            }
            if (isLQG) {
                //sxf 是否可以考虑加一个校验当前的商品shopid是否在召回里
                fillDynamicParams(issueCouponRequest, couponGroupDTO2);
                String unifiedCouponId = couponBiz.issueLQGCoupon(couponGroupDTO2.getCouponGroupId() != 0 ? String.valueOf(couponGroupDTO2.getCouponGroupId()) : couponGroupDTO2.getUnifiedCouponGroupId(), issueCouponRequest);
                UnifiedIssueResult result = new UnifiedIssueResult();
                result.setCouponGroupId(issueCouponRequest.getCouponOptionId());
                result.setUnifiedCouponId(unifiedCouponId);
                result.setCouponGroupName(couponGroupDTO2.getCouponGroupName());
                result.setToastMsg(buildToastMsg(unifiedCouponId, couponGroupDTO2));
                return result;
            }

            if (couponGroupDTO2.getTarget() == UnifiedCouponTargetEnum.meituan.getCode()) {
                int issuedCount = couponBiz.queryIssuedCount(issueCouponRequest.getUserId(), activity.getCouponGroupId(), issueCouponRequest.isDpClient());
                if (issuedCount > 0) {
                    throw new IssueCouponException("您已经领过啦", 2);
                }
            } else {
                List<UnifiedCouponDTO> unifiedCouponDTOs = couponBiz.queryIssuedCouponList(issueCouponRequest.getUserId(), activity.getCouponGroupId(), issueCouponRequest.isDpClient());
                if (activity.getQualificationType() == QualificationType.NOT_USE_ISSUE_QUALIFICATION.getCode()) {
                    if (IssueCouponUtils.canUseCoupon(unifiedCouponDTOs)) {
                        throw new IssueCouponException("您已经领过啦", 2);
                    }
                } else {
                    if (!IssueCouponTypeUtils.allowGetCoupon(unifiedCouponDTOs)) {
                        throw new IssueCouponException("您已经领过啦", 2);
                    }
                }
            }

            if (activity.getType() == IssueCouponOptionType.DP_SHOP_EVENT.getCode() || activity.getType() == IssueCouponOptionType.MT_SHOP_EVENT.getCode() ||
                    activity.getType() == IssueCouponOptionType.SHOP_CART_EVENT.getCode()) {
                com.dianping.tgc.process.entity.IssueCouponRequest tgcIssueCouponRequest = generateTgcIssueCouponRequest(issueCouponRequest, activity);
                // 做一下并发校验
                // 如果触发了频次限制就直接返回
                if (Lion.getBooleanValue("mapi-pay-promo-web.need.distribute.lock", false) && !distributeLockUtils.tryLock(tgcIssueCouponRequest.getUserId(), tgcIssueCouponRequest.getActivityId())) {
                    log.warn("issue coupon too frequency. userId: {}", tgcIssueCouponRequest.getUserId());
                    Cat.logEvent("IssueCoupon", "FREQUENCY");
                    throw new IssueCouponException("领取失败", GET_COUPON_FAIL);
                }


                PResponse<CouponIssueResult> response = merchantCouponIssueService.issueMerchantCouponToSingleUser(tgcIssueCouponRequest);

                if (response != null && response.isSuccess() && response.getResult() != null) {
                    CouponIssueResult issueResult = response.getResult();
                    UnifiedIssueResult result = new UnifiedIssueResult();
                    result.setBeginTime(issueResult.getBeginTime());
                    result.setEndTime(issueResult.getEndTime());
                    result.setCouponGroupId(issueCouponRequest.getCouponOptionId());
                    result.setUnifiedCouponId(issueResult.getUnifiedCouponId());
                    result.setCouponGroupName(issueResult.getCouponGroupName());
                    return result;
                } else {
                    log.warn(String.format("doIssueCoupon return fail: request %s, response %s", tgcIssueCouponRequest, JSON.toJSONString(response)));
                }
            } else if (activity.getType() == IssueCouponOptionType.DP_PLATFORM_EVENT.getCode()
                    || activity.getType() == IssueCouponOptionType.BEAUTY_EVENT.getCode()) {
                UnifiedCouponIssueRequest remoteIssueRequest = new UnifiedCouponIssueRequest();
                remoteIssueRequest.setOperator("mapi-pay-promo-web");
                remoteIssueRequest.setOperationToken(PropertiesLoaderSupportUtils.getProperty("pay-promo-attribute-service.gift.shanhuiCouponToken", ""));
                remoteIssueRequest.setUserId(issueCouponRequest.getUserId());
                ArrayList<Integer> couponGroupIdList = Lists.newArrayList(activity.getCouponGroupId());
                remoteIssueRequest.setCouponGroupIdList(couponGroupIdList);
                remoteIssueRequest.setDoubtCouponGroupIdList(couponGroupIdList);
                remoteIssueRequest.setBlockCouponGroupIdList(couponGroupIdList);
                UnifiedCouponIssueResponse remoteResponse = unifiedCouponIssueTrustService.issueTrustCoupon(remoteIssueRequest, null);
                if (remoteResponse != null
                        && remoteResponse.isSuccess()
                        && remoteResponse.getResult() != null
                        && CollectionUtils.isNotEmpty(remoteResponse.getResult().getResult())) {
                    UnifiedCouponIssueDetail couponIssueDetail = remoteResponse.getResult().getResult().get(0);
                    if (couponIssueDetail.getResultCode() == CouponOperationResultCode.SUCCESS.code) {
                        UnifiedIssueResult result = new UnifiedIssueResult();
                        result.setBeginTime(couponIssueDetail.getBeginTime());
                        result.setEndTime(couponIssueDetail.getEndTime());
                        result.setCouponGroupId(couponIssueDetail.getCouponGroupId());
                        result.setUnifiedCouponId(couponIssueDetail.getUnifiedCouponId());
                        result.setCouponGroupName(couponIssueDetail.getCouponGroupName());
                        return result;
                    }
                } else {
                    log.warn(String.format("doIssueCoupon failed: request:%s, remoteResponse:%s", remoteIssueRequest, JSON.toJSONString(remoteResponse)));
                }
            } else if (IssueCouponOptionType.EXTERNAL_LINK_EVENT.getCode() == activity.getType()) {
                return issueExternalLinkCoupon(issueCouponRequest, activity, couponGroupDTO2, riskMap);
            } else {
                throw new IllegalArgumentException("活动类型错误");
            }
        } catch (IssueCouponException e) {
            log.warn(String.format("doIssueCoupon failed: request :%s, msg:%s ", issueCouponRequest, e.getMessage()), e);
            throw e;
        } catch (Exception e) {
            log.warn(String.format("doIssueCoupon error: request :%s, msg:%s ", issueCouponRequest, e.getMessage()), e);
        }
        throw new IssueCouponException("领取失败", GET_COUPON_FAIL);
    }

    private String buildToastMsg(String unifiedCouponId, UnifiedCouponGroupDTO couponGroupDTO2) {
        String couponCommonTag = MapUtils.getString(couponGroupDTO2.getExtraInfoMap(), CouponGroupExtraKeyEnum.couponCommonTag.name());
        if (!CouponCommonTagEnum.beNormandyCoupon(couponCommonTag)) {
            return null;
        }
        if (StringUtils.isNotBlank(unifiedCouponId)) {
            Cat.logEvent("CouponIssue", couponCommonTag, "0", JsonUtils.toJson(couponGroupDTO2));
            return "已为您领取百亿补贴专属优惠券";
        }else {
            Cat.logEvent("CouponIssue", couponCommonTag, "-1", JsonUtils.toJson(couponGroupDTO2));
            return "领券失败，请稍后重试";
        }

    }

    private void fillDynamicParams(IssueCouponRequest issueCouponRequest, UnifiedCouponGroupDTO couponGroupDTO2) {
        String couponCommonTag = MapUtils.getString(couponGroupDTO2.getExtraInfoMap(), CouponGroupExtraKeyEnum.couponCommonTag.name());
        //诺曼低券 & 医美预付才加动态参数
        if (!CouponCommonTagEnum.beNormandyCoupon(couponCommonTag) || !Objects.equals(PageSourceEnum.BeautyMedicalProductDetail.code, issueCouponRequest.getCouponPageSource())) {
            return;
        }
        Map<String, String> apParams = Optional.ofNullable(issueCouponRequest.getApParams()).orElse(Maps.newHashMap());
        if (StringUtils.isNotBlank(issueCouponRequest.getUuid())) {
            apParams.put(ApParamConstants.UUID.getCode(), issueCouponRequest.getUuid());
        }
        if (issueCouponRequest.getProductId() > 0) {
            apParams.put(ApParamConstants.SKU_ID.getCode(), String.valueOf(issueCouponRequest.getProductId()));
            apParams.put(ApParamConstants.ROW_ID.getCode(), String.valueOf(issueCouponRequest.getProductId()));
        }
        if (issueCouponRequest.getShopIdL() > 0) {
            apParams.put(ApParamConstants.SHOP_ID.getCode(), String.valueOf(issueCouponRequest.getShopIdL()));
        }
        apParams.put(ApParamConstants.PAGE_SOURCE.getCode(), "0");
        issueCouponRequest.setApParams(apParams);

        Map<String, String> commonParams = Optional.ofNullable(issueCouponRequest.getCommonParams()).orElse(Maps.newHashMap());
        if (StringUtils.isNotBlank(issueCouponRequest.getUuid())) {
            commonParams.put(CommonConstants.DEVICE_ID, issueCouponRequest.getUuid());
        }
        issueCouponRequest.setCommonParams(commonParams);

        Map<String, String> extraParams = Optional.ofNullable(issueCouponRequest.getExtraParams()).orElse(Maps.newHashMap());
        if (hasDynamicProductLimit(couponGroupDTO2, CouponBusiness.MT_PREPAY.getCode())){
            CouponDynamicBindRuleInfo couponDynamicBindRuleInfo = new CouponDynamicBindRuleInfo();
            CouponDynamicBindProduct mtCouponDynamicBindProduct = new CouponDynamicBindProduct();
            mtCouponDynamicBindProduct.setProductId(issueCouponRequest.getProductId());
            mtCouponDynamicBindProduct.setType(UnifiedCouponProductType.PRODUCTID.code);
            mtCouponDynamicBindProduct.setProductCode(CouponBusiness.MT_PREPAY.getCode());

            CouponDynamicBindProduct dpCouponDynamicBindProduct = new CouponDynamicBindProduct();
            dpCouponDynamicBindProduct.setProductId(issueCouponRequest.getProductId());
            dpCouponDynamicBindProduct.setType(UnifiedCouponProductType.PRODUCTID.code);
            dpCouponDynamicBindProduct.setProductCode(CouponBusiness.DP_PREPAY.getCode());
            couponDynamicBindRuleInfo.setDynamicBindProductList(Lists.newArrayList(mtCouponDynamicBindProduct,dpCouponDynamicBindProduct));
            extraParams.put(ExtraParamEnum.couponDynamicRule.getKey(), JsonUtils.toJson(couponDynamicBindRuleInfo));
        }
        issueCouponRequest.setExtraParams(extraParams);

    }

    public static boolean hasDynamicProductLimit(UnifiedCouponGroupDTO couponGroupDto, int productCode){
        String groupDynamicBindRuleInfoStr = MapUtils.getString(couponGroupDto.getExtraInfoMap(), CouponGroupExtraKeyEnum.groupDynamicBindRuleInfo.name());
        if(StringUtils.isBlank(groupDynamicBindRuleInfoStr)){//无动态规则限制
            return false;
        }
        GroupDynamicBindRuleInfo groupDynamicBindRuleInfo = JsonUtils.toObject(groupDynamicBindRuleInfoStr, GroupDynamicBindRuleInfo.class);
        if(groupDynamicBindRuleInfo == null){
           return false;
        }

        Map<Integer, GroupDynamicBindRuleDTO> businessDynamicBindRuleMap = groupDynamicBindRuleInfo.getBusinessDynamicBindRuleMap();
        if(MapUtils.isEmpty(businessDynamicBindRuleMap)){
            return false;
        }
        GroupDynamicBindRuleDTO groupDynamicBindRuleDTO = businessDynamicBindRuleMap.get(productCode);
        return groupDynamicBindRuleDTO.isDynamicBindProduct();
    }

    private UnifiedIssueResult issueShopCoupon(NewIssueMultiCouponContext context, String unitId, String shopCouponGroupId) {
        if(context.getUserCouponAvailableCountMap().get(shopCouponGroupId) != null && context.getUserCouponAvailableCountMap().get(shopCouponGroupId) >= Lion.getInt(LionConstants.APP_KEY, LionConstants.SHOP_COUPON_AVAILABLE_COUNT, 20)) {
            throw new IssueCouponException("用户已领券数量达到最大限制", 2);
        }
        try {
            com.dianping.tgc.process.entity.IssueCouponRequest tgcIssueCouponRequest = generateTgcIssueCouponRequestNew(context, unitId, shopCouponGroupId);
            PResponse<CouponIssueResult> response = merchantCouponIssueService.issueMerchantCouponToSingleUser(tgcIssueCouponRequest);
            if (response != null && response.isSuccess() && response.getResult() != null) {
                CouponIssueResult issueResult = response.getResult();
                UnifiedIssueResult result = new UnifiedIssueResult();
                result.setBeginTime(issueResult.getBeginTime());
                result.setEndTime(issueResult.getEndTime());
                result.setCouponGroupId(Integer.parseInt(shopCouponGroupId));
                result.setUnifiedCouponId(issueResult.getUnifiedCouponId());
                result.setCouponGroupName(issueResult.getCouponGroupName());
                return result;
            } else {
                log.warn("doIssueCoupon return fail: context:{}, shopCouponGroupId:{}, response:{}", context, shopCouponGroupId, JSON.toJSONString(response));
            }
        } catch (IssueCouponException e) {
            log.error("doIssueCoupon failed: context:{}, shopCouponGroupId:{}", context, shopCouponGroupId, e);
            throw e;
        } catch (Exception e) {
            log.error("doIssueCoupon error: context:{}, shopCouponGroupId:{}", context, shopCouponGroupId, e);
        }
        throw new IssueCouponException("领取失败", GET_COUPON_FAIL);
    }

    public UnifiedIssueResult multiIssueLqgCoupon(NewIssueMultiCouponContext context, String lqgCouponGroupId) {
        Cat.logEvent("INVALID_METHOD_5", "com.dianping.pay.api.biz.activity.IssueCouponBiz.multiIssueLqgCoupon(com.dianping.pay.api.entity.issuecoupon.NewIssueMultiCouponContext,java.lang.String)");
        //领券购只允许领取一张
        if(context.getUserCouponAvailableCountMap().get(lqgCouponGroupId) != null && context.getUserCouponAvailableCountMap().get(lqgCouponGroupId) >= 1) {
            throw new IssueCouponException("用户已领取过", 2);
        }
        CouponIssueRequest req = new CouponIssueRequest();
        req.setUserId(context.getUserId());
        req.setUserType(context.isMt() ? User.MT.getFlag() : User.DP.getFlag());
        CouponIssueInfo couponIssueInfo = new CouponIssueInfo();
        couponIssueInfo.setCouponGroupId(lqgCouponGroupId);
        req.setCouponGroupInfo(Lists.newArrayList(couponIssueInfo));
        req.setOperator("mapi-pay-promo-web");
        req.setIssueSource("DAO_DIAN_LQG");
        try {
            UnifiedCouponIssueResponse response = issueCouponService.issueCouponsToUser(req, null);
            if (response.isSuccess() && response.getResult() != null && response.getResult().getIssueSuccessQuantity() > 0) {
                UnifiedCouponIssueDetail issueResult = response.getResult().getResult().get(0);
                UnifiedIssueResult result = new UnifiedIssueResult();
                result.setBeginTime(issueResult.getBeginTime());
                result.setEndTime(issueResult.getEndTime());
                result.setCouponGroupId(Integer.parseInt(lqgCouponGroupId));
                result.setUnifiedCouponId(issueResult.getUnifiedCouponId());
                result.setCouponGroupName(issueResult.getCouponGroupName());
                return result;
            } else {
                log.warn("issueLQGCoupon fail, request:{}, response:{}", req, response);
            }
        } catch (Exception e) {
            log.error("issueCouponsToUser error", e);
        }
        throw new IssueCouponException("领取失败", GET_COUPON_FAIL);
    }

    public UnifiedIssueResult issueLqgCoupon(NewIssueMultiCouponContext context, String unitId, String lqgCouponGroupId) {
        Cat.logEvent("INVALID_METHOD_4", "com.dianping.pay.api.biz.activity.IssueCouponBiz.issueLqgCoupon(com.dianping.pay.api.entity.issuecoupon.NewIssueMultiCouponContext,java.lang.String,java.lang.String)");
        //领券购只允许领取一张
        if(context.getUserCouponAvailableCountMap().get(lqgCouponGroupId) != null && context.getUserCouponAvailableCountMap().get(lqgCouponGroupId) >= 1) {
            throw new IssueCouponException("用户已领取过", 2);
        }
        CouponIssueRequest req = new CouponIssueRequest();
        req.setUserId(context.getUserId());
        req.setUserType(context.isMt() ? User.MT.getFlag() : User.DP.getFlag());
        CouponIssueInfo couponIssueInfo = new CouponIssueInfo();
        couponIssueInfo.setCouponGroupId(lqgCouponGroupId);
        req.setCouponGroupInfo(Lists.newArrayList(couponIssueInfo));
        req.setOperator("mapi-pay-promo-web");
        req.setIssueSource("DAO_DIAN_LQG");
        try {
            UnifiedCouponIssueResponse response = issueCouponService.issueCouponsToUser(req, null);
            if (response.isSuccess() && response.getResult() != null && response.getResult().getIssueSuccessQuantity() > 0) {
                UnifiedCouponIssueDetail issueResult = response.getResult().getResult().get(0);
                UnifiedIssueResult result = new UnifiedIssueResult();
                result.setBeginTime(issueResult.getBeginTime());
                result.setEndTime(issueResult.getEndTime());
                result.setCouponGroupId(Integer.parseInt(lqgCouponGroupId));
                result.setUnifiedCouponId(issueResult.getUnifiedCouponId());
                result.setCouponGroupName(issueResult.getCouponGroupName());
                return result;
            } else {
                log.warn("issueLQGCoupon fail, request:{}, response:{}", req, response);
            }
        } catch (Exception e) {
            log.error("issueCouponsToUser error", e);
        }
        throw new IssueCouponException("领取失败", GET_COUPON_FAIL);
    }

    public int doIssueCoupon(IssueCouponRequest issueCouponRequest) {
        IssueCouponActivity activity = queryMatchedActivity(issueCouponRequest);
        UnifiedIssueResult issueResult = issueCoupon(issueCouponRequest, activity, null);
        return issueCouponRequest != null ? issueResult.getCouponGroupId() : 0;
    }

    public UnifiedIssueResult doIssueCoupon(IssueCouponRequest issueCouponRequest, CouponIssueActivityQueryContext context) {
        IssueCouponActivity activity = queryMatchedUnifiedActivity(context);
        return issueCoupon(issueCouponRequest, activity, null);
    }


    public List<UnifiedIssueDetailResult> doIssueMultiCoupon(IssueMultiCouponRequest issueMultiCouponRequest, CouponIssueMultiActivityQueryContext context) {
        List<IssueCouponActivity> activities = couponIssueActivityQueryService.queryActivities(context.getCouponIssueActivityQueryContext());
        Map<Integer, IssueCouponActivity> activitiesMap = Maps.newHashMap();
        if (CollectionUtils.isNotEmpty(activities)) {
            for (IssueCouponActivity activity : activities) {
                activitiesMap.put(activity.getCouponGroupId(), activity);
            }
        }
        return issueMultiCoupon(issueMultiCouponRequest, activitiesMap);
    }

    public UnifiedIssueResult issuePlatformCoupon(CouponIssueActivityQueryContext context, IssueCouponRequest issueCouponRequest) {
        log.info(String.format("[IssueCouponBiz][issuePlatformCoupon][context=%s]", context));
        try {
            Validate.notNull(context, "invalidate context");
            Validate.notNull(context.getUnifiedCouponGroupId(), "invalidate unifiedCouponGroupId");
            try {
                UnifiedCouponGroupDTO couponGroupDTO = couponBiz.queryCouponGroup(Integer.valueOf(context.getUnifiedCouponGroupId()));
                if (couponGroupDTO != null && MapUtils.isNotEmpty(couponGroupDTO.getExtraInfoMap())) {
                    boolean isLQG = MapUtils.getBooleanValue(couponGroupDTO.getExtraInfoMap(), CouponGroupExtraKeyEnum.lingquangou.name(), false);
                    if (isLQG) {
                        Cat.logEvent("IssuePlatformCoupon", "LQG");
                        fillDynamicParams(issueCouponRequest, couponGroupDTO);
                        String unifiedCouponId = couponBiz.issueLQGCoupon(context.getUnifiedCouponGroupId(), issueCouponRequest);
                        if (StringUtils.isEmpty(unifiedCouponId)) {
                            throw new IssueCouponException("领取失败", GET_COUPON_FAIL);
                        }
                        UnifiedIssueResult unifiedIssueResult = new UnifiedIssueResult();
                        unifiedIssueResult.setUnifiedCouponId(unifiedCouponId);
                        return unifiedIssueResult;
                    }
                }
            } catch (Exception e) {
                log.error("process platformCoupon issue error,couponGroupId:" + context.getUnifiedCouponGroupId(), e);
            }
            GetCouponRequest request = generateGetCouponRequest(context,issueCouponRequest);
            UnifiedIssueResult unifiedIssueResult = new UnifiedIssueResult();
            Cat.logEvent("IssuePlatformCoupon", "IssueCenter");
            CommonResponse<DrawResult> response = couponCenterService.getCoupon(request);
            if (response != null && response.getBizCode() != null && response.getBizCode().getCode() == BizCode.success.getCode() && response.getData().isSucc()) {
                unifiedIssueResult.setUnifiedCouponId(response.getData().getUnifiedCouponId());
                return unifiedIssueResult;
            } else if (response != null && response.getData() != null) {
                log.warn(String.format("issuePlatformCoupon error: request :%s, msg: %s", request, response.getData().getFailReason()));
            }
        } catch (Exception e) {
            log.error("issuePlatformCoupon error: context :%s", context, e);
        }
        throw new IssueCouponException("领取失败", GET_COUPON_FAIL);
    }

    private GetCouponRequest generateGetCouponRequest(CouponIssueActivityQueryContext context, IssueCouponRequest issueCouponRequest) {
        GetCouponRequest request = new GetCouponRequest();
        request.setUserId(issueCouponRequest.getUserId());
        request.setChannel(context.getIssueChannel());
        request.setCouponCode(context.getUnifiedCouponGroupId());
        request.setCx(context.getCx());
        request.setPlatform(context.isDpClient() ? com.dianping.gmkt.wave.api.enums.PlatformEnum.DP.getCode() : com.dianping.gmkt.wave.api.enums.PlatformEnum.MT.getCode());
        return request;
    }

    private com.dianping.tgc.process.entity.IssueCouponRequest generateTgcIssueCouponRequest(IssueCouponRequest issueCouponRequest, IssueCouponActivity activity) {
        String uniqueKey = IssueCouponUtils.getUniqueKey(issueCouponRequest);
        String issueDetailSource = null;
        if (issueCouponRequest.getIssueDetailSourceCode() != null) {
            IssueDetailSourceEnum issueDetailSourceEnum = IssueDetailSourceEnum.getByCode(issueCouponRequest.getIssueDetailSourceCode());
            issueDetailSource = issueDetailSourceEnum == null ? null : issueDetailSourceEnum.getName();
        }
        return com.dianping.tgc.process.entity.IssueCouponRequest.IssueCouponRequestBuilder.newIssueCouponRequest()
                .withIdempotentKey(PREFIX,uniqueKey)//幂等键，必须能够体现本次发券的唯一性，再次发券时，由此键可知两次发券调用是因为同一件事
                .withActivityId(activity.getActivityId()) //或者用activityId 两者选其一,如果两个都调了，用后传的那个
                .withUserId(String.valueOf(issueCouponRequest.getUserId()))
                .withPlatform(issueCouponRequest.isDpClient() ? PlatformEnum.DP : PlatformEnum.MT)
                .withIssueSourceChannel(IssueSourceChannel.MERCHANT_PLATFORM)
                .withoutRiskControl()
                .withIssueDetailSource(issueDetailSource)
                .build();
    }

    private com.dianping.tgc.process.entity.IssueCouponRequest generateTgcIssueCouponRequestNew(NewIssueMultiCouponContext context, String unitId, String shopCouponGroupId) {
        String uniqueKey = IssueCouponUtils.getUniqueKey(context, unitId, shopCouponGroupId);
        String issueDetailSource = null;
        if (context.getIssueDetailSourceCode() != null) {
            IssueDetailSourceEnum issueDetailSourceEnum = IssueDetailSourceEnum.getByCode(context.getIssueDetailSourceCode());
            issueDetailSource = issueDetailSourceEnum == null ? null : issueDetailSourceEnum.getName();
        }
        ActivitySimpleDTO activitySimpleDTO = context.getShopCouponActivityMap().get(shopCouponGroupId);
        return com.dianping.tgc.process.entity.IssueCouponRequest.IssueCouponRequestBuilder.newIssueCouponRequest()
            .withIdempotentKey(PREFIX,uniqueKey)//幂等键，必须能够体现本次发券的唯一性，再次发券时，由此键可知两次发券调用是因为同一件事
            .withActivityId(activitySimpleDTO.getActivityID()) //或者用activityId 两者选其一,如果两个都调了，用后传的那个
            .withUserId(String.valueOf(context.getUserId()))
            .withPlatform(context.isMt() ? PlatformEnum.MT : PlatformEnum.DP)
            .withIssueSourceChannel(IssueSourceChannel.MERCHANT_PLATFORM)
            .withoutRiskControl()
            .withIssueDetailSource(issueDetailSource)
            .build();
    }

    private IssueCouponActivity queryMatchedActivity(IssueCouponRequest issueCouponRequest) {
        List<IssueCouponActivity> activities = couponIssueActivityQueryService.queryActivities(issueCouponRequest);
        for (IssueCouponActivity activity : activities) {
            if (activity.getCouponGroupId() == issueCouponRequest.getCouponOptionId()) {
                return activity;
            }
        }
        return null;
    }

    private IssueCouponActivity queryMatchedUnifiedActivity(CouponIssueActivityQueryContext context) {
        List<IssueCouponActivity> activities = couponIssueActivityQueryService.queryActivities(context);
        for (IssueCouponActivity activity : activities) {

            int couponGroupId = activity.getCouponGroupId();
            String contextUnifiedCouponGroupId = context.getUnifiedCouponGroupId();
            //兼容字符类型
            if (activity.getCouponGroupId() == context.getCouponGroupId() || StringUtils.equals(contextUnifiedCouponGroupId, String.valueOf(couponGroupId))) {
                return activity;
            }
        }
        return null;
    }

    public List<UnifiedIssueDetailResult> doIssueShopCartMultiCoupon(ShopCartIssueMultiCouponContext context) {
        QuerySimpleResponseDTO querySimpleResponseDTO = couponIssueActivityQueryService.queryActivitiesByBizIds(context);
        List<String> couponGroupIds = context.getCouponGroupIds();
        List<UnifiedIssueDetailResult> issueDetailResults = Lists.newArrayListWithExpectedSize(couponGroupIds.size());
        if (querySimpleResponseDTO == null || CollectionUtils.isEmpty(querySimpleResponseDTO.getActivityDTOs())) {
            log.warn("doIssueShopCartMultiCoupon, queryActivitiesByBizIds empty, context:{}", context);
            for (String couponGroupId : couponGroupIds) {
                issueDetailResults.add(new UnifiedIssueDetailResult(false, "券未匹配商品", couponGroupId));
            }
            return issueDetailResults;
        }
        Map<Integer, Future<UnifiedIssueDetailResult>> futureMap = Maps.newHashMapWithExpectedSize(couponGroupIds.size());
        List<UnifiedIssueDetailResult> result = Lists.newArrayListWithExpectedSize(couponGroupIds.size());
        Map<Integer, IssueCouponActivity> activitiesMap = Maps.newHashMap();
        for (ActivitySimpleDTO activitySimpleDTO : querySimpleResponseDTO.getActivityDTOs()) {
            activitiesMap.put(activitySimpleDTO.getCouponGroupId(), new IssueCouponActivity(activitySimpleDTO, IssueCouponOptionType.SHOP_CART_EVENT.getCode()));
        }
        for (String unifiedCouponGroupId : couponGroupIds) {
            int couponOptionId = NumberUtils.toInt(unifiedCouponGroupId);
            if (MapUtils.isEmpty(activitiesMap) || !activitiesMap.containsKey(couponOptionId)) {
                log.warn("doIssueShopCartMultiCoupon, biz coupon not match, context:{}, activitiesMap:{}, unifiedCouponGroupId:{}", context, activitiesMap, unifiedCouponGroupId);
                issueDetailResults.add(new UnifiedIssueDetailResult(false, "券未匹配商品", unifiedCouponGroupId));
                continue;
            }
            final IssueCouponActivity activity = activitiesMap.get(couponOptionId);
            final IssueCouponRequest issueCouponRequest = convert2IssueCouponRequest(context, unifiedCouponGroupId);
            issueCouponRequest.setCouponOptionId(couponOptionId);
            futureMap.put(couponOptionId, asyncExecutor.submit(new Callable<UnifiedIssueDetailResult>() {
                @Override
                public UnifiedIssueDetailResult call() throws Exception {
                    return issueCouponWithWrapResponse(issueCouponRequest, activity);
                }
            }));
        }
        for (Map.Entry<Integer, Future<UnifiedIssueDetailResult>> entry : futureMap.entrySet()) {
            try {
                UnifiedIssueDetailResult detailResult = entry.getValue().get(1000, TimeUnit.MILLISECONDS);
                result.add(detailResult);
            } catch (Exception e) {
                log.error("doIssueShopCartMultiCoupon, future get exception, context:{}, activitiesMap:{}", context, activitiesMap, e);
                UnifiedIssueDetailResult detailResult = new UnifiedIssueDetailResult();
                detailResult.setSuccess(false);
                detailResult.setResultMessage("系统异常");
                detailResult.setUnifiedCouponGroupId(String.valueOf(entry.getKey()));
                result.add(detailResult);
            }
        }
        return result;
    }

    public void doIssueMultiCouponNew(NewIssueMultiCouponContext context) {
        preLoadShopCouponActivityInfo(context);
        Map<String, Future<NewIssueDetailResult>> issueShopCouponResultFuture = doIssueShopCoupon(context);
        Map<String, Future<NewIssueDetailResult>> issueLqgCouponResultFuture = doIssueLqgCoupon(context);
        Map<String, NewIssueDetailResult>  issueShopCouponResult = getIssueFutureResult(issueShopCouponResultFuture, context.getOptUnitIdCouponMap());
        Map<String, NewIssueDetailResult>  issueLqgCouponResult = getIssueFutureResult(issueLqgCouponResultFuture, context.getOptUnitIdCouponMap());
        if(MapUtils.isNotEmpty(issueShopCouponResult)) {
            context.getUnitIssueResultMap().putAll(issueShopCouponResult);
        }
        if(MapUtils.isNotEmpty(issueLqgCouponResult)) {
            context.getUnitIssueResultMap().putAll(issueLqgCouponResult);
        }
    }

    private Map<String, NewIssueDetailResult> getIssueFutureResult(Map<String, Future<NewIssueDetailResult>> issueCouponResultFuture, Map<String, NewIssueCouponUnit> optUnitIdCouponMap) {
        Map<String, NewIssueDetailResult> issueDetailResultMap = Maps.newHashMap();
        for(Map.Entry<String, Future<NewIssueDetailResult>> entry : issueCouponResultFuture.entrySet()) {
            try {
                if (entry.getValue() != null) {
                    NewIssueDetailResult newIssueDetailResult = entry.getValue().get(5, TimeUnit.SECONDS);
                    issueDetailResultMap.put(entry.getKey(), newIssueDetailResult);
                } else {
                    NewIssueDetailResult newIssueDetailResult = new NewIssueDetailResult(entry.getKey(), false, "发券异常");
                    newIssueDetailResult.setUnifiedCouponGroupId(optUnitIdCouponMap.get(entry.getKey()).getUnifiedCouponGroupId());
                    issueDetailResultMap.put(entry.getKey(), newIssueDetailResult);
                }
            } catch (Exception e) {
                Cat.logEvent("NewIssueMultiCouponError", "发券异常");
                log.error("getIssueFutureResult error, unitId:{}", entry.getKey(), e);
                NewIssueDetailResult newIssueDetailResult = new NewIssueDetailResult(entry.getKey(), false, "发券异常");
                newIssueDetailResult.setUnifiedCouponGroupId(optUnitIdCouponMap.get(entry.getKey()).getUnifiedCouponGroupId());
                issueDetailResultMap.put(entry.getKey(), newIssueDetailResult);
            }
        }
        return issueDetailResultMap;
    }

    private void preLoadShopCouponActivityInfo(NewIssueMultiCouponContext context) {
        //预加载用户券已领取未使用数量
        Map<String, Integer> userCouponAvailableCountMap = preloadUserCouponAvailableCountMap(context);
        dotUserFetchedAvailableCount(userCouponAvailableCountMap);
        context.setUserCouponAvailableCountMap(userCouponAvailableCountMap);
        if(CollectionUtils.isNotEmpty(context.getShopCouponGroupIds())) {
            //1、美团团单转换为点评团单
            Pair<List<Integer>, List<Integer>> dealGroupIdPair = collectDpAndMtDealGroupIdListPair(context);
            List<Integer> dpDealGroupIdList = dealGroupIdPair.getLeft();
            List<Integer> mtDealGroupIdList = dealGroupIdPair.getRight();
            Map<Integer, Integer> mtDpDealGroupMap = Maps.newHashMap();
            if(CollectionUtils.isNotEmpty(mtDealGroupIdList)) {
                mtDpDealGroupMap = getMtDpDealGroupMap(mtDealGroupIdList);
            }
            //2、查询活动(统一使用点评团单)
            QuerySimpleResponseDTO querySimpleResponseDTO = couponIssueActivityQueryService.queryDealGroupActivities(dpDealGroupIdList, mtDpDealGroupMap, context.isMt());
            if (querySimpleResponseDTO == null || CollectionUtils.isEmpty(querySimpleResponseDTO.getActivityDTOs())) {
                return;
            }
            //3、将商家活动设置到上下文中
            for (ActivitySimpleDTO activitySimpleDTO : querySimpleResponseDTO.getActivityDTOs()) {
                context.getShopCouponActivityMap().put(String.valueOf(activitySimpleDTO.getCouponGroupId()), activitySimpleDTO);
            }
        }
    }

    private Map<String, Integer> preloadUserCouponAvailableCountMap(NewIssueMultiCouponContext context) {
        List<Integer> couponGroupIds = Lists.newArrayList();
        Map<String, Integer> userCouponGroupAvailableCountMap = Maps.newHashMap();
        if(CollectionUtils.isNotEmpty(context.getShopCouponGroupIds())) {
            for(String shopCouponGroupId : context.getShopCouponGroupIds()) {
                couponGroupIds.add(Integer.parseInt(shopCouponGroupId));
            }
        }
        if(CollectionUtils.isNotEmpty(context.getLqgCouponGroupIds())) {
            for(String lqgCouponGroupId : context.getLqgCouponGroupIds()) {
                couponGroupIds.add(Integer.parseInt(lqgCouponGroupId));
            }
        }
        if(CollectionUtils.isEmpty(couponGroupIds)) {
            return userCouponGroupAvailableCountMap;
        }
        List<UnifiedCouponDTO> unifiedCouponDTOs = couponBiz.batchQueryIssuedCouponList(context.getUserId(), couponGroupIds, !context.isMt());
        if(CollectionUtils.isNotEmpty(unifiedCouponDTOs)) {
            for(UnifiedCouponDTO unifiedCouponDTO : unifiedCouponDTOs) {
                if(userCouponIsAvailable(unifiedCouponDTO)) {
                    String couponGroupId = unifiedCouponDTO.getCouponGroupDTO().getUnifiedCouponGroupId();
                    if(userCouponGroupAvailableCountMap.containsKey(couponGroupId)) {
                        Integer currentCount = userCouponGroupAvailableCountMap.get(couponGroupId);
                        userCouponGroupAvailableCountMap.put(couponGroupId, ++currentCount);
                    } else {
                        userCouponGroupAvailableCountMap.put(couponGroupId, 1);
                    }
                }
            }
        }
        return userCouponGroupAvailableCountMap;
    }

    private boolean userCouponIsAvailable(UnifiedCouponDTO unifiedCouponDTO) {
        if(unifiedCouponDTO != null
            && unifiedCouponDTO.getEndTime().getTime() > System.currentTimeMillis()
            && unifiedCouponDTO.getStatus() == 1
            && !unifiedCouponDTO.isUsed()) {
            return true;
        }
        return false;
    }

    private Pair<List<Integer>, List<Integer>> collectDpAndMtDealGroupIdListPair(NewIssueMultiCouponContext context) {
        List<Integer> dpDealGroupIdList = Lists.newArrayList();
        List<Integer> mtDealGroupIdList = Lists.newArrayList();
        for(String shopCouponGroupId : context.getShopCouponGroupIds()) {
            List<String> unitIdList = context.getOptCouponGroupIdUnitIdListMap().get(shopCouponGroupId);
            for(String unitId : unitIdList) {
                NewIssueCouponUnit newIssueCouponUnit = context.getOptUnitIdCouponMap().get(unitId);
                if(context.isMt()) {
                    mtDealGroupIdList.add(newIssueCouponUnit.getProductId().intValue());
                } else {
                    dpDealGroupIdList.add(newIssueCouponUnit.getProductId().intValue());
                }
            }
        }
        return Pair.of(dpDealGroupIdList, mtDealGroupIdList);
    }

    private Map<String, Future<NewIssueDetailResult>> doIssueShopCoupon(NewIssueMultiCouponContext context) {
        Map<String, Future<NewIssueDetailResult>> futureMap = Maps.newHashMap();
        if(CollectionUtils.isEmpty(context.getShopCouponGroupIds())) {
            return futureMap;
        }
        for (String shopCouponGroupId : context.getShopCouponGroupIds()) {
            List<String> unitIdList = context.getOptCouponGroupIdUnitIdListMap().get(shopCouponGroupId);
            for(String unitId : unitIdList) {
                if(MapUtils.isNotEmpty(context.getShopCouponActivityMap())
                    && context.getShopCouponActivityMap().containsKey(shopCouponGroupId)) {
                    futureMap.put(unitId, asyncExecutor.submit(new Callable<NewIssueDetailResult>() {
                        @Override
                        public NewIssueDetailResult call() throws Exception {
                            return issueShopCouponWithWrapResponse(context, unitId, shopCouponGroupId);
                        }
                    }));
                } else {
                    Cat.logEvent(CatEventConstants.ISSUE_MULITY_FAIL_DETAIL, "shop-productNotMatch");
                    NewIssueDetailResult newIssueDetailResult = new NewIssueDetailResult(unitId, false, "券未匹配商品");
                    newIssueDetailResult.setUnifiedCouponGroupId(shopCouponGroupId);
                    context.getUnitIssueResultMap().put(unitId, newIssueDetailResult);
                }
            }
        }
        return futureMap;
    }

    private Map<String, Future<NewIssueDetailResult>>  doIssueLqgCoupon(NewIssueMultiCouponContext context) {
        Map<String, Future<NewIssueDetailResult>> futureMap = Maps.newHashMap();
        if(CollectionUtils.isEmpty(context.getLqgCouponGroupIds())) {
            return futureMap;
        }
        for (String lqgCouponGroupId : context.getLqgCouponGroupIds()) {
            List<String> unitIdList = context.getOptCouponGroupIdUnitIdListMap().get(lqgCouponGroupId);
            for(String unitId : unitIdList) {
                futureMap.put(unitId, asyncExecutor.submit(new Callable<NewIssueDetailResult>() {
                    @Override
                    public NewIssueDetailResult call() throws Exception {
                        return issueLqgCouponWithWrapResponse(context, unitId, lqgCouponGroupId);
                    }
                }));
            }
        }
        return futureMap;
    }

    private Map<Integer, Integer> getMtDpDealGroupMap(List<Integer> dpDealGroupIdList) {
        List<IdMapper> idMappers = dealIdMapperService.queryByMtDealGroupIds(dpDealGroupIdList);
        Map<Integer, Integer> mtDpDealGroupMap = Maps.newHashMapWithExpectedSize(dpDealGroupIdList.size());
        if (CollectionUtils.isNotEmpty(idMappers)) {
            for (IdMapper idMapper : idMappers) {
                mtDpDealGroupMap.put(idMapper.getMtDealGroupID(), idMapper.getDpDealGroupID());
            }
        }
        return mtDpDealGroupMap;
    }

    private IssueCouponRequest convert2IssueCouponRequest(ShopCartIssueMultiCouponContext context, String unifiedCouponGroupId) {
        IssueCouponRequest issueCouponRequest = new IssueCouponRequest();
        issueCouponRequest.setIssueDetailSourceCode(context.getIssueDetailSourceCode());
        ClientInfo clientInfo = new ClientInfo();
        clientInfo.setCx(context.getCx());
        issueCouponRequest.setClientInfo(clientInfo);
        issueCouponRequest.setUserId(context.getUserId());

        issueCouponRequest.setDpClient(!context.isMt());
        issueCouponRequest.setRequestType( CouponRequestTypeEnum.ISSUE.getCode());
        issueCouponRequest.setIssueDetailSourceCode(context.getIssueDetailSourceCode());
        return issueCouponRequest;
    }

    public UnifiedIssueResult issueExternalCoupon(IssueCouponRequest issueCouponRequest, Map<String, String> riskMap) {
        Cat.logEvent("INVALID_METHOD_1", "com.dianping.pay.api.biz.activity.IssueCouponBiz.issueExternalCoupon(com.dianping.pay.api.entity.issuecoupon.IssueCouponRequest,java.util.Map)");
        IssueCouponActivity activity = new IssueCouponActivity(issueCouponRequest.getCouponOptionId(), IssueCouponOptionType.EXTERNAL_LINK_EVENT.getCode(), QualificationType.NOT_USE_ISSUE_QUALIFICATION.getCode());
        return issueCoupon(issueCouponRequest, activity, riskMap);
    }

    private UnifiedIssueResult issueExternalLinkCoupon(IssueCouponRequest issueCouponRequest, IssueCouponActivity activity, UnifiedCouponGroupDTO couponGroupDTO2, Map<String, String> riskMap) {
        Cat.logEvent("INVALID_METHOD_2", "com.dianping.pay.api.biz.activity.IssueCouponBiz.issueExternalLinkCoupon(IssueCouponRequest,IssueCouponActivity,UnifiedCouponGroupDTO,Map)");
        com.dianping.tgc.process.entity.IssueCouponRequest tgcIssueCouponRequest = buildIssueCouponRequest(issueCouponRequest, activity, riskMap, couponGroupDTO2);
        // 做一下并发校验
        // 如果触发了频次限制就直接返回
        if (Lion.getBooleanValue("mapi-pay-promo-web.need.distribute.lock", false) && !distributeLockUtils.tryLock(tgcIssueCouponRequest.getUserId(), activity.getCouponGroupId())) {
            log.warn("issue coupon too frequency. userId: {}", tgcIssueCouponRequest.getUserId());
            Cat.logEvent("issueExternalLinkCoupon", "FREQUENCY");
            throw new IssueCouponException("领取失败", GET_COUPON_FAIL);
        }

        PResponse<CouponIssueResult> response = merchantCouponIssueService.issueMerchantCouponToSingleUser(tgcIssueCouponRequest);

        if (response != null && response.isSuccess() && response.getResult() != null) {
            CouponIssueResult issueResult = response.getResult();
            UnifiedIssueResult result = new UnifiedIssueResult();
            result.setBeginTime(issueResult.getBeginTime());
            result.setEndTime(issueResult.getEndTime());
            result.setCouponGroupId(issueCouponRequest.getCouponOptionId());
            result.setUnifiedCouponId(issueResult.getUnifiedCouponId());
            result.setCouponGroupName(issueResult.getCouponGroupName());
            return result;
        } else {
            log.warn(String.format("doIssueCoupon return fail: request %s, response %s", tgcIssueCouponRequest, JSON.toJSONString(response)));
        }
        throw new IssueCouponException("领取失败", GET_COUPON_FAIL);
    }

    private com.dianping.tgc.process.entity.IssueCouponRequest buildIssueCouponRequest(IssueCouponRequest issueCouponRequest, IssueCouponActivity activity, Map<String, String> riskMap, UnifiedCouponGroupDTO couponGroupDTO2) {
        Cat.logEvent("INVALID_METHOD_1", "com.dianping.pay.api.biz.activity.IssueCouponBiz.buildIssueCouponRequest(IssueCouponRequest,IssueCouponActivity,Map,UnifiedCouponGroupDTO)");
        String uniqueKey = IssueCouponUtils.getUniqueKey(issueCouponRequest);
        String issueDetailSource = null;
        if (issueCouponRequest.getIssueDetailSourceCode() != null) {
            IssueDetailSourceEnum issueDetailSourceEnum = IssueDetailSourceEnum.getByCode(issueCouponRequest.getIssueDetailSourceCode());
            issueDetailSource = issueDetailSourceEnum == null ? null : issueDetailSourceEnum.getName();
        }
        IssueSourceChannel sourceChannel = null;
        for (String issueSource : couponGroupDTO2.getIssueSourceLimit()) {
            List<String> arrowIssueSources = Lion.getList(LionConstants.EXTERNAL_LINK_ARROW_ISSUE_CHANNEL, String.class);
            if (arrowIssueSources.contains(issueSource)) {
                sourceChannel = IssueSourceChannel.valueOfCode(issueSource);
                if (sourceChannel != null) {
                    break;
                }
            }
        }
        if (sourceChannel == null) {
            throw new IssueCouponException("领取失败", GET_COUPON_FAIL);
        }
        return com.dianping.tgc.process.entity.IssueCouponRequest.IssueCouponRequestBuilder.newIssueCouponRequest()
                .withIdempotentKey(EXTERNAL_LINK_PREFIX,uniqueKey)//幂等键，必须能够体现本次发券的唯一性，再次发券时，由此键可知两次发券调用是因为同一件事
                .withCouponGroupIdStr(String.valueOf(activity.getCouponGroupId())) //或者用activityId 两者选其一,如果两个都调了，用后传的那个
                .withUserId(String.valueOf(issueCouponRequest.getUserId()))
                .withPlatform(issueCouponRequest.isDpClient() ? PlatformEnum.DP : PlatformEnum.MT)
                .withIssueSourceChannel(sourceChannel)
                .withRiskMap(riskMap)
                .withIssueDetailSource(issueDetailSource)
                .build();
    }

    private void dotUserFetchedAvailableCount(Map<String, Integer> userCouponAvailableCountMap) {
        if(MapUtils.isNotEmpty(userCouponAvailableCountMap)) {
            for(Map.Entry<String, Integer> entry : userCouponAvailableCountMap.entrySet()) {
                Cat.logEvent(CatEventConstants.ISSUE_MULITY_FETCHED_QUANTY, String.valueOf(entry.getValue()));
                if(entry.getValue() != null && entry.getValue() > 1) {
                    log.warn("issueMultiCoupon user fetched quantity warn, couponGroupId:{}, quantity:{}", entry.getKey(), entry.getValue().toString());
                }
            }
        }
    }
}
