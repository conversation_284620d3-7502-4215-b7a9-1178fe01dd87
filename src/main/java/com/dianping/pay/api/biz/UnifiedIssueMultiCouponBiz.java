package com.dianping.pay.api.biz;

import com.dianping.api.common.enums.ProductTypeEnum;
import com.dianping.api.domain.ClientInfo;
import com.dianping.api.picasso.UrlUtils;
import com.dianping.api.picasso.controller.UnifiedIssueMultiCouponAction;
import com.dianping.api.util.ApiUtil;
import com.dianping.api.util.CrossDomainUtils;
import com.dianping.api.util.DotUtils;
import com.dianping.cat.Cat;
import com.dianping.gmkt.coupon.common.api.enums.CouponGroupExtraKeyEnum;
import com.dianping.gmkt.coupon.common.api.enums.IssueDetailSourceEnum;
import com.dianping.gmkt.wave.api.enums.ActivityChannelEnum;
import com.dianping.mobile.framework.datatypes.CommonMobileResponse;
import com.dianping.mobile.framework.datatypes.IMobileContext;
import com.dianping.mobile.framework.datatypes.IMobileResponse;
import com.dianping.mobile.framework.datatypes.token.UserStatusResult;
import com.dianping.pay.api.biz.activity.CouponInfoBiz;
import com.dianping.pay.api.biz.activity.IssueCouponBiz;
import com.dianping.pay.api.biz.activity.RestUserInfoService;
import com.dianping.pay.api.biz.activity.newloader.CouponIssueActivityQueryService;
import com.dianping.pay.api.biz.activity.newloader.dto.CouponIssueActivityQueryContext;
import com.dianping.pay.api.biz.activity.newloader.dto.CouponIssueMultiActivityQueryContext;
import com.dianping.pay.api.biz.activity.newloader.dto.RestUserInfo;
import com.dianping.pay.api.biz.activity.newloader.promo.ResourcePromotionProcessor;
import com.dianping.pay.api.biz.discount.CouponBiz;
import com.dianping.pay.api.entity.issuecoupon.*;
import com.dianping.pay.api.enums.ShopProductType;
import com.dianping.pay.api.exception.IssueCouponException;
import com.dianping.pay.framework.utils.IdCryptoUtils;
import com.dianping.pay.framework.utils.shopUuid.ShopUuidUtils;
import com.dianping.pay.promo.common.enums.User;
import com.dianping.pay.promo.common.enums.coupon.UnifiedCouponGroupType;
import com.dianping.unified.coupon.manage.api.dto.UnifiedCouponGroupDTO;
import com.dianping.violet.enums.CouponRequestTypeEnum;
import com.google.common.collect.Lists;
import com.meituan.servicecatalog.api.annotations.*;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang.Validate;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2023/7/3
 */
@Service
public class UnifiedIssueMultiCouponBiz {

    private static final Logger LOGGER = LogManager.getLogger(UnifiedIssueMultiCouponAction.class);
    @Autowired
    private IssueCouponBiz issueCouponBiz;
    @Autowired
    private RestUserInfoService restUserInfoService;
    @Autowired
    private CouponInfoBiz couponInfoBiz;
    @Resource
    private ShopUuidUtils shopUuidUtils;
    @Resource
    private ResourcePromotionProcessor resourcePromotionProcessor;

    public IMobileResponse validate(UnifiedissuemulticouponRequest request, IMobileContext iMobileContext) {
        try {
            if (iMobileContext.getRequest() != null) {
                DotUtils.addRefererDot(iMobileContext.getRequest());
            }
            Validate.notNull(request, "null request");
            IdCryptoUtils.decryptByRequest(request);
            shopUuidUtils.prepareUuidInRequest(request, ApiUtil.isMapiRequest(iMobileContext));

            if (ApiUtil.isMapiRequest(iMobileContext)) {
                Validate.isTrue(iMobileContext.getUserId() > 0 || (iMobileContext.isMtClient() && iMobileContext.getUserStatus() != null && iMobileContext.getUserStatus().getMtUserId() > 0), "用户未登录");
            }
            if (!String.valueOf(Boolean.TRUE).equals(request.getNeedPushResourceCoupon())) {
                Validate.isTrue((request.getShopidL() != null && request.getShopidL() > 0)
                                || (request.getProductid() != null && request.getProductid() > 0)
                                || (StringUtils.isNotEmpty(request.getShopuuid()))
                        , "invalid shopId/productId");
                Validate.isTrue(StringUtils.isNotEmpty(request.getUnifiedCouponGroupIds()), " 券 id 列表非法");
                List<String> couponGroupIds = Lists.newArrayList(StringUtils.split(request.getUnifiedCouponGroupIds(), ","));
                Validate.isTrue(couponGroupIds.size() <= 20, "券批次个数过多");
                if (request.getProductid() != null && request.getProductid() > 0) {
                    if (request.getProducttype() == null) {
                        request.setProducttype(ProductTypeEnum.DEAL.code);
                    }
                    Validate.isTrue(request.getProducttype() == ProductTypeEnum.DEAL.code
                            || request.getProducttype() == ProductTypeEnum.SKU.code, "producttype invalid");
                }

            }

            if (request.getIssueDetailSourceCode() != null) {
                Validate.isTrue(IssueDetailSourceEnum.getByCode(request.getIssueDetailSourceCode()) != null, "issuedetailsourcecode invalid");
            }
        } catch (IllegalArgumentException e) {
            LOGGER.warn("invalid request:{}", e.getMessage());
            return new CommonMobileResponse(new IssueCouponMsg(401, e.getMessage(), e.getMessage()));
        }
        return null;
    }

    public UnifiedIssueMultiCouponRespDo doMultiIssueCoupon(UnifiedissuemulticouponRequest request, IMobileContext iMobileContext) {
        CrossDomainUtils.setEnableCrossDomain(iMobileContext.getRequest(), iMobileContext.getResponse());
        try {
            List<UnifiedIssueDetailResult> issueResult = Lists.newArrayList();
            Pair<Boolean, Long> user = processHttpUserInfo(iMobileContext);
            Boolean isDpClient = false;
            long userId = 0L;
            if (user != null) {
                isDpClient = user.getLeft();
                userId = user.getRight();
            }


            if (StringUtils.isNotBlank(request.getUnifiedCouponGroupIds())) {
                IssueMultiCouponRequest issueCouponRequest = issueCouponMultiRequestAdapter(request, iMobileContext, CouponRequestTypeEnum.ISSUE.getCode());
                LOGGER.info("issueCouponRequest:{}, token:{}, userid:{}",
                        issueCouponRequest,
                        iMobileContext.getHeader().getToken(),
                        issueCouponRequest.getIssueCouponRequest().getUserId());
                CouponIssueMultiActivityQueryContext context = issueRequest2QueryActivityContext(request, iMobileContext);

                if (!ApiUtil.isMapiRequest(iMobileContext)) {
                    issueCouponRequest.getIssueCouponRequest().setDpClient(isDpClient);
                    issueCouponRequest.getIssueCouponRequest().setUserId(userId);
                    context.getCouponIssueActivityQueryContext().setDpClient(isDpClient);
                    context.getCouponIssueActivityQueryContext().setUserId(userId);
                }

                Map<String, UnifiedCouponGroupDTO> couponGroupDTOMap = couponInfoBiz.batchLoadCouponGroup(context.getUnifiedCouponGroupIds());
                if (MapUtils.isEmpty(couponGroupDTOMap)) {
                    throw new IssueCouponException("活动不存在", 3);
                }
                List<String> lqgCouponGroupIds = Lists.newArrayList();
                List<String> couponGroupIds = Lists.newArrayList();
                for (Map.Entry<String, UnifiedCouponGroupDTO> entry : couponGroupDTOMap.entrySet()) {
                    int couponGroupType = entry.getValue().getCouponGroupType();
                    boolean isLQG = MapUtils.getBooleanValue(entry.getValue().getExtraInfoMap(), CouponGroupExtraKeyEnum.lingquangou.name(), false);
                    if (couponGroupType != UnifiedCouponGroupType.shopCoupon.value && couponGroupType != UnifiedCouponGroupType.mtShopCoupon.value && !isLQG) {
                        throw new IssueCouponException("不支持的券类型", 4);
                    }
                    if (isLQG) {
                        lqgCouponGroupIds.add(entry.getKey());
                    }else{
                        couponGroupIds.add(entry.getKey());
                    }
                }
                context.setLqgUnifiedCouponGroupIds(lqgCouponGroupIds);
                context.setUnifiedCouponGroupIds(couponGroupIds);

                //领券商家券
                if (issueCouponRequest.getIssueCouponRequest().getUserId() <= 0) {
                    Cat.logEvent("InvalidUserId", "unifiedissuemulticoupon.bin");
                }
                List<UnifiedIssueDetailResult> merchantIssueResult = issueCouponBiz.doIssueMultiCoupon(issueCouponRequest, context);
                batchFillUseUrl(issueCouponRequest.getIssueCouponRequest().isDpClient(), merchantIssueResult, couponGroupDTOMap);

                issueResult.addAll(merchantIssueResult);
            }

            if (String.valueOf(Boolean.TRUE).equals(request.getNeedPushResourceCoupon())) {
                IssueCouponRequest issueResourceCouponRequest = issueCouponRequestAdapter(request, iMobileContext, CouponRequestTypeEnum.ISSUE.getCode());
                if (!ApiUtil.isMapiRequest(iMobileContext)) {
                    issueResourceCouponRequest.setDpClient(isDpClient);
                    issueResourceCouponRequest.setUserId(userId);
                }

                UnifiedIssueDetailResult detailResult;
                try {
                    UnifiedIssueResult resourceResult = resourcePromotionProcessor.doIssueCoupon(issueResourceCouponRequest, iMobileContext);
                    detailResult = convertIssueResult2DetailResult(resourceResult);
                } catch (IssueCouponException e) {
                    LOGGER.error("doIssueCoupon failed. issueResourceCouponRequest: {}", issueResourceCouponRequest, e);
                    detailResult = new UnifiedIssueDetailResult();
                    detailResult.setSuccess(false);
                    detailResult.setResultMessage(e.getMessage());
                }

                issueResult.add(detailResult);
            }

            UnifiedIssueMultiCouponRespDo unifiedIssueMultiCouponRespDo = new UnifiedIssueMultiCouponRespDo();
            unifiedIssueMultiCouponRespDo.setSuccess(true);
            unifiedIssueMultiCouponRespDo.setIssueDetail(issueResult);
            return unifiedIssueMultiCouponRespDo;
        } catch (IssueCouponException e) {
            LOGGER.error("doMultiIssueCoupon ex, request:{}", request, e);
            UnifiedIssueMultiCouponRespDo unifiedIssueCouponRespDo = new UnifiedIssueMultiCouponRespDo();
            unifiedIssueCouponRespDo.setSuccess(false);
            unifiedIssueCouponRespDo.setErrorMsg(e.getMessage());
            return unifiedIssueCouponRespDo;
        }
    }

    private UnifiedIssueDetailResult convertIssueResult2DetailResult(UnifiedIssueResult issueResult) {
        UnifiedIssueDetailResult issueDetailResult = new UnifiedIssueDetailResult();
        issueDetailResult.setBeginTime(issueResult.getBeginTime());
        issueDetailResult.setEndTime(issueResult.getEndTime());
        issueDetailResult.setCouponGroupName(issueResult.getCouponGroupName());
        issueDetailResult.setSuccess(true);
        return issueDetailResult;
    }

    private void batchFillUseUrl(boolean dpClient, List<UnifiedIssueDetailResult> detailResults, Map<String, UnifiedCouponGroupDTO> couponGroupDTOMap) {
        for (UnifiedIssueDetailResult issueResult : detailResults) {
            if (issueResult == null || !issueResult.isSuccess() || StringUtils.isEmpty(issueResult.getUnifiedCouponGroupId())) {
                continue;
            }
            UnifiedCouponGroupDTO unifiedCouponGroupDTO = couponGroupDTOMap.get(issueResult.getUnifiedCouponGroupId());
            issueResult.setUseUrl(UrlUtils.getCouponGroupToUseUrl(unifiedCouponGroupDTO, issueResult.getUnifiedCouponId(), dpClient));
        }
    }

    private Pair<Boolean, Long> processHttpUserInfo(IMobileContext iMobileContext) {
        if (!ApiUtil.isMapiRequest(iMobileContext)) {
            RestUserInfo restUserInfo = restUserInfoService.handleMobileContext(iMobileContext);
            if (restUserInfo == null || restUserInfo.getUserId() <= 0) {
                LOGGER.warn(String.format("invalid request: %s", "rest用户未登录"));
                throw new IssueCouponException("用户未登录", 401);
            }
            boolean isDpClient = restUserInfo.getUserCode() != User.MT.getCode();

            return Pair.of(isDpClient, restUserInfo.getUserId());
        } else if (iMobileContext.getUserStatus() != null) {
            UserStatusResult userStatus = iMobileContext.getUserStatus();
            if (iMobileContext.isMtClient()) {
                return Pair.of(false, userStatus.getMtUserId());
            } else {
                return Pair.of(true, userStatus.getUserId());
            }
        }
        return null;
    }


    private CouponIssueMultiActivityQueryContext issueRequest2QueryActivityContext(UnifiedissuemulticouponRequest request, IMobileContext iMobileContext) {
        CouponIssueMultiActivityQueryContext result = new CouponIssueMultiActivityQueryContext();

        CouponIssueActivityQueryContext context = new CouponIssueActivityQueryContext();
        context.setShopIdL(request.getShopidL() == null ? 0 : request.getShopidL());
        context.setUserId(iMobileContext.getUserId());
        context.setDpClient(!iMobileContext.isMtClient());
        context.setProductId(request.getProductid() == null ? 0 : request.getProductid());
        context.setProductType(request.getProducttype() == null ? 0 : request.getProducttype());


        if (ApiUtil.isMapiRequest(iMobileContext)) {
            //native查询平台券时，设置为通用领券组件
            context.setIssueChannel(ActivityChannelEnum.COMMON.getCode());
        } else {
            context.setIssueChannel(request.getIssueChannel() == null ? 0 : request.getIssueChannel());
        }
        context.setCx(request.getCx());

        result.setCouponIssueActivityQueryContext(context);
        if (StringUtils.isNotEmpty(request.getUnifiedCouponGroupIds())) {
            result.setUnifiedCouponGroupIds(Lists.newArrayList(StringUtils.split(request.getUnifiedCouponGroupIds(), ",")));
        }
        return result;
    }

    private IssueMultiCouponRequest issueCouponMultiRequestAdapter(UnifiedissuemulticouponRequest request, IMobileContext iMobileContext, int code) {
        IssueMultiCouponRequest issueMultiCouponRequest = new IssueMultiCouponRequest();
        IssueCouponRequest issueCouponRequest = new IssueCouponRequest();
        issueCouponRequest.setShopIdL(request.getShopidL() == null ? 0 : request.getShopidL());
        issueCouponRequest.setShopUuid(request.getShopuuid());
        issueCouponRequest.setProductId(request.getProductid() == null ? 0 : request.getProductid());
        if (request.getProducttype() == null) {
            issueCouponRequest.setShopProductType(0);
        } else if (request.getProducttype() == ProductTypeEnum.DEAL.code) {
            issueCouponRequest.setShopProductType(ShopProductType.DEFAULT.getValue());
        } else if (request.getProducttype() == ProductTypeEnum.SKU.code) {
            issueCouponRequest.setShopProductType(ShopProductType.GENERAL_TRADE.getValue());
        }
        ClientInfo clientInfo = new ClientInfo();
        clientInfo.setCx(request.getCx());
        issueCouponRequest.setClientInfo(clientInfo);
        if (!iMobileContext.isMtClient() && iMobileContext.getUserId() > 0) {
            issueCouponRequest.setUserId(iMobileContext.getUserId());
        } else if (iMobileContext.isMtClient() && iMobileContext.getUserStatus() != null && iMobileContext.getUserStatus().getMtUserId() > 0) {
            issueCouponRequest.setUserId(iMobileContext.getUserStatus().getMtUserId());
        }
        issueCouponRequest.setDpClient(!iMobileContext.isMtClient());
        issueCouponRequest.setRequestType(code);
        issueCouponRequest.setIssueDetailSourceCode(request.getIssueDetailSourceCode());
        issueCouponRequest.setCouponPageSource(request.getCouponPageSource());
        if (StringUtils.isNotEmpty(request.getUuid())) {
            issueCouponRequest.setUuid(request.getUuid());
        } else {
            String uuidFromHeader = getUuidFromHeader(iMobileContext);
            issueCouponRequest.setUuid(uuidFromHeader);
        }
        issueMultiCouponRequest.setIssueCouponRequest(issueCouponRequest);
        issueMultiCouponRequest.setUnifiedCouponGroupIds(Lists.newArrayList(StringUtils.split(request.getUnifiedCouponGroupIds(), ",")));
        return issueMultiCouponRequest;
    }

    private static String getUuidFromHeader(IMobileContext iMobileContext) {
        if (ApiUtil.isMapiRequest(iMobileContext)) {
            if (iMobileContext.isMtClient()) {
                return iMobileContext.getHeader().getUuid();
            } else {
                return iMobileContext.getHeader().getDpid();
            }
        }
        return null;
    }

    private IssueCouponRequest issueCouponRequestAdapter(UnifiedissuemulticouponRequest request, IMobileContext iMobileContext, int code) {
        IssueCouponRequest issueCouponRequest = new IssueCouponRequest();
        issueCouponRequest.setShopIdL(request.getShopidL() == null ? 0 : request.getShopidL());
        issueCouponRequest.setShopUuid(request.getShopuuid());
        issueCouponRequest.setProductId(request.getProductid() == null ? 0 : request.getProductid());
        if (request.getProducttype() == null) {
            issueCouponRequest.setShopProductType(0);
        } else if (request.getProducttype() == ProductTypeEnum.DEAL.code) {
            issueCouponRequest.setShopProductType(ShopProductType.DEFAULT.getValue());
        } else if (request.getProducttype() == ProductTypeEnum.SKU.code) {
            issueCouponRequest.setShopProductType(ShopProductType.GENERAL_TRADE.getValue());
        }

        ClientInfo clientInfo = new ClientInfo();
        clientInfo.setCx(request.getCx());
        issueCouponRequest.setClientInfo(clientInfo);
        if (!iMobileContext.isMtClient() && iMobileContext.getUserId() > 0) {
            issueCouponRequest.setUserId(iMobileContext.getUserId());
        } else if (iMobileContext.isMtClient() && iMobileContext.getUserStatus() != null && iMobileContext.getUserStatus().getMtUserId() > 0) {
            issueCouponRequest.setUserId(iMobileContext.getUserStatus().getMtUserId());
        }
        issueCouponRequest.setDpClient(!iMobileContext.isMtClient());
        issueCouponRequest.setRequestType(code);
        issueCouponRequest.setIssueDetailSourceCode(request.getIssueDetailSourceCode());

        // 投放券参数填充
        issueCouponRequest.setActivityid(request.getActivityid());
        issueCouponRequest.setMaterialid(request.getMaterialid());
        issueCouponRequest.setResourceactivityid(request.getResourceactivityid());
        issueCouponRequest.setFlowid(request.getFlowid());
        issueCouponRequest.setRowkey(request.getRowkey());

        if (StringUtils.isNotEmpty(request.getUuid())) {
            issueCouponRequest.setUuid(request.getUuid());
        } else {
            if (ApiUtil.isMapiRequest(iMobileContext)) {
                if (issueCouponRequest.isDpClient()) {
                    issueCouponRequest.setUuid(iMobileContext.getHeader().getDpid());
                } else {
                    issueCouponRequest.setUuid(iMobileContext.getHeader().getUuid());
                }
            }
        }
        return issueCouponRequest;
    }

}
