package com.dianping.pay.api.biz.activity.newloader;

import com.dianping.deal.idmapper.api.DealIdMapperService;
import com.dianping.pay.api.biz.activity.newloader.dto.CouponIssueActivityQueryContext;
import com.dianping.pay.api.biz.activity.newloader.executors.*;
import com.dianping.pay.api.biz.activity.newloader.unified.executor.*;
import com.dianping.pay.api.biz.discount.CouponBiz;
import com.dianping.pay.api.entity.issuecoupon.IssueCouponRequest;
import com.dianping.poi.relation.service.api.PoiRelationService;
import com.dianping.tgc.open.DealGroupActivityQueryRemoteService;
import com.dianping.tgc.open.ShopActivityQueryRemoteService;
import com.dianping.tgc.open.SkuActivityQueryRemoteService;
import com.dianping.tgc.open.SpuActivityQueryRemoteService;
import com.dianping.tgc.open.v2.TGCActivityBizQueryService;
import com.dianping.violet.service.BeautyCouponService;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;

public class CouponIssueActivityQueryExecutorFactory {
    private static final Logger log = LogManager.getLogger(CouponIssueActivityQueryExecutorFactory.class);
    @Autowired
    private CouponBiz couponBiz;
    @Autowired
    private DealGroupActivityQueryRemoteService dealGroupActivityQueryRemoteService;
    @Autowired
    private ShopActivityQueryRemoteService shopActivityQueryRemoteService;
    @Autowired
    private DealIdMapperService dealIdMapperService;
    @Autowired
    private BeautyCouponService beautyCouponService;
    @Autowired
    private PoiRelationService poiRelationCacheService;
    @Autowired
    private SpuActivityQueryRemoteService spuActivityQueryRemoteService;
    @Autowired
    private SkuActivityQueryRemoteService skuActivityQueryRemoteService;
    @Autowired
    private TGCActivityBizQueryService tgcActivityBizQueryService;

    public <T extends AbstractUnifiedCouponActivityQueryExecutor> T getExecutor(Class<T> clazz, CouponIssueActivityQueryContext context) {
        T instance = null ;
        try{
            instance = clazz.getDeclaredConstructor(CouponIssueActivityQueryContext.class).newInstance(context);
            instance.setCouponBiz(couponBiz);
            if (instance instanceof UnifiedDealActivityQueryExecutor) {
                ((UnifiedDealActivityQueryExecutor) instance).setDealIdMapperService(dealIdMapperService)
                        .setDealGroupActivityQueryRemoteService(dealGroupActivityQueryRemoteService);
            }else if(instance instanceof UnifiedShopActivityQueryExecutor) {
                ((UnifiedShopActivityQueryExecutor) instance).setShopActivityQueryRemoteService(shopActivityQueryRemoteService)
                        .setPoiRelationCacheService(poiRelationCacheService);
            }else if(instance instanceof UnifiedShopDealActivityQueryExecutor){
                ((UnifiedShopDealActivityQueryExecutor) instance).setPoiRelationCacheService(poiRelationCacheService)
                        .setDealGroupActivityQueryRemoteService(dealGroupActivityQueryRemoteService);
            }else if(instance instanceof UnifiedShopSpuActivityQueryExecutor){
                ((UnifiedShopSpuActivityQueryExecutor) instance).setPoiRelationCacheService(poiRelationCacheService)
                        .setSpuQueryRemoteService(spuActivityQueryRemoteService);
            }else if(instance instanceof UnifiedSkuActivityQueryExecutor){
                ((UnifiedSkuActivityQueryExecutor) instance).setDealIdMapperService(dealIdMapperService)
                        .setSkuActivityQueryRemoteService(skuActivityQueryRemoteService);
            }else if(instance instanceof UnifiedSpuActivityQueryExecutor){
                ((UnifiedSpuActivityQueryExecutor) instance).setSpuActivityQueryRemoteService(tgcActivityBizQueryService);
            }
        }catch (Exception e){
            log.error(String.format("Unified CouponIssueActivityQueryExecutor Error,e:%s,context:%s",e.getMessage(),context.toString()),e);
        }
        return instance;
    }
    public <T extends AbstractCouponIssueActivityQueryExecutor> T getExecutor(Class<T> clazz, IssueCouponRequest request) {
        T instance = null;
        try {
            instance = clazz.getDeclaredConstructor(IssueCouponRequest.class).newInstance(request);
            instance.setCouponBiz(couponBiz)
                    .setBeautyCouponService(beautyCouponService);
            if (instance instanceof TuangouShopActivityQueryExecutor) {
                ((TuangouShopActivityQueryExecutor) instance)
                        .setDealGroupActivityQueryRemoteService(dealGroupActivityQueryRemoteService)
                        .setPoiRelationCacheService(poiRelationCacheService);
            } else if (instance instanceof GeneralShopActivityQueryExecutor) {
                ((GeneralShopActivityQueryExecutor) instance)
                        .setShopActivityQueryRemoteService(shopActivityQueryRemoteService)
                        .setPoiRelationCacheService(poiRelationCacheService);
            } else if (instance instanceof DealActivityQueryExecutor) {
                ((DealActivityQueryExecutor) instance)
                        .setDealGroupActivityQueryRemoteService(dealGroupActivityQueryRemoteService)
                        .setDealIdMapperService(dealIdMapperService);
            } else if (instance instanceof SpuShopActivityQueryExecutor) {
                ((SpuShopActivityQueryExecutor) instance)
                        .setSpuActivityQueryRemoteService(spuActivityQueryRemoteService)
                        .setPoiRelationCacheService(poiRelationCacheService);
            } else if (instance instanceof SkuActivityQueryExecutor) {
                ((SkuActivityQueryExecutor) instance)
                        .setSkuActivityQueryRemoteService(skuActivityQueryRemoteService);
            } else if (instance instanceof BeautyActivityQueryExecutor) {

            }
        } catch (Exception e) {
            log.error(String.format("CouponIssueActivityQueryExecutor Error,e:%s,request:%s",e.getMessage(),request.toString()),e);
        }
        return instance;
    }
}
