package com.dianping.pay.api.biz.activity.newloader.mapper;

import com.dianping.gmkt.coupon.common.api.enums.CouponBelonging;
import com.dianping.gmkt.coupon.common.api.enums.CouponBusiness;
import com.dianping.gmkt.coupon.common.api.enums.CouponGroupExtraKeyEnum;
import com.dianping.gmkt.coupon.common.api.enums.CouponGroupUserRuleDimension;
import com.dianping.lion.client.Lion;
import com.dianping.mobile.framework.datatypes.IMobileContext;
import com.dianping.pay.api.beans.IssueCouponActivity;
import com.dianping.pay.api.biz.activity.newloader.dto.CouponActivityContext;
import com.dianping.pay.api.entity.issuecoupon.*;
import com.dianping.pay.api.util.*;
import com.dianping.pay.promo.common.enums.coupon.UnifiedCouponGroupType;
import com.dianping.unified.coupon.manage.api.dto.UnifiedCouponDTO;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.tuple.Pair;

import java.math.BigDecimal;
import java.util.*;

/**
 * <AUTHOR>
 * @date 2021/1/20
 */
public class ShopCartPromoInfoMapper {

    public static ShopCartPromoInfo buildShopCartPromoInfo(CouponActivityContext context, List<ShopCartBrandProduct> shopCartBrandProducts) {
        ShopCartPromoInfo shopCartPromoInfo = new ShopCartPromoInfo();
        shopCartPromoInfo.setPlatformCoupons(buildPlatformCoupons(context));
        shopCartPromoInfo.setBrandMerchantCouponsMap(buildMerchantCoupons(context, shopCartBrandProducts));
        return shopCartPromoInfo;
    }

    public static ShopCartCouponInfo buildPlatformCoupons(CouponActivityContext context) {
        List<UnifiedCouponDTO> unifiedCouponDTOS = context.getUnifiedCouponDTOS();

        if (CollectionUtils.isEmpty(unifiedCouponDTOS)) {
            return null;
        }
        List<CouponDetailInfo> platformCoupons = Lists.newArrayList();

        for (UnifiedCouponDTO unifiedCouponDTO : unifiedCouponDTOS) {
            //过滤不可用
            if (unifiedCouponDTO.isUsed() || unifiedCouponDTO.getEndTime().compareTo(new Date()) < 0) {
                continue;
            }
            int couponGroupType = unifiedCouponDTO.getCouponGroupDTO().getCouponGroupType();
            //过滤商家券
            if (couponGroupType == UnifiedCouponGroupType.shopCoupon.value || couponGroupType == UnifiedCouponGroupType.mtShopCoupon.value) {
                continue;
            }
            //过滤平台通用券
            if (couponGroupType == UnifiedCouponGroupType.commonPlatCoupon.value) {
                continue;
            }
            List<Integer> productCodeList = unifiedCouponDTO.getCouponGroupDTO().getProductCodeList();
            Map<String, String> extraInfoMap = unifiedCouponDTO.getCouponGroupDTO().getExtraInfoMap();

            //预付业务
            if (!productCodeList.contains(CouponBusiness.DP_PREPAY.getCode()) && !productCodeList.contains(CouponBusiness.MT_PREPAY.getCode())) {
                continue;
            }
            if (Lion.getBooleanValue(LionConstants.SHOPCART_BELONG_FILTER_SWITCH, true)) {
                if (MapUtils.isEmpty(extraInfoMap) || !extraInfoMap.containsKey(CouponGroupExtraKeyEnum.couponBelonging.toString())) {
                    continue;
                }
                List<CouponBelonging> couponBelongs = CouponBelonging.getCouponBelonging(extraInfoMap.get(CouponGroupExtraKeyEnum.couponBelonging.toString()));
                if (!couponBelongs.contains(CouponBelonging.mediBeauty)) {
                    continue;
                }
            }
            CouponDetailInfo couponDetailInfo = ShopCartPromoInfoHelper.genCouponDetailItem(unifiedCouponDTO);
            platformCoupons.add(couponDetailInfo);
        }

        Collections.sort(platformCoupons, new Comparator<CouponDetailInfo>() {
            @Override
            public int compare(CouponDetailInfo o1, CouponDetailInfo o2) {
                int compare = o1.getPriceLimit().compareTo(o2.getPriceLimit());
                if (compare != 0) {
                    return compare;
                }
                return o2.getDiscountAmount().compareTo(o1.getDiscountAmount());
            }
        });

        ShopCartCouponInfo shopCartCouponInfo = new ShopCartCouponInfo();
        shopCartCouponInfo.setCoupons(platformCoupons);
        List<CouponTag> couponTags = Lists.newArrayListWithExpectedSize(platformCoupons.size());
        for (CouponDetailInfo couponDetailInfo : platformCoupons) {
            CouponTag couponTag = new CouponTag();
            couponTag.setTag(ShopCartPromoInfoHelper.genCouponTag(couponDetailInfo));
            couponTags.add(couponTag);
        }
        shopCartCouponInfo.setCouponTags(couponTags);

        return shopCartCouponInfo;
    }

    public static Map<String, ShopCartCouponInfo> buildMerchantCoupons(CouponActivityContext context, List<ShopCartBrandProduct> shopCartBrandProducts) {
        Pair<Map<Integer, Map<String, List<Integer>>>, List<IssueCouponActivity>> batchIssueCouponActivitiesPair = context.getBatchIssueCouponActivitiesPair();

        if (batchIssueCouponActivitiesPair == null) {
            return null;
        }

        Map<Integer, Map<String, List<Integer>>> bizTypeBizActivityIdsMap = batchIssueCouponActivitiesPair.getLeft();
        List<IssueCouponActivity> issueCouponActivities = batchIssueCouponActivitiesPair.getRight();

        if (CollectionUtils.isEmpty(issueCouponActivities)) {
            return null;
        }
        Map<String, List<Integer>> brandCouponIdsMap = ShopCartPromoInfoHelper.genBrandCouponIds(bizTypeBizActivityIdsMap, shopCartBrandProducts, issueCouponActivities, context);
        Map<String, CouponDetailInfo> couponDetailInfoMap = filterAndGenCouponDetailInfo(issueCouponActivities);

        Map<String, List<CouponDetailInfo>> brandCouponDetailInfosMap = ShopCartPromoInfoHelper.genBrandCouponInfoMap(brandCouponIdsMap, couponDetailInfoMap);

        Map<String, ShopCartCouponInfo> shopCartCouponInfoMap = Maps.newHashMapWithExpectedSize(brandCouponDetailInfosMap.size());

        for (Map.Entry<String, List<CouponDetailInfo>> entry : brandCouponDetailInfosMap.entrySet()) {
            String brandId = entry.getKey();
            List<CouponDetailInfo> couponDetailInfos = entry.getValue();
            List<CouponTag> couponTags = Lists.newArrayListWithExpectedSize(couponDetailInfos.size());
            ShopCartCouponInfo shopCartCouponInfo = new ShopCartCouponInfo();
            Collections.sort(couponDetailInfos, new ShopCartCouponDetailComparator());
            for (CouponDetailInfo couponDetailInfo : couponDetailInfos) {
                CouponTag couponTag = new CouponTag();
                couponTag.setTag(ShopCartPromoInfoHelper.genCouponTag(couponDetailInfo));
                couponTags.add(couponTag);
            }
            shopCartCouponInfo.setCoupons(couponDetailInfos);
            shopCartCouponInfo.setCouponTags(couponTags);
            shopCartCouponInfoMap.put(brandId, shopCartCouponInfo);
        }

        return shopCartCouponInfoMap;
    }

    public static ShopCartPromoDisplayInfo buildShopCartPromoDisplayInfo(CouponActivityContext context, List<ShopCartBrandProduct> shopCartBrandProducts) {
        ShopCartPromoDisplayInfo shopCartPromoDisplayInfo = new ShopCartPromoDisplayInfo();
        shopCartPromoDisplayInfo.setBrandMerchantShopCartCouponInfos(buildBrandMerchantCoupons(context, shopCartBrandProducts));
        return shopCartPromoDisplayInfo;
    }

    public static List<BrandMerchantShopCartCouponInfo> buildBrandMerchantCoupons(CouponActivityContext context, List<ShopCartBrandProduct> shopCartBrandProducts) {
        Pair<Map<Integer, Map<String, List<Integer>>>, List<IssueCouponActivity>> batchIssueCouponActivitiesPair = context.getBatchIssueCouponActivitiesPair();

        if (batchIssueCouponActivitiesPair == null) {
            return null;
        }

        Map<Integer, Map<String, List<Integer>>> bizTypeBizActivityIdsMap = batchIssueCouponActivitiesPair.getLeft();
        List<IssueCouponActivity> issueCouponActivities = batchIssueCouponActivitiesPair.getRight();

        if (CollectionUtils.isEmpty(issueCouponActivities)) {
            return null;
        }
        Map<String, List<Integer>> brandCouponIdsMap = ShopCartPromoInfoHelper.genBrandCouponIds(bizTypeBizActivityIdsMap, shopCartBrandProducts, issueCouponActivities, context);
        Map<String, CouponDetailInfo> couponDetailInfoMap = filterAndGenCouponDetailInfo(issueCouponActivities);

        Map<String, List<CouponDetailInfo>> brandCouponDetailInfosMap = ShopCartPromoInfoHelper.genBrandCouponInfoMap(brandCouponIdsMap, couponDetailInfoMap);

        List<BrandMerchantShopCartCouponInfo> brandMerchantShopCartCouponInfos = Lists.newArrayList();


        for (Map.Entry<String, List<CouponDetailInfo>> entry : brandCouponDetailInfosMap.entrySet()) {
            String brandId = entry.getKey();
            List<CouponDetailInfo> couponDetailInfos = entry.getValue();
            List<CouponTag> couponTags = Lists.newArrayListWithExpectedSize(couponDetailInfos.size());
            ShopCartCouponMapiInfo shopCartCouponMapiInfo = new ShopCartCouponMapiInfo();
            Collections.sort(couponDetailInfos, new ShopCartCouponDetailComparator());
            for (CouponDetailInfo couponDetailInfo : couponDetailInfos) {
                CouponTag couponTag = new CouponTag();
                couponTag.setTag(ShopCartPromoInfoHelper.genCouponTag(couponDetailInfo));
                couponTags.add(couponTag);
            }
            shopCartCouponMapiInfo.setCoupons(couponDetailInfos);
            shopCartCouponMapiInfo.setCouponTags(couponTags);

            BrandMerchantShopCartCouponInfo brandMerchantShopCartCouponInfo = new BrandMerchantShopCartCouponInfo();
            brandMerchantShopCartCouponInfo.setBrandId(brandId);
            brandMerchantShopCartCouponInfo.setShopCartCouponMapiInfo(shopCartCouponMapiInfo);
            brandMerchantShopCartCouponInfos.add(brandMerchantShopCartCouponInfo);
        }

        return brandMerchantShopCartCouponInfos;
    }

    private static Map<String, CouponDetailInfo> filterAndGenCouponDetailInfo(List<IssueCouponActivity> issueCouponActivities) {
        Map<String, CouponDetailInfo> couponDetailInfoMap = Maps.newHashMap();
        for (IssueCouponActivity issueCouponActivity : issueCouponActivities) {
            if (issueCouponActivity == null || issueCouponActivity.getCouponGroup() == null || issueCouponActivity.getCouponGroup().getDiscountAmount().compareTo(BigDecimal.ZERO) <= 0) {
                continue;
            }
            // 如果：既不能领取、又不能使用、还不是之前已经用过的，就把这张券过滤掉 （新模式下后面那个恒定为true）
            if (!IssueCouponTypeUtils.allowDisplayActivity(issueCouponActivity)) {
                continue;
            }

            CouponDetailInfo couponDetailInfo = ShopCartPromoInfoHelper.genCouponDetailItem(issueCouponActivity);

            couponDetailInfoMap.put(couponDetailInfo.getCouponGroupId(), couponDetailInfo);

        }
        return couponDetailInfoMap;
    }

}
