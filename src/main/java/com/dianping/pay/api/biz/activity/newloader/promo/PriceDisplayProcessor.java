package com.dianping.pay.api.biz.activity.newloader.promo;

import com.dianping.cat.Cat;
import com.dianping.mobile.framework.datatypes.IMobileContext;
import com.dianping.pay.api.biz.activity.newloader.dto.CouponActivityContext;
import com.dianping.pay.api.constants.ExtParamKeyConstant;
import com.dianping.pay.api.util.ClientTypeUtils;
import com.dianping.pay.api.util.DotUtils;
import com.dianping.pay.api.util.JsonUtils;
import com.google.common.collect.Lists;
import com.sankuai.dealuser.price.display.api.PriceDisplayService;
import com.sankuai.dealuser.price.display.api.enums.ExtensionKeyEnum;
import com.sankuai.dealuser.price.display.api.enums.ProductTypeEnum;
import com.sankuai.dealuser.price.display.api.enums.RequestSceneEnum;
import com.sankuai.dealuser.price.display.api.model.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.Callable;
import java.util.concurrent.Future;
import java.util.concurrent.TimeUnit;

@Component("priceDisplayProcessor")
@Slf4j
public class PriceDisplayProcessor extends AbstractPromoProcessor {

    @Resource
    private PriceDisplayService priceDisplayService;

    @Override
    public void prePare(CouponActivityContext promoCtx, IMobileContext iMobileContext) {
        if (!promoCtx.isNeedQueryPricePromotion()) {
            return;
        }
        DotUtils.dotForPromoProcessor(promoCtx, getPromoName());
        try {
            Future<PriceDisplayDTO> future = ExecutorService.submit(new Callable<PriceDisplayDTO>() {
                @Override
                public PriceDisplayDTO call() throws Exception {
                    return queryByPriceDisplay(promoCtx, iMobileContext);
                }
            });
            promoCtx.setPriceDisplayPromotionFuture(future);
        } catch (Exception e) {
            log.error("PriceDisplayProcessor# prepare error. ctx: {}", promoCtx, e);
        }
    }

    @Override
    public void loadPromo(CouponActivityContext promoCtx) {
        try {
            Future<PriceDisplayDTO> priceDisplayPromotionFuture = promoCtx.getPriceDisplayPromotionFuture();
            if (priceDisplayPromotionFuture != null) {
                PriceDisplayDTO priceDisplayDTO = priceDisplayPromotionFuture.get(500, TimeUnit.MILLISECONDS);
                DotUtils.dotForPromoHit(getPromoName(), promoCtx.isMt(), priceDisplayDTO != null);
                promoCtx.setPriceDisplayDTO(priceDisplayDTO);
            }
        } catch (Exception e) {
            log.error("PriceDisplayProcessor# loadPromo error. ctx: {}", promoCtx, e);
        }
    }

    @Override
    public String getPromoName() {
        Cat.logEvent("INVALID_METHOD_1", "com.dianping.pay.api.biz.activity.newloader.promo.PriceDisplayProcessor.getPromoName()");
        return "priceDisplay";
    }

    private PriceDisplayDTO queryByPriceDisplay(CouponActivityContext promoCtx, IMobileContext iMobileContext) {
        try {
            BatchPriceRequest request = new BatchPriceRequest();
            ProductIdentity identity;
            if (promoCtx.getProductType() == com.dianping.pay.api.enums.ProductTypeEnum.DEAL_GROUP.getCode()) {
                identity = new ProductIdentity(promoCtx.isMt() ? promoCtx.getMtDealGroupId() : promoCtx.getDpDealGroupId(), ProductTypeEnum.DEAL.getType());
            } else if (promoCtx.getQueryShopCouponType() == com.dianping.pay.api.enums.ProductTypeEnum.SKU.getCode()) {
                identity = new ProductIdentity(promoCtx.getSkuId(), ProductTypeEnum.PRODUCT.getType());
            } else {
                identity = new ProductIdentity(promoCtx.getSpuGroupId(), ProductTypeEnum.SPU.getType());
            }
            Map<String, String> extParams = new HashMap<>();
            if (MapUtils.isNotEmpty(promoCtx.getExtParam())) {
                if (promoCtx.getExtParam().containsKey(ExtParamKeyConstant.odpFloorId)) {
                    extParams.put(ExtensionKeyEnum.OdpFloorId.getDesc(), promoCtx.getExtParam().get(ExtParamKeyConstant.odpFloorId));
                }
                if (promoCtx.getExtParam().containsKey(ExtParamKeyConstant.odpLaunchId)) {
                    extParams.put(ExtensionKeyEnum.OdpLaunchId.getDesc(), promoCtx.getExtParam().get(ExtParamKeyConstant.odpLaunchId));
                }
            }
            identity.setExtParams(extParams);
            HashMap<Long, List<ProductIdentity>> longShopId2ProductIds = new HashMap<>();
            Long shopId = promoCtx.isMt() ? promoCtx.getMtShopIdL() : promoCtx.getDpShopIdL();
            longShopId2ProductIds.put(shopId, Lists.newArrayList(identity));
            request.setLongShopId2ProductIds(longShopId2ProductIds);
            ClientEnv clientEnv = new ClientEnv();
            clientEnv.setCityId(promoCtx.getCityId());
            clientEnv.setClientType(ClientTypeUtils.getDealClientType(promoCtx.getCouponPlatform(), promoCtx.isMt()));
            clientEnv.setVersion(promoCtx.getVersion());
            clientEnv.setUuid(promoCtx.getDpid());
            clientEnv.setUnionId(promoCtx.getDpid());
            request.setClientEnv(clientEnv);
            request.setScene(RequestSceneEnum.PROMO_DETAIL_DESC_RETAIL.getScene());
            request.setUserId(promoCtx.getUserId());
            HashMap<String, String> extension = new HashMap<>();
            extension.put(ExtensionKeyEnum.Source.getDesc(), promoCtx.getChannel());
            request.setExtension(extension);
            PriceResponse<Map<Long, List<PriceDisplayDTO>>> response = priceDisplayService.batchQueryPriceByLongShopId(request);
            if (response == null || !response.isSuccess() || MapUtils.isEmpty(response.getData()) || !response.getData().containsKey(shopId) || CollectionUtils.isEmpty(response.getData().get(shopId))) {
                log.error("PriceDisplayProcessor# call queryPrice exception. request: {}, response: {}", JsonUtils.toJson(request), JsonUtils.toJson(response));
                return null;
            }
            return response.getData().get(shopId).get(0);
        } catch (Exception e) {
            log.error("PriceDisplayProcessor# call queryPrice execption. context={}, e", JsonUtils.toJson(promoCtx), e);
            return null;
        }
    }
}
