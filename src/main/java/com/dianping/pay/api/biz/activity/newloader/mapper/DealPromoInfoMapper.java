package com.dianping.pay.api.biz.activity.newloader.mapper;


import com.dianping.cat.Cat;
import com.dianping.gm.bonus.exposure.api.enums.BonusTypeEnum;
import com.dianping.gmkt.coupon.common.api.enums.CouponGroupValueType;
import com.dianping.gmkt.scene.api.delivery.dto.res.CouponDetailDTO;
import com.dianping.gmkt.scene.api.delivery.enums.RecallTypeEnum;
import com.dianping.lion.client.Lion;
import com.dianping.mobile.framework.datatypes.IMobileContext;
import com.dianping.pay.api.beans.IssueCouponActivity;
import com.dianping.pay.api.beans.ResourcePromotionDo;
import com.dianping.pay.api.biz.activity.newloader.dto.CouponActivityContext;
import com.dianping.pay.api.constants.CatEventConstants;
import com.dianping.pay.api.constants.PlusIcons;
import com.dianping.pay.api.entity.issuecoupon.*;
import com.dianping.pay.api.enums.LeadActionEnum;
import com.dianping.pay.api.enums.PromoTypeEnum;
import com.dianping.pay.api.enums.QueryShopCouponTypeEnum;
import com.dianping.pay.api.repository.LeafRepository;
import com.dianping.pay.api.util.*;
import com.dianping.pay.promo.common.enums.PromoSource;
import com.dianping.pay.promo.common.enums.promo.PromoTimeFormatType;
import com.dianping.pay.promo.common.utils.coupon.UnifiedCouponGroupUtils;
import com.dianping.pay.promo.display.api.dto.EachPromoDetailComposition;
import com.dianping.pay.promo.display.api.dto.PromoDisplayDTO;
import com.dianping.unified.coupon.manage.api.dto.DiscountCouponDTO;
import com.dianping.unified.coupon.manage.api.dto.UnifiedCouponDTO;
import com.dianping.unified.coupon.manage.api.dto.UnifiedCouponGroupDTO;
import com.google.common.collect.Lists;
import com.sankuai.nibmktproxy.queryclient.proxy.*;
import com.sankuai.pay.access.client.predisplay.dto.ActivityPromoDTO;
import com.sankuai.pay.access.client.predisplay.dto.PrePromoDTO;
import java.util.Objects;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang.time.DateUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

// 团购详情页领券组件格式
@Component
@Slf4j
public class DealPromoInfoMapper {

    @Autowired
    private LeafRepository leafRepository;

    public CouponProductPromoModule buildCouponProductPromoModule(CouponActivityContext context, IMobileContext iMobileContext) {
        CouponProductPromoModule productPromoModule = new CouponProductPromoModule();
        try {
            CouponConcisePromoInfo concisePromoInfo = buildConcisePromoModule(context, iMobileContext);
            CouponDetailPromoInfo couponDetailPromoInfo = buildDetailPromoModule(context, iMobileContext);
            CouponProxyPromoInfo couponProxyPromoInfo = buildPromoAggInfo(context);


            if (concisePromoInfo != null && (containsIdlePromo(concisePromoInfo) || containsFinancialPromo(concisePromoInfo) || couponDetailPromoInfo != null || couponProxyPromoInfo != null)) {
                productPromoModule.setDetailPromoInfo(couponDetailPromoInfo);
                productPromoModule.setConcisePromoInfo(concisePromoInfo);
            }
            productPromoModule.setPromoAggInfo(couponProxyPromoInfo);
        } catch (Exception e) {
            log.error("buildCouponProductPromoModule exception", e);
        }
        return productPromoModule;
    }


    /**
     * 构建h5返回体
     * @return
     */
    public CouponProductHttpPromoModule buildCouponProductHttpPromoModule(CouponActivityContext context, IMobileContext iMobileContext) {
        CouponProductHttpPromoModule productPromoModule = new CouponProductHttpPromoModule();
        try {

            CouponConcisePromoInfo couponConcisePromoInfo = buildConcisePromoModule(context, iMobileContext);
            if (couponConcisePromoInfo != null && CollectionUtils.isNotEmpty(couponConcisePromoInfo.getPromoInfoItems())) {
                List<CouponPromoInfo> couponPromoInfos = couponConcisePromoInfo.getPromoInfoItems();
                couponPromoInfos.removeIf(couponPromoInfo -> couponPromoInfo.getType() == PromoTypeEnum.IDLE_HOURS.getCode());
            }

            productPromoModule.setConcisePromoInfo(buildConcisePromoModule(context, iMobileContext));
            CouponDetailHttpPromoInfo detailHttpPromoInfo = new CouponDetailHttpPromoInfo();
            productPromoModule.setDetailPromoInfo(detailHttpPromoInfo);

            CouponProxyPromoInfo couponProxyPromoInfo = buildPromoAggInfo(context);
            if (couponProxyPromoInfo != null) {
                detailHttpPromoInfo.setPlatformCoupons(couponProxyPromoInfo.getPlatformCoupons());
                detailHttpPromoInfo.setMerchantCoupons(couponProxyPromoInfo.getMerchantCoupons());
            } else {
                // 拼接券详细信息
                List<CouponDetailInfo>  couponInfoItem = buildCouponDetailInfos(context, iMobileContext);
                detailHttpPromoInfo.setMerchantCoupons(couponInfoItem);
            }

            // 拼接立减详细信息
            CouponPromoInfo promoInfoItem = buildDetailPromoInfo(context.getPromoDisplayDTOS(), context);
            detailHttpPromoInfo.setReductionPromo(promoInfoItem);
        } catch (Exception e) {
            log.error("buildCouponProductHttpPromoModule exception", e);
        }
        return productPromoModule;
    }


    private boolean containsFinancialPromo(CouponConcisePromoInfo concisePromoInfo) {
        return concisePromoInfo.getPromoInfoItems().stream().anyMatch(item -> item.getType() == PromoTypeEnum.FINALCIAL_COUPON_PROMO.getCode());
    }

    private boolean containsIdlePromo(CouponConcisePromoInfo concisePromoInfo) {
        return concisePromoInfo.getPromoInfoItems().get(0).getType() == PromoTypeEnum.IDLE_HOURS.getCode();
    }

    public CouponProxyPromoInfo buildPromoAggInfo(CouponActivityContext context) {
        PromotionDTOResult promoResult = context.getPromotionDTOResult();
        if (promoResult == null) {
            return null;
        }
        //走不走团单新样式，都会build政府消费券
        List<CouponDetailInfo> govConsumeCoupons = buildFinancialGovConsumeCoupons(promoResult);

        // 团单不走新样式的，这里也不操作
        if (context.getQueryShopCouponType() == QueryShopCouponTypeEnum.BY_DEAL.getCode() && !context.isDealPromoProxy() && CollectionUtils.isEmpty(govConsumeCoupons)) {
            return null;
        }


        // 新团详需要再内部平台券中拼接
        boolean existResource = false;
        ResourcePromotionDo resourcePromotionDo = context.getResourceExposure();
        String couponId = null;
        if (resourcePromotionDo != null && resourcePromotionDo.getDrawStatus() == 1) {
            couponId = resourcePromotionDo.getDetailDTO().getCouponId();
        }

        List<CouponDetailInfo> merchantCoupons = new ArrayList<>();
        List<CouponDetailInfo> platformCoupons = new ArrayList<>();
        List<GetPromotionDTO> promos = promoResult.getGetPromotionDTO();

        // 整体排序

        List<GetPromotionDTO> couponPromos = Lists.newArrayList();
        if (CollectionUtils.isEmpty(promos)) {
            return null;
        }
        for (GetPromotionDTO promo : promos) {
            if (isCouponPromo(promo)) {
                if (context.getNeedDiscountCoupon() == null || !context.getNeedDiscountCoupon()) {
                    if (promo.getPromotionDTO().getCouponDTO().getCouponValueType() == CouponGroupValueType.DISCOUNT.getCode()) {
                        continue;
                    }
                }
                couponPromos.add(promo);
            }
        }

        if (CollectionUtils.isEmpty(couponPromos) && CollectionUtils.isEmpty(govConsumeCoupons)) {
            return null;
        }

        couponPromos.sort(new ProxyPromoComparator());


        for (GetPromotionDTO promo : couponPromos) {
            if (!isCouponPromo(promo)) {
                continue;
            }

            CouponDTO couponDTO = promo.getPromotionDTO().getCouponDTO();
            CouponDetailInfo item = ProxyPromoInfoHelper.genCouponDetailItem(couponDTO, context, null);

            if (couponDTO.isIsMerchantCoupon()) {
                merchantCoupons.add(item);
            } else {
                if (StringUtils.isNotBlank(couponId) && couponId.equals(promo.getPromotionId())) {
                    existResource = true;
                }
                platformCoupons.add(item);
            }
        }
        if (StringUtils.isNotBlank(couponId) && !existResource) {
            platformCoupons.add(ProxyPromoInfoHelper.genCouponDetailItemByResourcePromo(resourcePromotionDo));
        }

        if (CollectionUtils.isEmpty(merchantCoupons) && CollectionUtils.isEmpty(platformCoupons) && CollectionUtils.isEmpty(govConsumeCoupons)) {
            return null;
        }
        CouponProxyPromoInfo info = new CouponProxyPromoInfo();
        info.setMerchantCoupons(merchantCoupons);
        info.setPlatformCoupons(platformCoupons);
        info.setGovConsumeCoupons(govConsumeCoupons);
        return info;
    }

    private List<CouponDetailInfo> buildFinancialGovConsumeCoupons(PromotionDTOResult promoResult) {
        if(Lion.getBoolean(LionConstants.APP_KEY, LionConstants.FINANCIAL_GOV_CONSUME_PROMO_DEGRADE_SWITCH, true)) {
            return new ArrayList<>();
        }
        List<CouponDetailInfo> govConsumeCoupons = new ArrayList<>();
        if(CollectionUtils.isNotEmpty(promoResult.getGetPromotionDTO())) {
            for (GetPromotionDTO promo : promoResult.getGetPromotionDTO()) {
                if (promo == null || !ProxyPromoInfoHelper.isFinancialGovConsumeCouponPromo(promo)) {
                    continue;
                }
                PromotionDTO promotionDTO = promo.getPromotionDTO();
                CouponDetailInfo item = ProxyPromoInfoHelper.genFinancialGovConsumeCouponDetailItem(promotionDTO);
                if(item == null) {
                    continue;
                }
                govConsumeCoupons.add(item);
            }
            if(CollectionUtils.isNotEmpty(govConsumeCoupons)) {
                List<Long> serialIdList = leafRepository.batchGenFinancialConsumeSerialId(govConsumeCoupons.size());
                if(CollectionUtils.isEmpty(serialIdList) || serialIdList.size() != govConsumeCoupons.size()) {
                    govConsumeCoupons = new ArrayList<>();
                } else {
                    int i = 0;
                    for(CouponDetailInfo couponDetailInfo : govConsumeCoupons) {
                        couponDetailInfo.setSerialNo(serialIdList.get(i++).toString());
                    }
                }
            }
        }
        return govConsumeCoupons;
    }

    private static boolean isCouponPromo(GetPromotionDTO promo) {
        if (promo.getPromotionDTO() == null || promo.getPromotionDTO().getCouponDTO() == null) {
            return false;
        }
        return promo.getPromotionType() != null && promo.getPromotionType().getValue() == PromotionType.COUPON.getValue();
    }

    private CouponDetailPromoInfo buildDetailPromoModule(CouponActivityContext context, IMobileContext iMobileContext) {
        CouponDetailPromoInfo result = new CouponDetailPromoInfo();
        result.setTitle("优惠");
        List<CouponPromoInfo> detailPromoInfoItems = Lists.newArrayList();
        result.setPromoInfoItems(detailPromoInfoItems);

        boolean juhuasuan = ProxyPromoInfoHelper.isJuhuasuan(context);

        // 拼接投放信息
        CouponPromoInfo resourceInfoItem = null;
        // 拼接立减详细信息
        CouponPromoInfo promoInfoItem = null;
        // 拼接券详细信息
        CouponPromoInfo couponInfoItem = null;
        // 拼接返礼详细信息
        CouponPromoInfo bonusInfoItem = null;

        if (juhuasuan) {
            promoInfoItem = buildDetailPromoInfoForJhs(context);
            couponInfoItem = buildDetailCouponInfoWithFinancialPromo(context, iMobileContext, false);
            bonusInfoItem = buildDetailBonusInfo(context);
        } else {
            resourceInfoItem = buildDetailResourceExposures(context, iMobileContext);
            promoInfoItem = buildDetailPromoInfo(context.getPromoDisplayDTOS(), context);
            couponInfoItem = buildDetailCouponInfoWithFinancialPromo(context, iMobileContext, true);
            bonusInfoItem = buildDetailBonusInfo(context);
        }

        addPromo(detailPromoInfoItems, resourceInfoItem);
        addPromo(detailPromoInfoItems, promoInfoItem);
        addPromo(detailPromoInfoItems, bonusInfoItem);
        addPromo(detailPromoInfoItems, couponInfoItem);
        if (CollectionUtils.isEmpty(detailPromoInfoItems)) {
            return null;
        }
        return result;
    }

    private CouponPromoInfo buildDetailCouponInfoWithFinancialPromo(CouponActivityContext context, IMobileContext iMobileContext, boolean needBuildShopActivity) {
        CouponPromoInfo couponInfoItem = null;

        if (needBuildShopActivity) {
            couponInfoItem = buildDetailShopCouponInfo(context, iMobileContext);
        }

        // 拼接现金券信息
        PrePromoDTO financialCoupon = context.getFinancialCouponPromotion();
        if (financialCoupon == null) {
            return couponInfoItem;
        }
        if (MapUtils.isEmpty(financialCoupon.getActivityAreas())) {
            return couponInfoItem;
        }
        if (CollectionUtils.isEmpty(financialCoupon.getActivityAreas().get("1"))) {
            return couponInfoItem;
        }
        if (financialCoupon.getActivityAreas().get("1").get(0) == null) {
            return couponInfoItem;
        }
        List<ActivityPromoDTO> promoDetails = financialCoupon.getActivityAreas().get("1");
        ActivityPromoDTO hasGetPromo = promoDetails.stream().filter(item -> {
            return item.getPromoStatus() == 1;
        }).findFirst().get();
        if (hasGetPromo == null) {
            return couponInfoItem;
        }

        List<PromoDetailInfoItem> couponDescItems = Lists.newArrayList();

        PromoDetailInfoItem couponDescItem = new PromoDetailInfoItem();
        couponDescItem.setStatus(1);
        PromoCommonBtn btn = new PromoCommonBtn();
        btn.setTitle("已领取");
        btn.setActionType(PromoCommonBtn.ActionEnum.REDIRECT.type);
        couponDescItem.setButton(btn);
        couponDescItem.setAmountCornerMark("元");
        String amountText = PlusNumUtils.trimDecimal(convertNumStrFromFen(hasGetPromo.getValue()));
        String couponAmountText = amountText;
        if (context.isMt()) {
            couponDescItem.setTitle(JsonLabelUtil.couponTitleMTJson(hasGetPromo.getTitle(), hasGetPromo.getTitle()));
            couponDescItem.setAmount(JsonLabelUtil.couponAmountMTJson(couponAmountText, amountText));
        } else {
            couponDescItem.setAmount(amountText);
            couponDescItem.setTitle(JsonLabelUtil.couponTitleDPJson(hasGetPromo.getTitle(), couponAmountText));
        }
        String couponTimeDesc = "";
        long remainTime = hasGetPromo.getEndTime() * 1000 - System.currentTimeMillis();
        if (remainTime / DateUtils.MILLIS_PER_DAY > 0) {
            couponTimeDesc = remainTime / DateUtils.MILLIS_PER_DAY + "天后过期";
        } else if (DateUtils.isSameDay(new Date(hasGetPromo.getEndTime()), new Date())) {
            couponTimeDesc = "今天过期";
        } else {
            couponTimeDesc = "1天后过期";
        }
        String priceLimitDesc = null;
        if (hasGetPromo.getMinUseAmount() != null && hasGetPromo.getMinUseAmount() > 0) {
            priceLimitDesc = "满 " + convertNumStrFromFen(hasGetPromo.getMinUseAmount()) + " 可用";
        } else {
            priceLimitDesc = "无限制";
        }
        if (context.isMt()) {
            couponDescItem.setTimeDesc(JsonLabelUtil.couponAmountDescMTJson(couponTimeDesc, couponTimeDesc));
            // 门槛描述
            couponDescItem.setAmountDesc(JsonLabelUtil.couponAmountRulesMTJson(priceLimitDesc, priceLimitDesc));
        } else {
            couponDescItem.setTimeDesc(JsonLabelUtil.couponAmountDescDPJson(couponTimeDesc, couponTimeDesc));
            couponDescItem.setAmountDesc(JsonLabelUtil.couponAmountRulesDPJson(priceLimitDesc, priceLimitDesc));
        }
        couponDescItems.add(couponDescItem);
        if (couponInfoItem == null) {
            couponInfoItem = new CouponPromoInfo();
            couponInfoItem.setType(3);
            couponInfoItem.setPromoTitle("抵用券");
            couponInfoItem.setStyle(1);
        }
        if (couponInfoItem.getPromoInfoDetailItems() == null) {
            couponInfoItem.setPromoInfoDetailItems(Lists.newArrayList());
        }
        couponInfoItem.getPromoInfoDetailItems().addAll(couponDescItems);
        return couponInfoItem;
    }



    private CouponPromoInfo buildDetailPromoInfoForJhs(CouponActivityContext context) {
        PromotionDTOResult promoResult = context.getPromotionDTOResult();

        List<GetPromotionDTO> bestPromotionList = ProxyPromoInfoHelper.getBestDeductionPromotionList(promoResult);
        String fullDiscountDeductionDesc = ProxyPromoInfoHelper.getFullDiscountDeductionDesc(promoResult);
        if (CollectionUtils.isEmpty(bestPromotionList) && StringUtils.isBlank(fullDiscountDeductionDesc)) {
            return null;
        }

        CouponPromoInfo result = new CouponPromoInfo();
        result.setType(1);
        result.setPromoTitle("立减");
        List<PromoSimpleInfoItem> detailPromoInfoDescItems = Lists.newArrayList();
        result.setPromoInfoDescItems(detailPromoInfoDescItems);

        // 新老处理逻辑会有不同
        boolean newReductionStyle = ReductionUtils.isNewPromoStyle(context.getQueryShopCouponType(), context.getDpDealGroupId(), context.isMt(), context.getShopDTO());

        if (newReductionStyle) {
            if (CollectionUtils.isNotEmpty(bestPromotionList)) {
                for (GetPromotionDTO getPromotionDTO1 : bestPromotionList) {
                    if (getPromotionDTO1 == null || getPromotionDTO1.getPromotionDTO() == null) {
                        continue;
                    }
                    DeductionDTO deductionDTO = getPromotionDTO1.getPromotionDTO().getDeductionDTO();
                    ProductDisplayShow productDisplayShow = promoResult.getProductExtraInfo().getProductDisplayShow();
                    Map<Integer, ProductShowDTO> productShowMap = productDisplayShow.getPrductShowMap();
                    StringBuffer sb = new StringBuffer();

                    ProductShowDTO productShowDTO;
                    if (deductionDTO.getDzPromoSource() == PromoSource.shopSource.getCode()) {
                        // 商家立减
                        productShowDTO = productShowMap.get(ProductShowEnum.SHOP_DEDUCTION_DESCRIPTION.getValue());
                        sb.append("商家立减");
                    } else if (context.isMt()) {
                        // 平台立减
                        productShowDTO = productShowMap.get(ProductShowEnum.PLATFORM_DEDUCTION_DESCRIPTION.getValue());
                        sb.append("美团立减");
                    } else {
                        productShowDTO = productShowMap.get(ProductShowEnum.PLATFORM_DEDUCTION_DESCRIPTION.getValue());
                        sb.append("平台立减");
                    }
                    if (productShowDTO != null && StringUtils.isNotBlank(productShowDTO.getValue())) {
                        sb.append("：").append(productShowDTO.getValue());
                        detailPromoInfoDescItems.add(new PromoSimpleInfoItem().setText(sb.toString()));
                    } else {
                        //去掉满件折的干扰
                        if (StringUtils.isBlank(fullDiscountDeductionDesc)) {
                            // 文案渲染有误，打点告警出来
                            Cat.logEvent(CatEventConstants.PROXY_RESULT_ILLEGAL, "-1");
                            log.warn("[proxyData] empty. result: {}", promoResult);
                        }
                    }
                }
            }
            if (StringUtils.isNotBlank(fullDiscountDeductionDesc)) {
                detailPromoInfoDescItems.add(new PromoSimpleInfoItem().setText(fullDiscountDeductionDesc));
            }
        } else {
            String deductionTag = ProxyPromoInfoHelper.getDeductionTag(promoResult);
            if (StringUtils.isNotBlank(deductionTag)) {
                detailPromoInfoDescItems.add(new PromoSimpleInfoItem().setText(deductionTag));
            } else {
                // 文案渲染有误，打点告警出来
                Cat.logEvent(CatEventConstants.PROXY_RESULT_ILLEGAL, "-1");
                log.warn("[proxyData] empty. result: {}", promoResult);
            }
        }
        return result;
    }

    private CouponPromoInfo buildDetailPromoInfo(List<PromoDisplayDTO> promoDisplayList, CouponActivityContext context) {
        //价格力优化
        if (context.isBeMemberPriceProxy()) {
            return buildMemberPriceDetailPromoInfo(context);
        }

        String fullDiscountDeductionDesc = ProxyPromoInfoHelper.getFullDiscountDeductionDesc(context.getPromotionDTOResult());
        promoDisplayList = ReductionPromoHelper.getNormalPromo(promoDisplayList);
        if (CollectionUtils.isEmpty(promoDisplayList) && StringUtils.isBlank(fullDiscountDeductionDesc)) {
            return null;
        }

        CouponPromoInfo result = new CouponPromoInfo();
        result.setType(1);
        result.setPromoTitle("立减");
        List<PromoSimpleInfoItem> detailPromoInfoDescItems = Lists.newArrayList();
        result.setPromoInfoDescItems(detailPromoInfoDescItems);

        // 新老处理逻辑会有不同
        boolean newReductionStyle = ReductionUtils.isNewPromoStyle(context.getQueryShopCouponType(), context.getDpDealGroupId(), context.isMt(), context.getShopDTO());

        if (newReductionStyle) {
            if (CollectionUtils.isNotEmpty(promoDisplayList)) {
                for (PromoDisplayDTO promoDisplayDTO : promoDisplayList) {
                    if (promoDisplayDTO == null) {
                        continue;
                    }
                    if (CollectionUtils.isEmpty(promoDisplayDTO.getEachPromoDetailCompositions())) {
                        detailPromoInfoDescItems.add(new PromoSimpleInfoItem().setText(promoDisplayDTO.getDescription()));
                    } else {
                        List<EachPromoDetailComposition> promoDetailCompositions = promoDisplayDTO.getEachPromoDetailCompositions();
                        for (EachPromoDetailComposition promoDetailComposition : promoDetailCompositions) {
                            StringBuffer sb = new StringBuffer();
                            if (promoDetailComposition.getPromoSource() == PromoSource.shopSource.getCode()) {
                                sb.append("商家立减");
                            } else if (context.isMt()) {
                                sb.append("美团立减");
                            } else {
                                sb.append("平台立减");
                            }
                            sb.append("：").append(promoDetailComposition.getDescription());
                            detailPromoInfoDescItems.add(new PromoSimpleInfoItem().setText(sb.toString()));
                        }
                    }
                }
            }
        } else {
            if (CollectionUtils.isNotEmpty(promoDisplayList)) {
                for (PromoDisplayDTO promoDisplayDTO : promoDisplayList) {
                    if (promoDisplayDTO == null || StringUtils.isBlank(promoDisplayDTO.getDescription())) {
                        continue;
                    }
                    detailPromoInfoDescItems.add(new PromoSimpleInfoItem().setText(promoDisplayDTO.getDescription()));
                }
            }
        }
        if (StringUtils.isNotBlank(fullDiscountDeductionDesc)) {
            detailPromoInfoDescItems.add(new PromoSimpleInfoItem().setText(fullDiscountDeductionDesc));
        }

        return result;
    }

    private CouponPromoInfo buildDetailBonusInfo(CouponActivityContext context) {
        if (context == null || context.getReturnPromotionDisplayDto() == null || CollectionUtils.isEmpty(context.getReturnPromotionDisplayDto().getReturnPromotionDetails())) {
            return null;
        }
        List<ReturnPromotionDetail> returnPromotionDetails = context.getReturnPromotionDisplayDto().getReturnPromotionDetails();
        CouponPromoInfo result = new CouponPromoInfo();
        result.setType(4);
        result.setPromoTitle("返礼");
        List<PromoSimpleInfoItem> detailPromoInfoDescItems = Lists.newArrayList();
        result.setPromoInfoDescItems(detailPromoInfoDescItems);
        for (ReturnPromotionDetail bonusExposureDTO : returnPromotionDetails) {
            if (bonusExposureDTO == null || StringUtils.isBlank(bonusExposureDTO.getSummaryInfo())) {
                continue;
            }
            String highLight = BonusTypeEnum.getBonusTypeEnumByCode(bonusExposureDTO.getBonusType()).getDesc();
            String bonusText = highLight + ":" + bonusExposureDTO.getReturnPromotionDetailText();
            String bonusDesc = context.isMt() ? JsonLabelUtil.bonusDescMTJson(bonusText, highLight) : JsonLabelUtil.bonusDescDPJson(bonusText, highLight);
            if (StringUtils.isEmpty(bonusDesc)) {
                continue;
            }
            detailPromoInfoDescItems.add(new PromoSimpleInfoItem().setText(bonusDesc));
        }
        if (Lion.getBooleanValue(LionConstants.BONUS_REDIRECT_ENABLE, false)) {
            result.setRedirectUrl(UrlHelper.getBonusUrl(context));
        }
        return result;
    }

    private CouponPromoInfo buildDetailResourceExposures(CouponActivityContext context, IMobileContext iMobileContext) {
        // 只有老团详内层需要拼接返利信息，新团详不需要
        if (context.isDealPromoProxy()) {
            return null;
        }
        ResourcePromotionDo resourcePromotionDo = context.getResourceExposure();
        if (resourcePromotionDo == null) {
            return null;
        }
        CouponPromoInfo promoInfoItem = new CouponPromoInfo();
        promoInfoItem.setType(7);
        promoInfoItem.setPromoTitle("投放资源");
        promoInfoItem.setStyle(2);
        PromoExposureInfoItem couponDescItem = loadResources(context);
        if (couponDescItem != null) {
            // 内层不弹窗
            couponDescItem.setNeedToast(false);
        }
        promoInfoItem.setPromoExposureInfoItem(couponDescItem);
        return promoInfoItem;
    }

    private CouponPromoInfo buildMemberPriceDetailPromoInfo(CouponActivityContext context) {
        Cat.logEvent("INVALID_METHOD_4", "com.dianping.pay.api.biz.activity.newloader.mapper.DealPromoInfoMapper.buildMemberPriceDetailPromoInfo(com.dianping.pay.api.biz.activity.newloader.dto.CouponActivityContext)");

        List<PromoSimpleInfoItem> detailPromoInfoDescItems = Lists.newArrayList();
        String shopReduceContent = MemberPriceCalculateMapper.getShopReduceContent(context.getBestProxyPromotionResult());
        if (StringUtils.isNotBlank(shopReduceContent)) {
            detailPromoInfoDescItems.add(new PromoSimpleInfoItem().setText("商家立减：" + shopReduceContent));
        }
        String platformReduceContent = MemberPriceCalculateMapper.getPlatformReduceContent(context.getBestProxyPromotionResult());
        if (StringUtils.isNotBlank(platformReduceContent)) {
            String platformPrefix = context.isMt() ? "美团立减：" : "平台立减：";
            detailPromoInfoDescItems.add(new PromoSimpleInfoItem().setText(platformPrefix + platformReduceContent));
        }
        String memberCardContent = MemberPriceCalculateMapper.getMemberCardContent(context.getBestProxyPromotionResult());
        if (StringUtils.isNotBlank(memberCardContent)) {
            String memberPrefix = MemberPriceCalculateMapper.isMemberDay(context.getBestProxyPromotionResult()) ? "会员日优惠：" : "会员优惠：";
            detailPromoInfoDescItems.add(new PromoSimpleInfoItem().setText(memberPrefix + memberCardContent));
        }
        String joyCardContent = MemberPriceCalculateMapper.getJoyCardContent(context.getBestProxyPromotionResult());
        if (StringUtils.isNotBlank(joyCardContent)) {
            detailPromoInfoDescItems.add(new PromoSimpleInfoItem().setText("会员优惠：" + joyCardContent));
        }

        String fullDiscountDeductionDesc = ProxyPromoInfoHelper.getFullDiscountDeductionDesc(context.getBestProxyPromotionResult());
        if (StringUtils.isNotBlank(fullDiscountDeductionDesc)) {
            detailPromoInfoDescItems.add(new PromoSimpleInfoItem().setText(fullDiscountDeductionDesc));
        }

        if (CollectionUtils.isEmpty(detailPromoInfoDescItems)) {
            return null;
        }

        CouponPromoInfo result = new CouponPromoInfo();
        result.setType(1);
        result.setPromoTitle("立减");
        result.setPromoInfoDescItems(detailPromoInfoDescItems);
        return result;
    }

    private static String formatTitle(String limitPrice) {
        Cat.logEvent("INVALID_METHOD_1", "com.dianping.pay.api.biz.activity.newloader.mapper.DealPromoInfoMapper.formatTitle(java.lang.String)");
        if (StringUtils.isBlank(limitPrice) || BigDecimal.ZERO.equals(new BigDecimal(limitPrice))) {
            return "无门槛";
        } else {
            return "满" + limitPrice + "元可用";
        }
    }

    private static PromoExposureInfoItem loadResources(CouponActivityContext context) {
        try {
            ResourcePromotionDo exposureResponseDTO = context.getResourceExposure();
            if (exposureResponseDTO == null) {
                return null;
            }
            // 如果是新团详，且是需要主动领券的，就屏蔽掉
            if (context.isDealPromoProxy()) {
                if (RecallTypeEnum.RECALL_PRE_DRAW_COUPON.getCode().equals(exposureResponseDTO.getRecallType())) {
                    return null;
                }
            }

            PromoExposureInfoItem couponDescItem = new PromoExposureInfoItem();
            couponDescItem.setFlowId(exposureResponseDTO.getFlowId());
            couponDescItem.setResourceActivityId(exposureResponseDTO.getActivityId());
            couponDescItem.setActivityId(exposureResponseDTO.getActivityId());
            couponDescItem.setMaterialId(exposureResponseDTO.getMaterialId());
            couponDescItem.setRowKey(exposureResponseDTO.getRowKey());
            couponDescItem.setCoupontype(1);

            CouponDetailDTO detailDTO = exposureResponseDTO.getDetailDTO();
            if (detailDTO == null) {
                return null;
            }
            couponDescItem.setAmount(detailDTO.getAmountPrice());
            Map<String, String> materialMap = exposureResponseDTO.getStaticMaterialContext();
            String toastContent = null;
            String prizeTitle = null;
            String prizeInfo = null;
            // 是否展示门槛
            String showCouponLimitDesc = null;
            if (MapUtils.isNotEmpty(materialMap)) {
                prizeTitle = materialMap.get("prizeTitle");
                prizeInfo = materialMap.get("prizeInfo");
                toastContent = materialMap.get("toastTitle");
                showCouponLimitDesc = materialMap.get("showCouponLimitDesc");
            }
            couponDescItem.setTitle(prizeTitle);

            StringBuilder subTitle = new StringBuilder();
            if (Boolean.TRUE.toString().equals(showCouponLimitDesc)) {
                subTitle.append(formatTitle(detailDTO.getLimitPrice()));
            } else if (StringUtils.isNotBlank(prizeInfo)) {
                if (StringUtils.isNotBlank(subTitle)) {
                    subTitle.append(" | ");
                }
                subTitle.append(prizeInfo);
            }

            boolean randomExpandCoupon = detailDTO.getRandomExpandMaxPrice() != null;
            if (randomExpandCoupon) {
                couponDescItem.setCouponValueType(CouponGroupValueType.RANDOM_EXPAND_COUPON.getCode());
            } else {
                // 非膨胀券目前都用普通满减进行表示
                couponDescItem.setCouponValueType(CouponGroupValueType.COMMON_COUPON.getCode());
            }
            if (detailDTO.getRandomExpandMaxPrice() != null) {
                if (StringUtils.isNotBlank(subTitle)) {
                    subTitle.append(",");
                }
                subTitle.append(String.format("最高膨胀至￥%s", detailDTO.getRandomExpandMaxPrice()));
            }
            couponDescItem.setSubTitle(subTitle.toString());

            if (exposureResponseDTO.getDrawStatus() == 1) {
                couponDescItem.setCanAssign(false);
                // 如果是已领取的，设置使用时间
                couponDescItem.setUseBeginTime(detailDTO.getUseBeginTime());
                int dayGap = randomExpandCoupon ? Lion.getIntValue(LionConstants.RANDOM_COUPON_COUNT_DOWN_TIME_GAP, 5) : 1;
                String timeDesc = IssueCouponUtils.formatResourceTimePeriod(new Date(detailDTO.getUseEndTime()), dayGap);
                // 目前标题为固定文案，大于一定阈值，展示X天，小于一定阈值，直接返回时间，前端按倒计时形式展示
                // 如果剩余天数过大，"有效期仅剩" 文案会有一定歧义，目前产品侧表示目前无该类情况，先打点看下，如果有后续再推动交互优化
                couponDescItem.setTimeDesc("有效期仅剩");
                if (StringUtils.isNotBlank(timeDesc)) {
                    couponDescItem.setTimeSubDesc(timeDesc);
                } else {
                    couponDescItem.setUseEndTime(detailDTO.getUseEndTime());
                }

                if (exposureResponseDTO.isCurrentIssue()) {
                    couponDescItem.setToastContent(toastContent);
                    couponDescItem.setNeedToast(true);
                }
            } else {
                // 设置成可领取
                couponDescItem.setCanAssign(true);
            }
            return couponDescItem;
        } catch (Exception e) {
            log.error("loadResources error.", e);
            return null;
        }
    }


    private List<CouponDetailInfo> buildCouponDetailInfos(CouponActivityContext context, IMobileContext iMobileContext) {
        List<IssueCouponActivity> activityDTOS = context.getIssueCouponActivities();
        if (CollectionUtils.isEmpty(activityDTOS)) {
            return null;
        }
        List<CouponDetailInfo> couponDescItems = Lists.newArrayList();
        for (IssueCouponActivity issueCouponActivity : activityDTOS) {
            if (issueCouponActivity == null || issueCouponActivity.getCouponGroup() == null || issueCouponActivity.getCouponGroup().getDiscountAmount().compareTo(BigDecimal.ZERO) <= 0) {
                continue;
            }
            boolean isIssued = IssueCouponTypeUtils.isIssued(issueCouponActivity);

            CouponDetailInfo couponDescItem = new CouponDetailInfo();
            String couponGroupId = StringUtils.isNotBlank(issueCouponActivity.getCouponGroup().getUnifiedCouponGroupId()) ? issueCouponActivity.getCouponGroup().getUnifiedCouponGroupId() : String.valueOf(issueCouponActivity.getCouponGroupId());
            couponDescItem.setCouponGroupId(couponGroupId);
            couponDescItem.setCanAssign(!isIssued);

            String couponTileText = issueCouponActivity.getCouponDesc();
            couponDescItem.setTitle(couponTileText);

            String couponTimeDesc = IssueCouponUtils.formatCouponTimePeriod(issueCouponActivity.getCouponGroup());
            couponDescItem.setCouponSuitDesc(couponTimeDesc);

            String couponRulesText = buildDetailCouponDescText(issueCouponActivity);
            couponDescItem.setCouponThresholdDesc(couponRulesText);
            couponDescItem.setCouponSrc(1);
            couponDescItem.setAmount(issueCouponActivity.getCouponGroup().getDiscountAmount().stripTrailingZeros().toPlainString());

            couponDescItems.add(couponDescItem);

        }
        if (CollectionUtils.isEmpty(couponDescItems)) {
            return null;
        }
        return couponDescItems;
    }

    private CouponPromoInfo buildDetailShopCouponInfo(CouponActivityContext context, IMobileContext iMobileContext) {
        List<IssueCouponActivity> activityDTOS = context.getIssueCouponActivities();
        if (CollectionUtils.isEmpty(activityDTOS)) {
            return null;
        }
        CouponPromoInfo promoInfoItem = new CouponPromoInfo();
        promoInfoItem.setType(3);
        promoInfoItem.setPromoTitle("抵用券");
        promoInfoItem.setStyle(1);
        List<PromoDetailInfoItem> couponDescItems = Lists.newArrayList();
        for (IssueCouponActivity issueCouponActivity : activityDTOS) {
            if (issueCouponActivity == null || issueCouponActivity.getCouponGroup() == null || issueCouponActivity.getCouponGroup().getDiscountAmount().compareTo(BigDecimal.ZERO) <= 0) {
                continue;
            }

            int couponValueType = issueCouponActivity.getCouponGroup().getValueType();
            if (context.getNeedDiscountCoupon() == null || !context.getNeedDiscountCoupon()) {
                if (couponValueType == CouponGroupValueType.DISCOUNT.getCode()) {
                    continue;
                }
            }

            boolean isIssued = IssueCouponTypeUtils.isIssued(issueCouponActivity);

            PromoDetailInfoItem couponDescItem = new PromoDetailInfoItem();
            couponDescItem.setCouponGroupId(issueCouponActivity.getCouponGroupId());
            couponDescItem.setStatus(isIssued ? 1 : 0);
            couponDescItem.setEncryptCouponGroupId(issueCouponActivity.getCouponGroup().getUnifiedCouponGroupId());

            PromoCommonBtn btn = new PromoCommonBtn();

            couponDescItem.setButton(btn);


            if (!isIssued) {
                btn.setTitle("点击领取");
                btn.setClickUrl("");
                btn.setActionType(PromoCommonBtn.ActionEnum.SHOW.type);
            } else {
                //
                int payPlatform = context.getCouponPlatform();
                // 非app目前不展示去使用链接了
                if (Lion.getBooleanValue(LionConstants.NOAPP_NOT_SHOW_CLICK_URL_SWITCH, false) && !ClientTypeUtils.isMainWeb(payPlatform, context.isMt())) {
                    btn.setTitle("已领取");
                    btn.setActionType(PromoCommonBtn.ActionEnum.REDIRECT.type);
                } else {
                    btn.setTitle("去使用");
                    btn.setActionType(PromoCommonBtn.ActionEnum.REDIRECT.type);
                    UnifiedCouponDTO unifiedCouponDTO = IssueCouponTypeUtils.getIssuedUnifiedCouponDto(issueCouponActivity);
                    if (unifiedCouponDTO != null) {
                        String unifiedCouponId = unifiedCouponDTO.getUnifiedCouponId();
                        if (ClientTypeUtils.isMainWeb(payPlatform, context.isMt())) {
                            btn.setClickUrl(context.isMt() ?
                                    UrlHelper.getCouponMtWebUrl(unifiedCouponId) :
                                    UrlHelper.getCouponDpWebUrl(unifiedCouponId));
                        } else if (ClientTypeUtils.isMainWX(payPlatform, context.isMt())) {
                            btn.setClickUrl(context.isMt() ?
                                    UrlHelper.getWxBaseShareUrl(UrlHelper.getCouponMtWebUrl(unifiedCouponId), true) :
                                    UrlHelper.getWxBaseShareUrl(UrlHelper.getCouponDpWebUrl(unifiedCouponId), false));
                        } else {
                            btn.setClickUrl(context.isMt() ?
                                    UrlHelper.getCouponMtUrl(unifiedCouponId) :
                                    UrlHelper.getCouponDpUrl(unifiedCouponId));
                        }
                    }
                }
            }

            if (context.getNeedDiscountCoupon() == null || !context.getNeedDiscountCoupon()) {
                // 非折扣券的赋值方式通通不变
                String couponTileText = issueCouponActivity.getCouponDesc();
                String amountText = PlusNumUtils.trimDecimal(issueCouponActivity.getCouponGroup().getDiscountAmount().toString());
                String couponAmountText = "￥ " + amountText;
                if (context.isMt()) {
                    couponDescItem.setTitle(JsonLabelUtil.couponTitleMTJson(couponTileText, couponTileText));
                    couponDescItem.setAmount(JsonLabelUtil.couponAmountMTJson(couponAmountText, amountText));
                } else {
                    couponDescItem.setAmount(amountText);
                    couponDescItem.setTitle(JsonLabelUtil.couponTitleDPJson(couponTileText, couponAmountText));
                }
            } else {
                // 新的满减券标题也进行变更了
                UnifiedCouponGroupDTO couponGroup = issueCouponActivity.getCouponGroup();
                String amountText;
                if (couponValueType == CouponGroupValueType.DISCOUNT.getCode()) {
                    couponDescItem.setAmountCornerMark("折");
                    amountText = (double) couponGroup.getDiscountCouponDTO().getDiscount() / 10 + "";
                } else {
                    couponDescItem.setAmountCornerMark("元");
                    amountText = PlusNumUtils.trimDecimal(issueCouponActivity.getCouponGroup().getDiscountAmount().toString());
                }
                couponDescItem.setTitle(getCouponGroupDescTitle(couponGroup));
                couponDescItem.setAmount(amountText);
            }

            String couponTimeDesc = IssueCouponUtils.formatCouponTimePeriod(issueCouponActivity.getCouponGroup());
            String couponRulesText = buildDetailCouponDescText(issueCouponActivity);
            if (context.isMt()) {
                couponDescItem.setTimeDesc(JsonLabelUtil.couponAmountDescMTJson(couponTimeDesc, couponTimeDesc));
                // 门槛描述
                couponDescItem.setAmountDesc(JsonLabelUtil.couponAmountRulesMTJson(couponRulesText, couponRulesText));
            } else {
                couponDescItem.setTimeDesc(JsonLabelUtil.couponAmountDescDPJson(couponTimeDesc, couponTimeDesc));
                couponDescItem.setAmountDesc(JsonLabelUtil.couponAmountRulesDPJson(couponRulesText, couponRulesText));
            }

            couponDescItems.add(couponDescItem);
        }
        if (CollectionUtils.isEmpty(couponDescItems)) {
            return null;
        }
        promoInfoItem.setPromoInfoDetailItems(couponDescItems);
        return promoInfoItem;
    }


    private String getCouponGroupDescTitle(UnifiedCouponGroupDTO couponGroup) {
        String title = UnifiedCouponGroupUtils.isShopCoupon(couponGroup.getCouponGroupType()) ? "商家券" : "平台券";
        if (couponGroup.getValueType() == CouponGroupValueType.DISCOUNT.getCode()) {
            DiscountCouponDTO discountCouponDTO = couponGroup.getDiscountCouponDTO();
            if (discountCouponDTO.getDiscountMaxAmount() != null) {
                title += String.format("·最高可减%s元", discountCouponDTO.getDiscountMaxAmount().stripTrailingZeros().toPlainString());
            }
        }
        return title;
    }

    private CouponConcisePromoInfo buildConcisePromoModule(CouponActivityContext context, IMobileContext iMobileContext) {
        CouponConcisePromoInfo result = new CouponConcisePromoInfo();
        result.setTitle("优惠");
        List<CouponPromoInfo> concisePromoInfoItems = Lists.newArrayList();
        result.setPromoInfoItems(concisePromoInfoItems);

        boolean juhuasuan = ProxyPromoInfoHelper.isJuhuasuan(context);

        // 拼接闲时优惠 （type = 6）
        CouponPromoInfo idleHoursPromo = null;
        // 拼接立减 （type = 1）
        List<CouponPromoInfo> promoInfoItems = null;
        // 拼接券信息
        CouponPromoInfo couponInfoItem = null;
        // 拼接返礼优惠 （type = 4）
        CouponPromoInfo bonusInfoItem = null;
        // 拼接投放优惠 （type = 7）
        CouponPromoInfo resourcesInfoItem = null;
        // 丽人新样式聚合优惠 (type = 8)
        CouponPromoInfo combinePromoInfo = null;
        // 金融券 (type = 10)
        CouponPromoInfo finalcialCouponPromo = null;
        // 政府消费券 (type = 11)
        CouponPromoInfo govConsumeCouponPromo = null;

        if (juhuasuan) {
            promoInfoItems = buildConcisePromoInfoForJhs(context, iMobileContext);
            // 聚划算对于抵用券拼接的区别为不会额外拼接可领取商家券
            couponInfoItem = buildConciseCouponInfoWithFinalcialPromo(context, iMobileContext, false);
            bonusInfoItem = buildConciseBonusInfo(context, iMobileContext);
            finalcialCouponPromo = buildFinalcialCouponPromo(context, iMobileContext);
            govConsumeCouponPromo = buildConciseGovConsumeCouponPromo(context, iMobileContext, true);
        } else {
            idleHoursPromo = buildConciseIdleHoursPromo(context, iMobileContext);
            promoInfoItems = buildConcisePromoInfo(context, iMobileContext);
            couponInfoItem = buildConciseCouponInfoWithFinalcialPromo(context, iMobileContext, true);
            bonusInfoItem = buildConciseBonusInfo(context, iMobileContext);
            resourcesInfoItem = buildConciseResourcePromo(context, iMobileContext);
            combinePromoInfo = buildBeautyNewStyleCombinePromo(context, iMobileContext);
            finalcialCouponPromo = buildFinalcialCouponPromo(context, iMobileContext);
            govConsumeCouponPromo = buildConciseGovConsumeCouponPromo(context, iMobileContext, false);
        }




        addPromos(concisePromoInfoItems, promoInfoItems);
        addPromo(concisePromoInfoItems, couponInfoItem);
        addPromo(concisePromoInfoItems, bonusInfoItem);
        addPromo(concisePromoInfoItems, combinePromoInfo);
        addPromo(concisePromoInfoItems, govConsumeCouponPromo);

        if (!Boolean.TRUE.equals(context.getBeautyDealNewType())) {
            addPromo(concisePromoInfoItems, resourcesInfoItem);
        }


        if (CollectionUtils.isEmpty(concisePromoInfoItems) && idleHoursPromo == null && resourcesInfoItem == null && finalcialCouponPromo == null) {
            return null;
        }

        if (!Boolean.TRUE.equals(context.getBeautyDealNewType())) {
            // 非丽人新样式
            boolean newReductionStyle = ReductionUtils.isNewPromoStyle(context.getQueryShopCouponType(), context.getDpDealGroupId(), context.isMt(), context.getShopDTO());
            if (concisePromoInfoItems.size() == 1) {
                if (newReductionStyle) {
                    if (resourcesInfoItem != null) {
                        //只有投放券 不显示引导文案, 不跳转
                        result.setLeadAction(LeadActionEnum.NOOP.getCode());
                    } else if (couponInfoItem != null || govConsumeCouponPromo != null) {
                        //只有抵用券 显示去领取, 弹出浮层
                        result.setLeadAction(LeadActionEnum.TOAST_DEFAULT.getCode());
                        result.setLeadText("去领券");
                    } else if (CollectionUtils.isNotEmpty(promoInfoItems)) {
                        //只有立减 新版页面也弹出浮层
                        result.setLeadAction(LeadActionEnum.TOAST_DEFAULT.getCode());
                        result.setLeadText("查看详情");
                    } else if (bonusInfoItem != null) {
                        //只有返礼, 直接跳转
                        result.setLeadAction(LeadActionEnum.REDIRECT_URL.getCode());
                        result.setLeadRedirectUrl(bonusInfoItem.getRedirectUrl());
                        bonusInfoItem.setRedirectUrl(null);
                    }
                } else {
                    if (CollectionUtils.isNotEmpty(promoInfoItems) || resourcesInfoItem != null) {
                        //只有立减或只有资源位 不显示引导文案, 不跳转
                        result.setLeadAction(LeadActionEnum.NOOP.getCode());
                    } else if (couponInfoItem != null || govConsumeCouponPromo != null) {
                        //只有抵用券 显示去领取, 弹出浮层
                        result.setLeadAction(LeadActionEnum.TOAST_DEFAULT.getCode());
                        result.setLeadText("去领券");
                    } else if (bonusInfoItem != null) {
                        //只有返礼, 直接跳转
                        result.setLeadAction(LeadActionEnum.REDIRECT_URL.getCode());
                        result.setLeadRedirectUrl(bonusInfoItem.getRedirectUrl());
                        bonusInfoItem.setRedirectUrl(null);
                    }
                }
                //TODO 只有新客券的情况下
            } else if (concisePromoInfoItems.size() <= 3) {
                result.setLeadAction(LeadActionEnum.TOAST_DEFAULT.getCode());
                result.setLeadText("查看优惠");
            } else {
                result.setLeadAction(LeadActionEnum.TOAST_DEFAULT.getCode());
                result.setLeadText("更多优惠");
            }

            if (concisePromoInfoItems.size() > 1) {
                for (CouponPromoInfo item : concisePromoInfoItems) {
                    item.setLeadUrl("");
                    item.setLeadText("");
                    item.setLeadAction(LeadActionEnum.NOOP.getCode());
                }
                concisePromoInfoItems.get(0).setLeadText("查看优惠");
                concisePromoInfoItems.get(0).setLeadAction(LeadActionEnum.TOAST_DEFAULT.getCode());
            }

            if (idleHoursPromo != null) {
                concisePromoInfoItems.add(0, idleHoursPromo);
            }
            if (finalcialCouponPromo != null) {
                concisePromoInfoItems.add(finalcialCouponPromo);
            }
        } else {
            if (combinePromoInfo != null) {
                result.setLeadAction(combinePromoInfo.getLeadAction());
                result.setLeadText(combinePromoInfo.getLeadText());
            }
            if (idleHoursPromo != null) {
                concisePromoInfoItems.add(0, idleHoursPromo);
            }
            if (resourcesInfoItem != null) {
                concisePromoInfoItems.add(resourcesInfoItem);
            }
            if (finalcialCouponPromo != null) {
                concisePromoInfoItems.add(finalcialCouponPromo);
            }
        }
        return result;
    }

    private final static String HasGetCouponAreasKey = "1";

    private CouponPromoInfo buildConciseGovConsumeCouponPromo(CouponActivityContext context, IMobileContext iMobileContext, boolean fromJhs) {
        //增加政府券的降级开关
        if(Lion.getBoolean(LionConstants.APP_KEY, LionConstants.FINANCIAL_GOV_CONSUME_PROMO_DEGRADE_SWITCH, true)) {
            return null;
        }
        Cat.logEvent("BuildConciseConsumePromoEvent", "directReturn_" + (context.getBeautyDealNewType() == null ? "false" : context.getBeautyDealNewType().toString()) + "_" + String.valueOf(fromJhs));
        if (context.getBeautyDealNewType() != null && Boolean.TRUE.equals(context.getBeautyDealNewType()) && !fromJhs) {
            // 走丽人团单新样式的就直接返回
            return null;
        }
        return  ProxyPromoInfoHelper.genFinancialGovConsumeCouponInfoItem(context);
    }

    /**
     *
     * @param context
     * @param iMobileContext
     * @param needBuildShopActivity 是否跳过商家券渲染
     * @return
     */
    private CouponPromoInfo buildConciseCouponInfoWithFinalcialPromo(CouponActivityContext context, IMobileContext iMobileContext, boolean needBuildShopActivity) {
        CouponPromoInfo couponPromoInfo = buildConciseCouponInfo(context, iMobileContext, needBuildShopActivity);
        if (couponPromoInfo != null) {
            return couponPromoInfo;
        }
        PrePromoDTO financialCoupon = context.getFinancialCouponPromotion();
        if (financialCoupon == null) {
            return null;
        }
        if (MapUtils.isEmpty(financialCoupon.getActivityAreas())) {
            return null;
        }
        if (CollectionUtils.isEmpty(financialCoupon.getActivityAreas().get(HasGetCouponAreasKey))) {
            return null;
        }
        if (financialCoupon.getActivityAreas().get(HasGetCouponAreasKey).get(0) == null) {
            return null;
        }
        List<ActivityPromoDTO> promoDetails = financialCoupon.getActivityAreas().get(HasGetCouponAreasKey);
        ActivityPromoDTO hasGetPromo = promoDetails.stream().filter(item -> {
            return item.getPromoStatus() == 1;
        }).findFirst().get();
        if (hasGetPromo == null) {
            return null;
        }

        CouponPromoInfo promoInfoItem = new CouponPromoInfo();
        promoInfoItem.setType(PromoTypeEnum.FINALCIAL_COUPON_PROMO.getCode());
        promoInfoItem.setPromoTitle("抵用券");
        promoInfoItem.setIconUrl(context.isMt() ? PlusIcons.MT_COUPON : PlusIcons.DP_COUPON);
        List<PromoSimpleInfoItem> concisePromoInfoDescItems = Lists.newArrayList();

        PromoSimpleInfoItem promoInfoDescItem = new PromoSimpleInfoItem();
        promoInfoDescItem.setStyle(1);
        if (hasGetPromo.getValue() == null) {
            return null;
        }
        String priceLimit = hasGetPromo.getMinUseAmount() == null || hasGetPromo.getMinUseAmount() == 0 ? null : convertNumStrFromFen(hasGetPromo.getMinUseAmount());
        StringBuilder sb = new StringBuilder("美团支付");
        if (priceLimit != null) {
            sb.append("满").append(priceLimit);
        }
        sb.append("减").append(convertNumStrFromFen(hasGetPromo.getValue()));
        promoInfoDescItem.setText(sb.toString());
        concisePromoInfoDescItems.add(promoInfoDescItem);
        promoInfoItem.setPromoInfoDescItems(concisePromoInfoDescItems);
        promoInfoItem.setLeadText("去查看");
        promoInfoItem.setLeadAction(LeadActionEnum.TOAST_DEFAULT.getCode());
        return promoInfoItem;
    }

    private String convertNumStrFromFen(long num) {
        return BigDecimal.valueOf(num).divide(BigDecimal.valueOf(100)).stripTrailingZeros().toPlainString();
    }

    private CouponPromoInfo buildFinalcialCouponPromo(CouponActivityContext context, IMobileContext iMobileContext) {
        PrePromoDTO financialCoupon = context.getFinancialCouponPromotion();
        if (financialCoupon == null) {
            return null;
        }
        if (MapUtils.isEmpty(financialCoupon.getActivityAreas())) {
            return null;
        }
        if (CollectionUtils.isEmpty(financialCoupon.getActivityAreas().get("2"))) {
            return null;
        }
        if (financialCoupon.getActivityAreas().get("2").get(0) == null) {
            return null;
        }
        List<ActivityPromoDTO> promoDetails = financialCoupon.getActivityAreas().get("2");
        ActivityPromoDTO unGetPromo = promoDetails.stream().filter(item -> {
            return item.getPromoStatus() == 0;
        }).findFirst().get();
        if (unGetPromo == null) {
            return null;
        }
        String promoJsonText = JsonLabelUtil.financialCouponJson(unGetPromo.getShortTitle());
        CouponPromoInfo result = new CouponPromoInfo();
        result.setType(PromoTypeEnum.FINALCIAL_COUPON_PROMO.getCode());
        result.setPromoTitle("抵用券");
        result.setIconUrl(unGetPromo.getIcon());
        result.setPromoInfoDescItems(Lists.newArrayList(new PromoSimpleInfoItem().setText(promoJsonText)));
        result.setLeadText("去领取");
        result.setLeadAction(LeadActionEnum.REDIRECT_URL.getCode());
        result.setLeadUrl(unGetPromo.getLinkUrl());
        return result;
    }

    private void addPromo(List<CouponPromoInfo> promoInfoItems, CouponPromoInfo promoInfoItem) {
        if (promoInfoItem != null) {
            promoInfoItems.add(promoInfoItem);
        }
    }

    private void addPromos(List<CouponPromoInfo> promoInfoItems, List<CouponPromoInfo> subPromoInfoItems) {
        if (CollectionUtils.isNotEmpty(subPromoInfoItems)) {
            promoInfoItems.addAll(subPromoInfoItems);
        }
    }

    private CouponPromoInfo buildConciseIdleHoursPromo(CouponActivityContext context, IMobileContext iMobileContext) {
        PromoDisplayDTO idlePromo = getValidIdlePromo(context, iMobileContext);
        if (idlePromo == null) {
            return null;
        }
        BigDecimal price = context.getDealGroupBaseDTO().getDealGroupPrice().subtract(idlePromo.getPromoAmount())
                .setScale(2, BigDecimal.ROUND_CEILING).stripTrailingZeros();
        if (price.compareTo(BigDecimal.ZERO) < 0) {
            return null;
        }
        String priceText = "￥" + price.toPlainString() + "/次";
        String promoText = idlePromo.getDescription() + "  " + priceText;
        String promoJsonText = JsonLabelUtil.idleHoursPromoJson(context.isMt(), promoText, priceText);
        CouponPromoInfo result = new CouponPromoInfo();
        result.setType(PromoTypeEnum.IDLE_HOURS.getCode());
        result.setPromoTitle("限时优惠");
        result.setIconUrl(PlusIcons.IDLE_HOURS_PROMO);
        result.setPromoInfoDescItems(Lists.newArrayList(new PromoSimpleInfoItem().setText(promoJsonText)));
        result.setLeadText("去购买");
        result.setLeadAction(LeadActionEnum.TOAST_IDLE_HOURS.getCode());
        return result;
    }

    private CouponPromoInfo buildBeautyNewStyleCombinePromo(CouponActivityContext context, IMobileContext iMobileContext) {
        // 包含三部分信息：券、立减、返礼
        if (!Boolean.TRUE.equals(context.getBeautyDealNewType())) {
            // 不走丽人团单新样式的就直接返回
            return null;
        }

        //价格力优化
        List<String> reduceTags = null;
        if (context.isBeMemberPriceProxy()) {
            PromotionDTOResult bestProxyPromotionResult = context.getBestProxyPromotionResult();
            String reduceTag = MemberPriceCalculateMapper.getTagContent(bestProxyPromotionResult);
            if (StringUtils.isNotBlank(reduceTag)) {
                reduceTags = Lists.newArrayList(reduceTag);
            }
        } else {
            // 拿立减信息
            reduceTags = buildReduceTag(context);
        }
        // 拿券信息
        List<String> couponTags = buildCouponTag(context);
        List<String> govConsumeCouponTags = buildFinancialGovConsumeCouponTag(context);
        // 拿返礼信息 （消费 -> 下单 -> 评价）
        List<String> returnCouponTags = buildReturnCouponTag(context);

        List<String> resultTags = Lists.newArrayList();
        if (CollectionUtils.isNotEmpty(reduceTags)) {
            resultTags.addAll(reduceTags);
        }
        if (CollectionUtils.isNotEmpty(couponTags)) {
            resultTags.addAll(couponTags);
        }
        if (CollectionUtils.isNotEmpty(govConsumeCouponTags)) {
            resultTags.addAll(govConsumeCouponTags);
        }
        if (CollectionUtils.isNotEmpty(returnCouponTags)) {
            resultTags.addAll(returnCouponTags);
        }

        String fullDiscountDeductionTag;
        if (context.isBeMemberPriceProxy()) {
            fullDiscountDeductionTag = ProxyPromoInfoHelper.getFullDiscountDeductionTag(context.getBestProxyPromotionResult());
        } else {
            fullDiscountDeductionTag = ProxyPromoInfoHelper.getFullDiscountDeductionTag(context.getPromotionDTOResult());
        }
        if (StringUtils.isNotBlank(fullDiscountDeductionTag)) {
            reduceTags = Lists.newArrayList(fullDiscountDeductionTag);
        }

        if (CollectionUtils.isEmpty(resultTags)) {
            return null;
        }


        CouponPromoInfo result = new CouponPromoInfo();
        result.setType(PromoTypeEnum.BEAUTY_COMBINE_PROMO.getCode());
        result.setStyle(0);
        List<PromoSimpleInfoItem> detailPromoInfoDescItems = Lists.newArrayListWithExpectedSize(couponTags.size());
        resultTags.forEach(desc -> {
            PromoSimpleInfoItem item = new PromoSimpleInfoItem();
            item.setStyle(1);
            item.setText(desc);
            detailPromoInfoDescItems.add(item);
        });

        result.setPromoInfoDescItems(detailPromoInfoDescItems);

        if (CollectionUtils.isEmpty(couponTags) && CollectionUtils.isEmpty(govConsumeCouponTags)) {
            if (CollectionUtils.isEmpty(reduceTags)) {
                result.setLeadAction(LeadActionEnum.REDIRECT_URL.getCode());
                result.setLeadUrl(UrlHelper.getBonusUrl(context));
            } else {
                result.setLeadAction(LeadActionEnum.TOAST_DEFAULT.getCode());
                result.setLeadText("查看优惠");
            }
        } else {
            result.setLeadAction(LeadActionEnum.TOAST_DEFAULT.getCode());
            result.setLeadText("领券");
        }
        // leadAction选择
        return result;
    }

    private List<String> buildReduceTag(CouponActivityContext context) {
        Cat.logEvent("INVALID_METHOD_3", "com.dianping.pay.api.biz.activity.newloader.mapper.DealPromoInfoMapper.buildReduceTag(com.dianping.pay.api.biz.activity.newloader.dto.CouponActivityContext)");
        List<PromoDisplayDTO> promoList = context.getPromoDisplayDTOS();
        if (CollectionUtils.isEmpty(promoList)) {
            return Lists.newArrayList();
        }
        List<PromoDisplayDTO> normalPromos = ReductionPromoHelper.getNormalPromo(promoList);
        if (CollectionUtils.isEmpty(normalPromos)) {
            return Lists.newArrayList();
        }
        String promoText = normalPromos.get(0).getTag();
        return Lists.newArrayList(promoText);
    }

    private List<String> buildCouponTag(CouponActivityContext context) {
        Cat.logEvent("INVALID_METHOD_2", "com.dianping.pay.api.biz.activity.newloader.mapper.DealPromoInfoMapper.buildCouponTag(com.dianping.pay.api.biz.activity.newloader.dto.CouponActivityContext)");
        if (context.isDealPromoProxy()) {
            // 团单新样式
            List<GetPromotionDTO> promotionDTOS = ProxyPromoInfoHelper.getPromotions(context);
            if (CollectionUtils.isEmpty(promotionDTOS)) {
                return Lists.newArrayList();
            }
            promotionDTOS.sort(new ProxyPromoComparator());
            return promotionDTOS.stream().map(e -> ProxyPromoInfoHelper.buildConciseCouponText(e.getPromotionDTO().getCouponDTO())).collect(Collectors.toList());
        } else {
            // 老样式
            List<IssueCouponActivity> activityDTOS = context.getIssueCouponActivities();
            if (CollectionUtils.isEmpty(activityDTOS)) {
                return Lists.newArrayList();
            }
            activityDTOS.sort(new IssueActivityComparator());
            return activityDTOS.stream().map(DealPromoInfoMapper::buildConciseCouponText).collect(Collectors.toList());
        }
    }

    public static List<String> buildFinancialGovConsumeCouponTag(CouponActivityContext context) {
        Cat.logEvent("INVALID_METHOD_1", "com.dianping.pay.api.biz.activity.newloader.mapper.DealPromoInfoMapper.buildFinancialGovConsumeCouponTag(com.dianping.pay.api.biz.activity.newloader.dto.CouponActivityContext)");
        List<GetPromotionDTO> promotionDTOS = ProxyPromoInfoHelper.getGovConsumePromotions(context);
        if (CollectionUtils.isEmpty(promotionDTOS)) {
            return Lists.newArrayList();
        }
        return promotionDTOS.stream().map(e -> ProxyPromoInfoHelper.buildFinancialConciseConsumeCouponText(e.getPromotionDTO().getFinancialCouponDTO())).filter(Objects::nonNull).collect(Collectors.toList());
    }

    private List<String> buildReturnCouponTag(CouponActivityContext context) {
        Cat.logEvent("INVALID_METHOD_4", "com.dianping.pay.api.biz.activity.newloader.mapper.DealPromoInfoMapper.buildReturnCouponTag(com.dianping.pay.api.biz.activity.newloader.dto.CouponActivityContext)");
        if (!context.isNeedReturnPromotion() ||
                context.getReturnPromotionDisplayDto() == null ||
                CollectionUtils.isEmpty(context.getReturnPromotionDisplayDto().getReturnPromotionDetails())) {
            return null;
        }

        List<ReturnPromotionDetail> returnPromotionDetails = context.getReturnPromotionDisplayDto().getReturnPromotionDetails();
        return returnPromotionDetails.stream().map(ReturnPromotionDetail::getSummaryInfo).collect(Collectors.toList());
    }


    private CouponPromoInfo buildConciseResourcePromo(CouponActivityContext context, IMobileContext iMobileContext) {

        PromoExposureInfoItem couponDescItem = loadResources(context);
        if (couponDescItem == null) {
            return null;
        }
        CouponPromoInfo result = new CouponPromoInfo();
        result.setType(PromoTypeEnum.RESOURCES_PROMOTION.getCode());
        result.setPromoTitle("平台投放");
        result.setStyle(2);
        result.setPromoExposureInfoItem(couponDescItem);
        return result;
    }


    private static PromoDisplayDTO getValidIdlePromo(CouponActivityContext context, IMobileContext iMobileContext) {
        if (!context.isNeedReductionPromotion()) {
            return null;
        }
        if (Version.idlePromoLimited(iMobileContext.getVersion(), context.isMt())) {
            return null;
        }
        List<PromoDisplayDTO> promoList = context.getPromoDisplayDTOS();
        if (CollectionUtils.isEmpty(promoList)) {
            return null;
        }
        List<PromoDisplayDTO> idlePromos = ReductionPromoHelper.getIdlePromo(promoList);
        if (CollectionUtils.isEmpty(idlePromos)) {
            return null;
        }
        PromoDisplayDTO idlePromo = idlePromos.get(0);
        if (idlePromo == null) {
            return null;
        }
        if (idlePromo.getCanConsumeTime().getType() != PromoTimeFormatType.DAY_OF_WEEK.getCode()) {
            return null;
        }
        if (CollectionUtils.isEmpty(idlePromo.getCanConsumeTime().getTimes())) {
            return null;
        }
        if (context.getDealGroupBaseDTO() == null ||
                context.getDealGroupBaseDTO().getDealGroupPrice() == null ||
                idlePromo.getPromoAmount() == null) {
            return null;
        }
        // 防止价格倒挂
        BigDecimal idlePrice = context.getDealGroupBaseDTO().getDealGroupPrice().subtract(idlePromo.getPromoAmount());
        BigDecimal originPrice = context.getDealGroupBaseDTO().getDealGroupPrice();
        PromoDisplayDTO normalPromo = ReductionPromoHelper.getValidPromo(promoList);
        if (normalPromo != null) {
            originPrice = originPrice.subtract(normalPromo.getPromoAmount());
        }
        if (idlePrice.compareTo(originPrice) >= 0) {
            return null;
        }
        if (idlePrice.compareTo(BigDecimal.ZERO) < 0) {
            return null;
        }
        return idlePromo;
    }

    private List<CouponPromoInfo> buildConcisePromoInfoForJhs(CouponActivityContext context, IMobileContext iMobileContext) {
        if (context.getBeautyDealNewType() != null && Boolean.TRUE.equals(context.getBeautyDealNewType())) {
            // 走丽人团单新样式的就直接返回
            return null;
        }
        PromotionDTOResult promoResult = context.getPromotionDTOResult();

        String fullDiscountDeductionTag = ProxyPromoInfoHelper.getFullDiscountDeductionTag(promoResult);

        List<GetPromotionDTO> bestPromotionList = ProxyPromoInfoHelper.getBestDeductionPromotionList(promoResult);
        // 最优优惠组合中没有立减就直接返回了
        if (CollectionUtils.isEmpty(bestPromotionList) && StringUtils.isBlank(fullDiscountDeductionTag)) {
            return null;
        }

        List<CouponPromoInfo> results = Lists.newArrayList();
        CouponPromoInfo result = new CouponPromoInfo();
        results.add(result);

        result.setType(PromoTypeEnum.REDUCTION.getCode());
        result.setPromoTitle("立减");
        result.setIconUrl(context.isMt() ? PlusIcons.MT_PROMO : PlusIcons.DP_PROMO);
        List<PromoSimpleInfoItem> deductionConcisePromoInfoDescItems = Lists.newArrayList();
        List<PromoSimpleInfoItem> fullDiscountConcisePromoInfoDescItems = Lists.newArrayList();
        boolean newReductionStyle = ReductionUtils.isNewPromoStyle(context.getQueryShopCouponType(), context.getDpDealGroupId(), context.isMt(), context.getShopDTO());

        if (CollectionUtils.isNotEmpty(bestPromotionList)) {
            String deductionTag = ProxyPromoInfoHelper.getDeductionTag(promoResult);
            if (StringUtils.isBlank(deductionTag)) {
                // 文案渲染有误，打点告警出来
                Cat.logEvent(CatEventConstants.PROXY_RESULT_ILLEGAL, "-1");
                log.warn("[proxyData] empty. result: {}", promoResult);
            }
            deductionConcisePromoInfoDescItems.add(new PromoSimpleInfoItem().setText(deductionTag));
        }

        if (StringUtils.isNotBlank(fullDiscountDeductionTag)) {
            fullDiscountConcisePromoInfoDescItems.add(new PromoSimpleInfoItem().setText(fullDiscountDeductionTag));
        }

        //都不为空先塞立减，再另取塞满件折
        if (CollectionUtils.isNotEmpty(deductionConcisePromoInfoDescItems) && CollectionUtils.isNotEmpty(fullDiscountConcisePromoInfoDescItems)) {
            result.setPromoInfoDescItems(deductionConcisePromoInfoDescItems);

            CouponPromoInfo fullDiscountResult = new CouponPromoInfo();
            fullDiscountResult.setType(PromoTypeEnum.REDUCTION.getCode());
            fullDiscountResult.setPromoTitle("立减");
            fullDiscountResult.setPromoInfoDescItems(fullDiscountConcisePromoInfoDescItems);
            results.add(fullDiscountResult);
        } else {
            result.setPromoInfoDescItems(CollectionUtils.isNotEmpty(deductionConcisePromoInfoDescItems) ? deductionConcisePromoInfoDescItems : fullDiscountConcisePromoInfoDescItems);
        }

        if (newReductionStyle) {
            result.setLeadText("查看优惠");
            result.setLeadAction(LeadActionEnum.TOAST_DEFAULT.getCode());
        }
        return results;

    }

    private List<CouponPromoInfo> buildConcisePromoInfo(CouponActivityContext context, IMobileContext iMobileContext) {
        if (context.getBeautyDealNewType() != null && Boolean.TRUE.equals(context.getBeautyDealNewType())) {
            // 走丽人团单新样式的就直接返回
            return null;
        }

        //价格力优化
        if (context.isBeMemberPriceProxy()) {
            return buildMemberPricePromoInfo(context, iMobileContext);
        }

        List<CouponPromoInfo> results = Lists.newArrayList();

        List<PromoDisplayDTO> promoList = context.getPromoDisplayDTOS();
        List<PromoDisplayDTO> normalPromos = ReductionPromoHelper.getNormalPromo(promoList);
        String fullDiscountDeductionTag = ProxyPromoInfoHelper.getFullDiscountDeductionTag(context.getPromotionDTOResult());
        if (CollectionUtils.isEmpty(normalPromos) && StringUtils.isBlank(fullDiscountDeductionTag)) {
            return null;
        }
        boolean newReductionStyle = ReductionUtils.isNewPromoStyle(context.getQueryShopCouponType(), context.getDpDealGroupId(), context.isMt(), context.getShopDTO());

        CouponPromoInfo result = new CouponPromoInfo();
        results.add(result);
        result.setType(PromoTypeEnum.REDUCTION.getCode());
        result.setPromoTitle("立减");
        result.setIconUrl(context.isMt() ? PlusIcons.MT_PROMO : PlusIcons.DP_PROMO);
        List<PromoSimpleInfoItem> deductionConcisePromoInfoDescItems = Lists.newArrayList();
        List<PromoSimpleInfoItem> fullDiscountConcisePromoInfoDescItems = Lists.newArrayList();
        String hiLight = newReductionStyle ? "(?<=优惠)[0-9.]*" : "减" + PlusNumUtils.trimDecimal(normalPromos.get(0).getPromoAmount().toString()) + "元";
        if (CollectionUtils.isNotEmpty(normalPromos)) {
            String promoText = normalPromos.get(0).getDescription();
            String promoJsonText = context.isMt() ? JsonLabelUtil.promoMTJson(promoText, hiLight) : JsonLabelUtil.promoDPJson(promoText, hiLight);
            deductionConcisePromoInfoDescItems.add(new PromoSimpleInfoItem().setText(promoJsonText));
        }
        if (StringUtils.isNotBlank(fullDiscountDeductionTag)) {
            String fullDiscountDeductionJsonText = context.isMt() ? JsonLabelUtil.promoMTJson(fullDiscountDeductionTag, hiLight) : JsonLabelUtil.promoDPJson(fullDiscountDeductionTag, hiLight);
            fullDiscountConcisePromoInfoDescItems.add(new PromoSimpleInfoItem().setText(fullDiscountDeductionJsonText));
        }

        //都不为空先塞立减，再另取塞满件折
        if (CollectionUtils.isNotEmpty(deductionConcisePromoInfoDescItems) && CollectionUtils.isNotEmpty(fullDiscountConcisePromoInfoDescItems)) {
            result.setPromoInfoDescItems(deductionConcisePromoInfoDescItems);

            CouponPromoInfo fullDiscountResult = new CouponPromoInfo();
            fullDiscountResult.setType(PromoTypeEnum.REDUCTION.getCode());
            fullDiscountResult.setPromoTitle("立减");
            fullDiscountResult.setPromoInfoDescItems(fullDiscountConcisePromoInfoDescItems);
            results.add(fullDiscountResult);
        } else {
            result.setPromoInfoDescItems(CollectionUtils.isNotEmpty(deductionConcisePromoInfoDescItems) ? deductionConcisePromoInfoDescItems : fullDiscountConcisePromoInfoDescItems);
        }

        if (newReductionStyle) {
            result.setLeadText("查看优惠");
            result.setLeadAction(LeadActionEnum.TOAST_DEFAULT.getCode());
        }
        return results;
    }

    //价格力优化
    private List<CouponPromoInfo> buildMemberPricePromoInfo(CouponActivityContext context, IMobileContext iMobileContext) {
        Cat.logEvent("INVALID_METHOD_3", "com.dianping.pay.api.biz.activity.newloader.mapper.DealPromoInfoMapper.buildMemberPricePromoInfo(CouponActivityContext,IMobileContext)");
        PromotionDTOResult bestProxyPromotionResult = context.getBestProxyPromotionResult();
        if (bestProxyPromotionResult == null) {
            return null;
        }
        String promoText = MemberPriceCalculateMapper.getTagContent(bestProxyPromotionResult);
        String fullDiscountDeductionTag = ProxyPromoInfoHelper.getFullDiscountDeductionTag(bestProxyPromotionResult);

        if (StringUtils.isBlank(promoText) && StringUtils.isBlank(fullDiscountDeductionTag)) {
            return null;
        }
        List<CouponPromoInfo> results = Lists.newArrayList();

        CouponPromoInfo result = new CouponPromoInfo();
        results.add(result);
        result.setType(PromoTypeEnum.REDUCTION.getCode());
        result.setPromoTitle("立减");
        result.setIconUrl(context.isMt() ? PlusIcons.MT_PROMO : PlusIcons.DP_PROMO);
        List<PromoSimpleInfoItem> deductionConcisePromoInfoDescItems = Lists.newArrayList();
        List<PromoSimpleInfoItem> fullDiscountConcisePromoInfoDescItems = Lists.newArrayList();
        String hiLight = "(?<=优惠)[0-9.]*";
        if (StringUtils.isNotBlank(promoText)) {
            String promoJsonText = context.isMt() ? JsonLabelUtil.promoMTJson(promoText, hiLight) : JsonLabelUtil.promoDPJson(promoText, hiLight);
            deductionConcisePromoInfoDescItems.add(new PromoSimpleInfoItem().setText(promoJsonText));
        }
        if (StringUtils.isNotBlank(fullDiscountDeductionTag)) {
            String fullDiscountDeductionJsonText = context.isMt() ? JsonLabelUtil.promoMTJson(fullDiscountDeductionTag, hiLight) : JsonLabelUtil.promoDPJson(fullDiscountDeductionTag, hiLight);
            fullDiscountConcisePromoInfoDescItems.add(new PromoSimpleInfoItem().setText(fullDiscountDeductionJsonText));
        }

        //都不为空先塞立减，再另取塞满件折
        if (CollectionUtils.isNotEmpty(deductionConcisePromoInfoDescItems) && CollectionUtils.isNotEmpty(fullDiscountConcisePromoInfoDescItems)) {
            result.setPromoInfoDescItems(deductionConcisePromoInfoDescItems);

            CouponPromoInfo fullDiscountResult = new CouponPromoInfo();
            fullDiscountResult.setType(PromoTypeEnum.REDUCTION.getCode());
            fullDiscountResult.setPromoTitle("立减");
            fullDiscountResult.setPromoInfoDescItems(fullDiscountConcisePromoInfoDescItems);
            results.add(fullDiscountResult);
        } else {
            result.setPromoInfoDescItems(CollectionUtils.isNotEmpty(deductionConcisePromoInfoDescItems) ? deductionConcisePromoInfoDescItems : fullDiscountConcisePromoInfoDescItems);
        }

        result.setLeadText("查看优惠");
        result.setLeadAction(LeadActionEnum.TOAST_DEFAULT.getCode());

        return results;
    }

    /**
     * 拼接外层丢欧诺个券
     * @param context
     * @param iMobileContext
     * @[param skipShopActivity 是否跳过商家券渲染
     * @return
     */
    private CouponPromoInfo buildConciseCouponInfo(CouponActivityContext context, IMobileContext iMobileContext, boolean needBuildShopActivity) {
        if (context.getBeautyDealNewType() != null && Boolean.TRUE.equals(context.getBeautyDealNewType())) {
            // 走丽人团单新样式的就直接返回
            return null;
        }

        // 团单新样式，展示用户可用券&可领券
        if (context.isDealPromoProxy()) {
            return ProxyPromoInfoHelper.genCouponInfoItem(context);
        }

        if (!needBuildShopActivity) {
            return null;
        }

        // 商家券老样式
        List<IssueCouponActivity> activityDTOS = context.getIssueCouponActivities();
        if (CollectionUtils.isEmpty(activityDTOS)) {
            return null;
        }
        CouponPromoInfo promoInfoItem = new CouponPromoInfo();
        promoInfoItem.setType(PromoTypeEnum.COUPON.getCode());
        promoInfoItem.setPromoTitle("抵用券");
        promoInfoItem.setIconUrl(context.isMt() ? PlusIcons.MT_COUPON : PlusIcons.DP_COUPON);
        List<PromoSimpleInfoItem> concisePromoInfoDescItems = Lists.newArrayList();
        for (IssueCouponActivity activityDTO : activityDTOS) {
            if (context.getNeedDiscountCoupon() == null || !context.getNeedDiscountCoupon()) {
                if (activityDTO.getCouponGroup() != null && activityDTO.getCouponGroup().getValueType() == CouponGroupValueType.DISCOUNT.getCode()) {
                    continue;
                }
            }
            PromoSimpleInfoItem promoInfoDescItem = new PromoSimpleInfoItem();
            if (buildConciseCouponText(activityDTO) == null) {
                continue;
            }
            promoInfoDescItem.setStyle(1);
            promoInfoDescItem.setText(buildConciseCouponText(activityDTO));
            concisePromoInfoDescItems.add(promoInfoDescItem);
        }
        if (CollectionUtils.isEmpty(concisePromoInfoDescItems)) {
            return null;
        }
        if (concisePromoInfoDescItems.size() > 2) {
            promoInfoItem.setPromoInfoDescItems(concisePromoInfoDescItems.subList(0, 2));
        } else {
            promoInfoItem.setPromoInfoDescItems(concisePromoInfoDescItems);
        }
        promoInfoItem.setLeadText("去领券");
        promoInfoItem.setLeadAction(LeadActionEnum.TOAST_DEFAULT.getCode());
        return promoInfoItem;
    }

    private CouponPromoInfo buildConciseBonusInfo(CouponActivityContext context, IMobileContext iMobileContext) {
        if (context.getBeautyDealNewType() != null && Boolean.TRUE.equals(context.getBeautyDealNewType())) {
            // 走丽人团单新样式的就直接返回
            return null;
        }

        if (!context.isNeedReturnPromotion() || context.getReturnPromotionDisplayDto() == null || CollectionUtils.isEmpty(context.getReturnPromotionDisplayDto().getReturnPromotionDetails())) {
            return null;
        }
        List<ReturnPromotionDetail> returnPromotionDetails = context.getReturnPromotionDisplayDto().getReturnPromotionDetails();
        CouponPromoInfo result = new CouponPromoInfo();
        result.setType(PromoTypeEnum.BONUS.getCode());
        result.setPromoTitle("返礼");
        result.setIconUrl(context.isMt() ? PlusIcons.MT_BONUS : PlusIcons.DP_BONUS);
        List<PromoSimpleInfoItem> concisePromoInfoDescItems = Lists.newArrayList();
        result.setPromoInfoDescItems(concisePromoInfoDescItems);
        String bonusTitle = returnPromotionDetails.get(0).getSummaryInfo();
        if (bonusTitle.lastIndexOf("返") > 0) {
            String highLightTitle = bonusTitle.substring(bonusTitle.lastIndexOf("返"), bonusTitle.length());
            String bonusJsonText = context.isMt() ?
                    JsonLabelUtil.bonusMTJson(bonusTitle, highLightTitle) :
                    JsonLabelUtil.bonusDPJson(bonusTitle, highLightTitle);
            concisePromoInfoDescItems.add(new PromoSimpleInfoItem().setText(bonusJsonText));
        } else {
            concisePromoInfoDescItems.add(new PromoSimpleInfoItem().setText(bonusTitle));
        }
        if (Lion.getBooleanValue(LionConstants.BONUS_REDIRECT_ENABLE, false)) {
            result.setRedirectUrl(UrlHelper.getBonusUrl(context));
        }
        result.setLeadAction(LeadActionEnum.REDIRECT_URL.getCode());
        result.setLeadUrl(result.getRedirectUrl());
        return result;
    }


    private static String buildConciseCouponText(IssueCouponActivity activityDTO) {
        if (activityDTO == null || activityDTO.getCouponGroup() == null) {
            return null;
        }


        int couponValueType = activityDTO.getCouponGroup().getValueType();

        if (couponValueType == CouponGroupValueType.DISCOUNT.getCode()) {
            // X折无门槛优惠券/满Y元X折
            DiscountCouponDTO discountCouponDTO = activityDTO.getCouponGroup().getDiscountCouponDTO();
            String discountText = (double) discountCouponDTO.getDiscount() / 10 + "";
            if (discountCouponDTO.getPriceLimit() != null && BigDecimal.ZERO.compareTo(discountCouponDTO.getPriceLimit()) < 0) {
                return "满" + discountCouponDTO.getPriceLimit().stripTrailingZeros().toPlainString() + "元" + discountText + "折";
            } else {
                return discountText + "折无门槛优惠券";
            }
        } else {
            if (BigDecimal.ZERO.compareTo(activityDTO.getCouponGroup().getPriceLimit()) < 0) {
                return "满" + PlusNumUtils.trimDecimal(activityDTO.getCouponGroup().getPriceLimit().toString())
                        + "减" + PlusNumUtils.trimDecimal(activityDTO.getCouponGroup().getDiscountAmount().toString()) + "券";
            } else {
                if (activityDTO.getCouponGroup().getDiscountAmount().compareTo(BigDecimal.ZERO) > 0) {
                    return PlusNumUtils.trimDecimal(activityDTO.getCouponGroup().getDiscountAmount().toString()) + "元无门槛券";
                }
            }
        }
        return null;
    }

    private static String buildDetailCouponDescText(IssueCouponActivity activity) {
        if (activity == null) {
            return null;
        }
        if (CollectionUtils.isEmpty(activity.getUseRules())) {
            return null;
        }
        return activity.getUseRules().get(0);
    }


}
