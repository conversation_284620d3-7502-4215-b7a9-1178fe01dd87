package com.dianping.pay.api.biz.activity.newloader.promo;

import com.dianping.api.util.FunctionPool;
import com.dianping.cat.Cat;
import com.dianping.gmkt.coupon.common.api.enums.CouponBusiness;
import com.dianping.mobile.framework.datatypes.IMobileContext;
import com.dianping.pay.api.beans.IssueCouponActivity;
import com.dianping.pay.api.beans.MerchantNewUserValidateResult;
import com.dianping.pay.api.biz.activity.newloader.dto.CouponActivityContext;
import com.dianping.pay.api.biz.discount.CouponBiz;
import com.dianping.pay.api.enums.IssueCouponOptionType;
import com.dianping.pay.api.enums.QueryShopCouponTypeEnum;
import com.dianping.pay.api.util.CouponBusinessUtils;
import com.dianping.pay.api.util.DotUtils;
import com.dianping.pay.api.util.IssueCouponUtils;
import com.dianping.pay.api.wrapper.CouponNewUserWrapper;
import com.dianping.pay.promo.common.enums.User;
import com.dianping.pay.promo.common.enums.coupon.UnifiedCouponProductType;
import com.dianping.spc.common.Response;
import com.dianping.tgc.open.entity.ActivitySimpleDTO;
import com.dianping.tgc.open.entity.BatchCouponBizIdQueryRequest;
import com.dianping.tgc.open.entity.BizIdType;
import com.dianping.tgc.open.entity.QuerySimpleResponseDTO;
import com.dianping.tgc.open.v2.TGCGetCouponComponentQueryService;
import com.dianping.unified.coupon.manage.api.dto.UnifiedCouponDTO;
import com.dianping.unified.coupon.manage.api.dto.UnifiedCouponGroupDTO;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.sankuai.mpmkt.coupon.execute.api.UnifiedCouponExecuteValidateService;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.jsoup.helper.Validate;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;
import java.util.concurrent.Callable;
import java.util.concurrent.Future;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2021/1/20
 */
@Component("tgcBatchCouponProcessor")
public class TgcBatchCouponProcessor extends AbstractPromoProcessor {

    public static final Logger log = LoggerFactory.getLogger(TgcBatchCouponProcessor.class);

    @Autowired
    private TGCGetCouponComponentQueryService tgcGetCouponComponentQueryService;

    @Autowired
    private CouponBiz couponBiz;

    @Autowired
    private UnifiedCouponExecuteValidateService unifiedCouponExecuteValidateService;

    @Autowired
    private CouponNewUserWrapper couponNewUserWrapper;

    public static final String CAT_TYPE = "TgcBatchCouponProcessor";

    @Override
    public void prePare(final CouponActivityContext promoCtx, IMobileContext iMobileContext) {
        try {
            Future<Pair<Map<Integer, Map<String, List<Integer>>>, List<IssueCouponActivity>>> future = ExecutorService.submit(new Callable<Pair<Map<Integer, Map<String, List<Integer>>>, List<IssueCouponActivity>>>() {
                @Override
                public Pair<Map<Integer, Map<String, List<Integer>>>, List<IssueCouponActivity>> call() throws Exception {
                    return queryIssueCouponActivity(promoCtx);
                }
            });
            promoCtx.setTgcBatchCouponPromotionFuture(future);
        } catch (Exception e) {
            Cat.logEvent(CAT_TYPE, "TgcBatchPromoPrePareException", "-1", e.getMessage());
            log.error("TgcBatchCouponProcessor# prepare error. ctx: {}", promoCtx, e);
        }
    }


    @Override
    public void loadPromo(CouponActivityContext promoCtx) {
        try {
            if (promoCtx.getBatchIssueCouponActivitiesPair() != null) {
                return;
            }
            Future<Pair<Map<Integer, Map<String, List<Integer>>>, List<IssueCouponActivity>>> tgcBatchCouponPromotionFuture = promoCtx.getTgcBatchCouponPromotionFuture();
            if (tgcBatchCouponPromotionFuture != null) {
                promoCtx.setBatchIssueCouponActivitiesPair(tgcBatchCouponPromotionFuture.get(1500, TimeUnit.MILLISECONDS));
            }
        } catch (Exception e) {
            Cat.logEvent(CAT_TYPE, "loadTgcBatchPromoException", "-1", e.getMessage());
            log.error("TgcBatchCouponProcessor# loadPromo error. ctx: {}", promoCtx, e);
        }
    }

    @Override
    public String getPromoName() {
        Cat.logEvent("INVALID_METHOD_4", "com.dianping.pay.api.biz.activity.newloader.promo.TgcBatchCouponProcessor.getPromoName()");
        return "tgcBatchCoupon";
    }

    private QuerySimpleResponseDTO batchQuerySimpleActivities(CouponActivityContext promoCtx) {
        BatchCouponBizIdQueryRequest batchCouponBizIdQueryRequest = new BatchCouponBizIdQueryRequest();
        Map<Integer, List<Number>> bizIdMap = Maps.newHashMap();
        if (CollectionUtils.isNotEmpty(promoCtx.getDpDealGroupIds())) {
            bizIdMap.put(BizIdType.DEAL_GROUP_ID.getCode(), Lists.transform(promoCtx.getDpDealGroupIds(), FunctionPool.INTEGER_TO_NUMBER));
        }
        //美团团单转成dp团单查
        if (CollectionUtils.isNotEmpty(promoCtx.getMtDealGroupIds()) && MapUtils.isNotEmpty(promoCtx.getMtDpDealGroupMap())) {
            bizIdMap.put(BizIdType.DEAL_GROUP_ID.getCode(), Lists.transform(Lists.newArrayList(promoCtx.getMtDpDealGroupMap().values()), FunctionPool.INTEGER_TO_NUMBER));
        }
        if (CollectionUtils.isNotEmpty(promoCtx.getSkuIds())) {
            bizIdMap.put(BizIdType.SKU_ID.getCode(), Lists.transform(promoCtx.getSkuIds(), FunctionPool.INTEGER_TO_NUMBER));
        }
        if (MapUtils.isEmpty(bizIdMap)) {
            log.warn("TgcBatchCouponProcessor# empty bizIdMap . promoCtx: {}", promoCtx);
            return null;
        }
        batchCouponBizIdQueryRequest.setBizIdMap(bizIdMap);
        batchCouponBizIdQueryRequest.setMt(promoCtx.isMt());
        Response<QuerySimpleResponseDTO> simpleResponseDTOResponse = tgcGetCouponComponentQueryService.batchQueryAllKindCouponListByBizId(batchCouponBizIdQueryRequest);
        if (simpleResponseDTOResponse == null || !simpleResponseDTOResponse.isSuccess() || simpleResponseDTOResponse.getResult() == null) {
            log.warn("TgcBatchCouponProcessor# batchQuerySimpleActivities error. promoCtx: {}, simpleResponseDTOResponse: {}", promoCtx, simpleResponseDTOResponse);
            return null;
        }
        return simpleResponseDTOResponse.getResult();
    }

    private Pair<Map<Integer, Map<String, List<Integer>>>, List<IssueCouponActivity>> queryIssueCouponActivity(CouponActivityContext promoCtx) {
        List<IssueCouponActivity> issueCouponActivities = Lists.newArrayList();
        Map<Integer, Map<String, List<Integer>>> bizTypeBizActivityIdsMap = Maps.newHashMap();
        Map<Integer, Boolean> brandNewUserValidateResult = Maps.newHashMap();
        try {
            if (promoCtx.getQueryShopCouponType() != QueryShopCouponTypeEnum.BY_SKUS.getCode() && promoCtx.getQueryShopCouponType() != QueryShopCouponTypeEnum.BY_DEALS.getCode()) {
                return Pair.of(bizTypeBizActivityIdsMap, issueCouponActivities);
            }
            DotUtils.dotForPromoProcessor(promoCtx, getPromoName());
            QuerySimpleResponseDTO querySimpleResponseDTO = batchQuerySimpleActivities(promoCtx);
            if (querySimpleResponseDTO != null && MapUtils.isNotEmpty(querySimpleResponseDTO.getBizActivitiesMap()) && CollectionUtils.isNotEmpty(querySimpleResponseDTO.getActivityDTOs())) {
                for (ActivitySimpleDTO activitySimpleDTO : querySimpleResponseDTO.getActivityDTOs()) {
                    try {
                        //测试环境存在String的券批次 忽略
                        IssueCouponActivity issueCouponActivity = new IssueCouponActivity(activitySimpleDTO, IssueCouponOptionType.SHOP_CART_EVENT.getCode());
                        issueCouponActivities.add(issueCouponActivity);
                    } catch (Exception e) {
                        log.error("IssueCouponActivity instance exception!", e);
                        Cat.logEvent("TgcBatchCouponProcessor", "buildActivityEx");
                    }
                }
                //过新客校验
                Map<Integer, Map<String, List<Integer>>> bizActivitiesMap = querySimpleResponseDTO.getBizActivitiesMap();

                //理论只有一个，大于1打点
                if (MapUtils.isNotEmpty(bizActivitiesMap) && bizActivitiesMap.size() > 1) {
                    Cat.logEvent("TgcBatchCouponProcessor", "bizTypeSizeBiggerThanOne");
                }
                Map<String, List<Integer>> productActivityIdMap = Maps.newHashMap();
                Integer bizType = -1;
                if (MapUtils.isNotEmpty(bizActivitiesMap)) {
                    //取一个
                    for (Map.Entry<Integer, Map<String, List<Integer>>> entry : bizActivitiesMap.entrySet()) {
                        bizType = entry.getKey();
                        productActivityIdMap = entry.getValue();
                    }
                    Pair<Map<String, List<Integer>>, Map<Integer, Boolean>> pair = filterNewUserActivity(bizType, productActivityIdMap, promoCtx, issueCouponActivities);
                    Map<String, List<Integer>> bizActivityIdsMap = pair.getLeft();
                    if (bizType > 0 && MapUtils.isNotEmpty(bizActivityIdsMap)) {
                        bizTypeBizActivityIdsMap.put(bizType, bizActivityIdsMap);
                    }
                    brandNewUserValidateResult = pair.getRight();
                }
            }

            //填充券批次信息和用户领券信息
            fillRelatedInfo(issueCouponActivities, promoCtx, brandNewUserValidateResult);
        } catch (Exception e) {
            Cat.logEvent(CAT_TYPE, "TgcBatchQueryIssueCouponException", "-1", e.getMessage());
            log.error("queryAllKindCouponListByShopId exception!", e);
            return null;
        }
        return Pair.of(bizTypeBizActivityIdsMap, issueCouponActivities);
    }

    private Pair<Map<String, List<Integer>>, Map<Integer, Boolean>> filterNewUserActivity(Integer bizType, Map<String, List<Integer>> productActivityMap, CouponActivityContext promoCtx, List<IssueCouponActivity> activities) {

        //品牌新客校验结果
        Map<Integer, Boolean> brandNewUserValidateResult = Maps.newHashMap();
        //商品限制活动映射
        Map<String, List<Integer>> productUnPassActivityMap = Maps.newHashMap();

        //活动批次映射
        Map<Integer, Integer> activityCouponMap = Maps.newHashMap();

        List<Integer> couponGroupIds = Lists.newArrayList();
        for (IssueCouponActivity activity : activities) {
            if (activity == null || activity.getCouponGroupId() <= 0) {
                continue;
            }
            activityCouponMap.put(activity.getActivityId(), activity.getCouponGroupId());
            couponGroupIds.add(activity.getCouponGroupId());
        }
        if (CollectionUtils.isEmpty(couponGroupIds)) {
            return Pair.of(productActivityMap, brandNewUserValidateResult);
        }

        Pair<Map<Integer, Boolean>, Map<String, List<Integer>>> pair = validateAndFillResult(bizType, productActivityMap, couponGroupIds, promoCtx);
        if (MapUtils.isNotEmpty(pair.getLeft())) {
            brandNewUserValidateResult.putAll(pair.getLeft());
        }
        if (MapUtils.isNotEmpty(pair.getRight())) {
            productUnPassActivityMap.putAll(pair.getRight());
        }

        if (MapUtils.isEmpty(productUnPassActivityMap)) {
            return Pair.of(productActivityMap, brandNewUserValidateResult);
        }

        Map<String, List<Integer>> productPassActivityMap = Maps.newHashMap();
        for (Map.Entry<String, List<Integer>> productEntry : productActivityMap.entrySet()) {
            String productId = productEntry.getKey();
            List<Integer> activityIds = productEntry.getValue();
            if (!productUnPassActivityMap.containsKey(productId)) {
                productPassActivityMap.put(productId, activityIds);
            } else {
                List<Integer> unPassCouponGroupIds = productUnPassActivityMap.get(productId);
                List<Integer> passActivityIds = Lists.newArrayList();
                for (Integer activityId : activityIds) {
                    if (!unPassCouponGroupIds.contains(activityCouponMap.get(activityId))) {
                        passActivityIds.add(activityId);
                    }
                }
                if (CollectionUtils.isNotEmpty(passActivityIds)) {
                    productPassActivityMap.put(productId, passActivityIds);
                }
            }
        }
        return Pair.of(productPassActivityMap, brandNewUserValidateResult);
    }

    private Pair<Map<Integer, Boolean>, Map<String, List<Integer>>> validateAndFillResult(Integer bizType, Map<String, List<Integer>> productActivityMap, List<Integer> couponGroupIds, CouponActivityContext promoCtx) {
        Map<Integer, Boolean> brandNewUserValidateResult = Maps.newHashMap();
        Map<String, List<Integer>> productUnPassActivityMap = Maps.newHashMap();

        int productCode = bizType == BizIdType.DEAL_GROUP_ID.getCode() ? CouponBusinessUtils.getDzTuangouProductCode() :
                promoCtx.getUserType() == User.DP ?
                        CouponBusiness.DP_PREPAY.getCode() :
                        CouponBusiness.MT_PREPAY.getCode();
        List<Long> productIds = productActivityMap.keySet()
                .stream()
                .map(Long::valueOf)
                .collect(Collectors.toList());
        int type = bizType == BizIdType.DEAL_GROUP_ID.getCode() ? UnifiedCouponProductType.DEALGROUPID.code : UnifiedCouponProductType.PRODUCTID.code;
        long userId = promoCtx.isMt() ? promoCtx.getMtUserId() : promoCtx.getDpUserId();
        MerchantNewUserValidateResult merchantNewUserValidateResult = couponNewUserWrapper.getMerchantNewUserResult(couponGroupIds, userId, promoCtx.getUserType().getCode(), productCode, productIds, type);
        if (merchantNewUserValidateResult != null) {
            if (MapUtils.isNotEmpty(merchantNewUserValidateResult.getBrandNewUserValidateResult())){
                brandNewUserValidateResult = merchantNewUserValidateResult.getBrandNewUserValidateResult();
            }
            if (MapUtils.isNotEmpty(merchantNewUserValidateResult.getProductUnPassActivityMap())) {
                productUnPassActivityMap = merchantNewUserValidateResult.getProductUnPassActivityMap()
                        .entrySet()
                        .stream()
                        .collect(Collectors.toMap(entry -> String.valueOf(entry.getKey()), Map.Entry::getValue));
            }
        }
        return Pair.of(brandNewUserValidateResult, productUnPassActivityMap);
    }

    private void fillRelatedInfo(List<IssueCouponActivity> activities, CouponActivityContext promotionRequestContext, Map<Integer, Boolean> brandNewUserValidateResult) {
        if (CollectionUtils.isEmpty(activities)) {
            return;
        }
        List<Integer> couponGroupIds = Lists.newArrayList();
        for (IssueCouponActivity activity : activities) {
            if (activity == null || activity.getCouponGroupId() <= 0) {
                continue;
            }
            couponGroupIds.add(activity.getCouponGroupId());
        }

        long userId;
        if (promotionRequestContext.isMt()) {
            userId = promotionRequestContext.getMtUserId();
        } else {
            userId = promotionRequestContext.getDpUserId();
        }

        Map<Integer, UnifiedCouponGroupDTO> couponGroupMap = couponBiz.batchQueryCouponGroupWithStock(couponGroupIds);
        List<UnifiedCouponDTO> unifiedCouponDTOs = couponBiz.batchQueryIssuedCouponList(userId, couponGroupIds, !promotionRequestContext.isMt());
        Map<Integer, List<UnifiedCouponDTO>> issuedCouponMap = Maps.newHashMap();
        for (UnifiedCouponDTO unifiedCouponDTO : unifiedCouponDTOs) {
            if (!issuedCouponMap.containsKey(unifiedCouponDTO.getCouponGroupDTO().getCouponGroupId())) {
                issuedCouponMap.put(unifiedCouponDTO.getCouponGroupDTO().getCouponGroupId(), Lists.<UnifiedCouponDTO>newArrayList());
            }
            issuedCouponMap.get(unifiedCouponDTO.getCouponGroupDTO().getCouponGroupId()).add(unifiedCouponDTO);
        }

        for (IssueCouponActivity activity : activities) {
            Validate.notNull(activity, "activity is null");
            Validate.notNull(couponGroupMap.get(activity.getCouponGroupId()), "couponGroup not exists");
            UnifiedCouponGroupDTO unifiedCouponGroupDTO = couponGroupMap.get(activity.getCouponGroupId());
            boolean isBrandNewUserCoupon = IssueCouponUtils.isBrandNewUserCoupon(unifiedCouponGroupDTO);
            if (isBrandNewUserCoupon) {
                if (!Boolean.TRUE.equals(brandNewUserValidateResult.get(unifiedCouponGroupDTO.getCouponGroupId()))) {
                    continue;
                }
            }
            if (unifiedCouponGroupDTO.getRemainDailyStock() <= 0) {
                activity.setUpDailyLimit(true);
            }
            activity.setCouponGroup(unifiedCouponGroupDTO);
            activity.setDpClient(!promotionRequestContext.isMt());
            List<UnifiedCouponDTO> issuedCoupon = issuedCouponMap.get(activity.getCouponGroupId());
            activity.setIssuedCoupon(issuedCoupon);
            activity.setIssuedUnUseUnExpireCoupon(IssueCouponUtils.buildUnUseUnExpiredCoupons(issuedCoupon));

        }
    }
}
