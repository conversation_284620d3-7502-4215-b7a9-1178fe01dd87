package com.dianping.pay.api.biz.activity.newloader.executors;

import com.dianping.api.log.util.LogContentUtil;
import com.dianping.cat.Cat;
import com.dianping.lion.client.Lion;
import com.dianping.pay.api.beans.IssueCouponActivity;
import com.dianping.pay.api.biz.activity.newloader.AbstractCouponIssueActivityQueryExecutor;
import com.dianping.pay.api.entity.issuecoupon.IssueCouponRequest;
import com.dianping.pay.api.enums.IssueCouponOptionType;
import com.dianping.pay.promo.log.annotation.LogLevel;
import com.dianping.violet.dto.Response;
import com.dianping.violet.enums.BeautyConfigEnum;
import com.dianping.violet.model.BeautyCouponInfo;
import com.dianping.violet.request.BeautyCouponRequest;
import com.google.common.collect.Lists;
import org.apache.commons.collections.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.List;

public class BeautyActivityQueryExecutor extends AbstractCouponIssueActivityQueryExecutor {

    private static final Logger LOGGER = LoggerFactory.getLogger(BeautyActivityQueryExecutor.class);

    public BeautyActivityQueryExecutor(IssueCouponRequest request) {
        super(request);
    }

    @Override
    protected List<IssueCouponActivity> loadFromRemote() {
        if (request.getProductId() == 0
                && request.getShopIdL() == 0
                && request.getCategoryId() == 0) {
            return Lists.newArrayList();
        }
        BeautyCouponRequest remoteRequest = new BeautyCouponRequest();
        remoteRequest.setUserIdLong(request.getUserId() );
        remoteRequest.setDealGroupId(request.getProductId());
        remoteRequest.setCategoryId(request.getCategoryId());
        remoteRequest.setShopIdLong(request.getShopIdL());
        remoteRequest.setCouponGroupId(request.getCouponOptionId());
        remoteRequest.setCityId(request.getCityId());
        if (request.isDpClient()) {
            remoteRequest.setAppCode(1);
            remoteRequest.setUuid(request.getClientInfo().getDpId());
        } else {
            remoteRequest.setAppCode(2);
            remoteRequest.setUuid(request.getClientInfo().getMtId());
        }
        remoteRequest.setRequestType(request.getRequestType());
        Response<List<BeautyCouponInfo>> beautyCouponInfoList = beautyCouponService.getBeautyCouponConfigInfo(remoteRequest);
        if(!beautyCouponInfoList.isSuccess() || CollectionUtils.isEmpty(beautyCouponInfoList.getResult())){
            return Lists.newArrayList();
        }
        if (LogLevel.getPriorityByName(Lion.getStringValue("mapi-pay-promo-web.auto.log.level", "DEBUG"))
                == LogLevel.DEBUG.getPriority()){
            LOGGER.info(String.format("userId=%s, shopId=%s, beautyCouponInfoList=%s", remoteRequest.getUserId(), remoteRequest.getShopId(),LogContentUtil.printList(beautyCouponInfoList.getResult())));
        }
        return toIssueCouponActivities(beautyCouponInfoList.getResult());
    }

    private List<IssueCouponActivity> toIssueCouponActivities(List<BeautyCouponInfo> beautyCouponInfoList) {
        Cat.logEvent("INVALID_METHOD_2", "com.dianping.pay.api.biz.activity.newloader.executors.BeautyActivityQueryExecutor.toIssueCouponActivities(java.util.List)");
        List<IssueCouponActivity> resultList = Lists.newArrayList();
        for (BeautyCouponInfo beautyCouponInfo : beautyCouponInfoList) {
            try {
                String beautyConfigStr = null;
                if(beautyCouponInfo.getBeautyCategory() != null && BeautyConfigEnum.getBeautyConfigEnum(beautyCouponInfo.getBeautyCategory()) != null){
                    beautyConfigStr = BeautyConfigEnum.getBeautyConfigEnum(beautyCouponInfo.getBeautyCategory()).desc;
                }
                resultList.add(new IssueCouponActivity(beautyCouponInfo.getCouponId(),
                        IssueCouponOptionType.BEAUTY_EVENT.getCode(),
                        beautyCouponInfo.getCategoryIconUrl(),
                        beautyConfigStr));
            } catch (IllegalArgumentException e) {
                LOGGER.info(String.format("BeautyActivityQueryExecutor toIssueCouponActivities IllegalArgumentException,coupon:%s",beautyCouponInfo.getCouponId()));
            }
        }
        return resultList;
    }
}
