package com.dianping.pay.api.biz.promodesk;

import com.dianping.cat.Cat;
import com.dianping.pay.api.beans.GetPromoDeskRequest;
import com.dianping.pay.api.beans.PromoProduct;
import com.dianping.pay.common.enums.PayPlatform;
import com.dianping.pay.common.enums.ProductCode;

import javax.annotation.Resource;

public class PromoDeskStrategyFactory {

    @Resource
    private CommonPromoDeskStrategy commonPromoDeskStrategy;
    @Resource
    private TuangouPromoDeskStrategy tuangouPromoDeskStrategy;
    @Resource
    private ShanhuiPromoDeskStrategy shanhuiPromoDeskStrategy;
    @Resource
    private KtvPromoDeskStrategy ktvPromoDeskStrategy;
    @Resource
    private MtKtvPromoDeskStrategy mtKtvPromoDeskStrategy;
    @Resource
    private DishPromoDeskStrategy dishPromoDeskStrategy;
    @Resource
    private TuangouPcPromoDeskStrategy tuangouPcPromoDeskStrategy;

    public PromoDeskStrategy getStrategy(GetPromoDeskRequest getPromoDeskRequest) {
        Cat.logEvent("INVALID_METHOD_4", "com.dianping.pay.api.biz.promodesk.PromoDeskStrategyFactory.getStrategy(com.dianping.pay.api.beans.GetPromoDeskRequest)");
        ProductCode productCode = ProductCode.getByCode(getMainProductCode(getPromoDeskRequest));
        if (productCode == ProductCode.TUANGOU
                && getPromoDeskRequest.getClientInfo().getPlatform() == PayPlatform.tg_pc.getCode()) {
            return tuangouPcPromoDeskStrategy;
        }
        switch (productCode) {
            case TUANGOU: return tuangouPromoDeskStrategy;
            case MO2O2PAY: return shanhuiPromoDeskStrategy;
            case KTV: return getPromoDeskRequest.isMtClient() ? mtKtvPromoDeskStrategy : ktvPromoDeskStrategy;
            case ORDERDISH: return dishPromoDeskStrategy;
            default: return commonPromoDeskStrategy;
        }
    }

    private int getMainProductCode(GetPromoDeskRequest getPromoDeskRequest) {
        Cat.logEvent("INVALID_METHOD_1", "com.dianping.pay.api.biz.promodesk.PromoDeskStrategyFactory.getMainProductCode(com.dianping.pay.api.beans.GetPromoDeskRequest)");
        int productCode = 0;
        if (getPromoDeskRequest.getPromoProductList().size() == 1) {
            productCode = getPromoDeskRequest.getPromoProductList().get(0).getProductCode();
        } else if (getPromoDeskRequest.getPromoProductList().size() > 1) {
            for (PromoProduct promoProduct : getPromoDeskRequest.getPromoProductList()) {
                if (promoProduct.getProductCode() > productCode) { // TODO 暂时先这么弄一下
                    productCode = promoProduct.getProductCode();
                }
            }
        }
        return productCode;
    }
}
