package com.dianping.pay.api.biz.activity.newloader.executors;

import com.dianping.cat.Cat;
import com.dianping.pay.api.beans.IssueCouponActivity;
import com.dianping.pay.api.biz.activity.newloader.AbstractCouponIssueActivityQueryExecutor;
import com.dianping.pay.api.entity.issuecoupon.IssueCouponRequest;
import com.dianping.poi.relation.service.api.PoiRelationService;
import com.dianping.spc.common.Response;
import com.dianping.tgc.open.ShopActivityQueryRemoteService;
import com.dianping.tgc.open.entity.ActivityDTO;
import com.dianping.tgc.open.entity.BatchActivityQueryRequest;
import com.dianping.tgc.open.entity.BatchQueryResponseDTO;
import com.dianping.tgc.open.enums.QuerySourceEnum;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;

import java.util.List;
import java.util.Map;

public class GeneralShopActivityQueryExecutor extends AbstractCouponIssueActivityQueryExecutor {

    private ShopActivityQueryRemoteService shopActivityQueryRemoteService;
    private PoiRelationService poiRelationCacheService;

    public GeneralShopActivityQueryExecutor(IssueCouponRequest request) {
        super(request);
    }

    @Override
    protected List<IssueCouponActivity> loadFromRemote() {
        if (CollectionUtils.isEmpty(request.getShopIdLList())) {
            return Lists.newArrayList();
        }
        List<Long> dpShopIdList = Lists.newArrayList();
        if (request.isDpClient()) {
            dpShopIdList = request.getShopIdLList();
        } else {
            Map<Long, List<Long>> matchedDpIdMap = Maps.newHashMap();
            try {
                matchedDpIdMap = poiRelationCacheService.queryDpByMtIdsL(request.getShopIdLList());
            } catch (Exception e) {
                Cat.logError("queryDpByMtIds", e);
            }
            if (MapUtils.isEmpty(matchedDpIdMap)) {
                return Lists.newArrayList();
            }
            for (List<Long> longs : matchedDpIdMap.values()) {
                if (CollectionUtils.isEmpty(longs)) {
                    continue;
                }
                dpShopIdList.addAll(longs);
            }
        }
        BatchActivityQueryRequest remoteRequest = new BatchActivityQueryRequest();
        remoteRequest.setShopLongIds(dpShopIdList);
        remoteRequest.setSource(request.isDpClient() ? QuerySourceEnum.DP.getValue() : QuerySourceEnum.MT.getValue());
        Response<BatchQueryResponseDTO> response = shopActivityQueryRemoteService.batchQueryShopActivity(remoteRequest);
        if (!response.isSuccess() || response.getResult() == null) {
            return Lists.newArrayList();
        }
        Map<Long, List<ActivityDTO>> activityMap = response.getResult().getShopCouponMap();
        if (MapUtils.isEmpty(activityMap)) {
            return Lists.newArrayList();
        }
        return toIssueCouponActivitiesL(activityMap);
    }

    public GeneralShopActivityQueryExecutor setShopActivityQueryRemoteService(ShopActivityQueryRemoteService shopActivityQueryRemoteService) {
        this.shopActivityQueryRemoteService = shopActivityQueryRemoteService;
        return this;
    }

    public GeneralShopActivityQueryExecutor setPoiRelationCacheService(PoiRelationService poiRelationCacheService) {
        this.poiRelationCacheService = poiRelationCacheService;
        return this;
    }

}
