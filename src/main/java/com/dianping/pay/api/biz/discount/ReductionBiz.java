package com.dianping.pay.api.biz.discount;

import com.dianping.api.common.enums.FeatureSwitchEnum;
import com.dianping.api.framework.FeatureContingency;
import com.dianping.cat.Cat;
import com.dianping.combiz.spring.util.PropertiesLoaderSupportUtils;
import com.dianping.pay.api.beans.GetPromoDeskRequest;
import com.dianping.pay.api.beans.PromoProduct;
import com.dianping.pay.api.biz.product.ProductJudgeBiz;
import com.dianping.pay.api.entity.promodesk.DiscountPromoTool;
import com.dianping.pay.common.enums.ProductCode;
import com.google.common.base.Predicate;
import com.google.common.collect.Collections2;
import com.google.common.collect.Lists;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.annotation.Resource;
import java.util.List;

/**
 * Created by huawei.li on 15/3/13.
 */
public class ReductionBiz {
    private static final Logger log = LoggerFactory.getLogger(ReductionBiz.class);
    @Resource
 	private ProductJudgeBiz productJudgeBiz;
    @Resource
 	private DiscountPromoQuery discountPromoQuery;
    @Resource
 	private MTDiscountPromoQuery mtDiscountPromoQuery;

    public DiscountPromoTool queryPromoDTOGroup(GetPromoDeskRequest getPromoDeskRequest, Predicate<PromoProduct> productFilterPredicate) {
        Cat.logEvent("INVALID_METHOD_5", "com.dianping.pay.api.biz.discount.ReductionBiz.queryPromoDTOGroup(com.dianping.pay.api.beans.GetPromoDeskRequest,com.google.common.base.Predicate)");
        if (FeatureContingency.isSwitchOff(FeatureSwitchEnum.REDUCTION)) {
            return new DiscountPromoTool();
        }
        if(PropertiesLoaderSupportUtils.getBoolProperty("pay-promo-display-octo-service.food.shanhui.promo.switch",false)
                && CollectionUtils.isNotEmpty(getPromoDeskRequest.getPromoProductList())
                && getPromoDeskRequest.getPromoProductList().get(0).getProductCode() == ProductCode.MO2O2PAY.getCode()){
            return mtDiscountPromoQuery.queryShanhuiPromo(getPromoDeskRequest,getPromoDeskRequest.getPromoProductList());
        }
        Pair<List<PromoProduct>, List<PromoProduct>> productPair = productJudgeBiz.judgeProductDeal(Lists.newArrayList(productFilterPredicate != null ? Collections2.filter(getPromoDeskRequest.getPromoProductList(), productFilterPredicate) : getPromoDeskRequest.getPromoProductList())
                , getPromoDeskRequest.isMtClient());
        if (CollectionUtils.isNotEmpty(productPair.getLeft())){
            return mtDiscountPromoQuery.queryPromo(getPromoDeskRequest, productPair.getLeft());
        } else {
            return discountPromoQuery.queryPromo(getPromoDeskRequest, productPair.getRight());
        }
    }
}
