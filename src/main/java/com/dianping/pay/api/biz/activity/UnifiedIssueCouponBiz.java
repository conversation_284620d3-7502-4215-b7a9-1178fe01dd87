package com.dianping.pay.api.biz.activity;

import com.alibaba.fastjson.JSON;
import com.dianping.api.common.enums.PageSourceEnum;
import com.dianping.api.constans.CommonConstants;
import com.dianping.api.constans.SplitConstants;
import com.dianping.api.picasso.UrlUtils;
import com.dianping.api.service.ShopWrapService;
import com.dianping.api.util.LionQueryUtils;
import com.dianping.cat.Cat;
import com.dianping.gmkt.coupon.common.api.enums.CouponBusiness;
import com.dianping.gmkt.coupon.common.api.enums.CouponGroupValueType;
import com.dianping.gmkt.coupon.common.api.enums.MerchantCouponProductType;
import com.dianping.gmkt.coupon.common.api.enums.label.MmcCategoryValueEnum;
import com.dianping.gmkt.coupon.common.api.utils.TgCouponBusinessUtils;
import com.dianping.gmkt.scene.api.delivery.dto.res.CouponDetailDTO;
import com.dianping.lion.client.Lion;
import com.dianping.mobile.framework.datatypes.IMobileContext;
import com.dianping.pay.api.beans.*;
import com.dianping.pay.api.biz.activity.newloader.dto.AbTestInfo;
import com.dianping.pay.api.biz.activity.newloader.dto.CouponActivityContext;
import com.dianping.pay.api.biz.activity.newloader.dto.PromotionRequestContext;
import com.dianping.pay.api.biz.discount.CouponBiz;
import com.dianping.pay.api.constants.AbTestConstant;
import com.dianping.pay.api.entity.IconPicInfo;
import com.dianping.pay.api.entity.issuecoupon.*;
import com.dianping.pay.api.enums.CouponStatus;
import com.dianping.pay.api.enums.CouponTitle;
import com.dianping.pay.api.enums.IssueCouponComponentStatus;
import com.dianping.pay.api.enums.MapiCouponValueType;
import com.dianping.pay.api.util.*;
import com.dianping.pay.api.wrapper.CouponNewUserWrapper;
import com.dianping.pay.coupon.service.enums.ExpireType;
import com.dianping.pay.promo.common.enums.coupon.UnifiedCouponProductType;
import com.dianping.pay.promo.execute.service.enums.PromoToolType;
import com.dianping.shopremote.remote.dto.ShopDTO;
import com.dianping.unified.coupon.manage.api.dto.UnifiedCouponDTO;
import com.dianping.unified.coupon.manage.api.dto.UnifiedCouponGroupDTO;
import com.google.common.base.Function;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.sankuai.ia.phx.utils.DateTimeUtils;
import com.sankuai.mkt.activity.api.merchantsharecoupon.enums.ExposeStatusEnum;
import com.sankuai.mkt.activity.api.merchantsharecoupon.model.MerchantShareCouponExposeDTO;
import com.sankuai.mpmkt.coupon.execute.api.UnifiedCouponExecuteValidateService;
import com.sankuai.mpmkt.coupon.search.api.dto.CouponDesc;
import com.sankuai.nib.mkt.common.base.enums.propertyValueType.ClientTypeEnum;
import com.sankuai.nib.mkt.magicBizGray.MagicBizControlReq;
import com.sankuai.nib.mkt.magicBizGray.MagicBizControlResult;
import com.sankuai.nib.mkt.magicBizGray.MagicBizGrayUtil;
import com.sankuai.nib.mkt.magicBizGray.MagicBizSceneEnum;
import com.sankuai.nibmkt.promotion.api.common.enums.MagicalMemberTagShowTypeEnum;
import com.sankuai.nibmkt.promotion.api.common.enums.PromotionPropertyEnum;
import com.sankuai.nibmkt.promotion.api.query.model.magicalmember.response.MagicalMemberCouponDTO;
import com.sankuai.nibmkt.promotion.api.query.model.magicalmember.response.MagicalMemberTagTextDTO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.ListUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang.ArrayUtils;
import org.apache.commons.lang.time.DateUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.joda.time.DateTime;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.unidal.tuple.Pair;
import com.sankuai.nib.mkt.common.base.util.JsonUtils;

import javax.annotation.Nullable;
import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

import static com.dianping.pay.api.biz.activity.newloader.promo.MagicalMemberCouponProcessor.*;

/**
 * Created by drintu on 18/6/8.
 */
@Component("unifiedIssueCouponBiz")
@Slf4j
public class UnifiedIssueCouponBiz {

    @Autowired
    private UnifiedCouponExecuteValidateService unifiedCouponExecuteValidateService;

    @Autowired
    private ShopWrapService shopWrapService;

    @Autowired
    private CouponNewUserWrapper couponNewUserWrapper;


    private static final String YIMEI_CATEGORY = "gc";
    private static final String YILIAO_CATEGORY = "gd";
    private static final String BEAUTY_CATEGORY = "ge";
    private static final Integer BEAUTY_SHOP_TYPE = 50;
    private static final Integer MEDICAL_SHOP_TYPE = 85;
    private static final Integer YIMEI_SHOP_CATEGORY = 183;
    public static final int XIUYU_SHOP_TYPE = 30;
    private static final int needHide = 1;
    private static final int notNeedHide = 0;
    private static final int magicalMemberCoupon = 1;


    public UnifiedIssueCouponComponentDo toUnifiedIssueComponent(List<IssueCouponActivity> activities, List<PlatformCoupon> platformCouponList) {
        UnifiedIssueCouponComponentDo componentDo = new UnifiedIssueCouponComponentDo();
        if (CollectionUtils.isEmpty(activities) && CollectionUtils.isEmpty(platformCouponList)) {
            return componentDo;
        }
        // 拆分商家券 平台券
        UnifiedIssueCouponListDo shopCoupon = new UnifiedIssueCouponListDo();
        shopCoupon.setUnifiedIssueCouponOption(new ArrayList<UnifiedIssueCouponOptionDo>());
        shopCoupon.setTitle(CouponTitle.SHOP_COUPON);
        UnifiedIssueCouponListDo platformCouponDo = new UnifiedIssueCouponListDo();
        platformCouponDo.setUnifiedIssueCouponOption(new ArrayList<UnifiedIssueCouponOptionDo>());
        platformCouponDo.setTitle(CouponTitle.PLATFROM_COUPON);
        //已领取的活动
        int issuedActivity = 0;

        //展示的活动
        int displayActivity = 0;

        //已领取未使用券数量
        int unUseActivity = 0;

        //商家券逻辑
        if (CollectionUtils.isNotEmpty(activities)) {
            Collections.sort(activities, new IssueCouponActivityComparator());
            for (IssueCouponActivity activity : activities) {
                if (!IssueCouponTypeUtils.allowDisplayActivity(activity)) {
                    continue;
                }
                displayActivity++;

                UnifiedCouponGroupDTO couponGroupDTO = activity.getCouponGroup();
                UnifiedIssueCouponOptionDo issueCouponOptionDo = toUnifiedIssueComponentOption(activity);
                if (issueCouponOptionDo != null) {
                    if (isShopCoupon(couponGroupDTO)) {
                        issueCouponOptionDo.setPlatformCoupon(false);
                        shopCoupon.getUnifiedIssueCouponOption().add(issueCouponOptionDo);
                    }
               /* else {
                    issueCouponOptionDo.setPlatformCoupon(true);
                    platformCoupon.getUnifiedIssueCouponOption().add(issueCouponOptionDo);
                }*/
                    IssueCouponOptionStatusDo issueCouponOptionStatusDo = issueCouponOptionDo.getStatus();
                    //已领取未使用
                    if (issueCouponOptionStatusDo != null && issueCouponOptionStatusDo.getStatus() == IssueCouponComponentStatus.ISSUED_MAX_PER_USER.getStatus()) {
                        unUseActivity++;
                    }
                }

                if (IssueCouponTypeUtils.isIssued(activity)) {
                    issuedActivity++;
                }
            }
            if (activities.get(0).isDpClient()) {
                componentDo.setIcon(UrlUtils.getDpCouponIcon());
            } else {
                componentDo.setIcon(UrlUtils.getMtCouponIcon());
            }
            //商家券外露文案
            componentDo.setCouponText(assembleCouponText(activities));
        }

        //平台券逻辑
        if (CollectionUtils.isNotEmpty(platformCouponList)) {
            Collections.sort(platformCouponList, new PlatformCouponComparator());
            for (PlatformCoupon platformCoupon : platformCouponList) {
                displayActivity++;
                //以下两种情况表示活动已经领取
                //1. 有可用券
                //2. 领券数量达到领取上限
                if (platformCoupon.getCouponAvailable() > 0
                        || (platformCoupon.getMaxPerUser() != 0 && platformCoupon.getCouponIssued() == platformCoupon.getMaxPerUser())) {
                    issuedActivity++;
                }
                UnifiedIssueCouponOptionDo issueCouponOptionDo = toPlatformCouponIssueComponentOption(platformCoupon);
                if (issueCouponOptionDo != null) {
                    issueCouponOptionDo.setPlatformCoupon(true);
                    platformCouponDo.getUnifiedIssueCouponOption().add(issueCouponOptionDo);

                    IssueCouponOptionStatusDo issueCouponOptionStatusDo = issueCouponOptionDo.getStatus();
                    //已领取未使用
                    if (issueCouponOptionStatusDo != null && issueCouponOptionStatusDo.getStatus() == IssueCouponComponentStatus.ISSUED_MAX_PER_USER.getStatus()) {
                        unUseActivity++;
                    }
                }
            }
            if (platformCouponList.get(0).isDpClient()) {
                componentDo.setIcon(UrlUtils.getDpCouponIcon());
            } else {
                componentDo.setIcon(UrlUtils.getMtCouponIcon());
            }

            //平台券外露文案
            componentDo.setPlatformCouponText(assemblePlatformCouponText(platformCouponList));
        }

        List<UnifiedIssueCouponListDo> unifiedIssueCouponListDos = Lists.newArrayList();
        if (CollectionUtils.isNotEmpty(shopCoupon.getUnifiedIssueCouponOption())) {
            unifiedIssueCouponListDos.add(shopCoupon);
        }
        if (CollectionUtils.isNotEmpty(platformCouponDo.getUnifiedIssueCouponOption())) {
            unifiedIssueCouponListDos.add(platformCouponDo);
        }
        componentDo.setUnifiedIssueCouponLists(unifiedIssueCouponListDos);

        /**
         * 有券未领取:去领取
         * 有券未使用:去使用
         * 其他:空
         */
        if (issuedActivity < displayActivity) {
            componentDo.setEntranceText("去领取");
        } else if (unUseActivity > 0) {
            componentDo.setEntranceText("去使用");
        } else {
            componentDo.setEntranceText("");
        }
        return componentDo;
    }

    private UnifiedIssueCouponOptionDo toPlatformCouponIssueComponentOption(PlatformCoupon platformCoupon) {
        Cat.logEvent("INVALID_METHOD_1", "com.dianping.pay.api.biz.activity.UnifiedIssueCouponBiz.toPlatformCouponIssueComponentOption(com.dianping.pay.api.beans.PlatformCoupon)");
        UnifiedIssueCouponOptionDo unifiedIssueCouponOptionDo = new UnifiedIssueCouponOptionDo();

        //设置券领取状态
        buildIssueStatus(platformCoupon, unifiedIssueCouponOptionDo);
        //设置满减文案
        buildAmountDesc(platformCoupon, unifiedIssueCouponOptionDo);
        //设置跳转链接
        buildToUseUrl(platformCoupon, unifiedIssueCouponOptionDo);
        //平台券直接使用配券的券标题
        unifiedIssueCouponOptionDo.setTitle(platformCoupon.getCouponTitle());

        unifiedIssueCouponOptionDo.setPriceLimit(platformCoupon.getPriceLimit());
        unifiedIssueCouponOptionDo.setAmount(platformCoupon.getAmount());
        unifiedIssueCouponOptionDo.setUnifiedCouponGroupId(platformCoupon.getUnifiedCouponGroupId());
        unifiedIssueCouponOptionDo.setDesc(formatCouponTimePeriod(platformCoupon));


        return unifiedIssueCouponOptionDo;
    }

    private void buildIssueStatus(PlatformCoupon platformCoupon, UnifiedIssueCouponOptionDo unifiedIssueCouponOptionDo) {
        Cat.logEvent("INVALID_METHOD_1", "com.dianping.pay.api.biz.activity.UnifiedIssueCouponBiz.buildIssueStatus(com.dianping.pay.api.beans.PlatformCoupon,com.dianping.pay.api.entity.issuecoupon.UnifiedIssueCouponOptionDo)");
        //以下两种情况均可以领券
        //1. 券未领取
        //2. 券已领取，但是已领取数小于最大可领取数量且用户目前无可使用券时（已领取的券过期了或者已经使用了)
        if (platformCoupon.getStatus() == CouponStatus.UNISSUED
                || (platformCoupon.getStatus() == CouponStatus.ALREADY_ISSUED && (platformCoupon.getCouponIssued() < platformCoupon.getMaxPerUser() || platformCoupon.getMaxPerUser() == 0) && platformCoupon.getCouponAvailable() == 0)) {
            //券可领
            unifiedIssueCouponOptionDo.setStatus(new IssueCouponOptionStatusDo(IssueCouponComponentStatus.ISSUE_ENABLE));
            unifiedIssueCouponOptionDo.setIssueButtonText("立即领取");

            //设置已领取的图片
            if (platformCoupon.isDpClient()) {
                unifiedIssueCouponOptionDo.setIssuedIcon(UrlUtils.getDpIssuedIcon());
            } else {
                unifiedIssueCouponOptionDo.setIssuedIcon(UrlUtils.getMtIssuedIcon());
            }
        } else {
            if (platformCoupon.getCouponAvailable() > 0) {
                //可用的券数量>0时，设置券文案为"去使用"
                unifiedIssueCouponOptionDo.setStatus(new IssueCouponOptionStatusDo(IssueCouponComponentStatus.ISSUED_MAX_PER_USER));
                unifiedIssueCouponOptionDo.setIssueButtonText("去使用");

                //设置已领取的图片
                if (platformCoupon.isDpClient()) {
                    unifiedIssueCouponOptionDo.setIssuedIcon(UrlUtils.getDpIssuedIcon());
                } else {
                    unifiedIssueCouponOptionDo.setIssuedIcon(UrlUtils.getMtIssuedIcon());
                }
            } else if (platformCoupon.getMaxPerUser() != 0 && platformCoupon.getCouponIssued() == platformCoupon.getMaxPerUser() && platformCoupon.getCouponUsed() > 0 && platformCoupon.getCouponAvailable() == 0) {
                //用户领券达到上限，已经使用过券，且无可用券时，展示"已使用"
                unifiedIssueCouponOptionDo.setStatus(new IssueCouponOptionStatusDo(IssueCouponComponentStatus.USED));
                unifiedIssueCouponOptionDo.setIssueButtonText("已使用");

                //设置已使用的图片
                if (platformCoupon.isDpClient()) {
                    unifiedIssueCouponOptionDo.setIssuedIcon(UrlUtils.getDpUsedIcon());
                } else {
                    unifiedIssueCouponOptionDo.setIssuedIcon(UrlUtils.getMtUsedIcon());
                }
            }
        }
    }


    private void buildToUseUrl(PlatformCoupon platformCoupon, UnifiedIssueCouponOptionDo unifiedIssueCouponOptionDo) {
        Cat.logEvent("INVALID_METHOD_2", "com.dianping.pay.api.biz.activity.UnifiedIssueCouponBiz.buildToUseUrl(com.dianping.pay.api.beans.PlatformCoupon,com.dianping.pay.api.entity.issuecoupon.UnifiedIssueCouponOptionDo)");
        //默认使用券配置的使用链接，如果没有配置，则跳转到券详情页
        if (StringUtils.isNotBlank(platformCoupon.getToUseUrl())) {
            unifiedIssueCouponOptionDo.setToUseUrl(platformCoupon.getToUseUrl());
        } else if (StringUtils.isNotBlank(platformCoupon.getUnifiedCouponId())) {
            if (platformCoupon.isDpClient()) {
                unifiedIssueCouponOptionDo.setToUseUrl(UrlUtils.getDpToUseUrl(platformCoupon.getUnifiedCouponId()));
            } else {
                unifiedIssueCouponOptionDo.setToUseUrl(UrlUtils.getMtToUseUrl(platformCoupon.getUnifiedCouponId()));
            }
        }

    }

    private void buildAmountDesc(PlatformCoupon platformCoupon, UnifiedIssueCouponOptionDo unifiedIssueCouponOptionDo) {
        Cat.logEvent("INVALID_METHOD_3", "com.dianping.pay.api.biz.activity.UnifiedIssueCouponBiz.buildAmountDesc(com.dianping.pay.api.beans.PlatformCoupon,com.dianping.pay.api.entity.issuecoupon.UnifiedIssueCouponOptionDo)");
        String amountDesc = "";
        if (new BigDecimal(Double.toString(platformCoupon.getPriceLimit())).compareTo(BigDecimal.ZERO) > 0) {
            amountDesc = "满" + Double.toString(platformCoupon.getPriceLimit()) + "可用";
        } else {
            amountDesc = "无门槛";
        }
        unifiedIssueCouponOptionDo.setAmountDesc(amountDesc);
    }

    public String formatCouponTimePeriod(PlatformCoupon platformCoupon) {
        Cat.logEvent("INVALID_METHOD_4", "com.dianping.pay.api.biz.activity.UnifiedIssueCouponBiz.formatCouponTimePeriod(com.dianping.pay.api.beans.PlatformCoupon)");
        if (platformCoupon.getExpireType() == ExpireType.FLOAT.code) {
            return "领取后" + platformCoupon.getFloatDay() + "天有效";
        } else if (platformCoupon.getExpireType() == ExpireType.NEXT_DAY_TAKE_EFFECT.code) {
            return "领取后第二天生效，有效期" + platformCoupon + "天";
        } else {
            long remainTime = platformCoupon.getEndTime() * 1000 - System.currentTimeMillis();
            if (remainTime / DateUtils.MILLIS_PER_DAY > 3) {
                SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy.MM.dd");
                //下面*1000的原因是：调搜索服务，查询出的券时间是秒级别的，这里使用毫秒级别
                return dateFormat.format(platformCoupon.getStartTime() * 1000) + "-" + dateFormat.format(platformCoupon.getEndTime() * 1000);
            } else if (remainTime / DateUtils.MILLIS_PER_DAY > 0) {
                return remainTime / DateUtils.MILLIS_PER_DAY + "天后过期";
            } else {
                return "今天过期";
            }
        }
    }

    private String assemblePlatformCouponText(List<PlatformCoupon> platformCouponList) {
        Cat.logEvent("INVALID_METHOD_1", "com.dianping.pay.api.biz.activity.UnifiedIssueCouponBiz.assemblePlatformCouponText(java.util.List)");
        int maxPlatformCouponDisplayLimit = 4;
        maxPlatformCouponDisplayLimit = Math.min(platformCouponList.size(), maxPlatformCouponDisplayLimit);
        List<String> platformCouponTextList = Lists.newArrayList();
        for (int i = 0; i < maxPlatformCouponDisplayLimit; i++) {
            String platformCouponText = buildPlatformCouponText(platformCouponList.get(i));
            platformCouponTextList.add(platformCouponText);
        }
        return StringUtils.join(platformCouponTextList, SplitConstants.COMMON_SPLIT);
    }

    private String buildPlatformCouponText(PlatformCoupon platformCoupon) {
        Cat.logEvent("INVALID_METHOD_2", "com.dianping.pay.api.biz.activity.UnifiedIssueCouponBiz.buildPlatformCouponText(com.dianping.pay.api.beans.PlatformCoupon)");
        StringBuffer resultBuf = new StringBuffer();
        if (new BigDecimal(Double.toString(platformCoupon.getPriceLimit())).compareTo(BigDecimal.ZERO) > 0) {
            String template = "满%s减%s";
            resultBuf.append(String.format(template, Double.toString(platformCoupon.getPriceLimit()), Double.toString(platformCoupon.getAmount())));
        } else {
            String template = "无门槛减%s";
            resultBuf.append(String.format(template, Double.toString(platformCoupon.getAmount())));

        }
        return resultBuf.toString();
    }

    private String assembleCouponText(List<IssueCouponActivity> activities) {
        boolean hasMultiProductCodeCoupon = false;
        int maxCouponDisplayLimit = 2;
        for (IssueCouponActivity activity : activities) {
            if (CollectionUtils.isNotEmpty(activity.getCouponGroup().getProductCodeList()) && activity.getCouponGroup().getProductCodeList().size() > 1) {
                hasMultiProductCodeCoupon = true;
                break;
            }
        }
        List<String> couponTextList = Lists.newArrayList();
        int maxCouponDisplay = Math.min(activities.size(), maxCouponDisplayLimit);
        for (int i = 0; i < maxCouponDisplay; i++) {
            String couponText = buildCouponText(activities.get(i), !hasMultiProductCodeCoupon);
            couponTextList.add(couponText);
        }
        return StringUtils.join(couponTextList, SplitConstants.COMMON_SPLIT);
    }

    private String buildCouponText(IssueCouponActivity activity, boolean displayProductCode) {
        StringBuffer resultBuf = new StringBuffer();
        UnifiedCouponGroupDTO couponGroupDTO = activity.getCouponGroup();
        if (CollectionUtils.isNotEmpty(couponGroupDTO.getProductCodeList()) &&
                displayProductCode) {
            Integer productCode = couponGroupDTO.getProductCodeList().get(0);
            CouponBusiness couponBusiness = CouponBusiness.getByCode(productCode);
            //保底为团购
            if (couponBusiness == null) {
                couponBusiness = CouponBusiness.DZ_TUANGOU;
            }
            if (ArrayUtils.contains(new int[]{CouponBusiness.KTV.getCode(), CouponBusiness.JOY.getCode(),
                    CouponBusiness.GENERAL_BOOK.getCode(), CouponBusiness.CARD_BUSINESS_KTV.getCode(),
                    CouponBusiness.CARD_BUSINESS_JOY.getCode(), CouponBusiness.CARD_BUSINESS_GENERAL_BOOK.getCode()}, productCode)) {
                resultBuf.append("预订");
            } else if (TgCouponBusinessUtils.isDzTuangouCouponBusiness(productCode)) {
                resultBuf.append("团购");
            } else {
                resultBuf.append(couponBusiness.getMessage());
            }

        }

        if (couponGroupDTO.getPriceLimit() != null && couponGroupDTO.getPriceLimit().compareTo(BigDecimal.ZERO) > 0) {
            String template = "满%s减%s";
            resultBuf.append(String.format(template, couponGroupDTO.getPriceLimit().stripTrailingZeros().toPlainString(),
                    couponGroupDTO.getDiscountAmount().stripTrailingZeros().toPlainString()));
        } else {
            String template = "无门槛减%s";
            resultBuf.append(String.format(template, couponGroupDTO.getDiscountAmount().stripTrailingZeros().toPlainString()));
        }
        return resultBuf.toString();

    }

    private UnifiedIssueCouponOptionDo toUnifiedIssueComponentOption(IssueCouponActivity activity) {
        UnifiedIssueCouponOptionDo unifiedIssueCouponOptionDo = new UnifiedIssueCouponOptionDo();
        if (IssueCouponTypeUtils.allowGetCoupon(activity)
                && (CollectionUtils.isEmpty(activity.getIssuedCoupon()) || activity.getCouponGroup().getMaxPerUser() == 0
                || activity.getCouponGroup().getMaxPerUser() > activity.getIssuedCoupon().size())) {
            // 可领
            unifiedIssueCouponOptionDo.setStatus(new IssueCouponOptionStatusDo(IssueCouponComponentStatus.ISSUE_ENABLE));
            unifiedIssueCouponOptionDo.setIssueButtonText("立即领取");

            //设置已领取的图片
            if (activity.isDpClient()) {
                unifiedIssueCouponOptionDo.setIssuedIcon(UrlUtils.getDpIssuedIcon());
            } else {
                unifiedIssueCouponOptionDo.setIssuedIcon(UrlUtils.getMtIssuedIcon());
            }
        } else if (!IssueCouponTypeUtils.allowGetCoupon(activity) && IssueCouponTypeUtils.canUse(activity)) {
            // 已经领了，未过期
            unifiedIssueCouponOptionDo.setStatus(new IssueCouponOptionStatusDo(IssueCouponComponentStatus.ISSUED_MAX_PER_USER));
            unifiedIssueCouponOptionDo.setIssueButtonText("马上使用");

            //设置已领取的图片
            if (activity.isDpClient()) {
                unifiedIssueCouponOptionDo.setIssuedIcon(UrlUtils.getDpIssuedIcon());
            } else {
                unifiedIssueCouponOptionDo.setIssuedIcon(UrlUtils.getMtIssuedIcon());
            }
        } else if (IssueCouponTypeUtils.isUsed(activity)) {
            unifiedIssueCouponOptionDo.setStatus(new IssueCouponOptionStatusDo(IssueCouponComponentStatus.USED));
            unifiedIssueCouponOptionDo.setIssueButtonText("");

            //设置已使用的图片
            if (activity.isDpClient()) {
                unifiedIssueCouponOptionDo.setIssuedIcon(UrlUtils.getDpUsedIcon());
            } else {
                unifiedIssueCouponOptionDo.setIssuedIcon(UrlUtils.getMtUsedIcon());
            }

        } else {
            return null;
        }
        unifiedIssueCouponOptionDo.setPriceLimit(activity.getCouponGroup().getPriceLimit().doubleValue());
        unifiedIssueCouponOptionDo.setAmount(activity.getCouponGroup().getDiscountAmount().doubleValue());
        unifiedIssueCouponOptionDo.setCouponGroupId(activity.getCouponGroupId());
        unifiedIssueCouponOptionDo.setUnifiedCouponGroupId(String.valueOf(activity.getCouponGroupId()));

        boolean containsTuangou = false;
        boolean couponProductLimit = CollectionUtils.isNotEmpty(activity.getCouponGroup().getCouponProductList());
        if (CollectionUtils.isNotEmpty(activity.getCouponGroup().getProductCodeList())) {
            String productCodeLimit = "";
            Iterator<Integer> iterator = activity.getCouponGroup().getProductCodeList().iterator();
            while (iterator.hasNext()) {
                Integer productCode = iterator.next();
                if (productCode != null && productCode > 0) {
                    String message = "";

                    boolean isTuangou = false;
                    CouponBusiness couponBusiness = CouponBusiness.getByCode(productCode);
                    //保底为团购
                    if (couponBusiness == null) {
                        couponBusiness = CouponBusiness.DZ_TUANGOU;
                    }
                    isTuangou = TgCouponBusinessUtils.isDzTuangouCouponBusiness(couponBusiness.getCode());
                    if (isTuangou) {
                        message = "团购";
                        containsTuangou = true;
                    } else {
                        message = couponBusiness.getMessage();
                    }

                    productCodeLimit = productCodeLimit.concat(String.format("限【%s】", message));
                    //仅在团购且有团单限制时才显示
                    if (isTuangou && couponProductLimit) {
                        productCodeLimit = productCodeLimit.concat("指定商品");
                    }
                    if (iterator.hasNext()) {
                        productCodeLimit = productCodeLimit + "和";
                    }
                }
            }
            unifiedIssueCouponOptionDo.setTitle(productCodeLimit);
        }
        unifiedIssueCouponOptionDo.setAmountDesc(IssueCouponUtils.formatTitle(activity.getCouponGroup()));

        IssueCouponOptionStatusDo status = unifiedIssueCouponOptionDo.getStatus();
        if (status != null && status.getStatus() == IssueCouponComponentStatus.ISSUED_MAX_PER_USER.getStatus()) {
            //仅在团购且有团单限制时才显示"马上使用"
            if (containsTuangou && couponProductLimit) {
                //设置"马上使用"链接
                if (IssueCouponTypeUtils.isIssued(activity) && isShopCoupon(activity.getCouponGroup())) {
                    if (activity.isDpClient()) {
                        unifiedIssueCouponOptionDo.setToUseUrl(UrlUtils.getDpToUseUrl(IssueCouponTypeUtils.getIssuedUnifiedCouponDto(activity).getUnifiedCouponId()));
                    } else {
                        unifiedIssueCouponOptionDo.setToUseUrl(UrlUtils.getMtToUseUrl(IssueCouponTypeUtils.getIssuedUnifiedCouponDto(activity).getUnifiedCouponId()));
                    }
                }
            } else {
                unifiedIssueCouponOptionDo.setIssueButtonText("");
            }
        }
/*

        if (!isShopCoupon(activity.getCouponGroup())) {
            unifiedIssueCouponOptionDo.setToUseUrl(activity.getCouponGroup().getRedirectLink());
        }
*/

        // 使用时间
        unifiedIssueCouponOptionDo.setDesc(IssueCouponUtils.formatCouponTimePeriod(activity.getCouponGroup()));
        return unifiedIssueCouponOptionDo;
    }

    public static boolean isShopCoupon(UnifiedCouponGroupDTO unifiedCouponGroupDTO) {
        return PromoToolType.SHOP_COUPON.getCode() == CouponBiz.couponGroupType2PromoToolType(unifiedCouponGroupDTO.getCouponGroupType());
    }

    public PromotionDisplayResponse toPromotionDisplayResponse(CouponActivityContext couponActivityContext, PromotionRequestContext promotionRequestContext) {
        if (couponActivityContext.getIssueCouponActivities() == null && couponActivityContext.getReturnPromotionDisplayDto() == null) {
            return null;
        }

        //领券明细
        IssueCouponDto issueCouponDto = null;
        //已领取的活动
        int issuedActivity = 0;
        //展示的活动
        int displayActivity = 0;
        //已领取未使用券数量
        int unUseActivity = 0;

        List<IssueCouponActivity> activities = couponActivityContext.getIssueCouponActivities();
        PromotionOuterDisplayDto promotionOuterDisplayDtoCoupon = null;
        //商家券逻辑
        if (CollectionUtils.isNotEmpty(activities)) {
            issueCouponDto = new IssueCouponDto();
            issueCouponDto.setIssueCouponTitle("抵用券");
            List<UnifiedIssueCouponOptionDo> unifiedIssueCouponOptionDos = Lists.newArrayListWithExpectedSize(activities.size());
            issueCouponDto.setUnifiedIssueCouponOptionDos(unifiedIssueCouponOptionDos);

            Collections.sort(activities, new IssueCouponActivityComparator());
            for (IssueCouponActivity activity : activities) {
                if (!IssueCouponTypeUtils.allowDisplayActivity(activity)) {
                    continue;
                }
                displayActivity++;

                UnifiedCouponGroupDTO couponGroupDTO = activity.getCouponGroup();
                UnifiedIssueCouponOptionDo issueCouponOptionDo = toUnifiedIssueComponentOption(activity);
                if (issueCouponOptionDo != null) {
                    if (isShopCoupon(couponGroupDTO)) {
                        issueCouponOptionDo.setPlatformCoupon(false);
                        unifiedIssueCouponOptionDos.add(issueCouponOptionDo);
                    }
                    IssueCouponOptionStatusDo issueCouponOptionStatusDo = issueCouponOptionDo.getStatus();
                    //已领取未使用
                    if (issueCouponOptionStatusDo != null && issueCouponOptionStatusDo.getStatus() == IssueCouponComponentStatus.ISSUED_MAX_PER_USER.getStatus()) {
                        unUseActivity++;
                    }
                }
                if (IssueCouponTypeUtils.isIssued(activity)) {
                    issuedActivity++;
                }
            }

            //外露文案
            promotionOuterDisplayDtoCoupon = new PromotionOuterDisplayDto();
            promotionOuterDisplayDtoCoupon.setOuterPromotionTitle("抵用券");
            promotionOuterDisplayDtoCoupon.setOuterPromotionText(assembleCouponText(activities));
        }

        //拼装内层优惠信息
        PromotionDisplayDto promotionDisplayDto = new PromotionDisplayDto();
        promotionDisplayDto.setIssueCouponDto(issueCouponDto);
        promotionDisplayDto.setPromotionDisplayTitle("优惠");

        ReturnPromotionDisplayDto returnPromotionDisplayDto = couponActivityContext.getReturnPromotionDisplayDto();
        PromotionOuterDisplayDto promotionOuterDisplayDtoReturn = null;
        if (returnPromotionDisplayDto != null) {
            promotionDisplayDto.setReturnPromotionDisplayDto(returnPromotionDisplayDto);

            List<ReturnPromotionDetail> returnPromotionDetails = returnPromotionDisplayDto.getReturnPromotionDetails();
            List<String> summaryInfos = Lists.newArrayListWithExpectedSize(returnPromotionDetails.size());
            if (CollectionUtils.isNotEmpty(returnPromotionDetails)) {
                for (ReturnPromotionDetail returnPromotionDetail : returnPromotionDetails) {
                    summaryInfos.add(returnPromotionDetail.getSummaryInfo());
                }
            }
            //外露文案
            promotionOuterDisplayDtoReturn = new PromotionOuterDisplayDto();
            promotionOuterDisplayDtoReturn.setOuterPromotionTitle("返礼");
            promotionOuterDisplayDtoReturn.setOuterPromotionText(StringUtils.join(summaryInfos, SplitConstants.COMMON_SPLIT));
        }

        //拼装优惠返回
        PromotionDisplayResponse promotionDisplayResponse = new PromotionDisplayResponse();
        //外层图标--暂不显示返券图标
        if (promotionRequestContext.isMt()) {
            promotionDisplayResponse.setIcon(UrlUtils.getMtCouponIcon());
        } else {
            promotionDisplayResponse.setIcon(UrlUtils.getDpCouponIcon());
        }

        //外层展示文案
        List<PromotionOuterDisplayDto> promotionOuterDisplayDtos = Lists.newArrayList();
        //领券
        CollectionUtils.addIgnoreNull(promotionOuterDisplayDtos, promotionOuterDisplayDtoCoupon);
        //返礼
        CollectionUtils.addIgnoreNull(promotionOuterDisplayDtos, promotionOuterDisplayDtoReturn);

        promotionDisplayResponse.setPromotionOuterDisplayDtos(promotionOuterDisplayDtos);


        //优先展示领券
        if (promotionOuterDisplayDtoCoupon != null) {
            /**
             * 有券未领取:去领取
             * 有券未使用:去使用
             * 其他:空
             */
            if (issuedActivity < displayActivity) {
                promotionDisplayResponse.setEntranceText("去领取");
            } else if (unUseActivity > 0) {
                promotionDisplayResponse.setEntranceText("去使用");
            } else {
                promotionDisplayResponse.setEntranceText("");
            }
        } else if (promotionOuterDisplayDtoReturn != null) {//展示返礼
            promotionDisplayResponse.setEntranceText("返券详情");
        }

        //领券组件详情
        promotionDisplayResponse.setPromotionDisplayDto(promotionDisplayDto);

        return promotionDisplayResponse;
    }


    public IssueCouponComponentDTO toIssueCouponComponentResponse(CouponActivityContext couponActivityContext,
                                                                  PromotionRequestContext promotionRequestContext, IMobileContext context) {
        IssueCouponComponentDTO issueCouponComponentDTO = new IssueCouponComponentDTO();
        IssueCouponComponentOceanDTO oceanDTO = new IssueCouponComponentOceanDTO();
        Pair<String, Integer> pair = callCategory(couponActivityContext.getShopDTO(), promotionRequestContext.getShopIdL(), promotionRequestContext.isMt());
        String category = pair.getKey();
        Integer shopType = pair.getValue();
        boolean fillLimitTime = shopType == XIUYU_SHOP_TYPE;
        // 外层-券整体模块打点
        oceanDTO.setSimpleModuleOcean(buildOceanItemIfo(category, Lion.get(LionConstants.SIMPLE_MODULE_BID_VIEW, ""),
                Lion.get(LionConstants.SIMPLE_MODULE_BID_CLICK, "")));
        // 外层-查看全部券模块打点
        oceanDTO.setViewAllOcean(buildOceanItemIfo(category, "", Lion.get(LionConstants.SIMPLE_MODULE_VIEW_CLICK, "")));
        // 外层-查看券信息
        oceanDTO.setCouponInfoOcean(buildOceanItemIfo(category, "", Lion.get(LionConstants.SIMPLE_MODULE_COUPON_INFO_CLICK, "")));

        //内层-券整体模块浮层打点
        oceanDTO.setModuleOcean(buildOceanItemIfo(category, Lion.get(LionConstants.MODULE_BID_VIEW, ""), ""));
        //内层-适用品类打点
        oceanDTO.setCategoryConditionOcean(buildOceanItemIfo(category, Lion.get(LionConstants.MODULE_CATEGORY_BID_VIEW, ""),
                Lion.get(LionConstants.MODULE_CATEGORY_CLICK_VIEW, "")));
        //内层-单个券模块打点
        oceanDTO.setCouponDetailItemOcean(buildOceanItemIfo(category, Lion.get(LionConstants.MODULE_COUPON_BID_VIEW, ""),
                Lion.get(LionConstants.MODULE_COUPON_CLICK_VIEW, "")));
        issueCouponComponentDTO.setOcean(oceanDTO);
        issueCouponComponentDTO.setCouponViewType(buildIssueCouponFullComponentDetail(couponActivityContext, promotionRequestContext, context, fillLimitTime));
        return issueCouponComponentDTO;
    }

    private IssueCouponFullComponentDetail buildIssueCouponFullComponentDetail(CouponActivityContext couponActivityContext,
                                                                               PromotionRequestContext promotionRequestContext, IMobileContext context, boolean fillLimitTime) {
        DotUtils.dotForPoiConsistentIssueCoupon(couponActivityContext, promotionRequestContext);
        List<IssueCouponActivity> issueCouponActivitieList = couponActivityContext.getIssueCouponActivities();
        List<MerchantShareCouponExposeDTO> couponExposeDTOS = couponActivityContext.getCouponExposeDTOS();
        ShopResourcePromotionDO shopResourcePromotionDO = couponActivityContext.getShopResourcePromotionDO();
        org.apache.commons.lang3.tuple.Pair<List<CouponDesc>, List<CouponDesc>> merchantAccountCouponPromotion = couponActivityContext.getMerchantAccountCouponPromotion();
        TopAdResourcePromotionDO topAdResourcePromotionDO = couponActivityContext.getTopAdResourcePromotionDO();
        MagicalMemberTagRawDO magicalMemberTagDO = couponActivityContext.getMagicalMemberTagRawDO();
        List<String> magicalMemberCouponIds = buildUserHoldMagicalMemberCouponIds(magicalMemberTagDO);   // 用户已有神券用户券id列表
        if (CollectionUtils.isEmpty(issueCouponActivitieList) && CollectionUtils.isEmpty(couponExposeDTOS)
                && shopResourcePromotionDO == null && merchantAccountCouponPromotion == null && magicalMemberTagDO == null) {
            return null;
        }
        IssueCouponFullComponentDetail fullComponentDetail = new IssueCouponFullComponentDetail();
        final List<IssueCouponActivity> issueCouponActivities = issueCouponActivitieList == null ? Lists.<IssueCouponActivity>newArrayList() : issueCouponActivitieList;

        // 分成4类：
        couponExposeDTOS = couponExposeDTOS == null ? Lists.<MerchantShareCouponExposeDTO>newArrayList() : couponExposeDTOS;
        // 店铺新客通用券
        final List<IssueCouponActivity> commonNewActivities = Lists.newArrayList();
        // 店铺非新客通用券
        final List<IssueCouponActivity> commonActivities = Lists.newArrayList();
        // 品类券
        final List<IssueCouponActivity> categoryActivities = Lists.newArrayList();

        // 校验品牌新客画像
        Map<Integer, Boolean> brandNewUserValidateResult = getBrandValidResult(issueCouponActivities, promotionRequestContext);

        for (IssueCouponActivity issueCouponActivity : issueCouponActivities) {

            // 目前只展示券有效 & 领取后可使用的券
            if (!IssueCouponTypeUtils.allowDisplayActivity(issueCouponActivity)) {
                continue;
            }

            UnifiedCouponGroupDTO unifiedCouponGroupDTO = issueCouponActivity.getCouponGroup();
            if (unifiedCouponGroupDTO == null) {
                continue;
            }
            int merchantProductType = IssueCouponUtils.getMerchantCouponProductType(unifiedCouponGroupDTO);
            boolean isNewUserCoupon = IssueCouponUtils.isNewUserCoupon(unifiedCouponGroupDTO);

            if (isNewUserCoupon) {
                if (!Boolean.TRUE.equals(brandNewUserValidateResult.get(unifiedCouponGroupDTO.getCouponGroupId()))) {
                    continue;
                }
            }

            if (merchantProductType == MerchantCouponProductType.ALL_PRODUCT.getValue()) {
                if (isNewUserCoupon) {
                    commonNewActivities.add(issueCouponActivity);
                } else {
                    commonActivities.add(issueCouponActivity);
                }
            } else if (merchantProductType == MerchantCouponProductType.CERTAIN_LABLE.getValue()) {
                categoryActivities.add(issueCouponActivity);
            }
        }

        List<CouponDesc> noIssuedMerchantCoupons = null;
        List<CouponDesc> issuedMerchantCoupons = null;
        if (merchantAccountCouponPromotion != null) {
            noIssuedMerchantCoupons = merchantAccountCouponPromotion.getLeft();
            issuedMerchantCoupons = merchantAccountCouponPromotion.getRight();
        }

        if (CollectionUtils.isEmpty(commonNewActivities)
                && CollectionUtils.isEmpty(commonActivities)
                && CollectionUtils.isEmpty(categoryActivities)
                && CollectionUtils.isEmpty(couponExposeDTOS)
                && (shopResourcePromotionDO == null || CollectionUtils.isEmpty(shopResourcePromotionDO.getResourceMaterialsDOS()))
                && CollectionUtils.isEmpty(noIssuedMerchantCoupons) && CollectionUtils.isEmpty(issuedMerchantCoupons)
                && (topAdResourcePromotionDO == null || CollectionUtils.isEmpty(topAdResourcePromotionDO.getVoucherDetailDOList()))
                && (magicalMemberTagDO == null || (magicalMemberTagDO.getMagicalMemberTagText() == null && CollectionUtils.isEmpty(magicalMemberTagDO.getMagicalMemberCouponPackageIds()) && CollectionUtils.isEmpty(magicalMemberTagDO.getMagicMemberCouponInfoList())))) {
            return null;
        }

        //分别对3个list进行排序，按金额降序
        if (CollectionUtils.isNotEmpty(commonNewActivities)) {
            Collections.sort(commonNewActivities, new IssueActivityComparator());
        }
        if (CollectionUtils.isNotEmpty(commonActivities)) {
            Collections.sort(commonActivities, new IssueActivityComparator());
        }
        if (CollectionUtils.isNotEmpty(categoryActivities)) {
            Collections.sort(categoryActivities, new IssueActivityComparator());
        }
//        if (CollectionUtils.isNotEmpty(couponExposeDTOS)) {
//            Collections.sort(couponExposeDTOS, new MerchantShareCouponComparator());
//        }


        // 排序后摘出符合条件的外层3个simple
        List<IssueCouponActivity> simpleActivities = Lists.newArrayList();
        for (int i = 0; i <= 3 && simpleActivities.size() <= 3; i++) {
            if (commonNewActivities.size() > i) {
                simpleActivities.add(commonNewActivities.get(i));
            }
            if (commonActivities.size() > i) {
                simpleActivities.add(commonActivities.get(i));
            }
            if (categoryActivities.size() > i) {
                simpleActivities.add(categoryActivities.get(i));
            }
        }
        simpleActivities = simpleActivities.subList(0, Math.min(3, simpleActivities.size()));
        Collections.sort(simpleActivities, new IssueActivityComparator());

        // 先组装外层信息
        IssueCouponSimpleComponentDetail simpleCouponListInfo = new IssueCouponSimpleComponentDetail();
        simpleCouponListInfo.setTitle("优惠券");
        int merchantAccountCouponCount = 0;
        if (merchantAccountCouponPromotion != null) {
            int noIssuedCount = CollectionUtils.isEmpty(merchantAccountCouponPromotion.getLeft()) ? 0 : merchantAccountCouponPromotion.getLeft().size();
            int issuedCount = CollectionUtils.isEmpty(merchantAccountCouponPromotion.getRight()) ? 0 : merchantAccountCouponPromotion.getRight().size();
            merchantAccountCouponCount = noIssuedCount + issuedCount;
        }
        int shopResourcePromotionCount = 0;
        if (shopResourcePromotionDO != null && CollectionUtils.isNotEmpty(shopResourcePromotionDO.getResourceMaterialsDOS())) {
            shopResourcePromotionCount = shopResourcePromotionDO.getResourceMaterialsDOS().size();
        }
        int topAdResourcePromotionCount=0;
        if (topAdResourcePromotionDO != null && CollectionUtils.isNotEmpty(topAdResourcePromotionDO.getVoucherDetailDOList())) {
            topAdResourcePromotionCount = topAdResourcePromotionDO.getVoucherDetailDOList().size();
        }
        simpleCouponListInfo.setSubTitle(commonNewActivities.size() + commonActivities.size() + categoryActivities.size() + couponExposeDTOS.size() + merchantAccountCouponCount + shopResourcePromotionCount + topAdResourcePromotionCount + "张");
        List<IssueCouponSimpleDetail> couponSimpleList = Lists.transform(simpleActivities, new Function<IssueCouponActivity, IssueCouponSimpleDetail>() {
            @Nullable
            @Override
            public IssueCouponSimpleDetail apply(@Nullable IssueCouponActivity input) {
                IssueCouponSimpleDetail issueCouponSimpleDetail = new IssueCouponSimpleDetail();
                if (commonNewActivities.contains(input)) {
                    issueCouponSimpleDetail.setType(0);
                } else if (commonActivities.contains(input)) {
                    issueCouponSimpleDetail.setType(1);
                } else {
                    issueCouponSimpleDetail.setType(2);
                }
                if (!IssueCouponTypeUtils.isIssued(input)) {
                    issueCouponSimpleDetail.setIssueStatus(0);
                } else {
                    issueCouponSimpleDetail.setIssueStatus(1);
                }
                issueCouponSimpleDetail.setAmount(input.getCouponGroup().getDiscountAmount().doubleValue());
                issueCouponSimpleDetail.setPriceLimit(input.getCouponGroup().getPriceLimit().stripTrailingZeros().toPlainString());

                if (input.getCouponGroup().getValueType() == CouponGroupValueType.DISCOUNT.getCode()) {
                    issueCouponSimpleDetail.setCouponValueType(MapiCouponValueType.DISCOUNT_COUPON.getCode());
                    issueCouponSimpleDetail.setDiscount((double) input.getCouponGroup().getDiscountCouponDTO().getDiscount() / 10);
                } else {
                    issueCouponSimpleDetail.setCouponValueType(MapiCouponValueType.COMMON_COUPON.getCode());
                }

                if (commonNewActivities.contains(input)) {
                    issueCouponSimpleDetail.setTitle("新客券");
                } else if (commonActivities.contains(input)) {
                    if (input.getCouponGroup().getPriceLimit() == null || input.getCouponGroup().getPriceLimit().signum() <= 0) {
                        issueCouponSimpleDetail.setTitle("无门槛");
                    } else {
                        issueCouponSimpleDetail.setTitle("满" + input.getCouponGroup().getPriceLimit().stripTrailingZeros().toPlainString());
                    }
                } else if (categoryActivities.contains(input)) {
                    issueCouponSimpleDetail.setTitle("品类券");
                }
                return issueCouponSimpleDetail;
            }
        });
        // 如果有分享券，那遍历couponSimpleList， 在第一张新新客券前插入多张分享券，凑满三张
        int exposeSize = 3 - couponSimpleList.size() > 0 ? 3 - couponSimpleList.size() : 1;
        List<IssueCouponSimpleDetail> couponSimpleListResult = Lists.newArrayList(couponSimpleList);
        if (CollectionUtils.isNotEmpty(couponExposeDTOS)) {
            List<IssueCouponSimpleDetail> simpleDetails = buildExposeCouponDetailList(couponExposeDTOS, exposeSize);
            if (CollectionUtils.isNotEmpty(simpleDetails)) {
                if (CollectionUtils.isEmpty(couponSimpleListResult)) {
                    couponSimpleListResult.addAll(0, simpleDetails);
                } else {
                    for (int i = 0; i < couponSimpleList.size(); i++) {
                        if (couponSimpleList.get(i).getType() != 0) {
                            couponSimpleListResult.addAll(i, simpleDetails);
                            break;
                        }
                    }
                }
            }
        }

        //平台券拼装：当前只会查团购的领券购的券，在这里会过滤掉非商家平台权益券
        int platformCouponSize = 3 - couponSimpleListResult.size() > 0 ? 3 - couponSimpleListResult.size() : 1;
        if (merchantAccountCouponPromotion != null) {
            List<IssueCouponSimpleDetail> simpleDetails = buildPlatformCouponDetailList(merchantAccountCouponPromotion, platformCouponSize, promotionRequestContext.getCouponPageSource());
            if (CollectionUtils.isNotEmpty(simpleDetails)) {
                couponSimpleListResult.addAll(0, simpleDetails);
            }
        }


        // 投放券入口层拼装
        boolean beMMCCoupon = false;
        boolean beDynamicCoupon = false;
        BigDecimal totalAmount = BigDecimal.ZERO;
        List<String> materialIds = Lists.newArrayList();
        List<CouponDetailDTO> couponDetailDTOS = Lists.newArrayList();
        int maxCount = 3;
        if (shopResourcePromotionDO != null && CollectionUtils.isNotEmpty(shopResourcePromotionDO.getResourceMaterialsDOS())) {
            for (ResourceMaterialsDO resourceMaterialsDO : shopResourcePromotionDO.getResourceMaterialsDOS()) {
                if (CollectionUtils.isEmpty(resourceMaterialsDO.getCouponDetailDTOS())) {
                    continue;
                }
                for (CouponDetailDTO couponDetailDTO : resourceMaterialsDO.getCouponDetailDTOS()) {
                    if (couponDetailDTO.getBusinessCouponType() == 2) {
                        beDynamicCoupon = true;
                    }
                    //点评端未灰度不展示神劵标签
                    if (BooleanUtils.isTrue(couponDetailDTO.getGodMemberCoupon()) && couponActivityContext.isMmcGrayControlPaas()) {
                        beMMCCoupon = true;
                    }
                }
                if (StringUtils.isNotBlank(resourceMaterialsDO.getMaterialId())) {
                    materialIds.add(resourceMaterialsDO.getMaterialId());
                }
                totalAmount = totalAmount.add(new BigDecimal(resourceMaterialsDO.getPrizeCouponAmount()));
                couponDetailDTOS.addAll(resourceMaterialsDO.getCouponDetailDTOS());
            }

            String subTitleImg = null;
            Map<String, String> staticMaterialContext = shopResourcePromotionDO.getStaticMaterialContext();
            if (MapUtils.isNotEmpty(staticMaterialContext) && Lion.getBooleanValue(LionConstants.RESOURCE_SUB_TITLE_IMG_SWITCH, true)) {
                subTitleImg = staticMaterialContext.get(CommonConstants.SUB_TITLE_IMG);
            }
            List<IssueCouponSimpleDetail> resourceSimpleDetails = buildResourcePromotionList(couponActivityContext, shopResourcePromotionDO.getDrawStatus(), beDynamicCoupon, totalAmount, couponDetailDTOS, magicalMemberCouponIds, subTitleImg, beMMCCoupon);
            if (CollectionUtils.isNotEmpty(resourceSimpleDetails)) {
                maxCount += resourceSimpleDetails.size();
                couponSimpleListResult.addAll(0, resourceSimpleDetails);
            }
            if (shopResourcePromotionDO.getDisplayBannerFlag() != null && shopResourcePromotionDO.getDisplayBannerFlag()) {
                simpleCouponListInfo.setIssueBannerTitle("新客专享神券");
                simpleCouponListInfo.setDisplayBannerFlag(Boolean.TRUE);
            }
        }

        //top券入口层封装
        if(topAdResourcePromotionDO != null && CollectionUtils.isNotEmpty(topAdResourcePromotionDO.getVoucherDetailDOList())) {
            List<IssueCouponSimpleDetail> topAdResourceSimpleDetails = buildTopAdResourcePromotionList(topAdResourcePromotionDO.getVoucherDetailDOList(), magicalMemberCouponIds);
            if (CollectionUtils.isNotEmpty(topAdResourceSimpleDetails)) {
                maxCount += topAdResourceSimpleDetails.size();
                couponSimpleListResult.addAll(0, topAdResourceSimpleDetails);
            }
        }

        // 神会员券入口层标签封装
        if (magicalMemberTagDO != null && magicalMemberTagDO.getMagicalMemberTagText() != null) {
            IssueCouponSimpleDetail magicalSimpleDetail = buildMagicalMemberTagList(magicalMemberTagDO.getMagicalMemberTagText());
            if (magicalSimpleDetail != null) {
                maxCount++;
                couponSimpleListResult.add(0, magicalSimpleDetail);
            }
        }


        simpleCouponListInfo.setCouponSimpleList(couponSimpleListResult.subList(0, Math.min(maxCount, couponSimpleListResult.size())));
        fullComponentDetail.setSimpleCouponListInfo(simpleCouponListInfo);

        // 组装内层信息
        // 组装
        IssueCouponListDetail shareCoupon = new IssueCouponListDetail();
        shareCoupon.setTitle("商家分享券");
        shareCoupon.setCouponProductType(3);
        if (CollectionUtils.isNotEmpty(couponExposeDTOS)) {
            List<IssueCouponDetail> couponList = Lists.transform(couponExposeDTOS, new Function<MerchantShareCouponExposeDTO, IssueCouponDetail>() {
                @Nullable
                @Override
                public IssueCouponDetail apply(@Nullable MerchantShareCouponExposeDTO input) {
                    return buildIssueCouponDetail(input);
                }
            });
            shareCoupon.setCouponList(couponList);
        }


        IssueCouponComponentDetail couponListInfo = new IssueCouponComponentDetail();
        couponListInfo.setTitle("优惠券");
        List<IssueCouponListDetail> issueCouponList = Lists.newArrayList();
        IssueCouponListDetail commonListDetail = new IssueCouponListDetail();
        commonListDetail.setCouponProductType(0);
        commonListDetail.setTitle("商家通用券");
        List<IssueCouponActivity> commonAllActivities = Lists.newArrayListWithExpectedSize(commonActivities.size() + commonNewActivities.size());
        commonAllActivities.addAll(commonNewActivities);
        commonAllActivities.addAll(commonActivities);
        if (CollectionUtils.isNotEmpty(commonAllActivities)) {
            List<IssueCouponDetail> couponList = Lists.transform(commonAllActivities, new Function<IssueCouponActivity, IssueCouponDetail>() {
                @Nullable
                @Override
                public IssueCouponDetail apply(@Nullable IssueCouponActivity input) {
                    return buildIssueCouponDetail(input, fillLimitTime);
                }
            });
            commonListDetail.setCouponList(couponList);
        } else {
            commonListDetail.setCouponList(Lists.<IssueCouponDetail>newArrayList());
        }

        IssueCouponListDetail categoryListDetail = new IssueCouponListDetail();
        categoryListDetail.setCouponProductType(1);
        categoryListDetail.setTitle("商家品类券");
        if (CollectionUtils.isNotEmpty(categoryActivities)) {
            List<IssueCouponDetail> couponList = Lists.transform(categoryActivities, new Function<IssueCouponActivity, IssueCouponDetail>() {
                @Nullable
                @Override
                public IssueCouponDetail apply(@Nullable IssueCouponActivity input) {
                    return buildIssueCouponDetail(input, fillLimitTime);
                }
            });
            categoryListDetail.setCouponList(couponList);
        } else {
            categoryListDetail.setCouponList(Lists.<IssueCouponDetail>newArrayList());
        }
        IssueCouponListDetail merchantAccountCoupon = buildMerchantAccountCoupon(merchantAccountCouponPromotion, promotionRequestContext.getCouponPageSource());
        IssueCouponListDetail topAdResourceCoupon = buildTopAdResourceCoupon(topAdResourcePromotionDO, magicalMemberCouponIds);
        if(topAdResourceCoupon != null) {
            issueCouponList.add(topAdResourceCoupon);
        }
        if (merchantAccountCoupon != null) {
            issueCouponList.add(merchantAccountCoupon);
        }
        if (CollectionUtils.isNotEmpty(shareCoupon.getCouponList())) {
            issueCouponList.add(shareCoupon);
        }
        if (CollectionUtils.isNotEmpty(commonListDetail.getCouponList())) {
            issueCouponList.add(commonListDetail);
        }
        if (CollectionUtils.isNotEmpty(categoryListDetail.getCouponList())) {
            issueCouponList.add(categoryListDetail);
        }

        //拼装投放券浮层信息
        if (shopResourcePromotionDO != null && CollectionUtils.isNotEmpty(shopResourcePromotionDO.getResourceMaterialsDOS())) {
            ShopResourcePromotionInfo shopResourcePromotionInfo = buildShopResourcePromotionInfo(shopResourcePromotionDO, beDynamicCoupon, totalAmount, couponDetailDTOS, materialIds, magicalMemberCouponIds);
            if (shopResourcePromotionInfo != null) {
                couponListInfo.setShopResourcePromotionInfo(shopResourcePromotionInfo);
            }
        }

        // 拼装神会员可购买券包id信息
        if (magicalMemberTagDO != null && CollectionUtils.isNotEmpty(magicalMemberTagDO.getMagicalMemberCouponPackageIds())) {
            couponListInfo.setMagicalMemberCouponPackageIds(magicalMemberTagDO.getMagicalMemberCouponPackageIds());
        }
        // 拼装神会员用户已有券信息
        if (magicalMemberTagDO != null && CollectionUtils.isNotEmpty(magicalMemberTagDO.getMagicMemberCouponInfoList())) {
            boolean couponAggregated = CouponAggrGrayUtils.hitCouponAggrGrayRule(promotionRequestContext.getUserId());
            boolean renameCouponPackage = hitRenameCouponPackageGray(promotionRequestContext, context);
            DotUtils.dotForMMCAggrAndIconRename(promotionRequestContext, couponAggregated, renameCouponPackage);
            couponListInfo.setMagicMemberCouponInfo(buildMagicMemberCouponInfoList(magicalMemberTagDO.getMagicMemberCouponInfoList(), couponAggregated, renameCouponPackage));
        }
        if (magicalMemberTagDO != null) {
            couponListInfo.setInflateExperimentId(buildInflateExperimentId(magicalMemberTagDO.getExtendedFieldsMap()));
        }
        couponListInfo.setCouponList(issueCouponList);
        fullComponentDetail.setCouponListInfo(couponListInfo);
        //处理ab实验结果
        fillAbResult(fullComponentDetail, couponActivityContext);

        return fullComponentDetail;
    }

    private List<Integer> buildInflateExperimentId(Map<String, String> extendedFieldsMap) {
        if (MapUtils.isEmpty(extendedFieldsMap) || !extendedFieldsMap.containsKey(PromotionPropertyEnum.COMMON_EXPERIMENT_ID_SET.getValue())) {
            return Lists.newArrayList();
        }
        return JSON.parseArray(extendedFieldsMap.get(PromotionPropertyEnum.COMMON_EXPERIMENT_ID_SET.getValue()),
                Integer.class);
    }

    private void fillAbResult(IssueCouponFullComponentDetail fullComponentDetail,
                              CouponActivityContext couponActivityContext) {
        Map<String, AbTestInfo> abResult = couponActivityContext.getAbResult();
        if (MapUtils.isEmpty(abResult)) {
            return;
        }
        List<String> collect = abResult.values().stream().map(AbTestInfo::getModuleAbInfo4Front)
                .collect(Collectors.toList());
        fullComponentDetail.setModuleAbInfo4Front(collect.toString());
        IssueCouponSimpleComponentDetail simpleCouponListInfo = fullComponentDetail.getSimpleCouponListInfo();
        AbTestInfo abTestInfo = abResult.get(AbTestConstant.POI_MMC_ISSUE_COUPON_AB);
        if (abTestInfo != null && AbTestConstant.SHOW_NEW_MMC_TEXT_WITH_ANIME.equals(abTestInfo.getHitStrategy())) {
            simpleCouponListInfo.setShowAnimeType(1);
        }
    }


    private boolean hitRenameCouponPackageGray(PromotionRequestContext promotionRequestContext, IMobileContext context) {
        try {
            MagicBizControlReq request = new MagicBizControlReq();
            request.setBizScene(MagicBizSceneEnum.COUPON_PACKAGE_RENAME.getValue());
            boolean isMiniProgram = promotionRequestContext.isMiniProgram();
            // app端和美小中美发一致性门店版本号从框架IMobileContext里取，美小里美发非一致性门店和其他行业从appVersion中取
            request.setVersion(isMiniProgram ? promotionRequestContext.getVersion() : context.getVersion());
            if (isMiniProgram) {
                request.setClientType(ClientTypeEnum.MT_WE_CHAT_APPLET.getValue());
            } else if (promotionRequestContext.isMt()) {
                request.setClientType(promotionRequestContext.getCPlatform() == 1 ? String.valueOf(ClientTypeEnum.IPHONE.getValue()) : String.valueOf(ClientTypeEnum.ANDROID.getValue()));
            }else {
                request.setClientType(promotionRequestContext.getCPlatform() == 1 ? String.valueOf(ClientTypeEnum.DP_IPHONE.getValue()) : String.valueOf(ClientTypeEnum.DP_ANDROID.getValue()));
            }
            if (promotionRequestContext.isMt()) {
                request.setMtUserId(promotionRequestContext.getUserId());
            } else {
                request.setDpUserId(promotionRequestContext.getUserId());
            }
            MagicBizControlResult result = MagicBizGrayUtil.bizGrayControl(request);
            if (!result.isPass()) {
                log.warn("hitRenameCouponPackageGray not pass, request:{}, failReason: {}", request, result.getFailReason());
                return false;
            }
            return true;
        } catch (Exception e) {
            Cat.logEvent(DotUtils.RENAME_MMC_ICON, "exception");
            log.error("hitRenameCouponPackageGray exception.", e);
        }
        return false;
    }

    private List<MagicMemberCouponInfo> buildMagicMemberCouponInfoList(List<MagicalMemberCouponDTO> magicMemberCouponInfoRawList, boolean couponAggregated, boolean renameCouponPackage) {
        if (couponAggregated) {
            return buildAggrMagicMemberCouponInfoListAndSort(magicMemberCouponInfoRawList, renameCouponPackage);
        } else {
            Map<String, MagicalMemberCouponCountDTO> mergeMap = new HashMap<>();
            List<MagicalMemberCouponDTO> restCoupons = new ArrayList<>();
            mergeMagicalMemberCouponDTOMap(magicMemberCouponInfoRawList, mergeMap, restCoupons);
            return buildMagicMemberCouponInfoListAndSort(mergeMap, restCoupons, renameCouponPackage);
        }
    }

    private List<MagicMemberCouponInfo> buildAggrMagicMemberCouponInfoListAndSort(List<MagicalMemberCouponDTO> magicMemberCouponInfoRawList, boolean renameCouponPackage) {
        List<MagicMemberCouponInfo> aggrMagicMemberCouponInfoList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(magicMemberCouponInfoRawList)) {
            for (MagicalMemberCouponDTO magicalMemberCouponDTO : magicMemberCouponInfoRawList) {
                MagicMemberCouponInfo aggrMagicMemberCouponInfo = buildMagicMemberCouponInfo(magicalMemberCouponDTO, renameCouponPackage);
                if (aggrMagicMemberCouponInfo == null) {
                    continue;
                }
                if (magicalMemberCouponDTO.getCouponAggregateNum() != 0) {
                    aggrMagicMemberCouponInfo.setCouponNum(magicalMemberCouponDTO.getCouponAggregateNum());
                }
                aggrMagicMemberCouponInfoList.add(aggrMagicMemberCouponInfo);
            }
        }

        // 排序
        Collections.sort(aggrMagicMemberCouponInfoList, new MagicalMemberCouponComparator());

        for (MagicMemberCouponInfo aggrMagicMemberCouponInfo : aggrMagicMemberCouponInfoList) {
            // 加工券到期时间
            fillValidTimeInfo(new Date(Long.parseLong(aggrMagicMemberCouponInfo.getValidTime())), aggrMagicMemberCouponInfo);

            // 加工券门槛描述
            if (new BigDecimal(aggrMagicMemberCouponInfo.getThresholdDesc()).compareTo(BigDecimal.ZERO) == 0) {
                aggrMagicMemberCouponInfo.setThresholdDesc("无门槛");
            } else {
                aggrMagicMemberCouponInfo.setThresholdDesc("满" + new BigDecimal(aggrMagicMemberCouponInfo.getThresholdDesc()).divide(new BigDecimal(100), 2, BigDecimal.ROUND_HALF_UP).stripTrailingZeros().toPlainString() + "元可用");
            }
        }
        return aggrMagicMemberCouponInfoList;
    }


    private void mergeMagicalMemberCouponDTOMap(List<MagicalMemberCouponDTO> magicalMemberCouponDTOS, Map<String, MagicalMemberCouponCountDTO> map, List<MagicalMemberCouponDTO> restCoupons) {
        for (MagicalMemberCouponDTO couponDTO : magicalMemberCouponDTOS) {
            // 不聚合领塞、膨后的神券
            if (couponDTO.getMmcCategory() == MmcCategoryValueEnum.MMC.getCode() || couponDTO.getInflateStatus() == HAS_INFLATED) {
                restCoupons.add(couponDTO);
                continue;
            }
            // 聚合券批次+门槛+金额+到期时间一样的神券
            String key = couponDTO.getCouponGroupId() + couponDTO.getThreshold() + couponDTO.getCouponAmount() + couponDTO.getCouponEndTime().toString();
            if (map.containsKey(key)) {
                map.get(key).setCount(map.get(key).getCount() + 1);
            } else {
                MagicalMemberCouponCountDTO magicalMemberCouponCountDTO = new MagicalMemberCouponCountDTO();
                magicalMemberCouponCountDTO.setMagicalMemberCouponDTO(couponDTO);
                magicalMemberCouponCountDTO.setCount(1);
                map.put(key, magicalMemberCouponCountDTO);
            }
        }
    }

    private List<MagicMemberCouponInfo> buildMagicMemberCouponInfoListAndSort(Map<String, MagicalMemberCouponCountDTO> map, List<MagicalMemberCouponDTO> restCoupons, boolean renameCouponPackage) {
        List<MagicMemberCouponInfo> magicMemberCouponInfoList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(restCoupons)) {
            for (MagicalMemberCouponDTO magicalMemberCouponDTO : restCoupons) {
                MagicMemberCouponInfo magicMemberCouponInfo = buildMagicMemberCouponInfo(magicalMemberCouponDTO, renameCouponPackage);
                if (magicMemberCouponInfo == null) {
                    continue;
                }
                magicMemberCouponInfoList.add(magicMemberCouponInfo);
            }
        }
        if (org.apache.commons.collections4.MapUtils.isNotEmpty(map)) {
            for (MagicalMemberCouponCountDTO magicalMemberCouponCountDTO : map.values()) {
                MagicMemberCouponInfo magicMemberCouponInfo = buildMagicMemberCouponInfo(magicalMemberCouponCountDTO.getMagicalMemberCouponDTO(), renameCouponPackage);
                if (magicMemberCouponInfo == null) {
                    continue;
                }
                magicMemberCouponInfo.setCouponNum(magicalMemberCouponCountDTO.getCount());
                magicMemberCouponInfoList.add(magicMemberCouponInfo);
            }
        }

        // 排序
        Collections.sort(magicMemberCouponInfoList, new MagicalMemberCouponComparator());

        for (MagicMemberCouponInfo magicMemberCouponInfo : magicMemberCouponInfoList) {
            // 加工券到期时间
            fillValidTimeInfo(new Date(Long.parseLong(magicMemberCouponInfo.getValidTime())), magicMemberCouponInfo);

            // 加工券门槛描述
            if (new BigDecimal(magicMemberCouponInfo.getThresholdDesc()).compareTo(BigDecimal.ZERO) == 0) {
                magicMemberCouponInfo.setThresholdDesc("无门槛");
            } else {
                magicMemberCouponInfo.setThresholdDesc("满" + new BigDecimal(magicMemberCouponInfo.getThresholdDesc()).divide(new BigDecimal(100), 2, BigDecimal.ROUND_HALF_UP).stripTrailingZeros().toPlainString() + "元可用");
            }
        }

        return magicMemberCouponInfoList;
    }

    private MagicMemberCouponInfo buildMagicMemberCouponInfo(MagicalMemberCouponDTO magicalMemberCouponDTO, boolean renameCouponPackage) {
        MagicMemberCouponInfo magicMemberCouponInfo = new MagicMemberCouponInfo();
        magicMemberCouponInfo.setAssetType(magicalMemberCouponDTO.getAssetType());
        if (magicalMemberCouponDTO.getAssetType() == DAO_JIA_COUPON) {
            magicMemberCouponInfo.setCouponCode(magicalMemberCouponDTO.getTspCouponId());
            magicMemberCouponInfo.setApplyId(magicalMemberCouponDTO.getTspCouponGroupId());
        } else if (magicalMemberCouponDTO.getAssetType() == DAO_DIAN_COUPON) {
            magicMemberCouponInfo.setCouponCode(magicalMemberCouponDTO.getCouponId());
            magicMemberCouponInfo.setApplyId(magicalMemberCouponDTO.getCouponGroupId());
        } else {
            return null;
        }
        if (magicalMemberCouponDTO.getInflateStatus() == NOT_YET_INFLATED) {
            magicMemberCouponInfo.setOriginalReduceAmount(magicalMemberCouponDTO.getCouponAmount());
            magicMemberCouponInfo.setOriginalRequiredAmount(magicalMemberCouponDTO.getThreshold());
        }
        magicMemberCouponInfo.setValidTime(String.valueOf(magicalMemberCouponDTO.getCouponEndTime()));  // 券到期时间还需加工
        if (StringUtils.isNotBlank(magicalMemberCouponDTO.getMaxInflateAmount())) {
            magicMemberCouponInfo.setCouponDesc("最高膨至" + new BigDecimal(magicalMemberCouponDTO.getMaxInflateAmount()).divide(BigDecimal.valueOf(100), 0, BigDecimal.ROUND_HALF_UP).stripTrailingZeros().toPlainString());
        }
        magicMemberCouponInfo.setThresholdDesc(magicalMemberCouponDTO.getThreshold());  // 券门槛描述还需加工
        magicMemberCouponInfo.setCouponName(magicalMemberCouponDTO.getCouponName());
        magicMemberCouponInfo.setCouponAmount(new BigDecimal(magicalMemberCouponDTO.getCouponAmount()).divide(BigDecimal.valueOf(100), 2, BigDecimal.ROUND_HALF_UP).stripTrailingZeros().toPlainString());
        if (magicalMemberCouponDTO.getMmcCategory()== MmcCategoryValueEnum.MMC.getCode()) {
            // 展示神券标签
            magicMemberCouponInfo.setLogoIconUrl(Lion.getString(LionConstants.APP_KEY, LionConstants.FREE_MMC_VALID_ICON));
            magicMemberCouponInfo.setPaidCoupon(FREE_MMC);
            // 新神券标签
            if (LionQueryUtils.POI_RENAME_FREE_MMC_ICON_SWITCH) {
                IconPicInfo newIconPicInfo = new IconPicInfo();
                newIconPicInfo.setIconPic(LionQueryUtils.FREE_MMC_NEW_ICON_PIC_URL);
                newIconPicInfo.setIconPicHeight(LionQueryUtils.FREE_MMC_NEW_ICON_PIC_HEIGHT);
                newIconPicInfo.setIconPicWidth(LionQueryUtils.FREE_MMC_NEW_ICON_PIC_WIDTH);
                magicMemberCouponInfo.setIconPicInfo(JsonUtils.toJSONString(newIconPicInfo));
            }
        } else if (magicalMemberCouponDTO.getMmcCategory() == MmcCategoryValueEnum.MMC_BUY.getCode()) {
            // 展示神券省钱包标签
            magicMemberCouponInfo.setLogoIconUrl(Lion.getString(LionConstants.APP_KEY, LionConstants.PAID_MMC_VALID_ICON));
            magicMemberCouponInfo.setPaidCoupon(PAID_MMC);
            // 新神券省钱包标签
            if (LionQueryUtils.POI_RENAME_MMC_ICON_SWITCH && renameCouponPackage) {
                IconPicInfo newIconPicInfo = new IconPicInfo();
                newIconPicInfo.setIconPic(LionQueryUtils.BUY_MMC_NEW_ICON_PIC_URL);
                newIconPicInfo.setIconPicHeight(LionQueryUtils.BUY_MMC_NEW_ICON_PIC_HEIGHT);
                newIconPicInfo.setIconPicWidth(LionQueryUtils.BUY_MMC_NEW_ICON_PIC_WIDTH);
                magicMemberCouponInfo.setIconPicInfo(JsonUtils.toJSONString(newIconPicInfo));
            }
        } else {
            return null;
        }
        magicMemberCouponInfo.setCouponNum(1);
        magicMemberCouponInfo.setCouponButtonText(couponButtonText);
        magicMemberCouponInfo.setCouponButtonType(GO_TO_INFLATE);
        magicMemberCouponInfo.setAvailable(AVAILABLE_COUPON);
        magicMemberCouponInfo.setCanInflate(BooleanUtils.isTrue(magicalMemberCouponDTO.isCanInflate()) ? CAN_INFLATE : CAN_NOT_INFLATE);
        magicMemberCouponInfo.setInflatedStatus(magicalMemberCouponDTO.getInflateStatus());
        magicMemberCouponInfo.setValidStatus(VALID_COUPON_STATUS);
        magicMemberCouponInfo.setBizToken(magicalMemberCouponDTO.getBizToken());
        return magicMemberCouponInfo;
    }

    public void fillValidTimeInfo(Date endDate, MagicMemberCouponInfo magicalMemberCouponData) {
        if(endDate == null){
            return;
        }
        DateTime endDateTime = new DateTime(endDate);
        DateTime now = new DateTime();
        // 只会存在未过期的券
        if (now.getMillis() < endDateTime.getMillis()) {
            if (now.getYear() == endDateTime.getYear() && now.getDayOfYear() == endDateTime.getDayOfYear()) {
                magicalMemberCouponData.setCouponValidTimeText("今日到期，仅剩 ");
                magicalMemberCouponData.setValidTime(String.valueOf(endDateTime.getMillis()));
                return;
            }
            magicalMemberCouponData.setCouponValidTimeText(DateTimeUtils.format(endDate, FORMAT_SMALL) + " 到期");
            magicalMemberCouponData.setValidTime(null);
        }
    }


    private List<String> buildUserHoldMagicalMemberCouponIds(MagicalMemberTagRawDO magicalMemberTagDO) {
        if (magicalMemberTagDO == null || CollectionUtils.isEmpty(magicalMemberTagDO.getMagicMemberCouponInfoList())) {
            return null;
        }
        List<String> magicalMemberCouponIds = new ArrayList<>();
        for (MagicalMemberCouponDTO couponDTO : magicalMemberTagDO.getMagicMemberCouponInfoList()) {
            if (couponDTO != null && StringUtils.isNotBlank(couponDTO.getCouponId())) {
                magicalMemberCouponIds.add(couponDTO.getCouponId());
            }
        }
        return magicalMemberCouponIds;
    }

    private IssueCouponListDetail buildMerchantAccountCoupon(org.apache.commons.lang3.tuple.Pair<List<CouponDesc>, List<CouponDesc>> merchantAccountCouponPromotion, String pageSource) {
        if (merchantAccountCouponPromotion == null) {
            return null;
        }
        List<CouponDesc> noIssued = merchantAccountCouponPromotion.getLeft();
        List<CouponDesc> issued = merchantAccountCouponPromotion.getRight();
        List<CouponDesc> allCoupon = Lists.newArrayList();
        if (CollectionUtils.isNotEmpty(noIssued)) {
            allCoupon.addAll(noIssued);
        }
        if (CollectionUtils.isNotEmpty(issued)) {
            allCoupon.addAll(issued);
        }
        if (CollectionUtils.isEmpty(allCoupon)) {
            return null;
        }
        IssueCouponListDetail merchantAccountCoupon = new IssueCouponListDetail();
        int couponProductType = (PageSourceEnum.BeautyMedicalPOIDetail.code.equals(pageSource) && !LionQueryUtils.PREPAY_POI_COUPON_PRODUCT_TYPE_SWITCH) ? 5 : 4;
        merchantAccountCoupon.setCouponProductType(couponProductType);
        merchantAccountCoupon.setTitle(PageSourceEnum.BeautyMedicalPOIDetail.code.equals(pageSource) ? "平台优惠券" : "神券");
        List<IssueCouponDetail> couponList = Lists.transform(allCoupon, new Function<CouponDesc, IssueCouponDetail>() {
            @Nullable
            @Override
            public IssueCouponDetail apply(@Nullable CouponDesc input) {
                return buildIssueCouponDetailFromCouponDesc(input, noIssued, couponProductType);
            }
        });
        merchantAccountCoupon.setCouponList(couponList);
        return merchantAccountCoupon;
    }

    public IssueCouponListDetail buildTopAdResourceCoupon(TopAdResourcePromotionDO topAdResourcePromotionDO, List<String> magicalMemberCouponIds) {
        if (topAdResourcePromotionDO == null || CollectionUtils.isEmpty(topAdResourcePromotionDO.getVoucherDetailDOList())) {
            return null;
        }
        List<IssueCouponDetail> couponList = Lists.newArrayList();
        for (VoucherDetailDO couponDetailDTO : topAdResourcePromotionDO.getVoucherDetailDOList()) {
            // 过滤用户已有神券
            if (CollectionUtils.isNotEmpty(magicalMemberCouponIds) && StringUtils.isNotBlank(couponDetailDTO.getCouponId()) && magicalMemberCouponIds.contains(couponDetailDTO.getCouponId())) {
                continue;
            }

            IssueCouponDetail issueCouponDetail = buildIssueCouponDetailFromCouponDesc(couponDetailDTO);
            if(issueCouponDetail != null) {
                couponList.add(issueCouponDetail);
            }
        }
        if(CollectionUtils.isEmpty(couponList)) {
            return null;
        }
        IssueCouponListDetail topAdResourceCoupon = new IssueCouponListDetail();
        topAdResourceCoupon.setCouponProductType(6);
        topAdResourceCoupon.setTitle("神券");
        topAdResourceCoupon.setCouponList(couponList);
        return topAdResourceCoupon;
    }

    private IssueCouponDetail buildIssueCouponDetailFromCouponDesc(CouponDesc couponDesc, List<CouponDesc> noIssued, int couponProductType) {
        if (couponDesc == null) {
            return null;
        }
        IssueCouponDetail issueCouponDetail = new IssueCouponDetail();
        issueCouponDetail.setCouponValueType(MapiCouponValueType.COMMON_COUPON.getCode());
        issueCouponDetail.setAmount(couponDesc.getAmount().doubleValue());
        issueCouponDetail.setPriceLimit(couponDesc.getRequireAmount().doubleValue());
        issueCouponDetail.setAmountDesc(IssueCouponUtils.formatPriceLimitDesc(couponDesc.getRequireAmount()));
        issueCouponDetail.setCouponProductType(couponProductType);
        issueCouponDetail.setTitle(couponDesc.getName());
        issueCouponDetail.setScript("神券");
        issueCouponDetail.setUnifiedCouponGroupId(couponDesc.getCouponGroupId());
        IssueCouponStatus issueCouponStatus = new IssueCouponStatus();
        if (CollectionUtils.isNotEmpty(noIssued) && noIssued.contains(couponDesc)) {
            issueCouponStatus.setStatus(0);
            issueCouponStatus.setComment("领券");
        }else{
            issueCouponStatus.setStatus(1);
            issueCouponStatus.setComment("已领取");
        }
        issueCouponDetail.setIssueStatus(issueCouponStatus);
        //和投放券都用神券的角标icon
        String cornerMarkUrl = Lion.getString(LionConstants.APP_KEY, LionConstants.RESOURCE_CORNER_MARK_URL);
        issueCouponDetail.setCornerMarkUrl(cornerMarkUrl);
        issueCouponDetail.setBeginTime(couponDesc.getStartTime().getTime());
        issueCouponDetail.setEndTime(couponDesc.getEndTime().getTime());
        //当前商家权益券都是有选单规则的，所以直接写死该规则
        issueCouponDetail.setCategoryDesc("指定商品可用");
        return issueCouponDetail;
    }

    public IssueCouponDetail buildIssueCouponDetailFromCouponDesc(VoucherDetailDO couponDetailDTO) {
        Cat.logEvent("INVALID_METHOD_1", "com.dianping.pay.api.biz.activity.UnifiedIssueCouponBiz.buildIssueCouponDetailFromCouponDesc(com.dianping.pay.api.beans.VoucherDetailDO)");
        IssueCouponDetail issueCouponDetail = new IssueCouponDetail();
        if(couponDetailDTO.getVoucherValueType() == 0) {
            issueCouponDetail.setCouponValueType(MapiCouponValueType.COMMON_COUPON.getCode());
        } else if(couponDetailDTO.getVoucherValueType() == 2) {
            issueCouponDetail.setCouponValueType(MapiCouponValueType.DISCOUNT_COUPON.getCode());
        }
        issueCouponDetail.setAmount(couponDetailDTO.getPriceAmount());
        issueCouponDetail.setPriceLimit(couponDetailDTO.getPriceLimit());
        issueCouponDetail.setAmountDesc(IssueCouponUtils.formatPriceLimitDesc(new BigDecimal(couponDetailDTO.getPriceLimit())));
        issueCouponDetail.setCouponProductType(6);
        issueCouponDetail.setTitle(couponDetailDTO.getTitle());
        issueCouponDetail.setScript("神券");
        IssueCouponStatus issueCouponStatus = new IssueCouponStatus();
        issueCouponStatus.setStatus(1);
        issueCouponStatus.setComment("已领取");
        issueCouponDetail.setIssueStatus(issueCouponStatus);
        //和投放券都用神券的角标icon
        String cornerMarkUrl = Lion.getString(LionConstants.APP_KEY, LionConstants.RESOURCE_CORNER_MARK_URL);
        issueCouponDetail.setCornerMarkUrl(cornerMarkUrl);
        issueCouponDetail.setBeginTime(couponDetailDTO.getUseBeginTime());
        issueCouponDetail.setEndTime(couponDetailDTO.getUseEndTime());
        return issueCouponDetail;
    }

    private List<IssueCouponSimpleDetail> buildPlatformCouponDetailList(org.apache.commons.lang3.tuple.Pair<List<CouponDesc>, List<CouponDesc>> platformCouponPromotion, int platformCouponSize, String pageSource) {
        List<CouponDesc> noIssuedCouponDescs = platformCouponPromotion.getLeft();
        List<CouponDesc> issuedCouponDescs = platformCouponPromotion.getRight();
        List<CouponDesc> couponDescs = Lists.newArrayList();
        if (CollectionUtils.isNotEmpty(noIssuedCouponDescs)) {
            couponDescs.addAll(noIssuedCouponDescs);
        }
        if (CollectionUtils.isNotEmpty(issuedCouponDescs)) {
            couponDescs.addAll(issuedCouponDescs);
        }
        if (CollectionUtils.isEmpty(couponDescs)) {
            return Lists.newArrayList();
        }
        //先排序
        Collections.sort(couponDescs, (c1, c2) -> {
            if (c1.getAmount().compareTo(c2.getAmount()) != 0) {
                return c2.getAmount().compareTo(c1.getAmount());
            }
            return c1.getRequireAmount().compareTo(c2.getRequireAmount());
        });
        couponDescs = couponDescs.subList(0, platformCouponSize > couponDescs.size() ? couponDescs.size() : platformCouponSize);
        return couponDescs.stream().map(item -> {
            IssueCouponSimpleDetail simpleDetail = new IssueCouponSimpleDetail();
            if (PageSourceEnum.BeautyMedicalPOIDetail.code.equals(pageSource)) {
                //医美神券=所有领券购
                simpleDetail.setTitle(item.getAmount().stripTrailingZeros().toPlainString() + "元医美券");
                simpleDetail.setType(8);
            }else{
                //团购神券样式只针对商家权益券,商家权益券是领券购
                if (item.getRequireAmount().compareTo(BigDecimal.ZERO) == 0) {
                    simpleDetail.setTitle("无门槛减" + item.getAmount().stripTrailingZeros().toPlainString());
                } else {
                    simpleDetail.setTitle("满" + item.getRequireAmount().stripTrailingZeros().toPlainString() + "减" + item.getAmount().stripTrailingZeros().toPlainString());
                }
                simpleDetail.setType(7);
            }
            simpleDetail.setAmount(item.getAmount().doubleValue());
            simpleDetail.setPriceLimit(item.getRequireAmount().stripTrailingZeros().toPlainString());
            if (CollectionUtils.isNotEmpty(issuedCouponDescs) && issuedCouponDescs.contains(item)) {
                simpleDetail.setIssueStatus(1);
            } else {
                simpleDetail.setIssueStatus(0);
            }
            simpleDetail.setBeginTime(item.getStartTime().getTime());
            simpleDetail.setEndTime(item.getEndTime().getTime());
            return simpleDetail;
        }).collect(Collectors.toList());

    }

    private ShopResourcePromotionInfo buildShopResourcePromotionInfo(ShopResourcePromotionDO shopResourcePromotionDO, boolean beDynamicCoupon, BigDecimal totalAmount, List<CouponDetailDTO> couponDetailDTOS, List<String> materialIds, List<String> magicalMemberCouponIds) {
        if (CollectionUtils.isEmpty(couponDetailDTOS)) {
            return null;
        }
        ShopResourcePromotionInfo shopResourcePromotionInfo = new ShopResourcePromotionInfo();
        shopResourcePromotionInfo.setCouponType(beDynamicCoupon ? 1 : 0);
        shopResourcePromotionInfo.setActivityId(shopResourcePromotionDO.getActivityId());
        shopResourcePromotionInfo.setFlowId(shopResourcePromotionDO.getFlowId());
        shopResourcePromotionInfo.setIssueStatus(shopResourcePromotionDO.getDrawStatus() == CommonConstants.CAN_DRAW ? 0 : 1);
        shopResourcePromotionInfo.setMaxAmount(totalAmount.stripTrailingZeros().toPlainString());
        shopResourcePromotionInfo.setResourceLocationId(shopResourcePromotionDO.getResourceLocationId());
        shopResourcePromotionInfo.setRowKey(shopResourcePromotionDO.getRowKey());
        if (CollectionUtils.isNotEmpty(materialIds)) {
            shopResourcePromotionInfo.setMaterialId(StringUtils.join(materialIds, ","));
        }
        if (CollectionUtils.isNotEmpty(shopResourcePromotionDO.getResourceMaterialsDOS())) {
            shopResourcePromotionInfo.setPersonTag(shopResourcePromotionDO.getResourceMaterialsDOS().get(0).getCouponUserTagDescription());
        }
        Map<String, String> staticMaterialContext = shopResourcePromotionDO.getStaticMaterialContext();
        if (MapUtils.isNotEmpty(staticMaterialContext)) {
            if (staticMaterialContext.containsKey(CommonConstants.SEND_COUPON_TYPE)) {
                String sendCouponType = staticMaterialContext.get(CommonConstants.SEND_COUPON_TYPE);
                if (StringUtils.equals(sendCouponType, CommonConstants.POPUP_MODULE)) {
                    shopResourcePromotionInfo.setPopUp(1);
                } else {
                    shopResourcePromotionInfo.setPopUp(0);
                }
            }
            String waitDrawImg = staticMaterialContext.get(CommonConstants.WAIT_DRAW_IMG);
            String drawSingleCouponImg = staticMaterialContext.get(CommonConstants.DRAW_SINGLE_COUPON_IMG);
            String drawMultiCouponsImg = staticMaterialContext.get(CommonConstants.DRAW_MULTI_COUPONS_IMG);
            shopResourcePromotionInfo.setWaitDrawImg(waitDrawImg == null?StringUtils.EMPTY:waitDrawImg);
            shopResourcePromotionInfo.setDrawSingleCouponImg(drawSingleCouponImg == null?StringUtils.EMPTY:drawSingleCouponImg);
            shopResourcePromotionInfo.setDrawMultiCouponsImg(drawMultiCouponsImg == null?StringUtils.EMPTY:drawMultiCouponsImg);
        }
        List<ExposureResourceCouponInfo> resourceCouponInfos = Lists.newArrayList();
        for (CouponDetailDTO couponDetailDTO : couponDetailDTOS) {
            ExposureResourceCouponInfo exposureResourceCouponInfo = new ExposureResourceCouponInfo();
            exposureResourceCouponInfo.setAmount(couponDetailDTO.getAmountPrice());
            exposureResourceCouponInfo.setCouponName(couponDetailDTO.getCouponGroupName());
            if (couponDetailDTO.getUseEndTime() != null) {
                exposureResourceCouponInfo.setEndTime(couponDetailDTO.getUseEndTime());
            }
            if (couponDetailDTO.getUseBeginTime() != null) {
                exposureResourceCouponInfo.setBeginTime(couponDetailDTO.getUseBeginTime());
            }
            exposureResourceCouponInfo.setPriceLimit(couponDetailDTO.getLimitPrice());
            if (BooleanUtils.isTrue(couponDetailDTO.getGodMemberCoupon())) {
                exposureResourceCouponInfo.setCornerMarkUrl(Lion.getString(LionConstants.APP_KEY, LionConstants.FREE_MMC_VALID_ICON));
                // 新神券标签
                if (LionQueryUtils.POI_RENAME_FREE_MMC_ICON_SWITCH) {
                    IconPicInfo newIconPicInfo = new IconPicInfo();
                    newIconPicInfo.setIconPic(LionQueryUtils.FREE_MMC_NEW_ICON_PIC_URL);
                    newIconPicInfo.setIconPicHeight(LionQueryUtils.FREE_MMC_NEW_ICON_PIC_HEIGHT);
                    newIconPicInfo.setIconPicWidth(LionQueryUtils.FREE_MMC_NEW_ICON_PIC_WIDTH);
                    exposureResourceCouponInfo.setIconPicInfo(JsonUtils.toJSONString(newIconPicInfo));
                }
            } else {
                exposureResourceCouponInfo.setCornerMarkUrl(Lion.getString(LionConstants.APP_KEY, LionConstants.RESOURCE_CORNER_MARK_URL));
            }
            if (couponDetailDTO.getLimitPrice() == null || BigDecimal.ZERO.compareTo(new BigDecimal(couponDetailDTO.getLimitPrice())) == 0) {
                exposureResourceCouponInfo.setPriceLimitDesc("无门槛");
            } else {
                exposureResourceCouponInfo.setPriceLimitDesc("满" + couponDetailDTO.getLimitPrice() + "元可用");
            }

            // 隐藏用户已有神券
            if (CollectionUtils.isNotEmpty(magicalMemberCouponIds) && StringUtils.isNotBlank(couponDetailDTO.getCouponId()) && magicalMemberCouponIds.contains(couponDetailDTO.getCouponId())) {
                exposureResourceCouponInfo.setNeedHideCoupon(needHide);
            } else {
                exposureResourceCouponInfo.setNeedHideCoupon(notNeedHide);
            }

            if (BooleanUtils.isTrue(couponDetailDTO.getGodMemberCoupon())) {
                exposureResourceCouponInfo.setMmcCoupon(magicalMemberCoupon);
            }

            resourceCouponInfos.add(exposureResourceCouponInfo);
        }
        if (CollectionUtils.isEmpty(resourceCouponInfos)) {
            return null;
        }
        resourceCouponInfos.sort(new ExposureResourceCouponInfoComparator());
        shopResourcePromotionInfo.setResourceCouponInfos(resourceCouponInfos);
        return shopResourcePromotionInfo;
    }

    private List<IssueCouponSimpleDetail> buildResourcePromotionList(CouponActivityContext couponActivityContext, Integer drawStatus, boolean beDynamicCoupon, BigDecimal totalAmount, List<CouponDetailDTO> couponDetailDTOS, List<String> magicalMemberCouponIds, String subTitleImg, boolean beMMCCoupon) {
        List<IssueCouponSimpleDetail> resourceSimpleDetails = Lists.newArrayList();
        // 适应前端字体样式改版，需要在金额前后各加一个空格
        if (drawStatus == CommonConstants.CAN_DRAW) {
            if (beDynamicCoupon) {
                String title = couponDetailDTOS.size() > 1 ?
                        "最高减 " + totalAmount.stripTrailingZeros().toPlainString() + " 元券包" :
                        "拼手气最高减 " + totalAmount.stripTrailingZeros().toPlainString() + " 元";
                IssueCouponSimpleDetail simpleDetail = new IssueCouponSimpleDetail();
                simpleDetail.setIssueStatus(0);
                simpleDetail.setType(6);
                simpleDetail.setTitle(title);
                simpleDetail.setSubTitleImg(subTitleImg);
                if (beMMCCoupon) {
                    simpleDetail.setMmcCoupon(magicalMemberCoupon);
                }
                simpleDetail.setTotalAmount(totalAmount.stripTrailingZeros().toPlainString());
                resourceSimpleDetails.add(simpleDetail);
            } else {
                for (CouponDetailDTO couponDetailDTO : couponDetailDTOS) {
                    IssueCouponSimpleDetail simpleDetail = new IssueCouponSimpleDetail();
                    simpleDetail.setIssueStatus(0);
                    simpleDetail.setType(6);
                    simpleDetail.setAmount(Double.parseDouble(couponDetailDTO.getAmountPrice()));
                    simpleDetail.setPriceLimit(couponDetailDTO.getLimitPrice());
                    String title;
                    if (couponDetailDTO.getLimitPrice() == null || BigDecimal.ZERO.compareTo(new BigDecimal(couponDetailDTO.getLimitPrice())) == 0) {
                        title = couponDetailDTO.getAmountPrice() + " 元无门槛券";
                    } else {
                        title = "满 " + couponDetailDTO.getLimitPrice() + " 减 " + couponDetailDTO.getAmountPrice() + " 券";
                    }
                    simpleDetail.setTitle(title);
                    simpleDetail.setSubTitleImg(subTitleImg);
                    if (BooleanUtils.isTrue(couponDetailDTO.getGodMemberCoupon()) && couponActivityContext.isMmcGrayControlPaas()) {
                        simpleDetail.setMmcCoupon(magicalMemberCoupon);
                    }
                    simpleDetail.setTotalAmount(totalAmount.stripTrailingZeros().toPlainString());
                    resourceSimpleDetails.add(simpleDetail);
                }
                resourceSimpleDetails.sort(new IssueCouponSimpleDetailComparator());
            }
        }
        if (drawStatus == CommonConstants.DRAWED) {
            for (CouponDetailDTO couponDetailDTO : couponDetailDTOS) {
                // 过滤神会员神券，神会员神券在banner位统一展示成标签
                if (CollectionUtils.isNotEmpty(magicalMemberCouponIds) && StringUtils.isNotBlank(couponDetailDTO.getCouponId()) && magicalMemberCouponIds.contains(couponDetailDTO.getCouponId())) {
                    continue;
                }
                IssueCouponSimpleDetail simpleDetail = new IssueCouponSimpleDetail();
                simpleDetail.setIssueStatus(1);
                simpleDetail.setType(6);
                simpleDetail.setAmount(Double.parseDouble(couponDetailDTO.getAmountPrice()));
                simpleDetail.setPriceLimit(couponDetailDTO.getLimitPrice());
                String title;
                if (couponDetailDTO.getLimitPrice() == null || BigDecimal.ZERO.compareTo(new BigDecimal(couponDetailDTO.getLimitPrice())) == 0) {
                    title = couponDetailDTO.getAmountPrice() + " 元无门槛券";
                } else {
                    title = "满 " + couponDetailDTO.getLimitPrice() + " 减 " + couponDetailDTO.getAmountPrice() + " 券";
                }
                simpleDetail.setTitle(title);
                if (couponDetailDTO.getUseEndTime() != null) {
                    simpleDetail.setEndTime(couponDetailDTO.getUseEndTime());
                }
                if (couponDetailDTO.getUseBeginTime() != null) {
                    simpleDetail.setBeginTime(couponDetailDTO.getUseBeginTime());
                }
                simpleDetail.setSubTitleImg(subTitleImg);
                if (BooleanUtils.isTrue(couponDetailDTO.getGodMemberCoupon()) &&
                        couponActivityContext.isMmcGrayControlPaas()) {
                    simpleDetail.setMmcCoupon(magicalMemberCoupon);
                }
                resourceSimpleDetails.add(simpleDetail);
            }
            if (CollectionUtils.isEmpty(resourceSimpleDetails)) {
                return null;
            }
            resourceSimpleDetails.sort(new IssueCouponSimpleDetailComparator());
        }
        return resourceSimpleDetails;
    }

    private IssueCouponSimpleDetail buildMagicalMemberTagList(MagicalMemberTagTextDTO magicalMemberTagText) {
        if (magicalMemberTagText.getMagicalMemberCouponTag() == null) {
            return null;
        }
        IssueCouponSimpleDetail simpleDetail = new IssueCouponSimpleDetail();
        simpleDetail.setType(11);
        simpleDetail.setInflateShowText(magicalMemberTagText.getInflateShowText());
        simpleDetail.setReduceMoney(magicalMemberTagText.getReduceMoney());
        if (StringUtils.isBlank(magicalMemberTagText.getStatus())) {
            simpleDetail.setMagicalMemberCouponStatus(StringUtils.EMPTY);
        } else {
            simpleDetail.setMagicalMemberCouponStatus(magicalMemberTagText.getStatus());
        }
        MagicalMemberTagShowTypeEnum showTypeEnum = MagicalMemberTagShowTypeEnum.findByValue(magicalMemberTagText.getShowType());
        if (showTypeEnum == null) {
            return null;
        }
        simpleDetail.setMagicalMemberTagShowType(showTypeEnum.getCode());
        simpleDetail.setAmount(Double.parseDouble(magicalMemberTagText.getReduceMoney()));
        return simpleDetail;
    }

    public List<IssueCouponSimpleDetail> buildTopAdResourcePromotionList(List<VoucherDetailDO> voucherDetailDOList, List<String> magicalMemberCouponIds) {
        Cat.logEvent("INVALID_METHOD_4", "com.dianping.pay.api.biz.activity.UnifiedIssueCouponBiz.buildTopAdResourcePromotionList(java.util.List,java.util.List)");
        List<IssueCouponSimpleDetail> topAdResourceSimpleDetails = Lists.newArrayList();
        for (VoucherDetailDO couponDetailDTO : voucherDetailDOList) {
            // 过滤神会员神券，神会员神券在banner位统一展示成标签
            if (CollectionUtils.isNotEmpty(magicalMemberCouponIds) && StringUtils.isNotBlank(couponDetailDTO.getCouponId()) && magicalMemberCouponIds.contains(couponDetailDTO.getCouponId())) {
                continue;
            }

            IssueCouponSimpleDetail simpleDetail = new IssueCouponSimpleDetail();
            simpleDetail.setIssueStatus(1);
            simpleDetail.setType(9);
            simpleDetail.setAmount(couponDetailDTO.getPriceAmount());
            simpleDetail.setPriceLimit(String.valueOf(couponDetailDTO.getPriceLimit()));
            simpleDetail.setDiscount(couponDetailDTO.getDiscount());
            String title = couponDetailDTO.getTitle();
            //普通券
            if(couponDetailDTO.getVoucherValueType() == 0) {
                simpleDetail.setCouponValueType(0);
                if (BigDecimal.ZERO.compareTo(new BigDecimal(couponDetailDTO.getPriceLimit())) == 0) {
                    title = new BigDecimal(couponDetailDTO.getPriceAmount()).stripTrailingZeros().toPlainString() + "元无门槛券";
                } else {
                    title = "满" +  new BigDecimal(couponDetailDTO.getPriceLimit()).stripTrailingZeros().toPlainString() + "减" + new BigDecimal(couponDetailDTO.getPriceAmount()).stripTrailingZeros().toPlainString() + "券";
                }
            } else if(couponDetailDTO.getVoucherValueType() == 2) {
                simpleDetail.setCouponValueType(1);
                title = couponDetailDTO.getDiscount() + "折券";
            }
            simpleDetail.setTitle(title);
            if (couponDetailDTO.getUseEndTime() != null) {
                simpleDetail.setEndTime(couponDetailDTO.getUseEndTime());
            }
            if (couponDetailDTO.getUseBeginTime() != null) {
                simpleDetail.setBeginTime(couponDetailDTO.getUseBeginTime());
            }
            topAdResourceSimpleDetails.add(simpleDetail);
        }
        topAdResourceSimpleDetails.sort(new IssueCouponSimpleDetailComparator());
        return topAdResourceSimpleDetails;
    }


    private IssueCouponDetail buildIssueCouponDetail(MerchantShareCouponExposeDTO issueCouponActivity) {
        Cat.logEvent("INVALID_METHOD_1", "com.dianping.pay.api.biz.activity.UnifiedIssueCouponBiz.buildIssueCouponDetail(com.sankuai.mkt.activity.api.merchantsharecoupon.model.MerchantShareCouponExposeDTO)");
        IssueCouponDetail issueCouponDetail = new IssueCouponDetail();
        issueCouponDetail.setCouponValueType(MapiCouponValueType.COMMON_COUPON.getCode());
        issueCouponDetail.setAmount(issueCouponActivity.getShareCouponAmount().doubleValue());
        issueCouponDetail.setPriceLimit(issueCouponActivity.getShareCouponLimitAmount().doubleValue());
        issueCouponDetail.setAmountDesc(IssueCouponUtils.formatPriceLimitDesc(issueCouponActivity.getShareCouponLimitAmount()));
        issueCouponDetail.setCouponProductType(3);
        CouponActivityDetail activityDetail = new CouponActivityDetail();
        if (issueCouponActivity.getCountdownTime() != null) {
            activityDetail.setCountdownTime(issueCouponActivity.getCountdownTime());
        }
        activityDetail.setDesc(issueCouponActivity.getDesc());
        activityDetail.setDescHighlight(issueCouponActivity.getDescHighlight());
        activityDetail.setSubTitle(issueCouponActivity.getSubTitle());
        activityDetail.setActivityId(issueCouponActivity.getActivityCode());
        activityDetail.setSharePic(issueCouponActivity.getSharePic());
        activityDetail.setSubTitleHighlight(issueCouponActivity.getSubTitleHighlight());
        activityDetail.setExposeStatus(issueCouponActivity.getExposeStatus());
        issueCouponDetail.setTitle("商家分享券");
        issueCouponDetail.setScript("分享专属券");
        issueCouponDetail.setUnifiedCouponGroupId(issueCouponActivity.getActivityCode());
        IssueCouponStatus issueCouponStatus = new IssueCouponStatus();
        if (issueCouponActivity.getExposeStatus() == ExposeStatusEnum.CAN_JOIN.code) {
            issueCouponStatus.setComment("分享抢券");
            issueCouponStatus.setHighLightType(1);
            issueCouponStatus.setStatus(0);
            activityDetail.setComment("分享抢券");
        } else if (issueCouponActivity.getExposeStatus() == ExposeStatusEnum.REWARDED.code) {
            issueCouponStatus.setComment("立即使用");
            issueCouponStatus.setHighLightType(0);
            issueCouponStatus.setStatus(1);
            activityDetail.setComment("立即使用");
        } else if (issueCouponActivity.getExposeStatus() == ExposeStatusEnum.ONGOING.code) {
            issueCouponStatus.setComment("继续分享");
            issueCouponStatus.setHighLightType(1);
            issueCouponStatus.setStatus(0);
            activityDetail.setComment("继续分享");
        }
        issueCouponDetail.setActivityInfo(activityDetail);
        issueCouponDetail.setIssueStatus(issueCouponStatus);
        return issueCouponDetail;
    }


    private IssueCouponDetail buildIssueCouponDetail(IssueCouponActivity issueCouponActivity, boolean fillLimitTime) {
        IssueCouponDetail issueCouponDetail = new IssueCouponDetail();
        issueCouponDetail.setUnifiedCouponGroupId(issueCouponActivity.getCouponGroup().getUnifiedCouponGroupId());

        int valueType = issueCouponActivity.getCouponGroup().getValueType();
        if (valueType == CouponGroupValueType.DISCOUNT.getCode()) {
            issueCouponDetail.setCouponValueType(MapiCouponValueType.DISCOUNT_COUPON.getCode());
            issueCouponDetail.setDiscount((double) issueCouponActivity.getCouponGroup().getDiscountCouponDTO().getDiscount() / 10);
        } else {
            issueCouponDetail.setCouponValueType(MapiCouponValueType.COMMON_COUPON.getCode());
        }

        issueCouponDetail.setAmount(issueCouponActivity.getCouponGroup().getDiscountAmount().doubleValue());
        issueCouponDetail.setPriceLimit(issueCouponActivity.getCouponGroup().getPriceLimit().doubleValue());
        issueCouponDetail.setAmountDesc(IssueCouponUtils.formatTitle(issueCouponActivity.getCouponGroup()));

        // 根据已领和未领两种状态填充不同的时间文案
        if (!IssueCouponTypeUtils.isIssued(issueCouponActivity)) {
            issueCouponDetail.setTimeDesc(IssueCouponUtils.formatCouponTimePeriod(issueCouponActivity.getCouponGroup()));
        } else {
            UnifiedCouponDTO unifiedCouponDTO = IssueCouponTypeUtils.getIssuedUnifiedCouponDto(issueCouponActivity);
            issueCouponDetail.setTimeDesc(IssueCouponUtils.formatTimePeriod(unifiedCouponDTO.getBeginTime(), unifiedCouponDTO.getEndTime()));
        }

        int merchantProductType = IssueCouponUtils.getMerchantCouponProductType(issueCouponActivity.getCouponGroup());
        if (merchantProductType == MerchantCouponProductType.CERTAIN_LABLE.getValue()) {
            // 副标题即为品类文案
            issueCouponDetail.setCategoryDesc(issueCouponActivity.getCouponGroup().getSubDisplayTitle());
            issueCouponDetail.setTitle("商家品类券");
        } else {
            issueCouponDetail.setTitle("商家通用券");
        }
        boolean isNewUserCoupon = IssueCouponUtils.isNewUserCoupon(issueCouponActivity.getCouponGroup());
        if (isNewUserCoupon) {
            issueCouponDetail.setScript("新客专享");
            issueCouponDetail.setUserTypeLimit(true);
        } else {
            issueCouponDetail.setUserTypeLimit(false);
        }
        IssueCouponStatus issueCouponStatus = new IssueCouponStatus();
        if (!IssueCouponTypeUtils.isIssued(issueCouponActivity)) {
            issueCouponStatus.setStatus(0);
            issueCouponStatus.setComment("领取");
        } else {
            issueCouponStatus.setStatus(1);
            issueCouponStatus.setComment("已领取");
        }
        issueCouponDetail.setIssueStatus(issueCouponStatus);
        if (StringUtils.isNotBlank(issueCouponActivity.getCouponGroup().getUnifiedCouponGroupId())) {
            issueCouponDetail.setUnifiedCouponGroupId(issueCouponActivity.getCouponGroup().getUnifiedCouponGroupId());
        } else {
            issueCouponDetail.setUnifiedCouponGroupId(String.valueOf(issueCouponActivity.getCouponGroup().getCouponGroupId()));
        }
        if (IssueCouponUtils.isDzTimeLimitRule(issueCouponActivity.getCouponGroup())) {
            issueCouponDetail.setDzTimeLimitRule(IssueCouponUtils.formatDzTimeLimitRule(issueCouponActivity.getCouponGroup()));
        }
        if (fillLimitTime) {
            issueCouponDetail.setBizAndTimeLimitDesc(issueCouponActivity.getBizAndTimeLimitDesc());
        }
        return issueCouponDetail;
    }


    private PFLOceanItemInfo buildOceanItemIfo(String category, String bidView, String bidClick) {
        PFLOceanItemInfo oceanItemInfo = new PFLOceanItemInfo();
        oceanItemInfo.setBidView(bidView);
        oceanItemInfo.setBidClick(bidClick);
        oceanItemInfo.setCategory(category);
        return oceanItemInfo;
    }

    private List<IssueCouponSimpleDetail> buildExposeCouponDetailList(List<MerchantShareCouponExposeDTO> couponExposeDTOS, int size) {
        Cat.logEvent("INVALID_METHOD_1", "com.dianping.pay.api.biz.activity.UnifiedIssueCouponBiz.buildExposeCouponDetailList(java.util.List,int)");
        if (CollectionUtils.isEmpty(couponExposeDTOS)) {
            return null;
        }
        List<MerchantShareCouponExposeDTO> shareCouponExposeDTOS = couponExposeDTOS.subList(0, Math.min(size, couponExposeDTOS.size()));
        List<IssueCouponSimpleDetail> simpleDetails = Lists.newArrayListWithExpectedSize(shareCouponExposeDTOS.size());
        for (MerchantShareCouponExposeDTO shareCouponExposeDTO : shareCouponExposeDTOS) {
            simpleDetails.add(convertShareCoupon2SimpleDetail(shareCouponExposeDTO));
        }
        return simpleDetails;
    }

    private IssueCouponSimpleDetail convertShareCoupon2SimpleDetail(MerchantShareCouponExposeDTO shareCouponExposeDTO) {
        Cat.logEvent("INVALID_METHOD_4", "com.dianping.pay.api.biz.activity.UnifiedIssueCouponBiz.convertShareCoupon2SimpleDetail(com.sankuai.mkt.activity.api.merchantsharecoupon.model.MerchantShareCouponExposeDTO)");
        IssueCouponSimpleDetail simpleDetail = new IssueCouponSimpleDetail();
        simpleDetail.setType(4);
        simpleDetail.setTitle("分享券");
        simpleDetail.setAmount(shareCouponExposeDTO.getShareCouponAmount().doubleValue());
        simpleDetail.setPriceLimit(shareCouponExposeDTO.getShareCouponLimitAmount().stripTrailingZeros().toPlainString());
        if (shareCouponExposeDTO.getExposeStatus() == ExposeStatusEnum.REWARDED.code) {
            simpleDetail.setIssueStatus(1);
        } else {
            simpleDetail.setIssueStatus(0);
        }
        return simpleDetail;
    }

    //TODO 后续改成配置化
    private Pair<String, Integer> callCategory(ShopDTO shopDTO, long shopId, boolean isMt) {
        try {
            if (shopDTO == null) {
                shopDTO = shopWrapService.loadShopInfo(shopId, isMt);
            }
            if (shopDTO == null) {
                return Pair.from(YIMEI_CATEGORY, 0);
            }
            int shopType = shopDTO.getShopType() != null ? shopDTO.getShopType() : 0;
            int category = shopDTO.getMainCategoryId();

            if (shopType == BEAUTY_SHOP_TYPE) {
                if (category == YIMEI_SHOP_CATEGORY) {
                    return Pair.from(YIMEI_CATEGORY, shopType);
                }
                return Pair.from(BEAUTY_CATEGORY, shopType);
            } else if (shopType == MEDICAL_SHOP_TYPE) {
                return Pair.from(YILIAO_CATEGORY, shopType);
            }
            return Pair.from(YIMEI_CATEGORY, shopType);
        } catch (Exception e) {
            log.error("call view category failed. shopId:{}, isMt:{}", shopId, isMt);
            return Pair.from(YIMEI_CATEGORY, 0);
        }
    }

    private Map<Integer, Boolean> getBrandValidResult(List<IssueCouponActivity> issueCouponActivities, PromotionRequestContext promotionRequestContext) {
        Map<Integer, Boolean> brandNewUserValidateResult = Maps.newHashMap();
        List<Integer> couponGroupIds = Lists.newArrayList();
        for (IssueCouponActivity issueCouponActivity : issueCouponActivities) {
            UnifiedCouponGroupDTO unifiedCouponGroupDTO = issueCouponActivity.getCouponGroup();
            boolean isNewUserCoupon = IssueCouponUtils.isNewUserCoupon(unifiedCouponGroupDTO);
            if (isNewUserCoupon) {
                couponGroupIds.add(issueCouponActivity.getCouponGroupId());
            }
        }
        if (CollectionUtils.isNotEmpty(couponGroupIds)) {
            MerchantNewUserValidateResult merchantNewUserValidateResult = couponNewUserWrapper.getMerchantNewUserResult(couponGroupIds, promotionRequestContext.getUserId(), promotionRequestContext.getUserType().getCode(), 0, Lists.newArrayList(), UnifiedCouponProductType.SHOPID.code);
            if (merchantNewUserValidateResult != null && MapUtils.isNotEmpty(merchantNewUserValidateResult.getBrandNewUserValidateResult())) {
                brandNewUserValidateResult = merchantNewUserValidateResult.getBrandNewUserValidateResult();
            }
        }
        return brandNewUserValidateResult;
    }

}
