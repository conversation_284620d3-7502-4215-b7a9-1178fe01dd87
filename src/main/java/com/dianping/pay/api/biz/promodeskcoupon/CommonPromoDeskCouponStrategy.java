package com.dianping.pay.api.biz.promodeskcoupon;

import com.dianping.cat.Cat;
import com.dianping.pay.api.beans.GetPromoDeskCouponRequest;
import com.dianping.pay.api.beans.GetPromoDeskRequest;
import com.dianping.pay.api.beans.PromoProduct;
import com.dianping.pay.api.biz.discount.CouponMapper;
import com.dianping.pay.api.entity.promodesk.PromoDeskCoupon;
import com.dianping.unified.coupon.manage.api.UnifiedCouponListService;
import com.dianping.unified.coupon.manage.api.dto.UnifiedCouponDTO;
import com.dianping.unified.coupon.manage.api.dto.UnifiedCouponListOption;
import com.dianping.unified.coupon.manage.api.dto.UnifiedCouponQueryContext;
import com.dianping.unified.coupon.manage.api.dto.UnifiedCouponQueryOption;
import com.dianping.unified.coupon.manage.api.response.UnifiedCouponManageResponse;

import javax.annotation.Resource;
import java.util.List;
import java.util.Set;

public class CommonPromoDeskCouponStrategy implements PromoDeskCouponStrategy {

    private static final UnifiedCouponQueryOption queryOption = new UnifiedCouponQueryOption();
    static {
        queryOption.setTemplateId(2); // short time format
        queryOption.setFormatTitle(true);
        queryOption.setFormatDesc(true);
    }

    @Resource
    protected UnifiedCouponListService unifiedCouponListService;

    @Override
    public boolean assembleCouponSet(Set<PromoDeskCoupon> resultSet,
                                        Set<PromoDeskCoupon> unavailableSet,
                                        GetPromoDeskCouponRequest request,
                                        PromoProduct promoProduct,
                                        int start,
                                        int limit) {
        Cat.logEvent("INVALID_METHOD_4", "com.dianping.pay.api.biz.promodeskcoupon.CommonPromoDeskCouponStrategy.assembleCouponSet(Set,Set,GetPromoDeskCouponRequest,PromoProduct,int,int)");
        UnifiedCouponQueryContext couponQueryContext = CouponMapper.toCouponQueryContext(request, promoProduct, false);
        UnifiedCouponManageResponse<List<UnifiedCouponDTO>> listResponse = unifiedCouponListService.queryCouponByContext(couponQueryContext, queryOption, new UnifiedCouponListOption(5, 0, start, limit));
        if (listResponse.isSuccess()) {
            for (UnifiedCouponDTO couponDTO : listResponse.getResult()) {
                PromoDeskCoupon promoDeskCoupon = CouponMapper.toPromoDeskCoupon(promoProduct.getProductCode(), couponDTO);
                if (promoDeskCoupon.isCanUse()) {
                    resultSet.add(promoDeskCoupon);
                    if (unavailableSet.contains(promoDeskCoupon)) {
                        unavailableSet.remove(promoDeskCoupon);
                    }
                } else if (start == 0 && !resultSet.contains(promoDeskCoupon)) {
                    unavailableSet.add(promoDeskCoupon);
                }
            }
            return listResponse.getResult().size() < limit;
        }
        return true;
    }

    @Override
    public boolean assembleShopCouponSet(Set<PromoDeskCoupon> resultSet,
                                         Set<PromoDeskCoupon> unavailableSet,
                                         GetPromoDeskCouponRequest request,
                                         PromoProduct promoProduct) {
        Cat.logEvent("INVALID_METHOD_2", "com.dianping.pay.api.biz.promodeskcoupon.CommonPromoDeskCouponStrategy.assembleShopCouponSet(Set,Set,GetPromoDeskCouponRequest,PromoProduct)");
        return true;
    }

    @Override
    public boolean isCouponOfShop(UnifiedCouponDTO couponDTO, GetPromoDeskRequest request) {
        Cat.logEvent("INVALID_METHOD_3", "com.dianping.pay.api.biz.promodeskcoupon.CommonPromoDeskCouponStrategy.isCouponOfShop(com.dianping.unified.coupon.manage.api.dto.UnifiedCouponDTO,com.dianping.pay.api.beans.GetPromoDeskRequest)");
        return false;
    }
}
