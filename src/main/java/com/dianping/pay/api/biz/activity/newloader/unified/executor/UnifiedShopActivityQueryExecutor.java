package com.dianping.pay.api.biz.activity.newloader.unified.executor;

import com.dianping.cat.Cat;
import com.dianping.pay.api.beans.IssueCouponActivity;
import com.dianping.pay.api.biz.activity.newloader.AbstractUnifiedCouponActivityQueryExecutor;
import com.dianping.pay.api.biz.activity.newloader.dto.CouponIssueActivityQueryContext;
import com.dianping.pay.api.biz.activity.newloader.mapper.IssueActivityMapper;
import com.dianping.poi.relation.service.api.PoiRelationService;
import com.dianping.spc.common.Response;
import com.dianping.tgc.open.ShopActivityQueryRemoteService;
import com.dianping.tgc.open.entity.ActivityDTO;
import com.dianping.tgc.open.entity.ActivityQueryOption;
import com.dianping.tgc.open.entity.BatchActivityQueryRequest;
import com.dianping.tgc.open.entity.BatchQueryResponseDTO;
import com.dianping.tgc.open.enums.QuerySourceEnum;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;

import java.util.List;
import java.util.Map;

/**
 * Created by drintu on 18/6/7.
 */

public class UnifiedShopActivityQueryExecutor extends AbstractUnifiedCouponActivityQueryExecutor{
    private ShopActivityQueryRemoteService shopActivityQueryRemoteService;
    private PoiRelationService poiRelationCacheService;

    public UnifiedShopActivityQueryExecutor(CouponIssueActivityQueryContext queryContext){
        super(queryContext);
    }
    @Override
    protected List<IssueCouponActivity> loadFromRemote() {
        if (queryContext.getShopIdL() <= 0) {
            return Lists.newArrayList();
        }
        List<Long> dpShopIdList = Lists.newArrayList();
        if (queryContext.isDpClient()) {
            dpShopIdList  = Lists.newArrayList(queryContext.getShopIdL());
        } else {
            Map<Long, List<Long>> matchedDpIdMap = Maps.newHashMap();
            try {
                matchedDpIdMap = poiRelationCacheService.queryDpByMtIdsL(Lists.newArrayList(queryContext.getShopIdL()));
            } catch (Exception e) {
                Cat.logError("queryDpByMtIds", e);
            }
            if (MapUtils.isEmpty(matchedDpIdMap)) {
                return Lists.newArrayList();
            }
            for (List<Long> longs : matchedDpIdMap.values()) {
                if (CollectionUtils.isEmpty(longs)) {
                    continue;
                }
                dpShopIdList.addAll(longs);
            }
        }
        BatchActivityQueryRequest remoteRequest = new BatchActivityQueryRequest();
        remoteRequest.setShopLongIds(dpShopIdList);
        remoteRequest.setSource(queryContext.isDpClient() ? QuerySourceEnum.DP.getValue() : QuerySourceEnum.MT.getValue());
        ActivityQueryOption queryOption = new ActivityQueryOption();
        queryOption.setFillDiscountCoupon(true);
        remoteRequest.setActivityQueryOption(queryOption);
        Response<BatchQueryResponseDTO> response = shopActivityQueryRemoteService.batchQueryShopActivity(remoteRequest);
        if (!response.isSuccess() || response.getResult() == null) {
            return Lists.newArrayList();
        }
        Map<Long, List<ActivityDTO>> activityMap = response.getResult().getShopCouponMap();
        if (MapUtils.isEmpty(activityMap)) {
            return Lists.newArrayList();
        }
        return IssueActivityMapper.activities2IssueActivitiesL(activityMap, queryContext.isDpClient());
    }

    public UnifiedShopActivityQueryExecutor setShopActivityQueryRemoteService(ShopActivityQueryRemoteService shopActivityQueryRemoteService) {
        this.shopActivityQueryRemoteService = shopActivityQueryRemoteService;
        return this;
    }

    public UnifiedShopActivityQueryExecutor setPoiRelationCacheService(PoiRelationService poiRelationCacheService) {
        this.poiRelationCacheService = poiRelationCacheService;
        return this;
    }
}
