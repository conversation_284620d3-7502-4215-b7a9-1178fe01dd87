package com.dianping.pay.api.biz.promodeskcoupon;

import com.dianping.cat.Cat;
import com.dianping.pay.api.beans.GetPromoDeskRequest;
import com.dianping.pay.coupon.service.enums.CouponProductType;
import com.dianping.unified.coupon.manage.api.dto.UnifiedCouponDTO;
import com.dianping.unified.coupon.manage.api.dto.UnifiedCouponProductDTO;

public class TuangouPromoDeskCouponStrategy extends ShanhuiPromoDeskCouponStrategy {

    @Override
    public boolean isCouponOfShop(UnifiedCouponDTO couponDTO, GetPromoDeskRequest request) {
        Cat.logEvent("INVALID_METHOD_3", "com.dianping.pay.api.biz.promodeskcoupon.TuangouPromoDeskCouponStrategy.isCouponOfShop(com.dianping.unified.coupon.manage.api.dto.UnifiedCouponDTO,com.dianping.pay.api.beans.GetPromoDeskRequest)");
        int dealGroupId = request.getPromoProductList().get(0).getProductId();
        for (UnifiedCouponProductDTO couponProductDTO : couponDTO.getCouponGroupDTO().getCouponProductList()) {
            if (couponProductDTO.getType() == CouponProductType.DEALGROUPID.code
                    && couponProductDTO.getProductId() == dealGroupId) {
                return true;
            }
        }
        return false;
    }
}
