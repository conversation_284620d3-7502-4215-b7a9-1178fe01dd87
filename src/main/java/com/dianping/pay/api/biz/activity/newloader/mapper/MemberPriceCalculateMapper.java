package com.dianping.pay.api.biz.activity.newloader.mapper;

import com.dianping.cat.Cat;
import com.dianping.pay.api.biz.activity.newloader.dto.CouponActivityContext;
import com.dianping.pay.api.enums.QueryShopCouponTypeEnum;
import com.sankuai.nibmktproxy.queryclient.proxy.*;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2021/11/11
 */
public class MemberPriceCalculateMapper {

    //选出最佳优惠组合
    //价格相同情况展示减后价
    public static PromotionDTOResult calculateBestPricePromo(CouponActivityContext promoCtx) {
        PromotionDTOResult bestPromotion = null;
        int currentPromo = 0;
        int reducePromo = calculateTotalPromo(promoCtx.getProxyReducePromotionResult());
        if (reducePromo > 0) {
            bestPromotion = promoCtx.getProxyReducePromotionResult();
            currentPromo = reducePromo;
        }
        int memberCardPromo = calculateTotalPromo(promoCtx.getProxyMemberCardPromotionResult());
        if (memberCardPromo > 0 && memberCardPromo > currentPromo) {
            bestPromotion = promoCtx.getProxyMemberCardPromotionResult();
            currentPromo = memberCardPromo;
        }
        int joyCardPromo = calculateTotalPromo(promoCtx.getProxyJoyCardPromotionResult());
        if (joyCardPromo > 0 && joyCardPromo > currentPromo) {
            bestPromotion = promoCtx.getProxyJoyCardPromotionResult();
        }
        return bestPromotion;
    }

    public static int calculateTotalPromo(PromotionDTOResult promotionDTOResult) {
        if (promotionDTOResult == null || CollectionUtils.isEmpty(promotionDTOResult.getGetPromotionDTO())) {
            return 0;
        }
        int totalPromo = 0;
        for (GetPromotionDTO getPromotionDTO : promotionDTOResult.getGetPromotionDTO()) {
            PromotionDTO promotionDTO = getPromotionDTO.getPromotionDTO();
            if (promotionDTO == null) {
                continue;
            }
            if (getPromotionDTO.getPromotionType() == PromotionType.DEDUCTION && promotionDTO.getDeductionDTO() != null) {
                totalPromo += Integer.parseInt(promotionDTO.getDeductionDTO().getReduce());
            }
            if (getPromotionDTO.getPromotionType() == PromotionType.DISCOUNT_CARD && promotionDTO.getDiscountCardDTO() != null) {
                totalPromo += Integer.parseInt(promotionDTO.getDiscountCardDTO().getPromoValue());
            }
            if (getPromotionDTO.getPromotionType() == PromotionType.JOY_CARD && promotionDTO.getJoyCardDTO() != null) {
                totalPromo += Integer.parseInt(promotionDTO.getJoyCardDTO().getPromoValue());
            }
        }
        return totalPromo;
    }

    //获取组件模块标签文案
    public static String getTagContent(PromotionDTOResult promotionDTOResult) {
        Cat.logEvent("INVALID_METHOD_1", "com.dianping.pay.api.biz.activity.newloader.mapper.MemberPriceCalculateMapper.getTagContent(com.sankuai.nibmktproxy.queryclient.proxy.PromotionDTOResult)");
        if (promotionDTOResult == null || promotionDTOResult.getProductExtraInfo() == null || promotionDTOResult.getProductExtraInfo().getProductDisplayShow() == null) {
            return null;
        }
        Map<Integer, ProductShowDTO> productShowMap = promotionDTOResult.getProductExtraInfo().getProductDisplayShow().getPrductShowMap();
        if (MapUtils.isEmpty(productShowMap) || !productShowMap.containsKey(ProductShowEnum.LONG_SUMMARY_TAG.getValue())) {
            return null;
        }
        return productShowMap.get(ProductShowEnum.LONG_SUMMARY_TAG.getValue()).getValue();
    }

    //获取商家立减优惠文案
    public static String getShopReduceContent(PromotionDTOResult promotionDTOResult) {
        Cat.logEvent("INVALID_METHOD_1", "com.dianping.pay.api.biz.activity.newloader.mapper.MemberPriceCalculateMapper.getShopReduceContent(com.sankuai.nibmktproxy.queryclient.proxy.PromotionDTOResult)");
        if (promotionDTOResult == null || promotionDTOResult.getProductExtraInfo() == null || promotionDTOResult.getProductExtraInfo().getProductDisplayShow() == null) {
            return null;
        }
        Map<Integer, ProductShowDTO> productShowMap = promotionDTOResult.getProductExtraInfo().getProductDisplayShow().getPrductShowMap();
        if (MapUtils.isEmpty(productShowMap) || !productShowMap.containsKey(ProductShowEnum.SHOP_DEDUCTION_DESCRIPTION.getValue())) {
            return null;
        }
        return productShowMap.get(ProductShowEnum.SHOP_DEDUCTION_DESCRIPTION.getValue()).getValue();
    }

    //获取平台立减优惠文案
    public static String getPlatformReduceContent(PromotionDTOResult promotionDTOResult) {
        Cat.logEvent("INVALID_METHOD_3", "com.dianping.pay.api.biz.activity.newloader.mapper.MemberPriceCalculateMapper.getPlatformReduceContent(com.sankuai.nibmktproxy.queryclient.proxy.PromotionDTOResult)");
        if (promotionDTOResult == null || promotionDTOResult.getProductExtraInfo() == null || promotionDTOResult.getProductExtraInfo().getProductDisplayShow() == null) {
            return null;
        }
        Map<Integer, ProductShowDTO> productShowMap = promotionDTOResult.getProductExtraInfo().getProductDisplayShow().getPrductShowMap();
        if (MapUtils.isEmpty(productShowMap) || !productShowMap.containsKey(ProductShowEnum.PLATFORM_DEDUCTION_DESCRIPTION.getValue())) {
            return null;
        }
        return productShowMap.get(ProductShowEnum.PLATFORM_DEDUCTION_DESCRIPTION.getValue()).getValue();
    }

    //获取会员卡(折扣卡)优惠文案
    public static String getMemberCardContent(PromotionDTOResult promotionDTOResult) {
        Cat.logEvent("INVALID_METHOD_5", "com.dianping.pay.api.biz.activity.newloader.mapper.MemberPriceCalculateMapper.getMemberCardContent(com.sankuai.nibmktproxy.queryclient.proxy.PromotionDTOResult)");
        if (promotionDTOResult == null || promotionDTOResult.getProductExtraInfo() == null || promotionDTOResult.getProductExtraInfo().getProductDisplayShow() == null) {
            return null;
        }
        if (!isExistPromo(promotionDTOResult, PromotionType.DISCOUNT_CARD)) {
            return null;
        }
        Map<Integer, ProductShowDTO> productShowMap = promotionDTOResult.getProductExtraInfo().getProductDisplayShow().getPrductShowMap();
        if (MapUtils.isEmpty(productShowMap) || !productShowMap.containsKey(ProductShowEnum.CARD_DESCRIPTION.getValue())) {
            return null;
        }
        return productShowMap.get(ProductShowEnum.CARD_DESCRIPTION.getValue()).getValue();
    }

    //获取玩乐卡优惠文案
    public static String getJoyCardContent(PromotionDTOResult promotionDTOResult) {
        Cat.logEvent("INVALID_METHOD_5", "com.dianping.pay.api.biz.activity.newloader.mapper.MemberPriceCalculateMapper.getJoyCardContent(com.sankuai.nibmktproxy.queryclient.proxy.PromotionDTOResult)");
        if (promotionDTOResult == null || promotionDTOResult.getProductExtraInfo() == null || promotionDTOResult.getProductExtraInfo().getProductDisplayShow() == null) {
            return null;
        }
        if (!isExistPromo(promotionDTOResult, PromotionType.JOY_CARD)) {
            return null;
        }
        Map<Integer, ProductShowDTO> productShowMap = promotionDTOResult.getProductExtraInfo().getProductDisplayShow().getPrductShowMap();
        if (MapUtils.isEmpty(productShowMap) || !productShowMap.containsKey(ProductShowEnum.CARD_DESCRIPTION.getValue())) {
            return null;
        }
        return productShowMap.get(ProductShowEnum.CARD_DESCRIPTION.getValue()).getValue();
    }

    public static boolean isExistPromo(PromotionDTOResult promotionDTOResult, PromotionType promotionType) {
        Cat.logEvent("INVALID_METHOD_1", "com.dianping.pay.api.biz.activity.newloader.mapper.MemberPriceCalculateMapper.isExistPromo(PromotionDTOResult,PromotionType)");
        if (promotionDTOResult == null || CollectionUtils.isEmpty(promotionDTOResult.getGetPromotionDTO())) {
            return false;
        }
        for (GetPromotionDTO getPromotionDTO : promotionDTOResult.getGetPromotionDTO()) {
            PromotionDTO promotionDTO = getPromotionDTO.getPromotionDTO();
            if (promotionDTO != null && getPromotionDTO.getPromotionType() == promotionType) {
                return true;
            }
        }
        return false;
    }

    public static boolean isMemberDay(PromotionDTOResult promotionDTOResult) {
        Cat.logEvent("INVALID_METHOD_5", "com.dianping.pay.api.biz.activity.newloader.mapper.MemberPriceCalculateMapper.isMemberDay(com.sankuai.nibmktproxy.queryclient.proxy.PromotionDTOResult)");
        if (promotionDTOResult == null || CollectionUtils.isEmpty(promotionDTOResult.getGetPromotionDTO())) {
            return false;
        }
        List<GetPromotionDTO> getPromotionDTOList = promotionDTOResult.getGetPromotionDTO();
        for (GetPromotionDTO getPromotionDTO : getPromotionDTOList) {
            PromotionDTO promotionDTO = getPromotionDTO.getPromotionDTO();
            if (promotionDTO == null) {
                continue;
            }
            if (getPromotionDTO.getPromotionType() == PromotionType.DISCOUNT_CARD && promotionDTO.getDiscountCardDTO() != null) {
                return promotionDTO.getDiscountCardDTO().isIsMemberDay();
            }
        }
        return false;
    }
}
