package com.dianping.pay.api.biz.user;

import com.dianping.api.common.enums.FeatureSwitchEnum;
import com.dianping.api.framework.FeatureContingency;
import com.dianping.cat.Cat;
import com.dianping.pay.account.enums.AccountCatalog;
import com.dianping.pay.account.model.UserAccountStatisticsDTO;
import com.dianping.pay.account.service.personal.PrivateAccountQueryService;
import com.dianping.pay.common.enums.ProductCode;
import com.sankuai.wpt.user.merge.query.thrift.message.BindRelationResp;
import com.sankuai.wpt.user.merge.query.thrift.message.UserMergeQueryService;
import org.apache.commons.lang.Validate;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.annotation.Resource;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * Created by huawei.li on 15/3/12.
 */
public class UserAccountBiz {
    private static final Logger log = LoggerFactory.getLogger(UserAccountBiz.class);
    @Resource
    private PrivateAccountQueryService privateAccountQueryService;

    @Autowired
    private UserMergeQueryService.Iface userMergeQueryService;

    public UserAccountStatisticsDTO getAccountStatistics(long userId,AccountCatalog... catalogs){
        Cat.logEvent("INVALID_METHOD_3", "com.dianping.pay.api.biz.user.UserAccountBiz.getAccountStatistics(long,com.dianping.pay.account.enums.AccountCatalog[])");
        try{
            if(FeatureContingency.isSwitchOff(FeatureSwitchEnum.ACCOUNT)){
                return null;
            }
            if(userId<=0){
                return null;
            }else{
                return privateAccountQueryService.queryUserAccountStatistics(userId, ProductCode.COMMON,catalogs);
            }
        }catch (Exception e){
            return null;
        }
    }

    /**
     * 将点评userId转化成对应的美团虚id
     * @param dpUserId
     * @return
     */
    public long getVirtualMtUserIdByDpUserId(long dpUserId) {
        Cat.logEvent("INVALID_METHOD_4", "com.dianping.pay.api.biz.user.UserAccountBiz.getVirtualMtUserIdByDpUserId(long)");
        if (dpUserId <= 0) {
            return 0;
        }
        long mtUserId = 0;

        try {
            BindRelationResp bindRelationResp = userMergeQueryService.getOrCreateVirtualBindByDpUserId(dpUserId);
            Validate.isTrue(bindRelationResp != null && bindRelationResp.getData() != null && bindRelationResp.getData().getMtUserId() != null, "bind user fail");
            mtUserId = bindRelationResp.getData().getMtUserId().getId();
        } catch (Exception e) {
            log.error("UserAccountService# getVirtualMtUserIdByDpUserId failed. dpUserId: {}", dpUserId, e);
        }
        log.info("dpUserId:{} convert to mtUserId:{}", dpUserId, mtUserId);
        return mtUserId;
    }

}
