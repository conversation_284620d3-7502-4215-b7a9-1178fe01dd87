package com.dianping.pay.api.biz.activity.newloader.mapper;

import com.dianping.cat.Cat;
import com.dianping.gmkt.coupon.common.api.enums.CouponGroupExtraKeyEnum;
import com.dianping.mobile.framework.datatypes.IMobileContext;
import com.dianping.pay.api.biz.activity.newloader.dto.CouponActivityContext;
import com.dianping.pay.api.entity.issuecoupon.BrandCouponInfo;
import com.dianping.pay.api.enums.BrandCouponIssueStatus;
import com.dianping.pay.api.util.BrandCouponInfoComparator;
import com.dianping.pay.api.util.IssueCouponUtils;
import com.dianping.pay.api.util.JsonUtils;
import com.dianping.pay.api.util.UrlHelper;
import com.dianping.tgc.open.entity.BrandActivityDTO;
import com.dianping.tgc.open.entity.SimpleCouponGroupDTO;
import com.dianping.unified.coupon.manage.api.dto.BrandCouponLogoDTO;
import com.dianping.unified.coupon.manage.api.dto.UnifiedCouponDTO;
import com.google.common.collect.Lists;
import com.sankuai.mpmctbrand.brandc.dto.brand.BrandInfoDTO;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;
import java.util.Map;

public class BrandPromoInfoMapper {

    private static final Logger logger = LogManager.getLogger(BrandPromoInfoMapper.class);

    public static List<BrandCouponInfo> buildBrandInfo(CouponActivityContext couponActivityContext, IMobileContext context) {
        try {
            List<BrandActivityDTO> brandActivityDTOS = couponActivityContext.getBrandActivityDTOS();
            Map<Integer, List<UnifiedCouponDTO>> userIssuedCoupon = couponActivityContext.getUserIssuedCoupon();
            Map<Long, BrandInfoDTO> brandInfoDTOMap = couponActivityContext.getBrandInfo();

            /**
             * 组装结果，共4种状态：
             * 未来可领： 领取时间 > 当前时间
             * 可领取：领取开始时间<=当前时间<领取结束时间，且剩余库存>0，且领取数<可领取上限
             * 已领取：领取开始时间<=当前时间<领取结束时间，且剩余库存>0， 且领取数=可领取上限
             * 已领光：领取开始时间<=当前时间<领取结束时间，且剩余库存=0
             */
            List<BrandCouponInfo> result = Lists.newArrayListWithExpectedSize(brandActivityDTOS.size());
            Date now = new Date();
            for (BrandActivityDTO brandActivityDTO : brandActivityDTOS) {
                if (brandActivityDTO == null || brandActivityDTO.getCouponGroupDTO() == null) {
                    continue;
                }
                SimpleCouponGroupDTO simpleCouponGroupDTO = brandActivityDTO.getCouponGroupDTO();
                int couponGroupId = brandActivityDTO.getCouponGroupDTO().getCouponGroupId();
                BrandCouponInfo brandCouponInfo = new BrandCouponInfo();
                brandCouponInfo.setBActivityId(brandActivityDTO.getBrandActivityID());
                brandCouponInfo.setCouponGroupName(simpleCouponGroupDTO.getCouponGroupName());
                brandCouponInfo.setDiscountAmount(simpleCouponGroupDTO.getDiscountAmount().stripTrailingZeros().toPlainString());

                if (MapUtils.isNotEmpty(simpleCouponGroupDTO.getExtraInfoMap()) && simpleCouponGroupDTO.getExtraInfoMap().containsKey(CouponGroupExtraKeyEnum.brandCouponLogo.name())) {
                    BrandCouponLogoDTO brandCouponLogoDTO = JsonUtils.toObject(simpleCouponGroupDTO.getExtraInfoMap().get(CouponGroupExtraKeyEnum.brandCouponLogo.name()), BrandCouponLogoDTO.class);
                    if (brandCouponLogoDTO != null) {
                        brandCouponInfo.setVerticalLogo(brandCouponLogoDTO.getVerticalLogo());
                        brandCouponInfo.setTransverseLogo(brandCouponLogoDTO.getTransverseLogo());
                    }
                }

                BrandInfoDTO brandInfoDTO = brandInfoDTOMap.get(brandActivityDTO.getBrandID());
                if (brandInfoDTO != null && brandInfoDTO.getBrand() != null) {
                    brandCouponInfo.setBrandName(brandInfoDTO.getBrand().getBrandName());
                    brandCouponInfo.setBrandEngName(brandInfoDTO.getBrand().getEnName());

                    String serialNum = "";
                    if (MapUtils.isNotEmpty(simpleCouponGroupDTO.getExtraInfoMap()) && simpleCouponGroupDTO.getExtraInfoMap().containsKey(CouponGroupExtraKeyEnum.brandCouponProductSeriesName.name())) {
                        serialNum = simpleCouponGroupDTO.getExtraInfoMap().get(CouponGroupExtraKeyEnum.brandCouponProductSeriesName.name());
                    }
                    if (simpleCouponGroupDTO.isCouponProductLimit()) {
                        brandCouponInfo.setUseRuleDesc(String.format("限%s指定部分商品专享", brandInfoDTO.getBrand().getBrandName() + serialNum));
                    } else {
                        brandCouponInfo.setUseRuleDesc(String.format("限%s品牌直供商品专享", brandInfoDTO.getBrand().getBrandName()));
                    }

                } else {
                    brandCouponInfo.setUseRuleDesc("限指定品牌直供商品");
                }

                brandCouponInfo.setPriceLimitDesc(IssueCouponUtils.formatPriceLimitDesc(simpleCouponGroupDTO.getPriceLimit()));
                brandCouponInfo.setTimeDesc(formatCouponTimePeriod(simpleCouponGroupDTO));

                int issueStatus;
                boolean showIssueButton = true;
                String issueTag = null;
                String couponDetailUrl = null;

                // 还没到开始时间
                if (simpleCouponGroupDTO.getBeginTime().after(now)) {
                    issueStatus = BrandCouponIssueStatus.FUTURE_CAN_ISSUE.getStatus();
                } else if (simpleCouponGroupDTO.getRemainStock() == 0 && CollectionUtils.isEmpty(userIssuedCoupon.get(couponGroupId))) {
                    // 用户没有领而且已经领光了
                    issueStatus = BrandCouponIssueStatus.SOLD_OUT.getStatus();
                } else {
                    int maxPerUser = simpleCouponGroupDTO.getMaxPerUser();
                    List<UnifiedCouponDTO> unifiedCouponDTOS = userIssuedCoupon.get(couponGroupId);
                    int size = CollectionUtils.isNotEmpty(unifiedCouponDTOS) ? unifiedCouponDTOS.size() : 0;
                    issueTag = buildIssueTag(size);

                    // 两种情况：1）用户已经达到了领取上线，2）库存为0，但用户历史上已经领取了
                    if ((maxPerUser > 0 && maxPerUser == size) ||
                            (simpleCouponGroupDTO.getRemainStock() == 0 && CollectionUtils.isNotEmpty(userIssuedCoupon.get(couponGroupId)))) {
                        UnifiedCouponDTO unUsedCouponDto = getUnUsedCouponDto(unifiedCouponDTOS);
                        if (unUsedCouponDto == null) {
                            showIssueButton = false;
                        } else {
                            couponDetailUrl = UrlHelper.getUrl(unUsedCouponDto.getUnifiedCouponId(), couponActivityContext.isMt());
                        }
                        issueStatus = BrandCouponIssueStatus.HAS_ISSUED.getStatus();
                    } else {
                        issueStatus = BrandCouponIssueStatus.CAN_ISSUE.getStatus();
                    }
                }
                brandCouponInfo.setCouponDetailUrl(couponDetailUrl);
                brandCouponInfo.setIssueTag(issueTag);
                brandCouponInfo.setIssueStatus(issueStatus);
                brandCouponInfo.setShowIssueButton(showIssueButton);
                brandCouponInfo.setIssueDesc(getIssueDesc(issueStatus, simpleCouponGroupDTO));

                result.add(brandCouponInfo);
            }

            if (CollectionUtils.isNotEmpty(result)) {
                result.sort(new BrandCouponInfoComparator());
            }
            return result;
        } catch (Exception e) {
            logger.error("BrandPromoInfoMapper# buildBrandInfo error", e);
            return Lists.newArrayList();
        }
    }

    private static UnifiedCouponDTO getUnUsedCouponDto(List<UnifiedCouponDTO> unifiedCouponDTOS) {
        Cat.logEvent("INVALID_METHOD_1", "com.dianping.pay.api.biz.activity.newloader.mapper.BrandPromoInfoMapper.getUnUsedCouponDto(java.util.List)");
        for (UnifiedCouponDTO unifiedCouponDTO : unifiedCouponDTOS) {
            if (!unifiedCouponDTO.isUsed()) {
                return unifiedCouponDTO;
            }
        }
        return null;
    }

    private static String buildIssueTag(int size) {
        Cat.logEvent("INVALID_METHOD_5", "com.dianping.pay.api.biz.activity.newloader.mapper.BrandPromoInfoMapper.buildIssueTag(int)");
        if (size == 1) {
            return "已领取";
        } else if (size > 1) {
            return String.format("已领%s张", size);
        }
        return null;
    }

    private static String getIssueDesc(int issueStatus, SimpleCouponGroupDTO simpleCouponGroupDTO) {
        Cat.logEvent("INVALID_METHOD_1", "com.dianping.pay.api.biz.activity.newloader.mapper.BrandPromoInfoMapper.getIssueDesc(int,com.dianping.tgc.open.entity.SimpleCouponGroupDTO)");
        if (issueStatus == BrandCouponIssueStatus.FUTURE_CAN_ISSUE.getStatus()) {
            SimpleDateFormat dateFormat = new SimpleDateFormat("MM.dd");
            return dateFormat.format(simpleCouponGroupDTO.getBeginTime()) + "起可领";
        } else if (issueStatus == BrandCouponIssueStatus.CAN_ISSUE.getStatus()) {
            return "领取";
        } else if (issueStatus == BrandCouponIssueStatus.HAS_ISSUED.getStatus()) {
            return "查看";
        } else if (issueStatus == BrandCouponIssueStatus.SOLD_OUT.getStatus()) {
            return "已领光";
        }
        return null;
    }

    /**
     * 目前品牌券只有固定时间模式
     *
     * @param couponGroupDTO
     * @return
     */
    private static String formatCouponTimePeriod(SimpleCouponGroupDTO couponGroupDTO) {
        Cat.logEvent("INVALID_METHOD_1", "com.dianping.pay.api.biz.activity.newloader.mapper.BrandPromoInfoMapper.formatCouponTimePeriod(com.dianping.tgc.open.entity.SimpleCouponGroupDTO)");
        Date beginTime = couponGroupDTO.getValidBeginTime() != null ? couponGroupDTO.getValidBeginTime() : couponGroupDTO.getBeginTime();
        Date endTime = couponGroupDTO.getValidEndTime() != null ? couponGroupDTO.getValidEndTime() : couponGroupDTO.getEndTime();
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy.MM.dd");
        return dateFormat.format(beginTime) + "-" + dateFormat.format(endTime);
    }


}
