package com.dianping.pay.api.biz.promodesk;

import com.dianping.cat.Cat;
import com.dianping.pay.account.enums.AccountCatalog;
import com.dianping.pay.account.model.UserAccountStatisticsDTO;
import com.dianping.pay.api.beans.DiscountRules;
import com.dianping.pay.api.beans.GetPromoDeskRequest;
import com.dianping.pay.api.biz.discount.CouponBiz;
import com.dianping.pay.api.biz.discount.ReductionBiz;
import com.dianping.pay.api.biz.user.UserAccountBiz;
import com.dianping.pay.api.entity.promodesk.*;
import com.dianping.pay.cashier.biz.enums.PaymentRule;
import com.dianping.pay.common.enums.ProductCode;
import com.dianping.pay.promo.execute.service.PayPromoDeskQueryService;
import com.dianping.pay.promo.execute.service.dto.PromoToolDTO;
import com.dianping.pay.promo.execute.service.dto.response.PromoQueryResponse;
import com.dianping.pay.promo.execute.service.enums.PromoToolType;
import com.google.common.base.Predicate;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * 默认优惠台加载策略
 * 返回：多个立减、抵用券、商户抵用券、红包
 * 不返回：积分、小黄条
 */
public class CommonPromoDeskStrategy implements PromoDeskStrategy {

    @Resource
    protected ReductionBiz reductionBiz;
    @Resource
    protected UserAccountBiz userAccountBiz;
    @Resource
    protected PayPromoDeskQueryService payPromoDeskQueryService;
    @Resource
    protected CouponBiz couponBiz;

    @Override
    public DiscountPromoTool assembleDiscountPromoTool(GetPromoDeskRequest getPromoDeskRequest) {
        Cat.logEvent("INVALID_METHOD_1", "com.dianping.pay.api.biz.promodesk.CommonPromoDeskStrategy.assembleDiscountPromoTool(com.dianping.pay.api.beans.GetPromoDeskRequest)");
        return reductionBiz.queryPromoDTOGroup(getPromoDeskRequest, null);
    }

    @Override
    public CouponPromoTool assembleCouponPromoTool(GetPromoDeskRequest getPromoDeskRequest, DiscountRules discountRules) {
        Cat.logEvent("INVALID_METHOD_4", "com.dianping.pay.api.biz.promodesk.CommonPromoDeskStrategy.assembleCouponPromoTool(com.dianping.pay.api.beans.GetPromoDeskRequest,com.dianping.pay.api.beans.DiscountRules)");
        if (getPromoDeskRequest.getUserId() > 0
                && discountRules.isCanUseCoupon()) {
            CouponPromoTool couponPromoTool = new CouponPromoTool(PaymentRule.COUPON.code);
            couponPromoTool.setCanUse(true);
            return couponPromoTool;
        }
        return null;
    }

    @Override
    public CouponPromoTool assembleShopCouponPromoTool(GetPromoDeskRequest getPromoDeskRequest, DiscountRules discountRules) {
        Cat.logEvent("INVALID_METHOD_5", "com.dianping.pay.api.biz.promodesk.CommonPromoDeskStrategy.assembleShopCouponPromoTool(com.dianping.pay.api.beans.GetPromoDeskRequest,com.dianping.pay.api.beans.DiscountRules)");
        CouponPromoTool couponPromoTool = new CouponPromoTool(PaymentRule.SHOPCOUPON.code); // 商家抵用券
        couponPromoTool.setCanUse(true);
        return couponPromoTool;
    }

    @Override
    public HongBaoPromoTool assembleHongBaoPromoTool(GetPromoDeskRequest getPromoDeskRequest, DiscountRules discountRules, Map<Integer, DiscountRules> productCodeDiscountRule) {
        Cat.logEvent("INVALID_METHOD_2", "com.dianping.pay.api.biz.promodesk.CommonPromoDeskStrategy.assembleHongBaoPromoTool(com.dianping.pay.api.beans.GetPromoDeskRequest,com.dianping.pay.api.beans.DiscountRules,java.util.Map)");
        if (getPromoDeskRequest.getUserId() > 0 && discountRules.isCanUseHongBao()) {
            UserAccountStatisticsDTO accountStatistics = userAccountBiz.getAccountStatistics(getPromoDeskRequest.getUserId() , AccountCatalog.RED_ENVELOPE);
            if (accountStatistics != null) {
                BigDecimal hbAmount = accountStatistics.getCatalogBalance(AccountCatalog.RED_ENVELOPE.getCode());
                if (hbAmount != null && hbAmount.signum() == 1) {
                    HongBaoPromoTool hongBaoPromoTool = new HongBaoPromoTool();
                    hongBaoPromoTool.setCanUse(true);
                    hongBaoPromoTool.setBalance(hbAmount.doubleValue());
                    hongBaoPromoTool.setProductCodes(new ArrayList<Integer>(Maps.filterEntries(productCodeDiscountRule, new Predicate<Map.Entry<Integer, DiscountRules>>() {
                        @Override
                        public boolean apply(Map.Entry<Integer, DiscountRules> entry) {
                            return entry.getKey() != ProductCode.PRIVATEBOOKING.getCode() // 预订不返回现金券
                                    && entry.getValue().isCanUseHongBao();
                        }
                    }).keySet()));

                    PromoToolDTO promoToolDTO = new PromoToolDTO();
                    promoToolDTO.setNewId(String.valueOf(getPromoDeskRequest.getUserId()));
                    promoToolDTO.setType(PromoToolType.HONGBAO.getCode());
                    promoToolDTO.setProductCodeList(hongBaoPromoTool.getProductCodes());
                    promoToolDTO.setUserId(getPromoDeskRequest.getUserId());
                    PromoQueryResponse<List<String>> response = payPromoDeskQueryService.encryptPromoTool(Lists.newArrayList(promoToolDTO), null);
                    if (response.isSuccess() && CollectionUtils.isNotEmpty(response.getResult())) {
                        hongBaoPromoTool.setPromoCipher(response.getResult().get(0));
                        return hongBaoPromoTool;
                    }
                }
            }
        }
        return null;
    }

    @Override
    public PointPromoTool assemblePointPromoTool(GetPromoDeskRequest getPromoDeskRequest, DiscountRules discountRules) {
        Cat.logEvent("INVALID_METHOD_2", "com.dianping.pay.api.biz.promodesk.CommonPromoDeskStrategy.assemblePointPromoTool(com.dianping.pay.api.beans.GetPromoDeskRequest,com.dianping.pay.api.beans.DiscountRules)");
        return null;
    }

    @Override
    public GiftCardPromoTool assembleGiftCardPromoTool(GetPromoDeskRequest getPromoDeskRequest, DiscountRules discountRules) {
        Cat.logEvent("INVALID_METHOD_2", "com.dianping.pay.api.biz.promodesk.CommonPromoDeskStrategy.assembleGiftCardPromoTool(com.dianping.pay.api.beans.GetPromoDeskRequest,com.dianping.pay.api.beans.DiscountRules)");
        return null;
    }

    @Override
    public String assemblePromptMsg(GetPromoDeskRequest getPromoDeskRequest, DiscountRules discountRules) {
        Cat.logEvent("INVALID_METHOD_1", "com.dianping.pay.api.biz.promodesk.CommonPromoDeskStrategy.assemblePromptMsg(com.dianping.pay.api.beans.GetPromoDeskRequest,com.dianping.pay.api.beans.DiscountRules)");
        return null;
    }
}
