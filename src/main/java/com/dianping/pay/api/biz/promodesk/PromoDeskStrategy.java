package com.dianping.pay.api.biz.promodesk;

import com.dianping.pay.api.beans.DiscountRules;
import com.dianping.pay.api.beans.GetPromoDeskRequest;
import com.dianping.pay.api.entity.promodesk.*;

import java.util.Map;

public interface PromoDeskStrategy {
    DiscountPromoTool assembleDiscountPromoTool(GetPromoDeskRequest getPromoDeskRequest);
    CouponPromoTool assembleCouponPromoTool(GetPromoDeskRequest getPromoDeskRequest, DiscountRules discountRules);
    CouponPromoTool assembleShopCouponPromoTool(GetPromoDeskRequest getPromoDeskRequest, DiscountRules discountRules);
    HongBaoPromoTool assembleHongBaoPromoTool(GetPromoDeskRequest getPromoDeskRequest, DiscountRules discountRules, Map<Integer, DiscountRules> productCodeDiscountRule);
    PointPromoTool assemblePointPromoTool(GetPromoDeskRequest getPromoDeskRequest, DiscountRules discountRules);
    GiftCardPromoTool assembleGiftCardPromoTool(GetPromoDeskRequest getPromoDeskRequest, DiscountRules discountRules);
    String assemblePromptMsg(GetPromoDeskRequest getPromoDeskRequest, DiscountRules discountRules);
}
