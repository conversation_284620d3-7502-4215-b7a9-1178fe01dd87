package com.dianping.pay.api.biz.activity.newloader.promo;

import com.dianping.cat.Cat;
import com.dianping.mobile.framework.datatypes.IMobileContext;
import com.dianping.pay.api.beans.IssueCouponActivity;
import com.dianping.pay.api.biz.activity.newloader.dto.CouponActivityContext;
import com.dianping.pay.api.util.DotUtils;
import com.dianping.unified.coupon.manage.api.UnifiedCouponListService;
import com.dianping.unified.coupon.manage.api.dto.UnifiedCouponDTO;
import com.dianping.unified.coupon.manage.api.dto.UnifiedCouponListOption;
import com.dianping.unified.coupon.manage.api.dto.UnifiedCouponQueryOption;
import com.dianping.unified.coupon.manage.api.request.UnifiedCouponQueryByTypeRequest;
import com.dianping.unified.coupon.manage.api.response.UnifiedCouponManageResponse;
import com.sankuai.mpmkt.coupon.userinstance.api.enums.UnifiedCouponUserSource;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;
import java.util.concurrent.Callable;
import java.util.concurrent.Future;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @date 2021/1/19
 */
@Component("userCouponProcessor")
public class UserCouponProcessor extends AbstractPromoProcessor {

    public static final Logger log = LoggerFactory.getLogger(UserCouponProcessor.class);

    @Resource
    private UnifiedCouponListService unifiedCouponListService;

    @Override
    public void prePare(final CouponActivityContext promoCtx, IMobileContext iMobileContext) {
        try {
            if (!promoCtx.isNeedUserPromotion()) {
                return;
            }
            DotUtils.dotForPromoProcessor(promoCtx, getPromoName());
            Future<List<UnifiedCouponDTO>> future = ExecutorService.submit(new Callable<List<UnifiedCouponDTO>>() {
                @Override
                public List<UnifiedCouponDTO> call() throws Exception {
                    return queryUserCoupon(promoCtx);
                }
            });
            promoCtx.setUserCouponPromotionFuture(future);
        } catch (Exception e) {
            log.error("UserCouponProcessor# prepare error. ctx: {}", promoCtx, e);
        }
    }

    @Override
    public void loadPromo(CouponActivityContext promoCtx) {
        try {
            if (promoCtx.getUnifiedCouponDTOS() != null) {
                return;
            }
            Future<List<UnifiedCouponDTO>> userCouponPromotionFuture = promoCtx.getUserCouponPromotionFuture();
            if (userCouponPromotionFuture != null) {
                promoCtx.setUnifiedCouponDTOS(userCouponPromotionFuture.get(1000, TimeUnit.MILLISECONDS));
            }
        } catch (Exception e) {
            log.error("UserCouponProcessor# loadPromo error. ctx: {}", promoCtx, e);
        }
    }

    @Override
    public String getPromoName() {
        Cat.logEvent("INVALID_METHOD_2", "com.dianping.pay.api.biz.activity.newloader.promo.UserCouponProcessor.getPromoName()");
        return "userCoupon";
    }

    private List<UnifiedCouponDTO> queryUserCoupon(CouponActivityContext promoCtx) {

        UnifiedCouponQueryByTypeRequest unifiedCouponQueryByTypeRequest = new UnifiedCouponQueryByTypeRequest();
        unifiedCouponQueryByTypeRequest.setUserId(promoCtx.isMt() ? promoCtx.getMtUserId() : promoCtx.getDpUserId());
        unifiedCouponQueryByTypeRequest.setUserType(promoCtx.isMt() ? UnifiedCouponUserSource.meituan.getCode() : UnifiedCouponUserSource.dianping.getCode());
        UnifiedCouponListOption listOption = new UnifiedCouponListOption();
        listOption.setFilterType(4);
        unifiedCouponQueryByTypeRequest.setListOption(listOption);
        UnifiedCouponQueryOption queryOption = new UnifiedCouponQueryOption();
        queryOption.setFormatTitle(true);
        unifiedCouponQueryByTypeRequest.setQueryOption(queryOption);
        UnifiedCouponManageResponse<List<UnifiedCouponDTO>> unifiedCouponListResponse = unifiedCouponListService.queryCouponByType(unifiedCouponQueryByTypeRequest);
        if (unifiedCouponListResponse == null || !unifiedCouponListResponse.isSuccess() || unifiedCouponListResponse.getResult() == null) {
            log.warn("TgcCouponProcessor# getActivityDTOS error. promoCtx: {}, unifiedCouponListResponse: {}", promoCtx, unifiedCouponListResponse);
            return null;
        }
        return unifiedCouponListResponse.getResult();
    }
}
