package com.dianping.pay.api.biz.activity.newloader.promo;


import com.dianping.mobile.framework.datatypes.IMobileContext;
import com.dianping.pay.api.biz.activity.newloader.dto.CouponActivityContext;
import com.dianping.pay.api.enums.QueryShopCouponTypeEnum;
import com.dianping.pay.api.service.impl.ProductService;
import com.dianping.pay.api.util.*;
import com.dianping.pay.promo.common.enums.ProductType;
import com.dianping.pay.promo.common.enums.promo.DisplayScene;
import com.dianping.pay.promo.display.api.PromoDisplayService;
import com.dianping.pay.promo.display.api.dto.Product;
import com.dianping.pay.promo.display.api.dto.PromoDisplayDTO;
import com.dianping.pay.promo.display.api.dto.QueryPromoDisplayRequest;
import com.dianping.pay.promo.display.api.dto.request.ReturnControl;
import com.dianping.pay.promo.rule.api.dto.Response;
import com.sankuai.dealuser.price.display.api.PriceDisplayService;
import com.sankuai.dealuser.price.display.api.enums.ProductTypeEnum;
import com.sankuai.dealuser.price.display.api.enums.RequestSceneEnum;
import com.sankuai.dealuser.price.display.api.model.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.List;
import java.util.concurrent.Callable;
import java.util.concurrent.Future;
import java.util.concurrent.TimeUnit;

@Component("reductionPromotionProcessor")
@Slf4j
public class ReductionPromotionProcessor extends AbstractPromoProcessor {

    @Resource
    private ProductService productService;
    @Resource
    private PromoDisplayService promoDisplayService;
    @Resource
    private PriceDisplayService priceDisplayService;


    //丽人新样式立减
    private static final int BEAUTY_PROMO_TEMP_ID = 272;
    //全部立减 （老）
    private static final int PROMO_TEMP_ID = 239;
    //全部立减 (新)
    private static final int NEW_PROMO_TEMP_ID = 267;
    //闲时立减
    private static final int DISCOUNT_PROMO_TEMP_ID = 18;

    @Autowired
    private LogUtils logUtils;

    @Override
    public void prePare(final CouponActivityContext promoCtx, final IMobileContext iMobileContext) {
        try {
            if (!promoCtx.isNeedReductionPromotion()) {
                return;
            }
            if (promoCtx.getQueryShopCouponType() != QueryShopCouponTypeEnum.BY_DEAL.getCode() && promoCtx.getQueryShopCouponType() != QueryShopCouponTypeEnum.BY_SPU_PRODUCT.getCode() && promoCtx.getQueryShopCouponType() != QueryShopCouponTypeEnum.BY_SKU.getCode() ) {
                return;
            }
            DotUtils.dotForPromoProcessor(promoCtx, getPromoName());
            Future<Pair<List<PromoDisplayDTO>, List<PromoDisplayDTO>>> future = ExecutorService.submit(new Callable<Pair<List<PromoDisplayDTO>, List<PromoDisplayDTO>>>() {
                @Override
                public Pair<List<PromoDisplayDTO>, List<PromoDisplayDTO>> call() throws Exception {
                    return getReduction(promoCtx, iMobileContext);
                }
            });
            promoCtx.setReductionPromotionFuture(future);
        } catch (Exception e) {
            log.error("reductionPromotionProcessor# prepare error. ctx: {}", promoCtx, e);
        }
    }

    @Override
    public void loadPromo(CouponActivityContext promoCtx) {
        try {
            Future<Pair<List<PromoDisplayDTO>, List<PromoDisplayDTO>>> reductionPromotionFuture = promoCtx.getReductionPromotionFuture();
            if (reductionPromotionFuture != null) {
                promoCtx.setReductionPromotions(reductionPromotionFuture.get(500, TimeUnit.MILLISECONDS));
            }
        } catch (Exception e) {
            log.error("reductionPromotionProcessor# loadPromo error. ctx: {}", promoCtx, e);
        }
    }

    @Override
    public String getPromoName() {
        return "reduction";
    }

    private Pair<List<PromoDisplayDTO>, List<PromoDisplayDTO>> getReduction(CouponActivityContext ctx, IMobileContext iMobileContext) {
        try {
            List<PromoDisplayDTO> promoDisplayDTOS = null;
            List<PromoDisplayDTO> discountPromoDisplayDTOS = null;
            BigDecimal spuPrice = null;
            if (ctx.getQueryShopCouponType() == QueryShopCouponTypeEnum.BY_SPU_PRODUCT.getCode()) {
                int productType = ctx.isMt() ? ProductType.mt_generalCard.getValue() : ProductType.generalCard.getValue();
                spuPrice = getSpuProductPrice(ctx, productType);
            }

            // 泛商品场景目前不需要闲时立减
            DisplayScene displayScene = ctx.getQueryShopCouponType() == QueryShopCouponTypeEnum.BY_SKU.getCode() ? DisplayScene.BUYCAR_COUDAN_DISCOUNT : DisplayScene.ALL_PROMO;
            QueryPromoDisplayRequest request = buildPromoReq(ctx, iMobileContext, getPromoTempId(ctx), displayScene, false, spuPrice);
            promoDisplayDTOS = callPromoDisplay(request, ctx);

            QueryPromoDisplayRequest request1 = buildPromoReq(ctx, iMobileContext, DISCOUNT_PROMO_TEMP_ID,
                    DisplayScene.NO_IDLETIMES_PROMO, null, spuPrice);
            discountPromoDisplayDTOS = callPromoDisplay(request1, ctx);
            //打点预付查询的立减信息
            if (ctx.getQueryShopCouponType() == QueryShopCouponTypeEnum.BY_SKU.getCode()) {
                DotUtils.dotForReduction(promoDisplayDTOS);
            }
            return Pair.of(promoDisplayDTOS, discountPromoDisplayDTOS);
        } catch (Exception e) {
            log.error("getReduction  error. ctx: {}", ctx, e);
        }
        return null;
    }

    private List<PromoDisplayDTO> callPromoDisplay(QueryPromoDisplayRequest request, CouponActivityContext ctx) {
        Response<List<PromoDisplayDTO>> promoResp = promoDisplayService.queryPromoDisplayDTO(request);
        logUtils.logInfo("ReductionPromotionProcessor# call queryPromoDisplayDTO. input: {}, resp: {}", JsonUtils.toJson(request), JsonUtils.toJson(promoResp));
        if (promoResp != null && promoResp.isSuccess()) {
            List<PromoDisplayDTO> promoDisplayDTOS = promoResp.getResult();
            boolean hit = CollectionUtils.isNotEmpty(promoDisplayDTOS);
            DotUtils.dotForPromoHit(getPromoName(), ctx.isMt(), hit);
            return promoDisplayDTOS;
        } else {
            DotUtils.dotForPromoCallFail(getPromoName(), "queryPromoDisplayDTO", ctx.isMt());
            log.warn("queryPromoDisplayDTO failed. input: {}, resp: {}", request, promoResp);
        }
        return null;
    }

    private QueryPromoDisplayRequest buildPromoReq(CouponActivityContext ctx, IMobileContext iMobileContext, int templateId,
                                                   DisplayScene displayScene, Boolean merge, BigDecimal spuPrice) {
        QueryPromoDisplayRequest request = new QueryPromoDisplayRequest();
        if (iMobileContext != null) {
            request.setClientVersion(iMobileContext.getVersion());
        }
        // 设置payPlatform
        request.setPlatform(ctx.getPayPlatform());
        request.setDisplayScene(displayScene.getCode());
        if (merge != null) {
            request.setMergeDisplayScenePromo(merge);
        }
        Product product = new Product();

        if (ctx.getQueryShopCouponType() == QueryShopCouponTypeEnum.BY_DEAL.getCode()) {
            if (ctx.isMt()) {
                product.setProductId(ctx.getMtDealGroupId());
                request.setProductType(ProductType.mt_tuangou.getValue());
            } else {
                product.setProductId(ctx.getDpDealGroupId());
                request.setProductType(ProductType.tuangou.getValue());
            }
            if (ctx.getDealGroupBaseDTO() != null) {
                product.setPrice(ctx.getDealGroupBaseDTO().getDealGroupPrice());
            }
        } else if (ctx.getQueryShopCouponType() == QueryShopCouponTypeEnum.BY_SPU_PRODUCT.getCode()) {
            product.setSpuGroupId(ctx.getSpuGroupId());
            int productType = ctx.isMt() ? ProductType.mt_generalCard.getValue() : ProductType.generalCard.getValue();
            request.setProductType(productType);
            product.setPrice(spuPrice);
        } else if (ctx.getQueryShopCouponType() == QueryShopCouponTypeEnum.BY_SKU.getCode()) {
            product.setProductId(ctx.getSkuId());
            if (ctx.isMt()) {
                request.setProductType(ProductType.mt_generalTrade.getValue());
            } else {
                request.setProductType(ProductType.generalTrade.getValue());
            }
            if (ctx.getProductDetailDO() != null) {
                product.setPrice(ctx.getProductDetailDO().getPrice());
            }
        }

        if (ctx.isMt()) {
            product.setLongShopId(ctx.getMtShopIdL());
        } else {
            product.setLongShopId(ctx.getDpShopIdL());
        }

        request.setProduct(product);

        ReturnControl returnControl = new ReturnControl();
        returnControl.setReturnComposition(true);
        request.setReturnControl(returnControl);
        request.setTemplateID(templateId);

        long userId;
        if (ctx.isMt()) {
            userId = ctx.getMtUserId();
        } else {
            userId = ctx.getDpUserId();
        }
        request.setUserId(userId);
        request.setCityId(ctx.getCityId());
        request.setDpId(ctx.getDpid());
        return request;
    }

    private BigDecimal getSpuProductPrice(CouponActivityContext ctx, int productType) {
        try {
            PriceRequest request = new PriceRequest();
            ProductIdentity identity = new ProductIdentity(ctx.getSpuGroupId(), ProductTypeEnum.SPU.getType());
            request.setIdentity(identity);
            ClientEnv clientEnv = new ClientEnv();
            clientEnv.setCityId(ctx.getCityId());
            clientEnv.setClientType(ClientTypeUtils.getDealClientType(ctx.getCouponPlatform(), ctx.isMt()));
            request.setClientEnv(clientEnv);
            request.setScene(RequestSceneEnum.DETAIL.getScene());
            request.setUserId(ctx.getUserId());
            PriceResponse<PriceDisplayDTO> response = priceDisplayService.queryPrice(request);
            logUtils.logInfo("ReductionPromotionProcessor# call queryPrice. request: {}, response: {}", JsonUtils.toJson(request), JsonUtils.toJson(response));
            if (response == null || !response.isSuccess() || response.getData() == null) {
                log.error("ReductionPromotionProcessor# call queryPrice exception. request: {}, response: {}", request, response);
                return null;
            }
            return response.getData().getBasePrice();
        } catch (Exception e) {
            log.error("ReductionPromotionProcessor# call queryPrice execption. e", e);
            return null;
        }
    }

    private static int getPromoTempId(CouponActivityContext promoCtx) {
        if (promoCtx.getBeautyDealNewType() != null && Boolean.TRUE.equals(promoCtx.getBeautyDealNewType())) {
            return BEAUTY_PROMO_TEMP_ID;
        }
        return ReductionUtils.isNewPromoStyle(promoCtx.getQueryShopCouponType(), promoCtx.getDpDealGroupId(), promoCtx.isMt(), promoCtx.getShopDTO()) ? NEW_PROMO_TEMP_ID : PROMO_TEMP_ID;
    }
}
