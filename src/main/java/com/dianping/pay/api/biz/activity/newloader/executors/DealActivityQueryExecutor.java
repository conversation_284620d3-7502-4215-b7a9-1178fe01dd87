package com.dianping.pay.api.biz.activity.newloader.executors;

import com.dianping.deal.idmapper.api.DealIdMapperService;
import com.dianping.deal.idmapper.api.dto.IdMapper;
import com.dianping.pay.api.beans.IssueCouponActivity;
import com.dianping.pay.api.biz.activity.newloader.AbstractCouponIssueActivityQueryExecutor;
import com.dianping.pay.api.entity.issuecoupon.IssueCouponRequest;
import com.dianping.spc.common.Response;
import com.dianping.tgc.open.DealGroupActivityQueryRemoteService;
import com.dianping.tgc.open.entity.ActivityDTO;
import com.dianping.tgc.open.entity.DealGroupActivityBatchQueryRequest;
import com.dianping.tgc.open.entity.DealGroupActivityQueryResponseDTO;
import com.dianping.tgc.open.enums.QuerySourceEnum;
import com.google.common.collect.Lists;
import org.apache.commons.collections.MapUtils;

import java.util.List;
import java.util.Map;

public class DealActivityQueryExecutor extends AbstractCouponIssueActivityQueryExecutor {

    private DealGroupActivityQueryRemoteService dealGroupActivityQueryRemoteService;
    private DealIdMapperService dealIdMapperService;

    public DealActivityQueryExecutor(IssueCouponRequest request) {
        super(request);
    }

    @Override
    protected List<IssueCouponActivity> loadFromRemote() {
        if (request.getProductId() == 0) {
            return Lists.newArrayList();
        }
        List<Integer> dpDealGroupIdList = null;
        if (request.isDpClient()) {
            dpDealGroupIdList = Lists.newArrayList(request.getProductId());
        } else {
            IdMapper idMapper = dealIdMapperService.queryByMtDealGroupId(request.getProductId());
            if (idMapper != null) {
                dpDealGroupIdList = Lists.newArrayList(idMapper.getDpDealGroupID());
            }
        }
        DealGroupActivityBatchQueryRequest remoteRequest = new DealGroupActivityBatchQueryRequest(dpDealGroupIdList, request.isDpClient() ? QuerySourceEnum.DP.getValue() : QuerySourceEnum.MT.getValue());
        Response<DealGroupActivityQueryResponseDTO> response = dealGroupActivityQueryRemoteService.queryActivityDTOsByDealGroupIds(remoteRequest);
        if (!response.isSuccess() || response.getResult() == null) {
            return Lists.newArrayList();
        }
        Map<Integer, List<ActivityDTO>> dealGroupActivityMap = response.getResult().getDealGroupActivityMap();
        if (MapUtils.isEmpty(dealGroupActivityMap)) {
            return Lists.newArrayList();
        }
        return toIssueCouponActivities(dealGroupActivityMap);
    }

    public DealActivityQueryExecutor setDealGroupActivityQueryRemoteService(DealGroupActivityQueryRemoteService dealGroupActivityQueryRemoteService) {
        this.dealGroupActivityQueryRemoteService = dealGroupActivityQueryRemoteService;
        return this;
    }

    public DealActivityQueryExecutor setDealIdMapperService(DealIdMapperService dealIdMapperService) {
        this.dealIdMapperService = dealIdMapperService;
        return this;
    }

}
