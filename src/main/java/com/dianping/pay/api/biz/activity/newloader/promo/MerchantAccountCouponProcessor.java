package com.dianping.pay.api.biz.activity.newloader.promo;

import com.dianping.api.common.enums.PageSourceEnum;
import com.dianping.api.util.LionQueryUtils;
import com.dianping.cat.Cat;
import com.dianping.deal.base.dto.DealGroupBaseDTO;
import com.dianping.gmkt.coupon.common.api.enums.CouponBusiness;
import com.dianping.gmkt.coupon.common.api.enums.CouponCommonTagEnum;
import com.dianping.gmkt.coupon.common.api.enums.ProductTypeLimitEnum;
import com.dianping.lion.client.ConfigEvent;
import com.dianping.lion.client.ConfigListener;
import com.dianping.lion.client.Lion;
import com.dianping.mobile.framework.datatypes.IMobileContext;
import com.dianping.pay.api.beans.ProductDetailDO;
import com.dianping.pay.api.biz.activity.newloader.dto.CouponActivityContext;
import com.dianping.pay.api.enums.QueryShopCouponTypeEnum;
import com.dianping.pay.api.util.DotUtils;
import com.dianping.pay.api.util.LionConstants;
import com.dianping.unified.coupon.manage.api.response.UnifiedCouponManageResponse;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.meituan.mtrace.Tracer;
import com.sankuai.mpmkt.coupon.search.api.SearchCouponService;
import com.sankuai.mpmkt.coupon.search.api.dto.CouponDesc;
import com.sankuai.mpmkt.coupon.search.api.enums.CouponDescGeneralKeyEnum;
import com.sankuai.mpmkt.coupon.search.api.enums.CustomProperty;
import com.sankuai.mpmkt.coupon.search.api.enums.GeneralProperty;
import com.sankuai.mpmkt.coupon.search.api.enums.PreviewQueryStrategy;
import com.sankuai.mpmkt.coupon.search.api.request.SearchCouponRequest;
import com.sankuai.mpmkt.coupon.search.api.response.SearchAllCouponResponse;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.concurrent.Callable;
import java.util.concurrent.Future;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2021/1/19
 */
@Component("merchantAccountCouponProcessor")
public class MerchantAccountCouponProcessor extends AbstractPromoProcessor {

    public static final Logger log = LoggerFactory.getLogger(MerchantAccountCouponProcessor.class);

    private static boolean setCellSwitch;

    @Resource
    private SearchCouponService searchCouponService;

    static {
        setCellSwitch = Lion.getBooleanValue(LionConstants.MERCHANT_ACCOUNT_COUPON_SET_CELL_SWITCH, true);
        Lion.addConfigListener(LionConstants.MERCHANT_ACCOUNT_COUPON_SET_CELL_SWITCH, new ConfigListener() {
            @Override
            public void configChanged(ConfigEvent configEvent) {
                setCellSwitch = Lion.getBooleanValue(LionConstants.MERCHANT_ACCOUNT_COUPON_SET_CELL_SWITCH, true);
            }
        });
    }

    @Override
    public void prePare(final CouponActivityContext promoCtx, IMobileContext iMobileContext) {
        try {
            if ((!promoCtx.isNeedMerchantAccountPromotion() && !PageSourceEnum.BeautyMedicalPOIDetail.code.equals(promoCtx.getCouponPageSource())) || getUserId(promoCtx) <= 0) {
                return;
            }
            DotUtils.dotForPromoProcessor(promoCtx, getPromoName());
            Future<Pair<List<CouponDesc>, List<CouponDesc>>> future = ExecutorService.submit(new Callable<Pair<List<CouponDesc>, List<CouponDesc>>>() {
                @Override
                public Pair<List<CouponDesc>, List<CouponDesc>> call() throws Exception {
                    return searchCoupon(promoCtx);
                }
            });
            promoCtx.setMerchantAccountCouponPromotionFuture(future);
        } catch (Exception e) {
            log.error("UserCouponProcessor# prepare error. ctx: {}", promoCtx, e);
        }
    }

    private long getUserId(final CouponActivityContext promoCtx) {
        return promoCtx.isMt() ? promoCtx.getMtUserId() : promoCtx.getDpUserId();
    }

    private Pair<List<CouponDesc>, List<CouponDesc>> searchCoupon(CouponActivityContext promoCtx) {
        List<CouponDesc> noIssuedResult = Lists.newArrayList();
        List<CouponDesc> issuedResult = Lists.newArrayList();
        SearchCouponRequest req = new SearchCouponRequest();
        req.setClientAppKey(Tracer.getAppKey());
        Map<Long, Map<CustomProperty, String>> customProps = Maps.newHashMap();
        req.setCustomProps(customProps);
        long shopId;
        if (promoCtx.isMt()) {
            shopId = promoCtx.getMtShopIdL();
        } else {
            shopId = promoCtx.getDpShopIdL();
        }
        Map<CustomProperty, String> shopIdCustomPropertyMap = Maps.newHashMap();
        customProps.put(shopId, shopIdCustomPropertyMap);
        shopIdCustomPropertyMap.put(CustomProperty.POIID, String.valueOf(shopId));
//        long productId = getProductId(promoCtx);
//        if (productId > 0) {
//            if (CouponBusiness.isPrepay(promoCtx.getProductCode())) {
//                shopIdCustomPropertyMap.put(CustomProperty.SKUID, String.valueOf(shopId));
//            }else {
//                shopIdCustomPropertyMap.put(CustomProperty.PRODUCTID, String.valueOf(shopId));
//            }
//        }

        shopIdCustomPropertyMap.put(CustomProperty.CITYID, String.valueOf(promoCtx.getCityId()));
        shopIdCustomPropertyMap.put(CustomProperty.PREVIEW_QUERY_STRATEGY, String.valueOf(PreviewQueryStrategy.SIEVE_W.code));
        Map<GeneralProperty, String> generalProps = Maps.newHashMap();
        generalProps.put(GeneralProperty.PLATFORM, String.valueOf(promoCtx.getCouponPlatform()));
        generalProps.put(GeneralProperty.USERTYPE, String.valueOf(promoCtx.isMt() ? 1 : 0));
        if (promoCtx.isMt()) {
            generalProps.put(GeneralProperty.USERID, String.valueOf(getUserId(promoCtx)));
        }else{
            generalProps.put(GeneralProperty.DPUSERID, String.valueOf(getUserId(promoCtx)));
        }
        if (PageSourceEnum.BeautyMedicalPOIDetail.code.equals(promoCtx.getCouponPageSource())) {
            generalProps.put(GeneralProperty.COUPONBUSINESS, String.valueOf(promoCtx.isMt()?CouponBusiness.MT_PREPAY.getCode():CouponBusiness.DP_PREPAY.getCode()));
        } else {
            //默认是团购
            generalProps.put(GeneralProperty.COUPONBUSINESS, String.valueOf(CouponBusiness.DZ_TUANGOU.getCode()));
            req.setOriginProductType(ProductTypeLimitEnum.LIMIT_DZ.getCode());
        }
        generalProps.put(GeneralProperty.DPID, String.valueOf(promoCtx.getDpid()));
        generalProps.put(GeneralProperty.CITYID, String.valueOf(promoCtx.getCityId()));
        generalProps.put(GeneralProperty.RET_GENERAL_COUPON, String.valueOf(true));

        req.setGeneralProps(generalProps);
        req.setReturnPreviewCoupon(true);
        req.setScene("daozongPoiIssueCouponComponent");
        String sourceCell = Tracer.getCell();
        try {
            if (setCellSwitch) {
                Tracer.setCell("gray-release-nibmkt-coupon-searchsh");
            }
            UnifiedCouponManageResponse<SearchAllCouponResponse> resp = searchCouponService.searchAvailableAndPreview(req);
            if (!resp.isSuccess()) {
                log.error("searchAvailableAndPreview error:" + resp.getResultCode());
                return Pair.of(noIssuedResult, issuedResult);
            }
            if (resp.getResult() == null || MapUtils.isEmpty(resp.getResult().getCouponDescMap())) {
                return Pair.of(noIssuedResult, issuedResult);
            }
            boolean isBeautyMedicalPOIDetailPage = PageSourceEnum.BeautyMedicalPOIDetail.code.equals(promoCtx.getCouponPageSource());
            if (MapUtils.isNotEmpty(resp.getResult().getAllPreviewOptimalCoupon())) {
                noIssuedResult = resp.getResult().getAllPreviewOptimalCoupon().get(shopId).stream().map(couponId -> resp.getResult().getCouponDescMap().get(couponId))
                        .filter(item -> (LionQueryUtils.PREPAY_LQG_RAINBOW_DEGRADE_SWITCH && isBeautyMedicalPOIDetailPage) || isMerchantAccountCoupon(item))
                        .collect(Collectors.toList());
            }
            if (MapUtils.isNotEmpty(resp.getResult().getAllIssuedPreviewOptimalCoupon())) {
                issuedResult = resp.getResult().getAllIssuedPreviewOptimalCoupon().get(shopId).stream().map(couponId -> resp.getResult().getCouponDescMap().get(couponId))
                        .filter(item -> (LionQueryUtils.PREPAY_LQG_RAINBOW_DEGRADE_SWITCH && isBeautyMedicalPOIDetailPage) || isMerchantAccountCoupon(item))
                        .collect(Collectors.toList());
            }
        } catch (Exception e) {
            log.error("searchCouponService.searchAvailableAndPreview exception", e);
        } finally {
            if (setCellSwitch || sourceCell == null) {
                Tracer.clearContext(Tracer.CELL);
            } else {
                Tracer.setCell(sourceCell);
            }
        }
        return Pair.of(noIssuedResult, issuedResult);
    }

    private long getProductId(CouponActivityContext ctx) {
        Cat.logEvent("INVALID_METHOD_5", "com.dianping.pay.api.biz.activity.newloader.promo.MerchantAccountCouponProcessor.getProductId(com.dianping.pay.api.biz.activity.newloader.dto.CouponActivityContext)");
        long productId = 0L;
        if (ctx.getQueryShopCouponType() == QueryShopCouponTypeEnum.BY_DEAL.getCode()) {
            DealGroupBaseDTO dealGroup = ctx.getDealGroupBaseDTO();
            if (dealGroup != null) {
                if (ctx.isMt()) {
                    productId = ctx.getMtDealGroupId();
                } else {
                    productId = ctx.getDpDealGroupId();
                }
            }
        } else {
            ProductDetailDO productDetailDO = ctx.getProductDetailDO();
            if (productDetailDO != null) {
                productId = ctx.getSkuId();
            }
        }
        // 除此之外都是异常情况
        return productId;
    }

    private boolean isMerchantAccountCoupon(CouponDesc couponDesc) {
        if (MapUtils.isEmpty(couponDesc.getGeneralDescMap())) {
            return false;
        }
        String commonTag = couponDesc.getGeneralDescMap().get(CouponDescGeneralKeyEnum.COUPON_COMMON_TAG.getKey());
        return CouponCommonTagEnum.MERCHANT_ACCOUNT_PLATFORM_COUPON.getCode().equals(commonTag);
    }

    @Override
    public void loadPromo(CouponActivityContext promoCtx) {
        try {
            if (promoCtx.getUnifiedCouponDTOS() != null) {
                return;
            }
            Future<Pair<List<CouponDesc>, List<CouponDesc>>> platformCouponPromotionFuture = promoCtx.getMerchantAccountCouponPromotionFuture();
            if (platformCouponPromotionFuture != null) {
                promoCtx.setMerchantAccountCouponPromotion(platformCouponPromotionFuture.get(200, TimeUnit.MILLISECONDS));
            }
        } catch (Exception e) {
            log.error("PlatformCouponProcessor# loadPromo error. ctx: {}", promoCtx, e);
        }
    }

    @Override
    public String getPromoName() {
        Cat.logEvent("INVALID_METHOD_5", "com.dianping.pay.api.biz.activity.newloader.promo.MerchantAccountCouponProcessor.getPromoName()");
        return "platformCoupon";
    }

}
