package com.dianping.pay.api.biz.activity.newloader.unified.executor;

import com.dianping.deal.idmapper.api.DealIdMapperService;
import com.dianping.pay.api.beans.IssueCouponActivity;
import com.dianping.pay.api.biz.activity.newloader.AbstractUnifiedCouponActivityQueryExecutor;
import com.dianping.pay.api.biz.activity.newloader.dto.CouponIssueActivityQueryContext;
import com.dianping.pay.api.biz.activity.newloader.mapper.IssueActivityMapper;
import com.dianping.spc.common.Response;
import com.dianping.tgc.open.SkuActivityQueryRemoteService;
import com.dianping.tgc.open.entity.*;
import com.dianping.tgc.open.v2.TGCActivityBizQueryService;
import com.google.common.collect.Lists;
import org.apache.commons.collections.MapUtils;
import org.springframework.beans.factory.annotation.Autowired;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * Created by drintu on 18/6/14.
 */

public class UnifiedSpuActivityQueryExecutor extends AbstractUnifiedCouponActivityQueryExecutor{

    private TGCActivityBizQueryService tgcActivityBizQueryService;

    public UnifiedSpuActivityQueryExecutor(CouponIssueActivityQueryContext context) {
        super(context);
    }

    @Override
    protected List<IssueCouponActivity> loadFromRemote() {
        if (queryContext.getProductId() == 0) {
            return Lists.newArrayList();
        }
        SpuActivityBatchQueryRequest remoteRequest = new SpuActivityBatchQueryRequest();
        int spuid = queryContext.getProductId();
        remoteRequest.setSpuIds(Lists.newArrayList(spuid));
        if (queryContext.isDpClient()) {
            remoteRequest.setSource(1);
        } else {
            remoteRequest.setSource(2);
        }
        ActivityQueryOption queryOption = new ActivityQueryOption();
        queryOption.setFillDiscountCoupon(true);
        remoteRequest.setQueryOption(queryOption);
        Response<SpuActivityQueryResponseDTO> response = tgcActivityBizQueryService.queryActivityDTOsBySpuIds(remoteRequest);
        if (!response.isSuccess() || response.getResult() == null) {
            return Lists.newArrayList();
        }
        Map<Integer, List<ActivityDTO>> spuActivityMap = response.getResult().getSpuActivityMap();
        if (MapUtils.isEmpty(spuActivityMap)) {
            return Lists.newArrayList();
        }
        List<ActivityDTO> activities = new ArrayList<>();
        for (Map.Entry<Integer, List<ActivityDTO>> activity : spuActivityMap.entrySet()) {
            activities.addAll(activity.getValue());
        }
        return IssueActivityMapper.activities2IssueActivities(activities,queryContext.isDpClient());
    }

    public UnifiedSpuActivityQueryExecutor setSpuActivityQueryRemoteService(TGCActivityBizQueryService tgcActivityBizQueryService) {
        this.tgcActivityBizQueryService = tgcActivityBizQueryService;
        return this;
    }

}
