package com.dianping.pay.api.biz.activity.newloader.mapper;

import com.dianping.cat.Cat;
import com.dianping.cat.CatConstants;
import com.dianping.gmkt.coupon.common.api.enums.CouponCommonTagEnum;
import com.dianping.gmkt.coupon.common.api.enums.CouponGroupValueType;
import com.dianping.gmkt.scene.api.delivery.dto.res.CouponDetailDTO;
import com.dianping.gmkt.scene.api.delivery.enums.RecallTypeEnum;
import com.dianping.lion.client.Lion;
import com.dianping.mobile.framework.datatypes.IMobileContext;
import com.dianping.pay.api.beans.IssueCouponActivity;
import com.dianping.pay.api.beans.ResourcePromotionDo;
import com.dianping.pay.api.biz.activity.newloader.dto.CouponActivityContext;
import com.dianping.pay.api.entity.issuecoupon.*;
import com.dianping.pay.api.enums.ChannelProductEnum;
import com.dianping.pay.api.enums.MerchantUserTypeEnum;
import com.dianping.pay.api.enums.PromoTypeEnum;
import com.dianping.pay.api.util.*;
import com.dianping.pay.promo.common.enums.PromoSource;
import com.dianping.pay.promo.common.utils.PromoConditionUtils;
import com.dianping.pay.promo.display.api.dto.EachPromoDetailComposition;
import com.dianping.pay.promo.display.api.dto.PromoComposition;
import com.dianping.pay.promo.display.api.dto.PromoDisplayDTO;
import com.google.common.collect.Lists;
import com.sankuai.dealuser.price.display.api.enums.PromoIdentityEnum;
import com.sankuai.dealuser.price.display.api.model.PromoDTO;
import com.sankuai.mpmkt.coupon.search.api.dto.CouponDesc;
import com.sankuai.nibmktproxy.queryclient.proxy.CouponDTO;
import com.sankuai.nibmktproxy.queryclient.proxy.GetPromotionDTO;
import com.sankuai.nibmktproxy.queryclient.proxy.PromotionDTOResult;
import edu.emory.mathcs.backport.java.util.Collections;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

public class ProductPromoInfoMapper {

    private static final Logger LOGGER = LogManager.getLogger(ProductPromoInfoMapper.class);

    public static ProductPromoInfo buildProductPromoInfo(CouponActivityContext context, IMobileContext iMobileContext) {
        ProductPromoInfo productPromoInfo = new ProductPromoInfo();
        ProductPromoDetailInfo productPromoDetailInfo = buildPromoAggInfo(context);
        ProductPromoConciseInfo conciseInfo = buildProductPromoConciseInfo(productPromoDetailInfo);

        if (context.getChannel() != null && ChannelProductEnum.getByDesc(context.getChannel()) == ChannelProductEnum.ODP) {
            conciseInfo = buildProductPromoConciseInfo4Channel(context);
        }

        if (conciseInfo == null) {
            return productPromoInfo;
        }

        productPromoInfo.setProductPromoDetailInfo(productPromoDetailInfo);
        productPromoInfo.setProductPromoConciseInfo(conciseInfo);
        return productPromoInfo;
    }

    private static ProductPromoConciseInfo buildProductPromoConciseInfo4Channel(CouponActivityContext context) {
        if (context.getPriceDisplayDTO() == null || CollectionUtils.isEmpty(context.getPriceDisplayDTO().getUsedPromos())) {
            return null;
        }
        List<PromoDTO> usedPromos = context.getPriceDisplayDTO().getUsedPromos();
        List<CouponTag> couponTags = Lists.newArrayListWithCapacity(usedPromos.size());
        for (PromoDTO usedPromo : usedPromos) {
            CouponTag couponTag = buildCouponTag(usedPromo);
            if (couponTag != null) {
                couponTags.add(couponTag);
            }
        }
        if (CollectionUtils.isEmpty(couponTags)) {
            return null;
        }
        ProductPromoConciseInfo promoConciseInfo = new ProductPromoConciseInfo();
        promoConciseInfo.setCouponTags(couponTags);
        return promoConciseInfo;
    }

    private static CouponTag buildCouponTag(PromoDTO usedPromo) {
        if (usedPromo == null || usedPromo.getAmount() == null) {
            return null;
        }
        CouponTag couponTag = new CouponTag();
        couponTag.setAmount(usedPromo.getAmount().toPlainString());
        if (usedPromo.getIdentity() != null && usedPromo.getIdentity().getPromoType() == com.sankuai.dealuser.price.display.api.enums.PromoTypeEnum.NORMAL_PROMO.getType()) {
            if (usedPromo.getIdentity().getSourceType() == 1) {
                //平台立减
                couponTag.setTag(Lion.get(LionConstants.CHANNEL_PRODUCT_PLAT_COUPON_TITLE, "平台补贴券"));
                couponTag.setBackgroundPicUrl(Lion.get(LionConstants.CHANNEL_PRODUCT_PLAT_COUPON_PIC_URL));
            } else if (usedPromo.getIdentity().getSourceType() == 2) {
                //商家立减
                couponTag.setTag(Lion.get(LionConstants.CHANNEL_PRODUCT_SHOP_COUPON_TITLE, "商家神券"));
                couponTag.setBackgroundPicUrl(Lion.get(LionConstants.CHANNEL_PRODUCT_SHOP_COUPON_PIC_URL));
            } else {
                return null;
            }
        } else {
            return null;
        }
        Date endTime = usedPromo.getEndTime();
        if (endTime == null) {
            couponTag.setCouponTimeDesc(Lion.get(LionConstants.CHANNEL_PRODUCT_END_TIME_DEFAULT_DESC, ""));
        } else {
            SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy/MM/dd");
            couponTag.setCouponTimeDesc(simpleDateFormat.format(endTime) + " 到期");
        }
        return couponTag;
    }

    private static ProductPromoConciseInfo buildProductPromoConciseInfo(ProductPromoDetailInfo productPromoDetailInfo) {
        if (productPromoDetailInfo == null || (CollectionUtils.isEmpty(productPromoDetailInfo.getMerchantCoupons()) && CollectionUtils.isEmpty(productPromoDetailInfo.getPlatformCoupons()) && CollectionUtils.isEmpty(productPromoDetailInfo.getBuycarDiscounts()))) {
            return null;
        }
        List<CouponDetailInfo> couponDetails = Lists.newArrayList();
        if (CollectionUtils.isNotEmpty(productPromoDetailInfo.getMerchantCoupons())) {
            Collections.sort(productPromoDetailInfo.getMerchantCoupons(), new CouponDetailInfoComparator());
            couponDetails.add(productPromoDetailInfo.getMerchantCoupons().get(0));
        }
        if (CollectionUtils.isNotEmpty(productPromoDetailInfo.getPlatformCoupons())) {
            Collections.sort(productPromoDetailInfo.getPlatformCoupons(), new CouponDetailInfoComparator());
            couponDetails.add(productPromoDetailInfo.getPlatformCoupons().get(0));
        }

        List<CouponTag> couponTags = Lists.newArrayList();
        for (CouponDetailInfo couponDetailInfo : couponDetails) {
            CouponTag couponTag = new CouponTag();
            /**
             * 1）商家券满XX减XX（有门槛券） / XX元无门槛券（无门槛）
             *
             * 2）平台券：运营配置的券名称
             */
            String title;
            if (couponDetailInfo.getCouponSrc() == 1) {
                if (couponDetailInfo.getPriceLimit() == null || BigDecimal.ZERO.compareTo(couponDetailInfo.getPriceLimit()) == 0) {
                    title = couponDetailInfo.getAmount() + "元无门槛券";
                } else {
                    String template;
                    if (Objects.equals(MerchantUserTypeEnum.PRODUCT_NEW_USER.getCode(), couponDetailInfo.getMerchantUserType())) {
                        template = "商品尝鲜券满%s减%s";
                    } else {
                        template = "商家券满%s减%s";
                    }
                    title = String.format(template, couponDetailInfo.getPriceLimit().stripTrailingZeros().divide(BigDecimal.valueOf(100), 2, BigDecimal.ROUND_HALF_UP).stripTrailingZeros().toPlainString(),
                            couponDetailInfo.getDiscountAmount().divide(BigDecimal.valueOf(100), 2, BigDecimal.ROUND_HALF_UP).stripTrailingZeros().toPlainString());
                }
            } else {
                title = couponDetailInfo.getTitle();
            }
            couponTag.setTag(title);
            couponTag.setType(PromoTypeEnum.COUPON.getCode());
            couponTags.add(couponTag);
        }

        // 头部补充满件折标签
        if (CollectionUtils.isNotEmpty(productPromoDetailInfo.getBuycarDiscounts())) {
            for (BuycarDiscountDetailInfo buycarDiscountDetailInfo : productPromoDetailInfo.getBuycarDiscounts()) {
                CouponTag couponTag = new CouponTag();
                couponTag.setType(PromoTypeEnum.SHOPPING_CART_DISCOUNT.getCode());
                couponTag.setTag(buycarDiscountDetailInfo.getDiscountLimitDesc() + buycarDiscountDetailInfo.getDiscountValue() + "折");
                couponTags.add(0, couponTag);
            }
        }


        ProductPromoConciseInfo conciseInfo = new ProductPromoConciseInfo();
        conciseInfo.setCouponTags(couponTags);
        return conciseInfo;
    }

    private static CouponDetailInfo loadExposureResources(CouponActivityContext context) {
        ResourcePromotionDo exposureResponseDTO = context.getResourceExposure();
        if (exposureResponseDTO == null) {
            return null;
        }
        if (!RecallTypeEnum.RECALL_PRE_DRAW_COUPON.getCode().equals(exposureResponseDTO.getRecallType())) {
            return null;
        }
        if (exposureResponseDTO.getDrawStatus() == 1) {
            return null;
        }
        CouponDetailInfo couponInfo = new CouponDetailInfo();
        CouponDetailDTO detailDTO = exposureResponseDTO.getDetailDTO();
        // 领券需要的参数
        couponInfo.setFlowId(exposureResponseDTO.getFlowId());
        couponInfo.setResourceActivityId(exposureResponseDTO.getActivityId());
        couponInfo.setActivityId(exposureResponseDTO.getActivityId());
        couponInfo.setMaterialId(exposureResponseDTO.getMaterialId());
        couponInfo.setRowKey(exposureResponseDTO.getRowKey());
        Map<String, String> materialMap = exposureResponseDTO.getStaticMaterialContext();
        String prizeTitle = null;
        String couponTag = null;
        String timeText = null;
        if (MapUtils.isNotEmpty(materialMap)) {
            prizeTitle = materialMap.get("prizeTitle");
            couponTag = materialMap.get("couponTag");
            timeText = materialMap.get("timeText");
        }
        // 券名称
        couponInfo.setTitle(prizeTitle);
        // 券面额
        couponInfo.setAmount(detailDTO.getAmountPrice());
        // 券门槛描述
        couponInfo.setPriceLimit(new BigDecimal(detailDTO.getLimitPrice()));
        couponInfo.setCouponThresholdDesc(formatPriceLimit(detailDTO.getLimitPrice()));
        // 投放券标签
        couponInfo.setTag(couponTag);
        // 投放券时间
        couponInfo.setCouponSuitDesc(timeText);
        // 这里设置为可领取的
        couponInfo.setCanAssign(true);
        couponInfo.setCoupontype(1);
        couponInfo.setFreshExclusive(true);
        return couponInfo;
    }

    private static String formatPriceLimit(String limitPrice) {
        Cat.logEvent("INVALID_METHOD_5", "com.dianping.pay.api.biz.activity.newloader.mapper.ProductPromoInfoMapper.formatPriceLimit(java.lang.String)");
        if (StringUtils.isBlank(limitPrice) || BigDecimal.ZERO.equals(new BigDecimal(limitPrice))) {
            return "无门槛";
        } else {
            return "满" + limitPrice + "元可用";
        }
    }

    private static ProductPromoDetailInfo buildPromoAggInfo(CouponActivityContext context) {
        PromotionDTOResult promoResult = context.getPromotionDTOResult();
        ResourcePromotionDo resourcePromotionDo = context.getResourceExposure();
        List<IssueCouponActivity> issueCouponActivities = context.getIssueCouponActivities();
        ProductPromoDetailInfo info = new ProductPromoDetailInfo();
        boolean needDegrade = context.isMt() ? HighPriceControlUtils.needDegradeOfMt(context.getBusiness(), context.getCityId()) : HighPriceControlUtils.needDegradeOfDp(context.getBusiness(), context.getCityId());

        if (promoResult == null && resourcePromotionDo == null && CollectionUtils.isEmpty(issueCouponActivities)) {
            return info;
        }
        List<CouponDetailInfo> merchantCoupons = new ArrayList<>();
        List<CouponDetailInfo> platformCoupons = new ArrayList<>();

        if (promoResult != null) {
            List<GetPromotionDTO> promos = promoResult.getGetPromotionDTO();
            if (CollectionUtils.isNotEmpty(promos)) {
                for (GetPromotionDTO promo : promos) {
                    if (promo != null && promo.getPromotionDTO() != null && promo.getPromotionDTO().getCouponDTO() != null) {
                        CouponDTO couponDTO = promo.getPromotionDTO().getCouponDTO();
                        CouponDetailInfo item = ProxyPromoInfoHelper.genCouponDetailItem(couponDTO, context, promo);
                        if (couponDTO.isIsMerchantCoupon()) {
                            if (CouponCommonTagEnum.beExclusiveSurprise(couponDTO.getCouponCommonTag())) {
                                merchantCoupons.add(0, item);
                            } else {
                                merchantCoupons.add(item);
                            }
                        } else {
                            platformCoupons.add(item);
                        }
                    }
                }
            }
        }

        if (CollectionUtils.isNotEmpty(issueCouponActivities)) {
            for (IssueCouponActivity issueCouponActivity : issueCouponActivities) {
                if (issueCouponActivity == null || issueCouponActivity.getCouponGroup() == null || issueCouponActivity.getCouponGroup().getDiscountAmount().compareTo(BigDecimal.ZERO) <= 0) {
                    continue;
                }
                CouponDetailInfo item = IssueActivityMapper.genCouponDetailItem(issueCouponActivity);
                merchantCoupons.add(item);
            }
        }
        if (CollectionUtils.isNotEmpty(platformCoupons)) {
            Collections.sort(platformCoupons, (Comparator<CouponDetailInfo>) (c1, c2) -> {
                if (c1.getCoupontype() == 3 && c2.getCoupontype() != 3) {
                    return 1;
                }
                if (c1.getCoupontype() != 3 && c2.getCoupontype() == 3) {
                    return -1;
                }
                int amountCompare = c1.getAmount().compareTo(c2.getAmount());
                if (amountCompare != 0) {
                    return amountCompare;
                }
                BigDecimal c1PriceLimit = c1.getPriceLimit() == null ? BigDecimal.ZERO : c1.getPriceLimit();
                BigDecimal c2PriceLimit = c2.getPriceLimit() == null ? BigDecimal.ZERO : c2.getPriceLimit();
                return c1PriceLimit.compareTo(c2PriceLimit);
            });
        }
        CouponDetailInfo couponInfo = loadExposureResources(context);
        if (couponInfo != null) {
            platformCoupons.add(0, couponInfo);
        }
//        List<CouponDetailInfo> merchantAccountCoupons = loadMerchantAccountCoupon(context);
//        if (CollectionUtils.isNotEmpty(merchantAccountCoupons)) {
//            platformCoupons.addAll(0, merchantAccountCoupons);
//        }
        if(!needDegrade) {
            info.setMerchantCoupons(merchantCoupons);
        }
        info.setPlatformCoupons(platformCoupons);

        // 立减信息为空就直接返回
        List<PromoDisplayDTO> promoList = context.getPromoDisplayDTOS();
        if (CollectionUtils.isEmpty(promoList)) {
            return info;
        }

        // 拼接立减
        try {
            List<PromoDisplayDTO> normalPromos = ReductionPromoHelper.getNormalPromo(promoList);
            if (CollectionUtils.isNotEmpty(normalPromos)) {
                List<ReductionDetailInfo> merchantReductionPromos = Lists.newArrayList();
                List<ReductionDetailInfo> platReductionPromos = Lists.newArrayList();
                for (PromoDisplayDTO promoDisplayDTO : normalPromos) {
                    if (promoDisplayDTO == null) {
                        continue;
                    }
                    if (CollectionUtils.isNotEmpty(promoDisplayDTO.getEachPromoDetailCompositions())) {
                        List<EachPromoDetailComposition> promoDetailCompositions = promoDisplayDTO.getEachPromoDetailCompositions();
                        for (EachPromoDetailComposition promoDetailComposition : promoDetailCompositions) {
                            String title = promoDetailComposition.isNewUserPromo() ? "新客立减" : "下单立减";
                            if (promoDetailComposition.getPromoSource() == PromoSource.shopSource.getCode()) {
                                merchantReductionPromos.add(buildReductionDetailInfo(title, promoDetailComposition.getDescription()));
                            } else {
                                platReductionPromos.add(buildReductionDetailInfo(title, promoDetailComposition.getDescription()));
                            }
                        }
                    }
                }
                if(!needDegrade) {
                    info.setMerchantReductionPromos(merchantReductionPromos);
                }
                info.setPlatReductionPromos(platReductionPromos);

            }
        } catch (Exception e){
            LOGGER.error("ProductPromoInfoMapper# build reduction info error. context: {}", context, e);
        }


        // 拼接满件折
        try {
            List<PromoDisplayDTO> buycarDiscountPromos = ReductionPromoHelper.getBuycarDiscountPromo(promoList);
            if (CollectionUtils.isNotEmpty(buycarDiscountPromos)) {
                List<BuycarDiscountDetailInfo> buycarDiscounts = Lists.newArrayList();

                for (PromoDisplayDTO promoDisplayDTO : buycarDiscountPromos) {
                    if (promoDisplayDTO == null) {
                        continue;
                    }
                    if (CollectionUtils.isNotEmpty(promoDisplayDTO.getEachPromoDetailCompositions())) {
                        List<EachPromoDetailComposition> promoDetailCompositions = promoDisplayDTO.getEachPromoDetailCompositions();
                        EachPromoDetailComposition promoDetailComposition = promoDetailCompositions.get(0);
                        BigDecimal discountValue = promoDetailComposition.getActionValue();
                        Integer discountLimit = PromoConditionUtils.getQuantityGECondition(promoDisplayDTO.getConditionMap2());
                        BuycarDiscountDetailInfo buycarDiscountDetailInfo = new BuycarDiscountDetailInfo();
                        buycarDiscountDetailInfo.setAdditionalUseRule(Lion.getStringValue(LionConstants.SHOPPING_CART_ADDITIONAL_RULE, "限本店指定商品可用，不可与其他商家优惠或平台优惠同享，不可跨店使用"));
                        buycarDiscountDetailInfo.setDiscountLimitDesc("满" + discountLimit + "件");

                        String discountValueStr = discountValue.multiply(new BigDecimal(10)).stripTrailingZeros().toPlainString();
                        buycarDiscountDetailInfo.setDiscountValue(discountValueStr);
                        buycarDiscountDetailInfo.setScript(Lion.getStringValue(LionConstants.SHOPPING_CART_DISCOUNT_SCRIPT_DESC, ""));

                        if (CollectionUtils.isNotEmpty(promoDisplayDTO.getPromoCompositions())) {
                            PromoComposition composition = promoDisplayDTO.getPromoCompositions().get(0);
                            Date beginDate = composition.getBeginDate();
                            Date endDate = composition.getEndDate();
                            SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy.MM.dd");
                            String timeDesc = dateFormat.format(beginDate) + "-" + dateFormat.format(endDate);
                            buycarDiscountDetailInfo.setTimeDesc(timeDesc);
                        }

                        buycarDiscountDetailInfo.setUseRuleDesc("限商品满" + discountLimit + "件" + discountValueStr + "折");
                        buycarDiscountDetailInfo.setUseRuleTip(Lion.getStringValue(LionConstants.SHOPPING_CART_DISCOUNT_USE_RULE_TIP, ""));

                        buycarDiscounts.add(buycarDiscountDetailInfo);
                    }
                }
                info.setBuycarDiscounts(buycarDiscounts);
            }
        } catch (Exception e) {
            LOGGER.error("ProductPromoInfoMapper# build buyCar discount info error. context: {}", context, e);
        }

        return info;
    }

    private static List<CouponDetailInfo> loadMerchantAccountCoupon(CouponActivityContext context) {
        Cat.logEvent("INVALID_METHOD_2", "com.dianping.pay.api.biz.activity.newloader.mapper.ProductPromoInfoMapper.loadMerchantAccountCoupon(com.dianping.pay.api.biz.activity.newloader.dto.CouponActivityContext)");
        Pair<List<CouponDesc>, List<CouponDesc>> merchantAccountCouponPromotion = context.getMerchantAccountCouponPromotion();
        if (merchantAccountCouponPromotion == null) {
            return null;
        }
        List<CouponDesc> noIssued = merchantAccountCouponPromotion.getLeft();
        List<CouponDesc> issued = merchantAccountCouponPromotion.getRight();
        List<CouponDesc> allCouponDesc = Lists.newArrayList();
        if (CollectionUtils.isNotEmpty(noIssued)) {
            allCouponDesc.addAll(noIssued);
        }
        if (CollectionUtils.isNotEmpty(issued)) {
            allCouponDesc.addAll(issued);
        }
        if (CollectionUtils.isEmpty(allCouponDesc)) {
            return null;
        }
        return allCouponDesc.stream().map(dto->{
            CouponDetailInfo item = new CouponDetailInfo();
            item.setCouponGroupId(dto.getCouponGroupId());
            item.setAmount(dto.getAmount().stripTrailingZeros().toPlainString());
            item.setAmountCornerMark("元");
            item.setDiscountAmount(dto.getAmount().multiply(BigDecimal.valueOf(100)));
            if (dto.getRequireAmount() != null && dto.getRequireAmount().compareTo(BigDecimal.ZERO) > 0) {
                item.setPriceLimit(dto.getRequireAmount().multiply(BigDecimal.valueOf(100)));
            }
            item.setCanAssign(noIssued.contains(dto));
            item.setTitle(dto.getName());
            item.setCouponCategoryDesc("部分商品可用");
            item.setCouponSuitDesc(IssueCouponUtils.formatMerchantAccountCouponTimePeriod(dto.getEndTime()));
            item.setCouponThresholdDesc(ProxyPromoInfoHelper.buildCouponThresholdDesc(dto.getRequireAmount()));
            item.setCouponSrc(0);
            item.setCornerMarkUrl(Lion.getString(LionConstants.APP_KEY, LionConstants.RESOURCE_CORNER_MARK_URL));
            return item;
        }).collect(Collectors.toList());
    }


    private static ReductionDetailInfo buildReductionDetailInfo(String title, String desc) {
        ReductionDetailInfo detailInfo = new ReductionDetailInfo();
        detailInfo.setDesc(desc);
        detailInfo.setTitle(title);
        return detailInfo;
    }
}
