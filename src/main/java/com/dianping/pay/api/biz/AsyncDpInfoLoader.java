package com.dianping.pay.api.biz;

import com.dianping.account.dto.UserAccountDTO;
import com.dianping.api.service.UserWrapperService;
import com.dianping.cat.Cat;
import com.dianping.pay.api.constants.CatEventConstants;
import com.dianping.pay.api.service.impl.ShopWrapperService;
import com.google.common.collect.Maps;
import com.sankuai.nibmkt.promotion.api.common.utils.CityIdTransformUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.tuple.Pair;

import java.util.*;
import java.util.concurrent.Future;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * 点评id体系异步转换器
 */
@Slf4j
public class AsyncDpInfoLoader {

    private ShopWrapperService shopWrapperService;
    private UserWrapperService userWrapperService;
    private ThreadPoolExecutor executor;

    //点评用户id转美团实+美团虚
    private Long dpUserId;
    private Future<Pair<Long, Long>> mtUserPairFuture;
    private Future<UserAccountDTO> dpUserAccountInfoFuture;

    //点评门店id转美团门店id
    private Long dpShopId;
    private Future<Long> mtShopIdFuture;
    private Future<Pair<Integer, Integer>> shopCategoryFuture;

    //点评城市id转美团城市id
    private Set<Integer> dpCityIds;
    private Future<Map<Integer, Integer>> dpCityId2MtCityIdMapFuture;


    public AsyncDpInfoLoader(ShopWrapperService shopWrapperService, UserWrapperService userWrapperService, ThreadPoolExecutor executor) {
        this.shopWrapperService = shopWrapperService;
        this.userWrapperService = userWrapperService;
        this.executor = executor;
    }

    public AsyncDpInfoLoader loadMtUserId(Long dpUserId){
        if (dpUserId == null || dpUserId <= 0) {
            return this;
        }
        this.dpUserId = dpUserId;
        mtUserPairFuture =  executor.submit(() -> userWrapperService.getMtUserId(dpUserId));
        return this;
    }

    public AsyncDpInfoLoader loadUserMobile(Long dpUserId) {
        if (dpUserId == null || dpUserId <= 0) {
            return this;
        }
        dpUserAccountInfoFuture = executor.submit(() -> userWrapperService.getUserAccountByDpUser(dpUserId));
        return this;
    }

    public String getUserMobile() {
        if (dpUserAccountInfoFuture == null) {
            return null;
        }
        UserAccountDTO userAccountDTO = getResult(dpUserAccountInfoFuture, 500, CatEventConstants.DP_ID_2_MOBILE_EVENT_NAME_PRE, String.valueOf(dpUserId));
        return userAccountDTO == null ? null : userAccountDTO.getMobile();
    }

    public Pair<Long, Long> getMtUserPairFuture() {
        if (mtUserPairFuture == null) {
            return Pair.of(0L, 0L);
        }
        Pair<Long, Long> dp2MtUserId = getResult(mtUserPairFuture, 500, CatEventConstants.DP_ID_2_MT_USER_EVENT_NAME_PRE, String.valueOf(dpUserId));
        return dp2MtUserId == null ? Pair.of(0L, 0L) : dp2MtUserId;
    }

    public AsyncDpInfoLoader loadMtShopId(Long dpShopId) {
        if (dpShopId == null || dpShopId <= 0) {
            return this;
        }
        this.dpShopId = dpShopId;
        mtShopIdFuture = executor.submit(() -> shopWrapperService.getMtShopId(dpShopId));
        return this;
    }

    public Long getMtShopId() {
        if (mtShopIdFuture == null) {
            return 0L;
        }
        Long dp2MtShopId = getResult(mtShopIdFuture, 500, CatEventConstants.DP_ID_2_MT_SHOP_EVENT_NAME_PRE, String.valueOf(dpShopId));
        return dp2MtShopId == null ? 0L : dp2MtShopId;
    }

    public AsyncDpInfoLoader loadShopCategory(Long dpShopId) {
        if (dpShopId == null || dpShopId <= 0) {
            return this;
        }
        if (this.dpShopId == null) {
            return this;
        }
        shopCategoryFuture = executor.submit(() -> {
            Pair<List<Integer>, List<Integer>> listListPair = shopWrapperService.loadShopCategory(dpShopId);
            if (listListPair == null) {
                return null;
            }
            return Pair.of(CollectionUtils.isEmpty(listListPair.getLeft()) ? null :  listListPair.getLeft().get(0),
                    CollectionUtils.isEmpty(listListPair.getRight()) ? null : listListPair.getRight().get(0));
        });
        return this;
    }

    public Pair<Integer, Integer> getShopCategory() {
        if (shopCategoryFuture == null) {
            return Pair.of(null, null);
        }
        Pair<Integer, Integer> result = getResult(shopCategoryFuture, 500, CatEventConstants.DP_ID_2_SHOP_CATEGORY_EVENT_NAME_PRE, String.valueOf(dpShopId));
        return result == null ? Pair.of(null, null) : result;
    }

    public AsyncDpInfoLoader batchLoadMtCityId(Integer...dpCityIds){
        if (dpCityIds == null || dpCityIds.length == 0) {
            return this;
        }
        Set<Integer> dpCityIdSet = Arrays.stream(dpCityIds)
                .filter(Objects::nonNull)
                .filter(cityId -> cityId > 0)
                .collect(Collectors.toSet());
        if (CollectionUtils.isEmpty(dpCityIdSet)) {
            return this;
        }
        this.dpCityIds = dpCityIdSet;
        dpCityId2MtCityIdMapFuture = executor.submit(() -> CityIdTransformUtil.getBatchMtCityIdByDp(dpCityIdSet));
        return this;
    }

    public Map<Integer, Integer> getDpCityId2MtCityIdMap() {
        if (dpCityId2MtCityIdMapFuture == null) {
            return Maps.newHashMap();
        }
        Map<Integer, Integer> dp2MtCityId = getResult(dpCityId2MtCityIdMapFuture, 500, CatEventConstants.DP_ID_2_MT_CITY_EVENT_NAME_PRE, String.valueOf(dpCityIds));
        if (MapUtils.isEmpty(dp2MtCityId)) {
            return Maps.newHashMap();
        }
        for (Integer dpCityId : dpCityIds) {
            if (dp2MtCityId.get(dpCityId) == null) {
                Cat.logEvent(CatEventConstants.DP_ID_2_MT_ERROR, CatEventConstants.DP_ID_2_MT_CITY_EVENT_NAME_PRE + "_null:" + dpCityId);
            }
        }
        return dp2MtCityId;
    }

    public <T> T getResult(Future<T> future, int timeout, String eventName, String convertOriginValue) {
        try {
            T value = future.get(timeout, TimeUnit.MILLISECONDS);
            if (value == null) {
                Cat.logEvent(CatEventConstants.DP_ID_2_MT_ERROR, eventName + "_null:" +convertOriginValue);
            }
            return value;
        }catch (Exception e) {
            Cat.logEvent(CatEventConstants.DP_ID_2_MT_ERROR, eventName + "_error:" +convertOriginValue);
            log.error("asyncDpInfoLoader getResult error, originValue: {}, eventName:{}", convertOriginValue, eventName, e);
        }
        return null;
    }
}
