package com.dianping.pay.api.biz.promodeskcoupon;

import com.dianping.cat.Cat;
import com.dianping.pay.api.beans.GetPromoDeskRequest;
import com.dianping.pay.api.beans.PromoProduct;
import com.dianping.pay.common.enums.ProductCode;

import javax.annotation.Resource;

public class PromoDeskCouponStrategyFactory {

    @Resource
    private CommonPromoDeskCouponStrategy commonPromoDeskCouponStrategy;
    @Resource
    private TuangouPromoDeskCouponStrategy tuangouPromoDeskCouponStrategy;
    @Resource
    private ShanhuiPromoDeskCouponStrategy shanhuiPromoDeskCouponStrategy;
    @Resource
    private KtvPromoDeskCouponStrategy ktvPromoDeskCouponStrategy;

    public PromoDeskCouponStrategy getStrategy(GetPromoDeskRequest request) {
        Cat.logEvent("INVALID_METHOD_3", "com.dianping.pay.api.biz.promodeskcoupon.PromoDeskCouponStrategyFactory.getStrategy(com.dianping.pay.api.beans.GetPromoDeskRequest)");
        ProductCode productCode = ProductCode.getByCode(getMainProductCode(request));
        switch (productCode) {
            case TUANGOU:
                return tuangouPromoDeskCouponStrategy;
            case MO2O2PAY:
                return shanhuiPromoDeskCouponStrategy;
            case KTV:
                return ktvPromoDeskCouponStrategy;
            default:
                return commonPromoDeskCouponStrategy;
        }
    }

    private int getMainProductCode(GetPromoDeskRequest getPromoDeskRequest) {
        Cat.logEvent("INVALID_METHOD_5", "com.dianping.pay.api.biz.promodeskcoupon.PromoDeskCouponStrategyFactory.getMainProductCode(com.dianping.pay.api.beans.GetPromoDeskRequest)");
        int productCode = 0;
        if (getPromoDeskRequest.getPromoProductList().size() == 1) {
            productCode = getPromoDeskRequest.getPromoProductList().get(0).getProductCode();
        } else if (getPromoDeskRequest.getPromoProductList().size() > 1) {
            for (PromoProduct promoProduct : getPromoDeskRequest.getPromoProductList()) {
                if (promoProduct.getProductCode() > productCode) { // TODO 暂时先这么弄一下
                    productCode = promoProduct.getProductCode();
                }
            }
        }
        return productCode;
    }
}
