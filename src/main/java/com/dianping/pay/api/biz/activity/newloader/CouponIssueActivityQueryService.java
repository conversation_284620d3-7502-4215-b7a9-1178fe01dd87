package com.dianping.pay.api.biz.activity.newloader;

import com.dianping.api.common.enums.ProductTypeEnum;
import com.dianping.api.service.ShopWrapService;
import com.dianping.api.service.UserWrapperService;
import com.dianping.api.util.FunctionPool;
import com.dianping.cat.Cat;
import com.dianping.cat.message.Transaction;
import com.dianping.combiz.spring.util.PropertiesLoaderSupportUtils;
import com.dianping.deal.base.dto.DealGroupBaseDTO;
import com.dianping.deal.idmapper.api.DealIdMapperService;
import com.dianping.deal.idmapper.api.dto.IdMapper;
import com.dianping.deal.publishcategory.dto.DealGroupChannelDTO;
import com.dianping.lion.client.Lion;
import com.dianping.mobile.framework.datatypes.IMobileContext;
import com.dianping.pay.api.beans.DealDetailDO;
import com.dianping.pay.api.beans.IssueCouponActivity;
import com.dianping.pay.api.beans.ProductDetailDO;
import com.dianping.pay.api.biz.AsyncDpInfoLoader;
import com.dianping.pay.api.biz.activity.newloader.dto.*;
import com.dianping.pay.api.biz.activity.newloader.executors.*;
import com.dianping.pay.api.biz.activity.newloader.mapper.MemberPriceCalculateMapper;
import com.dianping.pay.api.biz.activity.newloader.promo.*;
import com.dianping.pay.api.biz.activity.newloader.unified.executor.*;
import com.dianping.pay.api.constants.AbTestConstant;
import com.dianping.pay.api.constants.CatEventConstants;
import com.dianping.pay.api.entity.issuecoupon.IssueCouponRequest;
import com.dianping.pay.api.entity.issuecoupon.ShopCartIssueMultiCouponContext;
import com.dianping.pay.api.enums.ChannelProductEnum;
import com.dianping.pay.api.enums.QueryShopCouponTypeEnum;
import com.dianping.pay.api.enums.ShopProductType;
import com.dianping.pay.api.service.AbTestService;
import com.dianping.pay.api.service.impl.ProductService;
import com.dianping.pay.api.service.impl.ShopWrapperService;
import com.dianping.pay.api.util.CollUtils;
import com.dianping.pay.api.util.LionConstants;
import com.dianping.pay.api.util.LogUtils;
import com.dianping.pay.promo.display.api.dto.PromoDisplayDTO;
import com.dianping.poi.relation.service.api.PoiRelationService;
import com.dianping.shopremote.remote.dto.ShopDTO;
import com.dianping.spc.common.Response;
import com.dianping.tgc.open.entity.BatchCouponBizIdQueryRequest;
import com.dianping.tgc.open.entity.BizIdType;
import com.dianping.tgc.open.entity.QuerySimpleResponseDTO;
import com.dianping.tgc.open.v2.TGCGetCouponComponentQueryService;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.sankuai.douhu.absdk.enums.PlatformEnum;
import com.sankuai.nib.magic.member.base.enums.BizLineEnum;
import com.sankuai.nib.magic.member.base.enums.PageSourceEnum;
import com.sankuai.nib.mkt.common.base.enums.propertyValueType.ClientTypeEnum;
import com.sankuai.nib.mkt.magicDegarade.*;
import com.sankuai.nibmktproxy.queryclient.proxy.PromotionDTOResult;
import com.sankuai.wpt.user.merge.query.thrift.message.BindRelationResp;
import com.sankuai.wpt.user.merge.query.thrift.message.UserMergeQueryService;
import com.sankuai.wpt.user.retrieve.thrift.message.UserModel;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.*;

public class CouponIssueActivityQueryService {
    private static Logger logger = LogManager.getLogger(CouponIssueActivityQueryService.class);
    @Autowired
    private CouponIssueActivityQueryExecutorFactory executorFactory;

    private ThreadPoolExecutor threadPool;

    @Autowired
    private PoiRelationService poiRelationCacheService;

    @Autowired
    private UserMergeQueryService.Iface rpcUserService;

    @Resource(name = "tgcCouponProcessor")
    private TgcCouponProcessor tgcCouponProcessor;
    @Resource(name = "returnPromotionProcessor")
    private ReturnPromotionProcessor returnPromotionProcessor;
    @Resource(name = "shareCouponProcessor")
    private ShareCouponProcessor shareCouponProcessor;
    @Resource(name = "reductionPromotionProcessor")
    private ReductionPromotionProcessor reductionPromotionProcessor;
    @Resource(name = "proxyCouponProcessor")
    private ProxyCouponProcessor proxyCouponProcessor;
    @Resource(name = "resourcePromotionProcessor")
    private ResourcePromotionProcessor resourcePromotionProcessor;
    @Resource(name = "userCouponProcessor")
    private UserCouponProcessor userCouponProcessor;
    @Resource(name = "tgcBatchCouponProcessor")
    private TgcBatchCouponProcessor tgcBatchCouponProcessor;
    @Resource(name = "brandActivityPromotionProcessor")
    private BrandActivityPromotionProcessor brandActivityPromotionProcessor;
    @Resource(name = "proxyReduceProcessor")
    private ProxyReduceProcessor proxyReduceProcessor;
    @Resource(name = "proxyMemberCardProcessor")
    private ProxyMemberCardProcessor proxyMemberCardProcessor;
    @Resource(name = "proxyJoyCardProcessor")
    private ProxyJoyCardProcessor proxyJoyCardProcessor;
    @Resource(name = "financialCouponProcessor")
    private FinancialCouponProcessor financialCouponProcessor;
    @Resource(name = "priceDisplayProcessor")
    private PriceDisplayProcessor priceDisplayProcessor;
    @Resource(name = "shopResourcePromotionProcessor")
    private ShopResourcePromotionProcessor shopResourcePromotionProcessor;
    @Resource(name = "topAdResourcePromotionProcessor")
    private TopAdResourcePromotionProcessor topAdResourcePromotionProcessor;
    @Resource(name = "magicalMemberCouponProcessor")
    private MagicalMemberCouponProcessor magicalMemberCouponProcessor;


    @Autowired
    private LogUtils logUtils;

    @Resource
    private TGCGetCouponComponentQueryService tgcGetCouponComponentQueryService;

    @Autowired
    private DealIdMapperService dealIdMapperService;

    @Resource
    private ProductService productService;

    @Resource
    private ShopWrapService shopWrapService;

    @Resource
    private UserWrapperService userWrapperService;

    @Resource
    private MerchantAccountCouponProcessor merchantAccountCouponProcessor;

    @Resource
    private ShopWrapperService shopWrapperService;

    @Resource
    private AbTestService abTestService;

    private List<AbstractPromoProcessor> getChainForPromoProcessors() {
        return Lists.newArrayList(tgcCouponProcessor, returnPromotionProcessor, shareCouponProcessor, reductionPromotionProcessor,
                proxyCouponProcessor, resourcePromotionProcessor, userCouponProcessor, tgcBatchCouponProcessor, brandActivityPromotionProcessor,
                proxyReduceProcessor, proxyMemberCardProcessor, proxyJoyCardProcessor, financialCouponProcessor, priceDisplayProcessor, shopResourcePromotionProcessor,
                merchantAccountCouponProcessor, topAdResourcePromotionProcessor, magicalMemberCouponProcessor);
    }

    public CouponIssueActivityQueryService(int coreSize, int maxSize, long threadTimeOut, int blockQueueSize) {
        this.threadPool = new ThreadPoolExecutor(coreSize, maxSize, threadTimeOut, TimeUnit.MILLISECONDS, new LinkedBlockingQueue<Runnable>(blockQueueSize));
    }

    public List<IssueCouponActivity> queryActivities(IssueCouponRequest request) {
        List<IssueCouponActivity> resultList = new ArrayList<IssueCouponActivity>();
        Transaction t = Cat.newTransaction("CouponIssueActivityQueryService", "queryActivities");
        try {
            List<Callable<List<IssueCouponActivity>>> executors = getExecutors(request);
            long executeTimeout = PropertiesLoaderSupportUtils.getIntProperty("pay-api-mobile.issuecoupon.processThread.processTimeout", 500);
            List<Future<List<IssueCouponActivity>>> futures = threadPool.invokeAll(executors, executeTimeout, TimeUnit.MILLISECONDS);
            for (Future<List<IssueCouponActivity>> future : futures) {
                if (future != null) {
                    resultList.addAll(future.get());
                }
            }
            t.setStatus(Transaction.SUCCESS);
        } catch (Exception e) {
            logger.error("CouponIssueActivityQueryService queryActivities error,request:{}", request, e);
            t.setStatus(e);
        } finally {
            t.complete();
        }
        return resultList;
    }

    public List<IssueCouponActivity> queryActivities(CouponIssueActivityQueryContext context) {
        List<IssueCouponActivity> resultList = Lists.newArrayList();
        try {
            List<Callable<List<IssueCouponActivity>>> executors = getExecutors(context);
            long executeTimeout = PropertiesLoaderSupportUtils.getIntProperty("pay-api-mobile.issuecoupon.processThread.processTimeout", 500);
            List<Future<List<IssueCouponActivity>>> futures = threadPool.invokeAll(executors, executeTimeout, TimeUnit.MILLISECONDS);
            for (Future<List<IssueCouponActivity>> future : futures) {
                if (future != null) {
                    resultList.addAll(future.get());
                }
            }
            //resultList 可能存在重复activity 此处去重
            Set<Integer> activityIdSet = Sets.newHashSet();
            Iterator<IssueCouponActivity> iterator = resultList.iterator();
            while (iterator.hasNext()) {
                IssueCouponActivity issueCouponActivity = iterator.next();
                int activityId = issueCouponActivity.getActivityId();
                if (activityIdSet.contains(activityId)) {
                    iterator.remove();
                } else {
                    activityIdSet.add(activityId);
                }
            }
        } catch (Exception e) {
            logger.error(String.format("query activity error,e[%s],context:[%s]", e.getMessage(), context), e);
        }
        return resultList;
    }

    private List<Callable<List<IssueCouponActivity>>> getExecutors(CouponIssueActivityQueryContext context) {
        List<Callable<List<IssueCouponActivity>>> resultList = new ArrayList<Callable<List<IssueCouponActivity>>>();
        if (context.getShopIdL() > 0) {
            //预定抵用券
            CollectionUtils.addIgnoreNull(resultList, executorFactory.getExecutor(UnifiedShopActivityQueryExecutor.class, context));
            //团购+泛商品
            CollectionUtils.addIgnoreNull(resultList, executorFactory.getExecutor(UnifiedShopDealActivityQueryExecutor.class, context));
            //课程
            CollectionUtils.addIgnoreNull(resultList, executorFactory.getExecutor(UnifiedShopSpuActivityQueryExecutor.class, context));
        }
        if (context.getProductId() > 0 && context.getProductType() == ProductTypeEnum.DEAL.code) {
            //团购
            CollectionUtils.addIgnoreNull(resultList, executorFactory.getExecutor(UnifiedDealActivityQueryExecutor.class, context));
        }
        if (context.getProductId() > 0 && context.getProductType() == ProductTypeEnum.SKU.code) {
            //泛商品
            CollectionUtils.addIgnoreNull(resultList, executorFactory.getExecutor(UnifiedSkuActivityQueryExecutor.class, context));
        }
        if (context.getProductId() > 0 && context.getProductType() == ProductTypeEnum.SPU.code) {
            //泛商品spu
            CollectionUtils.addIgnoreNull(resultList, executorFactory.getExecutor(UnifiedSpuActivityQueryExecutor.class, context));
        }
        return resultList;
    }

    private List<Callable<List<IssueCouponActivity>>> getExecutors(IssueCouponRequest request) {
        List<Callable<List<IssueCouponActivity>>> resultList = new ArrayList<Callable<List<IssueCouponActivity>>>();
        CollectionUtils.addIgnoreNull(resultList, executorFactory.getExecutor(BeautyActivityQueryExecutor.class, request));
        if (CollectionUtils.isNotEmpty(request.getShopIdLList())) {
            CollectionUtils.addIgnoreNull(resultList, executorFactory.getExecutor(TuangouShopActivityQueryExecutor.class, request));
            CollectionUtils.addIgnoreNull(resultList, executorFactory.getExecutor(GeneralShopActivityQueryExecutor.class, request));
        }
        if (request.getShopIdL() > 0) {
            CollectionUtils.addIgnoreNull(resultList, executorFactory.getExecutor(SpuShopActivityQueryExecutor.class, request));
        }
        if (request.getProductId() > 0 && request.getShopProductType() == ShopProductType.DEFAULT.getValue()) {
            CollectionUtils.addIgnoreNull(resultList, executorFactory.getExecutor(DealActivityQueryExecutor.class, request));
        }
        if (request.getProductId() > 0 && request.getShopProductType() == ShopProductType.GENERAL_TRADE.getValue()) {
            CollectionUtils.addIgnoreNull(resultList, executorFactory.getExecutor(SkuActivityQueryExecutor.class, request));
        }
        return resultList;
    }

    private CouponActivityContext buildPromoCtx(PromotionRequestContext request, IMobileContext iMobileContext) {
        CouponActivityContext promoCtx = new CouponActivityContext();
        promoCtx.setCx(request.getCx());
        promoCtx.setDpid(request.getDpid());
        promoCtx.setVersion(request.getVersion());
        promoCtx.setCityId(request.getCityId());
        promoCtx.setBeautyDealNewType(request.getBeautyDealNewType());
        promoCtx.setPageSource(request.getPageSource());
        promoCtx.setCouponPageSource(request.getCouponPageSource());
        promoCtx.setNeedDiscountCoupon(request.getNeedDiscountCoupon());
        promoCtx.setLatitude(request.getLatitude());
        promoCtx.setLongitude(request.getLongitude());
        promoCtx.setDpShopIdL(request.getDpShopIdL());
        promoCtx.setMtShopIdL(request.getMtShopIdL());
        promoCtx.setSkuId(request.getSkuId());
        promoCtx.setDpDealGroupId(request.getDpDealGroupId());
        promoCtx.setMtDealGroupId(request.getMtDealGroupId());
        promoCtx.setSpuGroupId(request.getSpuGroupId());
        promoCtx.setCouponPlatform(request.getCouponPlatform());
        promoCtx.setPayPlatform(request.getPayPlatform());

        promoCtx.setUserType(request.getUserType());
        promoCtx.setUserId(iMobileContext.getUserId());
        promoCtx.setDpUserId(request.getDpUserId());
        promoCtx.setMtUserId(request.getMtUserId());
        promoCtx.setQueryShopCouponType(request.getQueryShopCouponType());
        promoCtx.setNeedReturnPromotion(request.isNeedReturnPromotion());
        promoCtx.setNeedResourcesPromotion(request.isNeedResourcesPromotion());
        promoCtx.setNeedReductionPromotion(request.isNeedReductionPromotion());
        promoCtx.setNeedShareCouponPromotion(request.isNeedShareCouponPromotion());
        promoCtx.setNeedUserPromotion(request.isNeedUserPromotion());
        promoCtx.setNeedBrandPromotion(request.isNeedBrandPromotion());
        promoCtx.setNeedFinancialPromo(request.isNeedFinalcialCouponPromotion());
        promoCtx.setNeedQueryPricePromotion(request.isNeedQueryPricePromotion());
        promoCtx.setNeedShopResourcesPromotion(request.isNeedShopResourcesPromotion());
        promoCtx.setNeedMerchantAccountPromotion(request.isNeedMerchantAccountPromotion());
        promoCtx.setNeedTopAdResourcePromotion(request.isNeedTopAdResourcePromotion());
        promoCtx.setNeedMagicalMemberCoupon(request.isNeedMagicalMemberCoupon());
        promoCtx.setActualCityId(request.getActualCityId());
        promoCtx.setUuid(request.getUuid());
        promoCtx.setCPlatform(request.getCPlatform());
        promoCtx.setMtFingerprint(request.getMtFingerprint());
        promoCtx.setMtgsig(request.getMtgsig());
        promoCtx.setRegionId(request.getRegionId());

        promoCtx.setSkuIds(request.getSkuIds());
        promoCtx.setDpDealGroupIds(request.getDpDealGroupIds());
        promoCtx.setMtDealGroupIds(request.getMtDealGroupIds());
        promoCtx.setBrandId(request.getBrandId());
        promoCtx.setChannel(request.getChannel());
        promoCtx.setProductType(request.getProductType());
        promoCtx.setExtParam(request.getExtParam());
        promoCtx.setTrafficSource(request.getTrafficSource());
        promoCtx.setBusiness(request.getBusiness());
        if (request.isMt()) {
            promoCtx.setMtCityId(request.getCityId());
            promoCtx.setMtActualCityId(request.getActualCityId());
        }
        promoCtx.setMiniProgramFlag(request.getMiniProgramFlag());
        promoCtx.setAppId(request.getAppId());
        promoCtx.setOpenId(request.getOpenId());
        promoCtx.setCposition(request.getCpositionOrDefault());
        transferMmcParam(request, promoCtx);

        promoCtx.setMagicMemberComponentVersion(request.getMagicMemberComponentVersion());
        promoCtx.setAbResult(Maps.newHashMap());
        return promoCtx;
    }

    public CouponActivityContext queryShopPromotions(final PromotionRequestContext promotionRequestContext, final IMobileContext iMobileContext) {
        CouponActivityContext promoCtx = buildPromoCtx(promotionRequestContext, iMobileContext);
        if(promotionRequestContext.isNeedResetUserId()) {
            resetUserId(promoCtx);
        }
        List<AbstractPromoProcessor> abstractPromoProcessors = getChainForPromoProcessors();
        if (CollectionUtils.isEmpty(abstractPromoProcessors)) {
            return promoCtx;
        }
        fillCtxInitInfo(promotionRequestContext, promoCtx, iMobileContext);
        //神会员开城时的开关，后续可以考虑下线掉
        grayMMCoupon(promoCtx, iMobileContext);
        logUtils.logInfo("queryShopPromotions# promoCtx: {}", promoCtx);
        for (AbstractPromoProcessor promoProcessor : abstractPromoProcessors) {
            promoProcessor.prePare(promoCtx, iMobileContext);
        }
        for (AbstractPromoProcessor promoProcessor : abstractPromoProcessors) {
            promoProcessor.loadPromo(promoCtx);
        }

        Pair<List<PromoDisplayDTO>, List<PromoDisplayDTO>> promotionDisplays = promoCtx.getReductionPromotions();
        if (promotionDisplays != null) {
            promoCtx.setPromoDisplayDTOS(promotionDisplays.getLeft());
            promoCtx.setDiscountPromoDisplayDTOS(promotionDisplays.getRight());
        }

        PromotionDTOResult bestProxyPromotionResult = MemberPriceCalculateMapper.calculateBestPricePromo(promoCtx);
        if (bestProxyPromotionResult!=null) {
            promoCtx.setBestProxyPromotionResult(bestProxyPromotionResult);
        }
        return promoCtx;
    }

    private Map<String, AbTestInfo> preAbTest(PromotionRequestContext promoCtx, IMobileContext iMobileContext) {
        Map<String, AbTestInfo> result = Maps.newHashMap();
        if (!promoCtx.isNeedMagicalMemberCoupon()) {
            return result;
        }
        AbTestRequest abTestRequest = AbTestRequest.builder()
                .abCode(AbTestConstant.POI_MMC_ISSUE_COUPON_AB)
                .userId(String.valueOf(promoCtx.isMt() ? promoCtx.getMtUserId() :  promoCtx.getDpUserId()))
                .uuid(StringUtils.isBlank(promoCtx.getUuid()) ? promoCtx.getDpid() : promoCtx.getUuid())
                .dpId(promoCtx.getDpid())
                .cityId(promoCtx.getCityId())
                .platform(promoCtx.isMt() ? PlatformEnum.MT.getCode() : PlatformEnum.DP.getCode())
                .os(iMobileContext.getOs())
                .appVersion(iMobileContext.getVersion())
                .appId(iMobileContext.getAppId())
                .miniProgramFlag(promoCtx.isMiniProgram())
                .build();
        logUtils.logInfo("===========ab=========:{}", abTestRequest);
        AbTestInfo abTestInfo = abTestService.tryAb(abTestRequest);
        if (abTestInfo != null) {
            result.put(AbTestConstant.POI_MMC_ISSUE_COUPON_AB, abTestInfo);
        }
        return result;
    }

    private void grayMMCoupon(CouponActivityContext promoCtx, IMobileContext iMobileContext) {
        //美团端不需要灰度
        if (promoCtx.isMt() || !promoCtx.isNeedMagicalMemberCoupon() || promoCtx.getMtUserId() <= 0) {
            return;
        }
        try {
            MagicControlReq magicControlReq = buildMagicControlReq(promoCtx, iMobileContext);

            ItemParamForControl itemParamForControl = new ItemParamForControl();
            itemParamForControl.setPoiId(promoCtx.getMtShopIdL());
            if (promoCtx.getDpShopCategoryPair() != null && promoCtx.getDpShopCategoryPair().getRight() != null) {
                itemParamForControl.setPoiSecondCategory(String.valueOf(promoCtx.getDpShopCategoryPair().getRight()));
            }
            MagicControlResult magicControlResult = MagicGrayUtil.magicControl(magicControlReq, itemParamForControl);
            if (!magicControlResult.isPass()) {
                logger.info("grayMMCoupon# not pass, reason:{}",magicControlResult.getFailReason());
            }
            Cat.logEvent(CatEventConstants.DP_MMC_MAGIC_CONTROL_EVENT_NAME, magicControlResult.isPass() ? "paas" : "fail");
            promoCtx.setDpMmcGrayControlPaas(magicControlResult.isPass());
        }catch (Exception e) {
            logger.error("grayMMCoupon# error, errMsg:{}", e.getMessage(), e);
        }

    }

    private static MagicControlReq buildMagicControlReq(CouponActivityContext promoCtx, IMobileContext iMobileContext) {
        MagicControlReq magicControlReq = new MagicControlReq();
        magicControlReq.setMagicControlScene(MagicControlEnum.MAGIC_COUPON_TAG.getValue());
        magicControlReq.setNibBiz(BizLineEnum.GENERAL.name());
        magicControlReq.setClientType(promoCtx.getCPlatform() == 1 ? String.valueOf(ClientTypeEnum.DP_IPHONE.getValue()) : String.valueOf(ClientTypeEnum.DP_ANDROID.getValue()));
        magicControlReq.setMtUserId(promoCtx.getMtUserId());
        magicControlReq.setDpUserId(promoCtx.getDpUserId());
        magicControlReq.setCityId((long) promoCtx.getMtActualCityId());
        magicControlReq.setVersion(iMobileContext.getVersion());
        magicControlReq.setPageSource(PageSourceEnum.POI_DETAIL.name());
        return magicControlReq;
    }

    private void resetUserId(CouponActivityContext promoCtx) {
        Cat.logEvent("INVALID_METHOD_5", "com.dianping.pay.api.biz.activity.newloader.CouponIssueActivityQueryService.resetUserId(com.dianping.pay.api.biz.activity.newloader.dto.CouponActivityContext)");
        if(promoCtx.getUserId() <= 0) {
            if(promoCtx.isMt() && promoCtx.getMtUserId() > 0) {
                promoCtx.setUserId(promoCtx.getMtUserId());
            }
            if(!promoCtx.isMt() && promoCtx.getDpUserId() > 0) {
                promoCtx.setUserId(promoCtx.getDpUserId());
            }
        }
    }


    private void fillCtxInitInfo(final PromotionRequestContext promotionRequestContext, final CouponActivityContext promoCtx, IMobileContext iMobileContext) {
        if (StringUtils.isNotEmpty(promotionRequestContext.getChannel()) && promotionRequestContext.getChannel().equals(ChannelProductEnum.ODP.getDesc())) {
            return;
        }
        // promoCtx 一些通用参数初始化
        Future<DealGroupBaseDTO> dealBaseDTOFuture = null;
        Future<ProductDetailDO> productBaseDTOFuture = null;
        Future<DealDetailDO> dealDetailDOFuture = null;

        Future<Long> dpShopIdFuture = null;
        Future<Pair<Boolean, Boolean>> dealProxyPairFuture = null;

        Future<List<IdMapper>> idMapperFuture = null;
        Future<Long> mtRealUserIdFuture = null;
        Future<UserModel> mtUserModelFuture = null;
        Future<Map<String, AbTestInfo>> abFuture = null;

        Future<ShopDTO> shopDTOFuture = threadPool.submit(new Callable<ShopDTO>() {
            @Override
            public ShopDTO call() throws Exception {
                return shopWrapService.loadShopInfo(promotionRequestContext.getShopIdL(), promotionRequestContext.isMt());
            }
        });

        if (promoCtx.getQueryShopCouponType() == QueryShopCouponTypeEnum.BY_DEAL.getCode()) {
            if (promotionRequestContext.isMt()) {
                int dpDealGroupId = productService.getDpDealGroupIdByMtId(promotionRequestContext.getMtDealGroupId());
                promoCtx.setDpDealGroupId(dpDealGroupId);
                promotionRequestContext.setDpDealGroupId(dpDealGroupId);
            }
        }

        if (promotionRequestContext.isMt() && promotionRequestContext.getMtShopIdL() > 0) {
            dpShopIdFuture = threadPool.submit(new Callable<Long>() {
                @Override
                public Long call() throws Exception {
                    return getDpShopId(promotionRequestContext.getMtShopIdL());
                }
            });
        }
        if (promotionRequestContext.getQueryShopCouponType() == QueryShopCouponTypeEnum.BY_DEAL.getCode()) {
            dealBaseDTOFuture = threadPool.submit(new Callable<DealGroupBaseDTO>() {
                @Override
                public DealGroupBaseDTO call() throws Exception {
                    return getDealBase(promotionRequestContext);
                }
            });
            dealProxyPairFuture = threadPool.submit(new Callable<Pair<Boolean, Boolean>>() {
                @Override
                public Pair<Boolean, Boolean> call() throws Exception {
                    return dealPromoPairProxy(promotionRequestContext);
                }
            });
            dealDetailDOFuture = threadPool.submit(new Callable<DealDetailDO>() {
                @Override
                public DealDetailDO call() throws Exception {
                    return getDealGroupDetail(promotionRequestContext.getDpDealGroupId());
                }
            });
        }
        if (promotionRequestContext.getQueryShopCouponType() == QueryShopCouponTypeEnum.BY_SKU.getCode()) {
            productBaseDTOFuture = threadPool.submit(new Callable<ProductDetailDO>() {
                @Override
                public ProductDetailDO call() throws Exception {
                    return getProductBase(promotionRequestContext);
                }
            });
        }
        if (promotionRequestContext.getQueryShopCouponType() == QueryShopCouponTypeEnum.BY_DEALS.getCode()) {
            idMapperFuture = threadPool.submit(new Callable<List<IdMapper>>() {
                @Override
                public List<IdMapper> call() throws Exception {
                    return dealIdMapperService.queryByMtDealGroupIds(promotionRequestContext.getMtDealGroupIds());
                }
            });
        }
        if (promoCtx.getDpUserId() > 0 && !promotionRequestContext.isMt() && promoCtx.isNeedFinancialPromo()) {
            mtRealUserIdFuture = threadPool.submit(new Callable<Long>() {
                @Override
                public Long call() throws Exception {
                    return getMtRealUserId(promoCtx.getDpUserId());
                }
            });
        }
        if (promotionRequestContext.isMt() && promotionRequestContext.getMtUserId() > 0
                && (promoCtx.isNeedResourcesPromotion() || promoCtx.isNeedShopResourcesPromotion())) {
            mtUserModelFuture = threadPool.submit(new Callable<UserModel>() {
                @Override
                public UserModel call() throws Exception {
                    return userWrapperService.getUserByIdWithMsg(promoCtx.getMtUserId());
                }
            });
        }

        if (promotionRequestContext.isMt()) {
            abFuture = threadPool.submit(() -> preAbTest(promotionRequestContext, iMobileContext));
        }

        //点评id体系转换
        if (!promoCtx.isMt() && promoCtx.isNeedMagicalMemberCoupon() && Lion.getBooleanValue(LionConstants.DP_MMC_COUPON_DEGRADE_SWITCH, false)) {
            AsyncDpInfoLoader dpIdConverter = new AsyncDpInfoLoader(shopWrapperService, userWrapperService, threadPool);
            dpIdConverter.loadMtUserId(promoCtx.getDpUserId())
                    .loadUserMobile(promoCtx.getDpUserId())
                    .loadMtShopId(promotionRequestContext.getDpShopIdL())
                    .loadShopCategory(promotionRequestContext.getDpShopIdL())
                    .batchLoadMtCityId(promoCtx.getCityId(), promoCtx.getActualCityId());

            promoCtx.setMtShopIdL(dpIdConverter.getMtShopId());
            Pair<Long, Long> mtUserPairFuture = dpIdConverter.getMtUserPairFuture();
            if (mtUserPairFuture != null) {
                promoCtx.setMtUserId(mtUserPairFuture.getLeft());
                promoCtx.setMtVirtualUserId(mtUserPairFuture.getRight());
            }
            promoCtx.setPhone(dpIdConverter.getUserMobile());
            promoCtx.setDpShopCategoryPair(dpIdConverter.getShopCategory());
            Map<Integer, Integer> dpCityId2MtCityIdMap = dpIdConverter.getDpCityId2MtCityIdMap();
            promoCtx.setMtCityId(MapUtils.getInteger(dpCityId2MtCityIdMap, promoCtx.getCityId(), 0));
            promoCtx.setMtActualCityId(MapUtils.getInteger(dpCityId2MtCityIdMap, promoCtx.getActualCityId(), 0));
        }

        try {
            ShopDTO shopDTO = shopDTOFuture.get(500, TimeUnit.MILLISECONDS);
            promoCtx.setShopDTO(shopDTO);
        } catch (Exception e) {
            logger.error("shopDTOFuture get error.", e);
        }

        if (dealBaseDTOFuture != null) {
            try {
                DealGroupBaseDTO dealGroupBaseDTO = dealBaseDTOFuture.get(500, TimeUnit.MILLISECONDS);
                if (dealGroupBaseDTO != null) {
                    promoCtx.setDealGroupBaseDTO(dealGroupBaseDTO);
                    promoCtx.setDpDealGroupId(dealGroupBaseDTO.getDealGroupId());
                }
            } catch (Exception e) {
                logger.error("dealBaseDTOFuture get error.", e);
            }
        }
        if(dealDetailDOFuture != null) {
            try {
                DealDetailDO dealDetailDO = dealDetailDOFuture.get(500, TimeUnit.MILLISECONDS);
                if (dealDetailDO != null) {
                    promoCtx.setDealDetailDO(dealDetailDO);
                }
            } catch (Exception e) {
                logger.error("dealDetailDOFuture get error.", e);
            }
        }
        if (productBaseDTOFuture != null) {
            try {
                int timeout = Lion.getIntValue(LionConstants.PRODUCT_BASE_TIMEOUT, 500);
                ProductDetailDO productDetailDO = productBaseDTOFuture.get(timeout, TimeUnit.MILLISECONDS);
                promoCtx.setProductDetailDO(productDetailDO);
            } catch (Exception e) {
                logger.error("productBaseDTOFuture get error.", e);
            }
        }
        if (dpShopIdFuture != null) {
            try {
                long dpShopId = dpShopIdFuture.get(500, TimeUnit.MILLISECONDS);
                promoCtx.setDpShopIdL(dpShopId);
            } catch (Exception e) {
                logger.error("dpShopIdFuture get error.", e);
            }
        }
        if (dealProxyPairFuture != null) {
            try {
                Pair<Boolean, Boolean> dealPromoProxy = dealProxyPairFuture.get(500, TimeUnit.MILLISECONDS);
                if (dealPromoProxy != null) {
                    promoCtx.setDealPromoProxy(dealPromoProxy.getLeft());
                    //团购的价格力优化展示赋值
                    promoCtx.setBeMemberPriceProxy(dealPromoProxy.getRight());
                }
            } catch (Exception e) {
                logger.error("dpShopIdFuture get error.", e);
            }
        }
        if (idMapperFuture != null) {
            try {

                List<IdMapper> idMappers = idMapperFuture.get(500, TimeUnit.MILLISECONDS);
                if (CollectionUtils.isNotEmpty(idMappers)) {
                    Map<Integer, Integer> mtDpDealGroupMap = Maps.newHashMapWithExpectedSize(idMappers.size());
                    for (IdMapper idMapper : idMappers) {
                        mtDpDealGroupMap.put(idMapper.getMtDealGroupID(), idMapper.getDpDealGroupID());
                    }
                    promoCtx.setMtDpDealGroupMap(mtDpDealGroupMap);
                }
            } catch (Exception e) {
                logger.error("idMapperFuture get error.", e);
            }
        }
        if (mtRealUserIdFuture != null) {
            try {

                Long mtRealUserId = mtRealUserIdFuture.get(500, TimeUnit.MILLISECONDS);
                if (mtRealUserId != null) {
                    promoCtx.setMtRealUserIdFromDp(mtRealUserId);
                }
            } catch (Exception e) {
                logger.error("idMapperFuture get error.", e);
            }
        }
        if (mtUserModelFuture != null) {
            try {
                UserModel userModel = mtUserModelFuture.get(500, TimeUnit.MILLISECONDS);
                if (userModel != null) {
                    promoCtx.setPhone(userModel.getMobile());
                }
            } catch (Exception e) {
                logger.error("mtUserModelFuture get error.", e);
            }
        }
        if (abFuture != null) {
            try {
                promoCtx.setAbResult(abFuture.get(500, TimeUnit.MILLISECONDS));
            }catch (Exception e) {
                logger.error("ab test get error.", e);
            }
        }
    }

    private Long getMtRealUserId(long dpUserId) {
        try {
            BindRelationResp realBind = rpcUserService.getRealBindByDpUserId(dpUserId);
            if (realBind != null && realBind.isSuccess()) {
                return realBind.getData() == null ? null : (realBind.getData().getMtUserId() == null ? null : realBind.getData().getMtUserId().getId());
            }else{
                logger.warn("getMtRealUserId fail,dpUserid:" + dpUserId);
            }
        } catch (Exception e) {
            logger.error("getRealBindByDpUserId error, dpUserid:" + dpUserId, e);
        }
        return null;
    }


    private ProductDetailDO getProductBase(PromotionRequestContext promotionRequestContext) {
        try {
            if (promotionRequestContext.getSkuId() <= 0) {
                return null;
            }
            return productService.loadProductDetail(promotionRequestContext.getSkuId());
        } catch (Exception e) {
            logger.error("getProductBase exception!", e);

        }
        return null;
    }

    private DealGroupBaseDTO getDealBase(PromotionRequestContext promotionRequestContext) {
        try {
            if (promotionRequestContext.getDpDealGroupId() <= 0) {
                return null;
            }
            return productService.getDealGroup(promotionRequestContext.getDpDealGroupId());
        } catch (Exception e) {
            logger.error("getDealBase exception!", e);
        }
        return null;
    }

    private DealDetailDO getDealGroupDetail(int dpDealGroupId) {
        try{
            if (dpDealGroupId <= 0) {
                return null;
            }
            return productService.getDealGroupDetailInfo(dpDealGroupId);
        } catch(Exception e) {
            logger.error("getDealDetail exception!, dpDealGroupId:{}", dpDealGroupId, e);
        }
        return null;
    }

    private long getDpShopId(long mtShopId) {
        try {
            List<Long> dpShopIds = poiRelationCacheService.queryDpByMtIdL(mtShopId);
            return CollUtils.getFirst(dpShopIds, 0);
        } catch (Exception e) {
            logger.error("queryDpByMtId exception!", e);
            return 0;
        }
    }

    private Pair<Boolean, Boolean> dealPromoPairProxy(PromotionRequestContext promotionRequestContext) {
        try {
            boolean isDealPromoProxy, beMemberPriceProxy;
            List<Integer> dealPromoWhiteList = Lion.getList(LionConstants.DEAL_PROXY_WHITE_LIST, Integer.class);
            List<Integer> memberPriceWhiteList = Lion.getList(LionConstants.DEAL_MEMBER_WHITE_LIST, Integer.class);

            if (CollectionUtils.isNotEmpty(dealPromoWhiteList) && CollectionUtils.isNotEmpty(memberPriceWhiteList)) {
                isDealPromoProxy = dealPromoWhiteList.contains(promotionRequestContext.isMt() ? promotionRequestContext.getMtDealGroupId() : promotionRequestContext.getDpDealGroupId());
                beMemberPriceProxy = memberPriceWhiteList.contains(promotionRequestContext.isMt() ? promotionRequestContext.getMtDealGroupId() : promotionRequestContext.getDpDealGroupId());
            } else {

                List<Integer> dealProxyCategories = Lion.getList(LionConstants.DEAL_PROXY_CATEGORY, Integer.class);
                List<Integer> dealMemberCategories = Lion.getList(LionConstants.DEAL_MEMBER_CATEGORY, Integer.class);

                DealGroupChannelDTO channelDTO = productService.loadDealGroupChannelByDpId(promotionRequestContext.getDpDealGroupId());

                isDealPromoProxy = CollectionUtils.isNotEmpty(dealPromoWhiteList)?
                        dealPromoWhiteList.contains(promotionRequestContext.isMt() ? promotionRequestContext.getMtDealGroupId() : promotionRequestContext.getDpDealGroupId()) :
                        channelDTO != null && CollectionUtils.isNotEmpty(dealProxyCategories) && dealProxyCategories.contains(channelDTO.getCategoryId());
                beMemberPriceProxy = CollectionUtils.isNotEmpty(memberPriceWhiteList)?
                        memberPriceWhiteList.contains(promotionRequestContext.isMt() ? promotionRequestContext.getMtDealGroupId() : promotionRequestContext.getDpDealGroupId()) :
                        channelDTO != null && CollectionUtils.isNotEmpty(dealMemberCategories) && dealMemberCategories.contains(channelDTO.getCategoryId());
            }

            return Pair.of(isDealPromoProxy, promotionRequestContext.isNeedMemberPromotion() && beMemberPriceProxy);
        } catch (Exception e) {
            logger.error("getDealProxy exception!", e);
            return Pair.of(false, false);
        }
    }

    public QuerySimpleResponseDTO queryActivitiesByBizIds(ShopCartIssueMultiCouponContext context) {
        BatchCouponBizIdQueryRequest batchCouponBizIdQueryRequest = new BatchCouponBizIdQueryRequest();
        Map<Integer, List<Number>> bizIdMap = Maps.newHashMap();
        if (CollectionUtils.isNotEmpty(context.getDpDealGroupIds())) {
            bizIdMap.put(BizIdType.DEAL_GROUP_ID.getCode(), Lists.transform(context.getDpDealGroupIds(), FunctionPool.INTEGER_TO_NUMBER));
        }
        if (CollectionUtils.isNotEmpty(context.getMtDealGroupIds()) && MapUtils.isNotEmpty(context.getMtDpDealGroupMap())) {
            bizIdMap.put(BizIdType.DEAL_GROUP_ID.getCode(), Lists.transform(Lists.newArrayList(context.getMtDpDealGroupMap().values()), FunctionPool.INTEGER_TO_NUMBER));
        }
        if (CollectionUtils.isNotEmpty(context.getSkuIds())) {
            bizIdMap.put(BizIdType.SKU_ID.getCode(), Lists.transform(context.getSkuIds(), FunctionPool.INTEGER_TO_NUMBER));
        }
        if (MapUtils.isEmpty(bizIdMap)) {
            logger.warn("queryActivitiesByBizIds# empty bizIdMap. context: {}", context);
            return null;
        }
        batchCouponBizIdQueryRequest.setBizIdMap(bizIdMap);
        batchCouponBizIdQueryRequest.setMt(context.isMt());
        Response<QuerySimpleResponseDTO> simpleResponseDTOResponse = tgcGetCouponComponentQueryService.batchQueryAllKindCouponListByBizId(batchCouponBizIdQueryRequest);
        if (simpleResponseDTOResponse == null || !simpleResponseDTOResponse.isSuccess() || simpleResponseDTOResponse.getResult() == null) {
            logger.warn("queryActivitiesByBizIds# queryActivitiesByBizIds error. context: {}, simpleResponseDTOResponse: {}", context, simpleResponseDTOResponse);
            return null;
        }
        return simpleResponseDTOResponse.getResult();
    }

    public QuerySimpleResponseDTO queryDealGroupActivities(List<Integer> dpDealGroupIdList, Map<Integer, Integer> mtDpDealGroupMap, boolean isMt) {
        BatchCouponBizIdQueryRequest batchCouponBizIdQueryRequest = new BatchCouponBizIdQueryRequest();
        Map<Integer, List<Number>> bizIdMap = Maps.newHashMap();
        if (CollectionUtils.isNotEmpty(dpDealGroupIdList)) {
            bizIdMap.put(BizIdType.DEAL_GROUP_ID.getCode(), Lists.transform(dpDealGroupIdList, FunctionPool.INTEGER_TO_NUMBER));
        }
        if (MapUtils.isNotEmpty(mtDpDealGroupMap)) {
            bizIdMap.put(BizIdType.DEAL_GROUP_ID.getCode(), Lists.transform(Lists.newArrayList(mtDpDealGroupMap.values()), FunctionPool.INTEGER_TO_NUMBER));
        }
        if (MapUtils.isEmpty(bizIdMap)) {
            return null;
        }
        batchCouponBizIdQueryRequest.setBizIdMap(bizIdMap);
        batchCouponBizIdQueryRequest.setMt(isMt);
        Response<QuerySimpleResponseDTO> simpleResponseDTOResponse = tgcGetCouponComponentQueryService.batchQueryAllKindCouponListByBizId(batchCouponBizIdQueryRequest);
        if (simpleResponseDTOResponse == null || !simpleResponseDTOResponse.isSuccess() || simpleResponseDTOResponse.getResult() == null) {
            logger.error("queryDealGroupActivities# queryDealGroupActivities error. batchCouponBizIdQueryRequest: {}, simpleResponseDTOResponse: {}", batchCouponBizIdQueryRequest, simpleResponseDTOResponse);
            return null;
        }
        return simpleResponseDTOResponse.getResult();
    }

    private void transferMmcParam(PromotionRequestContext promotionRequestContext, CouponActivityContext couponActivityContext) {
        if (Objects.isNull(promotionRequestContext) || Objects.isNull(couponActivityContext)) {
            return;
        }
        couponActivityContext.setMmcInflate(promotionRequestContext.getMmcInflate());
        couponActivityContext.setMmcBuy(promotionRequestContext.getMmcBuy());
        couponActivityContext.setMmcUse(promotionRequestContext.getMmcUse());
        couponActivityContext.setMmcFree(promotionRequestContext.getMmcFree());
    }
}
