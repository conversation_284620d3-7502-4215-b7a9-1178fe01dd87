package com.dianping.pay.api.biz.activity.newloader.executors;

import com.dianping.pay.api.beans.IssueCouponActivity;
import com.dianping.pay.api.biz.activity.newloader.AbstractCouponIssueActivityQueryExecutor;
import com.dianping.pay.api.entity.issuecoupon.IssueCouponRequest;
import com.dianping.pay.api.enums.IssueCouponOptionType;
import com.dianping.spc.common.Response;
import com.dianping.tgc.open.SkuActivityQueryRemoteService;
import com.dianping.tgc.open.entity.ActivityDTO;
import com.dianping.tgc.open.entity.SkuActivityBatchQueryRequest;
import com.dianping.tgc.open.entity.SkuActivityQueryResponseDTO;
import com.google.common.collect.Lists;
import org.apache.commons.collections.MapUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

public class SkuActivityQueryExecutor extends AbstractCouponIssueActivityQueryExecutor {
    private static final Logger LOGGER = LogManager.getLogger(SkuActivityQueryExecutor.class);
    private SkuActivityQueryRemoteService skuActivityQueryRemoteService;

    public SkuActivityQueryExecutor(IssueCouponRequest request) {
        super(request);
    }

    @Override
    protected List<IssueCouponActivity> loadFromRemote() {
        if (request.getProductId() == 0) {
            return Lists.newArrayList();
        }
        SkuActivityBatchQueryRequest remoteRequest = new SkuActivityBatchQueryRequest();
        long skuid = request.getProductId();
        remoteRequest.setSkuIds(Lists.newArrayList(skuid));
        if (request.isDpClient()) {
            remoteRequest.setSource(1);
        } else {
            remoteRequest.setSource(2);
        }
        Response<SkuActivityQueryResponseDTO> response = skuActivityQueryRemoteService.queryActivityDTOsBySkuIds(remoteRequest);
        if (!response.isSuccess() || response.getResult() == null) {
            return Lists.newArrayList();
        }
        Map<Long, List<ActivityDTO>> skuActivityMap = response.getResult().getSkuActivityMap();
        if (MapUtils.isEmpty(skuActivityMap)) {
            return Lists.newArrayList();
        }
        List<ActivityDTO> activities = new ArrayList<>();
        for (Map.Entry<Long, List<ActivityDTO>> activity : skuActivityMap.entrySet()) {
            activities.addAll(activity.getValue());
        }
        return toIssueCouponActivities(new ArrayList<>(activities));
    }

    public void setSkuActivityQueryRemoteService(SkuActivityQueryRemoteService skuActivityQueryRemoteService) {
        this.skuActivityQueryRemoteService = skuActivityQueryRemoteService;
    }

    private List<IssueCouponActivity> toIssueCouponActivities(List<ActivityDTO> activities) {
        List<IssueCouponActivity> resultList = Lists.newArrayList();
        for (ActivityDTO activityDTO : activities) {
            try {
                if (activityDTO.getCouponDTO().isUpDailyLimit()) {
                    continue;
                }
                resultList.add(new IssueCouponActivity(
                        activityDTO,
                        request.isDpClient() ? IssueCouponOptionType.DP_SHOP_EVENT.getCode() : IssueCouponOptionType.MT_SHOP_EVENT.getCode())
                );
            } catch (IllegalArgumentException e) {
                LOGGER.info(String.format("SkuActivityQueryExecutor toIssueCouponActivities IllegalArgumentException,coupon:%s",activityDTO.getCouponDTO().getPromoID()));
            }

        }
        return resultList;
    }
}
