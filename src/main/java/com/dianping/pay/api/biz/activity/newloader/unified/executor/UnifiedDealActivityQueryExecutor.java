package com.dianping.pay.api.biz.activity.newloader.unified.executor;

import com.dianping.deal.idmapper.api.DealIdMapperService;
import com.dianping.deal.idmapper.api.dto.IdMapper;
import com.dianping.pay.api.beans.IssueCouponActivity;
import com.dianping.pay.api.biz.activity.newloader.AbstractUnifiedCouponActivityQueryExecutor;
import com.dianping.pay.api.biz.activity.newloader.dto.CouponIssueActivityQueryContext;
import com.dianping.pay.api.biz.activity.newloader.mapper.IssueActivityMapper;
import com.dianping.spc.common.Response;
import com.dianping.tgc.open.DealGroupActivityQueryRemoteService;
import com.dianping.tgc.open.entity.ActivityDTO;
import com.dianping.tgc.open.entity.ActivityQueryOption;
import com.dianping.tgc.open.entity.DealGroupActivityBatchQueryRequest;
import com.dianping.tgc.open.entity.DealGroupActivityQueryResponseDTO;
import com.dianping.tgc.open.enums.QuerySourceEnum;
import com.google.common.collect.Lists;
import org.apache.commons.collections.MapUtils;

import java.util.List;
import java.util.Map;

/**
 * Created by drintu on 18/6/7.
 */

public class UnifiedDealActivityQueryExecutor extends AbstractUnifiedCouponActivityQueryExecutor{

    private DealGroupActivityQueryRemoteService dealGroupActivityQueryRemoteService;
    private DealIdMapperService dealIdMapperService;

    public UnifiedDealActivityQueryExecutor(CouponIssueActivityQueryContext queryContext){
        super(queryContext);
    }

    @Override
    protected List<IssueCouponActivity> loadFromRemote() {
        if (queryContext.getProductId() == 0) {
            return Lists.newArrayList();
        }
        List<Integer> dpDealGroupIdList = null;
        if (queryContext.isDpClient()) {
            dpDealGroupIdList = Lists.newArrayList(queryContext.getProductId());
        } else {
            IdMapper idMapper = dealIdMapperService.queryByMtDealGroupId(queryContext.getProductId());
            if (idMapper != null) {
                dpDealGroupIdList = Lists.newArrayList(idMapper.getDpDealGroupID());
            }
        }
        DealGroupActivityBatchQueryRequest remoteRequest = new DealGroupActivityBatchQueryRequest(dpDealGroupIdList, queryContext.isDpClient() ? QuerySourceEnum.DP.getValue() : QuerySourceEnum.MT.getValue());
        ActivityQueryOption queryOption = new ActivityQueryOption();
        queryOption.setFillDiscountCoupon(true);
        remoteRequest.setActivityQueryOption(queryOption);

        Response<DealGroupActivityQueryResponseDTO> response = dealGroupActivityQueryRemoteService.queryActivityDTOsByDealGroupIds(remoteRequest);
        if (!response.isSuccess() || response.getResult() == null) {
            return Lists.newArrayList();
        }
        Map<Integer, List<ActivityDTO>> dealGroupActivityMap = response.getResult().getDealGroupActivityMap();
        if (MapUtils.isEmpty(dealGroupActivityMap)) {
            return Lists.newArrayList();
        }
        return IssueActivityMapper.activities2IssueActivities(dealGroupActivityMap, queryContext.isDpClient());
    }



    public UnifiedDealActivityQueryExecutor setDealGroupActivityQueryRemoteService(DealGroupActivityQueryRemoteService dealGroupActivityQueryRemoteService) {
        this.dealGroupActivityQueryRemoteService = dealGroupActivityQueryRemoteService;
        return this;
    }

    public UnifiedDealActivityQueryExecutor setDealIdMapperService(DealIdMapperService dealIdMapperService) {
        this.dealIdMapperService = dealIdMapperService;
        return this;
    }
}
