package com.dianping.pay.api.biz;

import com.alibaba.fastjson.JSON;
import com.dianping.api.util.ApiUtil;
import com.dianping.api.util.CrossDomainUtils;
import com.dianping.api.util.DotUtils;
import com.dianping.cat.Cat;
import com.dianping.deal.idmapper.api.DealIdMapperService;
import com.dianping.deal.idmapper.api.dto.IdMapper;
import com.dianping.gmkt.coupon.common.api.enums.IssueDetailSourceEnum;
import com.dianping.mobile.framework.datatypes.CommonMobileResponse;
import com.dianping.mobile.framework.datatypes.IMobileContext;
import com.dianping.mobile.framework.datatypes.IMobileResponse;
import com.dianping.pay.api.biz.activity.CouponInfoBiz;
import com.dianping.pay.api.biz.activity.IssueCouponBiz;
import com.dianping.pay.api.biz.activity.RestUserInfoService;
import com.dianping.pay.api.biz.activity.newloader.dto.RestUserInfo;
import com.dianping.pay.api.biz.activity.newloader.mapper.IssueActivityMapper;
import com.dianping.pay.api.entity.issuecoupon.*;
import com.dianping.pay.api.exception.IssueCouponException;
import com.dianping.pay.promo.common.enums.coupon.UnifiedCouponGroupType;
import com.dianping.unified.coupon.manage.api.dto.UnifiedCouponGroupDTO;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang.Validate;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2023/5/16
 */
@Service
public class ShopCartIssueMultiCouponBiz {

    public static final Logger logger = LoggerFactory.getLogger(ShopCartIssueMultiCouponBiz.class);

    public static final String CAT_TYPE = "ShopCartIssueMultiCouponAction";

    @Autowired
    private IssueCouponBiz issueCouponBiz;
    @Autowired
    private RestUserInfoService restUserInfoService;
    @Autowired
    private CouponInfoBiz couponInfoBiz;
    @Autowired
    private DealIdMapperService dealIdMapperService;

    public IMobileResponse validate(ShopCartIssueMultiCouponRequest request, IMobileContext iMobileContext) {
        try {
            if (iMobileContext.getRequest() != null) {
                DotUtils.addRefererDot(iMobileContext.getRequest());
            }
            Validate.notNull(request, "null request");

            if (ApiUtil.isMapiRequest(iMobileContext)) {
                Validate.isTrue(iMobileContext.getUserId() > 0 || (iMobileContext.isMtClient() && iMobileContext.getUserStatus() != null && iMobileContext.getUserStatus().getMtUserId() > 0), "用户未登录");
            }
            Validate.isTrue(StringUtils.isNotBlank(request.getShopcartproducts()), "invalid products");
            Validate.isTrue(StringUtils.isNotEmpty(request.getUnifiedcoupongroupids()), " 券 id 列表非法");

            List<String> couponGroupIds = Lists.newArrayList(StringUtils.split(request.getUnifiedcoupongroupids(), ","));
            Validate.isTrue(couponGroupIds.size() <= 20, "券批次个数过多");

            if (request.getIssuedetailsourcecode() != null) {
                Validate.isTrue(IssueDetailSourceEnum.getByCode(request.getIssuedetailsourcecode()) != null, "issuedetailsourcecode invalid");
            }
        } catch (IllegalArgumentException e) {
            logger.warn("invalid request:{}", request, e);
            return new CommonMobileResponse(new IssueCouponMsg(401, e.getMessage(), e.getMessage()));
        }
        return null;
    }

    public UnifiedIssueMultiCouponRespDo doIssueShopCartCoupon(ShopCartIssueMultiCouponRequest request, IMobileContext iMobileContext) {
        CrossDomainUtils.setEnableCrossDomain(iMobileContext.getRequest(), iMobileContext.getResponse());
        try {
            ShopCartIssueMultiCouponContext context;
            List<ShopCartProduct> shopCartProducts;
            logger.info("ShopCartIssueMultiCouponAction# request:{}", request);
            try {
                shopCartProducts = JSON.parseArray(request.getShopcartproducts(), ShopCartProduct.class);
                if (CollectionUtils.isEmpty(shopCartProducts)) {
                    logger.error("ShopCartIssueMultiCouponAction# empty product, request:{}", request);
                    Cat.logEvent(CAT_TYPE, "empty shopcartproducts", "-1", JSON.toJSONString(request));
                    return null;
                }
            } catch (Exception e) {
                logger.warn("validate shopcartproducts exception!, shopcartproducts:{}", request.getShopcartproducts(), e);
                Cat.logEvent(CAT_TYPE, "invalid shopCartProducts", "-1", JSON.toJSONString(request));
                return null;
            }

            // 如果是h5场景
            if (!ApiUtil.isMapiRequest(iMobileContext)) {
                RestUserInfo restUserInfo = restUserInfoService.handleMobileContext(iMobileContext);
                context = IssueActivityMapper.buildHttpPromotionRequestContext(request, restUserInfo, shopCartProducts);
            } else {
                context = IssueActivityMapper.buildNativePromotionRequestContext(request, iMobileContext, shopCartProducts);
            }

            //领券商家券
            if (context.getUserId() <= 0) {
                Cat.logEvent("InvalidUserId", "shopcartissuemulticouponaction.bin");
                throw new IssueCouponException("用户未登录", 4);
            }

            List<UnifiedIssueDetailResult> issueResult;
            Map<String, UnifiedCouponGroupDTO> couponGroupDTOMap = couponInfoBiz.batchLoadCouponGroup(context.getCouponGroupIds());
            if (MapUtils.isEmpty(couponGroupDTOMap)) {
                throw new IssueCouponException("活动不存在", 3);
            }
            for (Map.Entry<String, UnifiedCouponGroupDTO> entry : couponGroupDTOMap.entrySet()) {
                int couponGroupType = entry.getValue().getCouponGroupType();
                if (couponGroupType != UnifiedCouponGroupType.shopCoupon.value && couponGroupType != UnifiedCouponGroupType.mtShopCoupon.value) {
                    throw new IssueCouponException("不支持的券类型", 4);
                }
            }

            fillContext(context);
            issueResult = issueCouponBiz.doIssueShopCartMultiCoupon(context);
            // 拼装返回结果

            UnifiedIssueMultiCouponRespDo unifiedIssueMultiCouponRespDo = new UnifiedIssueMultiCouponRespDo();
            unifiedIssueMultiCouponRespDo.setSuccess(true);
            unifiedIssueMultiCouponRespDo.setIssueDetail(issueResult);
            logger.info("ShopCartIssueMultiCouponAction# request:{}, unifiedIssueMultiCouponRespDo:{}", request, unifiedIssueMultiCouponRespDo);
            return unifiedIssueMultiCouponRespDo;
        } catch (IssueCouponException e) {
            logger.error("ShopCartIssueMultiCouponAction# request:{}", request, e);
            UnifiedIssueMultiCouponRespDo unifiedIssueCouponRespDo = new UnifiedIssueMultiCouponRespDo();
            unifiedIssueCouponRespDo.setSuccess(false);
            unifiedIssueCouponRespDo.setErrorMsg(e.getMessage());
            return unifiedIssueCouponRespDo;
        }
    }

    private void fillContext(ShopCartIssueMultiCouponContext context) {
        if (CollectionUtils.isNotEmpty(context.getMtDealGroupIds())) {
            List<IdMapper> idMappers = dealIdMapperService.queryByMtDealGroupIds(context.getMtDealGroupIds());
            Map<Integer, Integer> mtDpDealGroupMap = Maps.newHashMapWithExpectedSize(context.getMtDealGroupIds().size());
            if (CollectionUtils.isNotEmpty(idMappers)) {
                for (IdMapper idMapper : idMappers) {
                    mtDpDealGroupMap.put(idMapper.getMtDealGroupID(), idMapper.getDpDealGroupID());
                }
            }
            context.setMtDpDealGroupMap(mtDpDealGroupMap);
        }
    }

}
