package com.dianping.pay.api.biz.activity.newloader.promo;

import com.dianping.api.constans.SplitConstants;
import com.dianping.gm.bonus.exposure.api.dto.BEResponse;
import com.dianping.gm.bonus.exposure.api.dto.BonusExposureQueryRequestDTO;
import com.dianping.gm.bonus.exposure.api.dto.CommonBonusExposureDTO;
import com.dianping.gm.bonus.exposure.api.enums.BonusTypeEnum;
import com.dianping.gm.bonus.exposure.api.service.BonusExposureQueryService;
import com.dianping.mobile.framework.datatypes.IMobileContext;
import com.dianping.pay.api.biz.activity.RestUserInfoService;
import com.dianping.pay.api.biz.activity.newloader.dto.CouponActivityContext;
import com.dianping.pay.api.biz.activity.newloader.dto.RestUserInfo;
import com.dianping.pay.api.entity.issuecoupon.ReturnPromotionDetail;
import com.dianping.pay.api.entity.issuecoupon.ReturnPromotionDisplayDto;
import com.dianping.pay.api.enums.QueryShopCouponTypeEnum;
import com.dianping.pay.api.util.DotUtils;
import com.dianping.pay.api.util.PoiIdUtils;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.concurrent.Callable;
import java.util.concurrent.Future;
import java.util.concurrent.TimeUnit;

@Component("returnPromotionProcessor")
@Slf4j
public class ReturnPromotionProcessor extends AbstractPromoProcessor {

    @Autowired
    private RestUserInfoService restUserInfoService;

    @Autowired
    private BonusExposureQueryService bonusExposureQueryService;

    @Override
    public void prePare(final CouponActivityContext promoCtx, final IMobileContext iMobileContext) {
        try {
            if (!promoCtx.isNeedReturnPromotion()) {
                return;
            }
            DotUtils.dotForPromoProcessor(promoCtx, getPromoName());
            Future<ReturnPromotionDisplayDto> future = ExecutorService.submit(new Callable<ReturnPromotionDisplayDto>() {
                @Override
                public ReturnPromotionDisplayDto call() throws Exception {
                    return queryBonus(promoCtx, iMobileContext);
                }
            });
            promoCtx.setReturnPromotionFuture(future);
        } catch (Exception e) {
            log.error("ReturnPromotionProcessor# prepare error. ctx: {}", promoCtx, e);
        }
    }

    @Override
    public void loadPromo(CouponActivityContext promoCtx) {
        try {
            if (promoCtx.getReturnPromotionDisplayDto() != null) {
                return;
            }
            Future<ReturnPromotionDisplayDto> returnPromotionDisplayDtoFuture = promoCtx.getReturnPromotionFuture();
            if (returnPromotionDisplayDtoFuture != null) {
                promoCtx.setReturnPromotionDisplayDto(returnPromotionDisplayDtoFuture.get(500, TimeUnit.MILLISECONDS));
            }
        } catch (Exception e) {
            log.error("ReturnPromotionProcessor# loadPromo error. ctx: {}", promoCtx, e);
        }
    }

    @Override
    public String getPromoName() {
        return "returnPromo";
    }

    private ReturnPromotionDisplayDto queryBonus(CouponActivityContext promoCtx, IMobileContext iMobileContext) {
        ReturnPromotionDisplayDto returnPromotionDisplayDto = null;
        try {
            BonusExposureQueryRequestDTO bonusExposureQueryRequestDTO = new BonusExposureQueryRequestDTO();

            if (promoCtx.isMt()) {
                bonusExposureQueryRequestDTO.setPlatform(2);
                bonusExposureQueryRequestDTO.setShopIdL(promoCtx.getDpShopIdL());
                if (iMobileContext == null) {
                    return returnPromotionDisplayDto;
                }
                RestUserInfo dpUserInfoByMt = restUserInfoService.getDPUserInfoByMtToken(iMobileContext);
                String userIdStr = "";
                if (dpUserInfoByMt != null) {
                    userIdStr = String.valueOf(dpUserInfoByMt.getUserId());
                }
                bonusExposureQueryRequestDTO.setUserIdStr(userIdStr);
            } else {
                bonusExposureQueryRequestDTO.setPlatform(1);
                bonusExposureQueryRequestDTO.setShopIdL(promoCtx.getDpShopIdL());
                bonusExposureQueryRequestDTO.setUserIdStr(promoCtx.getDpUserId() > 0 ? String.valueOf(promoCtx.getDpUserId()) : "");
            }

            // 如果是根据团购查的，则不传shopid，传团购id
            if (promoCtx.getQueryShopCouponType() == QueryShopCouponTypeEnum.BY_DEAL.getCode()) {
                bonusExposureQueryRequestDTO.setDealGroupId(promoCtx.getDpDealGroupId());
                bonusExposureQueryRequestDTO.setShopIdL(0L);
            }

            BEResponse<List<CommonBonusExposureDTO>> queryBonusExposure =
                    bonusExposureQueryService.queryBonusExposure(bonusExposureQueryRequestDTO);
            if (queryBonusExposure != null && queryBonusExposure.isSuccess()) {
                List<CommonBonusExposureDTO> queryBonusExposureData = queryBonusExposure.getData();
                boolean isHit = CollectionUtils.isNotEmpty(queryBonusExposureData);
                DotUtils.dotForPromoHit(getPromoName(), promoCtx.isMt(), isHit);

                if (CollectionUtils.isNotEmpty(queryBonusExposureData)) {
                    //聚合
                    Map<BonusTypeEnum, List<CommonBonusExposureDTO>> bonusTypeEnumListMap = Maps.newTreeMap(new Comparator<BonusTypeEnum>() {
                        @Override
                        public int compare(BonusTypeEnum o1, BonusTypeEnum o2) {
                            return getBonusTypeEnumPriority(o1).compareTo(getBonusTypeEnumPriority(o2));
                        }
                    });

                    for (CommonBonusExposureDTO commonBonusExposureDTO : queryBonusExposureData) {
                        BonusTypeEnum bonusTypeEnumByCode =
                                BonusTypeEnum.getBonusTypeEnumByCode(commonBonusExposureDTO.getBonusType());
                        if (bonusTypeEnumByCode == null) {
                            continue;
                        }
                        if (!bonusTypeEnumListMap.containsKey(bonusTypeEnumByCode)) {
                            bonusTypeEnumListMap.put(bonusTypeEnumByCode, Lists.<CommonBonusExposureDTO>newArrayList());
                        }
                        bonusTypeEnumListMap.get(bonusTypeEnumByCode).add(commonBonusExposureDTO);
                    }

                    List<ReturnPromotionDetail> returnPromotionDetails = Lists.newArrayList();
                    for (Map.Entry<BonusTypeEnum, List<CommonBonusExposureDTO>> entry : bonusTypeEnumListMap.entrySet()) {
                        ReturnPromotionDetail returnPromotionDetail = new ReturnPromotionDetail();
                        returnPromotionDetail.setReturnPromotionDetailTitle(entry.getKey().getDesc());

                        Pair<String, String> returnPromotionDetailPair = buildReturnPromotionDetailPair(entry.getKey(), entry.getValue());
                        returnPromotionDetail.setSummaryInfo(returnPromotionDetailPair.getLeft());
                        returnPromotionDetail.setReturnPromotionDetailText(returnPromotionDetailPair.getRight());
                        returnPromotionDetail.setBonusType(entry.getKey().getCode());
                        returnPromotionDetails.add(returnPromotionDetail);
                    }

                    if (CollectionUtils.isNotEmpty(returnPromotionDetails)) {
                        returnPromotionDisplayDto = new ReturnPromotionDisplayDto();
                        returnPromotionDisplayDto.setReturnPromotionDirectUrl("");
                        returnPromotionDisplayDto.setReturnPromotionTitle("返礼");
                        returnPromotionDisplayDto.setReturnPromotionDetails(returnPromotionDetails);
                    }
                }
            } else {
                DotUtils.dotForPromoCallFail(getPromoName(), "queryBonusExposure", promoCtx.isMt());
            }
        } catch (Exception e) {
            log.error("queryBonusExposure exception!", e);
        }
        return returnPromotionDisplayDto;
    }

    private Pair<String, String> buildReturnPromotionDetailPair(BonusTypeEnum bonusTypeEnum, List<CommonBonusExposureDTO> commonBonusExposureDTOS) {
        String summaryInfo = "";
        String returnPromotionDetailText = "";

        //下单返券合并打车券
        if (bonusTypeEnum == BonusTypeEnum.GRANT_COUPON_AFTER_ORDER) {
            List<String> orderSummaryInfoList = Lists.newArrayListWithCapacity(2);
            String summaryInfoHead = null;
            String longestConditionInfo = "";
            List<String> giftInfoList = Lists.newArrayList();
            for (CommonBonusExposureDTO bonusExposureDTO : commonBonusExposureDTOS) {
                if (longestConditionInfo.length() < bonusExposureDTO.getConditionInfo().length()) {
                    longestConditionInfo = bonusExposureDTO.getConditionInfo();
                }
                giftInfoList.add(bonusExposureDTO.getGiftInfo());

                //需要在summaryInfo上拼上打车
                if (bonusExposureDTO.getSummaryInfo().contains("下单返")) {
                    orderSummaryInfoList.add(bonusExposureDTO.getSummaryInfo().replace("下单返", ""));
                } else {
                    String orderSummerInfo = bonusExposureDTO.getSummaryInfo().substring(bonusExposureDTO.getConditionInfo().indexOf('返') + 1);
                    summaryInfoHead = bonusExposureDTO.getSummaryInfo().replace(orderSummerInfo, "");
                    orderSummaryInfoList.add(orderSummerInfo);
                }
            }
            returnPromotionDetailText = longestConditionInfo + "返" + StringUtils.join(giftInfoList, "和");
            summaryInfo = StringUtils.defaultIfBlank(summaryInfoHead, "下单返") + StringUtils.join(orderSummaryInfoList, "和");
        } else {
            List<String> summaryTemps = Lists.newArrayList();
            List<String> titleList = Lists.newArrayList();
            for (CommonBonusExposureDTO bonusExposureDTO : commonBonusExposureDTOS) {
                titleList.add(bonusExposureDTO.getConditionInfo() + "返" + bonusExposureDTO.getGiftInfo());
                summaryTemps.add(bonusExposureDTO.getSummaryInfo());
            }
            returnPromotionDetailText = StringUtils.join(titleList, SplitConstants.COMMON_SPLIT);
            summaryInfo = StringUtils.join(summaryTemps, SplitConstants.COMMON_SPLIT);
        }
        return Pair.of(summaryInfo, returnPromotionDetailText);
    }

    /**
     * 消费返券>下单返券>评价返券
     *
     * @param bonusTypeEnum
     * @return
     */
    private static Integer getBonusTypeEnumPriority(BonusTypeEnum bonusTypeEnum) {
        switch (bonusTypeEnum) {
            case GRANT_COUPON_AFTER_VERIFY:
                return 1;
            case GRANT_COUPON_AFTER_ORDER:
                return 2;
            case GRANT_COUPON_AFTER_REVIEW:
                return 3;
            default:
                return 999;
        }
    }


}
