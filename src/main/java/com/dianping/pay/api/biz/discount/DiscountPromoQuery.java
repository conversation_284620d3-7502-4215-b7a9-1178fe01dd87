package com.dianping.pay.api.biz.discount;

import com.dianping.api.domain.Version;
import com.dianping.cat.Cat;
import com.dianping.pay.api.beans.GetPromoDeskRequest;
import com.dianping.pay.api.beans.PromoDTOGroup;
import com.dianping.pay.api.beans.PromoProduct;
import com.dianping.pay.api.entity.promodesk.DiscountPromoTool;
import com.dianping.pay.cashier.biz.enums.PaymentRule;
import com.dianping.pay.promo.common.enums.PromoSource;
import com.dianping.pay.promo.execute.service.PayPromoExecuteQueryService;
import com.dianping.pay.promo.execute.service.dto.PromoExecuteDetailDTO;
import com.dianping.pay.promo.execute.service.dto.request.PromoExecuteQueryRequest;
import com.dianping.pay.promo.execute.service.dto.response.PromoQueryResponse;
import com.google.common.collect.Lists;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Created by lixiang on 2016/12/12.
 */
public class DiscountPromoQuery {

    @Resource
    private PayPromoExecuteQueryService payPromoExecuteQueryService;
    public DiscountPromoTool queryPromo(GetPromoDeskRequest getPromoDeskRequest, List<PromoProduct> promoProductList){
        Cat.logEvent("INVALID_METHOD_3", "com.dianping.pay.api.biz.discount.DiscountPromoQuery.queryPromo(com.dianping.pay.api.beans.GetPromoDeskRequest,java.util.List)");
        Map<Integer, PromoDTOGroup> promoDTOGroupMap = new HashMap<Integer, PromoDTOGroup>();
        Version version = new Version("8.0.8");

        List<PromoExecuteQueryRequest> requestList = PayPromoQueryMapper.buildRequest(
                getPromoDeskRequest,
                promoProductList);
        for (PromoExecuteQueryRequest promoExecuteQueryRequest : requestList) {
            PromoQueryResponse<List<PromoExecuteDetailDTO>> response = payPromoExecuteQueryService.queryAvailablePromo(promoExecuteQueryRequest);
            int productCode = PayPromoQueryMapper.toProductCode(promoExecuteQueryRequest.getProductType());
            if (response.isSuccess() && response.getResult() != null) {
                for (PromoExecuteDetailDTO promoExecuteDetailDTO : response.getResult()) {
                    if (Version.compareTo(getPromoDeskRequest.getClientInfo().getVersion(), version) < 0
                            && promoExecuteDetailDTO.getPromoSource() == PromoSource.shopSource.getCode()) {
                        continue;
                    }
                    int promoGroupId = promoExecuteDetailDTO.getPromoGroupId();
                    PromoDTOGroup promoDTOGroup = promoDTOGroupMap.get(promoGroupId);
                    if (promoDTOGroup == null) {
                        promoDTOGroup = new PromoDTOGroup();
                        promoDTOGroup.setPromoGroupId(promoExecuteDetailDTO.getPromoGroupId());
                        promoDTOGroup.setProductCode(productCode);
                        promoDTOGroup.setMutexProductCodes(PayPromoQueryMapper.toMutexProductCodes(productCode));
                        promoDTOGroup.setMutexTools(PaymentRule.getMutexPaymentRuleCode(PaymentRule.REDUCTION, promoExecuteDetailDTO.getPaymentRuleId()));
                        promoDTOGroup.setMaxPromoValue(promoExecuteDetailDTO.getTotalPromoAmount());
                        promoDTOGroupMap.put(promoGroupId, promoDTOGroup);
                    }
                    promoDTOGroup.getPromoDTOList().add(promoExecuteDetailDTO);
                    if(promoExecuteDetailDTO.getTotalPromoAmount().compareTo(promoDTOGroup.getMaxPromoValue()) > 0){
                        promoDTOGroup.setMaxPromoValue(promoExecuteDetailDTO.getTotalPromoAmount());
                    }
                }
            }
        }
        List<PromoDTOGroup> promoDTOGroupList = Lists.newArrayList(promoDTOGroupMap.values());
        Collections.sort(promoDTOGroupList);
        return PayPromoQueryMapper.toDiscountPromoTool(promoDTOGroupList);
    }
}
