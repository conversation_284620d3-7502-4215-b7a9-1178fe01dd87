package com.dianping.pay.api.biz.discount;

import com.dianping.cat.Cat;
import com.dianping.combiz.spring.util.PropertiesLoaderSupportUtils;
import com.dianping.pay.api.beans.GetPromoDeskCouponRequest;
import com.dianping.pay.api.beans.GetPromoDeskRequest;
import com.dianping.pay.api.beans.PromoProduct;
import com.dianping.pay.api.entity.promodesk.PromoDeskCoupon;
import com.dianping.pay.common.enums.ProductCode;
import com.dianping.pay.coupon.service.enums.CouponGroupType;
import com.dianping.pay.discount.service.beans.Discount;
import com.dianping.pay.discount.service.beans.DiscountRule;
import com.dianping.pay.discount.service.beans.ValidateDiscountRequest;
import com.dianping.pay.discount.service.enums.DiscountRuleType;
import com.dianping.pay.discount.service.enums.DiscountValidationSource;
import com.dianping.unified.coupon.manage.api.dto.UnifiedCouponDTO;
import com.dianping.unified.coupon.manage.api.dto.UnifiedCouponQueryContext;
import com.google.common.base.Function;
import com.google.common.collect.Lists;
import org.apache.commons.lang.math.NumberUtils;

import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.List;

public class CouponMapper {

    public static final ArrayList<Integer> SHOP_COUPON_TYPES = Lists.newArrayList(CouponGroupType.shopCoupon.value, CouponGroupType.huiYuanKa.value);
    public static final ArrayList<Integer> PLATFORM_COUPON_TYPES = Lists.newArrayList(CouponGroupType.coupon.value);

    public static List<PromoDeskCoupon> toPromoDeskCouponList(final int productCode, final List<UnifiedCouponDTO> coupon) {
        Cat.logEvent("INVALID_METHOD_5", "com.dianping.pay.api.biz.discount.CouponMapper.toPromoDeskCouponList(int,java.util.List)");
        return Lists.newArrayList(Lists.transform(coupon, new Function<UnifiedCouponDTO, PromoDeskCoupon>() {
            @Override
            public PromoDeskCoupon apply(UnifiedCouponDTO coupon) {
                return toPromoDeskCoupon(productCode, coupon);
            }
        }));
    }

    public static PromoDeskCoupon toPromoDeskCoupon(int productCode, UnifiedCouponDTO couponDTO) {
        Cat.logEvent("INVALID_METHOD_4", "com.dianping.pay.api.biz.discount.CouponMapper.toPromoDeskCoupon(int,com.dianping.unified.coupon.manage.api.dto.UnifiedCouponDTO)");
        PromoDeskCoupon promoDeskCoupon = null;
        if (couponDTO != null) {
            promoDeskCoupon = new PromoDeskCoupon();
            promoDeskCoupon.setId(toPromoDeskCouponId(couponDTO));
            promoDeskCoupon.setNewId(toPromoDeskCouponNewId(productCode, couponDTO));
            promoDeskCoupon.setTitle(couponDTO.getCouponGroupDTO().getDisplayTitle());
            promoDeskCoupon.setExpireDate((new SimpleDateFormat("yyyy-MM-dd")).format(couponDTO.getEndTime()));
            promoDeskCoupon.setDesc(couponDTO.getCouponGroupDTO().getDisplayDesc());
            promoDeskCoupon.setPriceValue(couponDTO.getCouponGroupDTO().getDiscountAmount());
            promoDeskCoupon.setOrderPriceLimit(couponDTO.getCouponGroupDTO().getPriceLimit() != null ? couponDTO.getCouponGroupDTO().getPriceLimit().doubleValue() : 0.0);
            promoDeskCoupon.setProductCode(productCode);
            promoDeskCoupon.setCanUse(couponDTO.isAvailable());
            promoDeskCoupon.setCouponType(couponDTO.getCouponGroupDTO().getCouponGroupType());
            promoDeskCoupon.setCalculateNoDiscountAmount(couponDTO.getCouponGroupDTO().isCalculateNoDiscountAmount());
        }
        return promoDeskCoupon;
    }

    public static UnifiedCouponQueryContext toCouponQueryContext(GetPromoDeskRequest getPromoDeskRequest, PromoProduct promoProduct, boolean isShopCoupon) {
        Cat.logEvent("INVALID_METHOD_2", "com.dianping.pay.api.biz.discount.CouponMapper.toCouponQueryContext(com.dianping.pay.api.beans.GetPromoDeskRequest,com.dianping.pay.api.beans.PromoProduct,boolean)");
        BigDecimal orderAmount = promoProduct.getTotalAmount();
        if (promoProduct.getQuantity() == 0 && promoProduct.getProductCode() == ProductCode.TUANGOU.getCode()) {
            orderAmount = promoProduct.getPrice().multiply(new BigDecimal(CouponBiz.OPTIMALDISCOUNTSIZE));
        } else if (isShopCoupon && promoProduct.getProductCode() == ProductCode.MO2O2PAY.getCode()) {
            orderAmount = promoProduct.getOriginalTotalAmount();
        }
        return toCouponQueryContext(
                promoProduct.getProductCode(),
                getPromoDeskRequest.getUserId(),
                getPromoDeskRequest.getMobileNo(),
                getPromoDeskRequest.getClientInfo().getDpId(),
                getPromoDeskRequest.getCityId(),
                getPromoDeskRequest.getClientInfo().getPlatform(),
                getPromoDeskRequest.getClientInfo().getVersion().getVersion(),
                orderAmount,
                promoProduct.getNoDiscountAmount(),
                promoProduct.getProductId(),
                getPromoDeskRequest.getShopIdL(),
                isShopCoupon ? SHOP_COUPON_TYPES : PLATFORM_COUPON_TYPES
        );
    }

    public static UnifiedCouponQueryContext toCouponQueryContext(int productCode,
                                                          long userId,
                                                          String mobileNo,
                                                          String dpId,
                                                          int cityId,
                                                          int platform,
                                                          String clientVersion,
                                                          BigDecimal amount,
                                                          BigDecimal noDiscountAmount,
                                                          int productId,
                                                          long shopId,
                                                          List<Integer> typeList) {
        Cat.logEvent("INVALID_METHOD_4", "com.dianping.pay.api.biz.discount.CouponMapper.toCouponQueryContext(int,long,String,String,int,int,String,BigDecimal,BigDecimal,int,long,List)");
        UnifiedCouponQueryContext context = new UnifiedCouponQueryContext();
        context.setProductCode(productCode);
        context.setUserId(userId);
        context.setMobileNo(mobileNo);
        context.setDpid(dpId);
        context.setCityId(cityId);
        context.setPlatform(platform);
        context.setClientVersion(clientVersion);
        context.setAmount(amount);
        context.setNoDiscountAmount(noDiscountAmount);
        context.setProductId(productId);
        context.setShopId(shopId);
        context.setTypeList(typeList);
        return context;
    }

    private static int toPromoDeskCouponId(UnifiedCouponDTO unifiedCouponDTO) {
        Cat.logEvent("INVALID_METHOD_4", "com.dianping.pay.api.biz.discount.CouponMapper.toPromoDeskCouponId(com.dianping.unified.coupon.manage.api.dto.UnifiedCouponDTO)");
        if (unifiedCouponDTO.getOldCouponId() > 0) {
            return unifiedCouponDTO.getOldCouponId();
        } else {
            return NumberUtils.toInt(unifiedCouponDTO.getUnifiedCouponId().length() == 24 ? unifiedCouponDTO.getUnifiedCouponId().substring(15) : unifiedCouponDTO.getUnifiedCouponId());
        }
    }

    private static String toPromoDeskCouponNewId(int productCode, UnifiedCouponDTO unifiedCouponDTO) {
        Cat.logEvent("INVALID_METHOD_2", "com.dianping.pay.api.biz.discount.CouponMapper.toPromoDeskCouponNewId(int,com.dianping.unified.coupon.manage.api.dto.UnifiedCouponDTO)");
        if (isQueryUnifiedCouponService(productCode)) {
            return unifiedCouponDTO.getUnifiedCouponId();
        } else if (unifiedCouponDTO.getOldCouponId() > 0) {
            return String.valueOf(unifiedCouponDTO.getOldCouponId());
        } else {
            return "";
        }
    }

    private static boolean isQueryUnifiedCouponService(int productCode) {
        Cat.logEvent("INVALID_METHOD_3", "com.dianping.pay.api.biz.discount.CouponMapper.isQueryUnifiedCouponService(int)");
        return Lists.newArrayList(
                PropertiesLoaderSupportUtils.getProperty("pay-api-mobile.unifiedcouponservice.productcode", "").split(",")
        ).contains(String.valueOf(productCode));
    }

    public static ValidateDiscountRequest toDiscountValidateContext(int discountId, GetPromoDeskCouponRequest request, PromoProduct product) {
        Cat.logEvent("INVALID_METHOD_1", "com.dianping.pay.api.biz.discount.CouponMapper.toDiscountValidateContext(int,com.dianping.pay.api.beans.GetPromoDeskCouponRequest,com.dianping.pay.api.beans.PromoProduct)");
        BigDecimal orderAmount = product.getTotalAmount();
        if (product.getQuantity() == 0 && product.getProductCode() == ProductCode.TUANGOU.getCode()) {
            orderAmount = product.getPrice().multiply(new BigDecimal(CouponBiz.OPTIMALDISCOUNTSIZE));
        }
        ValidateDiscountRequest validateDiscountRequest = new ValidateDiscountRequest();
        validateDiscountRequest.setDiscountID(discountId);
        validateDiscountRequest.setProductCode(product.getProductCode());
        validateDiscountRequest.setUserID(request.getUserId()!=null ? request.getUserId() : 0L);
        validateDiscountRequest.setTotalAmount(orderAmount);
        validateDiscountRequest.setCityID(request.getCityId());
        validateDiscountRequest.setPayPlatform(request.getClientInfo().getPlatform());
        if (product.getProductCode() == ProductCode.TUANGOU.getCode()) {
            validateDiscountRequest.setProductGroupID(product.getProductId());
        } else {
            validateDiscountRequest.setProductID(product.getProductId());
        }
        validateDiscountRequest.setMobileNO(request.getMobileNo());
        validateDiscountRequest.setGuid(request.getClientInfo().getDpId());
        validateDiscountRequest.setAppVersion(request.getClientInfo().getVersion().getVersion());
        validateDiscountRequest.setShopIDL(request.getShopIdL());
        validateDiscountRequest.setSource(DiscountValidationSource.createOrder.code);
        return validateDiscountRequest;
    }

    public static PromoDeskCoupon toPromoDeskCoupon(int productCode, Discount discount) {
        Cat.logEvent("INVALID_METHOD_4", "com.dianping.pay.api.biz.discount.CouponMapper.toPromoDeskCoupon(int,com.dianping.pay.discount.service.beans.Discount)");
        PromoDeskCoupon promoDeskCoupon = null;
        if (discount != null) {
            promoDeskCoupon = new PromoDeskCoupon();
            promoDeskCoupon.setId(discount.getDiscountID());
            promoDeskCoupon.setNewId(String.valueOf(discount.getDiscountID()));
            promoDeskCoupon.setTitle(discount.getDiscountGroup().getDiscountGroupTitle());
            promoDeskCoupon.setExpireDate((new SimpleDateFormat("yyyy-MM-dd")).format(discount.getEndDate()));
            promoDeskCoupon.setDesc(discount.getDiscountGroup().getDiscountGroupTitle());
            promoDeskCoupon.setPriceValue(discount.getDiscountAmount());
            for (DiscountRule discountRule : discount.getDiscountGroup().getDiscountRule()) {
                if (discountRule.getTypeValue() == DiscountRuleType.price.code) {
                    promoDeskCoupon.setOrderPriceLimit(Double.valueOf(discountRule.getRuleDetailList().get(0).getValue()));
                }
            }
            promoDeskCoupon.setProductCode(productCode);
            promoDeskCoupon.setCanUse(true);
            promoDeskCoupon.setCouponType(CouponGroupType.code.value);
            promoDeskCoupon.setCalculateNoDiscountAmount(true);
        }
        return promoDeskCoupon;
    }
}
