package com.dianping.pay.api.biz.activity.newloader.executors;

import com.dianping.cat.Cat;
import com.dianping.pay.api.beans.IssueCouponActivity;
import com.dianping.pay.api.biz.activity.newloader.AbstractCouponIssueActivityQueryExecutor;
import com.dianping.pay.api.entity.issuecoupon.IssueCouponRequest;
import com.dianping.poi.relation.service.api.PoiRelationService;
import com.dianping.spc.common.Response;
import com.dianping.tgc.open.DealGroupActivityQueryRemoteService;
import com.dianping.tgc.open.entity.ActivityDTO;
import com.dianping.tgc.open.entity.BatchActivityQueryRequest;
import com.dianping.tgc.open.entity.BatchQueryResponseDTO;
import com.dianping.tgc.open.enums.QuerySourceEnum;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;

import java.util.List;
import java.util.Map;

public class TuangouShopActivityQueryExecutor extends AbstractCouponIssueActivityQueryExecutor {

    private DealGroupActivityQueryRemoteService dealGroupActivityQueryRemoteService;
    private PoiRelationService poiRelationCacheService;

    public TuangouShopActivityQueryExecutor(IssueCouponRequest request) {
        super(request);
    }

    @Override
    protected List<IssueCouponActivity> loadFromRemote() {
        if (CollectionUtils.isEmpty(request.getShopIdLList())) {
            return Lists.newArrayList();
        }
        List<Long> dpShopIdList = Lists.newArrayList();
        if (request.isDpClient()) {
            dpShopIdList = request.getShopIdLList();
        } else {
            //TODO
            Map<Long, List<Long>> matchedDpIdMap = Maps.newHashMap();
            try {
                matchedDpIdMap = poiRelationCacheService.queryDpByMtIdsL(request.getShopIdLList());
            } catch (Exception e) {
                Cat.logError("queryDpByMtIds", e);
            }
            if (MapUtils.isEmpty(matchedDpIdMap)) {
                return Lists.newArrayList();
            }
            for (List<Long> longs : matchedDpIdMap.values()) {
                if (CollectionUtils.isEmpty(longs)) {
                    continue;
                }
                dpShopIdList.addAll(longs);
            }
        }
        BatchActivityQueryRequest remoteRequest = new BatchActivityQueryRequest();
        remoteRequest.setShopLongIds(dpShopIdList);
        remoteRequest.setSource(request.isDpClient() ? QuerySourceEnum.DP.getValue() : QuerySourceEnum.MT.getValue());
        Response<BatchQueryResponseDTO> dealgroupActivityResponse = dealGroupActivityQueryRemoteService.batchQueryDealGroupActivity(remoteRequest);
        if (!dealgroupActivityResponse.isSuccess() || dealgroupActivityResponse.getResult() == null) {
            return Lists.newArrayList();
        }
        Map<Long, List<ActivityDTO>> activityMap = dealgroupActivityResponse.getResult().getShopCouponMap();
        if (MapUtils.isEmpty(activityMap)) {
            return Lists.newArrayList();
        }
        return toIssueCouponActivitiesL(activityMap);
    }

    public TuangouShopActivityQueryExecutor setDealGroupActivityQueryRemoteService(DealGroupActivityQueryRemoteService dealGroupActivityQueryRemoteService) {
        this.dealGroupActivityQueryRemoteService = dealGroupActivityQueryRemoteService;
        return this;
    }

    public TuangouShopActivityQueryExecutor setPoiRelationCacheService(PoiRelationService poiRelationCacheService) {
        this.poiRelationCacheService = poiRelationCacheService;
        return this;
    }

}
