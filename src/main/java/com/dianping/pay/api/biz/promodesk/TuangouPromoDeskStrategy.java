package com.dianping.pay.api.biz.promodesk;

import com.dianping.api.domain.Version;
import com.dianping.cat.Cat;
import com.dianping.combiz.spring.util.PropertiesLoaderSupportUtils;
import com.dianping.pay.api.beans.DiscountRules;
import com.dianping.pay.api.beans.GetPromoDeskRequest;
import com.dianping.pay.api.entity.promodesk.PointExchangeRule;
import com.dianping.pay.api.entity.promodesk.PointPromoTool;

import java.util.Arrays;

/**
 * 团购优惠台加载策略
 * 返回：多个立减、抵用券、商户抵用券、红包、积分、小黄条
 * 不返回：
 */
public class TuangouPromoDeskStrategy extends CommonPromoDeskStrategy implements PromoDeskStrategy {

    protected boolean isPointEnable(GetPromoDeskRequest getPromoDeskRequest) {
        Cat.logEvent("INVALID_METHOD_1", "com.dianping.pay.api.biz.promodesk.TuangouPromoDeskStrategy.isPointEnable(com.dianping.pay.api.beans.GetPromoDeskRequest)");
        return Version.compareTo(getPromoDeskRequest.getClientInfo().getVersion(), new Version("8.0.2")) >= 0
                || PropertiesLoaderSupportUtils.getBoolProperty("pay-api-mobile.promodesk.enablePoint", true);
    }

    @Override
    public PointPromoTool assemblePointPromoTool(GetPromoDeskRequest getPromoDeskRequest, DiscountRules discountRules) {
        Cat.logEvent("INVALID_METHOD_1", "com.dianping.pay.api.biz.promodesk.TuangouPromoDeskStrategy.assemblePointPromoTool(com.dianping.pay.api.beans.GetPromoDeskRequest,com.dianping.pay.api.beans.DiscountRules)");
        if (getPromoDeskRequest.getUserId() > 0
                && discountRules.isCanUsePoint()
                && isPointEnable(getPromoDeskRequest)) {
            PointPromoTool pointPromoTool = new PointPromoTool();
            pointPromoTool.setPointBalance(0);
            PointExchangeRule pointExchangeRule = new PointExchangeRule();
            pointExchangeRule.setMemo("使用积分抵现请升级最新版本");
            pointPromoTool.setPointExchangeRules(Arrays.asList(pointExchangeRule));
            return pointPromoTool;
        }
        return null;
    }

    @Override
    public String assemblePromptMsg(GetPromoDeskRequest getPromoDeskRequest, DiscountRules discountRules) {
        Cat.logEvent("INVALID_METHOD_2", "com.dianping.pay.api.biz.promodesk.TuangouPromoDeskStrategy.assemblePromptMsg(com.dianping.pay.api.beans.GetPromoDeskRequest,com.dianping.pay.api.beans.DiscountRules)");
        if (Version.compareTo(getPromoDeskRequest.getClientInfo().getVersion(), new Version("7.9.6")) >= 0) {
            return null;
        }
        if (!discountRules.isCanUseCoupon() || !discountRules.isCanUseHongBao()) {
            StringBuilder sb = new StringBuilder();
            if (!discountRules.isCanUseCoupon()) {
                sb.append("抵用券/优惠代码");
            }
            if (!discountRules.isCanUseHongBao()) {
                if (sb.length() == 0) {
                    sb.append("现金券");
                } else {
                    sb.append("/现金券");
                }
            }
            sb.insert(0, "本商品不支持");
            return sb.toString();
        }
        return null;
    }

}
