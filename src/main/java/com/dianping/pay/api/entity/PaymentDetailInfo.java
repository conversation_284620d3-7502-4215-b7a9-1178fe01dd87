package com.dianping.pay.api.entity;

import com.dianping.api.common.entity.Pair;
import com.dianping.api.common.entity.PaymentTool;
import com.dianping.api.common.util.KeyValuePair;
import com.dianping.api.domain.ClientInfo;
import com.dianping.api.domain.Enum.AppType;
import com.dianping.api.domain.Enum.ClientType;
import com.dianping.api.domain.Version;
import com.dianping.api.netio.DPEncoder;
import com.dianping.api.util.ApiUtil;
import com.google.common.collect.Lists;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

public class PaymentDetailInfo extends DPEncoder implements Serializable {

	
	private static final long serialVersionUID = -5563515434755983775L;
	
	public List<Discount> getDiscountList() {
		return discountList;
	}
	public void setDiscountList(List<Discount> discountList) {
		this.discountList = discountList;
	}
	public BigDecimal getBalance() {
		return balance;
	}
	public void setBalance(BigDecimal balance) {
		this.balance = balance;
		this.balanceStr = balance.toPlainString();
	}
	public String getBalanceStr() {
		return balanceStr;
	}
	public void setBalanceStr(String balanceStr) {
		this.balanceStr = balanceStr;
	}

    public List<PaymentTool> getPaymentType() {
        return paymentType;
    }

    public void setPaymentType(List<PaymentTool> paymentType) {
        this.paymentType = paymentType;
    }

    public long getUserId() {
        return userId;
    }

    public void setUserId(long userId) {
        this.userId = userId;
    }

    public String getPhoneNO() {
        return phoneNO;
    }

    public void setPhoneNO(String phoneNO) {
        this.phoneNO = phoneNO;
    }

    public String getReductionAmountStr() {
        return reductionAmountStr;
    }

    public void setReductionAmountStr(String reductionAmountStr) {
        this.reductionAmountStr = reductionAmountStr;
    }

    public String getReductionLimitStr() {
        return reductionLimitStr;
    }

    public void setReductionLimitStr(String reductionLimitStr) {
        this.reductionLimitStr = reductionLimitStr;
    }

    private static final List<KeyValuePair> LIST = Lists.newArrayList();
	static {
		LIST.add(new KeyValuePair("discountList", 0x4f16));
		LIST.add(new KeyValuePair("balanceStr", 0x49bd));
		LIST.add(new KeyValuePair("paymentType", 0x6dd9));
		LIST.add(new KeyValuePair("userId", 0x8df6));
		LIST.add(new KeyValuePair("phoneNO", 0x435));
		LIST.add(new KeyValuePair("reductionAmountStr", 0x811f));
		LIST.add(new KeyValuePair("reductionLimitStr", 0x916c));
 	}
	
	private static final List<KeyValuePair> LEGACY_LIST = Lists.newArrayList();
	static {
		LEGACY_LIST.add(new KeyValuePair("discountList", 0x4f16));
		LEGACY_LIST.add(new KeyValuePair("balanceStr", 0x49bd));
		LEGACY_LIST.add(new KeyValuePair("paymentType", 0x6dd9));
 	}
	
	@Override
	public List<KeyValuePair> getSerializeList(ClientInfo clientInfo, Version version) {
		if(clientInfo.getApp() == AppType.TG
				|| (clientInfo.getClient() == ClientType.iPadHd && Version.compareTo(version, new Version("2.3")) >= 0)
				|| (clientInfo.getApp() == AppType.Main && Version.compareTo(version, new Version("5.3")) >= 0)) {
			return LIST;
		} else {
			return LEGACY_LIST;
		}
	}
				
	private List<Discount> discountList;  //订单可以使用的抵用券	
	private BigDecimal balance;               //帐户余额	
	private String balanceStr;
	private List<PaymentTool> paymentType;       //支付方式
    private long userId;
	private String phoneNO;
    private String reductionAmountStr;
    private String reductionLimitStr;

	/* (non-Javadoc)
	 * @see com.dianping.api.netio.DPEncoder#getClassId()
	 */
	@Override
	public int getClassId() {
 		return 0x350b;
	}

    public static void main(String[] args) {
        System.out.println(Integer.toHexString(ApiUtil.getHash("RedirectUrl")));
    }
}
