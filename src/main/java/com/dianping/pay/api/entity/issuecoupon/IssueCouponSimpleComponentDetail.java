package com.dianping.pay.api.entity.issuecoupon;

import com.dianping.mobile.framework.annotation.MobileDo;
import com.dianping.mobile.framework.annotation.MobileDo.MobileField;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * 模型描述：领券组件 - 券模块缩略信息组件
 * 移动之家链接: https://mobile.sankuai.com/studio/model/info/24451
 */
@MobileDo(id = 0xfd3)
@Data
public class IssueCouponSimpleComponentDetail implements Serializable {
    /**
     * 外层标题 （eg：优惠券）
     */
    @MobileDo.MobileField(key = 0x24cc)
    private String title;

    /**
     * 副标题展示 （eg：10张待领取/查看）
     */
    @MobileDo.MobileField(key = 0xd894)
    private String subTitle;
    /**
     * 是否第一次进商品详情页
     */
    @MobileField(key = 0xbfd9)
    private boolean displayBannerFlag;

    /**
     * 新客横幅标题
     */
    @MobileField(key = 0x2ed3)
    private String issueBannerTitle;

    /**
     * 券缩略信息列表
     */
    @MobileDo.MobileField(key = 0x5d37)
    private List<IssueCouponSimpleDetail> couponSimpleList;

    /**
     * 前端展示动效
     */
    @MobileField(key = 0x67ca)
    private Integer showAnimeType;

}
