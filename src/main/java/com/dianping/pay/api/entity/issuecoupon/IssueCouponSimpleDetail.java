package com.dianping.pay.api.entity.issuecoupon;

import com.dianping.mobile.framework.annotation.MobileDo;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * 模型描述：优惠券缩略信息
 * 移动之家链接: https://mobile.sankuai.com/studio/model/info/24450
 */
@MobileDo(id = 0xf8d3)
@Data
public class IssueCouponSimpleDetail implements Serializable {
    /**
     * 券类型 0=通用-新客券、1=通用-非新客券（有门槛）、2=品类券、7=商家平台权益券、8=预付的神券、9=top广告投放券（如变美神券）、[10=预留为top广告投放券（休娱神券)]、11=神会员神券
     */
    @MobileDo.MobileField(key = 0x8f0c)
    private int type;

    /**
     * 券折扣，折扣券专用, eg：8折返回8，85折返回85
     */
    @MobileDo.MobileField(key = 0x6509)
    private double discount;

    /**
     * 券面额类型，0表示满减券，1表示折扣券。用该字段进行尾标渲染
     */
    @MobileDo.MobileField(key = 0x59e0)
    private int couponValueType;

    /**
     * 领取状态 0未领取，1已领取
     */
    @MobileDo.MobileField(key = 0xc851)
    private int issueStatus;

    /**
     * 标题
     */
    @MobileDo.MobileField(key = 0x24cc)
    private String title;

    /**
     * 券面额
     */
    @MobileDo.MobileField(key = 0xfbe2)
    private double amount;

    /**
     * 券有效期结束时间，已领取状态赋值
     */
    @MobileDo.MobileField(key = 0xca7b)
    private long endTime;

    /**
     * 券有效期开始时间，已领取状态赋值
     */
    @MobileDo.MobileField(key = 0x31b)
    private long beginTime;

    /**
     * 券门槛
     */
    @MobileDo.MobileField(key = 0xa84b)
    private String priceLimit;

    /**
     * 领券模块-标题图片
     */
    @MobileDo.MobileField(key = 0x7e48)
    private String subTitleImg;

    /**
     * 标签类型，1=膨胀商家付费券包未购，2=膨胀商家付费神券未膨，3=付费神券已膨，4=免费神券不可膨，5=普通商家付费券包未购，6=普通商家付费神券未膨，7=普通商家免费神券不可膨，8=非神券商家
     */
    @MobileDo.MobileField(key = 0x385b)
    private int magicalMemberTagShowType;

    /**
     * 购买/膨胀/空（展示的附属文案）
     */
    @MobileDo.MobileField(key = 0x363f)
    private String magicalMemberCouponStatus;

    /**
     * 膨胀至X金额，对应 X（单位：元）
     */
    @MobileDo.MobileField(key = 0x93b3)
    private String reduceMoney;

    /**
     * 膨胀文案，对应"膨胀至""已膨胀""立减"
     */
    @MobileDo.MobileField(key = 0x36f9)
    private String inflateShowText;

    /**
     * 神会员标签，对应"神券"
     */
    @MobileDo.MobileField(key = 0x3c0)
    private String magicalMemberCouponTag;

    /**
     * 0=非神会员券，1=神会员券，目前彩虹投放券会赋值
     */
    @MobileDo.MobileField(key = 0x457d)
    private int mmcCoupon;

    /**
     * 彩虹投放券未领取情况下的最高优惠金额总和（单位：元），其他情况赋值为空
     */
    @MobileDo.MobileField(key = 0x21b1)
    private String totalAmount;
}
