package com.dianping.pay.api.entity.promodesk;

import com.dianping.api.common.util.KeyValuePair;
import com.dianping.api.domain.ClientInfo;
import com.dianping.api.domain.Version;
import com.dianping.api.netio.DPEncoder;
import com.dianping.pay.api.entity.OptimalDiscount;

import java.io.Serializable;
import java.util.LinkedList;
import java.util.List;

public class PromoDesk extends DPEncoder implements Serializable {

    private HongBaoPromoTool hongBao;
    private DiscountPromoTool discount;
    private CouponPromoTool coupon;
    private CouponPromoTool shopCoupon;
    private PointPromoTool point;
    private GiftCardPromoTool giftCard;
    private List<OptimalPromoTool> optimalPromoTools;
    private String promptMsg;

    private static final LinkedList<KeyValuePair> l = new LinkedList<KeyValuePair>();
    static {
        l.add(new KeyValuePair("hongBao", 0x22b8));
        l.add(new KeyValuePair("giftCard", 0x92f2));
        l.add(new KeyValuePair("discount", 0xcd43));
        l.add(new KeyValuePair("coupon", 0xbce1));
        l.add(new KeyValuePair("shopCoupon", 0xfd7c));
        l.add(new KeyValuePair("point", 0x61eb));
        l.add(new KeyValuePair("optimalPromoTools", 0x6987));
        l.add(new KeyValuePair("promptMsg", 0x8c81));
    }

    @Override
    public int getClassId() {
        return 0x7b91;
    }

    @Override
    public List<KeyValuePair> getSerializeList(ClientInfo clientInfo, Version version) {
        return l;
    }

    public HongBaoPromoTool getHongBao() {
        return hongBao;
    }

    public void setHongBao(HongBaoPromoTool hongBao) {
        this.hongBao = hongBao;
    }

    public DiscountPromoTool getDiscount() {
        return discount;
    }

    public void setDiscount(DiscountPromoTool discount) {
        this.discount = discount;
    }

    public CouponPromoTool getCoupon() {
        return coupon;
    }

    public void setCoupon(CouponPromoTool coupon) {
        this.coupon = coupon;
    }

    public CouponPromoTool getShopCoupon() {
        return shopCoupon;
    }

    public void setShopCoupon(CouponPromoTool shopCoupon) {
        this.shopCoupon = shopCoupon;
    }

    public PointPromoTool getPoint() {
        return point;
    }

    public void setPoint(PointPromoTool point) {
        this.point = point;
    }

    public GiftCardPromoTool getGiftCard() {
        return giftCard;
    }

    public void setGiftCard(GiftCardPromoTool giftCard) {
        this.giftCard = giftCard;
    }

    public List<OptimalPromoTool> getOptimalPromoTools() {
        return optimalPromoTools;
    }

    public void setOptimalPromoTools(List<OptimalPromoTool> optimalPromoTools) {
        this.optimalPromoTools = optimalPromoTools;
    }

    public String getPromptMsg() {
        return promptMsg;
    }

    public void setPromptMsg(String promptMsg) {
        this.promptMsg = promptMsg;
    }
}
