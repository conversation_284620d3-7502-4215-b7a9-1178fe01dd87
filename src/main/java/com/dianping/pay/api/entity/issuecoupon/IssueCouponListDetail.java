package com.dianping.pay.api.entity.issuecoupon;

import com.dianping.mobile.framework.annotation.MobileDo;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * 模型描述：领券组件券列表信息
 * 移动之家链接: https://mobile.sankuai.com/studio/model/info/24448
 */
@MobileDo(id = 0xe9d1)
@Data
public class IssueCouponListDetail implements Serializable {
    /**
     * 券商品类型分类，0通用券，1品类券，4商家账户权益券,5预付的神券，6=top广告投放（如变美神券）
     */
    @MobileDo.MobileField(key = 0xb925)
    private int couponProductType;

    /**
     * 券信息列表模块标题 （eg：商家品类券）
     */
    @MobileDo.MobileField(key = 0x24cc)
    private String title;

    /**
     * 券信息
     */
    @MobileDo.MobileField(key = 0x5db6)
    private List<IssueCouponDetail> couponList;

}
