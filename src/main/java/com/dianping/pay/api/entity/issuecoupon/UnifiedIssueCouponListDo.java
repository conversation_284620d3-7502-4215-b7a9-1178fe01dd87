package com.dianping.pay.api.entity.issuecoupon;

import com.dianping.mobile.framework.annotation.MobileDo;

import java.io.Serializable;
import java.util.List;

/**
 * Created by drintu on 18/6/6.
 */

@MobileDo(id = 0x201c)
public class UnifiedIssueCouponListDo implements Serializable {
    /**
     *  平台券、商家券......
     */
    @MobileDo.MobileField(key = 0x36e9)
    private String title;

    /**
     *
     */
    @MobileDo.MobileField(key = 0x4dd6)
    private List<UnifiedIssueCouponOptionDo> unifiedIssueCouponOption;

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public List<UnifiedIssueCouponOptionDo> getUnifiedIssueCouponOption() {
        return unifiedIssueCouponOption;
    }

    public void setUnifiedIssueCouponOption(
            List<UnifiedIssueCouponOptionDo> unifiedIssueCouponOption) {
        this.unifiedIssueCouponOption = unifiedIssueCouponOption;
    }

    @Override
    public String toString() {
        final StringBuilder sb = new StringBuilder("UnifiedIssueCouponListDo{");
        sb.append("title='").append(title).append('\'');
        sb.append(", unifiedIssueCouponOption=").append(unifiedIssueCouponOption);
        sb.append('}');
        return sb.toString();
    }
}