package com.dianping.pay.api.entity.issuecoupon;

import com.dianping.mobile.framework.annotation.MobileDo;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

@TypeDoc(
        description = "领券结果"
)
@MobileDo(id = 0x9527)
@Data
public class UnifiedIssueCouponRespDo implements Serializable {
    @FieldDoc(
            description = "领券是否成功 true:成功"
    )
    @MobileDo.MobileField(key = 0xe95f)
    private boolean isSuccess;

    @FieldDoc(
            description = "错误信息"
    )
    @MobileDo.MobileField(key = 0x7368)
    private IssueCouponErrorDo errorMsg;

    @FieldDoc(
            description = "券使用链接"
    )
    @MobileDo.MobileField(key = 0x81a5)
    private String toUseUrl;


    private Date beginTime;

    private Date endTime;

    /**
     * 使用结束时间时间戳
     */
    @MobileDo.MobileField(key = 0x7950)
    private long useEndTime;

    /**
     * 使用开始时间戳
     */
    @MobileDo.MobileField(key = 0x9a29)
    private long useBeginTime;

    /**
     * 发券成功文案
     */
    @MobileDo.MobileField(key = 0x38ec)
    private String message;

    /**
     * 弹窗提示文案
     */
    @MobileDo.MobileField(key = 0x3e36)
    private String toastMsg;

    private String couponGroupName;

    private boolean currentCanUse;

    private String unifiedCouponId;

    public UnifiedIssueCouponRespDo(boolean isSuccess,String toUseUrl) {
        this.isSuccess = isSuccess;
        this.toUseUrl = toUseUrl;
    }

    public UnifiedIssueCouponRespDo(IssueCouponErrorDo error) {
        this.isSuccess = false;
        this.errorMsg = error;
    }

    public UnifiedIssueCouponRespDo(IssueCouponErrorDo error, String toastMsg) {
        this.isSuccess = false;
        this.errorMsg = error;
        this.toastMsg = toastMsg;
    }

    public boolean getIsSuccess() {
        return isSuccess;
    }

    public void setIsSuccess(boolean isSuccess) {
        this.isSuccess = isSuccess;
    }

    public IssueCouponErrorDo getErrorMsg() {
        return errorMsg;
    }

    public void setErrorMsg(IssueCouponErrorDo errorMsg) {
        this.errorMsg = errorMsg;
    }

    public String getToUseUrl() {
        return toUseUrl;
    }

    public void setToUseUrl(String toUseUrl) {
        this.toUseUrl = toUseUrl;
    }

    public Date getBeginTime() {
        return beginTime;
    }

    public void setBeginTime(Date beginTime) {
        this.beginTime = beginTime;
    }

    public Date getEndTime() {
        return endTime;
    }

    public void setEndTime(Date endTime) {
        this.endTime = endTime;
    }

    public String getCouponGroupName() {
        return couponGroupName;
    }

    public void setCouponGroupName(String couponGroupName) {
        this.couponGroupName = couponGroupName;
    }

    public boolean isCurrentCanUse() {
        return currentCanUse;
    }

    public void setCurrentCanUse(boolean currentCanUse) {
        this.currentCanUse = currentCanUse;
    }

    public String getUnifiedCouponId() {
        return unifiedCouponId;
    }

    public void setUnifiedCouponId(String unifiedCouponId) {
        this.unifiedCouponId = unifiedCouponId;
    }

    public String getToastMsg() {
        return toastMsg;
    }

    public void setToastMsg(String toastMsg) {
        this.toastMsg = toastMsg;
    }
}