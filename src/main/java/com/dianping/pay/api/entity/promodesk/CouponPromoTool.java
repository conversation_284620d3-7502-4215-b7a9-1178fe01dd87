package com.dianping.pay.api.entity.promodesk;

import com.dianping.api.common.util.KeyValuePair;
import com.dianping.api.domain.ClientInfo;
import com.dianping.api.domain.Version;
import com.dianping.api.netio.DPEncoder;
import com.dianping.cat.Cat;
import com.dianping.pay.cashier.biz.enums.PaymentRule;

import java.io.Serializable;
import java.util.Arrays;
import java.util.LinkedList;
import java.util.List;

public class CouponPromoTool extends DPEncoder implements Serializable {

    private int id;
    private List<Integer> mutexTools;
    private boolean canUse;

    public CouponPromoTool(int id) {
        this.id = id;
        this.mutexTools = Arrays.asList(PaymentRule.HONGBAO.code);
    }

    private static final LinkedList<KeyValuePair> l = new LinkedList<KeyValuePair>();
    static {
        l.add(new KeyValuePair("id", 0x91b));
        l.add(new KeyValuePair("mutexTools", 0x2468));
        l.add(new KeyValuePair("canUse", 0x9069));
    }

    @Override
    public int getClassId() {
        Cat.logEvent("INVALID_METHOD_1", "com.dianping.pay.api.entity.promodesk.CouponPromoTool.getClassId()");
        return 0xf00b;
    }

    @Override
    public List<KeyValuePair> getSerializeList(ClientInfo clientInfo, Version version) {
        Cat.logEvent("INVALID_METHOD_3", "com.dianping.pay.api.entity.promodesk.CouponPromoTool.getSerializeList(com.dianping.api.domain.ClientInfo,com.dianping.api.domain.Version)");
        return l;
    }

    public int getId() {
        return id;
    }

    public void setId(int id) {
        this.id = id;
    }

    public List<Integer> getMutexTools() {
        return mutexTools;
    }

    public void setMutexTools(List<Integer> mutexTools) {
        this.mutexTools = mutexTools;
    }

    public boolean isCanUse() {
        return canUse;
    }

    public void setCanUse(boolean canUse) {
        this.canUse = canUse;
    }
}
