package com.dianping.pay.api.entity.issuecoupon;

import com.dianping.mobile.framework.annotation.MobileDo;
import java.io.Serializable;
import lombok.Data;

@Data
@MobileDo(id = 0x2ab6)
public class NewIssueDetailResult implements Serializable {

    /**
     * 请求单元唯一id
     */
    @MobileDo.MobileField(key = 0x5c59)
    private String unitId;

    /**
     * 是否成功
     */
    @MobileDo.MobileField(key = 0xbbb7)
    private boolean beSuccess;

    /**
     * 结果描述
     */
    @MobileDo.MobileField(key = 0xe7ec)
    private String resultMessage;

    /**
     * 券活动id
     */
    @MobileDo.MobileField(key = 0x9256)
    private String unifiedCouponGroupId;

    /**
     * 用户券id
     */
    @MobileDo.MobileField(key = 0xadf0)
    private String unifiedCouponId;

    /**
     * 券名称
     */
    @MobileDo.MobileField(key = 0x1445)
    private String couponGroupName;

    public NewIssueDetailResult() {
    }

    public NewIssueDetailResult(String unitId, boolean beSuccess, String resultMessage) {
        this.unitId = unitId;
        this.beSuccess = beSuccess;
        this.resultMessage = resultMessage;
    }
}
