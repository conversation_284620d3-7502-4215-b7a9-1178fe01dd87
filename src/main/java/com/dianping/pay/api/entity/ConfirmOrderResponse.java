package com.dianping.pay.api.entity;

import com.dianping.api.common.util.KeyValuePair;
import com.dianping.api.domain.AlertMsg;
import com.dianping.api.domain.ClientInfo;
import com.dianping.api.domain.Version;
import com.dianping.api.netio.DPEncoder;
import com.google.common.collect.Lists;

import java.io.Serializable;
import java.util.List;

/**
 * Created by xiaoxiao.li on 3/10/15.
 */
public class ConfirmOrderResponse extends DPEncoder implements Serializable {

    private static final long serialVersionUID = -7774924469081650899L;
    private int orderID;
    private AlertMsg alertMsg;
    private int flag;
    private int retryCount;
    private int intervalTime;
    private String advanceOrderID;
    private String redirectUrl;

    @Override
    public int getClassId() {
        return 0xe499;
    }

    private static final List<KeyValuePair> LIST = Lists.newArrayList();
    static {
        LIST.add(new KeyValuePair("orderID",0x978c));
        LIST.add(new KeyValuePair("alertMsg",0x5886));
        LIST.add(new KeyValuePair("flag",0x73ad));
        LIST.add(new KeyValuePair("retryCount",0xb214));
        LIST.add(new KeyValuePair("intervalTime",0xb96b));
        LIST.add(new KeyValuePair("advanceOrderID",0x9a6d));
        LIST.add(new KeyValuePair("redirectUrl",0x4709));
    }

    @Override
    public List<KeyValuePair> getSerializeList(ClientInfo clientInfo, Version version) {
        return LIST;
    }

    public int getOrderID() {
        return orderID;
    }

    public void setOrderID(int orderID) {
        this.orderID = orderID;
    }

    public AlertMsg getAlertMsg() {
        return alertMsg;
    }

    public void setAlertMsg(AlertMsg alertMsg) {
        this.alertMsg = alertMsg;
    }

    public int getFlag() {
        return flag;
    }

    public void setFlag(int flag) {
        this.flag = flag;
    }

    public int getRetryCount() {
        return retryCount;
    }

    public void setRetryCount(int retryCount) {
        this.retryCount = retryCount;
    }

    public int getIntervalTime() {
        return intervalTime;
    }

    public void setIntervalTime(int intervalTime) {
        this.intervalTime = intervalTime;
    }

    public String getAdvanceOrderID() {
        return advanceOrderID;
    }

    public void setAdvanceOrderID(String advanceOrderID) {
        this.advanceOrderID = advanceOrderID;
    }

    public String getRedirectUrl() {
        return redirectUrl;
    }

    public void setRedirectUrl(String redirectUrl) {
        this.redirectUrl = redirectUrl;
    }
}
