package com.dianping.pay.api.entity.issuecoupon;

import com.dianping.mobile.framework.annotation.MobileDo;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

@MobileDo(id = 0xfdd6)
@Data
public class CouponDetailInfo implements Serializable {

    /**
     * 面额属性角标 eg: "元"、"折"
     */
    @MobileDo.MobileField(key = 0x3aa)
    private String amountCornerMark;

    /**
     * 是否最优
     */
    @MobileDo.MobileField(key = 0xc804)
    private boolean optimal;

    /**
     * 券是否新客专享
     */
    @MobileDo.MobileField(key = 0xa349)
    private boolean freshExclusive;

    /**
     * 优惠券来源 0 平台券 1 商家券
     */
    @MobileDo.MobileField(key = 0xe01e)
    private int couponSrc;

    /**
     * 最优券详情页外露信息
     */
    @MobileDo.MobileField(key = 0xc721)
    private String displayText;

    /**
     * 券门槛描述
     */
    @MobileDo.MobileField(key = 0x8ded)
    private String couponThresholdDesc;

    /**
     * 券适用日期及叠加情况描述
     */
    @MobileDo.MobileField(key = 0x4479)
    private String couponSuitDesc;

    /**
     * 券适用品类
     */
    @MobileDo.MobileField(key = 0x60b4)
    private String couponCategory;

    /**
     * 券适用品类描述
     */
    @MobileDo.MobileField(key = 0x57a8)
    private String couponCategoryDesc;

    /**
     * 券名称
     */
    @MobileDo.MobileField(key = 0x24cc)
    private String title;

    /**
     * 发劵序列号，唯一。调用者需要保存这个值，如果两次调用使用相同的值，表示一次访问请求发劵，劵后台如果判断是已经发过的，直接返回已经发过劵。
     */
    @MobileDo.MobileField(key = 0xbaf)
    private String serialNo;

    /**
     * 是否可领
     */
    @MobileDo.MobileField(key = 0x33f4)
    private boolean canAssign;

    /**
     * 优惠券金额
     */
    @MobileDo.MobileField(key = 0xfbe2)
    private String amount;


    /**
     * 优惠券id
     */
    @MobileDo.MobileField(key = 0x2488)
    private String couponGroupId;

    /**
     * 券类型，0普通券，1新客投放券,3 神券（预付定义为领券购为神券）
     */
    @MobileDo.MobileField(key = 0xc004)
    private int coupontype;

    //单位：分
    private BigDecimal discountAmount;
    //单位：分
    private BigDecimal priceLimit;

    @MobileDo.MobileField(key = 0x6e40)
    private int merchantProductType;
    // 投放券专属角标标识
    @MobileDo.MobileField(key = 0xbf9b)
    private String tag;

    // 平台投放券相关的领券参数
    /**
     * 投放活动id
     */
    @MobileDo.MobileField(key = 0x1b0d)
    private long resourceActivityId;

    /**
     * 投放活动分流id
     */
    @MobileDo.MobileField(key = 0xc2dd)
    private long flowId;

    /**
     * 活动id，投放券专属字段
     */
    @MobileDo.MobileField(key = 0xe91)
    private long activityId;

    /**
     * rowkey，投放券专属字段
     */
    @MobileDo.MobileField(key = 0x2efe)
    private String rowKey;

    /**
     * 物料id，投放券专属字段
     */
    @MobileDo.MobileField(key = 0x2a2c)
    private String materialId;

    /**
     * 角标图片类型
     * @see com.dianping.pay.api.enums.CornerMarkTypeEnum
     */
    @MobileDo.MobileField(key = 0x29ef)
    private Integer cornerMarkType;

    /**
     * 角标图片url
     */
    @MobileDo.MobileField(key = 0xadd1)
    private String cornerMarkUrl;

    /**
     * 券简易描述(适用业务+时间限制描述)
     */
    @MobileDo.MobileField(key = 0xab5)
    private String bizAndTimeLimitDesc;

    /**
     * 商家券新客类型，1品牌新客3商品新客
     */
    @MobileDo.MobileField(key = 0x2fa)
    private Integer merchantUserType;

    /**
     * 综商家券使用时间限制描述，目前只有密室使用（eg：限周一、周三、周五使用）
     */
    @MobileDo.MobileField(key = 0xa212)
    private String dzTimeLimitRule;

    /**
     * 金融券密钥
     */
    @MobileDo.MobileField(key = 0x37ce)
    private String packageSecretKey;

}
