package com.dianping.pay.api.entity.issuecoupon;

import com.dianping.mobile.framework.annotation.MobileDo;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;

@MobileDo(id = 0x395c)
@Data
@Accessors(chain = true)
public class PromoSimpleInfoItem implements Serializable {
    /**
     * 描述文案，e.g. 新客下单减10元，每人可享1次
     */
    @MobileDo.MobileField(key = 0x451b)
    private String text;

    /**
     * 样式类型，0：普通文案型，1：标签型
     */
    @MobileDo.MobileField(key = 0x1b3a)
    private int style;

}