package com.dianping.pay.api.entity.issuecoupon;

import com.dianping.pay.promo.common.enums.User;
import com.dianping.tgc.open.entity.ActivitySimpleDTO;
import com.google.common.collect.Maps;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import lombok.Data;

@Data
public class NewIssueMultiCouponContext {
    //0:点评 1:美团
    private User userType;
    private long dpUserId;
    private long mtUserId;
    private Integer issueDetailSourceCode;
    private List<NewIssueCouponUnit> newIssueCouponUnitList;

    //opt券相关上下文
    private List<String> shopCouponGroupIds;
    private List<String> lqgCouponGroupIds;
    private Map<String, NewIssueCouponUnit> optUnitIdCouponMap;
    private Map<String, List<String>> optCouponGroupIdUnitIdListMap = Maps.newHashMap();
    private Map<String, ActivitySimpleDTO> shopCouponActivityMap = Maps.newHashMap();
    private Map<String, Integer> userCouponAvailableCountMap = Maps.newHashMap();
    private Map<String, NewIssueDetailResult> unitIssueResultMap = Maps.newHashMap();

    public boolean isMt() {
        return userType == User.MT;
    }

    public long getUserId() {
        return isMt() ? mtUserId : dpUserId;
    }
}
