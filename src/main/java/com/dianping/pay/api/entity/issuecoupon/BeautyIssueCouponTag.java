package com.dianping.pay.api.entity.issuecoupon;

import com.dianping.api.common.util.KeyValuePair;
import com.dianping.api.domain.ClientInfo;
import com.dianping.api.domain.Version;
import com.dianping.api.netio.DPEncoder;
import com.dianping.mobile.framework.annotation.MobileDo;

import java.io.Serializable;
import java.util.LinkedList;
import java.util.List;

@MobileDo(id = 0x8635)
public class BeautyIssueCouponTag extends DPEncoder implements Serializable {

    @MobileDo.MobileField(key = 0x3c48)
    private String icon;

    @MobileDo.MobileField(key = 0x8635)
    private String tag;

    private static final LinkedList<KeyValuePair> l = new LinkedList<KeyValuePair>();
    static {
        l.add(new KeyValuePair("icon", 0x3c48));
        l.add(new KeyValuePair("tag", 0xbf9b));
    }

    @Override
    public int getClassId() {
        return 0x8635;
    }

    @Override
    public List<KeyValuePair> getSerializeList(ClientInfo clientInfo, Version version) {
        return l;
    }

    public String getIcon() {
        return icon;
    }

    public void setIcon(String icon) {
        this.icon = icon;
    }

    public String getTag() {
        return tag;
    }

    public void setTag(String tag) {
        this.tag = tag;
    }

    @Override
    public String toString() {
        return "BeautyIssueCouponTag{" +
                "icon='" + icon + '\'' +
                ", tag='" + tag + '\'' +
                '}';
    }
}
