package com.dianping.pay.api.entity.issuecoupon;

import com.dianping.mobile.framework.annotation.MobileDo;
import lombok.Data;

import java.io.Serializable;

@MobileDo(id = 0x296a)
@Data
public class PromoDetailInfoItem implements Serializable {

    /**
     * 面额属性角标 eg: "元"、"折"
     */
    @MobileDo.MobileField(key = 0x3aa)
    private String amountCornerMark;

    /**
     * 抵用券加密id
     */
    @MobileDo.MobileField(key = 0x8625)
    private String encryptCouponGroupId;

    /**
     * 券副标题 （平台投放券）
     */
    @MobileDo.MobileField(key = 0x8664)
    private String couponSubTitle;

    /**
     * 是否最优券
     */
    @MobileDo.MobileField(key = 0xc804)
    private boolean optimal;

    /**
     * 券是否是新客专享的
     */
    @MobileDo.MobileField(key = 0xa349)
    private boolean freshExclusive;

    /**
     * 券适用品类描述
     */
    @MobileDo.MobileField(key = 0x57a8)
    private String couponCategoryDesc;

    /**
     * 活动投放券专用。 倒计时时间
     */
    @MobileDo.MobileField(key = 0x1e0d)
    private long countDownTime;

    /**
     * 按钮
     */
    @MobileDo.MobileField(key = 0x8ad0)
    private PromoCommonBtn button;

    /**
     * 抵用券金额描述，例如满***元 （jsonlabel）
     */
    @MobileDo.MobileField(key = 0x7c03)
    private String amountDesc;

    /**
     * 领券状态 0：可领取 1：已领取
     */
    @MobileDo.MobileField(key = 0x53f)
    private int status;

    /**
     * 已领取iconUrl
     */
    @MobileDo.MobileField(key = 0xd9b8)
    private String issuedIconUrl;

    /**
     * 抵用券金额 （老逻辑使用的jsonLabel）
     */
    @MobileDo.MobileField(key = 0xfbe2)
    private String amount;

    /**
     * 券使用时间文案，eg：领取后8天有效 （原逻辑使用的jsonlabel）
     */
    @MobileDo.MobileField(key = 0x12d8)
    private String timeDesc;

    /**
     * 名称，限【团购】指定商品 （老逻辑是jsonLabel格式）
     */
    @MobileDo.MobileField(key = 0x24cc)
    private String title;

    /**
     * 抵用券加密ID
     */
    @MobileDo.MobileField(key = 0x9256)
    private String unifiedCouponGroupId;

    /**
     * 抵用券id
     */
    @MobileDo.MobileField(key = 0x2488)
    private int couponGroupId;

    /**
     * 投放活动id，活动投放券专属
     */
    @MobileDo.MobileField(key = 0x1b0d)
    private long resourceActivityId;

    /**
     * 分流位id，投放券专属字段
     */
    @MobileDo.MobileField(key = 0xc2dd)
    private long flowId;


    /**
     * 物料id，投放券专属字段
     */
    @MobileDo.MobileField(key = 0x2a2c)
    private String materialId;

    /**
     * rowkey，投放券专属字段
     */
    @MobileDo.MobileField(key = 0x2efe)
    private String rowKey;

    /**
     * 活动id，投放券专属字段
     */
    @MobileDo.MobileField(key = 0xe91)
    private long activityId;

}
