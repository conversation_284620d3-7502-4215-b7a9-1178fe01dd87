package com.dianping.pay.api.entity;

import java.io.Serializable;
import java.util.List;

import com.dianping.api.common.entity.Pair;
import com.dianping.api.common.util.KeyValuePair;
import com.dianping.api.domain.ClientInfo;
import com.dianping.api.domain.Version;
import com.dianping.api.netio.DPEncoder;
import com.dianping.cat.Cat;
import com.google.common.collect.Lists;

public class RemindConfig extends DPEncoder implements Serializable {

	private static final long serialVersionUID = -3162535101688362244L;

	/**
	 * 提醒方式: 1=短信; 2=邮件; 3=短信&邮件
	 */
	private int remindType;
	/**
	 * 发送时间: 1=早上 2=午夜
	 */
	private int remindTime;
	/**
	 * 每个Pair表示一个城市: ID=城市ID; Name=城市名; Type=已设置提醒的商户数量
	 */
	private List<Pair> remindCities;

	public static List<KeyValuePair> LIST = Lists.newArrayList();
	static {
		LIST.add(new KeyValuePair("remindType", 0xd8cd));
		LIST.add(new KeyValuePair("remindTime", 0x14a0));
		LIST.add(new KeyValuePair("remindCities", 0x89b5));
	}

	@Override
	public List<KeyValuePair> getSerializeList(ClientInfo clientInfo,
			Version version) {
        Cat.logEvent("INVALID_METHOD_5", "com.dianping.pay.api.entity.RemindConfig.getSerializeList(com.dianping.api.domain.ClientInfo,com.dianping.api.domain.Version)");
        return LIST;
	}

	/*
	 * (non-Javadoc)
	 * 
	 * @see com.dianping.api.netio.DPEncoder#getClassId()
	 */
	@Override
	public int getClassId() {
        Cat.logEvent("INVALID_METHOD_5", "com.dianping.pay.api.entity.RemindConfig.getClassId()");
        return 0xc10a;
	}

	public List<Pair> getRemindCities() {
		return remindCities;
	}

	public void setRemindCities(List<Pair> remindCities) {
		this.remindCities = remindCities;
	}

	public int getRemindType() {
		return remindType;
	}

	public void setRemindType(int remindType) {
		this.remindType = remindType;
	}

	public int getRemindTime() {
		return remindTime;
	}

	public void setRemindTime(int remindTime) {
		this.remindTime = remindTime;
	}

}
