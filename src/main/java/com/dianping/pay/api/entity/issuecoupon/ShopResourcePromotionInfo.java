package com.dianping.pay.api.entity.issuecoupon;

import com.dianping.mobile.framework.annotation.MobileDo;
import com.dianping.mobile.framework.annotation.MobileDo.MobileField;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/8/14
 */
@MobileDo(id = 0xfd10)
@Data
public class ShopResourcePromotionInfo {

    @MobileDo.MobileField(key = 0xe91)
    private Long activityId;

    @MobileDo.MobileField(key = 0x545e)
    private Long resourceLocationId;

    @MobileDo.MobileField(key = 0xc2dd)
    private Long flowId;

    @MobileDo.MobileField(key = 0x2efe)
    private String rowKey;

    @MobileDo.MobileField(key = 0xc851)
    private Integer issueStatus;

    @MobileDo.MobileField(key = 0x42df)
    private String personTag;

    @MobileDo.MobileField(key = 0xf85c)
    private String maxAmount;

    //0-普通券 1-算法券
    @MobileDo.MobileField(key = 0x7cd6)
    private int couponType;

    //0-不弹窗 1-弹窗
    @MobileDo.MobileField(key = 0x6a92)
    private int popUp;

    @MobileDo.MobileField(key = 0x2a2c)
    private String materialId;

    /**
     * 弹窗背景图-已领取多张券
     */
    @MobileField(key = 0x877f)
    private String drawMultiCouponsImg;

    /**
     * 弹窗背景图-已领取单张券
     */
    @MobileField(key = 0xf648)
    private String drawSingleCouponImg;

    /**
     * 弹窗封面图-未领取状态
     */
    @MobileField(key = 0x59d1)
    private String waitDrawImg;

    @MobileDo.MobileField(key = 0xe316)
    private List<ExposureResourceCouponInfo> resourceCouponInfos;

}
