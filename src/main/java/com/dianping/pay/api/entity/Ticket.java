package com.dianping.pay.api.entity;

import java.io.Serializable;
import java.util.List;

import com.dianping.api.common.util.KeyValuePair;
import com.dianping.api.domain.ClientInfo;
import com.dianping.api.domain.Version;
import com.dianping.api.netio.DPEncoder;
import com.dianping.cat.Cat;
import com.google.common.collect.Lists;

public class Ticket extends DPEncoder implements Serializable{
	
	private static final long serialVersionUID = -1874207274599992562L;
	
	private String title;
	private int dealSelectedID;
	private int count;
	private int maxCount;
	private int minCount;
	
	public Ticket (String title, int dealSelectId, int count, int maxCount,int minCount)
	{
		this.title = title;
		this.dealSelectedID = dealSelectId;
		this.count = count;
		this.maxCount = maxCount;
		this.minCount = minCount;
		
	}
	
	public String getTitle() {
		return title;
	}
	public void setTitle(String title) {
		this.title = title;
	}
	public int getDealSelectedID() {
		return dealSelectedID;
	}
	public void setDealSelectedID(int dealSelectedID) {
		this.dealSelectedID = dealSelectedID;
	}
	public int getCount() {
		return count;
	}
	public void setCount(int count) {
		this.count = count;
	}
	public int getMaxCount() {
		return maxCount;
	}
	public void setMaxCount(int maxCount) {
		this.maxCount = maxCount;
	}
	public int getMinCount() {
		return minCount;
	}
	public void setMinCount(int minCount) {
		this.minCount = minCount;
	}
	@Override
	public int getClassId() {
        Cat.logEvent("INVALID_METHOD_1", "com.dianping.pay.api.entity.Ticket.getClassId()");
        // TODO Auto-generated method stub
		return 0xc261;
	}
	private static final List<KeyValuePair> LIST = Lists.newArrayList();
	static {
		LIST.add(new KeyValuePair("title", 0x36e9));
		LIST.add(new KeyValuePair("dealSelectedID", 0x473e));
		LIST.add(new KeyValuePair("count", 0x630b));
		LIST.add(new KeyValuePair("maxCount", 0xc48b));
		LIST.add(new KeyValuePair("minCount", 0xf13f));
	}
	@Override
	public List<KeyValuePair> getSerializeList(ClientInfo clientInfo,
			Version version) {
        Cat.logEvent("INVALID_METHOD_4", "com.dianping.pay.api.entity.Ticket.getSerializeList(com.dianping.api.domain.ClientInfo,com.dianping.api.domain.Version)");
        // TODO Auto-generated method stub
		return LIST;
	}
	

}
