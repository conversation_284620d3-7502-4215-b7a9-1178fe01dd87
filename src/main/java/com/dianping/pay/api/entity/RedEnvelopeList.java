package com.dianping.pay.api.entity;

import com.dianping.api.common.util.KeyValuePair;
import com.dianping.api.domain.ResultList;
import com.dianping.cat.Cat;

/**
 * User: huawei.li
 * Date: 14-6-6
 * Time: 上午10:48
 */
public class RedEnvelopeList extends ResultList<RedEnvelope> {

    private RedEnvelopeAdvertise advertise;

    public RedEnvelopeAdvertise getAdvertise() {
        return advertise;
    }

    public void setAdvertise(RedEnvelopeAdvertise advertise) {
        this.advertise = advertise;
    }

    @Override
    public int getClassId() {
        Cat.logEvent("INVALID_METHOD_4", "com.dianping.pay.api.entity.RedEnvelopeList.getClassId()");
        return 0x8203;
    }

    static {
        RESULTLIST.add(new KeyValuePair("advertise", 0x64c));
    }

}
