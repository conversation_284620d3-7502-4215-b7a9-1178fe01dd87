package com.dianping.pay.api.entity.issuecoupon;

import com.dianping.mobile.framework.annotation.MobileRequest;
import com.dianping.mobile.framework.datatypes.IMobileRequest;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import lombok.Data;
import org.apache.commons.lang.math.NumberUtils;

@TypeDoc(
        description = "用户领券参数"
)
@MobileRequest
@Data
public class UnifiedissuemulticouponRequest implements IMobileRequest {

    @FieldDoc(
            description = "团单id，已废弃"
    )
    @MobileRequest.Param(name = "productid")
    @Deprecated
    private Integer productid;
    
    @FieldDoc(
            description = "团单id，长整型"
    )
    @MobileRequest.Param(name = "longproductid")
    private Long longProductId;

    @FieldDoc(
            description = "门店id"
    )
    @MobileRequest.Param(name = "shopid")
    private String shopid;

    @FieldDoc(
            description = "门店id"
    )
    @MobileRequest.Param(name = "shopuuid")
    private String shopuuid;

    @FieldDoc(
            description = "诚信字段"
    )
    @MobileRequest.Param(name = "cx")
    private String cx;
    @FieldDoc(
            description = "产品类型，用来区分productid字段是团购id还是商品id"
    )
    @MobileRequest.Param(name = "producttype")
    private Integer producttype;

    @FieldDoc(
            description = "商家券：券批次ID；平台券：券码ID"
    )
    @MobileRequest.Param(name = "unifiedcoupongroupids")
    private String unifiedCouponGroupIds;

    @FieldDoc(
            description = "发券渠道 {@link com.dianping.gmkt.wave.api.enums.ActivityChannelEnum}"
    )
    @MobileRequest.Param(name = "issuechannel")
    private Integer issueChannel;

    @FieldDoc(
            description = "发券渠道 {@link com.dianping.gmkt.coupon.common.api.enums.IssueDetailSourceEnum}"
    )
    @MobileRequest.Param(name = "issuedetailsourcecode")
    private Integer issueDetailSourceCode;

    @FieldDoc(
            description = "渠道来源,@PageSourceEnum"
    )
    @MobileRequest.Param(name = "pagesource")
    private String pagesource;

    public String getShopidStr() {
        return shopid;
    }

    @Deprecated
    public Integer getShopid() {
        return NumberUtils.toInt(shopid);
    }

    public Long getShopidL() {
        return NumberUtils.toLong(shopid);
    }

    @FieldDoc(
            description = "是否需要推送投放资源券"
    )
    @MobileRequest.Param(name = "needpushresourcecoupon")
    private String needPushResourceCoupon;

    @MobileRequest.Param(name = "uuid")
    private String uuid;

    /**
     * 投放活动券发券专用-活动id
     */
    @MobileRequest.Param(name = "activityid")
    private Long activityid;

    /**
     * 投放活动券发券专用-物料id
     */
    @MobileRequest.Param(name = "materialid")
    private String materialid;

    /**
     * 投放活动券发券专用-投放活动id，如果发放的是投放券的话传递这个参数
     */
    @MobileRequest.Param(name = "resourceactivityid")
    private Long resourceactivityid;

    /**
     * 投放活动券发券专用-分流位id，投放的是活动投放券时传这个id
     */
    @MobileRequest.Param(name = "flowid")
    private Long flowid;

    /**
     * 投放活动券发券专用-rowkey
     */
    @MobileRequest.Param(name = "rowkey")
    private String rowkey;

    /**
     * @PageSourceEnum
     * 页面来源，pagesource是透传给活动的不能用
     */
    @MobileRequest.Param(name = "couponpagesource")
    private String couponPageSource;

    @FieldDoc(
            description = "shopId加密值"
    )
    @MobileRequest.Param(name = "shopidencrypt")
    private String shopidencrypt;

    @FieldDoc(
            description = "shopUuid加密文"
    )
    @MobileRequest.Param(name = "shopuuidencrypt")
    private String shopuuidencrypt;

}