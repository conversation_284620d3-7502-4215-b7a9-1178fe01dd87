package com.dianping.pay.api.entity.issuecoupon;

import com.dianping.api.common.util.KeyValuePair;
import com.dianping.api.domain.ClientInfo;
import com.dianping.api.domain.Version;
import com.dianping.api.netio.DPEncoder;

import java.io.Serializable;
import java.util.LinkedList;
import java.util.List;

public class IssueCouponOption extends DPEncoder implements Serializable {

    private int id;
    private double amount;
    private String tag;
    private String title;
    private String desc;
    private boolean enable;

    private static final LinkedList<KeyValuePair> l = new LinkedList<KeyValuePair>();
    static {
        l.add(new KeyValuePair("id", 0x91b));
        l.add(new KeyValuePair("amount", 0x4967));
        l.add(new KeyValuePair("tag", 0x477b));
        l.add(new KeyValuePair("title", 0x36e9));
        l.add(new KeyValuePair("desc", 0x7291));
        l.add(new KeyValuePair("enable", 0x6a59));
    }

    @Override
    public int getClassId() {
        return 0xb8ea;
    }

    @Override
    public List<KeyValuePair> getSerializeList(ClientInfo clientInfo, Version version) {
        return l;
    }

    public int getId() {
        return id;
    }

    public void setId(int id) {
        this.id = id;
    }

    public double getAmount() {
        return amount;
    }

    public void setAmount(double amount) {
        this.amount = amount;
    }

    public String getTag() {
        return tag;
    }

    public void setTag(String tag) {
        this.tag = tag;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }

    public boolean isEnable() {
        return enable;
    }

    public void setEnable(boolean enable) {
        this.enable = enable;
    }

    @Override
    public String toString() {
        return "IssueCouponOption{" +
                "id=" + id +
                ", amount=" + amount +
                ", tag='" + tag + '\'' +
                ", title='" + title + '\'' +
                ", desc='" + desc + '\'' +
                ", enable=" + enable +
                '}';
    }
}
