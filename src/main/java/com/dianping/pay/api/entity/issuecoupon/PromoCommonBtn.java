package com.dianping.pay.api.entity.issuecoupon;

import com.dianping.mobile.framework.annotation.MobileDo;
import lombok.Data;

import java.io.Serializable;

@Data
@MobileDo(id = 0x658a)
public class PromoCommonBtn implements Serializable {
    /**
     * 0仅展示；1点击跳转
     */
    @MobileDo.MobileField(key = 0x18f1)
    private int actionType;

    /**
     * 跳转地址
     */
    @MobileDo.MobileField(key = 0xcc0)
    private String clickUrl;

    /**
     * 按钮标题
     */
    @MobileDo.MobileField(key = 0x24cc)
    private String title;


    //0-只展示 1-点击跳转 2-点击分享
    public enum ActionEnum{
        SHOW(0),
        REDIRECT(1),
        SHARE(3);

        public int type;

        ActionEnum(int type) {
            this.type = type;
        }
    }
}
