package com.dianping.pay.api.entity;

import com.dianping.api.common.util.KeyValuePair;
import com.dianping.api.domain.ClientInfo;
import com.dianping.api.domain.Version;
import com.dianping.api.netio.DPEncoder;
import com.google.common.collect.Lists;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

public class DiscountEvent extends DPEncoder implements Serializable {

    public int getId() {
        return id;
    }

    public void setId(int id) {
        this.id = id;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }

    public double getDiscountAmount() {
        return discountAmount;
    }

    public void setDiscountAmount(double discountAmount) {
        this.discountAmount = discountAmount;
    }

    public BigDecimal getDiscountAmountObj() {
        return discountAmountObj;
    }

    public void setDiscountAmountObj(BigDecimal discountAmountObj) {
        this.discountAmountObj = discountAmountObj;
        this.discountAmount = discountAmountObj.doubleValue();
    }

    public double getLimitAmount() {
        return limitAmount;
    }

    public void setLimitAmount(double limitAmount) {
        this.limitAmount = limitAmount;
    }

    public BigDecimal getLimitAmountObj() {
        return limitAmountObj;
    }

    public void setLimitAmountObj(BigDecimal limitAmountObj) {
        this.limitAmountObj = limitAmountObj;
        this.limitAmount = limitAmountObj.doubleValue();
    }

    public int getLimitCount() {
        return limitCount;
    }

    public void setLimitCount(int limitCount) {
        this.limitCount = limitCount;
    }

    public int getType() {
        return type;
    }

    public void setType(int type) {
        this.type = type;
    }

    public boolean getIsShowTitle() {
        return isShowTitle;
    }

    public void setIsShowTitle(boolean isShowTitle) {
        this.isShowTitle = isShowTitle;
    }

    public boolean getIsEnable() {
        return isEnable;
    }

    public void setIsEnable(boolean isEnable) {
        this.isEnable = isEnable;
    }

    public String getTip() {
        return tip;
    }

    public void setTip(String tip) {
        this.tip = tip;
    }

    public List<Integer> getMutexDiscountTools() {
        return mutexDiscountTools;
    }

    public void setMutexDiscountTools(List<Integer> mutexDiscountTools) {
        this.mutexDiscountTools = mutexDiscountTools;
    }

    private static final List<KeyValuePair> LIST = Lists.newArrayList();
	static {
		LIST.add(new KeyValuePair("id", 0x91b));
		LIST.add(new KeyValuePair("title", 0x36e9));
		LIST.add(new KeyValuePair("desc", 0x7291));
		LIST.add(new KeyValuePair("discountAmount", 0x522e));
		LIST.add(new KeyValuePair("limitAmount", 0xb826));
		LIST.add(new KeyValuePair("limitCount", 0x330));
        LIST.add(new KeyValuePair("type", 0x372));
        LIST.add(new KeyValuePair("isShowTitle", 0xa0a5));
		LIST.add(new KeyValuePair("isEnable", 0xa5f1));
		LIST.add(new KeyValuePair("tip", 0x487a));
		LIST.add(new KeyValuePair("mutexDiscountTools", 0x8bc1));
	}

	@Override
	public List<KeyValuePair> getSerializeList(ClientInfo clientInfo, Version version) {
		return LIST;
	}

    /**
     * 立减ID（大于0有效）
     */
	private int id;
    /**
     * 短标题
     */
	private String title;
    /**
     * 文案描述
     */
	private String desc;
    /**
     * 立减金额
     */
    private double discountAmount;
    private BigDecimal discountAmountObj;
    /**
     * 金额下限
     */
    private double limitAmount;
    private BigDecimal limitAmountObj = BigDecimal.ZERO;
    /**
     * 数量上限
     */
    private int limitCount;
    /**
     * 1-按订单立减 2-按份数立减
     */
    private int type;
    /**
     * 是否展示短标题
     */
    private boolean isShowTitle;
    /**
     * 是否可用
     */
    private boolean isEnable;
    /**
     * 提示
     */
    private String tip;
    /**
     * 优惠工具互斥规则
     */
    private List<Integer> mutexDiscountTools;

	/*
	 * (non-Javadoc)
	 * 
	 * @see com.dianping.api.netio.DPEncoder#getClassId()
	 */
	@Override
	public int getClassId() {
		return 0x7bff;
	}

}
