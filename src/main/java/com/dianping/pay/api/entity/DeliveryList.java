package com.dianping.pay.api.entity;

import java.io.Serializable;
import java.util.List;

import com.dianping.api.common.util.KeyValuePair;
import com.dianping.api.domain.ResultList;
import com.dianping.api.domain.ClientInfo;
import com.dianping.api.domain.Version;
import com.dianping.cat.Cat;
import com.google.common.collect.Lists;

public class DeliveryList extends ResultList<Delivery> implements Serializable {

	private static final long serialVersionUID = 1939787928850113557L;

	private int maxCount;

	public int getMaxCount() {
		return maxCount;
	}

	public void setMaxCount(int maxCount) {
		this.maxCount = maxCount;
	}

	static final List<KeyValuePair> LIST = Lists.newArrayList();
	static {
		LIST.add(new KeyValuePair("maxCount", 0xc48b));
		LIST.addAll(ResultList.RESULTLIST);

	}

	@Override
	public List<KeyValuePair> getSerializeList(ClientInfo clientInfo,
			Version version) {
        Cat.logEvent("INVALID_METHOD_4", "com.dianping.pay.api.entity.DeliveryList.getSerializeList(com.dianping.api.domain.ClientInfo,com.dianping.api.domain.Version)");
        return LIST;
	}

	@Override
	public int getClassId() {
        Cat.logEvent("INVALID_METHOD_5", "com.dianping.pay.api.entity.DeliveryList.getClassId()");
        return 0x48bf;
	}
}
