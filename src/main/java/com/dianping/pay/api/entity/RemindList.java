package com.dianping.pay.api.entity;

import java.io.Serializable;
import java.util.List;

import com.dianping.api.common.util.KeyValuePair;
import com.dianping.api.domain.ResultList;
import com.dianping.api.domain.ClientInfo;
import com.dianping.api.domain.Version;
import com.dianping.cat.Cat;
import com.google.common.collect.Lists;

public class RemindList extends ResultList<Remind> implements Serializable {

	private static final long serialVersionUID = -7220257385178646172L;

	public static final List<KeyValuePair> LIST = Lists.newArrayList();
	static {
		LIST.addAll(ResultList.RESULTLIST);
	}

	@Override
	public List<KeyValuePair> getSerializeList(ClientInfo clientInfo,
			Version version) {
        Cat.logEvent("INVALID_METHOD_2", "com.dianping.pay.api.entity.RemindList.getSerializeList(com.dianping.api.domain.ClientInfo,com.dianping.api.domain.Version)");
        return LIST;
	}

	/*
	 * (non-Javadoc)
	 * 
	 * @see com.dianping.api.netio.DPEncoder#getClassId()
	 */
	@Override
	public int getClassId() {
        Cat.logEvent("INVALID_METHOD_5", "com.dianping.pay.api.entity.RemindList.getClassId()");
        return 0xba4d;
	}

}
