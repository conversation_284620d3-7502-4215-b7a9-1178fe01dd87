package com.dianping.pay.api.entity;

import com.dianping.api.common.util.KeyValuePair;
import com.dianping.api.domain.ClientInfo;
import com.dianping.api.domain.Version;
import com.dianping.api.netio.DPEncoder;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * User: huawei.li
 * Date: 14-6-2
 * Time: 下午3:37
 */
public class RedEnvelope extends DPEncoder implements Serializable {
    private String accountId;
    private Date addTime;
    private Date expiredTime;
    private double amount;
    private String memo;
    private boolean expired;
    private int remainDay;
    @Override
    public int getClassId() {
        return 0x35d3;
    }
    protected static final List<KeyValuePair> RedEnvelopeList =new ArrayList<KeyValuePair>();

    static{
        RedEnvelopeList.add(new KeyValuePair("accountId", 0xd683));
        RedEnvelopeList.add(new KeyValuePair("addTime", 0x802c));
        RedEnvelopeList.add(new KeyValuePair("expiredTime", 0xa697));
        RedEnvelopeList.add(new KeyValuePair("amount", 0x4967));
        RedEnvelopeList.add(new KeyValuePair("memo", 0x897e));
        RedEnvelopeList.add(new KeyValuePair("expired", 0x2a2a));
        RedEnvelopeList.add(new KeyValuePair("remainDay",0xea3a));
    }

    @Override
    public List<KeyValuePair> getSerializeList(ClientInfo clientInfo, Version version) {
        return RedEnvelopeList;
    }

    public String getAccountId() {
        return accountId;
    }

    public void setAccountId(String accountId) {
        this.accountId = accountId;
    }

    public Date getAddTime() {
        return addTime;
    }

    public void setAddTime(Date addTime) {
        this.addTime = addTime;
    }

    public Date getExpiredTime() {
        return expiredTime;
    }

    public void setExpiredTime(Date expiredTime) {
        this.expiredTime = expiredTime;
    }

    public double getAmount() {
        return amount;
    }

    public void setAmount(double amount) {
        this.amount = amount;
    }

    public String getMemo() {
        return memo;
    }

    public void setMemo(String memo) {
        this.memo = memo;
    }

    public boolean isExpired() {
        return expired;
    }

    public void setExpired(boolean expired) {
        this.expired = expired;
    }

    public int getRemainDay() {
        return remainDay;
    }

    public void setRemainDay(int remainDay) {
        this.remainDay = remainDay;
    }
}
