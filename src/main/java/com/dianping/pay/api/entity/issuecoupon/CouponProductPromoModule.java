package com.dianping.pay.api.entity.issuecoupon;

import com.dianping.mobile.framework.annotation.MobileDo;
import lombok.Data;

import java.io.Serializable;

@MobileDo(id = 0x6198)
@Data
public class CouponProductPromoModule implements Serializable {

    /**
     * 浮层内详细优惠信息（走代理查询）
     */
    @MobileDo.MobileField(key = 0xde74)
    private CouponProxyPromoInfo promoAggInfo;

    /**
     * 浮层内详细优惠信息
     */
    @MobileDo.MobileField(key = 0xc965)
    private CouponDetailPromoInfo detailPromoInfo;

    /**
     * 外层缩略优惠信息
     */
    @MobileDo.MobileField(key = 0x4711)
    private CouponConcisePromoInfo concisePromoInfo;


}
