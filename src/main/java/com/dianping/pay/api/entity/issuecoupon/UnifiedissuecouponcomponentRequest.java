package com.dianping.pay.api.entity.issuecoupon;

import com.dianping.mobile.framework.annotation.MobileRequest;
import com.dianping.mobile.framework.datatypes.IMobileRequest;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;

/**
 * Created by d<PERSON><PERSON> on 18/6/6.
 */

@TypeDoc(description = "unifiedissuecouponcomponent.bin接口请求参数")
@MobileRequest
public class UnifiedissuecouponcomponentRequest implements IMobileRequest {
    /**
     * 诚信字段
     */
    @MobileRequest.Param(name = "cx")
    private String cx;

    /**
     *
     */
    @MobileRequest.Param(name = "shopid")
    private String shopid;

    @MobileRequest.Param(name = "shopidstring")
    private String shopidstring;

    /**
     * 稀疏化shopId
     */
    @MobileRequest.Param(name = "shopuuid")
    private String shopuuid;

    /**
     *
     */
    @MobileRequest.Param(name = "productid")
    private String productid;

    /**
     * 城市 id
     */
//    @MobileRequest.Param(name = "cityid")
    private Integer cityid;

    /**
     * 产品类型，用来区分productid字段是表示团购还是商品
     */
    @MobileRequest.Param(name = "producttype")
    private Integer producttype;

    /**
     * 查询平台券
     * */
    @MobileRequest.Param(name = "queryplatformcoupon")
    private Boolean queryPlatformCoupon;

    /**
     * 领券渠道来源 {@link com.dianping.gmkt.wave.api.enums.ActivityChannelEnum}
     * */
    @MobileRequest.Param(name = "issuechannel")
    private Integer issueChannel;

    @MobileRequest.Param(name = "businessline")
    private Integer businessLine;

    @MobileRequest.Param(name = "mobilenumber")
    private String mobileNo;

    /**
     * 优惠券使用渠道
     * */
    @MobileRequest.Param(name = "couponplatform")
    private Integer couponPlatform;

    public String getCx() {
        return cx;
    }

    public void setCx(String cx) {
        this.cx = cx;
    }

    @Deprecated
    public Integer getShopid() {
        return NumberUtils.toInt(shopid);
    }

    public Long getShopidL() {
        long shopIdL = NumberUtils.toLong(shopidstring);
        return shopIdL > 0 ? shopIdL : NumberUtils.toLong(shopid);
    }

    public String getShopIdStr() {
        return StringUtils.isNotBlank(shopidstring) ? shopidstring : shopid;
    }

    public void setShopid(String shopid) {
        this.shopid = shopid;
        this.shopidstring = shopid;
    }

    public void setShopidlong(String shopidlong) {
        this.shopidstring = shopidlong;
    }

    public Integer getProductid() {
        return NumberUtils.toInt(productid);
    }

    public void setProductid(String productid) {
        this.productid = productid;
    }


    public Integer getProducttype() {
        return producttype;
    }

    public void setProducttype(Integer producttype) {
        this.producttype = producttype;
    }

    public Boolean getQueryPlatformCoupon() {
        return queryPlatformCoupon;
    }

    public void setQueryPlatformCoupon(Boolean queryPlatformCoupon) {
        this.queryPlatformCoupon = queryPlatformCoupon;
    }

    public Integer getIssueChannel() {
        return issueChannel;
    }

    public void setIssueChannel(Integer issueChannel) {
        this.issueChannel = issueChannel;
    }

    public Integer getBusinessLine() {
        return businessLine;
    }

    public String getMobileNo() {
        return mobileNo;
    }

    public void setMobileNo(String mobileNo) {
        this.mobileNo = mobileNo;
    }

    public void setBusinessLine(Integer businessLine) {
        this.businessLine = businessLine;
    }

    public Integer getCouponPlatform() {
        return couponPlatform;
    }

    public void setCouponPlatform(Integer couponPlatform) {
        this.couponPlatform = couponPlatform;
    }

    public String getShopuuid() {
        return shopuuid;
    }

    public void setShopuuid(String shopuuid) {
        this.shopuuid = shopuuid;
    }
}
