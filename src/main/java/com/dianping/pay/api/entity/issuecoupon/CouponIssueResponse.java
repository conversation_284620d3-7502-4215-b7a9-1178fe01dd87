package com.dianping.pay.api.entity.issuecoupon;

import lombok.Data;

/**
 * 发券通用resp
 */
@Data
public class CouponIssueResponse {

    private boolean isSuccess;

    private String errorMsg;

    private Object issueDetail;

    public static CouponIssueResponse errorResult(String errorMsg) {
        CouponIssueResponse couponIssueResponse = new CouponIssueResponse();
        couponIssueResponse.setSuccess(false);
        couponIssueResponse.setErrorMsg(errorMsg);
        couponIssueResponse.setIssueDetail(null);
        return couponIssueResponse;
    }

    public static CouponIssueResponse successResult(Object data) {
        CouponIssueResponse couponIssueResponse = new CouponIssueResponse();
        couponIssueResponse.setSuccess(true);
        couponIssueResponse.setErrorMsg("");
        couponIssueResponse.setIssueDetail(data);
        return couponIssueResponse;
    }
}
