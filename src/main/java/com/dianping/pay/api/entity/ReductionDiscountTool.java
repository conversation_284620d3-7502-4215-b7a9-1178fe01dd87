package com.dianping.pay.api.entity;

import com.dianping.api.common.util.KeyValuePair;
import com.dianping.api.domain.ClientInfo;
import com.dianping.api.domain.Version;
import com.dianping.cat.Cat;
import com.dianping.pay.cashier.biz.enums.PaymentRule;

import java.util.Arrays;
import java.util.List;

/**
 * Created by huawei.li on 15/3/10.
 */
public class ReductionDiscountTool extends AbstractDiscountTool {
    private static final long serialVersionUID = 2733908717331691196L;
    private List<DiscountEvent> discountEvents;

    private static final List<KeyValuePair> LIST = Arrays.asList(
            new KeyValuePair("ID", 0x91b),
            new KeyValuePair("name", 0xee8f),
            new KeyValuePair("desc", 0x7291),
            new KeyValuePair("canUse", 0x9069),
            new KeyValuePair("mutexDiscountTools", 0x8bc1),
            new KeyValuePair("discountEvents", 0xfb5f)
    );
    public ReductionDiscountTool(){
        super();
        setName("立减");
        setID(PaymentRule.REDUCTION.code);
        setCanUse(true);
    }
    public ReductionDiscountTool(List<DiscountEvent> events){
        super();
        setName("立减");
        setID(PaymentRule.REDUCTION.code);
        setCanUse(true);
        this.discountEvents = events;
    }
    @Override
    public int getClassId() {
        Cat.logEvent("INVALID_METHOD_2", "com.dianping.pay.api.entity.ReductionDiscountTool.getClassId()");
        return 0x7db3;
    }

    @Override
    public List<KeyValuePair> getSerializeList(ClientInfo clientInfo, Version version) {
        Cat.logEvent("INVALID_METHOD_4", "com.dianping.pay.api.entity.ReductionDiscountTool.getSerializeList(com.dianping.api.domain.ClientInfo,com.dianping.api.domain.Version)");
        return LIST;
    }

    public List<DiscountEvent> getDiscountEvents() {
        return discountEvents;
    }

    public void setDiscountEvents(List<DiscountEvent> discountEvents) {
        this.discountEvents = discountEvents;
    }
}
