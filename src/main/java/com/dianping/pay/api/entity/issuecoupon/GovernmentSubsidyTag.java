package com.dianping.pay.api.entity.issuecoupon;

import com.dianping.mobile.framework.annotation.MobileDo;
import lombok.Data;

import java.io.Serializable;

/**
 * 国补标签信息
 * <AUTHOR>
 */
@MobileDo(id = 0xc0a1)
@Data
public class GovernmentSubsidyTag implements Serializable {

    /**
     * 标签文案
     */
    @MobileDo.MobileField(key = 0x4210)
    private String tagText;

    /**
     * 补贴比例
     */
    @MobileDo.MobileField(key = 0x4211)
    private String subsidyRate;

    /**
     * 标签状态：0-可领取，1-已领取
     */
    @MobileDo.MobileField(key = 0x4212)
    private int tagStatus;

    /**
     * 标签优先级（用于排序）
     */
    @MobileDo.MobileField(key = 0x4213)
    private int priority = 1;

    /**
     * 标签ID
     */
    @MobileDo.MobileField(key = 0x4214)
    private String tagId = "21857";

    /**
     * 标签组名称
     */
    @MobileDo.MobileField(key = 0x4215)
    private String tagGroupName = "国补氛围条";

    /**
     * 标签值名称
     */
    @MobileDo.MobileField(key = 0x4216)
    private String tagValueName = "国家补贴";

}
