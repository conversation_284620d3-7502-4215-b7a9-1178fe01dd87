package com.dianping.pay.api.entity.issuecoupon;

import com.dianping.mobile.framework.annotation.MobileDo;
import com.dianping.pay.api.entity.IconPicInfo;
import lombok.Data;

@MobileDo(id = 0xe2f2)
@Data
public class ExposureResourceCouponInfo {

    /**
     * 投放资源门槛
     */
    @MobileDo.MobileField(key = 0xa84b)
    private String priceLimit;

    @MobileDo.MobileField(key = 0x3daf)
    private String priceLimitDesc;

    /**
     * 投放资源金额
     */
    @MobileDo.MobileField(key = 0xfbe2)
    private String amount;

    /**
     * 投放资源角标链接
     */
    @MobileDo.MobileField(key = 0xadd1)
    private String cornerMarkUrl;

    /**
     * 投放资源券名称
     */
    @MobileDo.MobileField(key = 0x6782)
    private String couponName;

    /**
     * 投放资源结束时间
     */
    @MobileDo.MobileField(key = 0xca7b)
    private long endTime;

    /**
     * 投放资源开始时间
     */
    @MobileDo.MobileField(key = 0x31b)
    private long beginTime;

    /**
     * 0=不隐藏，1=需要隐藏券（目前包括神会员券）
     */
    @MobileDo.MobileField(key = 0x80a8)
    private int needHideCoupon;

    /**
     * 0=非神会员券，1=神会员券
     */
    @MobileDo.MobileField(key = 0x457d)
    private int mmcCoupon;

    /**
     * 券列表组件新icon信息
     */
    @MobileDo.MobileField(key = 0x71c9)
    private String iconPicInfo;
}
