
package com.dianping.pay.api.entity;

import java.io.Serializable;
import java.util.List;

import org.apache.commons.lang3.StringUtils;

import com.dianping.api.common.entity.Pair;
import com.dianping.api.common.util.KeyValuePair;
import com.dianping.api.domain.Enum.ClientType;
import com.dianping.api.domain.ResultList;
import com.dianping.api.domain.ClientInfo;
import com.dianping.api.domain.Version;
import com.dianping.api.domain.Enum.AppType;
import com.dianping.api.framework.FeatureContingency;
import com.dianping.api.framework.FeatureContingencyConstants;
import com.dianping.api.util.ApiUtil;
import com.google.common.collect.Lists;

public class DealList extends ResultList<DealGroup> implements Serializable, Cloneable {
 
	private static final long serialVersionUID = 8433680789417041615L;
	private int version = -1;
	private String onClickLink;
	private String onClickLinkOld;
	private List<Pair> filterNav;

	// for main app
	// before 5.3: return url
	// 5.3 or later: empty or not depends on switch
	private String selectLink = "";
	private String buyLink = "";
	private String emptyString = StringUtils.EMPTY;
	
	private String detailLink;
	private String oldDetailLink;
	private int distance;
	private List<NaviTag> screeningList;
	
	private String queryId;
	
	public int getVersion() {
		if(version == -1) {
			StringBuilder sb = new StringBuilder(list.size()*100);
			for(DealGroup deal : list) {
				 deal.getVersion(sb) ;
			}
			version=sb.toString().hashCode();
		}
		return Math.abs(version);
	}
	
	public void setVersion(int version) {
		this.version = version;
	}
	 
	public static final List<KeyValuePair> LEGACY_OTHER_LIST = Lists.newArrayList();
	static{		
		LEGACY_OTHER_LIST.add(new KeyValuePair("onClickLinkOld",0x243e));		 
		LEGACY_OTHER_LIST.add(new KeyValuePair("version",0xefb4));
		LEGACY_OTHER_LIST.add(new KeyValuePair("regionNavs", 0x58ce));
		LEGACY_OTHER_LIST.add(new KeyValuePair("categoryNavs", 0x72de));
		LEGACY_OTHER_LIST.add(new KeyValuePair("filterNav", 0x155));
		LEGACY_OTHER_LIST.add(new KeyValuePair("buyLink", 0xf64f));
		LEGACY_OTHER_LIST.add(new KeyValuePair("selectLink", 0x432c));
		LEGACY_OTHER_LIST.add(new KeyValuePair("oldDetailLink", 0xb102));
		LEGACY_OTHER_LIST.addAll(ResultList.RESULTLIST);
	}

	public static final List<KeyValuePair> MAIN_APP_WEB_LIST = Lists.newArrayList();
	static {
		MAIN_APP_WEB_LIST.add(new KeyValuePair("onClickLink", 0x243e));
		MAIN_APP_WEB_LIST.add(new KeyValuePair("version", 0xefb4));
		MAIN_APP_WEB_LIST.add(new KeyValuePair("regionNavs", 0x58ce));
		MAIN_APP_WEB_LIST.add(new KeyValuePair("categoryNavs", 0x72de));
		MAIN_APP_WEB_LIST.add(new KeyValuePair("filterNav", 0x155));
		MAIN_APP_WEB_LIST.add(new KeyValuePair("buyLink", 0xf64f));
		MAIN_APP_WEB_LIST.add(new KeyValuePair("selectLink", 0x432c));
		MAIN_APP_WEB_LIST.add(new KeyValuePair("detailLink", 0xb102));
		MAIN_APP_WEB_LIST.addAll(ResultList.RESULTLIST);
	}
	
	public static final List<KeyValuePair> LIST = Lists.newArrayList();
	static {
		LIST.add(new KeyValuePair("onClickLink", 0x243e));
		LIST.add(new KeyValuePair("version", 0xefb4));
		LIST.add(new KeyValuePair("regionNavs", 0x58ce));
		LIST.add(new KeyValuePair("categoryNavs", 0x72de));
		LIST.add(new KeyValuePair("filterNav", 0x155));
		LIST.add(new KeyValuePair("emptyString", 0xf64f)); // buyLink
		LIST.add(new KeyValuePair("emptyString", 0x432c)); // selectLink
		LIST.add(new KeyValuePair("detailLink", 0xb102));
		LIST.add(new KeyValuePair("distance", ApiUtil.getHash("Distance")));
		LIST.add(new KeyValuePair("screeningList", ApiUtil.getHash("ScreeningList")));
		LIST.add(new KeyValuePair("queryId", 0x2d87));
		LIST.addAll(ResultList.RESULTLIST);
	}

	public static final List<KeyValuePair> LEGACY_LIST = Lists.newArrayList();
	static{		
		LEGACY_LIST.add(new KeyValuePair("onClickLinkOld",0x243e));		 
		LEGACY_LIST.add(new KeyValuePair("version",0xefb4));
		LEGACY_LIST.addAll(ResultList.RESULTLIST);
	}
	
	public DealList clone() {
		DealList clone = new DealList();
		clone.buyLink = this.getBuyLink();
		clone.detailLink = this.getDetailLink();
		List<Pair> tempFilterNav = Lists.newArrayList();
		List<Pair> fromFilterNav= this.getFilterNav();
		if (fromFilterNav != null) {
			for (Pair p : fromFilterNav) {
				tempFilterNav.add(p);
			}
		}
		clone.filterNav = tempFilterNav;
		clone.isEnd = this.isEnd;
		//clone deal 
		List<DealGroup> cloneList = Lists.newArrayList();
		if (this.getList() != null) {
			for (DealGroup d : this.getList()) {
				DealGroup dealclone = d.clone();
				cloneList.add(dealclone);
			}
		}
		clone.list = cloneList;
		clone.nextStartIndex = this.nextStartIndex;
		clone.onClickLink = this.onClickLink;
		clone.onClickLinkOld = this.onClickLinkOld;		
		clone.recordCount = this.recordCount;
		clone.selectLink = this.selectLink;
		clone.startIndex = this.startIndex;
		clone.version = this.version;
		return clone;
	}
	 
	
	@Override
	public List<KeyValuePair> getSerializeList(ClientInfo clientInfo, Version version) {
		if(clientInfo.getApp() == AppType.TG
				|| (clientInfo.getClient() == ClientType.iPadHd && Version.compareTo(version, new Version("2.3")) >= 0)) {
			return LIST;
		} else if (clientInfo.getApp() == AppType.Main && Version.compareTo(version, new Version("5.3")) >= 0) {
			if (FeatureContingency.isOn(FeatureContingencyConstants.FCID_MAIN_APP_NATIVE)) {
				return LIST;
			} else {
				return MAIN_APP_WEB_LIST;
			}
		} else {
			if(clientInfo.getClient().value == ClientType.iPadHd.value&&Version.compareTo(version, new Version("1.5"))<=0){
				return LEGACY_LIST;
			}else if(clientInfo.getClient().value == ClientType.iPhone.value&&Version.compareTo(version, new Version("4.7.2"))<=0){
				return LEGACY_LIST;
			}else if((clientInfo.getClient().value == ClientType.iPhone.value||clientInfo.getClient().value == ClientType.Android.value)&&Version.compareTo(version, new Version("4.8"))>=0){				 
				 return MAIN_APP_WEB_LIST;			
			}else{
				 return LEGACY_OTHER_LIST;	
			}
		}
	}

	public void addDeal(DealGroup deal){
		if(this.list == null){
			list = Lists.newArrayList();
		}
		list.add(deal);
	}
	public String getOnClickLink() {
		return onClickLink;
	}

	public void setOnClickLink(String onClickLink) {
		this.onClickLink = onClickLink;
	}


	public List<Pair> getFilterNav() {
		return filterNav;
	}

	public void setFilterNav(List<Pair> filterNav) {
		this.filterNav = filterNav;
	}

	public String getBuyLink() {
		return buyLink;
	}

	public void setBuyLink(String buyLink) {
		this.buyLink = buyLink;
	}

	public String getSelectLink() {
		return selectLink;
	}

	public void setSelectLink(String selectLink) {
		this.selectLink = selectLink;
	}

	public String getDetailLink() {
		return detailLink;
	}

	public void setDetailLink(String detailLink) {
		this.detailLink = detailLink;
	}

	public String getOnClickLinkOld() {
		return onClickLinkOld;
	}

	public void setOnClickLinkOld(String onClickLinkOld) {
		this.onClickLinkOld = onClickLinkOld;
	}

	public int getDistance() {
		return distance;
	}

	public void setDistance(int distance) {
		this.distance = distance;
	}

	public List<NaviTag> getScreeningList() {
		return screeningList;
	}

	public void setScreeningList(List<NaviTag> screeningList) {
		this.screeningList = screeningList;
	}

	/* (non-Javadoc)
	 * @see com.dianping.api.netio.DPEncoder#getClassId()
	 */
	@Override
	public int getClassId() {
 		return 0x4d0b;
	}
	public String getOldDetailLink() {
		return oldDetailLink;
	}
	public void setOldDetailLink(String oldDetailLink) {
		this.oldDetailLink = oldDetailLink;
	}

	public String getEmptyString() {
		return emptyString;
	}

	public void setEmptyString(String emptyString) {
		this.emptyString = emptyString;
	}
	
	public String getQueryId() {
		return this.queryId;
	}
	
	public void setQueryId(String queryId) {
		this.queryId = queryId;
	}
 
}

 
