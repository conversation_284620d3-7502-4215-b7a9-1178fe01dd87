package com.dianping.pay.api.entity;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

import com.dianping.api.common.util.KeyValuePair;
import com.dianping.api.domain.ClientInfo;
import com.dianping.api.domain.Version;
import com.dianping.api.netio.DPEncoder;
import com.google.common.collect.Lists;

public class Discount extends DPEncoder implements Serializable,Comparable<Discount> {

	private static final long serialVersionUID = -7923890025682872873L;

	public int getId() {
		return id;
	}

	public void setId(int id) {
		this.id = id;
	}

	public String getTitle() {
		return title;
	}

	public void setTitle(String title) {
		this.title = title;
	}

	public Date getExpiredDate() {
		return expiredDate;
	}

	public void setExpiredDate(Date expiredDate) {
		this.expiredDate = expiredDate;
	}

	public Date getBeginDate() {
		return beginDate;
	}

	public void setBeginDate(Date beginDate) {
		this.beginDate = beginDate;
	}

	public BigDecimal getPrice() {
		return price;
	}

	public void setPrice(BigDecimal price) {
		this.price = price;
		this.priceStr = price.toPlainString();
	}

	public String getPriceStr() {
		return priceStr;
	}

	public void setPriceStr(String priceStr) {
		this.priceStr = priceStr;
	}

    public double getPriceLimitStr() {
        return priceLimitStr;
    }

    public void setPriceLimitStr(double priceLimitStr) {
        this.priceLimitStr = priceLimitStr;
    }

    public Date getAddDate() {
        return addDate;
    }

    public void setAddDate(Date addDate) {
        this.addDate = addDate;
    }

    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content;
    }

    public String getUseLimit() {
        return useLimit;
    }

    public void setUseLimit(String useLimit) {
        this.useLimit = useLimit;
    }

    public boolean isExpired() {
        return expired;
    }

    public void setExpired(boolean expired) {
        this.expired = expired;
    }

    public BigDecimal getPriceLimit() {
        return priceLimit;
    }

    public void setPriceLimit(BigDecimal priceLimit) {
        this.priceLimit = priceLimit;
    }

    private static final List<KeyValuePair> LIST = Lists.newArrayList();
	static {
		LIST.add(new KeyValuePair("id", 0x91b));
		LIST.add(new KeyValuePair("title", 0x36e9));
		LIST.add(new KeyValuePair("expiredDate", 0x63ee));
		LIST.add(new KeyValuePair("beginDate", 0x48af));
		LIST.add(new KeyValuePair("priceStr", 0xc5b5));
		LIST.add(new KeyValuePair("priceLimitStr", 0x4038));
        LIST.add(new KeyValuePair("content",0x57b6));
        LIST.add(new KeyValuePair("addDate",0x2574));
        LIST.add(new KeyValuePair("useLimit",0xa77e));
        LIST.add(new KeyValuePair("expired",0x2a2a));
	}

	@Override
	public List<KeyValuePair> getSerializeList(ClientInfo clientInfo, Version version) {
		return LIST;
	}

	private int id;
	private String title;
	private Date expiredDate; // 过期时间
	private Date beginDate;
	private BigDecimal price; // 抵用价格
	private String priceStr;
    private BigDecimal priceLimit;
	private double priceLimitStr;
    private Date addDate;
    private String content;
    private String useLimit;
    private boolean expired;

	/*
	 * (non-Javadoc)
	 * 
	 * @see com.dianping.api.netio.DPEncoder#getClassId()
	 */
	@Override
	public int getClassId() {
		return 0xcd43;
	}

    @Override
    public int compareTo(Discount discount) {
        return -this.getPrice().compareTo(discount.getPrice());
    }
}
