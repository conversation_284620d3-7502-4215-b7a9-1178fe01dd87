package com.dianping.pay.api.entity.promodesk;

import com.dianping.api.common.util.KeyValuePair;
import com.dianping.api.domain.ClientInfo;
import com.dianping.api.domain.Version;
import com.dianping.api.netio.DPEncoder;

import java.io.Serializable;
import java.util.LinkedList;
import java.util.List;

public class PromoDeskCouponList extends DPEncoder implements Serializable {

    private int startIndex;
    private int nextStartIndex;
    private boolean isEnd;
    private List<PromoDeskCoupon> list;
    private List<PromoDeskCoupon> unavailableList;
    private List<PromoDeskCoupon> shopCouponList;
    private List<PromoDeskCoupon> unavailableShopCouponList;

    private static final LinkedList<KeyValuePair> l = new LinkedList<KeyValuePair>();
    static {
        l.add(new KeyValuePair("startIndex", 0xaa64));
        l.add(new KeyValuePair("nextStartIndex", 0x5703));
        l.add(new KeyValuePair("isEnd", 0xf0b));
        l.add(new KeyValuePair("list", 0x249a));
        l.add(new KeyValuePair("unavailableList", 0x5e26));
        l.add(new KeyValuePair("shopCouponList", 0x74c6));
        l.add(new KeyValuePair("unavailableShopCouponList", 0xa8d5));
    }

    @Override
    public int getClassId() {
        return 0xad12;
    }

    @Override
    public List<KeyValuePair> getSerializeList(ClientInfo clientInfo, Version version) {
        return l;
    }

    public int getStartIndex() {
        return startIndex;
    }

    public void setStartIndex(int startIndex) {
        this.startIndex = startIndex;
    }

    public int getNextStartIndex() {
        return nextStartIndex;
    }

    public void setNextStartIndex(int nextStartIndex) {
        this.nextStartIndex = nextStartIndex;
    }

    public boolean isIsEnd() {
        return isEnd;
    }

    public void setIsEnd(boolean isEnd) {
        this.isEnd = isEnd;
    }

    public List<PromoDeskCoupon> getList() {
        return list;
    }

    public void setList(List<PromoDeskCoupon> list) {
        this.list = list;
    }

    public List<PromoDeskCoupon> getUnavailableList() {
        return unavailableList;
    }

    public void setUnavailableList(List<PromoDeskCoupon> unavailableList) {
        this.unavailableList = unavailableList;
    }

    public List<PromoDeskCoupon> getShopCouponList() {
        return shopCouponList;
    }

    public void setShopCouponList(List<PromoDeskCoupon> shopCouponList) {
        this.shopCouponList = shopCouponList;
    }

    public List<PromoDeskCoupon> getUnavailableShopCouponList() {
        return unavailableShopCouponList;
    }

    public void setUnavailableShopCouponList(List<PromoDeskCoupon> unavailableShopCouponList) {
        this.unavailableShopCouponList = unavailableShopCouponList;
    }
}
