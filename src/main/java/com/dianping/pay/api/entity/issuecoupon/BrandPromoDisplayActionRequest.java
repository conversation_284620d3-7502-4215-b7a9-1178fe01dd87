package com.dianping.pay.api.entity.issuecoupon;

import com.dianping.mobile.framework.annotation.MobileRequest;
import com.dianping.mobile.framework.datatypes.IMobileRequest;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import lombok.Data;

/**
 * <AUTHOR>
 */
@TypeDoc(description = "brandpromodisplay.bin接口请求参数")
@MobileRequest
@Data
public class BrandPromoDisplayActionRequest implements IMobileRequest {

    /**
     * 购物车商品列表
     */
    @MobileRequest.Param(name = "brandid")
    private Long brandId;

    /**
     * 用户token
     */
    @MobileRequest.Param(name = "token")
    private String token;

    /**
     * 用户类型，0点评，1美团
     */
    @MobileRequest.Param(name = "usertype")
    private Integer usercode;

}
