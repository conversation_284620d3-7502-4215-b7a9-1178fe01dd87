package com.dianping.pay.api.entity.issuecoupon;

import lombok.Data;

/**
 * <AUTHOR>
 * @date 2021/1/7
 */
@Data
public class ExternalLinkCouponResponse {

    /**
     * 错误码，200成功，2001失败
     */
    private int code;

    /**
     * 描述信息
     */
    private String msg;

    /**
     * 结果
     */
    private Object data;

    public static ExternalLinkCouponResponse errorResult(String errorMsg) {
        ExternalLinkCouponResponse externalLinkCouponResponse = new ExternalLinkCouponResponse();
        externalLinkCouponResponse.setCode(2001);
        externalLinkCouponResponse.setMsg(errorMsg);
        externalLinkCouponResponse.setData(null);
        return externalLinkCouponResponse;
    }

    public static ExternalLinkCouponResponse successResult(Object data) {
        ExternalLinkCouponResponse externalLinkCouponResponse = new ExternalLinkCouponResponse();
        externalLinkCouponResponse.setCode(200);
        externalLinkCouponResponse.setMsg("");
        externalLinkCouponResponse.setData(data);
        return externalLinkCouponResponse;
    }
}
