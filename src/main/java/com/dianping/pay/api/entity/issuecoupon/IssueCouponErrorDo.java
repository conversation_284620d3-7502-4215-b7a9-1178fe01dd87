package com.dianping.pay.api.entity.issuecoupon;

import com.dianping.mobile.framework.annotation.MobileDo;

import java.io.Serializable;

/**
 * Created by drin<PERSON> on 18/6/11.
 */

@MobileDo(id = 0x59c3)
public class IssueCouponErrorDo implements Serializable {
    /**
     *
     */
    @MobileDo.MobileField(key = 0x44fb)
    private int errorCode;

    /**
     *
     */
    @MobileDo.MobileField(key = 0x7368)
    private String errorMsg;

    public int getErrorCode() {
        return errorCode;
    }

    public void setErrorCode(int errorCode) {
        this.errorCode = errorCode;
    }

    public String getErrorMsg() {
        return errorMsg;
    }

    public void setErrorMsg(String errorMsg) {
        this.errorMsg = errorMsg;
    }

    public IssueCouponErrorDo(int errorCode, String errorMsg) {
        this.errorCode = errorCode;
        this.errorMsg = errorMsg;
    }


}
