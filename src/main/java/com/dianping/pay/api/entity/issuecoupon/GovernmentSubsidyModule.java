package com.dianping.pay.api.entity.issuecoupon;

import com.dianping.mobile.framework.annotation.MobileDo;
import lombok.Data;

import java.io.Serializable;

/**
 * 国补浮层模块信息
 * <AUTHOR>
 */
@MobileDo(id = 0xc0a2)
@Data
public class GovernmentSubsidyModule implements Serializable {

    /**
     * 图标
     */
    @MobileDo.MobileField(key = 0x4220)
    private String icon = "国家补贴";

    /**
     * 优惠信息
     */
    @MobileDo.MobileField(key = 0x4221)
    private String discountInfo;

    /**
     * 注释信息
     */
    @MobileDo.MobileField(key = 0x4222)
    private String noteInfo = "最高补贴";

    /**
     * 主标题
     */
    @MobileDo.MobileField(key = 0x4223)
    private String mainTitle = "国家补贴";

    /**
     * 副标题
     */
    @MobileDo.MobileField(key = 0x4224)
    private String subTitle = "领后使用 美团支付、 云闪付可享";

    /**
     * 交互按钮文案
     */
    @MobileDo.MobileField(key = 0x4225)
    private String buttonText;

    /**
     * 交互按钮状态：0-立即领取，1-已领取
     */
    @MobileDo.MobileField(key = 0x4226)
    private int buttonStatus;

    /**
     * 跳转链接
     */
    @MobileDo.MobileField(key = 0x4227)
    private String jumpUrl;

}
