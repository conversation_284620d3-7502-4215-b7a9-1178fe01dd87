package com.dianping.pay.api.entity.issuecoupon;

import com.dianping.mobile.framework.annotation.MobileDo;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@MobileDo(id = 0x72d0)
@Data
public class CouponDetailPromoInfo implements Serializable {
    /**
     * 优惠类型，0：走老组件优惠样式，1：走新组件优惠样式
     */
    @MobileDo.MobileField(key = 0x7ca7)
    private int promoType;

    /**
     *
     */
    @MobileDo.MobileField(key = 0x926)
    private List<CouponPromoInfo> promoInfoItems;

    /**
     * 名称，例如:优惠
     */
    @MobileDo.MobileField(key = 0x24cc)
    private String title;
}