package com.dianping.pay.api.entity;

import java.io.Serializable;
import java.util.List;

import com.dianping.api.common.util.KeyValuePair;
import com.dianping.api.domain.ClientInfo;
import com.dianping.api.domain.Version;
import com.dianping.api.netio.DPEncoder;
import com.dianping.cat.Cat;
import com.google.common.collect.Lists;

public class DealComment extends DPEncoder implements Serializable {

	
	private static final long serialVersionUID = 9166507762105682091L;
	
	public String getComment() {
		return comment;
	}
	public void setComment(String comment) {
		this.comment = comment;
	}
	public String getUserName() {
		return userName;
	}
	public void setUserName(String userName) {
		this.userName = userName;
	}
	public String getUserLevel() {
		return userLevel;
	}
	public void setUserLevel(String userLevel) {
		this.userLevel = userLevel;
	}
	
	public long getUserId() {
		return userId;
	}
	public void setUserId(long userId) {
		this.userId = userId;
	}
	 

	private static final List<KeyValuePair> LIST = Lists.newArrayList();
	static {
		LIST.add(new KeyValuePair("comment", 0x1de1));
		LIST.add(new KeyValuePair("userName", 0x92e3));
		LIST.add(new KeyValuePair("userLevel", 0x8622));
		LIST.add(new KeyValuePair("userId", 0x8df6));		
	}

	@Override
	public List<KeyValuePair> getSerializeList(ClientInfo clientInfo, Version version) {
        Cat.logEvent("INVALID_METHOD_4", "com.dianping.pay.api.entity.DealComment.getSerializeList(com.dianping.api.domain.ClientInfo,com.dianping.api.domain.Version)");
        return LIST;
	}
	
	// 团购会员说
	private String comment;   //评论内容
	private String userName;  //用户名
	private String userLevel; //用户级别（钻石用户、四星用户）
	private long userId;

	/* (non-Javadoc)
	 * @see com.dianping.api.netio.DPEncoder#getClassId()
	 */
	@Override
	public int getClassId() {
        Cat.logEvent("INVALID_METHOD_1", "com.dianping.pay.api.entity.DealComment.getClassId()");
        return 0x2ab7;
	}
	
}
