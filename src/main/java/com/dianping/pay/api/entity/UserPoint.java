package com.dianping.pay.api.entity;

import com.dianping.api.common.util.KeyValuePair;
import com.dianping.api.domain.ClientInfo;
import com.dianping.api.domain.Version;
import com.dianping.api.netio.DPEncoder;
import com.dianping.cat.Cat;
import com.google.common.collect.Lists;

import java.io.Serializable;
import java.util.List;

/**
 * Created by xiaoxiao.li on 12/8/14.
 */
public class UserPoint extends DPEncoder implements Serializable {
    private static final long serialVersionUID = -2533806896822318734L;
    /**
     * 会员积分是否可用
     */
    private boolean canUseUserPoint;
    /**
     * 会员总积分
     */
    private int totalUserPointValue;
    /**
     * 会员积分兑换规则
     */
    private List<UserPointExchangeRule> userPointExchangeRules;

    private static final List<KeyValuePair> LIST = Lists.newArrayList();
    static {
        LIST.add(new KeyValuePair("canUseUserPoint",0xe4c0));
        LIST.add(new KeyValuePair("totalUserPointValue",0x9519));
        LIST.add(new KeyValuePair("userPointExchangeRules",0x9de));
    }
    @Override
    public int getClassId() {
        Cat.logEvent("INVALID_METHOD_5", "com.dianping.pay.api.entity.UserPoint.getClassId()");
        return 0xcabc;
    }

    @Override
    public List<KeyValuePair> getSerializeList(ClientInfo clientInfo, Version version) {
        Cat.logEvent("INVALID_METHOD_2", "com.dianping.pay.api.entity.UserPoint.getSerializeList(com.dianping.api.domain.ClientInfo,com.dianping.api.domain.Version)");
        return LIST;
    }

    public boolean isCanUseUserPoint() {
        return canUseUserPoint;
    }

    public void setCanUseUserPoint(boolean canUseUserPoint) {
        this.canUseUserPoint = canUseUserPoint;
    }

    public int getTotalUserPointValue() {
        return totalUserPointValue;
    }

    public void setTotalUserPointValue(int totalUserPointValue) {
        this.totalUserPointValue = totalUserPointValue;
    }

    public List<UserPointExchangeRule> getUserPointExchangeRules() {
        return userPointExchangeRules;
    }

    public void setUserPointExchangeRules(List<UserPointExchangeRule> userPointExchangeRules) {
        this.userPointExchangeRules = userPointExchangeRules;
    }
}
