package com.dianping.pay.api.entity;

import java.io.Serializable;
import java.util.LinkedList;
import java.util.List;

import com.dianping.api.common.util.KeyValuePair;
import com.dianping.api.domain.ClientInfo;
import com.dianping.api.domain.Version;
import com.dianping.api.netio.DPEncoder;
import com.dianping.api.util.ApiUtil;
import com.dianping.cat.Cat;

public class DiscountList extends DPEncoder implements Serializable {

	/**
	 * 
	 */
	private static final long serialVersionUID = 7027858182789846661L;
	
	private List<Discount> discountList;
	
	private boolean isEnd = true;
	
	private int startIndex; 
	private int nextStartIndex;

	public List<Discount> getDiscountList() {
		return discountList;
	}

	public void setDiscountList(List<Discount> discountList) {
		this.discountList = discountList;
	}

	public boolean isIsEnd() {
		return isEnd;
	}

	public void setIsEnd(boolean isEnd) {
		this.isEnd = isEnd;
	}

	public int getStartIndex() {
		return startIndex;
	}

	public void setStartIndex(int startIndex) {
		this.startIndex = startIndex;
	}

	public int getNextStartIndex() {
		return nextStartIndex;
	}

	public void setNextStartIndex(int nextStartIndex) {
		this.nextStartIndex = nextStartIndex;
	}

	@Override
	public int getClassId() {
        Cat.logEvent("INVALID_METHOD_5", "com.dianping.pay.api.entity.DiscountList.getClassId()");
        return ApiUtil.getHash(DiscountList.class.getSimpleName());
	}

	public static final LinkedList<KeyValuePair> l = new LinkedList<KeyValuePair>();
	static {
		l.add(new KeyValuePair("discountList",ApiUtil.getHash("List")));
		l.add(new KeyValuePair("isEnd",ApiUtil.getHash("IsEnd")));
		l.add(new KeyValuePair("startIndex",ApiUtil.getHash("StartIndex")));
		l.add(new KeyValuePair("nextStartIndex",ApiUtil.getHash("NextStartIndex")));
	}
	
	@Override
	public List<KeyValuePair> getSerializeList(ClientInfo clientInfo,
			Version version) {
        Cat.logEvent("INVALID_METHOD_1", "com.dianping.pay.api.entity.DiscountList.getSerializeList(com.dianping.api.domain.ClientInfo,com.dianping.api.domain.Version)");
        return l;
	}

}
