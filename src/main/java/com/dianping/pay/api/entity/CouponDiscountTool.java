package com.dianping.pay.api.entity;

import com.dianping.api.common.util.KeyValuePair;
import com.dianping.api.domain.ClientInfo;
import com.dianping.api.domain.Version;
import com.dianping.cat.Cat;
import com.dianping.pay.cashier.biz.enums.PaymentRule;

import java.util.Arrays;
import java.util.List;

/**
 * Created by huawei.li on 15/3/10.
 */
public class CouponDiscountTool extends AbstractDiscountTool {
    private static final long serialVersionUID = 3230052930446638651L;
    private boolean hasCoupon;
    private List<Discount> optimalCoupons;
    private static final List<KeyValuePair> LIST = Arrays.asList(
            new KeyValuePair("ID", 0x91b),
            new KeyValuePair("name", 0xee8f),
            new KeyValuePair("desc", 0x7291),
            new KeyValuePair("canUse", 0x9069),
            new KeyValuePair("mutexDiscountTools", 0x8bc1),
            new KeyValuePair("hasCoupon", 0xd0e5)
    );
    public CouponDiscountTool(){
        setID(PaymentRule.COUPON.code);
        setName("抵用券/优惠代码");
        setMutexDiscountTools(Arrays.asList(PaymentRule.HONGBAO.code));
    }
    public CouponDiscountTool(boolean hasCoupon){
        setID(PaymentRule.COUPON.code);
        setName("抵用券/优惠代码");
        setMutexDiscountTools(Arrays.asList(PaymentRule.HONGBAO.code));
        setHasCoupon(hasCoupon);
    }
    @Override
    public int getClassId() {
        Cat.logEvent("INVALID_METHOD_2", "com.dianping.pay.api.entity.CouponDiscountTool.getClassId()");
        return 0xc05d;
    }

    @Override
    public List<KeyValuePair> getSerializeList(ClientInfo clientInfo, Version version) {
        Cat.logEvent("INVALID_METHOD_2", "com.dianping.pay.api.entity.CouponDiscountTool.getSerializeList(com.dianping.api.domain.ClientInfo,com.dianping.api.domain.Version)");
        return LIST;
    }

    public boolean isHasCoupon() {
        return hasCoupon;
    }

    public void setHasCoupon(boolean hasCoupon) {
        this.hasCoupon = hasCoupon;
    }

    public List<Discount> getOptimalCoupons() {
        return optimalCoupons;
    }

    public void setOptimalCoupons(List<Discount> optimalCoupons) {
        this.optimalCoupons = optimalCoupons;
    }
}
