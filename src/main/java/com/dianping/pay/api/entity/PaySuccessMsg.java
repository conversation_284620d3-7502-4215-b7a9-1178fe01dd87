package com.dianping.pay.api.entity;

import com.dianping.api.common.util.KeyValuePair;
import com.dianping.api.domain.ClientInfo;
import com.dianping.api.domain.SuccessMsg;
import com.dianping.api.domain.Version;
import com.dianping.cat.Cat;

import java.util.ArrayList;
import java.util.List;

/**
 * User: huawei.li
 * Date: 14-4-2
 * Time: 下午3:47
 */
public class PaySuccessMsg extends SuccessMsg {
    private static final long serialVersionUID = -1920044134012069559L;
    private boolean needReconfirm;
    private double reductionPrice;
    private String reductionDesc;
    private DiscountList discountList;
    private int orderID;

    private static final List<KeyValuePair> payList=new ArrayList<KeyValuePair>();
    static{
        payList.add(new KeyValuePair("title", 0x36e9));
        payList.add(new KeyValuePair("content",0x57b6));
        payList.add(new KeyValuePair("icon",0xb0bb));
        payList.add(new KeyValuePair("flag",0x73ad));
        payList.add(new KeyValuePair("returnID",0xefe5));
        payList.add(new KeyValuePair("needReconfirm",0x8b77));
        payList.add(new KeyValuePair("reductionPrice",0x1926));
        payList.add(new KeyValuePair("reductionDesc",0xfffe));
        payList.add(new KeyValuePair("discountList",0x4f16));
        payList.add(new KeyValuePair("orderID",0x978c));
    }
    public boolean isNeedReconfirm() {
        return needReconfirm;
    }

    public void setNeedReconfirm(boolean needReconfirm) {
        this.needReconfirm = needReconfirm;
    }

    public double getReductionPrice() {
        return reductionPrice;
    }

    public void setReductionPrice(double reductionPrice) {
        this.reductionPrice = reductionPrice;
    }

    public String getReductionDesc() {
        return reductionDesc;
    }

    public void setReductionDesc(String reductionDesc) {
        this.reductionDesc = reductionDesc;
    }

    public DiscountList getDiscountList() {
        return discountList;
    }

    public void setDiscountList(DiscountList discountList) {
        this.discountList = discountList;
    }

    public int getOrderID() {
        return orderID;
    }

    public void setOrderID(int orderID) {
        this.orderID = orderID;
    }
    @Override
    public int getClassId() {
        Cat.logEvent("INVALID_METHOD_3", "com.dianping.pay.api.entity.PaySuccessMsg.getClassId()");
        return 0x4957;
    }
    @Override
    public List<KeyValuePair> getSerializeList(ClientInfo clientInfo, Version version){
        Cat.logEvent("INVALID_METHOD_5", "com.dianping.pay.api.entity.PaySuccessMsg.getSerializeList(com.dianping.api.domain.ClientInfo,com.dianping.api.domain.Version)");
        return payList;
    }
}
