package com.dianping.pay.api.entity.issuecoupon;

import com.dianping.api.common.util.KeyValuePair;
import com.dianping.api.domain.ClientInfo;
import com.dianping.api.domain.Version;
import com.dianping.api.netio.DPEncoder;

import java.io.Serializable;
import java.util.LinkedList;
import java.util.List;

public class IssueCouponTag extends DPEncoder implements Serializable {

    private String category;
    private String tag;

    private static final LinkedList<KeyValuePair> l = new LinkedList<KeyValuePair>();
    static {
        l.add(new KeyValuePair("category", 0xbffc));
        l.add(new KeyValuePair("tag", 0xbf9b));
    }

    @Override
    public int getClassId() {
        return 0xe642;
    }

    @Override
    public List<KeyValuePair> getSerializeList(ClientInfo clientInfo, Version version) {
        return l;
    }

    public String getCategory() {
        return category;
    }

    public void setCategory(String category) {
        this.category = category;
    }

    public String getTag() {
        return tag;
    }

    public void setTag(String tag) {
        this.tag = tag;
    }

    @Override
    public String toString() {
        return "IssueCouponTag{" +
                "category='" + category + '\'' +
                ", tag='" + tag + '\'' +
                '}';
    }
}
