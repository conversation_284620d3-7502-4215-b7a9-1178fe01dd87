package com.dianping.pay.api.entity.issuecoupon;

import com.dianping.api.common.util.KeyValuePair;
import com.dianping.api.domain.ClientInfo;
import com.dianping.api.domain.Version;
import com.dianping.api.netio.DPEncoder;
import com.dianping.mobile.framework.annotation.MobileDo;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.LinkedList;
import java.util.List;

@MobileDo(id = 0x1b95)
public class BeautyIssueCouponComponent extends DPEncoder implements Serializable {

    @MobileDo.MobileField(key = 0xe3e5)
    private String beautyShopCouponTag;

    @MobileDo.MobileField(key = 0xe349)
    private String beautyPlatformCouponTag;

    @MobileDo.MobileField(key = 0xd79f)
    private String beautyShopSecondTag;

    @MobileDo.MobileField(key = 0xd61c)
    private List<BeautyIssueCouponTag> beautyCouponTagList = new ArrayList<BeautyIssueCouponTag>();

    @MobileDo.MobileField(key = 0x223c)
    private List<BeautyIssueCouponOption> beautyShopCouponOptionList = new ArrayList<BeautyIssueCouponOption>();

    @MobileDo.MobileField(key = 0xe153)
    private List<BeautyIssueCouponOption> beautyPlatformCouponOptionList = new ArrayList<BeautyIssueCouponOption>();

    private static final LinkedList<KeyValuePair> l = new LinkedList<KeyValuePair>();
    static {
        l.add(new KeyValuePair("beautyShopCouponTag", 0xe3e5));
        l.add(new KeyValuePair("beautyPlatformCouponTag", 0xe349));
        l.add(new KeyValuePair("beautyShopSecondTag", 0xd79f));
        l.add(new KeyValuePair("beautyCouponTagList", 0xd61c));
        l.add(new KeyValuePair("beautyShopCouponOptionList", 0x223c));
        l.add(new KeyValuePair("beautyPlatformCouponOptionList", 0xe153));
    }

    @Override
    public int getClassId() {
        return 0x1b95;
    }

    @Override
    public List<KeyValuePair> getSerializeList(ClientInfo clientInfo, Version version) {
        return l;
    }

    public List<BeautyIssueCouponTag> getBeautyCouponTagList() {
        return beautyCouponTagList;
    }

    public void setBeautyCouponTagList(List<BeautyIssueCouponTag> beautyCouponTagList) {
        this.beautyCouponTagList = beautyCouponTagList;
    }

    public List<BeautyIssueCouponOption> getBeautyShopCouponOptionList() {
        return beautyShopCouponOptionList;
    }

    public void setBeautyShopCouponOptionList(List<BeautyIssueCouponOption> beautyShopCouponOptionList) {
        this.beautyShopCouponOptionList = beautyShopCouponOptionList;
    }

    public List<BeautyIssueCouponOption> getBeautyPlatformCouponOptionList() {
        return beautyPlatformCouponOptionList;
    }

    public void setBeautyPlatformCouponOptionList(List<BeautyIssueCouponOption> beautyPlatformCouponOptionList) {
        this.beautyPlatformCouponOptionList = beautyPlatformCouponOptionList;
    }

    public String getBeautyShopCouponTag() {
        return beautyShopCouponTag;
    }

    public void setBeautyShopCouponTag(String beautyShopCouponTag) {
        this.beautyShopCouponTag = beautyShopCouponTag;
    }

    public String getBeautyPlatformCouponTag() {
        return beautyPlatformCouponTag;
    }

    public void setBeautyPlatformCouponTag(String beautyPlatformCouponTag) {
        this.beautyPlatformCouponTag = beautyPlatformCouponTag;
    }

    public String getBeautyShopSecondTag() {
        return beautyShopSecondTag;
    }

    public void setBeautyShopSecondTag(String beautyShopSecondTag) {
        this.beautyShopSecondTag = beautyShopSecondTag;
    }


}
