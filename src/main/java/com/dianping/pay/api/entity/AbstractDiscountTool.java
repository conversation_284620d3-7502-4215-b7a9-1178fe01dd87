package com.dianping.pay.api.entity;

import com.dianping.api.common.util.KeyValuePair;
import com.dianping.api.netio.DPEncoder;

import java.io.Serializable;
import java.util.Arrays;
import java.util.List;

/**
 * Created by huawei.li on 15/3/10.
 */
public abstract class AbstractDiscountTool extends DPEncoder implements Serializable {
    private static final long serialVersionUID = -3451709961369476797L;
    private int ID;
    private String name;
    private String desc;
    private boolean canUse;
    private List<Integer> mutexDiscountTools;

    private static final List<KeyValuePair> ABSTRACTLIST = Arrays.asList(
            new KeyValuePair("ID", 0x91b),
            new KeyValuePair("name", 0xee8f),
            new KeyValuePair("desc", 0x7291),
            new KeyValuePair("canUse", 0x9069),
            new KeyValuePair("mutexDisountTools", 0x8bc1)
    );


    public int getID() {
        return ID;
    }

    public void setID(int ID) {
        this.ID = ID;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }

    public boolean isCanUse() {
        return canUse;
    }

    public void setCanUse(boolean canUse) {
        this.canUse = canUse;
    }

    public List<Integer> getMutexDiscountTools() {
        return mutexDiscountTools;
    }

    public void setMutexDiscountTools(List<Integer> mutexDiscountTools) {
        this.mutexDiscountTools = mutexDiscountTools;
    }
}
