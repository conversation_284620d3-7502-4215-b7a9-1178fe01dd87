package com.dianping.pay.api.entity.issuecoupon;

import com.dianping.mobile.framework.annotation.MobileDo;
import lombok.Data;

import java.io.Serializable;

/**
 * 购物车满件折
 */
@Data
@MobileDo(id = 0x5979)
public class BuycarDiscountDetailInfo implements Serializable {


    /**
     * 角标文案 （eg：限新客专享）
     */
    @MobileDo.MobileField(key = 0x192e)
    private String script;

    /**
     * 满件折使用规则描述
     */
    @MobileDo.MobileField(key = 0xa783)
    private String useRuleDesc;

    /**
     * 满件折使用规则tip
     */
    @MobileDo.MobileField(key = 0xa7d4)
    private String useRuleTip;

    /**
     * 使用规则补充说明
     */
    @MobileDo.MobileField(key = 0xa2b7)
    private String additionalUseRule;

    /**
     * 满件折时间规则
     */
    @MobileDo.MobileField(key = 0x12d8)
    private String timeDesc;

    /**
     * X折，不带"折"字， eg: 8
     */
    @MobileDo.MobileField(key = 0xbb3d)
    private String discountValue;

    /**
     * 满件折门槛描述， eg：满2件
     */
    @MobileDo.MobileField(key = 0x5bdf)
    private String discountLimitDesc;


}
