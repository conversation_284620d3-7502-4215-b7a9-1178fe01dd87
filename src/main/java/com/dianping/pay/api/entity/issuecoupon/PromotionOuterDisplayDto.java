package com.dianping.pay.api.entity.issuecoupon;

import com.dianping.mobile.framework.annotation.MobileDo;
import com.dianping.mobile.framework.annotation.MobileDo.MobileField;
import lombok.Data;

import java.util.List;
import java.io.Serializable;


/**
 * <AUTHOR>
 * @date 08/04/2020
 * @time 18:27
 * 模型描述：优惠信息外层缩略展示文案
 * 移动之家链接: https://mobile.sankuai.com/studio/model/info/23273
 */
@MobileDo(id = 0xe87d)
@Data
public class PromotionOuterDisplayDto implements Serializable {


    /**
     * 外层展示内容   eg.满xxx减yyy.../消费返X元券
     */
    @MobileField(key = 0x2a3a)
    private String outerPromotionText;

    /**
     * 外层展示title  eg.抵用券/返礼
     */
    @MobileField(key = 0x1cb5)
    private String outerPromotionTitle;

}
