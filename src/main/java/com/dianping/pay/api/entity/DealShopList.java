
package com.dianping.pay.api.entity;

import java.util.List;

import com.dianping.cat.Cat;
import com.google.common.collect.Lists;

public class DealShopList {

	private List<MyDealShop> dealShopList = Lists.newArrayList();

	public List<MyDealShop> getDealShopList() {
		return dealShopList;
	}

	public void setDealShopList(List<MyDealShop> dealShopList) {
		this.dealShopList = dealShopList;
	}

	public void addDealShop(MyDealShop dealShop) {
        Cat.logEvent("INVALID_METHOD_2", "com.dianping.pay.api.entity.DealShopList.addDealShop(com.dianping.pay.api.entity.MyDealShop)");
        this.dealShopList.add(dealShop);
	}

}
