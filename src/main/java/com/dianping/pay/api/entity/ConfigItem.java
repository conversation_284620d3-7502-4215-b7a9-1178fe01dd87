package com.dianping.pay.api.entity;

import java.io.Serializable;
import java.util.List;

import com.dianping.api.common.entity.Pair;
import com.dianping.api.common.util.KeyValuePair;
import com.dianping.api.domain.ClientInfo;
import com.dianping.api.domain.Version;
import com.dianping.api.netio.DPEncoder;
import com.dianping.cat.Cat;
import com.google.common.collect.Lists;

public class ConfigItem extends DPEncoder implements Serializable {

	private static final long serialVersionUID = 3344011510173946431L;

	private String id;
	private String name;
	private int type = 1;
	private List<Pair> options;

	public static List<KeyValuePair> LIST = Lists.newArrayList();
	static {
		LIST.add(new KeyValuePair("id", 0x091b));
		LIST.add(new KeyValuePair("name", 0xee8f));
		LIST.add(new KeyValuePair("type", 0x0372));
		LIST.add(new KeyValuePair("options", 0x06c1));
	}

	public ConfigItem() {
	}
	
	public ConfigItem(String id, String name, List<Pair> options) {
		this.id = id;
		this.name = name;
		this.options = options;
	}
	
	@Override
	public List<KeyValuePair> getSerializeList(ClientInfo clientInfo,
			Version version) {
        Cat.logEvent("INVALID_METHOD_3", "com.dianping.pay.api.entity.ConfigItem.getSerializeList(com.dianping.api.domain.ClientInfo,com.dianping.api.domain.Version)");
        return LIST;
	}

	/*
	 * (non-Javadoc)
	 * 
	 * @see com.dianping.api.netio.DPEncoder#getClassId()
	 */
	@Override
	public int getClassId() {
        Cat.logEvent("INVALID_METHOD_5", "com.dianping.pay.api.entity.ConfigItem.getClassId()");
        return 0x75c9;
	}

	public String getId() {
		return id;
	}

	public void setId(String id) {
		this.id = id;
	}

	public String getName() {
		return name;
	}

	public void setName(String name) {
		this.name = name;
	}

	public List<Pair> getOptions() {
		return options;
	}

	public void setOptions(List<Pair> options) {
		this.options = options;
	}

	public int getType() {
		return type;
	}

	public void setType(int type) {
		this.type = type;
	}

}
