package com.dianping.pay.api.entity;

import com.dianping.api.common.util.KeyValuePair;
import com.dianping.api.domain.ClientInfo;
import com.dianping.api.domain.Version;
import com.dianping.api.netio.DPEncoder;
import com.dianping.cat.Cat;
import com.google.common.collect.Lists;

import java.io.Serializable;
import java.util.List;

/**
 * Created by xiaoxiao.li on 12/11/14.
 */
public class UserPointExchangeRule extends DPEncoder implements Serializable {
    /**
     * 规则ID
     */
    private int ruleID;
    /**
     * 会员积分数额
     */
    private int userPointValue;
    /**
     * 会员积分抵扣金额
     */
    private double userPointBalance;
    /**
     * 备注
     */
    private String memo;

    private static final List<KeyValuePair> LIST = Lists.newArrayList();
    static {
        LIST.add(new KeyValuePair("ruleID",0x2fa7));
        LIST.add(new KeyValuePair("userPointValue",0xe43d));
        LIST.add(new KeyValuePair("userPointBalance",0xafa1));
        LIST.add(new KeyValuePair("memo",0x897e));
    }

    public int getRuleID() {
        return ruleID;
    }

    public void setRuleID(int ruleID) {
        this.ruleID = ruleID;
    }

    public int getUserPointValue() {
        return userPointValue;
    }

    public void setUserPointValue(int userPointValue) {
        this.userPointValue = userPointValue;
    }

    public double getUserPointBalance() {
        return userPointBalance;
    }

    public void setUserPointBalance(double userPointBalance) {
        this.userPointBalance = userPointBalance;
    }

    public String getMemo() {
        return memo;
    }

    public void setMemo(String memo) {
        this.memo = memo;
    }

    @Override
    public int getClassId() {
        Cat.logEvent("INVALID_METHOD_1", "com.dianping.pay.api.entity.UserPointExchangeRule.getClassId()");
        return 0xb368;
    }

    @Override
    public List<KeyValuePair> getSerializeList(ClientInfo clientInfo, Version version) {
        Cat.logEvent("INVALID_METHOD_1", "com.dianping.pay.api.entity.UserPointExchangeRule.getSerializeList(com.dianping.api.domain.ClientInfo,com.dianping.api.domain.Version)");
        return LIST;
    }
}
