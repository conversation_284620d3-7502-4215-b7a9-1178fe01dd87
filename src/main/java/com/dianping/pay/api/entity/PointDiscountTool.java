package com.dianping.pay.api.entity;

import com.dianping.api.common.util.KeyValuePair;
import com.dianping.api.domain.ClientInfo;
import com.dianping.api.domain.Version;
import com.dianping.cat.Cat;
import com.dianping.pay.cashier.biz.enums.PaymentRule;

import java.util.Arrays;
import java.util.List;

/**
 * Created by huawei.li on 15/3/10.
 */
public class PointDiscountTool extends AbstractDiscountTool {
    private static final long serialVersionUID = 1351433859214998188L;
    /**
     * 会员总积分
     */
    private int totalUserPointValue;
    /**
     * 会员积分兑换规则
     */
    private List<UserPointExchangeRule> userPointExchangeRules;

    private static final List<KeyValuePair> LIST = Arrays.asList(
            new KeyValuePair("ID", 0x91b),
            new KeyValuePair("name", 0xee8f),
            new KeyValuePair("desc", 0x7291),
            new KeyValuePair("canUse", 0x9069),
            new KeyValuePair("mutexDiscountTools", 0x8bc1),
            new KeyValuePair("totalUserPointValue", 0x9519),
            new KeyValuePair("userPointExchangeRules", 0x9de)
    );
    public PointDiscountTool(){
        setID(PaymentRule.POINT.code);
        setName("积分");
    }
    @Override
    public int getClassId() {
        Cat.logEvent("INVALID_METHOD_1", "com.dianping.pay.api.entity.PointDiscountTool.getClassId()");
        return 0xf582;
    }

    @Override
    public List<KeyValuePair> getSerializeList(ClientInfo clientInfo, Version version) {
        Cat.logEvent("INVALID_METHOD_1", "com.dianping.pay.api.entity.PointDiscountTool.getSerializeList(com.dianping.api.domain.ClientInfo,com.dianping.api.domain.Version)");
        return LIST;
    }

    public int getTotalUserPointValue() {
        return totalUserPointValue;
    }

    public void setTotalUserPointValue(int totalUserPointValue) {
        this.totalUserPointValue = totalUserPointValue;
    }

    public List<UserPointExchangeRule> getUserPointExchangeRules() {
        return userPointExchangeRules;
    }

    public void setUserPointExchangeRules(List<UserPointExchangeRule> userPointExchangeRules) {
        this.userPointExchangeRules = userPointExchangeRules;
    }
}
