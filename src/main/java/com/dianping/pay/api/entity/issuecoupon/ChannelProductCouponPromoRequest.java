package com.dianping.pay.api.entity.issuecoupon;

import com.dianping.mobile.framework.annotation.MobileRequest;
import com.dianping.mobile.framework.datatypes.IMobileRequest;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import lombok.Data;

@MobileRequest
@Data
public class ChannelProductCouponPromoRequest implements IMobileRequest {

    /**
     * 0：团单；1：商品sku; 3：标品; 4:spu
     */
    @MobileRequest.Param(name = "producttype")
    private Integer producttype;

    /**
     * 设备号，有则传
     */
    @MobileRequest.Param(name = "dpid")
    private String dpid;

    /**
     * 用户token
     */
    @MobileRequest.Param(name = "token")
    private String token;

    @MobileRequest.Param(name = "cx")
    private String cx;

    /**
     * 用户类型，0点评，1美团
     */
    @MobileRequest.Param(name = "usertype")
    private Integer usercode;

    /**
     * 门店id，点评传点评，美团传美团
     */
    @MobileRequest.Param(name = "shopid")
    private Long shopid;

    /**
     * 稀疏化shopid，如果是点评的话，传点评shopuuid
     */
    @MobileRequest.Param(name = "shopuuid")
    private String shopuuid;

    /**
     * 城市id
     */
    @MobileRequest.Param(name = "cityid")
    private Integer cityid;

    /**
     * 商品id，如果是团购类型，则是团单id(dealGroupId)
     */
    @MobileRequest.Param(name = "productid")
    private Long productid;

    /**
     * 经度
     */
    @MobileRequest.Param(name = "longitude")
    private Double longitude;

    /**
     * 纬度
     */
    @MobileRequest.Param(name = "latitude")
    private Double latitude;

    @MobileRequest.Param(name = "clienttype")
    private Integer clienttype;

    @MobileRequest.Param(name = "platform")
    private Integer platform;

    @FieldDoc(
            description = "渠道标识"
    )
    @MobileRequest.Param(name = "channel")
    private String channel;

    @FieldDoc(
            description = "分销详细信息 urlEncode编码过的字段 需要进行解码"
    )
    @MobileRequest.Param(name = "dealextparam")
    private String dealextparam;


}
