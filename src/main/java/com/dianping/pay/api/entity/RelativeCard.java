package com.dianping.pay.api.entity;

import com.dianping.api.common.util.KeyValuePair;
import com.dianping.api.domain.ClientInfo;
import com.dianping.api.domain.Version;
import com.dianping.api.netio.DPEncoder;
import com.dianping.api.util.ApiUtil;

import java.io.Serializable;
import java.util.Date;
import java.util.LinkedList;
import java.util.List;

public class RelativeCard extends DPEncoder implements Serializable, Cloneable {

    private static final long serialVersionUID = -83023815046568609L;

    public static final LinkedList<KeyValuePair> l = new LinkedList<KeyValuePair>();

    static {
        l.add(new KeyValuePair("cardId", ApiUtil.getHash("CardID")));
        l.add(new KeyValuePair("productId", ApiUtil.getHash("ProductID")));
        l.add(new KeyValuePair("endDate", ApiUtil.getHash("EndDate")));
        l.add(new KeyValuePair("cardNO", ApiUtil.getHash("CardNO")));
        l.add(new KeyValuePair("balance", ApiUtil.getHash("Balance")));
        l.add(new KeyValuePair("title", ApiUtil.getHash("Title")));
    }

    /**
    * 团购券描述符
    */
    private String title;
    /**
     * 会员卡ID
     */
    private Integer cardId;
    /**
     * 产品ID
     */
    private Integer productId;
    /**
     * 会员卡卡号
     */
    private String cardNO;
    /**
     * 用户团购券余额
     */
    private String balance;
    /**
     * 此团购券的有效期
     */
    private Date endDate;
    /**
     * 用户最后充值时间
     */
    private Date lastBuyDate;

    public RelativeCard() {
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public Integer getProductId() {
        return productId;
    }

    public void setProductId(Integer productId) {
        this.productId = productId;
    }

    public Date getEndDate() {
        return endDate;
    }

    public void setEndDate(Date endDate) {
        this.endDate = endDate;
    }

    public Date getLastBuyDate() {
        return lastBuyDate;
    }

    public void setLastBuyDate(Date lastBuyDate) {
        this.lastBuyDate = lastBuyDate;
    }

    @Override
    public int getClassId() {
        return ApiUtil.getHash(getClass().getSimpleName());
    }

    @Override
    public List<KeyValuePair> getSerializeList(ClientInfo clientInfo,
                                               Version version) {
        return l;
    }

    public Integer getCardId() {
        return cardId;
    }

    public void setCardId(Integer cardId) {
        this.cardId = cardId;
    }

    public String getBalance() {
        return balance;
    }

    public void setBalance(String balance) {
        this.balance = balance;
    }

    public String getCardNO() {
        return cardNO;
    }

    public void setCardNO(String cardNO) {
        this.cardNO = cardNO;
    }
}
