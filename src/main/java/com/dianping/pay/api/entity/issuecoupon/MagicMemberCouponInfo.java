package com.dianping.pay.api.entity.issuecoupon;

import com.dianping.mobile.framework.annotation.MobileDo;
import com.dianping.mobile.framework.annotation.MobileDo.*;
import lombok.Data;

import java.io.Serializable;

@MobileDo(id = 0x85b)
@Data
public class MagicMemberCouponInfo implements Serializable {
    /**
     * 膨胀用加密串
     */
    @MobileField(key = 0xcb02)
    private String bizToken;

    /**
     * 是否已失效，0=失效，1=有效，固定传1
     */
    @MobileField(key = 0xc894)
    private int validStatus;

    /**
     * 是否已膨胀，0=未膨胀，1=已膨胀
     */
    @MobileField(key = 0x73d9)
    private int inflatedStatus;

    /**
     * 是否可膨胀，0=不可膨胀，1=可膨胀
     */
    @MobileField(key = 0x2055)
    private int canInflate;

    /**
     * 0=不可用券，1=可用券，固定传1
     */
    @MobileField(key = 0x5e88)
    private int available;

    /**
     * 神券类型，1=付费神券，2=免费神券
     */
    @MobileField(key = 0x1213)
    private int paidCoupon;

    /**
     * 1=去膨胀，2=去使用，固定传1
     */
    @MobileField(key = 0xdfea)
    private int couponButtonType;

    /**
     * 固定传"免费膨胀"
     */
    @MobileField(key = 0x95fd)
    private String couponButtonText;

    /**
     * 券张数，大于1即为多张券，付费神券根据券批次id和券包id聚合，免费神券都平铺展示（传1）
     */
    @MobileField(key = 0xf240)
    private int couponNum;

    /**
     * 券列表组件icon
     */
    @MobileField(key = 0x8d6f)
    private String logoIconUrl;

    /**
     * 券金额，单位为元
     */
    @MobileField(key = 0xa712)
    private String couponAmount;

    /**
     * 券名称
     */
    @MobileField(key = 0x6782)
    private String couponName;

    /**
     * "满X可用"
     */
    @MobileField(key = 0x77c8)
    private String thresholdDesc;

    /**
     * "最高膨至X"
     */
    @MobileField(key = 0xe399)
    private String couponDesc;

    /**
     * 过期时间戳（毫秒时间戳），今日到期时才会传
     */
    @MobileField(key = 0x453a)
    private String validTime;

    /**
     * 结束日期文案，如"有效期至2024.04.01""今日到期，仅剩"
     */
    @MobileField(key = 0x8f8a)
    private String couponValidTimeText;

    /**
     * 资产类型 1：到家券类型（包括到家、家店通用券）、2到店券类型、3到家通用券
     */
    @MobileField(key = 0xb82f)
    private int assetType;

    /**
     * 券膨胀前金额，单位为分，未膨胀会传
     */
    @MobileField(key = 0xea09)
    private String originalReduceAmount;

    /**
     * 券膨胀前门槛，单位为分，未膨胀会传
     */
    @MobileField(key = 0xdbd2)
    private String originalRequiredAmount;

    /**
     * 券批次id，assetType=1传到家，assetType=2传到店
     */
    @MobileField(key = 0xb991)
    private String applyId;

    /**
     * 券码，assetType=1传到家，assetType=2传到店
     */
    @MobileField(key = 0x537d)
    private String couponCode;

    /**
     * 券列表组件新icon信息
     */
    @MobileField(key = 0x71c9)
    private String iconPicInfo;
}