package com.dianping.pay.api.entity.issuecoupon;

import com.dianping.mobile.framework.annotation.MobileRequest;
import com.dianping.mobile.framework.annotation.MobileRequest.Param;
import com.dianping.mobile.framework.datatypes.IMobileRequest;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import lombok.Data;
import org.apache.commons.lang.math.NumberUtils;

/**
 * <AUTHOR>
 */
@TypeDoc(description = "IssueCouponComponentAction.bin接口请求参数")
@MobileRequest
@Data
public class IssuecouponcomponentRequest implements IMobileRequest {

    /**
     * 用户类型，0点评，1美团
     */
    @Param(name = "usertype", required = true)
    private Integer usertype;

    /**
     * 用户token
     */
    @Param(name = "token")
    private String token;

    /**
     * 稀疏化shopUuid
     */
    @Param(name = "shopuuid")
    private String shopuuid;

    /**
     * 商户id
     */
    @Param(name = "shopid")
    @Deprecated
    private Integer shopid;

    /**
     * 商户idLong
     */
    @Param(name = "shopidstring")
    private String shopidstring;

    /**
     * dpid
     */
    @Param(name = "dpid")
    private String dpid;

    /**
     * 诚信字段
     */
    @Param(name = "cx")
    private String cx;

    /**
     * 经度
     */
    @MobileRequest.Param(name = "longitude")
    private Double longitude;

    /**
     * 纬度
     */
    @MobileRequest.Param(name = "latitude")
    private Double latitude;

    /**
     * 城市id
     */
    @MobileRequest.Param(name = "cityid")
    private Integer cityid;

    /**
     * 渠道来源
     * @PageSourceEnum
     */
    @MobileRequest.Param(name = "pagesource")
    private String pagesource;

    /**
     * 投放券请求，1会请求投放券
     */
    @MobileRequest.Param(name = "needshopresourcespromotion")
    private Integer needshopresourcespromotion;

    @MobileRequest.Param(name = "needtopadresourcepromotion")
    private Integer needtopadresourcepromotion;

    /**
     * 是否需要神会员券，1查，0&其他不查
     */
    @MobileRequest.Param(name = "needmagicalmembercoupon")
    private Integer needmagicalmembercoupon;

    /**
     * 是否要分享券，休娱不要，传0
     */
    @MobileRequest.Param(name = "needsharecouponpromotion")
    private Integer needsharecouponpromotion = 1;

    /**
     * 是否要商家营销账户券,null或0表示不要，1表示需要
     * 这里加一个是为了防止团购其他类目不需要权益券的来查询导致流量增高
     * 医美预付POI详情页也需要查权益券但这里是传的false，我们通过pageSource来判断
     */
    @MobileRequest.Param(name = "needmerchantaccountcoupon")
    private Integer needmerchantaccountcoupon;

    /**
     * @PageSourceEnum
     * 页面来源，pagesource是透传给活动的不能用
     */
    @MobileRequest.Param(name = "couponpagesource")
    private String couponPageSource;

    /**
     * 实际定位城市ID
     */
    @Param(name = "actualcityid")
    private Integer actualcityid;

    /**
     * 1ios 2android
     */
    @Param(name = "cplatform")
    private Integer cplatform;

    /**
     * 指纹参数
     */
    @Param(name = "mtfingerprint")
    private String mtfingerprint;

    /**
     * 设备id
     */
    @Param(name = "uuid")
    private String uuid;

    /**
     * poi新老页面标识，1: 新页面（当前仅用于打点判断）
     */
    @Param(name = "poipagesource")
    private Integer poiPageSource;

    /**
     * 标识小程序请求，1: 小程序（当前仅用于打点判断）
     */
    @Param(name = "miniprogramflag")
    private Integer miniProgramFlag;


    @FieldDoc(
            description = "shopidstring加密值"
    )
    @MobileRequest.Param(name = "shopidstringencrypt")
    private String shopidstringencrypt;

    @FieldDoc(
            description = "shopUuid加密文"
    )
    @MobileRequest.Param(name = "shopuuidencrypt")
    private String shopuuidencrypt;

    /**
     * 0: GCJ-02，缺省值为 GCJ-02
     * 1: WGS84, 如果是 WGS84，需要显示指定为 1
     * @see com.dianping.pay.api.enums.GPSCoordEnum
     */
    @Param(name = "gpscoord")
    private Integer gpsCoord;

    /**
     * appid
     */
    @Param(name = "appid")
    private String appid;

    /**
     * 点位参数
     */
    @Param(name = "cposition")
    private Integer cposition;

    /**
     * 客户端版本号，小程序传主包版本号
     */
    @Param(name = "appversion")
    private String appversion;

    /**
     * 流量来源，目前线下扫码使用 @see TrafficSourceEnum
     */
    @Param(name = "trafficsource")
    private String trafficsource;


    /**
     * 神券是否可膨，透传给优惠台
     */
    @Param(name = "mmcinflate")
    private String mmcinflate;

    /**
     * 神券是否可用，透传给优惠台
     */
    @Param(name = "mmcuse")
    private String mmcuse;

    /**
     * 券包是否可买，透传给优惠台
     */
    @Param(name = "mmcbuy")
    private String mmcbuy;

    /**
     * 神券是否可领塞，透传给优惠台
     */
    @Param(name = "mmcfree")
    private String mmcfree;

    /**
     * 神券组件版本
     * @return
     */
    @Param(name = "magicmembercomponentversion")
    private String magicMemberComponentVersion;

    public Long getShopIdL() {
        long shopIdL = NumberUtils.toLong(shopidstring);
        if(shopIdL > 0) {
            return shopIdL;
        }
        if(shopid != null) {
            return shopid.longValue();
        }
        return null;
    }

    public void setShopidL(Long shopidL) {
        this.shopidstring = String.valueOf(shopidL);
    }

}
