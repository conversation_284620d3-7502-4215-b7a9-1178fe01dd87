package com.dianping.pay.api.entity.issuecoupon;

import com.dianping.pay.promo.common.enums.User;
import lombok.Data;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2021/1/22
 */
@Data
public class ShopCartIssueMultiCouponContext {

    private String cx;

    private String dpid;

    private long dpUserId;

    private long mtUserId;

    private int payPlatform;

    //0:点评 1:美团
    private User userType;

    private List<Integer> skuIds;

    private List<Integer> dpDealGroupIds;

    private List<Integer> mtDealGroupIds;

    private List<String> couponGroupIds;

    private Integer issueDetailSourceCode;

    private Map<Integer, Integer> mtDpDealGroupMap;

    public boolean isMt() {
        return userType == User.MT;
    }


    public long getUserId() {
        return isMt() ? mtUserId : dpUserId;
    }

}
