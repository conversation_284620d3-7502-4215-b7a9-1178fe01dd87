package com.dianping.pay.api.entity.promodesk;

import com.dianping.api.common.util.KeyValuePair;
import com.dianping.api.domain.ClientInfo;
import com.dianping.api.domain.Version;
import com.dianping.api.netio.DPEncoder;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.LinkedList;
import java.util.List;

public class PromoDeskCoupon extends DPEncoder implements Serializable, Comparable<PromoDeskCoupon> {

    private int id;
    private String newId;
    private String title;
    private String desc;
    private String expireDate;
    private BigDecimal priceValue;
    private double orderPriceLimit;
    private int productCode;
    private String promoCipher;
    private boolean canUse;
    private boolean calculateNoDiscountAmount;

    private int couponType; // 1-优惠代码 2-抵用券

    private static final LinkedList<KeyValuePair> l = new LinkedList<KeyValuePair>();
    static {
        l.add(new KeyValuePair("id", 0x91b));
        l.add(new KeyValuePair("title", 0x36e9));
        l.add(new KeyValuePair("desc", 0x7291));
        l.add(new KeyValuePair("expireDate", 0xd084));
        l.add(new KeyValuePair("price", 0xc5b5));
        l.add(new KeyValuePair("orderPriceLimit", 0x11e0));
        l.add(new KeyValuePair("productCode", 0x8d25));
        l.add(new KeyValuePair("promoCipher", 0xc706));
        l.add(new KeyValuePair("canUse", 0x9069));
        l.add(new KeyValuePair("calculateNoDiscountAmount", 0x4c08));
    }

    @Override
    public int getClassId() {
        return 0xc913;
    }

    @Override
    public List<KeyValuePair> getSerializeList(ClientInfo clientInfo, Version version) {
        return l;
    }

    @Override
    public boolean equals(Object o) {
        if (o == null) {
            return false;
        }
        if (!(o instanceof PromoDeskCoupon)) {
            return false;
        }
        return this.id == ((PromoDeskCoupon) o).id;
    }

    @Override
    public int hashCode() {
        return id;
    }

    @Override
    public int compareTo(PromoDeskCoupon other) {
        if (other == null) {
            return 1;
        }
        if (!other.canUse) {
            return 1;
        }
        return priceValue.compareTo(other.priceValue);
    }

    public int getId() {
        return id;
    }

    public void setId(int id) {
        this.id = id;
    }

    public String getNewId() {
        return newId;
    }

    public void setNewId(String newId) {
        this.newId = newId;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }

    public String getExpireDate() {
        return expireDate;
    }

    public void setExpireDate(String expireDate) {
        this.expireDate = expireDate;
    }

    public String getPrice() {
        return priceValue != null ? priceValue.stripTrailingZeros().toPlainString() : "0";
    }

    public void setPrice(String price) {
        try {
            priceValue = new BigDecimal(price);
        } catch (Exception e) { }
    }

    public BigDecimal getPriceValue() {
        return priceValue;
    }

    public void setPriceValue(BigDecimal priceValue) {
        this.priceValue = priceValue;
    }

    public double getOrderPriceLimit() {
        return orderPriceLimit;
    }

    public void setOrderPriceLimit(double orderPriceLimit) {
        this.orderPriceLimit = orderPriceLimit;
    }

    public int getProductCode() {
        return productCode;
    }

    public void setProductCode(int productCode) {
        this.productCode = productCode;
    }

    public String getPromoCipher() {
        return promoCipher;
    }

    public void setPromoCipher(String promoCipher) {
        this.promoCipher = promoCipher;
    }

    public boolean isCanUse() {
        return canUse;
    }

    public void setCanUse(boolean canUse) {
        this.canUse = canUse;
    }

    public int getCouponType() {
        return couponType;
    }

    public void setCouponType(int couponType) {
        this.couponType = couponType;
    }

    public boolean isCalculateNoDiscountAmount() {
        return calculateNoDiscountAmount;
    }

    public void setCalculateNoDiscountAmount(boolean calculateNoDiscountAmount) {
        this.calculateNoDiscountAmount = calculateNoDiscountAmount;
    }
}
