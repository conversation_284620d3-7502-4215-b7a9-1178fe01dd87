package com.dianping.pay.api.entity.issuecoupon;

import com.dianping.mobile.framework.annotation.MobileRequest;
import com.dianping.mobile.framework.datatypes.IMobileRequest;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2020/12/9
 */
@MobileRequest
@Data
public class ExternalLinkIssueCouponRequest implements IMobileRequest {

    @FieldDoc(
            description = "外链参数",
            requiredness = Requiredness.REQUIRED,
            rule = ""
    )
    @MobileRequest.Param(name = "couponexternallink", required = true)
    private String couponExternalLink;

    /**
     * 用户类型，0点评，1美团
     */
    @MobileRequest.Param(name = "usercode", required = true)
    private Integer usercode;

    /**
     * 用户token
     */
    @MobileRequest.Param(name = "token")
    private String token;

    /**
     * dpid
     */
    @MobileRequest.Param(name = "dpid")
    private String dpid;

    /**
     * 诚信字段
     */
    @MobileRequest.Param(name = "cx")
    private String cx;

    @FieldDoc(
            description = "发券渠道 {@link com.dianping.gmkt.coupon.common.api.enums.IssueDetailSourceEnum}"
    )
    @MobileRequest.Param(name = "issuedetailsourcecode")
    private Integer issueDetailSourceCode;

    @MobileRequest.Param(name = "deviceplatform")
    private Integer devicePlatform;

}
