package com.dianping.pay.api.entity.issuecoupon;

import com.dianping.api.common.util.KeyValuePair;
import com.dianping.api.domain.ClientInfo;
import com.dianping.api.domain.Version;
import com.dianping.api.netio.DPEncoder;
import com.dianping.mobile.framework.annotation.MobileDo;

import java.io.Serializable;
import java.util.LinkedList;
import java.util.List;

@MobileDo(id =0x4667)
public class BeautyIssueCouponOption extends DPEncoder implements Serializable {

    @MobileDo.MobileField(key = 0x91b)
    private int id;

    @MobileDo.MobileField(key = 0x4967)
    private double amount;

    @MobileDo.MobileField(key = 0x477b)
    private String tag;

    @MobileDo.MobileField(key = 0x36e9)
    private String title;

    @MobileDo.MobileField(key = 0x7291)
    private String desc;

    @MobileDo.MobileField(key = 0x6a59)
    private boolean enable;

    @MobileDo.MobileField(key = 0x286a)
    private boolean incompatible;

    @MobileDo.MobileField(key = 0x3d09)
    private String incompatibleRule;

    @MobileDo.MobileField(key = 0x5ff5)
    private boolean used;

    @MobileDo.MobileField(key = 0xe4cf)
    private int couponGroupId;

    private static final LinkedList<KeyValuePair> l = new LinkedList<KeyValuePair>();
    static {
        l.add(new KeyValuePair("id", 0x91b));
        l.add(new KeyValuePair("amount", 0x4967));
        l.add(new KeyValuePair("tag", 0x477b));
        l.add(new KeyValuePair("title", 0x36e9));
        l.add(new KeyValuePair("desc", 0x7291));
        l.add(new KeyValuePair("enable", 0x6a59));
        l.add(new KeyValuePair("incompatible", 0x286a));
        l.add(new KeyValuePair("incompatibleRule", 0x3d09));
        l.add(new KeyValuePair("used", 0x5ff5));
        l.add(new KeyValuePair("couponGroupId", 0xe4cf));
    }

    @Override
    public int getClassId() {
        return 0x4667;
    }

    @Override
    public List<KeyValuePair> getSerializeList(ClientInfo clientInfo, Version version) {
        return l;
    }

    public int getId() {
        return id;
    }

    public void setId(int id) {
        this.id = id;
    }

    public double getAmount() {
        return amount;
    }

    public void setAmount(double amount) {
        this.amount = amount;
    }

    public String getTag() {
        return tag;
    }

    public void setTag(String tag) {
        this.tag = tag;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }

    public boolean isEnable() {
        return enable;
    }

    public void setEnable(boolean enable) {
        this.enable = enable;
    }

    public boolean isIncompatible() {
        return incompatible;
    }

    public void setIncompatible(boolean incompatible) {
        this.incompatible = incompatible;
    }

    public String getIncompatibleRule() {
        return incompatibleRule;
    }

    public void setIncompatibleRule(String incompatibleRule) {
        this.incompatibleRule = incompatibleRule;
    }

    public boolean isUsed() {
        return used;
    }

    public void setUsed(boolean used) {
        this.used = used;
    }

    public int getCouponGroupId() {
        return couponGroupId;
    }

    public void setCouponGroupId(int couponGroupId) {
        this.couponGroupId = couponGroupId;
    }

    @Override
    public String toString() {
        return "BeautyIssueCouponOption{" +
                "id=" + id +
                ", amount=" + amount +
                ", tag='" + tag + '\'' +
                ", title='" + title + '\'' +
                ", desc='" + desc + '\'' +
                ", enable=" + enable +
                ", incompatible=" + incompatible +
                ", incompatibleRule='" + incompatibleRule + '\'' +
                ", used=" + used +
                ", couponGroupId=" + couponGroupId +
                '}';
    }
}
