package com.dianping.pay.api.entity;

import java.util.Date;

/**
 * Created by huawei.li on 14/12/15.
 */
public class DealGroupSelect {
    private int dealGroupId;
    private String dealGroupShortTitle;
    private String dealGroupTitleDesc;
    private Date beginDate;
    private Date endDate;
    private int maxPerUser;
    private int minPerUser;
    private int status;
    private int publishStatus;
    private int autoRefundSwitch;
    private int saleChannel;
    private int dealGroupTypeId;
    private int payChannelIdAllowed;
    private int discountRuleId;
    private boolean blockStock;
    private boolean thirdPartVerify;
    private boolean canUseCoupon;

    public int getDealGroupId() {
        return dealGroupId;
    }

    public void setDealGroupId(int dealGroupId) {
        this.dealGroupId = dealGroupId;
    }

    public String getDealGroupShortTitle() {
        return dealGroupShortTitle;
    }

    public void setDealGroupShortTitle(String dealGroupShortTitle) {
        this.dealGroupShortTitle = dealGroupShortTitle;
    }

    public String getDealGroupTitleDesc() {
        return dealGroupTitleDesc;
    }

    public void setDealGroupTitleDesc(String dealGroupTitleDesc) {
        this.dealGroupTitleDesc = dealGroupTitleDesc;
    }

    public Date getBeginDate() {
        return beginDate;
    }

    public void setBeginDate(Date beginDate) {
        this.beginDate = beginDate;
    }

    public Date getEndDate() {
        return endDate;
    }

    public void setEndDate(Date endDate) {
        this.endDate = endDate;
    }

    public int getMaxPerUser() {
        return maxPerUser;
    }

    public void setMaxPerUser(int maxPerUser) {
        this.maxPerUser = maxPerUser;
    }

    public int getMinPerUser() {
        return minPerUser;
    }

    public void setMinPerUser(int minPerUser) {
        this.minPerUser = minPerUser;
    }

    public int getStatus() {
        return status;
    }

    public void setStatus(int status) {
        this.status = status;
    }

    public int getPublishStatus() {
        return publishStatus;
    }

    public void setPublishStatus(int publishStatus) {
        this.publishStatus = publishStatus;
    }

    public int getAutoRefundSwitch() {
        return autoRefundSwitch;
    }

    public void setAutoRefundSwitch(int autoRefundSwitch) {
        this.autoRefundSwitch = autoRefundSwitch;
    }

    public int getSaleChannel() {
        return saleChannel;
    }

    public void setSaleChannel(int saleChannel) {
        this.saleChannel = saleChannel;
    }

    public int getDealGroupTypeId() {
        return dealGroupTypeId;
    }

    public void setDealGroupTypeId(int dealGroupTypeId) {
        this.dealGroupTypeId = dealGroupTypeId;
    }

    public int getPayChannelIdAllowed() {
        return payChannelIdAllowed;
    }

    public void setPayChannelIdAllowed(int payChannelIdAllowed) {
        this.payChannelIdAllowed = payChannelIdAllowed;
    }

    public int getDiscountRuleId() {
        return discountRuleId;
    }

    public void setDiscountRuleId(int discountRuleId) {
        this.discountRuleId = discountRuleId;
    }

    public boolean isBlockStock() {
        return blockStock;
    }

    public void setBlockStock(boolean blockStock) {
        this.blockStock = blockStock;
    }

    public boolean isThirdPartVerify() {
        return thirdPartVerify;
    }

    public void setThirdPartVerify(boolean thirdPartVerify) {
        this.thirdPartVerify = thirdPartVerify;
    }

    public boolean isCanUseCoupon() {
        return canUseCoupon;
    }

    public void setCanUseCoupon(boolean canUseCoupon) {
        this.canUseCoupon = canUseCoupon;
    }
}
