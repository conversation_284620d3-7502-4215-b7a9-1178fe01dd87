package com.dianping.pay.api.entity.issuecoupon;

import com.dianping.mobile.framework.annotation.MobileDo;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * 模型描述：券批次详细信息
 * 移动之家链接: https://mobile.sankuai.com/studio/model/info/24446
 */
@MobileDo(id = 0x3b6e)
@Data
public class IssueCouponDetail implements Serializable {


    /**
     * 券活动附属信息
     */
    @MobileDo.MobileField(key = 0xcd4d)
    private CouponActivityDetail activityInfo;

    /**
     * 是否已经领取，0未领取，1已领取
     */
    @MobileDo.MobileField(key = 0xc851)
    private IssueCouponStatus issueStatus;

    /**
     * 角标文案 （eg：限新客专享）
     */
    @MobileDo.MobileField(key = 0x192e)
    private String script;

    /**
     * 是否限制新客，false不限制，true限制新客
     */
    @MobileDo.MobileField(key = 0x1be2)
    private boolean userTypeLimit;

    /**
     * 券类型，0通用券，1品类券，3活动券，6top投放券
     */
    @MobileDo.MobileField(key = 0xb925)
    private int couponProductType;

    /**
     * 券时间描述 （eg：领取后***天有效）
     */
    @MobileDo.MobileField(key = 0x12d8)
    private String timeDesc;

    /**
     * 券金额描述 （eg：满***元可用）
     */
    @MobileDo.MobileField(key = 0x7c03)
    private String amountDesc;

    /**
     * 券标题
     */
    @MobileDo.MobileField(key = 0x24cc)
    private String title;

    /**
     * 品类限制描述
     */
    @MobileDo.MobileField(key = 0x99cf)
    private String categoryDesc;

    /**
     * 券门槛
     */
    @MobileDo.MobileField(key = 0xa84b)
    private double priceLimit;

    /**
     * 券金额
     */
    @MobileDo.MobileField(key = 0xfbe2)
    private double amount;

    /**
     * 券折扣，折扣券专用, eg：8折：8；85折：85
     */
    @MobileDo.MobileField(key = 0x6509)
    private double discount;

    /**
     * 券面额类型，0表示满减券，1表示折扣券。用该字段进行尾标渲染
     */
    @MobileDo.MobileField(key = 0x59e0)
    private int couponValueType;
    /**
     * 券批次id
     */
    @MobileDo.MobileField(key = 0x9256)
    private String unifiedCouponGroupId;

    /**
     * 券简易描述(适用业务+时间限制描述)
     */
    @MobileDo.MobileField(key = 0xab5)
    private String bizAndTimeLimitDesc;

    /**
     * 综商家券使用时间限制描述，目前只有密室使用（eg：限周一、周三、周五使用）
     */
    @MobileDo.MobileField(key = 0xa212)
    private String dzTimeLimitRule;

    @MobileDo.MobileField(key = 0x31b)
    private long beginTime;

    @MobileDo.MobileField(key = 0xca7b)
    private long endTime;

    /**
     * 投放资源角标链接
     */
    @MobileDo.MobileField(key = 0xadd1)
    private String cornerMarkUrl;
}
