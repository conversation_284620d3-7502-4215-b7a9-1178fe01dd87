package com.dianping.pay.api.entity;

import com.dianping.api.common.util.KeyValuePair;
import com.dianping.api.domain.AlertMsg;
import com.dianping.api.domain.ClientInfo;
import com.dianping.api.domain.Version;
import com.dianping.api.netio.DPEncoder;
import com.dianping.cat.Cat;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * User: huawei.li
 * Date: 14-6-2
 * Time: 下午3:37
 */
public class SubmitOrderResult extends DPEncoder implements Serializable {
    private static final long serialVersionUID = 8113925252845284236L;
    private int orderId;
    private int status;
    private String errorCode;
    private String errorMsg;
    @Override
    public int getClassId() {
        Cat.logEvent("INVALID_METHOD_5", "com.dianping.pay.api.entity.SubmitOrderResult.getClassId()");
        return 0xd3c9;
    }
    private static final List<KeyValuePair> SUBMITLIST =new ArrayList<KeyValuePair>();

    static{
        SUBMITLIST.add(new KeyValuePair("orderId", 0x97ac));
        SUBMITLIST.add(new KeyValuePair("status", 0x2820));
        SUBMITLIST.add(new KeyValuePair("errorCode", 0x44fb));
        SUBMITLIST.add(new KeyValuePair("errorMsg", 0x7368));
    }

    @Override
    public List<KeyValuePair> getSerializeList(ClientInfo clientInfo, Version version) {
        Cat.logEvent("INVALID_METHOD_1", "com.dianping.pay.api.entity.SubmitOrderResult.getSerializeList(com.dianping.api.domain.ClientInfo,com.dianping.api.domain.Version)");
        return SUBMITLIST;
    }

    public int getOrderId() {
        return orderId;
    }

    public void setOrderId(int orderId) {
        this.orderId = orderId;
    }

    public int getStatus() {
        return status;
    }

    public void setStatus(int status) {
        this.status = status;
    }

    public String getErrorCode() {
        return errorCode;
    }

    public void setErrorCode(String errorCode) {
        this.errorCode = errorCode;
    }

    public String getErrorMsg() {
        return errorMsg;
    }

    public void setErrorMsg(String errorMsg) {
        this.errorMsg = errorMsg;
    }

}
