package com.dianping.pay.api.entity.issuecoupon;

import com.dianping.mobile.framework.annotation.MobileRequest;
import com.dianping.mobile.framework.datatypes.IMobileRequest;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import lombok.Data;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang.math.NumberUtils;

@TypeDoc(
        description = "用户领券参数"
)
@MobileRequest
@Data
public class UnifiedissuecouponRequest implements IMobileRequest {

    /**
     * 纬度
     */
    @MobileRequest.Param(name = "latitude")
    private Double latitude;

    /**
     * 经度
     */
    @MobileRequest.Param(name = "longitude")
    private Double longitude;

    @MobileRequest.Param(name = "cityid")
    @FieldDoc(
            description = "城市id"
    )
    private Integer cityid;

    @FieldDoc(
            description = "团单id，已废弃"
    )
    @MobileRequest.Param(name = "productid")
    @Deprecated
    private Integer productid;

    @FieldDoc(
            description = "团单id，长整型"
    )
    @MobileRequest.Param(name = "longproductid")
    private Long longProductId;

    @FieldDoc(
            description = "门店id"
    )
    @MobileRequest.Param(name = "shopid")
    private String shopid;

    @FieldDoc(
            description = "门店id"
    )
    @MobileRequest.Param(name = "shopidstring")
    private String shopidstring;

    @FieldDoc(
            description = "门店id"
    )
    @MobileRequest.Param(name = "shopuuid")
    private String shopuuid;

    @FieldDoc(
            description = "诚信字段"
    )
    @MobileRequest.Param(name = "cx")
    private String cx;

    @FieldDoc(
            description = "券批次ID"
    )
    @Deprecated
    @MobileRequest.Param(name = "coupongroupid")
    private Integer coupongroupid;

    @FieldDoc(
            description = "产品类型，用来区分productid字段是团购id还是商品id"
    )
    @MobileRequest.Param(name = "producttype")
    private Integer producttype;

    @FieldDoc(
            description = "商家券：券批次ID；平台券：券码ID"
    )
    @MobileRequest.Param(name = "unifiedcoupongroupid")
    private String unifiedCouponGroupId;

    @FieldDoc(
            description = "是否需要查询平台券"
    )
    @MobileRequest.Param(name = "queryplatformcoupon")
    private Boolean queryPlatformCoupon;

    @FieldDoc(
            description = "发券渠道 {@link com.dianping.gmkt.wave.api.enums.ActivityChannelEnum}"
    )
    @MobileRequest.Param(name = "issuechannel")
    private Integer issueChannel;

    @FieldDoc(
            description = "发券渠道 {@link com.dianping.gmkt.coupon.common.api.enums.IssueDetailSourceEnum}"
    )
    @MobileRequest.Param(name = "issuedetailsourcecode")
    private Integer issueDetailSourceCode;

    /**
     * 投放活动券发券专用-活动id
     */
    @MobileRequest.Param(name = "activityid")
    private Long activityid;

    /**
     * 投放活动券发券专用-物料id
     */
    @MobileRequest.Param(name = "materialid")
    private String materialid;

    /**
     * 投放活动券发券专用-投放活动id，如果发放的是投放券的话传递这个参数
     */
    @MobileRequest.Param(name = "resourceactivityid")
    private Long resourceactivityid;

    /**
     * 投放活动券发券专用-分流位id，投放的是活动投放券时传这个id
     */
    @MobileRequest.Param(name = "flowid")
    private Long flowid;

    /**
     * 投放活动券发券专用-rowkey
     */
    @MobileRequest.Param(name = "rowkey")
    private String rowkey;

    /**
     * 券类型，0是抵用券，1是投放活动券
     */
    @MobileRequest.Param(name = "coupontype")
    private Integer coupontype;

    @MobileRequest.Param(name = "uuid")
    private String uuid;

    @MobileRequest.Param(name = "serialno")
    private String serialno;

    /**
     * @PageSourceEnum
     * 页面来源，pagesource是透传给活动的不能用
     */
    @MobileRequest.Param(name = "couponpagesource")
    private String couponPageSource;

    /**
     * 平台标识：1-IOS；2-Android
     */
    @MobileRequest.Param(name = "cplatform")
    private Integer cplatform;

    /**
     * 指纹参数
     */
    @MobileRequest.Param(name = "mtfingerprint")
    private String mtfingerprint;

    /**
     * 实际定位城市ID
     */
    @MobileRequest.Param(name = "actualcityid")
    private Integer actualcityid;

    @FieldDoc(
            description = "券包密钥，政府N选1券必填"
    )
    @MobileRequest.Param(name = "packagesecretkey")
    private String packagesecretkey;

    @FieldDoc(
            description = "shopUuid加密文"
    )
    @MobileRequest.Param(name = "shopuuidencrypt")
    private String shopuuidencrypt;

    @FieldDoc(
            description = "shopIdString加密值"
    )
    @MobileRequest.Param(name = "shopidstringencrypt")
    private String shopidstringencrypt;

    public Integer getProductid() {
        return productid;
    }

    public void setProductid(Integer productid) {
        this.productid = productid;
    }
    
    public Long getLongProductId() {
        return longProductId;
    }

    public void setLongProductId(Long longProductId) {
        this.longProductId = longProductId;
    }

    public String getShopidStr() {
        return StringUtils.isNotBlank(shopidstring) ? shopidstring : shopid;
    }

    @Deprecated
    public Integer getShopid() {
        return NumberUtils.toInt(shopid);
    }

    @Deprecated
    public void setShopid(Integer shopid) {
        this.shopid = String.valueOf(shopid);
    }

    public Long getShopidL() {
        long shopIdL = NumberUtils.toLong(shopidstring);
        return shopIdL > 0 ? shopIdL : NumberUtils.toLong(shopid);
    }

    public void setShopidL(Long shopid) {
        this.shopid = String.valueOf(shopid);
        this.shopidstring = String.valueOf(shopid);
    }

    public String getCx() {
        return cx;
    }

    public void setCx(String cx) {
        this.cx = cx;
    }

    public Integer getCoupongroupid() {
        return coupongroupid;
    }

    public void setCoupongroupid(Integer coupongroupid) {
        this.coupongroupid = coupongroupid;
    }

    public Integer getProducttype() {
        return producttype;
    }

    public void setProducttype(Integer producttype) {
        this.producttype = producttype;
    }

    public Integer getIssueChannel() {
        return issueChannel;
    }

    public void setIssueChannel(Integer issueChannel) {
        this.issueChannel = issueChannel;
    }

    public String getUnifiedCouponGroupId() {
        return unifiedCouponGroupId;
    }

    public void setUnifiedCouponGroupId(String unifiedCouponGroupId) {
        this.unifiedCouponGroupId = unifiedCouponGroupId;
    }

    public Boolean getQueryPlatformCoupon() {
        return queryPlatformCoupon;
    }

    public void setQueryPlatformCoupon(Boolean queryPlatformCoupon) {
        this.queryPlatformCoupon = queryPlatformCoupon;
    }

    public String getShopuuid() {
        return shopuuid;
    }

    public void setShopuuid(String shopuuid) {
        this.shopuuid = shopuuid;
    }

    public Integer getIssueDetailSourceCode() {
        return issueDetailSourceCode;
    }

    public void setIssueDetailSourceCode(Integer issueDetailSourceCode) {
        this.issueDetailSourceCode = issueDetailSourceCode;
    }

    public String getSerialno() {
        return serialno;
    }

    public void setSerialno(String serialno) {
        this.serialno = serialno;
    }


}