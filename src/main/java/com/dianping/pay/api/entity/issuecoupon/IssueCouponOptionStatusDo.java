package com.dianping.pay.api.entity.issuecoupon;

import com.dianping.mobile.framework.annotation.MobileDo;
import com.dianping.pay.api.enums.IssueCouponComponentStatus;

import java.io.Serializable;

/**
 * Created by drin<PERSON> on 18/6/6.
 */

@MobileDo(id = 0x4aa8)
public class IssueCouponOptionStatusDo implements Serializable {
    /**
     *
     */
    @MobileDo.MobileField(key = 0x2820)
    private int status;

    /**
     *
     */
    @MobileDo.MobileField(key = 0x1de1)
    private String comment;

    public int getStatus() {
        return status;
    }

    public void setStatus(int status) {
        this.status = status;
    }

    public String getComment() {
        return comment;
    }

    public void setComment(String comment) {
        this.comment = comment;
    }

    public IssueCouponOptionStatusDo(int status, String comment) {
        this.status = status;
        this.comment = comment;
    }

    public IssueCouponOptionStatusDo(IssueCouponComponentStatus statusEnum) {
        this.status = statusEnum.getStatus();
        this.comment = statusEnum.getComment();
    }

    @Override
    public String toString() {
        final StringBuilder sb = new StringBuilder("IssueCouponOptionStatusDo{");
        sb.append("status=").append(status);
        sb.append(", comment='").append(comment).append('\'');
        sb.append('}');
        return sb.toString();
    }
}
