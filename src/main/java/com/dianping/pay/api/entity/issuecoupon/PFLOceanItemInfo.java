package com.dianping.pay.api.entity.issuecoupon;

import com.dianping.mobile.framework.annotation.MobileDo;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * 模型描述：到综统一接口中Ocean打点后端返回模型Item
 * 移动之家链接: https://mobile.sankuai.com/studio/model/info/24005
 */
@MobileDo(id = 0xbf00)
@Data
public class PFLOceanItemInfo implements Serializable {
    /**
     *
     */
    @MobileDo.MobileField(key = 0x90b4)
    private String labs;

    /**
     * 斗斛abtest上报的内容，例如：[\"{\\\"query_id\\\":\\\"ab0ac22d-c8ad-4488-99d6-9fd054c4817d\\\",\\\"ab_id\\\":\\\"exp000089_a\\\"}\"]
     */
    @MobileDo.MobileField(key = 0xf312)
    private String abtest;

    /**
     * 灵犀埋点中的category字段
     */
    @MobileDo.MobileField(key = 0xbffc)
    private String category;

    /**
     * 曝光bid
     */
    @MobileDo.MobileField(key = 0x9dbb)
    private String bidView;

    /**
     * 点击bid
     */
    @MobileDo.MobileField(key = 0xcba3)
    private String bidClick;
}
