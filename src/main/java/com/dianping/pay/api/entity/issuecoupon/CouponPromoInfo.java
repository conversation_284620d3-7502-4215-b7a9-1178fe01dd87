package com.dianping.pay.api.entity.issuecoupon;

import com.dianping.mobile.framework.annotation.MobileDo;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@MobileDo(id = 0xed12)
@Data
public class CouponPromoInfo implements Serializable {
    /**
     * 优惠类型。 1是立减，2是拼团，3是通用组件中的抵用券，4是反礼，5是红包分享, 6是闲时优惠, 7是新客投放券，8是丽人新样式聚合信息（聚合）
     */
    @MobileDo.MobileField(key = 0x8f0c)
    private int type;

    /**
     * 0：文案型取promoInfoDescItems，1：组件型取promoInfoDetailItems，2：组建类型取promoExposureInfoItem
     */
    @MobileDo.MobileField(key = 0x1b3a)
    private int style;

    /**
     * 投放资源券
     */
    @MobileDo.MobileField(key = 0xc62b)
    private PromoExposureInfoItem promoExposureInfoItem;

    /**
     *
     */
    @MobileDo.MobileField(key = 0x22ce)
    private List<PromoDetailInfoItem> promoInfoDetailItems;

    /**
     * 优惠信息描述项列表
     */
    @MobileDo.MobileField(key = 0x2b40)
    private List<PromoSimpleInfoItem> promoInfoDescItems;

    /**
     * 跳转链接
     */
    @MobileDo.MobileField(key = 0x8283)
    private String redirectUrl;

    /**
     * 小图标url
     */
    @MobileDo.MobileField(key = 0xf39b)
    private String iconUrl;

    /**
     * 引导跳转链接，例如券的去使用链接
     */
    @MobileDo.MobileField(key = 0x2373)
    private String leadUrl;

    /**
     * 引导文案，e.g. 去领券/查看优惠/更多优惠
     */
    @MobileDo.MobileField(key = 0x17aa)
    private String leadText;

    /**
     * 引导动作，0：无引导 1：弹出浮层 2：跳转leadUrl 3: 弹出闲时单浮层
     */
    @MobileDo.MobileField(key = 0x1c63)
    private int leadAction;

    /**
     * 优惠标题，立减/抵用券/反礼
     */
    @MobileDo.MobileField(key = 0x83f6)
    private String promoTitle;
}
