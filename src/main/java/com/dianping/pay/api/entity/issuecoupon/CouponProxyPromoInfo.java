package com.dianping.pay.api.entity.issuecoupon;

import com.dianping.mobile.framework.annotation.MobileDo;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@MobileDo(id = 0x5261)
@Data
public class CouponProxyPromoInfo implements Serializable {
    /**
     * 平台券信息
     */
    @MobileDo.MobileField(key = 0x174f)
    private List<CouponDetailInfo> platformCoupons;

    /**
     * 商家券信息
     */
    @MobileDo.MobileField(key = 0x1550)
    private List<CouponDetailInfo> merchantCoupons;

    /**
     * 政府消费券
     * */
    @MobileDo.MobileField(key = 0x241d)
    private List<CouponDetailInfo> govConsumeCoupons;


}
