package com.dianping.pay.api.entity.issuecoupon;

import com.dianping.mobile.framework.annotation.MobileRequest;
import com.dianping.mobile.framework.datatypes.IMobileRequest;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2021/1/21
 */
@TypeDoc(description = "shopcartissuemulticouponaction.bin接口请求参数")
@MobileRequest
@Data
public class ShopCartIssueMultiCouponRequest implements IMobileRequest {

    @FieldDoc(
            description = "商品列表"
    )
    @MobileRequest.Param(name = "shopcartproducts")
    private String shopcartproducts;

    @FieldDoc(
            description = "商家券：券批次ID；平台券：券码ID"
    )
    @MobileRequest.Param(name = "unifiedcoupongroupids")
    private String unifiedcoupongroupids;

    @FieldDoc(
            description = "发券渠道 {@link com.dianping.gmkt.coupon.common.api.enums.IssueDetailSourceEnum}"
    )
    @MobileRequest.Param(name = "issuedetailsourcecode")
    private Integer issuedetailsourcecode;

    /**
     * 用户token
     */
    @MobileRequest.Param(name = "token")
    private String token;

    /**
     * 用户类型，0点评，1美团
     */
    @MobileRequest.Param(name = "usertype")
    private Integer usercode;

}
