package com.dianping.pay.api.entity;

import com.dianping.api.common.util.KeyValuePair;
import com.dianping.api.domain.ClientInfo;
import com.dianping.api.domain.ResultAction;
import com.dianping.api.domain.SuccessMsg;
import com.dianping.api.domain.Version;
import com.dianping.cat.Cat;

import java.util.Arrays;
import java.util.List;

/**
 * Created by huawei.li on 15/3/23.
 */
public class PaymentResult extends SuccessMsg {
    private static final long serialVersionUID = 177439038949262448L;
    private int resultCode;
    private List<ResultAction> actionList;

    private static final List<KeyValuePair> LIST = Arrays.asList(
            new KeyValuePair("title", 0x36e9),
            new KeyValuePair("content",0x57b6),
            new KeyValuePair("flag",0x73ad),
            new KeyValuePair("actionList",0x913b),
            new KeyValuePair("resultCode", 0x3b7e)
    );
    @Override
    public int getClassId() {
        Cat.logEvent("INVALID_METHOD_2", "com.dianping.pay.api.entity.PaymentResult.getClassId()");
        return 0x7bc6;
    }

    @Override
    public List<KeyValuePair> getSerializeList(ClientInfo clientInfo, Version version) {
        Cat.logEvent("INVALID_METHOD_3", "com.dianping.pay.api.entity.PaymentResult.getSerializeList(com.dianping.api.domain.ClientInfo,com.dianping.api.domain.Version)");
        return LIST;
    }

    public List<ResultAction> getActionList() {
        return actionList;
    }

    public void setActionList(List<ResultAction> actionList) {
        this.actionList = actionList;
    }

    public int getResultCode() {
        return resultCode;
    }

    public void setResultCode(int resultCode) {
        this.resultCode = resultCode;
    }
}
