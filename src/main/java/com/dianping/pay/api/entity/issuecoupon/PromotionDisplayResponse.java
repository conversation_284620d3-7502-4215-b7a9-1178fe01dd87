package com.dianping.pay.api.entity.issuecoupon;

import com.dianping.mobile.framework.annotation.MobileDo;
import com.dianping.mobile.framework.annotation.MobileDo.MobileField;
import lombok.Data;

import java.util.List;
import java.io.Serializable;


/**
 * <AUTHOR>
 * @date 08/04/2020
 * @time 18:27
 * 模型描述：商详页新版领券组件-展示信息
 * 移动之家链接: https://mobile.sankuai.com/studio/model/info/23272
 */
@MobileDo(id = 0x5f67)
@Data
public class PromotionDisplayResponse implements Serializable {


    /**
     * 领券弹框组件-详细展示数据
     */
    @MobileField(key = 0xf5fe)
    private PromotionDisplayDto promotionDisplayDto;

    /**
     * 优惠信息外层缩略展示文案
     */
    @MobileField(key = 0xa8a8)
    private List<PromotionOuterDisplayDto> promotionOuterDisplayDtos;

    /**
     * 入口文案  eg.去领取
     */
    @MobileField(key = 0x7802)
    private String entranceText;

    /**
     * 优惠展示图标
     */
    @MobileField(key = 0x3c48)
    private String icon;



}
