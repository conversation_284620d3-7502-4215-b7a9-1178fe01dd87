
package com.dianping.pay.api.entity;

import java.util.List;

import com.dianping.api.common.entity.Point;
import com.dianping.cat.Cat;
import com.google.common.collect.Lists;

public class DealLocation {

	private List<Point> locationList = Lists.newArrayList();

	public void setShopLocation(List<Point> locationList) {
		this.locationList = locationList;
	}

	public List<Point> getShopLocation() {
		return locationList;
	}

	public void addShopLocation(Point shopLocation) {
        Cat.logEvent("INVALID_METHOD_2", "com.dianping.pay.api.entity.DealLocation.addShopLocation(com.dianping.api.common.entity.Point)");
        this.locationList.add(shopLocation);
	}

}
