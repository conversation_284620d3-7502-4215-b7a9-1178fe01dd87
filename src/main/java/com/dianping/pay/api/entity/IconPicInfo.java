package com.dianping.pay.api.entity;

import com.dianping.mobile.framework.annotation.MobileDo;
import com.dianping.mobile.framework.annotation.MobileDo.*;
import lombok.Data;

import java.io.Serializable;

@Data
public class IconPicInfo implements Serializable {
    /**
     * icon图片链接
     */
    private String iconPic;

    /**
     * icon图片宽度
     */
    private int iconPicWidth;

    /**
     * icon图片高度
     */
    private int iconPicHeight;

}
