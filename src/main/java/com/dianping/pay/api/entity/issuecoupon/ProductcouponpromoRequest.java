package com.dianping.pay.api.entity.issuecoupon;

import com.dianping.mobile.framework.annotation.MobileRequest;
import com.dianping.mobile.framework.datatypes.IMobileRequest;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import lombok.Data;
import org.apache.commons.lang.math.NumberUtils;

import static com.dianping.pay.cashier.biz.enums.CouponLimitKey.shopId;

/**
 *
 */
@MobileRequest
@Data
public class ProductcouponpromoRequest implements IMobileRequest {

    /**
     * 是否需要折扣券
     */
    @MobileRequest.Param(name = "needdiscountcoupon")
    private Boolean needdiscountcoupon = false;

    /**
     * 0：团单；1：商品sku; 3：标品; 4:spu
     */
    @MobileRequest.Param(name = "producttype")
    private Integer producttype;

    /**
     * 设备号，有则传
     */
    @MobileRequest.Param(name = "dpid")
    private String dpid;

    /**
     * 用户token
     */
    @MobileRequest.Param(name = "token")
    private String token;

    @MobileRequest.Param(name = "cx")
    private String cx;

    /**
     * 用户类型，0点评，1美团
     */
    @MobileRequest.Param(name = "usertype")
    private Integer usercode;

    /**
     * 门店id，点评传点评，美团传美团
     */
    @MobileRequest.Param(name = "shopid")
    @Deprecated
    private Integer shopid;

    /**
     * 门店id，点评传点评，美团传美团
     */
    @MobileRequest.Param(name = "shopidstring")
    private String shopidstring;

    /**
     * 稀疏化shopid，如果是点评的话，传点评shopuuid
     */
    @MobileRequest.Param(name = "shopuuid")
    private String shopuuid;

    /**
     * 城市id
     */
    @MobileRequest.Param(name = "cityid")
    private Integer cityid;

    /**
     * 商品id，如果是团购类型，则是团单id(dealGroupId)
     */
    @MobileRequest.Param(name = "productid")
    private Long productid;

    /**
     * 经度
     */
    @MobileRequest.Param(name = "longitude")
    private Double longitude;

    /**
     * 纬度
     */
    @MobileRequest.Param(name = "latitude")
    private Double latitude;

    @MobileRequest.Param(name = "clienttype")
    private Integer clienttype;

    @MobileRequest.Param(name = "platform")
    private Integer platform;

    @MobileRequest.Param(name = "scene")
    private Integer scene;

    @FieldDoc(
            description = "丽人团单新样式"
    )
    @MobileRequest.Param(name = "beautydealnewtype", required = false)
    private Boolean beautyDealNewType = false;


    @FieldDoc(
            description = "渠道来源"
    )
    @MobileRequest.Param(name = "pagesource")
    private String pagesource;

    /**
     * 流量来源，百度地图跳转，填"baiduMapApp"
     */
    @MobileRequest.Param(name = "trafficsource")
    private String trafficsource;

    @MobileRequest.Param(name = "business")
    private String business;

    /**
     * @PageSourceEnum
     * 页面来源，pagesource是透传给活动的不能用
     */
    @MobileRequest.Param(name = "couponpagesource")
    private String couponPageSource;

    @FieldDoc(
            description = "shopUuid加密文"
    )
    @MobileRequest.Param(name = "shopuuidencrypt")
    private String shopuuidencrypt;

    @FieldDoc(
            description = "shopIdString加密值"
    )
    @MobileRequest.Param(name = "shopidstringencrypt")
    private String shopidstringencrypt;

    public Long getShopIdL() {
        long shopIdL = NumberUtils.toLong(shopidstring);
        if(shopIdL > 0) {
            return shopIdL;
        }
        if(shopid != null) {
            return shopid.longValue();
        }
        return null;
    }

    public void setShopIdL(Long shopIdl) {
        this.shopidstring = String.valueOf(shopIdl);
    }
}
