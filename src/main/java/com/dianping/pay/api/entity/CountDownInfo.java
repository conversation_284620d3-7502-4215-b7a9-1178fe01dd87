package com.dianping.pay.api.entity;

import com.dianping.api.common.util.KeyValuePair;
import com.dianping.api.domain.ClientInfo;
import com.dianping.api.domain.Version;
import com.dianping.api.netio.DPEncoder;
import com.dianping.cat.Cat;
import com.google.common.collect.Lists;

import java.io.Serializable;
import java.util.List;

/**
 * Created by xiaoxiao.li on 3/13/15.
 */
public class CountDownInfo extends DPEncoder implements Serializable {
    private static final long serialVersionUID = -7470231802069346345L;
    private boolean needCountDown;
    private int remainSecond;
    @Override
    public int getClassId() {
        Cat.logEvent("INVALID_METHOD_1", "com.dianping.pay.api.entity.CountDownInfo.getClassId()");
        return 0xc32;
    }

    private static final List<KeyValuePair> LIST = Lists.newArrayList();
    static {
        LIST.add(new KeyValuePair("needCountDown",0x7dd2));
        LIST.add(new KeyValuePair("remainSecond",0x89dd));
    }
    @Override
    public List<KeyValuePair> getSerializeList(ClientInfo clientInfo, Version version) {
        Cat.logEvent("INVALID_METHOD_5", "com.dianping.pay.api.entity.CountDownInfo.getSerializeList(com.dianping.api.domain.ClientInfo,com.dianping.api.domain.Version)");
        return LIST;
    }

    public boolean isNeedCountDown() {
        return needCountDown;
    }

    public void setNeedCountDown(boolean needCountDown) {
        this.needCountDown = needCountDown;
    }

    public int getRemainSecond() {
        return remainSecond;
    }

    public void setRemainSecond(int remainSecond) {
        this.remainSecond = remainSecond;
    }
}
