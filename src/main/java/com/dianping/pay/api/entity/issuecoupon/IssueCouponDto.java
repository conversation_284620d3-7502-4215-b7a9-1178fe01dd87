package com.dianping.pay.api.entity.issuecoupon;

import com.dianping.mobile.framework.annotation.MobileDo;
import com.dianping.mobile.framework.annotation.MobileDo.MobileField;
import lombok.Data;

import java.io.Serializable;
import java.util.List;


/**
 * <AUTHOR>
 * @date 08/04/2020
 * @time 18:27
 * 模型描述：领券组件弹框-优惠券信息
 * 移动之家链接: https://mobile.sankuai.com/studio/model/info/23275
 */
@MobileDo(id = 0x99b9)
@Data
public class IssueCouponDto implements Serializable {


    /**
     * 领券列表信息
     */
    @MobileField(key = 0x85df)
    private List<UnifiedIssueCouponOptionDo> unifiedIssueCouponOptionDos;

    /**
     * 券列表信息
     */
    @MobileField(key = 0x82ec)
    private String issueCouponTitle;

}
