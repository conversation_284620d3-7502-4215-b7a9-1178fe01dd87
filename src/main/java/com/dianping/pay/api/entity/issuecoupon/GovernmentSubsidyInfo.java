package com.dianping.pay.api.entity.issuecoupon;

import com.dianping.mobile.framework.annotation.MobileDo;
import lombok.Data;

import java.io.Serializable;

/**
 * 国补氛围信息
 * <AUTHOR>
 */
@MobileDo(id = 0xc0a0)
@Data
public class GovernmentSubsidyInfo implements Serializable {

    /**
     * 是否展示国补氛围
     */
    @MobileDo.MobileField(key = 0x4200)
    private boolean showGovernmentSubsidy;

    /**
     * 国补标签信息
     */
    @MobileDo.MobileField(key = 0x4201)
    private GovernmentSubsidyTag subsidyTag;

    /**
     * 国补浮层模块信息
     */
    @MobileDo.MobileField(key = 0x4202)
    private GovernmentSubsidyModule subsidyModule;

    /**
     * 展位标识
     */
    @MobileDo.MobileField(key = 0x4203)
    private String boothId = "MT_MERCHANT_DETAILS_BANNER";

}
