package com.dianping.pay.api.entity;

import com.dianping.api.common.util.KeyValuePair;
import com.dianping.api.domain.ClientInfo;
import com.dianping.api.domain.Version;
import com.dianping.api.netio.DPEncoder;
import com.dianping.cat.Cat;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

/**
 * User: huawei.li
 * Date: 14-6-2
 * Time: 下午3:37
 */
public class UserWallet extends DPEncoder implements Serializable {
    private double userBalance;
    private double redEnvelopeBalance;
    private double giftCardBalance;
    private int discountNum;
    @Override
    public int getClassId() {
        Cat.logEvent("INVALID_METHOD_4", "com.dianping.pay.api.entity.UserWallet.getClassId()");
        return 0x18b8;
    }
    protected static final List<KeyValuePair> LIST =new ArrayList<KeyValuePair>();

    static{
        LIST.add(new KeyValuePair("userBalance", 0xc542));
        LIST.add(new KeyValuePair("redEnvelopeBalance", 0xfefb));
        LIST.add(new KeyValuePair("giftCardBalance", 0xd798));
        LIST.add(new KeyValuePair("discountNum", 0x910a));
    }

    @Override
    public List<KeyValuePair> getSerializeList(ClientInfo clientInfo, Version version) {
        Cat.logEvent("INVALID_METHOD_3", "com.dianping.pay.api.entity.UserWallet.getSerializeList(com.dianping.api.domain.ClientInfo,com.dianping.api.domain.Version)");
        return LIST;
    }

    public double getUserBalance() {
        return userBalance;
    }

    public void setUserBalance(double userBalance) {
        this.userBalance = userBalance;
    }

    public double getRedEnvelopeBalance() {
        return redEnvelopeBalance;
    }

    public void setRedEnvelopeBalance(double redEnvelopeBalance) {
        this.redEnvelopeBalance = redEnvelopeBalance;
    }

    public double getGiftCardBalance() {
        return giftCardBalance;
    }

    public void setGiftCardBalance(double giftCardBalance) {
        this.giftCardBalance = giftCardBalance;
    }

    public int getDiscountNum() {
        return discountNum;
    }

    public void setDiscountNum(int discountNum) {
        this.discountNum = discountNum;
    }
}
