package com.dianping.pay.api.entity.issuecoupon;

import com.dianping.mobile.framework.annotation.MobileDo;
import com.dianping.mobile.framework.annotation.MobileDo.MobileField;
import lombok.Data;

import java.io.Serializable;


/**
 * <AUTHOR>
 * @date 08/04/2020
 * @time 18:27
 * 模型描述：领券弹框组件展示数据
 * 移动之家链接: https://mobile.sankuai.com/studio/model/info/23274
 */
@MobileDo(id = 0x1352)
@Data
public class PromotionDisplayDto implements Serializable {


    /**
     * 领券组件信息
     */
    @MobileField(key = 0xa152)
    private IssueCouponDto issueCouponDto;

    /**
     * 返礼展示
     */
    @MobileField(key = 0x6621)
    private ReturnPromotionDisplayDto returnPromotionDisplayDto;

    /**
     * 领券组件弹框标题
     */
    @MobileField(key = 0xd15b)
    private String promotionDisplayTitle;


}
