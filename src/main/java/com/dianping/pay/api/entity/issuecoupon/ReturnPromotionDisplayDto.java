package com.dianping.pay.api.entity.issuecoupon;

import com.dianping.mobile.framework.annotation.MobileDo;
import com.dianping.mobile.framework.annotation.MobileDo.MobileField;
import lombok.Data;

import java.io.Serializable;
import java.util.List;


/**
 * <AUTHOR>
 * @date 08/04/2020
 * @time 18:27
 * 模型描述：领券组件-返礼模块展示
 * 移动之家链接: https://mobile.sankuai.com/studio/model/info/23277
 */
@MobileDo(id = 0x4c17)
@Data
public class ReturnPromotionDisplayDto implements Serializable {


    /**
     * 返礼跳转链接
     */
    @MobileField(key = 0x6791)
    private String returnPromotionDirectUrl;

    /**
     * 返礼明细
     */
    @MobileField(key = 0x71fd)
    private List<ReturnPromotionDetail> returnPromotionDetails;

    /**
     * 标题
     */
    @MobileField(key = 0xee24)
    private String returnPromotionTitle;

}
