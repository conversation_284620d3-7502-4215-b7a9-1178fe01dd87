package com.dianping.pay.api.entity.issuecoupon;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import java.util.List;
import lombok.Data;

@Data
public class NewBatchIssueCouponRequest {
    @FieldDoc(
        description = "发券渠道 {@link com.dianping.gmkt.coupon.common.api.enums.IssueDetailSourceEnum}"
    )
    private Integer issueDetailSourceCode;

    @FieldDoc(
        description = "请求商品类型，0：团单"
    )
    private Integer productType;

    @FieldDoc(
        description = "请求发券单元"
    )
    private List<NewIssueCouponUnit> newIssueCouponUnitList;
}
