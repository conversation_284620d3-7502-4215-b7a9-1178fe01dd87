package com.dianping.pay.api.entity;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

import com.dianping.api.common.util.KeyValuePair;
import com.dianping.api.domain.ClientInfo;
import com.dianping.api.domain.Version;
import com.dianping.api.netio.DPEncoder;
import com.google.common.collect.Lists;

public class BatchTicket extends DPEncoder implements Serializable {
	
	private static final long serialVersionUID = 4244766410300319462L;

	private int batchTicketID;
	private String title;
	private String code;
	private int count;
	private Date generateTime;
	private int status;

	public BatchTicket( int tiketID ,String title, String code ,int count )
	{
		this.batchTicketID = tiketID;
		this.title = title;
		this.code = code;
		this.count = count;
		this.status = 0;
	}

	
	public BatchTicket(int batchTicketID, String title, String code, int count,
			Date generateTime, int status) {
		super();
		this.batchTicketID = batchTicketID;
		this.title = title;
		this.code = code;
		this.count = count;
		this.generateTime = generateTime;
		this.status = status;
	}


	public int getBatchTicketID() {
		return batchTicketID;
	}
	public void setBatchTicketID(int batchTicketID) {
		this.batchTicketID = batchTicketID;
	}
	public String getTitle() {
		return title;
	}
	public void setTitle(String title) {
		this.title = title;
	}
	public String getCode() {
		return code;
	}
	public void setCode(String code) {
		this.code = code;
	}
	public int getCount() {
		return count;
	}
	public void setCount(int count) {
		this.count = count;
	}
	public Date getGenerateTime() {
		return generateTime;
	}
	public void setGenerateTime(Date generateTime) {
		this.generateTime = generateTime;
	}
	public int getStatus() {
		return status;
	}
	public void setStatus(int status) {
		this.status = status;
	}
	@Override
	public int getClassId() {
		// TODO Auto-generated method stub
		return 0x6270;
	}
	private static final List<KeyValuePair> LIST = Lists.newArrayList();
	static {
		LIST.add(new KeyValuePair("batchTicketID", 0x7921));
		LIST.add(new KeyValuePair("title", 0x36e9));
		LIST.add(new KeyValuePair("code", 0x222d));
		LIST.add(new KeyValuePair("count", 0x630b));
		LIST.add(new KeyValuePair("generateTime", 0x1e3b));
		LIST.add(new KeyValuePair("status", 0x2820));
	}
	@Override
	public List<KeyValuePair> getSerializeList(ClientInfo clientInfo,
			Version version) {
		// TODO Auto-generated method stub
		return LIST;
	}

}
