package com.dianping.pay.api.entity.issuecoupon;

import com.dianping.api.domain.ClientInfo;
import com.dianping.api.framework.Controller;
import com.dianping.cat.Cat;
import com.dianping.mobile.framework.annotation.MobileRequest;
import com.dianping.mobile.framework.datatypes.IMobileContext;
import com.dianping.pay.api.beans.ApiBaseRequest;
import com.dianping.pay.api.biz.activity.newloader.dto.RestUserInfo;
import com.dianping.pay.promo.common.enums.User;
import com.google.common.base.Function;
import com.google.common.collect.Lists;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import lombok.Data;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang.math.NumberUtils;

import javax.annotation.Nullable;
import java.util.List;
import java.util.Map;

@TypeDoc(description = "接口请求参数")
@MobileRequest
@Data
public class IssueCouponRequest extends ApiBaseRequest {

    @FieldDoc(
            description = "纬度"
    )
    @MobileRequest.Param(name = "latitude")
    private Double latitude;

    @FieldDoc(
            description = "经度"
    )
    @MobileRequest.Param(name = "longitude")
    private Double longitude;

    @FieldDoc(
            description = "商品id，已废弃"
    )
    @MobileRequest.Param(name = "productid", required = false)
    @Deprecated
    private Integer productId = 0;

    @FieldDoc(
            description = "商品id，长整型"
    )
    @MobileRequest.Param(name = "longproductid", required = false)
    private Long longProductId;

    @FieldDoc(
            description = "门店id"
    )
    @MobileRequest.Param(name = "shopid", required = false)
    private String shopId;

    @FieldDoc(
            description = "String类型门店id"
    )
    @MobileRequest.Param(name = "shopidstring", required = false)
    private String shopIdString;

    @FieldDoc(
            description = "稀疏化门店id"
    )
    @MobileRequest.Param(name = "shopuuid", required = false)
    private String shopUuid;

    @FieldDoc(
            description = "门店id列表"
    )
    @MobileRequest.Param(name = "shopidlist", required = false)
    private List<String> shopIdList;

    @FieldDoc(
            description = "稀疏化门店id列表"
    )
    @MobileRequest.Param(name = "shopuuids", required = false)
    private List<String> shopUuids;

    @FieldDoc(
            description = "券批次Id"
    )
    @MobileRequest.Param(name = "couponoptionid", required = false)
    private Integer couponOptionId = 0;

    @FieldDoc(
            description = "券批次Id"
    )
    @MobileRequest.Param(name = "stringcouponoptionid", required = false)
    private String stringCouponOptionId;

    @FieldDoc(
            description = "类目id"
    )
    @MobileRequest.Param(name = "categoryid", required = false)
    private Integer categoryId = 0;

    @FieldDoc(
            description = "是否是点评用户"
    )
    @MobileRequest.Param(name = "isdpclient", required = false)
    private Boolean isDpClient = false;

    @FieldDoc(
            description = "城市id"
    )
    @MobileRequest.Param(name = "cityid", required = false)
    private Integer cityId = 0;

    @FieldDoc(
            description = "请求类型"
    )
    @MobileRequest.Param(name = "requesttype", required = false)
    private Integer requestType = 0;

    @FieldDoc(
            description = "当前商品的参数"
    )
    @MobileRequest.Param(name = "shopproducttype", required = false)
    private Integer shopProductType = 0;

    @FieldDoc(
            description = "发券渠道 {@link com.dianping.gmkt.coupon.common.api.enums.IssueDetailSourceEnum}"
    )
    @MobileRequest.Param(name = "issuedetailsourcecode", required = false)
    private Integer issueDetailSourceCode;

    /**
     * 投放活动券发券专用-活动id
     */
    @MobileRequest.Param(name = "activityid")
    private Long activityid;

    /**
     * 投放活动券发券专用-物料id
     */
    @MobileRequest.Param(name = "materialid")
    private String materialid;

    /**
     * 投放活动券发券专用-投放活动id，如果发放的是投放券的话传递这个参数
     */
    @MobileRequest.Param(name = "resourceactivityid")
    private Long resourceactivityid;

    /**
     * 投放活动券发券专用-分流位id，投放的是活动投放券时传这个id
     */
    @MobileRequest.Param(name = "flowid")
    private Long flowid;

    /**
     * 投放活动券发券专用-rowkey
     */
    @MobileRequest.Param(name = "rowkey")
    private String rowkey;

    @MobileRequest.Param(name = "uuid")
    private String uuid;

    @FieldDoc(
            description = "渠道来源,@PageSourceEnum"
    )
    @MobileRequest.Param(name = "couponpagesource")
    private String couponPageSource;

    @FieldDoc(
            description = "平台标识：1-IOS；2-Android"
    )
    @MobileRequest.Param(name = "cplatform")
    private Integer cPlatform;

    @FieldDoc(
            description = "指纹参数"
    )
    @MobileRequest.Param(name = "mtfingerprint")
    private String mtFingerprint;

    @FieldDoc(
            description = "实际定位城市ID"
    )
    @MobileRequest.Param(name = "actualcityid")
    private Integer actualCityId;

    @FieldDoc(
            description = "券包密钥，政府N选1券必填"
    )
    @MobileRequest.Param(name = "packagesecretkey")
    private String packagesecretkey;

    @FieldDoc(
            description = "regionId,用于路由到家set"
    )
    @MobileRequest.Param(name = "regionId")
    private String regionId;

    @FieldDoc(
            description = "ap参数"
    )
    private Map<String, String> apParams;

    @FieldDoc(
            description = "通用参数"
    )
    private Map<String, String> commonParams;

    @FieldDoc(
            description = "额外参数"
    )
    private Map<String, String> extraParams;

    public IssueCouponRequest(){}

    public IssueCouponRequest(Controller controller) {
        super(controller);

        productIdCompatible(controller.getParametersMap().getInteger("productid"), controller.getParametersMap().getLong("longproductid"));
        if (productId == 0) {

            this.shopId = controller.getParametersMap().getString("shopid");
            this.shopIdString = controller.getParametersMap().getString("shopidstring");
            this.shopUuid = controller.getParametersMap().getString("shopuuid");


            if (StringUtils.isBlank(shopId) && StringUtils.isBlank(shopUuid) && StringUtils.isBlank(shopIdString)) {
                this.categoryId = controller.getParametersMap().getInteger("categoryid");
            }

            if (StringUtils.isBlank(shopId) && StringUtils.isBlank(shopIdString)) {
                if(StringUtils.isNotBlank(controller.getParametersMap().getString("shopids"))) {
                    this.shopIdList = Lists.newArrayList(controller.getParametersMap().getString("shopids").split(","));
                }
            } else {
                shopIdList = Lists.newArrayList(StringUtils.isNotBlank(shopIdString) ? shopIdString : shopId);
            }


            if (StringUtils.isBlank(shopUuid)) {
                if(StringUtils.isNotBlank(controller.getParametersMap().getString("shopuuids"))) {
                    this.shopUuids = Lists.newArrayList(controller.getParametersMap().getString("shopuuids").split(","));
                }
            } else {
                shopUuids = Lists.newArrayList(shopUuid);
            }

        }

        if (CollectionUtils.isNotEmpty(shopIdList) && shopIdList.size() > 1) {
            Cat.logEvent("shopIdList", String.valueOf(shopIdList.size()));
        }

        couponGroupIdCompatible(controller.getParametersMap().getInteger("couponoptionid"), controller.getParametersMap().getString("stringcouponoptionid"));
        this.isDpClient = !getClientInfo().getClient().isMtClient();
        this.cityId = controller.getParametersMap().getInteger("cityid");
        this.shopProductType = controller.getParametersMap().getInteger("shopproducttype");
        this.issueDetailSourceCode = controller.getParametersMap().getInteger("issuedetailsourcecode");
    }

    public IssueCouponRequest(IssueCouponRequest request, IMobileContext context, int requestType) {
        if (context.isMtClient() && context.getUserStatus() != null) {
            this.userId = context.getUserStatus().getMtUserId();
        } else if (!context.isMtClient()) {
            this.userId = context.getUserId();
        } else {
            this.userId = 0L;
        }

        productIdCompatible(request.getProductId(), request.getLongProductId());

        if (productId == 0) {
            this.shopId = request.getShopIdStr();
            this.shopIdString = request.getShopIdString();
            this.shopUuid = request.getShopUuid();

            if (StringUtils.isBlank(shopId) && StringUtils.isBlank(shopUuid)) {
                this.categoryId = request.getCategoryId();
            }

            if (StringUtils.isBlank(shopId) && StringUtils.isBlank(shopIdString)) {
                this.shopIdList = request.getShopIdListStr();
            } else {
                shopIdList = Lists.newArrayList(StringUtils.isNotBlank(shopIdString) ? shopIdString : shopId);
            }
            if (StringUtils.isBlank(shopUuid)) {
                this.shopUuids = request.getShopUuids();
            } else {
                shopUuids = Lists.newArrayList(shopUuid);
            }
        }
        couponGroupIdCompatible(request.getCouponOptionId(), request.getStringCouponOptionId());

        this.isDpClient = !context.isMtClient();
        this.cityId = request.getCityId();
        this.shopProductType = request.getShopProductType();
        this.issueDetailSourceCode = request.getIssueDetailSourceCode();

        ClientInfo clientInfo = new ClientInfo();
        clientInfo.setDpId(context.getHeader().getDpid());
        clientInfo.setMtId(context.getHeader().getUuid());
        this.clientInfo = clientInfo;
        this.requestType = requestType;
    }

    public IssueCouponRequest(Controller controller, int requestType) {
        this(controller);
        this.requestType = requestType;
    }

    public IssueCouponRequest(ExternalLinkIssueCouponRequest request, IMobileContext context, Integer couponOptionId, int requestType) {
        if (context.isMtClient() && context.getUserStatus() != null) {
            this.userId = context.getUserStatus().getMtUserId();
        } else if (!context.isMtClient()) {
            this.userId = context.getUserId();
        } else {
            this.userId = 0L;
        }

        this.couponOptionId = couponOptionId;
        this.stringCouponOptionId = couponOptionId + "";
        this.isDpClient = !context.isMtClient();
        this.issueDetailSourceCode = request.getIssueDetailSourceCode();

        ClientInfo clientInfo = new ClientInfo();
        clientInfo.setDpId(context.getHeader().getDpid());
        clientInfo.setMtId(context.getHeader().getUuid());
        this.clientInfo = clientInfo;
        this.requestType = requestType;
    }

    public IssueCouponRequest(ExternalLinkIssueCouponRequest request, RestUserInfo restUserInfo, int couponOptionId, int requestType) {
        boolean isDpClient = restUserInfo.getUserCode() != User.MT.getCode();

        this.userId = restUserInfo.getUserId();
        this.couponOptionId = couponOptionId;
        this.stringCouponOptionId = couponOptionId + "";
        this.isDpClient = isDpClient;
        this.issueDetailSourceCode = request.getIssueDetailSourceCode();

        ClientInfo clientInfo = new ClientInfo();
        clientInfo.setDpId(request.getDpid());
        clientInfo.setMtId("");
        this.clientInfo = clientInfo;
        this.requestType = requestType;
    }

    private void productIdCompatible(Integer productId, Long longProductId) {
        this.productId = productId;
        this.longProductId = longProductId;

        // 兼容性处理：如果productId为0，但longProductId有值，则将longProductId的值赋给productId
        if (productId == 0 && longProductId != null && longProductId > 0) {
            this.productId = longProductId.intValue();
        } else if (productId > 0 && (longProductId == null || longProductId == 0L)) {
            // 如果productId有值但longProductId没有值，则将productId的值赋给longProductId
            this.longProductId = Long.valueOf(productId);
        }
    }

    private void couponGroupIdCompatible(Integer couponOptionId, String stringCouponOptionId) {
        this.couponOptionId = couponOptionId;
        this.stringCouponOptionId = stringCouponOptionId;
        // 兼容性处理：如果couponOptionId为0，但stringCouponOptionId有值，则将stringCouponOptionId的值赋给couponOptionId
        if (couponOptionId == 0 && StringUtils.isNotBlank(stringCouponOptionId)) {
            this.couponOptionId = NumberUtils.toInt(stringCouponOptionId);
        } else if (couponOptionId > 0 && StringUtils.isBlank(stringCouponOptionId)) {
            // 如果productId有值但longProductId没有值，则将productId的值赋给longProductId
            this.stringCouponOptionId = couponOptionId + "";
        }
    }

    public void validate() throws IllegalArgumentException {

    }

    public int getProductId() {
        return productId;
    }

    public void setProductId(int productId) {
        this.productId = productId;
    }

    public Long getLongProductId() {
        return longProductId;
    }

    public void setLongProductId(Long longProductId) {
        this.longProductId = longProductId;
    }

    @Deprecated
    public int getShopId() {
        return NumberUtils.toInt(shopId);
    }

    public long getShopIdL() {
        long shopIdL = NumberUtils.toLong(shopIdString);
        return shopIdL > 0 ? shopIdL : NumberUtils.toLong(shopId);
    }

    public String getShopIdStr() {
        return StringUtils.isNotBlank(shopIdString) ? shopIdString : shopId;
    }

    @Deprecated
    public void setShopId(int shopId) {
        this.shopId = String.valueOf(shopId);
    }

    public void setShopIdL(long shopId) {
        this.shopIdString = String.valueOf(shopId);
    }

    public int getCouponOptionId() {
        return couponOptionId;
    }

    public void setCouponOptionId(int couponOptionId) {
        this.couponOptionId = couponOptionId;
    }

    public String getStringCouponOptionId() {
        return stringCouponOptionId;
    }

    public void setStringCouponOptionId(String stringCouponOptionId) {
        this.stringCouponOptionId = stringCouponOptionId;
    }

    public int getCategoryId() {
        return categoryId;
    }

    public void setCategoryId(int categoryId) {
        this.categoryId = categoryId;
    }

    public boolean isDpClient() {
        return isDpClient;
    }

    public void setDpClient(boolean isDpClient) {
        this.isDpClient = isDpClient;
    }

    public int getCityId() {
        return cityId;
    }

    public void setCityId(int cityId) {
        this.cityId = cityId;
    }

    @Deprecated
    public List<Integer> getShopIdList() {
        return CollectionUtils.isNotEmpty(shopIdList) ? Lists.newArrayList(Lists.transform(shopIdList, new Function<String, Integer>() {
            @Nullable
            @Override
            public Integer apply(@Nullable String input) {
                return StringUtils.isBlank(input) ? 0 : NumberUtils.toInt(input);
            }
        })) : null;
    }

    public List<Long> getShopIdLList() {
        return CollectionUtils.isNotEmpty(shopIdList) ? Lists.newArrayList(Lists.transform(shopIdList, new Function<String, Long>() {
            @Nullable
            @Override
            public Long apply(@Nullable String input) {
                return StringUtils.isBlank(input) ? 0 : NumberUtils.toLong(input);
            }
        })) : null;
    }

    public List<String> getShopIdListStr() {
        return shopIdList;
    }

    @Deprecated
    public void setShopIdList(List<Integer> shopIdList) {
        if (CollectionUtils.isNotEmpty(shopIdList)) {
            this.shopIdList = Lists.newArrayList(Lists.transform(shopIdList, new Function<Integer, String>() {
                @Nullable
                @Override
                public String apply(@Nullable Integer input) {
                    return String.valueOf(input);
                }
            }));
        }
    }

    public void setShopIdLList(List<Long> shopIdList) {
        if (CollectionUtils.isNotEmpty(shopIdList)) {
            this.shopIdList = Lists.newArrayList(Lists.transform(shopIdList, new Function<Long, String>() {
                @Nullable
                @Override
                public String apply(@Nullable Long input) {
                    return String.valueOf(input);
                }
            }));
        }
    }

    public int getRequestType() {
        return requestType;
    }

    public void setRequestType(int requestType) {
        this.requestType = requestType;
    }

    public int getShopProductType() {
        return shopProductType;
    }

    public void setShopProductType(int shopProductType) {
        this.shopProductType = shopProductType;
    }

    public String getShopUuid() {
        return shopUuid;
    }

    public void setShopUuid(String shopUuid) {
        this.shopUuid = shopUuid;
    }

    public List<String> getShopUuids() {
        return shopUuids;
    }

    public void setShopUuids(List<String> shopUuids) {
        this.shopUuids = shopUuids;
    }

    public Integer getIssueDetailSourceCode() {
        return issueDetailSourceCode;
    }

    public void setIssueDetailSourceCode(Integer issueDetailSourceCode) {
        this.issueDetailSourceCode = issueDetailSourceCode;
    }

    public Map<String, String> getApParams() {
        return apParams;
    }

    public void setApParams(Map<String, String> apParams) {
        this.apParams = apParams;
    }
}
