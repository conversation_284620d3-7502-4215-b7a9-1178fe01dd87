package com.dianping.pay.api.entity;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import com.dianping.api.common.entity.Pair;
import com.dianping.api.common.util.KeyValuePair;
import com.dianping.api.domain.ClientInfo;
import com.dianping.api.domain.Version;
import com.dianping.api.netio.DPEncoder;
import com.dianping.api.util.ApiUtil;

public class PrePayOrderDTO extends DPEncoder implements Serializable {
	private static final long serialVersionUID = 1958983304967769053L;
	private int orderId;
	private long userId;
	private int count;
	private int cityId;
	private Date addDate;
	private String mobileNo;
	private int productId;
	private int productGroupId;
	private double totalPrice;
	private Pair deliveryType;
	private Delivery delivery;
	private String invoiceTitle;
	private Discount discount;
    private double reductionPrice;
    private String reductionDesc;
    private boolean needCountDown;
    private int remainSecond;
    private boolean isCouponEnable = true;
	@Override
	public int getClassId() {
		return 0x1c38;
	}
	static final List<KeyValuePair> list=new ArrayList<KeyValuePair>();
	static{
		list.add(new KeyValuePair("orderId",0x978c));
		list.add(new KeyValuePair("count",0x630b));
		list.add(new KeyValuePair("addDate",0xc6ca));
		list.add(new KeyValuePair("userId",0x8dd6));
		list.add(new KeyValuePair("mobileNo",0x85e0));
		list.add(new KeyValuePair("cityId",0xa7b4));
		list.add(new KeyValuePair("totalPrice",0x6777));
		list.add(new KeyValuePair("deliveryType",0x6b1f));
		list.add(new KeyValuePair("discount",0xcd43));
		list.add(new KeyValuePair("delivery",0xb4fb));
		list.add(new KeyValuePair("invoiceTitle",0xe649));
		list.add(new KeyValuePair("productGroupId",0x95c1));
		list.add(new KeyValuePair("productId",0x4eea));
        list.add(new KeyValuePair("reductionPrice",0x1926));
        list.add(new KeyValuePair("reductionDesc",0xfffe));
        list.add(new KeyValuePair("needCountDown",0x7dd2));
        list.add(new KeyValuePair("remainSecond",0x89dd));
	}
	@Override
	public List<KeyValuePair> getSerializeList(ClientInfo clientInfo,
			Version version) {
		return list;
	}
	public int getOrderId() {
		return orderId;
	}
	public void setOrderId(int orderId) {
		this.orderId = orderId;
	}
	public Date getAddDate() {
		return addDate;
	}
	public void setAddDate(Date addDate) {
		this.addDate = addDate;
	}
	public long getUserId() {
		return userId;
	}
	public void setUserId(long userId) {
		this.userId = userId;
	}
	public int getCount() {
		return count;
	}
	public void setCount(int count) {
		this.count = count;
	}
	public int getCityId() {
		return cityId;
	}
	public void setCityId(int cityId) {
		this.cityId = cityId;
	}
	public String getMobileNo() {
		return mobileNo;
	}
	public void setMobileNo(String mobileNo) {
		this.mobileNo = mobileNo;
	}
	public int getProductId() {
		return productId;
	}
	public void setProductId(int productId) {
		this.productId = productId;
	}
	public int getProductGroupId() {
		return productGroupId;
	}
	public void setProductGroupId(int productGroupId) {
		this.productGroupId = productGroupId;
	}
	public double getTotalPrice() {
		return totalPrice;
	}
	public void setTotalPrice(double totalPrice) {
		this.totalPrice = totalPrice;
	}
	public Pair getDeliveryType() {
		return deliveryType;
	}
	public void setDeliveryType(Pair deliveryType) {
		this.deliveryType = deliveryType;
	}
	public Delivery getDelivery() {
		return delivery;
	}
	public void setDelivery(Delivery delivery) {
		this.delivery = delivery;
	}
	public String getInvoiceTitle() {
		return invoiceTitle;
	}
	public void setInvoiceTitle(String invoiceTitle) {
		this.invoiceTitle = invoiceTitle;
	}
	public Discount getDiscount() {
		return discount;
	}
	public void setDiscount(Discount discount) {
		this.discount = discount;
	}
    public double getReductionPrice() {
        return reductionPrice;
    }

    public void setReductionPrice(double reductionPrice) {
        this.reductionPrice = reductionPrice;
    }

    public String getReductionDesc() {
        return reductionDesc;
    }

    public void setReductionDesc(String reductionDesc) {
        this.reductionDesc = reductionDesc;
    }

    public int getRemainSecond() {
        return remainSecond;
    }

    public void setRemainSecond(int remainSecond) {
        this.remainSecond = remainSecond;
    }

    public boolean isNeedCountDown() {
        return needCountDown;
    }

    public void setNeedCountDown(boolean needCountDown) {
        this.needCountDown = needCountDown;
    }

    public boolean isCouponEnable() {
        return isCouponEnable;
    }

    public void setCouponEnable(boolean isCouponEnable) {
        this.isCouponEnable = isCouponEnable;
    }
}
