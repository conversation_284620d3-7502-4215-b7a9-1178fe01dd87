package com.dianping.pay.api.entity;

import java.io.Serializable;
import java.util.List;

import com.dianping.api.common.entity.Pair;
import com.dianping.api.common.util.KeyValuePair;
import com.dianping.api.domain.ClientInfo;
import com.dianping.api.domain.Version;
import com.dianping.api.netio.DPEncoder;
import com.dianping.cat.Cat;
import com.google.common.collect.Lists;

public class SaleInfo extends DPEncoder implements Serializable {

	
	private static final long serialVersionUID = 9035036106044950524L;
	
	public int getBuyLimit() {
		return buyLimit;
	}

	public void setBuyLimit(int buyLimit) {
		this.buyLimit = buyLimit;
	}

	public String getPhone() {
		return phone;
	}

	public void setPhone(String phone) {
		this.phone = phone;
	}

	public List<Delivery> getDeliveryList() {
		return deliveryList;
	}

	public void setDeliveryList(List<Delivery> deliveryList) {
		this.deliveryList = deliveryList;
	}

	public List<Pair> getDeliveryTypes() {
		return deliveryTypes;
	}

	public void setDeliveryTypes(List<Pair> deliveryTypes) {
		this.deliveryTypes = deliveryTypes;
	}
	
	private static final List<KeyValuePair> LIST = Lists.newArrayList();
	static {
		LIST.add(new KeyValuePair("buyLimit", 0xacd4));
		LIST.add(new KeyValuePair("phone", 0x49d6));
		LIST.add(new KeyValuePair("deliveryList", 0x48bf));
		LIST.add(new KeyValuePair("deliveryTypes", 0x6b1f));
 	}
	
	@Override
	public List<KeyValuePair> getSerializeList(ClientInfo clientInfo, Version version) {
        Cat.logEvent("INVALID_METHOD_2", "com.dianping.pay.api.entity.SaleInfo.getSerializeList(com.dianping.api.domain.ClientInfo,com.dianping.api.domain.Version)");
        return LIST;
	}
	
	private int buyLimit;                //购买上限
	private String phone;	             //用户手机号
	private List<Delivery> deliveryList; //（用户之前填写过的）送货地址
	private List<Pair> deliveryTypes;     //配送时间类型

	/* (non-Javadoc)
	 * @see com.dianping.api.netio.DPEncoder#getClassId()
	 */
	@Override
	public int getClassId() {
        Cat.logEvent("INVALID_METHOD_2", "com.dianping.pay.api.entity.SaleInfo.getClassId()");
        return 0xf68e;
	}
}
