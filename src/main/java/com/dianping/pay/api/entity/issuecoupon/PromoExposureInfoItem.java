package com.dianping.pay.api.entity.issuecoupon;

import com.dianping.mobile.framework.annotation.MobileDo;
import lombok.Data;

import java.io.Serializable;

@MobileDo(id = 0x7aea)
@Data
public class PromoExposureInfoItem implements Serializable {

    /**
     *
     */
    @MobileDo.MobileField(key = 0xc004)
    private int coupontype;

    /**
     * 时间描述字段
     */
    @MobileDo.MobileField(key = 0x12d8)
    private String timeDesc;

    /**
     *
     */
    @MobileDo.MobileField(key = 0xbfe8)
    private String timeSubDesc;

    /**
     * 是否可领取 （如果可领取是，右侧展示点击领取按钮，如果不可领取，右侧展示倒计时时间）
     */
    @MobileDo.MobileField(key = 0x33f4)
    private boolean canAssign;

    /**
     * 金额
     */
    @MobileDo.MobileField(key = 0xfbe2)
    private String amount;

    /**
     * 副标题
     */
    @MobileDo.MobileField(key = 0xd894)
    private String subTitle;

    /**
     * 券标题
     */
    @MobileDo.MobileField(key = 0x24cc)
    private String title;

    /**
     * 倒计时时间 （大于0时使用该字段作为倒计时字段）
     */
    @MobileDo.MobileField(key = 0x1e0d)
    private long countDownTime;

    /**
     * 投放活动id，活动投放券专属
     */
    @MobileDo.MobileField(key = 0x1b0d)
    private long resourceActivityId;

    /**
     * 活动id，投放券专属字段
     */
    @MobileDo.MobileField(key = 0xe91)
    private long activityId;

    /**
     * 分流位id，投放券专属字段
     */
    @MobileDo.MobileField(key = 0xc2dd)
    private long flowId;

    /**
     * rowkey，投放券专属字段
     */
    @MobileDo.MobileField(key = 0x2efe)
    private String rowKey;

    /**
     * 物料id，投放券专属字段
     */
    @MobileDo.MobileField(key = 0x2a2c)
    private String materialId;

    /**
     * 是否需要弹窗提示，为true情况下，弹窗文案使用toastContent
     */
    @MobileDo.MobileField(key = 0x34bd)
    private boolean needToast;

    /**
     * 弹窗文案
     */
    @MobileDo.MobileField(key = 0xa860)
    private String toastContent;

    /**
     * 券使用开始时间
     */
    @MobileDo.MobileField(key = 0x9a29)
    private long useBeginTime;

    /**
     * 券使用结束时间
     */
    @MobileDo.MobileField(key = 0x7950)
    private long useEndTime;

    /**
     * 券类型，0：普通券；6：膨胀券
     */
    @MobileDo.MobileField(key = 0x59e0)
    private long couponValueType;


}
