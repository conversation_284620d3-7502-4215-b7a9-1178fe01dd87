package com.dianping.pay.api.entity.promodesk;

import com.dianping.api.common.util.KeyValuePair;
import com.dianping.api.domain.ClientInfo;
import com.dianping.api.domain.Version;
import com.dianping.api.netio.DPEncoder;
import com.dianping.cat.Cat;

import java.io.Serializable;
import java.util.LinkedList;
import java.util.List;

public class PointExchangeRule extends DPEncoder implements Serializable {

    private int id;
    private String memo;
    private double promoAmount;
    private int pointAmount;
    private String promoCipher;

    private static final LinkedList<KeyValuePair> l = new LinkedList<KeyValuePair>();
    static {
        l.add(new KeyValuePair("id", 0x91b));
        l.add(new KeyValuePair("memo", 0x897e));
        l.add(new KeyValuePair("promoAmount", 0x5f8c));
        l.add(new KeyValuePair("pointAmount", 0x9e7c));
        l.add(new KeyValuePair("promoCipher", 0xc706));
    }

    @Override
    public int getClassId() {
        Cat.logEvent("INVALID_METHOD_5", "com.dianping.pay.api.entity.promodesk.PointExchangeRule.getClassId()");
        return 0xbf4f;
    }

    @Override
    public List<KeyValuePair> getSerializeList(ClientInfo clientInfo, Version version) {
        Cat.logEvent("INVALID_METHOD_5", "com.dianping.pay.api.entity.promodesk.PointExchangeRule.getSerializeList(com.dianping.api.domain.ClientInfo,com.dianping.api.domain.Version)");
        return l;
    }

    public int getId() {
        return id;
    }

    public void setId(int id) {
        this.id = id;
    }

    public String getMemo() {
        return memo;
    }

    public void setMemo(String memo) {
        this.memo = memo;
    }

    public double getPromoAmount() {
        return promoAmount;
    }

    public void setPromoAmount(double promoAmount) {
        this.promoAmount = promoAmount;
    }

    public int getPointAmount() {
        return pointAmount;
    }

    public void setPointAmount(int pointAmount) {
        this.pointAmount = pointAmount;
    }

    public String getPromoCipher() {
        return promoCipher;
    }

    public void setPromoCipher(String promoCipher) {
        this.promoCipher = promoCipher;
    }
}
