package com.dianping.pay.api.entity;

import java.io.Serializable;
import java.util.LinkedList;
import java.util.List;

import com.dianping.api.common.entity.Pair;
import com.dianping.api.common.util.KeyValuePair;
import com.dianping.api.domain.ClientInfo;
import com.dianping.api.domain.Version;
import com.dianping.api.netio.DPEncoder;
import com.dianping.api.util.ApiUtil;
import com.dianping.cat.Cat;

public class NaviTag extends DPEncoder implements Serializable{
	
	private static final long serialVersionUID = -539543056283294398L;
	private String tagName;
	private String enName;
	private List<Pair> options;

	public String getTagName() {
		return tagName;
	}

	public void setTagName(String tagName) {
		this.tagName = tagName;
	}

	public String getEnName() {
		return enName;
	}

	public void setEnName(String enName) {
		this.enName = enName;
	}

	public List<Pair> getOptions() {
		return options;
	}

	public void setOptions(List<Pair> options) {
		this.options = options;
	}

	@Override
	public int getClassId() {
        Cat.logEvent("INVALID_METHOD_3", "com.dianping.pay.api.entity.NaviTag.getClassId()");
        return ApiUtil.getHash(NaviTag.class.getSimpleName());
	}

	public static final LinkedList<KeyValuePair> l = new LinkedList<KeyValuePair>();
	static {
		l.add(new KeyValuePair("tagName",ApiUtil.getHash("TagName")));
		l.add(new KeyValuePair("enName",ApiUtil.getHash("EnName")));
		l.add(new KeyValuePair("options", ApiUtil.getHash("Options")));
	}

	@Override
	public List<KeyValuePair> getSerializeList(ClientInfo clientInfo,
			Version version) {
        Cat.logEvent("INVALID_METHOD_5", "com.dianping.pay.api.entity.NaviTag.getSerializeList(com.dianping.api.domain.ClientInfo,com.dianping.api.domain.Version)");
        return l;
	}

}
