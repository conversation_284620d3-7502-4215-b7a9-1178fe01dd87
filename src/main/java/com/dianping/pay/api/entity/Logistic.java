package com.dianping.pay.api.entity;

import java.io.Serializable;
import java.util.LinkedList;
import java.util.List;

import com.dianping.api.common.entity.Pair;
import com.dianping.api.common.util.KeyValuePair;
import com.dianping.api.domain.ClientInfo;
import com.dianping.api.domain.Version;
import com.dianping.api.netio.DPEncoder;
import com.dianping.api.util.ApiUtil;
import com.dianping.cat.Cat;

public class Logistic extends DPEncoder implements Serializable{

	/**
	 * 
	 */
	private static final long serialVersionUID = -641955305295856445L;
	
	/* 运送状态 */
	private int status;
	
	/* 公司 */
	private String company;
	
	private String number;
	
	private Pair[] details;

	public int getStatus() {
		return status;
	}

	public void setStatus(int status) {
		this.status = status;
	}

	public String getCompany() {
		return company;
	}

	public void setCompany(String company) {
		this.company = company;
	}

	public String getNumber() {
		return number;
	}

	public void setNumber(String number) {
		this.number = number;
	}

	public Pair[] getDetails() {
		return details;
	}

	public void setDetails(Pair[] details) {
		this.details = details;
	}

	@Override
	public int getClassId() {
        Cat.logEvent("INVALID_METHOD_4", "com.dianping.pay.api.entity.Logistic.getClassId()");
        return ApiUtil.getHash(Logistic.class.getSimpleName());
	}

	public static final LinkedList<KeyValuePair> l = new LinkedList<KeyValuePair>();
	static {
		l.add(new KeyValuePair("status",ApiUtil.getHash("Status")));
		l.add(new KeyValuePair("company", ApiUtil.getHash("Company")));
		l.add(new KeyValuePair("number", ApiUtil.getHash("Number")));
		l.add(new KeyValuePair("details", ApiUtil.getHash("Details")));
	}

	@Override
	public List<KeyValuePair> getSerializeList(ClientInfo clientInfo,
			Version version) {
        Cat.logEvent("INVALID_METHOD_5", "com.dianping.pay.api.entity.Logistic.getSerializeList(com.dianping.api.domain.ClientInfo,com.dianping.api.domain.Version)");
        return l;
	}

}
