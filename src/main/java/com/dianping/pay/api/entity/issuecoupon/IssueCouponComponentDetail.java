package com.dianping.pay.api.entity.issuecoupon;

import com.dianping.mobile.framework.annotation.MobileDo;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * 模型描述：领券组件券列表组件 - 浮层组件
 * 移动之家链接: https://mobile.sankuai.com/studio/model/info/24449
 */
@MobileDo(id = 0xc1d5)
@Data
public class IssueCouponComponentDetail implements Serializable {
    /**
     * 浮层组件标题 （eg：商家优惠券）
     */
    @MobileDo.MobileField(key = 0x24cc)
    private String title;

    /**
     * 各券列表模块
     */
    @MobileDo.MobileField(key = 0x5db6)
    private List<IssueCouponListDetail> couponList;

    /**
     * 投放券
     */
    @MobileDo.MobileField(key = 0x930b)
    private ShopResourcePromotionInfo shopResourcePromotionInfo;

    /**
     * 神会员券信息
     */
    @MobileDo.MobileField(key = 0xe564)
    private List<MagicMemberCouponInfo> magicMemberCouponInfo;

    /**
     * 神会员可购买券包id
     */
    @MobileDo.MobileField(key = 0xd227)
    private List<String> magicalMemberCouponPackageIds;

    /**
     * 领券组件 - 膨胀实验id
     */
    @MobileDo.MobileField(key = 0x2354)
    private List<Integer> inflateExperimentId;

}
