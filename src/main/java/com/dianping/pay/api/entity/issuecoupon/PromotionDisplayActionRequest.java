package com.dianping.pay.api.entity.issuecoupon;

import com.dianping.mobile.framework.annotation.MobileRequest;
import com.dianping.mobile.framework.annotation.MobileRequest.Param;
import com.dianping.mobile.framework.datatypes.IMobileRequest;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import lombok.Data;
import org.apache.commons.lang.math.NumberUtils;


/**
 * <AUTHOR>
 * @date 08/04/2020
 * @time 18:40
 */

@TypeDoc(description = "promotiondisplayaction.bin接口请求参数")
@MobileRequest
@Data
public class PromotionDisplayActionRequest implements IMobileRequest {

    @FieldDoc(description = "点评侧shopuuid")
    @Param(name = "shopuuid", required = false)
    private String shopuuid;

    @FieldDoc(description = "poiid")
    @Param(name = "shopid", required = false)
    @Deprecated
    private Integer shopid;

    @FieldDoc(description = "poiidstring")
    @Param(name = "shopidstring", required = false)
    private String shopidstring;

    @FieldDoc(description = "用户类型  0:点评 1:美团")
    @Param(name = "usertype", required = true)
    private Integer usertype;

    @FieldDoc(description = "诚信字段")
    @Param(name = "cx", required = true)
    private String cx;

    public Long getShopIdL() {
        long shopIdL = NumberUtils.toLong(shopidstring);
        if(shopIdL > 0) {
            return shopIdL;
        }
        if(shopid != null) {
            return shopid.longValue();
        }
        return null;
    }

    public void setShopidL(Long shopidL) {
        this.shopidstring = String.valueOf(shopidL);
    }


}
