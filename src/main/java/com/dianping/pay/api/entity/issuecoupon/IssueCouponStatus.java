package com.dianping.pay.api.entity.issuecoupon;

import com.dianping.mobile.framework.annotation.MobileDo;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * 模型描述：券领取状态
 * 移动之家链接: https://mobile.sankuai.com/studio/model/info/24447
 */
@MobileDo(id = 0x6993)
@Data
public class IssueCouponStatus implements Serializable {

    /**
     * 高亮类型，0：高亮，1：非高亮
     */
    @MobileDo.MobileField(key = 0xe6ac)
    private int highLightType;

    /**
     * 领取文案描述，例如“领取”，“已领取”
     */
    @MobileDo.MobileField(key = 0xd6fa)
    private String comment;

    /**
     * 领取状态，0可领取，1已领取
     */
    @MobileDo.MobileField(key = 0x53f)
    private int status;



}
