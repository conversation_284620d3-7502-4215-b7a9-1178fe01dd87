package com.dianping.pay.api.entity;

import java.io.Serializable;
import java.util.LinkedList;
import java.util.List;

import com.dianping.api.common.util.KeyValuePair;
import com.dianping.api.domain.ClientInfo;
import com.dianping.api.domain.Version;
import com.dianping.api.netio.DPEncoder;
import com.dianping.api.util.ApiUtil;
import com.dianping.cat.Cat;

public class BankList extends DPEncoder implements Serializable {
	
	private List<BankInfo> bankList;
	
	private boolean isEnd = true;

	public List<BankInfo> getBankList() {
		return bankList;
	}

	public void setBankList(List<BankInfo> bankList) {
		this.bankList = bankList;
	}

	public boolean isIsEnd() {
		return isEnd;
	}

	public void setIsEnd(boolean isEnd) {
		this.isEnd = isEnd;
	}

	/**
	 * 
	 */
	private static final long serialVersionUID = -7844387445588792119L;

	@Override
	public int getClassId() {
        Cat.logEvent("INVALID_METHOD_5", "com.dianping.pay.api.entity.BankList.getClassId()");
        return ApiUtil.getHash(BankList.class.getSimpleName());
	}

	public static final LinkedList<KeyValuePair> l = new LinkedList<KeyValuePair>();
	static {
		l.add(new KeyValuePair("bankList",ApiUtil.getHash("BankList")));
		l.add(new KeyValuePair("isEnd",ApiUtil.getHash("IsEnd")));
	}
	
	@Override
	public List<KeyValuePair> getSerializeList(ClientInfo clientInfo,
			Version version) {
        Cat.logEvent("INVALID_METHOD_1", "com.dianping.pay.api.entity.BankList.getSerializeList(com.dianping.api.domain.ClientInfo,com.dianping.api.domain.Version)");
        return l;
	}

}
