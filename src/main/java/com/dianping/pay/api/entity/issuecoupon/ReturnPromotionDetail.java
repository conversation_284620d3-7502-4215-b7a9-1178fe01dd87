package com.dianping.pay.api.entity.issuecoupon;

import com.dianping.mobile.framework.annotation.MobileDo;
import com.dianping.mobile.framework.annotation.MobileDo.MobileField;
import lombok.Data;

import java.io.Serializable;


/**
 * <AUTHOR>
 * @date 08/04/2020
 * @time 18:27
 * 模型描述：返券优惠详情
 * 移动之家链接: https://mobile.sankuai.com/studio/model/info/23276
 */
@MobileDo(id = 0x89d0)
@Data
public class ReturnPromotionDetail implements Serializable {


    /**
     * 返礼详细文案
     */
    @MobileField(key = 0x80de)
    private String returnPromotionDetailText;

    /**
     * 返礼标题
     */
    @MobileField(key = 0xaed7)
    private String returnPromotionDetailTitle;


    /**
     * 前端忽略该字段
     */
    private String summaryInfo;


    /**
     * 返礼类型
     */
    private int bonusType;
}
