package com.dianping.pay.api.entity.issuecoupon;

import com.dianping.mobile.framework.annotation.MobileDo;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/5/16
 */
@Data
@MobileDo(id = 0x60ba)
public class UnifiedIssueMultiCouponResp implements Serializable {

    @FieldDoc(
            description = "领券是否成功 true:成功"
    )
    @MobileDo.MobileField(key = 0xbbb7)
    private boolean beSuccess;

    @MobileDo.MobileField(key = 0x936f)
    private String errorMsg;

    @FieldDoc(
            description = "错误信息"
    )
    @MobileDo.MobileField(key = 0xda9c)
    private List<IssueDetailResult> issueDetail;

}