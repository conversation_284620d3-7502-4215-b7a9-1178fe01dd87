package com.dianping.pay.api.entity.issuecoupon;

import com.dianping.mobile.framework.annotation.MobileRequest;
import com.dianping.pay.api.beans.ApiBaseRequest;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import lombok.Data;

/**
 * <AUTHOR>
 */
@TypeDoc(description = "brandissuecouponaction.bin接口请求参数")
@MobileRequest
@Data
public class BrandActivityIssueCouponRequest extends ApiBaseRequest {

    @FieldDoc(
            description = "商品列表"
    )
    @MobileRequest.Param(name = "brandid")
    private Long brandId;

    @FieldDoc(
            description = "商家券：券批次ID；平台券：券码ID"
    )
    @MobileRequest.Param(name = "bactivityid")
    private Integer bActivityId;

    @FieldDoc(
            description = "发券风控cx"
    )
    @MobileRequest.Param(name = "cx")
    private String cx;

    /**
     * 用户token
     */
    @MobileRequest.Param(name = "token")
    private String token;

    /**
     * 用户类型，0点评，1美团
     */
    @MobileRequest.Param(name = "usertype")
    private Integer usercode;

    @MobileRequest.Param(name = "deviceplatform")
    private String devicePlatform;

    @MobileRequest.Param(name = "clienttype")
    private Integer clienttype;

    @MobileRequest.Param(name = "platform")
    private Integer platform;

}
