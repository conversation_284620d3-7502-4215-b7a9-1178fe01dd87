package com.dianping.pay.api.entity.issuecoupon;

import com.dianping.mobile.framework.annotation.MobileDo;
import java.io.Serializable;
import lombok.Data;

@MobileDo(id = 0x1e46)
@Data
public class CouponTag implements Serializable {

    @MobileDo.MobileField(key = 0x3c48)
    private String icon;

    @MobileDo.MobileField(key = 0xbf9b)
    private String tag;

    @MobileDo.MobileField(key = 0x8f0c)
    private int type;

    @MobileDo.MobileField(key = 0xd850)
    private String couponTimeDesc;

    @MobileDo.MobileField(key = 0xfbe2)
    private String amount;

    @MobileDo.MobileField(key = 0xbe4f)
    private String backgroundPicUrl;

}
