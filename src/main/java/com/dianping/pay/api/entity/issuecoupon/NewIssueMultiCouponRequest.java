package com.dianping.pay.api.entity.issuecoupon;

import com.dianping.mobile.framework.annotation.MobileRequest;
import com.dianping.mobile.framework.datatypes.IMobileRequest;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import lombok.Data;

@TypeDoc(description = "promo/newissuemulticouponaction.bin接口请求参数")
@MobileRequest
@Data
public class NewIssueMultiCouponRequest implements IMobileRequest {
    @FieldDoc(
        description = "发券请求体，josn转化为NewBatchIssueCouponRequest类型"
    )
    @MobileRequest.Param(name = "issuemulticouponrequestbody")
    private String issuemulticouponrequestbody;

}
