package com.dianping.pay.api.entity.issuecoupon;

import com.dianping.api.netio.DPEncoder;
import com.dianping.mobile.framework.annotation.MobileDo;

import java.io.Serializable;
import java.util.List;

/**
 * Created by drin<PERSON> on 18/6/6.
 */
@MobileDo(id = 0x4917)
public class UnifiedIssueCouponComponentDo implements Serializable {
    /**
     * 多个券列表
     */
    @MobileDo.MobileField(key = 0x2112)
    private List<UnifiedIssueCouponListDo> unifiedIssueCouponLists;

    /**
     *
     */
    @MobileDo.MobileField(key = 0x3c48)
    private String icon;

    /**
     * 领券入口-券缩略文案
     */
    @MobileDo.MobileField(key = 0x71b4)
    private String couponText;

    /**
     * 领券入口-券缩略文案(平台券)
     */
    @MobileDo.MobileField(key = 0x92b9)
    private String platformCouponText;

    /**
     * 领券入口文案
     */
    @MobileDo.MobileField(key = 0xc7ee)
    private String entranceText;

    public List<UnifiedIssueCouponListDo> getUnifiedIssueCouponLists() {
        return unifiedIssueCouponLists;
    }

    public void setUnifiedIssueCouponLists(
            List<UnifiedIssueCouponListDo> unifiedIssueCouponLists) {
        this.unifiedIssueCouponLists = unifiedIssueCouponLists;
    }

    public String getIcon() {
        return icon;
    }

    public void setIcon(String icon) {
        this.icon = icon;
    }

    public String getCouponText() {
        return couponText;
    }

    public void setCouponText(String couponText) {
        this.couponText = couponText;
    }

    public String getEntranceText() {
        return entranceText;
    }

    public void setEntranceText(String entranceText) {
        this.entranceText = entranceText;
    }

    public String getPlatformCouponText() {
        return platformCouponText;
    }

    public void setPlatformCouponText(String platformCouponText) {
        this.platformCouponText = platformCouponText;
    }

    @Override
    public String toString() {
        final StringBuilder sb = new StringBuilder("UnifiedIssueCouponComponentDo{");
        sb.append("unifiedIssueCouponLists=").append(unifiedIssueCouponLists);
        sb.append(", icon='").append(icon).append('\'');
        sb.append(", couponText='").append(couponText).append('\'');
        sb.append(", platformCouponText='").append(platformCouponText).append('\'');
        sb.append(", entranceText='").append(entranceText).append('\'');
        sb.append('}');
        return sb.toString();
    }
}
