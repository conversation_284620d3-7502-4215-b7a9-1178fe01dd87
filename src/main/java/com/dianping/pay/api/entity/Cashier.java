package com.dianping.pay.api.entity;

import java.io.Serializable;
import java.util.List;

import com.dianping.api.common.entity.PaymentTool;
import com.dianping.api.common.util.KeyValuePair;
import com.dianping.api.domain.ClientInfo;
import com.dianping.api.domain.Version;
import com.dianping.api.netio.DPEncoder;
import com.dianping.api.util.ApiUtil;
import com.google.common.collect.Lists;

public class Cashier extends DPEncoder implements Serializable {

	private static final long serialVersionUID = -1225073955664007105L;
	private PaymentTool lastPaymentTool;
	private List<PaymentTool> paymentTools;
	private PrePayOrderDTO prePayOrderDTO;
	private boolean canUseBalance;
	private boolean canUseDiscount;
	private double userBalance;
	private boolean userHasDiscount;
    private int showBalanceMode;
    private int showDiscountMode;
    private int showQuantityMode;
    private int showTotalAmountMode;
    private int showPaymentAmountMode;
    private boolean canUseRedEnvelope;
    private double redEnvelopeBalance;
    private boolean canUseGiftCard;
    private double giftCardBalance;
    private int showRedEnvelopeMode;
    private int showGiftCardMode;
    private boolean canCombineDiscountPayment;
    private String confirmContent;
    private boolean needConfirm;
    private int showUserPointMode;
    //会员积分
    private UserPoint userPoint;

    @Override
	public int getClassId() {
		return 0x86ee;
	}
	public static void main(String[] args) {
		System.out.println("0x"+Integer.toHexString(ApiUtil.getHash("UserHasDiscount")));
	}
	private static final List<KeyValuePair> LIST = Lists.newArrayList();
	static {
		LIST.add(new KeyValuePair("lastPaymentTool", 0xfb09));
		LIST.add(new KeyValuePair("paymentTools", 0xa41a));
		LIST.add(new KeyValuePair("prePayOrderDTO", 0x1c38));
		LIST.add(new KeyValuePair("canUseBalance", 0x1f2));
		LIST.add(new KeyValuePair("canUseDiscount", 0x910e));
		LIST.add(new KeyValuePair("userBalance", 0xc542));
		LIST.add(new KeyValuePair("userHasDiscount", 0x1b5f));
		LIST.add(new KeyValuePair("showBalanceMode", 0x2b6f));
        LIST.add(new KeyValuePair("showDiscountMode", 0xe64b));
        LIST.add(new KeyValuePair("showQuantityMode", 0x7726));
        LIST.add(new KeyValuePair("showTotalAmountMode", 0x9689));
        LIST.add(new KeyValuePair("showPaymentAmountMode", 0x302a));
        LIST.add(new KeyValuePair("canUseRedEnvelope", 0x4700));
        LIST.add(new KeyValuePair("redEnvelopeBalance", 0xfefb));
        LIST.add(new KeyValuePair("canUseGiftCard", 0x4852));
        LIST.add(new KeyValuePair("giftCardBalance", 0xd798));
        LIST.add(new KeyValuePair("showRedEnvelopeMode", 0xa074));
        LIST.add(new KeyValuePair("showGiftCardMode", 0xb0d1));
        LIST.add(new KeyValuePair("canCombineDiscountPayment",0x8bba));
        LIST.add(new KeyValuePair("confirmContent",0xa101));
        LIST.add(new KeyValuePair("needConfirm",0x84ac));
        LIST.add(new KeyValuePair("showUserPointMode",0xdbd5));
        LIST.add(new KeyValuePair("userPoint",0xcabc));
	}
	@Override
	public List<KeyValuePair> getSerializeList(ClientInfo clientInfo,
			Version version) {
		return LIST;
	}

	public PaymentTool getLastPaymentTool() {
		return lastPaymentTool;
	}

	public void setLastPaymentTool(PaymentTool lastPaymentTool) {
		this.lastPaymentTool = lastPaymentTool;
	}

	public List<PaymentTool> getPaymentTools() {
		return paymentTools;
	}

	public void setPaymentTools(List<PaymentTool> paymentTools) {
		this.paymentTools = paymentTools;
	}

	public PrePayOrderDTO getPrePayOrderDTO() {
		return prePayOrderDTO;
	}

	public void setPrePayOrderDTO(PrePayOrderDTO prePayOrderDTO) {
		this.prePayOrderDTO = prePayOrderDTO;
	}

	public boolean getCanUseBalance() {
		return canUseBalance;
	}

	public void setCanUseBalance(boolean canUseBalance) {
		this.canUseBalance = canUseBalance;
	}

	public boolean getCanUseDiscount() {
		return canUseDiscount;
	}

	public void setCanUseDiscount(boolean canUseDiscount) {
		this.canUseDiscount = canUseDiscount;
	}

	public double getUserBalance() {
		return userBalance;
	}

	public void setUserBalance(double userBalance) {
		this.userBalance = userBalance;
	}
	public boolean isUserHasDiscount() {
		return userHasDiscount;
	}
	public void setUserHasDiscount(boolean userHasDiscount) {
		this.userHasDiscount = userHasDiscount;
	}

    public int getShowBalanceMode() {
        return showBalanceMode;
    }

    public void setShowBalanceMode(int showBalanceMode) {
        this.showBalanceMode = showBalanceMode;
    }

    public int getShowDiscountMode() {
        return showDiscountMode;
    }

    public void setShowDiscountMode(int showDiscountMode) {
        this.showDiscountMode = showDiscountMode;
    }

    public int getShowQuantityMode() {
        return showQuantityMode;
    }

    public void setShowQuantityMode(int showQuantityMode) {
        this.showQuantityMode = showQuantityMode;
    }

    public int getShowTotalAmountMode() {
        return showTotalAmountMode;
    }

    public void setShowTotalAmountMode(int showTotalAmountMode) {
        this.showTotalAmountMode = showTotalAmountMode;
    }

    public int getShowPaymentAmountMode() {
        return showPaymentAmountMode;
    }

    public void setShowPaymentAmountMode(int showPaymentAmountMode) {
        this.showPaymentAmountMode = showPaymentAmountMode;
    }

    public boolean isCanUseRedEnvelope() {
        return canUseRedEnvelope;
    }

    public void setCanUseRedEnvelope(boolean canUseRedEnvelope) {
        this.canUseRedEnvelope = canUseRedEnvelope;
    }

    public double getRedEnvelopeBalance() {
        return redEnvelopeBalance;
    }

    public void setRedEnvelopeBalance(double redEnvelopeBalance) {
        this.redEnvelopeBalance = redEnvelopeBalance;
    }

    public boolean isCanUseGiftCard() {
        return canUseGiftCard;
    }

    public void setCanUseGiftCard(boolean canUseGiftCard) {
        this.canUseGiftCard = canUseGiftCard;
    }

    public double getGiftCardBalance() {
        return giftCardBalance;
    }

    public void setGiftCardBalance(double giftCardBalance) {
        this.giftCardBalance = giftCardBalance;
    }

    public int getShowRedEnvelopeMode() {
        return showRedEnvelopeMode;
    }

    public void setShowRedEnvelopeMode(int showRedEnvelopeMode) {
        this.showRedEnvelopeMode = showRedEnvelopeMode;
    }

    public int getShowGiftCardMode() {
        return showGiftCardMode;
    }

    public void setShowGiftCardMode(int showGiftCardMode) {
        this.showGiftCardMode = showGiftCardMode;
    }

    public boolean isCanCombineDiscountPayment() {
        return canCombineDiscountPayment;
    }

    public void setCanCombineDiscountPayment(boolean canCombineDiscountPayment) {
        this.canCombineDiscountPayment = canCombineDiscountPayment;
    }

    public boolean isNeedConfirm() {
        return needConfirm;
    }

    public void setNeedConfirm(boolean needConfirm) {
        this.needConfirm = needConfirm;
    }

    public String getConfirmContent() {
        return confirmContent;
    }

    public void setConfirmContent(String confirmContent) {
        this.confirmContent = confirmContent;
    }

    public UserPoint getUserPoint() {
        return userPoint;
    }

    public void setUserPoint(UserPoint userPoint) {
        this.userPoint = userPoint;
    }

    public int getShowUserPointMode() {
        return showUserPointMode;
    }

    public void setShowUserPointMode(int showUserPointMode) {
        this.showUserPointMode = showUserPointMode;
    }
}
