package com.dianping.pay.api.entity;

import com.dianping.api.common.util.KeyValuePair;
import com.dianping.api.domain.ClientInfo;
import com.dianping.api.domain.Version;
import com.dianping.cat.Cat;
import com.dianping.pay.cashier.biz.enums.PaymentRule;
import java.math.BigDecimal;
import java.util.Arrays;
import java.util.List;

/**
 * Created by huawei.li on 15/3/10.
 */
public class HongBaoDiscountTool extends AbstractDiscountTool {
    private static final long serialVersionUID = -5706388895999195329L;
    private double balance;
    private static final List<KeyValuePair> LIST = Arrays.asList(
            new KeyValuePair("ID", 0x91b),
            new KeyValuePair("name", 0xee8f),
            new KeyValuePair("desc", 0x7291),
            new KeyValuePair("canUse", 0x9069),
            new KeyValuePair("mutexDiscountTools", 0x8bc1),
            new KeyValuePair("balance", 0x49bd)
    );
    public HongBaoDiscountTool(){
        this.setName("现金券");
        this.setID(PaymentRule.HONGBAO.code);
        this.setMutexDiscountTools(Arrays.asList(PaymentRule.COUPON.code));
    }
    public HongBaoDiscountTool(double balance){
        this.setName("现金券");
        this.setID(PaymentRule.HONGBAO.code);
        this.setMutexDiscountTools(Arrays.asList(PaymentRule.COUPON.code));
        this.balance = balance;
    }
    public HongBaoDiscountTool(BigDecimal balance){
        this.setName("现金券");
        this.setID(PaymentRule.HONGBAO.code);
        this.setMutexDiscountTools(Arrays.asList(PaymentRule.COUPON.code));
        if(balance!=null){
            this.balance = balance.doubleValue();
        }
    }
    @Override
    public int getClassId() {
        Cat.logEvent("INVALID_METHOD_5", "com.dianping.pay.api.entity.HongBaoDiscountTool.getClassId()");
        return 0x3fb8;
    }

    @Override
    public List<KeyValuePair> getSerializeList(ClientInfo clientInfo, Version version) {
        Cat.logEvent("INVALID_METHOD_5", "com.dianping.pay.api.entity.HongBaoDiscountTool.getSerializeList(com.dianping.api.domain.ClientInfo,com.dianping.api.domain.Version)");
        return LIST;
    }

    public double getBalance() {
        return balance;
    }

    public void setBalance(double balance) {
        this.balance = balance;
    }
}
