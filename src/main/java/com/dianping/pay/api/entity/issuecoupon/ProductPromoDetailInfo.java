package com.dianping.pay.api.entity.issuecoupon;

import com.dianping.mobile.framework.annotation.MobileDo;
import java.io.Serializable;
import lombok.Data;

import java.util.List;

@MobileDo(id = 0x498f)
@Data
public class ProductPromoDetailInfo implements Serializable {

    /**
     * 平台券
     */
    @MobileDo.MobileField(key = 0x174f)
    private List<CouponDetailInfo> platformCoupons;

    /**
     * 商家券
     */
    @MobileDo.MobileField(key = 0x1550)
    private List<CouponDetailInfo> merchantCoupons;

    /**
     * 购物车车多件折扣
     */
    @MobileDo.MobileField(key = 0xbef5)
    private List<BuycarDiscountDetailInfo> buycarDiscounts;

    /**
     * 平台立减信息
     */
    @MobileDo.MobileField(key = 0x3363)
    private List<ReductionDetailInfo> platReductionPromos;

    /**
     * 商家立减信息
     */
    @MobileDo.MobileField(key = 0x8507)
    private List<ReductionDetailInfo> merchantReductionPromos;
}
