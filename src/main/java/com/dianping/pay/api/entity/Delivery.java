package com.dianping.pay.api.entity;

import java.io.Serializable;
import java.util.List;
import java.util.Map;

import com.dianping.api.common.util.KeyValuePair;
import com.dianping.api.domain.ClientInfo;
import com.dianping.api.domain.Version;
import com.dianping.api.netio.DPEncoder;
import com.google.common.collect.Lists;

public class Delivery extends DPEncoder implements Serializable {

	private static final long serialVersionUID = -4971579471875488567L;
	
	public int getId() {
		return id;
	}


	public void setId(int id) {
		this.id = id;
	}


	public String getReceiver() {
		return receiver;
	}


	public void setReceiver(String receiver) {
		this.receiver = receiver;
	}


	public String getPhoneNo() {
		return phoneNo;
	}


	public void setPhoneNo(String phoneNo) {
		this.phoneNo = phoneNo;
	}

	public int getProvince() {
		return province;
	}


	public void setProvince(int province) {
		this.province = province;
	}


	public int getCity() {
		return city;
	}


	public void setCity(int city) {
		this.city = city;
	}


	public int getCounty() {
		return county;
	}


	public void setCounty(int county) {
		this.county = county;
	}


	public String getAddress() {
		return address;
	}


	public void setAddress(String address) {
		this.address = address;
	}
	

	public String getShowAddress() {
		return showAddress;
	}


	public void setShowAddress(String showAddress) {
		this.showAddress = showAddress;
	}


	public String getPostCode() {
		return postCode;
	}


	public void setPostCode(String postCode) {
		this.postCode = postCode;
	}
	

	public int getDeliveryTyteId() {
		return deliveryTyteId;
	}


	public void setDeliveryTyteId(int deliveryTyteId) {
		this.deliveryTyteId = deliveryTyteId;
	}


	public String getDeliveryTypeName() {
		return deliveryTypeName;
	}


	public void setDeliveryTypeName(String deliveryTypeName) {
		this.deliveryTypeName = deliveryTypeName;
	}

	public String getMemo() {
		return memo;
	}


	public void setMemo(String memo) {
		this.memo = memo;
	}


	public Map<String, Integer> getMap() {
		return null;
	}

	private static final List<KeyValuePair> LIST = Lists.newArrayList();
	static {
		LIST.add(new KeyValuePair("id", 0x91b));
		LIST.add(new KeyValuePair("receiver", 0x9da9));
		LIST.add(new KeyValuePair("phoneNo", 0x415));
		LIST.add(new KeyValuePair("province", 0x47ae));
		LIST.add(new KeyValuePair("city", 0xdab));
		LIST.add(new KeyValuePair("county", 0xc5cd));
		LIST.add(new KeyValuePair("address", 0x2d04));
		LIST.add(new KeyValuePair("showAddress", 0x2c33));
		LIST.add(new KeyValuePair("postCode", 0xfe3c));
        LIST.add(new KeyValuePair("memo", 0x897e));
 	}
	
	public List<KeyValuePair> getSerializeList(ClientInfo clientInfo, Version version) {
		return LIST;
	}
	
	private int id;	
	private String receiver; //收货人
	private String phoneNo;  //电话
	private int province;   //省
	private int city;       //市
	private int county;     //区
	private String address;	 //详细地址
	private String showAddress; //省+市+区+详细地址
	private String postCode; //邮编
	private int deliveryTyteId; //配送方式Id
	private String deliveryTypeName;//配送方式name
	private String memo; //备注

	/* (non-Javadoc)
	 * @see com.dianping.api.netio.DPEncoder#getClassId()
	 */
	@Override
	public int getClassId() {
 		return 0xb4fb;
	}
	
	public static void main(String args[]) {
		String name="DeliveryList";
		int i = name.hashCode();
		i = (0xFFFF & i) ^ (i >>> 16);
		System.out.println("x"+intToHex(i));
		}

	
	public static char[] HEX_TABLE = {
	        '0' , '1' , '2' , '3' ,
	        '4' , '5' , '6' , '7' ,
	        '8' , '9' , 'a' , 'b' ,
	        'c' , 'd' , 'e' , 'f' ,
	};

	public static String intToHex( int num ){
	        String sResult = "";
	        while( num > 0 ){
	            sResult = HEX_TABLE[ num % 16 ] + sResult;
	            num = num / 16;
	        }
	        return sResult;
	}


}
