package com.dianping.pay.api.entity.issuecoupon;

import com.dianping.api.common.util.KeyValuePair;
import com.dianping.api.domain.ClientInfo;
import com.dianping.api.domain.Version;
import com.dianping.api.netio.DPEncoder;
import com.dianping.mobile.framework.annotation.MobileDo;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.LinkedList;
import java.util.List;
@MobileDo(id = 0xfe5b)
public class IssueCouponMsg extends DPEncoder implements Serializable {

    @MobileDo.MobileField(key = 0x3e36)
    private String toastMsg;

    @MobileDo.MobileField(key = 0xbc4)
    private String message;

    @MobileDo.MobileField(key = 0x44fb)
    private int errorCode;

    @MobileDo.MobileField(key = 0x7368)
    private String errorMsg;

    @MobileDo.MobileField(key = 0x57b6)
    private List<String> content = new ArrayList<String>();

    public IssueCouponMsg(int errorCode, String errorMsg, String message) {
        this.errorCode = errorCode;
        this.errorMsg = errorMsg;
        this.message = message;
    }

    public IssueCouponMsg(int errorCode, String errorMsg, String message, String toastMsg) {
        this(errorCode, errorMsg, message);
        this.toastMsg = toastMsg;
    }

    public IssueCouponMsg(int errorCode, String errorMsg, String message, List<String> content) {
        this(errorCode, errorMsg, message);
        this.content = content;
    }

    private static final LinkedList<KeyValuePair> l = new LinkedList<KeyValuePair>();
    static {
        l.add(new KeyValuePair("message", 0xbc4));
        l.add(new KeyValuePair("errorCode", 0x44fb));
        l.add(new KeyValuePair("errorMsg", 0x7368));
        l.add(new KeyValuePair("content", 0x57b6));
    }

    @Override
    public int getClassId() {
        return 0xfe5b;
    }

    @Override
    public List<KeyValuePair> getSerializeList(ClientInfo clientInfo, Version version) {
        return l;
    }

    public String getMessage() {
        return message;
    }

    public void setMessage(String message) {
        this.message = message;
    }

    public int getErrorCode() {
        return errorCode;
    }

    public void setErrorCode(int errorCode) {
        this.errorCode = errorCode;
    }

    public String getErrorMsg() {
        return errorMsg;
    }

    public void setErrorMsg(String errorMsg) {
        this.errorMsg = errorMsg;
    }

    public List<String> getContent() {
        return content;
    }

    public void setContent(List<String> content) {
        this.content = content;
    }

    public String getToastMsg() {
        return toastMsg;
    }

    public void setToastMsg(String toastMsg) {
        this.toastMsg = toastMsg;
    }
}
