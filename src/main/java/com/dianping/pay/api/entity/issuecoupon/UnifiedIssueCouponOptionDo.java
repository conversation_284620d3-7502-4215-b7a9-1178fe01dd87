package com.dianping.pay.api.entity.issuecoupon;

import com.dianping.mobile.framework.annotation.MobileDo;

import java.io.Serializable;

/**
 * Created by drintu on 18/6/6.
 */

@MobileDo(id = 0x4dd6)
public class UnifiedIssueCouponOptionDo implements Serializable {
    /**
     * 券 ID
     */
    @Deprecated
    @MobileDo.MobileField(key = 0xe4cf)
    private int couponGroupId;

    /**
     * 券批次ID 、 平台券时为券码ID
     */
    @MobileDo.MobileField(key = 0x9256)
    private String unifiedCouponGroupId;

    /**
     * 描述
     */
    @MobileDo.MobileField(key = 0x7291)
    private String desc;

    /**
     * 券金额
     */
    @MobileDo.MobileField(key = 0x4967)
    private double amount;

    /**
     * 券门槛
     */
    @MobileDo.MobileField(key = 0xa84b)
    private double priceLimit;

    /**
     * 是否为平台券
     */
    @MobileDo.MobileField(key = 0xa8c)
    private boolean isPlatformCoupon;

    /**
     * 金额描述
     */
    @MobileDo.MobileField(key = 0x92f5)
    private String amountDesc;

    /**
     * 券标题
     */
    @MobileDo.MobileField(key = 0x36e9)
    private String title;

    /**
     *  已领取 Icon
     */
    @MobileDo.MobileField(key = 0x7ce8)
    private String issuedIcon;

    /**
     * 领券/使用按钮文案
     */
    @MobileDo.MobileField(key = 0xb0b2)
    private String issueButtonText;

    /**
     * 领券选项状态枚举
     */
    @MobileDo.MobileField(key = 0x2820)
    private IssueCouponOptionStatusDo status;

    /**
     * 使用链接
     */
    @MobileDo.MobileField(key = 0x81a5)
    private String toUseUrl;

    public int getCouponGroupId() {
        return couponGroupId;
    }

    public void setCouponGroupId(int couponGroupId) {
        this.couponGroupId = couponGroupId;
    }

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }

    public double getAmount() {
        return amount;
    }

    public void setAmount(double amount) {
        this.amount = amount;
    }

    public String getAmountDesc() {
        return amountDesc;
    }

    public void setAmountDesc(String amountDesc) {
        this.amountDesc = amountDesc;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public String getIssuedIcon() {
        return issuedIcon;
    }

    public void setIssuedIcon(String issuedIcon) {
        this.issuedIcon = issuedIcon;
    }

    public String getIssueButtonText() {
        return issueButtonText;
    }

    public void setIssueButtonText(String issueButtonText) {
        this.issueButtonText = issueButtonText;
    }

    public IssueCouponOptionStatusDo getStatus() {
        return status;
    }

    public void setStatus(IssueCouponOptionStatusDo status) {
        this.status = status;
    }

    public String getToUseUrl() {
        return toUseUrl;
    }

    public void setToUseUrl(String toUseUrl) {
        this.toUseUrl = toUseUrl;
    }

    public String getUnifiedCouponGroupId() {
        return unifiedCouponGroupId;
    }

    public void setUnifiedCouponGroupId(String unifiedCouponGroupId) {
        this.unifiedCouponGroupId = unifiedCouponGroupId;
    }

    public double getPriceLimit() {
        return priceLimit;
    }

    public void setPriceLimit(double priceLimit) {
        this.priceLimit = priceLimit;
    }

    public boolean isPlatformCoupon() {
        return isPlatformCoupon;
    }

    public void setPlatformCoupon(boolean platformCoupon) {
        isPlatformCoupon = platformCoupon;
    }

    @Override
    public String toString() {
        final StringBuilder sb = new StringBuilder("UnifiedIssueCouponOptionDo{");
        sb.append("couponGroupId=").append(couponGroupId);
        sb.append(", unifiedCouponGroupId='").append(unifiedCouponGroupId).append('\'');
        sb.append(", desc='").append(desc).append('\'');
        sb.append(", amount=").append(amount);
        sb.append(", priceLimit=").append(priceLimit);
        sb.append(", isPlatformCoupon=").append(isPlatformCoupon);
        sb.append(", amountDesc='").append(amountDesc).append('\'');
        sb.append(", title='").append(title).append('\'');
        sb.append(", issuedIcon='").append(issuedIcon).append('\'');
        sb.append(", issueButtonText='").append(issueButtonText).append('\'');
        sb.append(", status=").append(status);
        sb.append(", toUseUrl='").append(toUseUrl).append('\'');
        sb.append('}');
        return sb.toString();
    }
}