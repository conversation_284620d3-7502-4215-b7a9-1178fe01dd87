
package com.dianping.pay.api.entity;

import java.io.Serializable;
import java.util.LinkedList;
import java.util.List;

import com.dianping.api.common.util.KeyValuePair;
import com.dianping.api.domain.ClientInfo;
import com.dianping.api.domain.Version;
import com.dianping.api.netio.DPEncoder;
import com.dianping.api.util.ApiUtil;
import com.dianping.cat.Cat;

/**
 * <AUTHOR>
 */
public class RefundInfo extends DPEncoder implements Serializable {

	private static final long serialVersionUID = -7934603378183010062L;

	/* 退款金额 */
	private String amount;

	/* 提示信息 */
	private String note;

	/* 可退券列表 */
	private String receiptList;

	/* 是否允许退款至点评账户余额 */
	private boolean refundToBalance;

	public String getAmount() {
		return amount;
	}

	public void setAmount(String amount) {
		this.amount = amount;
	}

	public String getNote() {
		return note;
	}

	public void setNote(String note) {
		this.note = note;
	}

	public String getReceiptList() {
		return receiptList;
	}

	public void setReceiptList(String receiptList) {
		this.receiptList = receiptList;
	}

	@Override
	public int getClassId() {
        Cat.logEvent("INVALID_METHOD_3", "com.dianping.pay.api.entity.RefundInfo.getClassId()");
        return ApiUtil.getHash(RefundInfo.class.getSimpleName());
	}

	public static final LinkedList<KeyValuePair> LIST = new LinkedList<KeyValuePair>();
	static {
		LIST.add(new KeyValuePair("amount", ApiUtil.getHash("Amount")));
		LIST.add(new KeyValuePair("note", ApiUtil.getHash("Note")));
		LIST.add(new KeyValuePair("receiptList", ApiUtil.getHash("ReceiptList")));
		LIST.add(new KeyValuePair("refundToBalance", 0x7f13));
	}

	@Override
	public List<KeyValuePair> getSerializeList(ClientInfo clientInfo,
			Version version) {
        Cat.logEvent("INVALID_METHOD_4", "com.dianping.pay.api.entity.RefundInfo.getSerializeList(com.dianping.api.domain.ClientInfo,com.dianping.api.domain.Version)");
        return LIST;
	}

	public boolean isRefundToBalance() {
		return refundToBalance;
	}

	public void setRefundToBalance(boolean refundToBalance) {
		this.refundToBalance = refundToBalance;
	}

}
