package com.dianping.pay.api.entity.promodesk;

import com.dianping.api.common.util.KeyValuePair;
import com.dianping.api.domain.ClientInfo;
import com.dianping.api.domain.Version;
import com.dianping.api.netio.DPEncoder;
import com.dianping.pay.cashier.biz.enums.PaymentRule;
import com.dianping.pay.common.enums.ProductCode;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.LinkedList;
import java.util.List;

public class PointPromoTool extends DPEncoder implements Serializable {

    private int id;
    private boolean canUse;
    private int pointBalance;
    private List<PointExchangeRule> pointExchangeRules;
    private List<Integer> mutexTools;
    private int productCode;


    public PointPromoTool() {
        this.id = PaymentRule.POINT.code;
        this.mutexTools = new ArrayList<Integer>();
        this.pointExchangeRules = new ArrayList<PointExchangeRule>();
        this.productCode = ProductCode.TUANGOU.getCode();
    }

    private static final LinkedList<KeyValuePair> l = new LinkedList<KeyValuePair>();
    static {
        l.add(new KeyValuePair("id", 0x91b));
        l.add(new KeyValuePair("mutexTools", 0x2468));
        l.add(new KeyValuePair("canUse", 0x9069));
        l.add(new KeyValuePair("productCode", 0x8d25));
        l.add(new KeyValuePair("pointExchangeRules", 0x2b1f));
        l.add(new KeyValuePair("pointBalance", 0x9c71));
    }

    @Override
    public int getClassId() {
        return 0x44ff;
    }

    @Override
    public List<KeyValuePair> getSerializeList(ClientInfo clientInfo, Version version) {
        return l;
    }

    public int getId() {
        return id;
    }

    public void setId(int id) {
        this.id = id;
    }

    public boolean isCanUse() {
        return canUse;
    }

    public void setCanUse(boolean canUse) {
        this.canUse = canUse;
    }

    public int getPointBalance() {
        return pointBalance;
    }

    public void setPointBalance(int pointBalance) {
        this.pointBalance = pointBalance;
    }

    public List<PointExchangeRule> getPointExchangeRules() {
        return pointExchangeRules;
    }

    public void setPointExchangeRules(List<PointExchangeRule> pointExchangeRules) {
        this.pointExchangeRules = pointExchangeRules;
    }

    public List<Integer> getMutexTools() {
        return mutexTools;
    }

    public void setMutexTools(List<Integer> mutexTools) {
        this.mutexTools = mutexTools;
    }

    public int getProductCode() {
        return productCode;
    }

    public void setProductCode(int productCode) {
        this.productCode = productCode;
    }
}
