package com.dianping.pay.api.entity;

import com.dianping.api.framework.Controller;
import com.dianping.pay.api.beans.ApiBaseRequest;

/**
 * Created by xiaoxiao.li on 3/19/15.
 */
public class GetPayCashierRequest extends ApiBaseRequest {
    private int orderID;
    private int productCode;

    public GetPayCashierRequest(Controller controller) {
        super(controller);
        this.orderID = controller.getParametersMap().getInteger("orderid");
        this.productCode = controller.getParametersMap().getInteger("productcode");
    }
    public int getOrderID() {
        return orderID;
    }

    public void setOrderID(int orderID) {
        this.orderID = orderID;
    }

    public int getProductCode() {
        return productCode;
    }

    public void setProductCode(int productCode) {
        this.productCode = productCode;
    }
}
