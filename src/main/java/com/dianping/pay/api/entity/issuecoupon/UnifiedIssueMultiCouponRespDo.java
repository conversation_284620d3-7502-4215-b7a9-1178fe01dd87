package com.dianping.pay.api.entity.issuecoupon;

import com.dianping.mobile.framework.annotation.MobileDo;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

@TypeDoc(
        description = "领券结果"
)
@Data
public class UnifiedIssueMultiCouponRespDo implements Serializable {
    @FieldDoc(
            description = "领券是否成功 true:成功"
    )
    private boolean isSuccess;

    private String errorMsg;

    @FieldDoc(
            description = "错误信息"
    )
    private List<UnifiedIssueDetailResult> issueDetail;

}