package com.dianping.pay.api.entity.issuecoupon;

import com.dianping.mobile.framework.annotation.MobileDo;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@MobileDo(id = 0x4580)
@Data
public class CouponConcisePromoInfo implements Serializable {
    /**
     * 一种优惠信息类型对应一个PromoInfoItem
     */
    @MobileDo.MobileField(key = 0x926)
    private List<CouponPromoInfo> promoInfoItems;

    /**
     * 引导跳转链接
     */
    @MobileDo.MobileField(key = 0x3001)
    private String leadRedirectUrl;

    /**
     * 引导动作，0：无引导 1：弹出浮层 2：跳转leadRedirectUrl
     */
    @MobileDo.MobileField(key = 0x1c63)
    private int leadAction;

    /**
     * 引导文案，e.g. 去领券/查看优惠/更多优惠
     */
    @MobileDo.MobileField(key = 0x17aa)
    private String leadText;

    /**
     * 名称，比如:优惠
     */
    @MobileDo.MobileField(key = 0x24cc)
    private String title;
}