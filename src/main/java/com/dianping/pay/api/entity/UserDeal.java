package com.dianping.pay.api.entity;

import java.io.Serializable;

public class UserDeal implements Serializable {

	private static final long serialVersionUID = 5869198895555082225L;
	private long userId;
	@Deprecated
	private int shopId;
	private long shopIdL;
	private int dealGroupId;
	private int rank;
	private double distance = 0;

	public long getUserId() {
		return userId;
	}

	public void setUserId(long userId) {
		this.userId = userId;
	}

	public int getShopId() {
		return shopId;
	}

	public void setShopId(int shopId) {
		this.shopId = shopId;
	}

	public long getShopIdL() {
		return shopIdL;
	}

	public void setShopIdL(long shopIdL) {
		this.shopIdL = shopIdL;
	}

	public int getDealGroupId() {
		return dealGroupId;
	}

	public void setDealGroupId(int dealGroupId) {
		this.dealGroupId = dealGroupId;
	}

	public int getRank() {
		return rank;
	}

	public void setRank(int rank) {
		this.rank = rank;
	}

	public double getDistance() {
		return distance;
	}

	public void setDistance(double distance) {
		this.distance = distance;
	}

}
