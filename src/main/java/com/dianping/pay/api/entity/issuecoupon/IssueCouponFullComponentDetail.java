package com.dianping.pay.api.entity.issuecoupon;

import com.dianping.mobile.framework.annotation.MobileDo;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * 模型描述：领券组件完整信息
 * 移动之家链接: https://mobile.sankuai.com/studio/model/info/24452
 */
@MobileDo(id = 0x4736)
@Data
public class IssueCouponFullComponentDetail implements Serializable {
    /**
     * 领券组件 - 外层缩略信息组件
     */
    @MobileDo.MobileField(key = 0xf886)
    private IssueCouponSimpleComponentDetail simpleCouponListInfo;

    /**
     * 领券组件 - 浮层详细信息组件
     */
    @MobileDo.MobileField(key = 0xa1af)
    private IssueCouponComponentDetail couponListInfo;

    /**
     * 前端ab实验埋点
     */
    @MobileDo.MobileField(key = 0x7bb1)
    private String moduleAbInfo4Front;

}

