package com.dianping.pay.api.entity;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

import com.dianping.api.common.entity.Pair;
import com.dianping.api.common.util.KeyValuePair;
import com.dianping.api.domain.ClientInfo;
import com.dianping.api.domain.Enum.AppType;
import com.dianping.api.domain.Enum.ClientType;
import com.dianping.api.domain.Version;
import com.dianping.api.netio.DPEncoder;
import com.dianping.api.util.ApiUtil;
import com.google.common.collect.Lists;

public class Order extends DPEncoder implements Serializable{
	
	private static final long serialVersionUID = -6137689094127390989L;
	
	private int id;
	private int count;
	private String photo;
	private String title;
	private double price;      //兼容老版本返回double类型
	private BigDecimal priceB; //服务器端计算使用bigdecimal，不使用double
	private String priceStr;   //团购native之后返回String类型
	private String statusMemo;
	private Date time;//下单时间
	private long addtime;
	private String extraTitle;
	private List<Pair> extra;
	private OrderDeal orderDeal;
	private OrderLottery orderLottery;
	private OrderDeliver orderDeliver;
	private int refundStatus; //退款状态
	
	// hd wp
	private String unitPrice;         //	单价		
	private String shipment;          //	发货状态（非配送单传空）ipad专有
	private List<Pair> logisticsInfo; //	物流信息（非配送单传空）ipad专有 LogisticsInfo中包含显示内容与物流网站的URL需要显示的内容pair的type值为0，url的type为1
	private int dealType;             //	1-团购券 2-实物 3-抽奖	
	private int status;               //	1-未付款 2-已付
	private Pair deliveryType;        //    配送时间
	private Discount discount;			  //    优惠券

	// tuan-app
	Delivery delivery;
	String invoiceTitle;
	DealGroup relativeDeal;
	
	public Discount getDiscount() {
		return discount;
	}
	public void setDiscount(Discount discount) {
		this.discount = discount;
	}
	public int getId() {
		return id;
	}
	public void setId(int id) {
		this.id = id;
	}
	
	public int getRefundStatus() {
		return refundStatus;
	}
	public void setRefundStatus(int refundStatus) {
		this.refundStatus = refundStatus;
	}
	public int getCount() {
		return count;
	}
	public void setCount(int count) {
		this.count = count;
	}
	public String getPhoto() {
		return photo;
	}
	public void setPhoto(String photo) {
		this.photo = photo;
	}
	public double getPrice() {
		return price;
	}
	public void setPrice(double price) {
		this.price = price;
	}
	public BigDecimal getPriceB() {
		return priceB;
	}
	public void setPriceB(BigDecimal priceB) {
		this.priceB = priceB;
		this.priceStr = priceB.toPlainString();
	}
	public String getPriceStr() {
		return priceStr; 
	}
	public void setPriceStr(String priceStr) {
		this.priceStr = priceStr;
	}
	public String getStatusMemo() {
		return statusMemo;
	}
	public void setStatusMemo(String statusMemo) {
		this.statusMemo = statusMemo;
	}
	public Date getTime() {
		return time;
	}
	public void setTime(Date time) {
		this.time = time;
	}
	public String getExtraTitle() {
		return extraTitle;
	}
	public void setExtraTitle(String extraTitle) {
		this.extraTitle = extraTitle;
	}
	public List<Pair> getExtra() {
		return extra;
	}
	public void setExtra(List<Pair> extra) {
		this.extra = extra;
	}
	public OrderDeal getOrderDeal() {
		return orderDeal;
	}
	public void setOrderDeal(OrderDeal orderDeal) {
		this.orderDeal = orderDeal;
	}
	public OrderLottery getOrderLottery() {
		return orderLottery;
	}
	public void setOrderLottery(OrderLottery orderLottery) {
		this.orderLottery = orderLottery;
	}
	public OrderDeliver getOrderDeliver() {
		return orderDeliver;
	}
	public void setOrderDeliver(OrderDeliver orderDeliver) {
		this.orderDeliver = orderDeliver;
	}
	public long getAddtime() {
		return addtime;
	}
	public void setAddtime(long addtime) {
		this.addtime = addtime;
	}
	public String getTitle() {
		return title;
	}
	public void setTitle(String title) {
		this.title = title;
	}
	public String getUnitPrice() {
		return unitPrice;
	}
	public void setUnitPrice(String unitPrice) {
		this.unitPrice = unitPrice;
	}
	public String getShipment() {
		return shipment;
	}
	public void setShipment(String shipment) {
		this.shipment = shipment;
	}
	public List<Pair> getLogisticsInfo() {
		return logisticsInfo;
	}
	public void setLogisticsInfo(List<Pair> logisticsInfo) {
		this.logisticsInfo = logisticsInfo;
	}
	public int getDealType() {
		return dealType;
	}
	public void setDealType(int dealType) {
		this.dealType = dealType;
	}
	public int getStatus() {
		return status;
	}
	public void setStatus(int status) {
		this.status = status;
	}
    public Delivery getDelivery() {
		return delivery;
	}
	public void setDelivery(Delivery delivery) {
		this.delivery = delivery;
	}
	public String getInvoiceTitle() {
		return invoiceTitle;
	}
	public void setInvoiceTitle(String invoiceTitle) {
		this.invoiceTitle = invoiceTitle;
	}
	public DealGroup getRelativeDeal() {
		return relativeDeal;
	}
	public void setRelativeDeal(DealGroup relativeDeal) {
		this.relativeDeal = relativeDeal;
	}
	public Pair getDeliveryType() {
		return deliveryType;
	}
	public void setDeliveryType(Pair deliveryType) {
		this.deliveryType = deliveryType;
	}
 
	static final List<KeyValuePair> LEGACY=Lists.newArrayList();
	static{
		LEGACY.add(new KeyValuePair("id",0x91b));
		LEGACY.add(new KeyValuePair("title",0x36e9));
		LEGACY.add(new KeyValuePair("count", 0x630b));
		LEGACY.add(new KeyValuePair("photo",0x4a8a));
		LEGACY.add(new KeyValuePair("price",0xc5b5));
		LEGACY.add(new KeyValuePair("statusMemo",0x69fa));
		LEGACY.add(new KeyValuePair("time",0xc6ca));
		LEGACY.add(new KeyValuePair("extraTitle",0x268d));
		LEGACY.add(new KeyValuePair("extra",0xa7f4));
	}
	
	static final List<KeyValuePair> LIST=Lists.newArrayList();
	static{
		LIST.add(new KeyValuePair("id",0x91b));
		LIST.add(new KeyValuePair("title",0x36e9));
		LIST.add(new KeyValuePair("count", 0x630b));
		LIST.add(new KeyValuePair("photo",0x4a8a));
		//LIST.add(new KeyValuePair("price",0xc5b5));
		LIST.add(new KeyValuePair("priceStr",0x6777));
		LIST.add(new KeyValuePair("statusMemo",0x69fa));
		LIST.add(new KeyValuePair("time",0xc6ca));
		LIST.add(new KeyValuePair("extraTitle",0x268d));
		LIST.add(new KeyValuePair("extra",0xa7f4));
		LIST.add(new KeyValuePair("unitPrice",0x72));
		LIST.add(new KeyValuePair("shipment",0x30a9));
		LIST.add(new KeyValuePair("logisticsInfo",0xecea));
		LIST.add(new KeyValuePair("dealType",0x6fa3));
		LIST.add(new KeyValuePair("status",0x2820));
		LIST.add(new KeyValuePair("deliveryType",0x6b1f));
		LIST.add(new KeyValuePair("discount", 0xcd43));	
		
		LIST.add(new KeyValuePair("delivery", 0xb4fb));	
		LIST.add(new KeyValuePair("invoiceTitle", 0xe649));	
		LIST.add(new KeyValuePair("relativeDeal", 0x7cf5));
		LIST.add(new KeyValuePair("refundStatus", ApiUtil.getHash("RefundStatus")));
	}
	
	@Override
	public List<KeyValuePair> getSerializeList(ClientInfo clientInfo, Version version ){
		if(clientInfo.getApp() == AppType.TG || (clientInfo.getApp() == AppType.Main && Version.compareTo(version, new Version("5.3")) >= 0)) {
			return LIST;
		} else if((clientInfo.getClient().value == ClientType.iPadHd.value&&Version.compareTo(version, new Version("2.0"))>=0)
					|| (clientInfo.getClient().value == ClientType.WinPhone.value&&Version.compareTo(version, new Version("1.5"))>=0)) { 
			return LIST;
		} else {
			return LEGACY;
		}
	}
	
	/* (non-Javadoc)
	 * @see com.dianping.api.netio.DPEncoder#getClassId()
	 */
	@Override
	public int getClassId() {
		return 0x93a0;
	}

}


