package com.dianping.pay.api.entity.issuecoupon;

import com.dianping.cat.Cat;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import org.springframework.http.HttpStatus;


@JsonInclude(value = Include.NON_EMPTY, content = Include.NON_NULL)
public class ResultWrapper {
    @FieldDoc(
            description = "状态码"
    )
    private int code;
    @FieldDoc(
            description = "错误信息"
    )
    private String msg;
    @FieldDoc(
            description = "返回数据"
    )
    private Object content;

    public ResultWrapper() {
    }

    public ResultWrapper(int code, String msg, Object content) {
        this.code = code;
        this.msg = msg;
        this.content = content;
    }

    public static final ResultWrapper ERROR = new ResultWrapper(HttpStatus.INTERNAL_SERVER_ERROR.value(), "服务器内部错误", null);

    public static ResultWrapper success(Object content) {
        return new ResultWrapper(HttpStatus.OK.value(), "success", content);
    }

    public static ResultWrapper fail(String msg) {
        Cat.logEvent("INVALID_METHOD_2", "com.dianping.pay.api.entity.issuecoupon.ResultWrapper.fail(java.lang.String)");
        return new ResultWrapper(HttpStatus.BAD_REQUEST.value(), msg, null);
    }

    public static ResultWrapper fail(int code, String msg) {
        Cat.logEvent("INVALID_METHOD_5", "com.dianping.pay.api.entity.issuecoupon.ResultWrapper.fail(int,java.lang.String)");
        return new ResultWrapper(code, msg, null);
    }

    public int getCode() {
        return code;
    }

    public void setCode(int code) {
        this.code = code;
    }

    public String getMsg() {
        return msg;
    }

    public void setMsg(String msg) {
        this.msg = msg;
    }

    public Object getContent() {
        return content;
    }

    public void setContent(Object content) {
        this.content = content;
    }
}
