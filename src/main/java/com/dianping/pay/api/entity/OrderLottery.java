
package com.dianping.pay.api.entity;

import java.util.List;

import com.dianping.cat.Cat;
import com.google.common.collect.Lists;

public class OrderLottery {

	private List<String> lotteryNumList = Lists.newArrayList();

	public OrderLottery() {
	}

	public void setLotteryNumber(String lotteryNumber) {
        Cat.logEvent("INVALID_METHOD_3", "com.dianping.pay.api.entity.OrderLottery.setLotteryNumber(java.lang.String)");
        lotteryNumList.add(lotteryNumber);
	}

	public List<String> getLotteryNumberList() {
        Cat.logEvent("INVALID_METHOD_5", "com.dianping.pay.api.entity.OrderLottery.getLotteryNumberList()");
        return lotteryNumList;
	}
}
