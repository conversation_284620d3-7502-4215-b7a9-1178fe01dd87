package com.dianping.pay.api.entity;

import java.io.Serializable;
import java.util.List;

import com.dianping.api.common.util.KeyValuePair;
import com.dianping.api.domain.ClientInfo;
import com.dianping.api.domain.Version;
import com.dianping.api.netio.DPEncoder;
import com.dianping.api.util.ApiUtil;
import com.google.common.collect.Lists;

public class BindInfo extends DPEncoder implements Serializable{

	private static final long serialVersionUID = -6893467038542792664L;
	
	/* 银行帐号 */
	private String cardNo;
	
	/* 姓名 */
	private String name;
	
	/* 身份证号 */
	private String identity;
	
	/* 手机号 */
	private String mobile;
	
	/* 状态 */
	private String status;
	
	/* 结果状态码 */
	private String resultCode;
	
	/* 结果描述 */
	private String description;
	
	/* 签约号 */
	private String externalSignNo;

	public String getExternalSignNo() {
		return externalSignNo;
	}

	public void setExternalSignNo(String externalSignNo) {
		this.externalSignNo = externalSignNo;
	}

	public String getCardNo() {
		return cardNo;
	}

	public void setCardNo(String cardNo) {
		this.cardNo = cardNo;
	}

	public String getName() {
		return name;
	}

	public void setName(String name) {
		this.name = name;
	}

	public String getIdentity() {
		return identity;
	}

	public void setIdentity(String identity) {
		this.identity = identity;
	}

	public String getMobile() {
		return mobile;
	}

	public void setMobile(String mobile) {
		this.mobile = mobile;
	}

	public String getStatus() {
		return status;
	}

	public void setStatus(String status) {
		this.status = status;
	}

	public String getResultCode() {
		return resultCode;
	}

	public void setResultCode(String resultCode) {
		this.resultCode = resultCode;
	}

	public String getDescription() {
		return description;
	}

	public void setDescription(String description) {
		this.description = description;
	}

	@Override
	public int getClassId() {
		return ApiUtil.getHash(BindInfo.class.getSimpleName());
	}

	static final List<KeyValuePair> LIST = Lists.newArrayList();
	static{
		LIST.add(new KeyValuePair("cardNo",ApiUtil.getHash("CardNo")));
		LIST.add(new KeyValuePair("name", ApiUtil.getHash("Name")));
		LIST.add(new KeyValuePair("identity", ApiUtil.getHash("Identity")));
		LIST.add(new KeyValuePair("mobile", ApiUtil.getHash("Mobile")));
		LIST.add(new KeyValuePair("status", ApiUtil.getHash("Status")));
		LIST.add(new KeyValuePair("resultCode", ApiUtil.getHash("ResultCode")));
		LIST.add(new KeyValuePair("description", ApiUtil.getHash("Description")));
		LIST.add(new KeyValuePair("externalSignNo", ApiUtil.getHash("SignNo")));
	}
	
	@Override
	public List<KeyValuePair> getSerializeList(ClientInfo clientInfo,
			Version version) {
		return LIST;
	}

}
