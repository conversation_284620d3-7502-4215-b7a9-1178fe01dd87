package com.dianping.pay.api.entity.issuecoupon;

import com.dianping.mobile.framework.annotation.MobileRequest;
import com.dianping.mobile.framework.datatypes.IMobileRequest;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2021/1/19
 */
@TypeDoc(description = "shopcartpromodisplay.bin接口请求参数")
@MobileRequest
@Data
public class ShopCartProduct implements IMobileRequest {

    /**
     * 0：团单；1：商品sku
     */
    @MobileRequest.Param(name = "producttype")
    private Integer producttype;

    /**
     * 商品id，如果是团购类型，则是团单id(dealGroupId)
     */
    @MobileRequest.Param(name = "productid")
    private Integer productid;

}
