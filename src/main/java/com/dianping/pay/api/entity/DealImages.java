
package com.dianping.pay.api.entity;

import java.util.List;

import com.dianping.cat.Cat;
import com.google.common.collect.Lists;

public class DealImages {

	private List<String> imageList = Lists.newArrayList();

	public void setImage(String image) {
        Cat.logEvent("INVALID_METHOD_4", "com.dianping.pay.api.entity.DealImages.setImage(java.lang.String)");
        imageList.add(image);
	}

	public List<String> getImageList() {
        Cat.logEvent("INVALID_METHOD_3", "com.dianping.pay.api.entity.DealImages.getImageList()");
        return imageList;
	}

}
