package com.dianping.pay.api.entity;

import java.io.Serializable;
import java.util.List;

import com.dianping.api.common.util.KeyValuePair;
import com.dianping.api.domain.ClientInfo;
import com.dianping.api.domain.Version;
import com.dianping.api.netio.DPEncoder;
import com.dianping.api.util.ApiUtil;
import com.dianping.cat.Cat;
import com.google.common.collect.Lists;

public class BankInfo extends DPEncoder implements Serializable{
	
	/* 是否需要有效期 */
	private boolean needValidDate;
	
	/* 银行中文名称 */
	private String bankName;
	
	/* 银行E文名称 */
	private String bankId;
	
	/* 银行图片logo，BASE64编码 */
	private String bankLogo;

	public boolean isNeedValidDate() {
		return needValidDate;
	}

	public void setNeedValidDate(boolean needValidDate) {
		this.needValidDate = needValidDate;
	}

	public String getBankName() {
		return bankName;
	}

	public void setBankName(String bankName) {
		this.bankName = bankName;
	}

	public String getBankId() {
		return bankId;
	}

	public void setBankId(String bankId) {
		this.bankId = bankId;
	}

	public String getBankLogo() {
		return bankLogo;
	}

	public void setBankLogo(String bankLogo) {
		this.bankLogo = bankLogo;
	}

	/**
	 * 
	 */
	private static final long serialVersionUID = -5662536232218985639L;

	@Override
	public int getClassId() {
        Cat.logEvent("INVALID_METHOD_5", "com.dianping.pay.api.entity.BankInfo.getClassId()");
        return ApiUtil.getHash(BankInfo.class.getSimpleName());
	}

	static final List<KeyValuePair> LIST = Lists.newArrayList();
	static {
		LIST.add(new KeyValuePair("needValidDate", ApiUtil.getHash("NeedValidDate")));
		LIST.add(new KeyValuePair("bankName", ApiUtil.getHash("BankName")));
		LIST.add(new KeyValuePair("bankId", ApiUtil.getHash("BankId")));
		LIST.add(new KeyValuePair("bankLogo", ApiUtil.getHash("BankLogo")));
	}
	
	@Override
	public List<KeyValuePair> getSerializeList(ClientInfo clientInfo,
			Version version) {
        Cat.logEvent("INVALID_METHOD_4", "com.dianping.pay.api.entity.BankInfo.getSerializeList(com.dianping.api.domain.ClientInfo,com.dianping.api.domain.Version)");
        return LIST;
	}

}
