package com.dianping.pay.api.entity.issuecoupon;

import com.dianping.mobile.framework.annotation.MobileDo;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import java.io.Serializable;
import java.util.List;
import java.util.Map;
import lombok.Data;

@Data
@MobileDo(id = 0x1d4b)
public class NewIssueMultiCouponResp implements Serializable {
    @FieldDoc(
        description = "领券是否成功, 所有发券都成功返回true"
    )
    @MobileDo.MobileField(key = 0xbbb7)
    private boolean beSuccess;

    /**
     * 领券结果
     */
    @MobileDo.MobileField(key = 0x936f)
    private String errorMsg;

    @FieldDoc(
        description = "发券结果详情"
    )
    @MobileDo.MobileField(key = 0x9db5)
    private List<NewIssueDetailResult> issueDetailResultList;
}
