package com.dianping.pay.api.entity.promodesk;

import com.dianping.api.common.util.KeyValuePair;
import com.dianping.api.domain.ClientInfo;
import com.dianping.api.domain.Version;
import com.dianping.api.netio.DPEncoder;
import com.dianping.cat.Cat;

import java.io.Serializable;
import java.util.LinkedList;
import java.util.List;

public class DiscountPromoEventGroup extends DPEncoder implements Serializable {

    private List<DiscountPromoEvent> discountPromoEvents;
    private List<Integer> mutexTools;
    private int productCode;
    private List<Integer> mutexProductCodes;
    private String icon;

    private static final LinkedList<KeyValuePair> l = new LinkedList<KeyValuePair>();
    static {
        l.add(new KeyValuePair("discountPromoEvents", 0x5f3));
        l.add(new KeyValuePair("mutexTools", 0x2468));
        l.add(new KeyValuePair("productCode", 0x8d25));
        l.add(new KeyValuePair("mutexProductCodes", 0xfa7));
        l.add(new KeyValuePair("icon", 0xb0bb));
    }

    @Override
    public int getClassId() {
        Cat.logEvent("INVALID_METHOD_4", "com.dianping.pay.api.entity.promodesk.DiscountPromoEventGroup.getClassId()");
        return 0x49c2;
    }

    @Override
    public List<KeyValuePair> getSerializeList(ClientInfo clientInfo, Version version) {
        Cat.logEvent("INVALID_METHOD_5", "com.dianping.pay.api.entity.promodesk.DiscountPromoEventGroup.getSerializeList(com.dianping.api.domain.ClientInfo,com.dianping.api.domain.Version)");
        return l;
    }

    public List<DiscountPromoEvent> getDiscountPromoEvents() {
        return discountPromoEvents;
    }

    public void setDiscountPromoEvents(List<DiscountPromoEvent> discountPromoEvents) {
        this.discountPromoEvents = discountPromoEvents;
    }

    public List<Integer> getMutexTools() {
        return mutexTools;
    }

    public void setMutexTools(List<Integer> mutexTools) {
        this.mutexTools = mutexTools;
    }

    public int getProductCode() {
        return productCode;
    }

    public void setProductCode(int productCode) {
        this.productCode = productCode;
    }

    public String getIcon() {
        return icon;
    }

    public void setIcon(String icon) {
        this.icon = icon;
    }

    public List<Integer> getMutexProductCodes() {
        return mutexProductCodes;
    }

    public void setMutexProductCodes(List<Integer> mutexProductCodes) {
        this.mutexProductCodes = mutexProductCodes;
    }
}
