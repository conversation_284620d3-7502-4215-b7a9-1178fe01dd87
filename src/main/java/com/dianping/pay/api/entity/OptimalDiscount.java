package com.dianping.pay.api.entity;

import com.dianping.api.common.util.KeyValuePair;
import com.dianping.api.domain.ClientInfo;
import com.dianping.api.domain.Version;
import com.dianping.api.netio.DPEncoder;
import com.dianping.cat.Cat;

import java.io.Serializable;
import java.util.Arrays;
import java.util.List;

/**
 * Created by huawei.li on 15/4/16.
 */
public class OptimalDiscount extends DPEncoder implements Serializable {
    private boolean usePoint; // always false
    private boolean useHongBao;
    private Discount coupon;
    private int reductionId;
    private int quantity;

    private static final List<KeyValuePair> LIST = Arrays.asList(
            new KeyValuePair("usePoint", 0x924c),
            new KeyValuePair("useHongBao", 0xf0d6),
            new KeyValuePair("coupon", 0xbce1),
            new KeyValuePair("reductionId", 0x2518),
            new KeyValuePair("quantity", 0x7fe9)
    );

    @Override
    public int getClassId() {
        Cat.logEvent("INVALID_METHOD_1", "com.dianping.pay.api.entity.OptimalDiscount.getClassId()");
        return 0x31ec;
    }

    @Override
    public List<KeyValuePair> getSerializeList(ClientInfo clientInfo, Version version) {
        Cat.logEvent("INVALID_METHOD_3", "com.dianping.pay.api.entity.OptimalDiscount.getSerializeList(com.dianping.api.domain.ClientInfo,com.dianping.api.domain.Version)");
        return LIST;
    }

    public boolean isUsePoint() {
        return usePoint;
    }

    public void setUsePoint(boolean usePoint) {
        this.usePoint = usePoint;
    }

    public boolean isUseHongBao() {
        return useHongBao;
    }

    public void setUseHongBao(boolean useHongBao) {
        this.useHongBao = useHongBao;
    }

    public Discount getCoupon() {
        return coupon;
    }

    public void setCoupon(Discount coupon) {
        this.coupon = coupon;
    }

    public int getReductionId() {
        return reductionId;
    }

    public void setReductionId(int reductionId) {
        this.reductionId = reductionId;
    }

    public int getQuantity() {
        return quantity;
    }

    public void setQuantity(int quantity) {
        this.quantity = quantity;
    }
}
