package com.dianping.pay.api.entity;

import java.util.List;

import com.google.common.collect.Lists;

/**
 * <AUTHOR>
 *
 */
public class DealCategoryList {
	
	private List<DealCategory> dealCategoryList = Lists.newArrayList();

	public List<DealCategory> getDealCategoryList() {
		return dealCategoryList;
	}

	public void setDealCategoryList(DealCategory dealCategory) {
		dealCategoryList.add(dealCategory);
	}
	
	

}
