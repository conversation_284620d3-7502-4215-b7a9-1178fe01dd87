
package com.dianping.pay.api.entity;

import java.io.Serializable;
import java.util.List;

import com.dianping.api.common.util.KeyValuePair;
import com.dianping.api.domain.ClientInfo;
import com.dianping.api.domain.Version;
import com.dianping.api.netio.DPEncoder;
import com.dianping.cat.Cat;
import com.google.common.collect.Lists;

public class CityConfig extends DPEncoder implements Serializable, Cloneable {

	private static final long serialVersionUID = 5710091427994341117L;

	public static final List<KeyValuePair> LIST = Lists.newArrayList();
	static {
		LIST.add(new KeyValuePair("cityId", 0xa7b4));
		LIST.add(new KeyValuePair("isNearByEnabled", 0x7e94));
	}

	@Override
	public List<KeyValuePair> getSerializeList(ClientInfo clientInfo, Version version) {
        Cat.logEvent("INVALID_METHOD_3", "com.dianping.pay.api.entity.CityConfig.getSerializeList(com.dianping.api.domain.ClientInfo,com.dianping.api.domain.Version)");
        return LIST;
	}

	/*
	 * (non-Javadoc)
	 * @see com.dianping.api.netio.DPEncoder#getClassId()
	 */
	@Override
	public int getClassId() {
        Cat.logEvent("INVALID_METHOD_1", "com.dianping.pay.api.entity.CityConfig.getClassId()");
        return 0xa265;
	}

	public int getCityId() {
		return cityId;
	}

	public void setCityId(int cityId) {
		this.cityId = cityId;
	}

	public boolean isNearByEnabled() {
        Cat.logEvent("INVALID_METHOD_1", "com.dianping.pay.api.entity.CityConfig.isNearByEnabled()");
        return isNearByEnabled;
	}

	public boolean isIsNearByEnabled() {
		return isNearByEnabled;
	}

	public void setIsNearByEnabled(boolean isNearByEnabled) {
		this.isNearByEnabled = isNearByEnabled;
	}

	private int cityId;
	private boolean isNearByEnabled;

}
