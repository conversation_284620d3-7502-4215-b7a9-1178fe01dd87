package com.dianping.pay.api.entity.issuecoupon;

import com.dianping.mobile.framework.annotation.MobileDo;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2023/5/16
 */
@Data
@MobileDo(id = 0x6642)
public class IssueDetailResult {

    @MobileDo.MobileField(key = 0xbbb7)
    private boolean beSuccess;

    @MobileDo.MobileField(key = 0xe7ec)
    private String resultMessage;

    @MobileDo.MobileField(key = 0x9256)
    private String unifiedCouponGroupId;

    @MobileDo.MobileField(key = 0xadf0)
    private String unifiedCouponId;

    @MobileDo.MobileField(key = 0x31b)
    private String beginTime;

    @MobileDo.MobileField(key = 0xca7b)
    private String endTime;

    @MobileDo.MobileField(key = 0x1445)
    private String couponGroupName;

    @MobileDo.MobileField(key = 0x842)
    private String useUrl;

    @MobileDo.MobileField(key = 0x3e36)
    private String toastMsg;

}
