package com.dianping.pay.api.entity.issuecoupon;

import com.dianping.mobile.framework.annotation.MobileDo;
import lombok.Data;

import java.io.Serializable;

@Data
@MobileDo(id = 0xbcba)
public class CouponActivityDetail implements Serializable {

    /**
     *
     */
    @MobileDo.MobileField(key = 0xd6fa)
    private String comment;

    /**
     * 券活动状态：1可参加,2进行中,3已领取
     */
    @MobileDo.MobileField(key = 0xf790)
    private int exposeStatus;

    /**
     * 活动倒计时时间
     */
    @MobileDo.MobileField(key = 0x8646)
    private long countdownTime;

    /**
     * 描述是否要高亮
     */
    @MobileDo.MobileField(key = 0x5f1c)
    private boolean descHighlight;

    /**
     * 副标题是否高亮
     */
    @MobileDo.MobileField(key = 0xe0db)
    private boolean subTitleHighlight;

    /**
     * 活动描述
     */
    @MobileDo.MobileField(key = 0xfebf)
    private String desc;

    /**
     * 活动副标题
     */
    @MobileDo.MobileField(key = 0xd894)
    private String subTitle;

    /**
     * 商家券活动id，用于活动分享
     */
    @MobileDo.MobileField(key = 0xe91)
    private String activityId;

    /**
     * 分享图片
     */
    @MobileDo.MobileField(key = 0x3a81)
    private String sharePic;
}