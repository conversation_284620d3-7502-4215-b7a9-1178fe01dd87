package com.dianping.pay.api.entity.issuecoupon;

import lombok.Data;

@Data
public class BrandCouponInfo {

    /**
     * 商家券活动id
     */
    private long bActivityId;

    /**
     * 品牌侧边logo
     */
    private String verticalLogo;

    /**
     * 品牌标题logo
     */
    private String transverseLogo;

    /**
     * 券名称
     */
    private String couponGroupName;

    /**
     * 券面额 (单位元，返回参数中不带“元”)
     */
    private String discountAmount;

    /**
     * 使用规则描述
     */
    private String useRuleDesc;

    /**
     * 使用门槛描述
     */
    private String priceLimitDesc;

    /**
     * 使用时间描述
     */
    private String timeDesc;

    /**
     * 可领取状态：0：未来可领、1：当前可领、2：已领取、3：已领光
     */
    private int issueStatus;

    /**
     * 领取文案描述
     */
    private String issueDesc;

    /**
     * 券详情页链接，当用户有未使用的在线券时，会展示券详情页
     */
    private String couponDetailUrl;


    /**
     * 右上角的领取角标，表征领取了多少张
     */
    private String issueTag;

    /**
     * 是否展示侧边按钮
     */
    private boolean showIssueButton;


    private String brandName;

    private String brandEngName;

}
