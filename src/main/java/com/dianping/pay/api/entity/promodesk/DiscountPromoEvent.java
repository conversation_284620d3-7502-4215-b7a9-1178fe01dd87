package com.dianping.pay.api.entity.promodesk;

import com.dianping.api.common.util.KeyValuePair;
import com.dianping.api.domain.ClientInfo;
import com.dianping.api.domain.Version;
import com.dianping.api.netio.DPEncoder;
import com.dianping.cat.Cat;

import java.io.Serializable;
import java.util.LinkedList;
import java.util.List;

public class DiscountPromoEvent extends DPEncoder implements Serializable {

    private int id;
    private String title;
    private String desc;
    private double promoAmount;
    private double orderPriceLimit;
    private String promoCipher;
    private boolean canUse;

    private DiscountPromoEventGroup group;

    public DiscountPromoEvent(DiscountPromoEventGroup group) {
        this.group = group;
    }

    private static final LinkedList<KeyValuePair> l = new LinkedList<KeyValuePair>();
    static {
        l.add(new KeyValuePair("id", 0x91b));
        l.add(new KeyValuePair("title", 0x36e9));
        l.add(new KeyValuePair("desc", 0x7291));
        l.add(new KeyValuePair("promoAmount", 0x5f8c));
        l.add(new KeyValuePair("orderPriceLimit", 0x11e0));
        l.add(new KeyValuePair("promoCipher", 0xc706));
        l.add(new KeyValuePair("canUse", 0x9069));
    }

    @Override
    public int getClassId() {
        Cat.logEvent("INVALID_METHOD_3", "com.dianping.pay.api.entity.promodesk.DiscountPromoEvent.getClassId()");
        return 0xfc5f;
    }

    @Override
    public List<KeyValuePair> getSerializeList(ClientInfo clientInfo, Version version) {
        Cat.logEvent("INVALID_METHOD_1", "com.dianping.pay.api.entity.promodesk.DiscountPromoEvent.getSerializeList(com.dianping.api.domain.ClientInfo,com.dianping.api.domain.Version)");
        return l;
    }

    public int getId() {
        return id;
    }

    public void setId(int id) {
        this.id = id;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }

    public double getPromoAmount() {
        return promoAmount;
    }

    public void setPromoAmount(double promoAmount) {
        this.promoAmount = promoAmount;
    }

    public double getOrderPriceLimit() {
        return orderPriceLimit;
    }

    public void setOrderPriceLimit(double orderPriceLimit) {
        this.orderPriceLimit = orderPriceLimit;
    }

    public String getPromoCipher() {
        return promoCipher;
    }

    public void setPromoCipher(String promoCipher) {
        this.promoCipher = promoCipher;
    }

    public boolean isCanUse() {
        return canUse;
    }

    public void setCanUse(boolean canUse) {
        this.canUse = canUse;
    }

    public List<Integer> getMutexPromoTools() {
        Cat.logEvent("INVALID_METHOD_4", "com.dianping.pay.api.entity.promodesk.DiscountPromoEvent.getMutexPromoTools()");
        return this.group != null ? group.getMutexTools() : null;
    }
}
