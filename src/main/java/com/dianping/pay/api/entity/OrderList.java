
package com.dianping.pay.api.entity;

import java.io.Serializable;
import java.util.List;

import com.dianping.api.common.util.KeyValuePair;
import com.dianping.api.domain.ClientInfo;
import com.dianping.api.domain.ResultList;
import com.dianping.api.domain.Version;
import com.dianping.cat.Cat;
import com.google.common.collect.Lists;

public class OrderList extends ResultList<Order> implements Serializable {

	private static final long serialVersionUID = 1L;
	private String link;
	private boolean isShaiDanEnabled;

	public String getLink() {
		return link;
	}

	public void setLink(String link) {
		this.link = link;
	}

	public void addOrder(Order order) {
        Cat.logEvent("INVALID_METHOD_3", "com.dianping.pay.api.entity.OrderList.addOrder(com.dianping.pay.api.entity.Order)");
        if (this.list == null) {
			list = Lists.newArrayList();
		}
		list.add(order);
	}

	static final List<KeyValuePair> LIST = Lists.newArrayList();
	static {
		LIST.addAll(ResultList.RESULTLIST);
		LIST.add(new KeyValuePair("link", 0x243e));
		LIST.add(new KeyValuePair("isShaiDanEnabled", 0x0095));
	}

	@Override
	public List<KeyValuePair> getSerializeList(ClientInfo clientInfo, Version version) {
        Cat.logEvent("INVALID_METHOD_3", "com.dianping.pay.api.entity.OrderList.getSerializeList(com.dianping.api.domain.ClientInfo,com.dianping.api.domain.Version)");
        return LIST;
	}

	/*
	 * (non-Javadoc)
	 * @see com.dianping.api.netio.DPEncoder#getClassId()
	 */
	@Override
	public int getClassId() {
        Cat.logEvent("INVALID_METHOD_1", "com.dianping.pay.api.entity.OrderList.getClassId()");
        return 0x14fb;
	}

	public boolean isIsShaiDanEnabled() {
		return isShaiDanEnabled;
	}

	public void setIsShaiDanEnabled(boolean isShaiDanEnabled) {
		this.isShaiDanEnabled = isShaiDanEnabled;
	}

}
