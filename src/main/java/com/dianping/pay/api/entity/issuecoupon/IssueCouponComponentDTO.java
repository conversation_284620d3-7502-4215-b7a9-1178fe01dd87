package com.dianping.pay.api.entity.issuecoupon;

import com.dianping.mobile.framework.annotation.MobileDo;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * 模型描述：领券组件结构 （含打点信息）
 * 移动之家链接: https://mobile.sankuai.com/studio/model/info/24453
 */
@MobileDo(id = 0xc09c)
@Data
public class IssueCouponComponentDTO implements Serializable {

    /**
     *
     */
    @MobileDo.MobileField(key = 0x41f0)
    private IssueCouponFullComponentDetail couponViewType;

    /**
     * 打点信息
     */
    @MobileDo.MobileField(key = 0xbeb4)
    private IssueCouponComponentOceanDTO ocean;

}
