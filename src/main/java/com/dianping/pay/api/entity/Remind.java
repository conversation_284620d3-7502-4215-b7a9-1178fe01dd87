package com.dianping.pay.api.entity;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

import com.dianping.api.common.util.KeyValuePair;
import com.dianping.api.domain.ClientInfo;
import com.dianping.api.domain.Version;
import com.dianping.api.netio.DPEncoder;
import com.google.common.collect.Lists;

public class Remind extends DPEncoder implements Serializable {

	private static final long serialVersionUID = -6191375133618155437L;

	private int shopGroupId;
	private String shopGroupName;
	private int cityId;
	private int dealGroupId;
	private boolean isRemind;
	private Date addDate = new Date(0);

	public static List<KeyValuePair> LIST = Lists.newArrayList();
	static {
		LIST.add(new KeyValuePair("shopGroupId", 0x2b05));
		LIST.add(new KeyValuePair("shopGroupName", 0xf5cf));
		LIST.add(new KeyValuePair("cityId", 0xa7b4));
		LIST.add(new KeyValuePair("dealGroupId", 0x6ef2));
		LIST.add(new KeyValuePair("isRemind", 0xc2bd));
		LIST.add(new KeyValuePair("addDate", 0x2574));
	}

	@Override
	public List<KeyValuePair> getSerializeList(ClientInfo clientInfo,
			Version version) {
		return LIST;
	}

	/*
	 * (non-Javadoc)
	 * 
	 * @see com.dianping.api.netio.DPEncoder#getClassId()
	 */
	@Override
	public int getClassId() {
		return 0x5c8a;
	}

	public int getShopGroupId() {
		return shopGroupId;
	}

	public void setShopGroupId(int shopGroupId) {
		this.shopGroupId = shopGroupId;
	}

	public String getShopGroupName() {
		return shopGroupName;
	}

	public void setShopGroupName(String shopGroupName) {
		this.shopGroupName = shopGroupName;
	}

	public int getCityId() {
		return cityId;
	}

	public void setCityId(int cityId) {
		this.cityId = cityId;
	}

	public int getDealGroupId() {
		return dealGroupId;
	}

	public void setDealGroupId(int dealGroupId) {
		this.dealGroupId = dealGroupId;
	}

	public boolean getIsRemind() {
		return isRemind;
	}

	public void setIsRemind(boolean isRemind) {
		this.isRemind = isRemind;
	}

	public Date getAddDate() {
		return addDate;
	}

	public void setAddDate(Date addDate) {
		this.addDate = addDate;
	}

}
