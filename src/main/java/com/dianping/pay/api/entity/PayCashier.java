package com.dianping.pay.api.entity;

import com.dianping.api.common.entity.PaymentTool;
import com.dianping.api.common.util.KeyValuePair;
import com.dianping.api.domain.ClientInfo;
import com.dianping.api.domain.Version;
import com.dianping.api.netio.DPEncoder;
import com.google.common.collect.Lists;

import java.io.Serializable;
import java.util.List;

/**
 * Created by xiaoxiao.li on 3/10/15.
 */
public class PayCashier extends DPEncoder implements Serializable {

    private static final long serialVersionUID = -8641396273360843254L;

    private List<PaymentTool> paymentTools;
    private boolean canUseBalance;
    private double userBalance;
    private String confirmContent;
    private boolean needConfirm;
    private double amount;
    private double discountAmount;
    private String productTitle;
    private String productInfo;
    private CountDownInfo countDownInfo;
    private int count;


    @Override
    public int getClassId() {
        return 0x8387;
    }

    private static final List<KeyValuePair> LIST = Lists.newArrayList();
    static {
        LIST.add(new KeyValuePair("paymentTools",0xa41a));
        LIST.add(new KeyValuePair("canUseBalance",0x1f2));
        LIST.add(new KeyValuePair("userBalance",0xc542));
        LIST.add(new KeyValuePair("confirmContent",0xa101));
        LIST.add(new KeyValuePair("needConfirm",0x84ac));
        LIST.add(new KeyValuePair("amount",0x4967));
        LIST.add(new KeyValuePair("discountAmount",0x522e));
        LIST.add(new KeyValuePair("productTitle",0x336d));
        LIST.add(new KeyValuePair("productInfo",0x3461));
        LIST.add(new KeyValuePair("countDownInfo",0xc32));
        LIST.add(new KeyValuePair("count",0x630b));
    }

    @Override
    public List<KeyValuePair> getSerializeList(ClientInfo clientInfo, Version version) {
        return LIST;
    }

    public List<PaymentTool> getPaymentTools() {
        return paymentTools;
    }

    public void setPaymentTools(List<PaymentTool> paymentTools) {
        this.paymentTools = paymentTools;
    }

    public boolean isCanUseBalance() {
        return canUseBalance;
    }

    public void setCanUseBalance(boolean canUseBalance) {
        this.canUseBalance = canUseBalance;
    }

    public double getUserBalance() {
        return userBalance;
    }

    public void setUserBalance(double userBalance) {
        this.userBalance = userBalance;
    }

    public String getConfirmContent() {
        return confirmContent;
    }

    public void setConfirmContent(String confirmContent) {
        this.confirmContent = confirmContent;
    }

    public boolean isNeedConfirm() {
        return needConfirm;
    }

    public void setNeedConfirm(boolean needConfirm) {
        this.needConfirm = needConfirm;
    }

    public double getAmount() {
        return amount;
    }

    public void setAmount(double amount) {
        this.amount = amount;
    }

    public double getDiscountAmount() {
        return discountAmount;
    }

    public void setDiscountAmount(double discountAmount) {
        this.discountAmount = discountAmount;
    }

    public String getProductTitle() {
        return productTitle;
    }

    public void setProductTitle(String productTitle) {
        this.productTitle = productTitle;
    }

    public String getProductInfo() {
        return productInfo;
    }

    public void setProductInfo(String productInfo) {
        this.productInfo = productInfo;
    }

    public CountDownInfo getCountDownInfo() {
        return countDownInfo;
    }

    public void setCountDownInfo(CountDownInfo countDownInfo) {
        this.countDownInfo = countDownInfo;
    }

    public int getCount() {
        return count;
    }

    public void setCount(int count) {
        this.count = count;
    }
}
