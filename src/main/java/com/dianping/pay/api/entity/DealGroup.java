package com.dianping.pay.api.entity;

import com.dianping.api.common.entity.Pair;
import com.dianping.api.common.util.KeyValuePair;
import com.dianping.api.domain.ClientInfo;
import com.dianping.api.domain.Enum.AppType;
import com.dianping.api.domain.Enum.ClientType;
import com.dianping.api.domain.Version;
import com.dianping.api.netio.DPEncoder;
import com.dianping.api.util.ApiUtil;
import com.dianping.tuangou.remote.api.deal.dto.NaviTagSelectionDTO;
import com.dianping.tuangou.remote.api.deal.dto.RegionDTO;
import com.google.common.collect.Lists;

import java.io.Serializable;
import java.util.Arrays;
import java.util.Date;
import java.util.List;

public class DealGroup extends DPEncoder implements Serializable, Cloneable {
	
	private static final long serialVersionUID = -83023815046568609L;

	// private int version = -1;
	private long endDate;
	private long startDate;
	private double originalPrice;
	private int buyerCounter;//keep changing
	private int distance;	
	private DealLocation dealLocation;
	private int cityId;
	private boolean interested;
	private boolean isLimitPerUser;
    private boolean isMemberCard;

    public boolean isIsMemberCard() {
        return isMemberCard;
    }

    public void setIsMemberCard(boolean memberCard) {
        this.isMemberCard = memberCard;
    }
	
	public boolean isIsLimitPerUser() {
		return isLimitPerUser;
	}

	public void setIsLimitPerUser(boolean isLimitPerUser) {
		this.isLimitPerUser = isLimitPerUser;
	}

	public boolean isInterested() {
		return interested;
	}

	public void setInterested(boolean interested) {
		this.interested = interested;
	}

	public int getCityId() {
		return cityId;
	}


	public void setCityId(int cityId) {
		this.cityId = cityId;
	}


	public int getDistance() {
		return distance;
	}


	public void setDistance(int distance) {
		this.distance = distance;
	}


	public long getEndDate() {
		return endDate;
	}


	public void setEndDate(long endDate) {
		this.endDate = endDate;
	}


	public double getOriginalPrice() {
		return originalPrice;
	}

	public DealLocation getDealLocation() {
		return dealLocation;
	}

	public void setDealLocation(DealLocation dealLocation) {
		this.dealLocation = dealLocation;
	}

	public void setOriginalPrice(double originalPrice) {
		this.originalPrice = originalPrice;
	}
	private String memo; //keep changing
	private int id;
	private int dealId;
	private int dealGroupId;
	private String dealIdStr;
	private String title;
	private String imageUrl;
	private double price;      //兼容老版本返回double类型
//	private BigDecimal priceB; //服务器端计算使用bigdecimal，不使用double 
//	private String priceStr;   //团购native之后返回String类型
//	private String onClickLink; // 未定
	
	private String prefix;
	private String shortTitle;
	private String titleDesc;
	private String bigImageUrl;
//	private String currentRegionName;
	private String regionPrefix;
	/**
	 * 0x01 ==新单  
	   0x02 ==卖光了
       0x04==已结束
       0x08==无法购买(显示灰色抢购)
       0x10 ==即将开始
       0x20 ==正常单
	 */
	private int status;
	private int remainCount;
	private int maxJoin;
	private List<String> detailPhotos;
	private Date time=new Date();
	private List<Pair> extra;
	private String dealDetail;  //团购详情
	private List<Integer> categoryIds;
	private List<Integer> regionIds ;
	private DealImages detailPics;
	private List<String> dealImageList;
	private DealRegionList regionNavs;
	private DealCategoryList categoryNavs;
	private DealShopList shopList;
	private String today;
	private boolean isToday;
	private String specialPoint;
	private String notice;
	
	//团购app
	private String contentTitle;
	private boolean refund;
	private int buyLimit;
	private List<Pair> deliveryType;
	private boolean hasReceipt;
	private int buyMixCount;
	
	/**
	 * 开团提醒按钮状态 (v1.3)
	 * 1=不显示开团提醒按钮;
	 * 2=显示添加按钮, 点击请求addremindgn.bin,
 	 * 3=显示添加按钮, 点击请求getremindgn.bin, 转到开团提醒商户选择页 
	 * 4=显示取消按钮, 点击请求delremindgn.bin
	*/
	private int tuanRemindStatus;
	
	/**
	 * 是否免预约 (v1.3)
	 */
	private boolean mianYuYue;
	
	/**
	 * 从右到左各个bit依次表示具有某种标签
	 * 0x1: 免预约
	 */
	private int tag;
	
	/**
	 *  最近商户的经纬度
	 */
	private double lat;
	private double lng;
	
	// 4.6  new propertities for interface getALLDeals 
	private String shopIdsStr;
	private String[] shopIds;
	private int shopId;
	
	// wp  ipad  native  groupon need
	private double discount;	 	        //折扣	
	private List<Pair> detailInfo;          //菜品展示、环境介绍、小编说等信息 DetailInfo中如果该Pair的内容是标题，则其type值为1；文字内容，type=2；图片内容，type=3 内容放在Pair的uid
	private List<DealComment> dealComments; //会员说列表		
	private List<DealSelect> dealSelects;   //	套餐列表		
	private int dealType;                   // 1-团购券 2-实物 3-抽奖	
	private boolean canUseBalance = true;	    	//是否允许余额支付			
	private boolean canUseDiscount = true;         //是否允许使用抵用券/优惠代码			
	
	// five-yuan offer
	private String destinationPrefix;
	private List<RegionDTO> destinationList;
	// voucher offer
	private List<NaviTagSelectionDTO> tags;
	
	public static final List<KeyValuePair> IPAD2LEGACY_WP_LIST = Lists.newArrayList();
	static {
		IPAD2LEGACY_WP_LIST.add(new KeyValuePair("id",0x91b));
		IPAD2LEGACY_WP_LIST.add(new KeyValuePair("titleDesc", 0x36e9));
		IPAD2LEGACY_WP_LIST.add(new KeyValuePair("imageUrl",0x4a8a));
		IPAD2LEGACY_WP_LIST.add(new KeyValuePair("buyerCounter",0x630b));
		IPAD2LEGACY_WP_LIST.add(new KeyValuePair("price",0xc5b5));
		IPAD2LEGACY_WP_LIST.add(new KeyValuePair("memo",0x897e));
		//allMap.put("onClickLink",0);//未定
		IPAD2LEGACY_WP_LIST.add(new KeyValuePair("lat",0xbe8a)); // 会经常变，但和客户端确认 不回传
		IPAD2LEGACY_WP_LIST.add(new KeyValuePair("lng",0x562d)); // 会经常变，但和客户端确认 不回传
		IPAD2LEGACY_WP_LIST.add(new KeyValuePair("shortTitle",0x24d4));
		IPAD2LEGACY_WP_LIST.add(new KeyValuePair("bigImageUrl",0x7be5));
		IPAD2LEGACY_WP_LIST.add(new KeyValuePair("originalPrice",0x7c03));
		IPAD2LEGACY_WP_LIST.add(new KeyValuePair("categoryIds",0x9ea0));
		IPAD2LEGACY_WP_LIST.add(new KeyValuePair("regionIds",0x7d9b));
		IPAD2LEGACY_WP_LIST.add(new KeyValuePair("regionPrefix",0x5795));
		IPAD2LEGACY_WP_LIST.add(new KeyValuePair("status",0x2820));
		IPAD2LEGACY_WP_LIST.add(new KeyValuePair("remainCount",0xe854));
		IPAD2LEGACY_WP_LIST.add(new KeyValuePair("shopIdsStr",0xb58f));
		IPAD2LEGACY_WP_LIST.add(new KeyValuePair("time",0xc6ca));
		IPAD2LEGACY_WP_LIST.add(new KeyValuePair("dealImageList",0x83df));
		IPAD2LEGACY_WP_LIST.add(new KeyValuePair("extra",0xa7f4));
		IPAD2LEGACY_WP_LIST.add(new KeyValuePair("dealId",0xff01));
		
		IPAD2LEGACY_WP_LIST.add(new KeyValuePair("discount",0xcd43));
		IPAD2LEGACY_WP_LIST.add(new KeyValuePair("detailInfo",0x5b7));
		IPAD2LEGACY_WP_LIST.add(new KeyValuePair("dealComments",0xa0af));
		IPAD2LEGACY_WP_LIST.add(new KeyValuePair("dealSelects",0x6ee6));
		IPAD2LEGACY_WP_LIST.add(new KeyValuePair("dealType",0x6fa3));
		IPAD2LEGACY_WP_LIST.add(new KeyValuePair("canUseBalance",0x1f2));
		IPAD2LEGACY_WP_LIST.add(new KeyValuePair("canUseDiscount",0x910e));
		IPAD2LEGACY_WP_LIST.add(new KeyValuePair("extraCategoryList",0xa5f3));
		IPAD2LEGACY_WP_LIST.add(new KeyValuePair("extraRegionList",0x8dfb));
		IPAD2LEGACY_WP_LIST.add(new KeyValuePair("interested",ApiUtil.getHash("Interested")));
        IPAD2LEGACY_WP_LIST.add(new KeyValuePair("isLimitPerUser",ApiUtil.getHash("IsLimitPerUser")));
	}
	
	public static final List<KeyValuePair> LIST = Lists.newArrayList();
	static {
		LIST.add(new KeyValuePair("id",0x91b));
		LIST.add(new KeyValuePair("titleDesc", 0x36e9));
		LIST.add(new KeyValuePair("imageUrl",0x4a8a));
		LIST.add(new KeyValuePair("buyerCounter",0x630b));
		LIST.add(new KeyValuePair("price",0xc5b5));
		LIST.add(new KeyValuePair("memo",0x897e));
		//allMap.put("onClickLink",0);//未定
		LIST.add(new KeyValuePair("lat",0xbe8a)); // 会经常变，但和客户端确认 不回传
		LIST.add(new KeyValuePair("lng",0x562d)); // 会经常变，但和客户端确认 不回传
		LIST.add(new KeyValuePair("shortTitle",0x24d4));
		LIST.add(new KeyValuePair("bigImageUrl",0x7be5));
		LIST.add(new KeyValuePair("originalPrice",0x7c03));
		LIST.add(new KeyValuePair("categoryIds",0x9ea0));
		LIST.add(new KeyValuePair("regionIds",0x7d9b));
		LIST.add(new KeyValuePair("regionPrefix",0x5795));
		LIST.add(new KeyValuePair("status",0x2820));
		LIST.add(new KeyValuePair("remainCount",0xe854));
		LIST.add(new KeyValuePair("shopIdsStr",0xb58f));
		LIST.add(new KeyValuePair("time",0xc6ca));
		LIST.add(new KeyValuePair("dealImageList",0x83df));
		LIST.add(new KeyValuePair("extra",0xa7f4));
		LIST.add(new KeyValuePair("dealId",0xff61));
		LIST.add(new KeyValuePair("dealId",0xff01));// merged from ipad-list
		
		LIST.add(new KeyValuePair("discount",0xcd43));
		LIST.add(new KeyValuePair("detailInfo",0x5b7));
		LIST.add(new KeyValuePair("dealComments",0xa0af));
		LIST.add(new KeyValuePair("dealSelects",0x6ee6));
		LIST.add(new KeyValuePair("dealType",0x6fa3));
		LIST.add(new KeyValuePair("canUseBalance",0x1f2));
		LIST.add(new KeyValuePair("canUseDiscount",0x910e));
		LIST.add(new KeyValuePair("extraCategoryList",0xa5f3));
		LIST.add(new KeyValuePair("extraRegionList",0x8dfb));
		
		LIST.add(new KeyValuePair("contentTitle",0xfbf));
		LIST.add(new KeyValuePair("refund",0x399d));
		LIST.add(new KeyValuePair("buyLimit",0xacd4));
		LIST.add(new KeyValuePair("deliveryType",0x6b1f));
		LIST.add(new KeyValuePair("hasReceipt",0x2226));
		LIST.add(new KeyValuePair("buyMixCount",0x2628));
		LIST.add(new KeyValuePair("interested",ApiUtil.getHash("Interested")));
        LIST.add(new KeyValuePair("isLimitPerUser",ApiUtil.getHash("IsLimitPerUser")));
        LIST.add(new KeyValuePair("isMemberCard",ApiUtil.getHash("IsMemberCard")));

        LIST.add(new KeyValuePair("tuanRemindStatus",0xe8d0));
        LIST.add(new KeyValuePair("mianYuYue",0xf55b));
        LIST.add(new KeyValuePair("tag",0x477b));
	}
	 
	public List<KeyValuePair> list = LEGACY_LIST;
	
	public static final List<KeyValuePair> MAIN_APP_LEGACY_LIST = Lists.newArrayList();
	static {
		MAIN_APP_LEGACY_LIST.add(new KeyValuePair("id",0x91b));
		MAIN_APP_LEGACY_LIST.add(new KeyValuePair("titleDesc", 0x36e9));
		MAIN_APP_LEGACY_LIST.add(new KeyValuePair("imageUrl",0x4a8a));
		MAIN_APP_LEGACY_LIST.add(new KeyValuePair("buyerCounter",0x630b));
		MAIN_APP_LEGACY_LIST.add(new KeyValuePair("price",0xc5b5));
		MAIN_APP_LEGACY_LIST.add(new KeyValuePair("memo",0x897e));
		//allMap.put("onClickLink",0);//未定
		MAIN_APP_LEGACY_LIST.add(new KeyValuePair("lat",0xbe8a)); // 会经常变，但和客户端确认 不回传
		MAIN_APP_LEGACY_LIST.add(new KeyValuePair("lng",0x562d)); // 会经常变，但和客户端确认 不回传
		MAIN_APP_LEGACY_LIST.add(new KeyValuePair("shortTitle",0x24d4));
		MAIN_APP_LEGACY_LIST.add(new KeyValuePair("bigImageUrl",0x7be5));
		MAIN_APP_LEGACY_LIST.add(new KeyValuePair("originalPrice",0x7c03));
		MAIN_APP_LEGACY_LIST.add(new KeyValuePair("categoryIds",0x9ea0));
		MAIN_APP_LEGACY_LIST.add(new KeyValuePair("regionIds",0x7d9b));
		MAIN_APP_LEGACY_LIST.add(new KeyValuePair("regionPrefix",0x5795));
		MAIN_APP_LEGACY_LIST.add(new KeyValuePair("status",0x2820));
		MAIN_APP_LEGACY_LIST.add(new KeyValuePair("remainCount",0xe854));
		MAIN_APP_LEGACY_LIST.add(new KeyValuePair("shopIdsStr",0xb58f));
		MAIN_APP_LEGACY_LIST.add(new KeyValuePair("time",0xc6ca));
		MAIN_APP_LEGACY_LIST.add(new KeyValuePair("dealImageList",0x83df));
		MAIN_APP_LEGACY_LIST.add(new KeyValuePair("extra",0xa7f4));
		MAIN_APP_LEGACY_LIST.add(new KeyValuePair("dealId",0xff01));
	}

	public static final List<KeyValuePair> LEGACY_OTHER = Lists.newArrayList();
	static {
		LEGACY_OTHER.add(new KeyValuePair("id",0x91b));
		LEGACY_OTHER.add(new KeyValuePair("title", 0x36e9));
		LEGACY_OTHER.add(new KeyValuePair("imageUrl",0x4a8a));
		LEGACY_OTHER.add(new KeyValuePair("buyerCounter",0x630b));
		LEGACY_OTHER.add(new KeyValuePair("price",0xc5b5));
		LEGACY_OTHER.add(new KeyValuePair("memo",0x897e));
		//allMap.put("onClickLink",0);//未定
		LEGACY_OTHER.add(new KeyValuePair("lat",0xbe8a)); // 会经常变，但和客户端确认 不回传
		LEGACY_OTHER.add(new KeyValuePair("lng",0x562d)); // 会经常变，但和客户端确认 不回传
		LEGACY_OTHER.add(new KeyValuePair("shortTitle",0x24d4));
		LEGACY_OTHER.add(new KeyValuePair("bigImageUrl",0x7be5));
		LEGACY_OTHER.add(new KeyValuePair("originalPrice",0x7c03));
		LEGACY_OTHER.add(new KeyValuePair("categoryIds",0x9ea0));
		LEGACY_OTHER.add(new KeyValuePair("regionIds",0x7d9b));
		LEGACY_OTHER.add(new KeyValuePair("regionPrefix",0x5795));
		LEGACY_OTHER.add(new KeyValuePair("status",0x2820));
		LEGACY_OTHER.add(new KeyValuePair("remainCount",0xe854));
		LEGACY_OTHER.add(new KeyValuePair("shopIdsStr",0xb58f));
		LEGACY_OTHER.add(new KeyValuePair("time",0xc6ca));
		LEGACY_OTHER.add(new KeyValuePair("dealImageList",0x83df));
		LEGACY_OTHER.add(new KeyValuePair("extra",0xa7f4));
		LEGACY_OTHER.add(new KeyValuePair("dealId",0xff01));		
	}

	public static final List<KeyValuePair> LEGACY_LIST = Lists.newArrayList();
	static {
		LEGACY_LIST.add(new KeyValuePair("id",0x91b));
		LEGACY_LIST.add(new KeyValuePair("title", 0x36e9));
		LEGACY_LIST.add(new KeyValuePair("imageUrl",0x4a8a));
		LEGACY_LIST.add(new KeyValuePair("buyerCounter",0x630b));
		LEGACY_LIST.add(new KeyValuePair("price",0xc5b5));
		LEGACY_LIST.add(new KeyValuePair("memo",0x897e));
		LEGACY_LIST.add(new KeyValuePair("lat",0xbe8a)); // 会经常变，但和客户端确认 不回传
		LEGACY_LIST.add(new KeyValuePair("lng",0x562d)); // 会经常变，但和客户端确认 不回传 
	}

	static final List<KeyValuePair> PARTLIST = Lists.newArrayList();
	static{		
		PARTLIST.add(new KeyValuePair("id",0x91b));
		PARTLIST.add(new KeyValuePair("buyerCounter",0x630b));		 
		PARTLIST.add(new KeyValuePair("memo",0x897e));	
	}
	
	public void setSendPart(boolean isSendPart) {
		if(isSendPart) {
			this.list = PARTLIST;			 
		} else {
			this.list = MAIN_APP_LEGACY_LIST;			 
		}
	}

	/**
	 * the method behave good only when it is called after all the other properties are properly set 
	 * @return
	 */
	public StringBuilder getVersion(StringBuilder sb) {			
			sb.append(id);
			sb.append(title);
			sb.append(imageUrl);
			sb.append(price);
			sb.append(memo);
			sb.append(buyerCounter);
			sb.append(lat);
			sb.append(lng);
			return sb;
	}
	public int getId() {
		return id;
	}
	public void setId(int id) {
		this.id = id;
	}
	public String getTitle() {
		return title;
	}
	public void setTitle(String title) {
		this.title = title;
	}
	public String getImageUrl() {
		return imageUrl;
	}
	public void setImageUrl(String imageUrl) {
		this.imageUrl = imageUrl;
	}
	public int getBuyerCounter() {
		return buyerCounter;
	}
	public void setBuyerCounter(int buyerCounter) {
		this.buyerCounter = buyerCounter;
	}
	public double getPrice() {
		return price;
	}
	public void setPrice(double price) {
		this.price = price;
	}
	public String getMemo() {
		return memo;
	}
	public void setMemo(String memo) {
		this.memo = memo;
	}
	public double getLat() {
		return lat;
	}
	public void setLat(double lat) {
		this.lat = lat;
	}
	public double getLng() {
		return lng;
	}
	public void setLng(double lng) {
		this.lng = lng;
	}
	
	public String getShopIdsStr() {
		return shopIdsStr;
	}

	public void setShopIdsStr(String shopIdsStr) {
		this.shopIdsStr = shopIdsStr;
		this.shopIds = shopIdsStr.split(",");
	}

	public String[] getShopIds() {
		return shopIds;
	}


	public void setShopIds(String[] shopIds) {
		this.shopIds = shopIds == null ? null : Arrays.copyOf(shopIds,
				shopIds.length);
	}

//	public String getCurrentRegionName() {
//		return currentRegionName;
//	}
//
//
//	public void setCurrentRegionName(String currentRegionName) {
//		this.currentRegionName = currentRegionName;
//	}

	/**
	 * 	/**
	 * 0x01 ==新单  
	   0x02 ==卖光了
       0x04==已结束
       0x08==无法购买(显示灰色抢购)
       0x10 ==即将开始
       0x20 ==正常单
	 */
	
	public int getStatus() {
		return status;
	}


	public void setStatus(int status) {
		this.status = status;
	}


	public int getRemainCount() {
		return remainCount;
	}


	public void setRemainCount(int remainCount) {
		this.remainCount = remainCount;
	}


	public List<Pair> getExtra() {
		return extra;
	}


	public void setExtra(List<Pair> extra) {
		this.extra = extra;
	}



	


	public DealRegionList getRegionNavs() {
		return regionNavs;
	}


	public void setRegionNavs(DealRegionList regionNavs) {
		this.regionNavs = regionNavs;
	}

	/**
	 * 该团购单子隶属的分类，可能为多个；每个分类会记录其也节点类和父类　
	 * @return
	 */
	public DealCategoryList getCategoryNavs() {
		return categoryNavs;
	}


	public void setCategoryNavs(DealCategoryList categoryNavs) {
		this.categoryNavs = categoryNavs;
	}
	
	


	public long getStartDate() {
		return startDate;
	}


	public void setStartDate(long startDate) {
		this.startDate = startDate;
	}


	public String getPrefix() {
		return prefix;
	}


	public void setPrefix(String prefix) {
		this.prefix = prefix;
	}


	public String getShortTitle() {
		return shortTitle;
	}


	public void setShortTitle(String shortTitle) {
		this.shortTitle = shortTitle;
	}


	public String getTitleDesc() {
		return titleDesc;
	}


	public void setTitleDesc(String titleDesc) {
		this.titleDesc = titleDesc;
	}


	public String getBigImageUrl() {
		return bigImageUrl;
	}


	public void setBigImageUrl(String bigImageUrl) {
		this.bigImageUrl = bigImageUrl;
	}


	public List<String> getDetailPhotos() {
		return detailPhotos;
	}


	public void setDetailPhotos(List<String> detailPhotos) {
		this.detailPhotos = detailPhotos;
	}


	public Date getTime() {
		return time;
	}


	public void setTime(Date time) {
		//this.time = time;
		//this.time=new Date(getStartDate()*1000);
		this.time = time;
	}
	
	private static final long TIME_Y2K = 946656000000l;
	
	public void setTime(long time) {
		//this.time = time;
		//this.time=new Date(getStartDate()*1000);
		this.time = new Date(time > TIME_Y2K ? time : TIME_Y2K);
	}
	
	/*
	public static void main(String[] args) throws ParseException {
		SimpleDateFormat dateFormatter = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
		Date d = dateFormatter.parse("2000-01-01 00:00:00");
		System.out.println(new Date(0));
	}
	*/

	/**
	 * 该deal隶属于的所有叶节点子类
	 * @return
	 */
	public List<Integer> getCategoryIds() {
		return categoryIds;
	}


	public void setCategoryIds(List<Integer> categoryIds) {
		this.categoryIds = categoryIds;
	}


	public List<Integer> getRegionIds() {
		return regionIds;
	}


	public void setRegionIds(List<Integer> regionIds) {
		this.regionIds = regionIds;
	}


	public DealImages getDetailPics() {
		return detailPics;
	}

	public void setDetailPics(DealImages detailPics) {
		this.detailPics = detailPics;
	}
	
	public DealShopList getShopList() {
		return shopList;
	}

	public void setShopList(DealShopList shopList) {
		this.shopList = shopList;
	}

	public String getDealDetail() {
		return dealDetail;
	}

	public void setDealDetail(String dealDetail) {
		this.dealDetail = dealDetail;
	}

	public int getMaxJoin() {
		return maxJoin;
	}

	public void setMaxJoin(int maxJoin) {
		this.maxJoin = maxJoin;
	}

	public List<String> getDealImageList() {
		return dealImageList;
	}

	public void setDealImageList(List<String> dealImageList) {
		this.dealImageList = dealImageList;
	}
	
	public int getDealId() {
		return dealId;
	}

	public void setDealId(int dealId) {
		this.dealId = dealId;
	}

	public String getDealIdStr() {
		return dealIdStr;
	}

	public void setDealIdStr(String dealIdStr) {
		this.dealIdStr = dealIdStr;
	}

	public String getToday() {
		return today;
	}

	public void setToday(String today) {
		this.today = today;
	}

	public String getSpecialPoint() {
		return specialPoint;
	}

	public void setSpecialPoint(String specialPoint) {
		this.specialPoint = specialPoint;
	}

	public double getDiscount() {
		return discount;
	}

	public void setDiscount(double discount) {
		this.discount = discount;
	}

	public List<Pair> getDetailInfo() {
		return detailInfo;
	}

	public void setDetailInfo(List<Pair> detailInfo) {
		this.detailInfo = detailInfo;
	}

	public List<DealComment> getDealComments() {
		return dealComments;
	}

	public void setDealComments(List<DealComment> dealComments) {
		this.dealComments = dealComments;
	}

	public List<DealSelect> getDealSelects() {
		return dealSelects;
	}


	public void setDealSelects(List<DealSelect> dealSelects) {
		this.dealSelects = dealSelects;
	}

	public int getDealType() {
		return dealType;
	}

	public void setDealType(int dealType) {
		this.dealType = dealType;
	}


	public boolean getCanUseBalance() {
		return canUseBalance;
	}


	public void setCanUseBalance(boolean canUseBalance) {
		this.canUseBalance = canUseBalance;
	}


	public boolean getCanUseDiscount() {
		return canUseDiscount;
	}


	public void setCanUseDiscount(boolean canUseDiscount) {
		this.canUseDiscount = canUseDiscount;
	}

	

	public int getDealGroupId() {
		return dealGroupId;
	}


	public void setDealGroupId(int dealGroupId) {
		this.dealGroupId = dealGroupId;
	}
	
	

	public boolean isToday() {
		return isToday;
	}


	public void setToday(boolean isToday) {
		this.isToday = isToday;
	}


	public String getContentTitle() {
		return contentTitle;
	}


	public void setContentTitle(String contentTitle) {
		this.contentTitle = contentTitle;
	}

	public boolean isRefund() {
		return refund;
	}

	public void setRefund(boolean refund) {
		this.refund = refund;
	}

	public int getBuyLimit() {
		return buyLimit;
	}

	public void setBuyLimit(int buyLimit) {
		this.buyLimit = buyLimit;
	}

	public List<Pair> getDeliveryType() {
		return deliveryType;
	}


	public void setDeliveryType(List<Pair> deliveryType) {
		this.deliveryType = deliveryType;
	}


	public boolean isHasReceipt() {
		return hasReceipt;
	}


	public void setHasReceipt(boolean hasReceipt) {
		this.hasReceipt = hasReceipt;
	}


	public int getBuyMixCount() {
		return buyMixCount;
	}


	public void setBuyMixCount(int buyMixCount) {
		this.buyMixCount = buyMixCount;
	}


	public DealGroup clone() {
		DealGroup deal = new DealGroup();
		deal.setId(id);
		deal.setTitle(title);
		deal.setTitleDesc(titleDesc);
		deal.setImageUrl(imageUrl);
		deal.setPrice(price);
		//deal.setPriceB(priceB);
		//deal.setPriceStr(priceStr);
		deal.setMemo(memo);
		deal.setBuyerCounter(buyerCounter);
		deal.setEndDate(endDate);
		deal.setOriginalPrice(originalPrice);
		deal.setLat(lat);
		deal.setLng(lng);
		deal.setDealLocation(dealLocation);
		deal.setShortTitle(shortTitle);
		deal.setBigImageUrl(bigImageUrl);		
		deal.setCategoryIds(categoryIds);
		deal.setRegionIds(regionIds);
//		deal.setCurrentRegionName(currentRegionName);
		deal.setStatus(status);
		deal.setStartDate(startDate);
		deal.setRemainCount(remainCount);
		deal.setShopIdsStr(shopIdsStr);
		deal.setTime(time);
		deal.setDealImageList(dealImageList);
		deal.setExtra(extra);
		deal.setDealId(dealId);
		deal.setShopList(shopList);
		deal.setCategoryNavs(categoryNavs);
		deal.setRegionNavs(regionNavs);
		deal.setToday(isToday);
		deal.setDiscount(discount);
		deal.setDetailInfo(detailInfo);
		deal.setDealComments(dealComments);
		deal.setDealSelects(dealSelects);
		deal.setDealType(dealType);
		deal.setCanUseBalance(canUseBalance);
		deal.setCanUseDiscount(canUseDiscount);
		deal.setDetailPhotos(detailPhotos);
		deal.setDetailPics(detailPics);
		return deal;
	}
   
	//FIXME: too complicated
	@Override
	public List<KeyValuePair> getSerializeList(ClientInfo clientInfo, Version version ){
		if(clientInfo.getApp() == AppType.TG
				|| (clientInfo.getClient() == ClientType.iPadHd && Version.compareTo(version, new Version("2.3")) >= 0)
				|| (clientInfo.getApp() == AppType.Main && Version.compareTo(version, new Version("5.3")) >= 0)) {
			return LIST;
		} else {
			if(clientInfo.getClient().value == ClientType.iPadHd.value&&Version.compareTo(version, new Version("1.5"))<=0){
				return LEGACY_LIST;
			}else if(clientInfo.getClient().value == ClientType.iPhone.value&&Version.compareTo(version, new Version("4.7.2"))<=0){
				return LEGACY_LIST;
			}else if((clientInfo.getClient().value == ClientType.iPhone.value||clientInfo.getClient().value == ClientType.Android.value)&&Version.compareTo(version, new Version("4.8"))>=0){
				 return MAIN_APP_LEGACY_LIST;
			}else if((clientInfo.getClient().value == ClientType.iPadHd.value&&Version.compareTo(version, new Version("2.0"))>=0) || (clientInfo.getClient().value == ClientType.WinPhone.value&&Version.compareTo(version, new Version("1.5"))>=0)) {
				return IPAD2LEGACY_WP_LIST;
			}else{
				 return LEGACY_OTHER;
			}
		}
	}


	/* (non-Javadoc)
	 * @see com.dianping.api.netio.DPEncoder#getClassId()
	 */
	@Override
	public int getClassId() {
 		return 0x70ac;
	}

	public String getNotice() {
		return notice;
	}

	public void setNotice(String notice) {
		this.notice = notice;
	}

	public String getRegionPrefix() {
		return regionPrefix;
	}

	public void setRegionPrefix(String regionPrefix) {
		this.regionPrefix = regionPrefix;
	}

	public int getShopId() {
		return shopId;
	}

	public void setShopId(int shopId) {
		this.shopId = shopId;
	}

	public int getTuanRemindStatus() {
		return tuanRemindStatus;
	}

	public void setTuanRemindStatus(int tuanRemindStatus) {
		this.tuanRemindStatus = tuanRemindStatus;
	}

	public boolean getMianYuYue() {
		return mianYuYue;
	}

	public void setMianYuYue(boolean mianYuYue) {
		this.mianYuYue = mianYuYue;
	}

	public int getTag() {
		return tag;
	}

	public void setTag(int tag) {
		this.tag = tag;
	}

	public String getDestinationPrefix() {
		return destinationPrefix;
	}

	public void setDestinationPrefix(String destinationPrefix) {
		this.destinationPrefix = destinationPrefix;
	}

	public List<RegionDTO> getDestinationList() {
		return destinationList;
	}

	public void setDestinationList(List<RegionDTO> destinationList) {
		this.destinationList = destinationList;
	}

	public List<NaviTagSelectionDTO> getTags() {
		return tags;
	}

	public void setTags(List<NaviTagSelectionDTO> tags) {
		this.tags = tags;
	}
}
