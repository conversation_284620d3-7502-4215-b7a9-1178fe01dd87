package com.dianping.pay.api.entity;

import com.dianping.api.common.util.KeyValuePair;
import com.dianping.api.domain.ClientInfo;
import com.dianping.api.domain.Version;
import com.dianping.api.netio.DPEncoder;
import com.dianping.cat.Cat;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

public class RedEnvelopeAdvertise extends DPEncoder implements Serializable {

    private String imageUrl;
    private String redirectUrl;

    public String getImageUrl() {
        return imageUrl;
    }

    public void setImageUrl(String imageUrl) {
        this.imageUrl = imageUrl;
    }

    public String getRedirectUrl() {
        return redirectUrl;
    }

    public void setRedirectUrl(String redirectUrl) {
        this.redirectUrl = redirectUrl;
    }

    protected static final List<KeyValuePair> serializeList =new ArrayList<KeyValuePair>();
    static{
        serializeList.add(new KeyValuePair("imageUrl", 0x16a9));
        serializeList.add(new KeyValuePair("redirectUrl", 0x4709));
    }

    @Override
    public int getClassId() {
        Cat.logEvent("INVALID_METHOD_2", "com.dianping.pay.api.entity.RedEnvelopeAdvertise.getClassId()");
        return 0xc803;
    }

    @Override
    public List<KeyValuePair> getSerializeList(ClientInfo clientInfo, Version version) {
        Cat.logEvent("INVALID_METHOD_1", "com.dianping.pay.api.entity.RedEnvelopeAdvertise.getSerializeList(com.dianping.api.domain.ClientInfo,com.dianping.api.domain.Version)");
        return serializeList;
    }
}
