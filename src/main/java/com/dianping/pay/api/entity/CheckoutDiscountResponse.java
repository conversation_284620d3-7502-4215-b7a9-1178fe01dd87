package com.dianping.pay.api.entity;

import com.dianping.api.common.util.KeyValuePair;
import com.dianping.api.domain.ClientInfo;
import com.dianping.api.domain.Version;
import com.dianping.api.netio.DPEncoder;
import com.dianping.cat.Cat;

import java.io.Serializable;
import java.util.Arrays;
import java.util.List;

/**
 * Created by huawei.li on 15/3/10.
 */
public class CheckoutDiscountResponse extends DPEncoder implements Serializable {
    private static final long serialVersionUID = -2850730406543710416L;
    private List<AbstractDiscountTool> discounts;
    private List<OptimalDiscount> optimalDiscounts;
    private String promptMsg;

    private static final List<KeyValuePair> LIST = Arrays.asList(new KeyValuePair("discounts", 0x230a),new KeyValuePair("optimalDiscounts",0x64ae),new KeyValuePair("promptMsg",0x8c81));
    @Override
    public int getClassId() {
        Cat.logEvent("INVALID_METHOD_4", "com.dianping.pay.api.entity.CheckoutDiscountResponse.getClassId()");
        return 0xdb73;
    }

    @Override
    public List<KeyValuePair> getSerializeList(ClientInfo clientInfo, Version version) {
        Cat.logEvent("INVALID_METHOD_2", "com.dianping.pay.api.entity.CheckoutDiscountResponse.getSerializeList(com.dianping.api.domain.ClientInfo,com.dianping.api.domain.Version)");
        return LIST;
    }

    public List<AbstractDiscountTool> getDiscounts() {
        return discounts;
    }

    public void setDiscounts(List<AbstractDiscountTool> discounts) {
        this.discounts = discounts;
    }

    public List<OptimalDiscount> getOptimalDiscounts() {
        return optimalDiscounts;
    }

    public void setOptimalDiscounts(List<OptimalDiscount> optimalDiscounts) {
        this.optimalDiscounts = optimalDiscounts;
    }

    public String getPromptMsg() {
        return promptMsg;
    }

    public void setPromptMsg(String promptMsg) {
        this.promptMsg = promptMsg;
    }
}
