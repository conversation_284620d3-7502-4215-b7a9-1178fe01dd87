package com.dianping.pay.api.entity.issuecoupon;

import lombok.NoArgsConstructor;

import java.util.Date;

/**
 * Created by drintu on 18/6/14.
 */

@NoArgsConstructor
public class UnifiedIssueResult {
    private int couponGroupId ;
    private String unifiedCouponId ;

    private Date beginTime;
    private Date endTime;
    private String couponGroupName;

    private String toastMsg;

    public UnifiedIssueResult(int couponGroupId, String unifiedCouponId) {
        this.couponGroupId = couponGroupId;
        this.unifiedCouponId = unifiedCouponId;
    }

    public int getCouponGroupId() {
        return couponGroupId;
    }

    public void setCouponGroupId(int couponGroupId) {
        this.couponGroupId = couponGroupId;
    }

    public String getUnifiedCouponId() {
        return unifiedCouponId;
    }

    public void setUnifiedCouponId(String unifiedCouponId) {
        this.unifiedCouponId = unifiedCouponId;
    }

    public Date getBeginTime() {
        return beginTime;
    }

    public void setBeginTime(Date beginTime) {
        this.beginTime = beginTime;
    }

    public Date getEndTime() {
        return endTime;
    }

    public void setEndTime(Date endTime) {
        this.endTime = endTime;
    }

    public String getCouponGroupName() {
        return couponGroupName;
    }

    public void setCouponGroupName(String couponGroupName) {
        this.couponGroupName = couponGroupName;
    }

    public String getToastMsg() {
        return toastMsg;
    }

    public void setToastMsg(String toastMsg) {
        this.toastMsg = toastMsg;
    }
}
