package com.dianping.pay.api.entity.issuecoupon;

import com.dianping.mobile.framework.annotation.MobileDo;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * 模型描述：领券组件模块打点信息
 * 移动之家链接: https://mobile.sankuai.com/studio/model/info/24445
 */
@MobileDo(id = 0x308a)
@Data
public class IssueCouponComponentOceanDTO implements Serializable {
    /**
     * 内层-券适用品类ocean （适用品类展开）
     */
    @MobileDo.MobileField(key = 0xc410)
    private PFLOceanItemInfo categoryConditionOcean;

    /**
     * 内层-单个券的ocean
     */
    @MobileDo.MobileField(key = 0xa0d6)
    private PFLOceanItemInfo couponDetailItemOcean;

    /**
     * 内存-浮层模块ocean
     */
    @MobileDo.MobileField(key = 0xf22d)
    private PFLOceanItemInfo moduleOcean;

    /**
     * 外层-poi券缩略信息模块ocean
     */
    @MobileDo.MobileField(key = 0x80dd)
    private PFLOceanItemInfo simpleModuleOcean;

    /**
     * 外层-券模块-查看全部券ocean （右上角）
     */
    @MobileDo.MobileField(key = 0xd3b)
    private PFLOceanItemInfo viewAllOcean;

    /**
     * 外层-券模块-单个券信息ocean
     */
    @MobileDo.MobileField(key = 0x6623)
    private PFLOceanItemInfo couponInfoOcean;

}
