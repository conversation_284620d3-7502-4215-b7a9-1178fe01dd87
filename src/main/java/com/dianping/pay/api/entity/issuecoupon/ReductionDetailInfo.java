package com.dianping.pay.api.entity.issuecoupon;

import com.dianping.mobile.framework.annotation.MobileDo;
import lombok.Data;

import java.io.Serializable;

@MobileDo(id = 0xa42f)
@Data
public class ReductionDetailInfo implements Serializable {

    /**
     * 立减标题，eg：下单立减 / 新客立减
     */
    @MobileDo.MobileField(key = 0x24cc)
    private String title;

    /**
     * 立减描述
     */
    @MobileDo.MobileField(key = 0xfebf)
    private String desc;

}
