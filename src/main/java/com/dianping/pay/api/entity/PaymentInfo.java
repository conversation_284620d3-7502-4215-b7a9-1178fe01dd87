package com.dianping.pay.api.entity;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

import com.dianping.api.common.entity.Pair;
import com.dianping.api.common.util.KeyValuePair;
import com.dianping.api.domain.AlertMsg;
import com.dianping.api.domain.Enum.AppType;
import com.dianping.api.domain.Enum.ClientType;
import com.dianping.api.domain.ClientInfo;
import com.dianping.api.domain.Version;
import com.dianping.api.netio.DPEncoder;
import com.google.common.collect.Lists;

public class PaymentInfo extends DPEncoder implements Serializable {

	
	private static final long serialVersionUID = -5563515434755983775L;
	
	public List<Discount> getDiscountList() {
		return discountList;
	}
	public void setDiscountList(List<Discount> discountList) {
		this.discountList = discountList;
	}
	public BigDecimal getBalance() {
		return balance;
	}
	public void setBalance(BigDecimal balance) {
		this.balance = balance;
		this.balanceStr = balance.toPlainString();
	}
	public String getBalanceStr() {
		return balanceStr;
	}
	public void setBalanceStr(String balanceStr) {
		this.balanceStr = balanceStr;
	}
	public List<Pair> getPaymentType() {
		return paymentType;
	}	
	
	public int getOrderId() {
		return orderId;
	}
	public void setOrderId(int orderId) {
		this.orderId = orderId;
	}
	public void setPaymentType(List<Pair> paymentType) {
		this.paymentType = paymentType;
	}

    public AlertMsg getAlertMsg() {
        return alertMsg;
    }

    public void setAlertMsg(AlertMsg alertMsg) {
        this.alertMsg = alertMsg;
    }

    public int getFlag() {
        return flag;
    }

    public void setFlag(int flag) {
        this.flag = flag;
    }

    public String getDealTitle() {
        return dealTitle;
    }

    public void setDealTitle(String dealTitle) {
        this.dealTitle = dealTitle;
    }

    public int getRetryCount() {
        return retryCount;
    }

    public void setRetryCount(int retryCount) {
        this.retryCount = retryCount;
    }

    public int getIntervalTime() {
        return intervalTime;
    }

    public void setIntervalTime(int intervalTime) {
        this.intervalTime = intervalTime;
    }

    public String getAdvanceOrderId() {
        return advanceOrderId;
    }

    public void setAdvanceOrderId(String advanceOrderId) {
        this.advanceOrderId = advanceOrderId;
    }

    public String getRedirectUrl() {
        return redirectUrl;
    }

    public void setRedirectUrl(String redirectUrl) {
        this.redirectUrl = redirectUrl;
    }

    private static final List<KeyValuePair> LIST = Lists.newArrayList();
	static {
		LIST.add(new KeyValuePair("discountList", 0x4f16));
		LIST.add(new KeyValuePair("balanceStr", 0x49bd));
		LIST.add(new KeyValuePair("paymentType", 0x6dd9));
		LIST.add(new KeyValuePair("orderId", 0x91b));
        LIST.add(new KeyValuePair("alertMsg", 0x5886));
        LIST.add(new KeyValuePair("flag",0x73ad));
        LIST.add(new KeyValuePair("retryCount",0xb214));
        LIST.add(new KeyValuePair("intervalTime",0xb96b));
        LIST.add(new KeyValuePair("advanceOrderId",0x9a6d));
        LIST.add(new KeyValuePair("redirectUrl",0x4709));
 	}
	
	private static final List<KeyValuePair> LEGACY_LIST = Lists.newArrayList();
	static {
		LEGACY_LIST.add(new KeyValuePair("discountList", 0x4f16));
		LEGACY_LIST.add(new KeyValuePair("balanceStr", 0x49bd));
		LEGACY_LIST.add(new KeyValuePair("paymentType", 0x6dd9));
 	}
	
	@Override
	public List<KeyValuePair> getSerializeList(ClientInfo clientInfo, Version version) {
		if(clientInfo.getApp() == AppType.TG
				|| (clientInfo.getClient() == ClientType.iPadHd && Version.compareTo(version, new Version("2.3")) >= 0)
				|| (clientInfo.getApp() == AppType.Main && Version.compareTo(version, new Version("5.3")) >= 0)) {
			return LIST;
		} else {
			return LEGACY_LIST;
		}
	}
				
	private List<Discount> discountList;  //订单可以使用的抵用券	
	private BigDecimal balance;               //帐户余额	
	private String balanceStr;
	private List<Pair> paymentType;       //支付方式	
	private int orderId;
    private AlertMsg alertMsg;
    private int flag;
    private String dealTitle;
    private int retryCount;
    private int intervalTime;
    private String advanceOrderId;
    private String redirectUrl;

	/* (non-Javadoc)
	 * @see com.dianping.api.netio.DPEncoder#getClassId()
	 */
	@Override
	public int getClassId() {
 		return 0x3820;
	}
}
