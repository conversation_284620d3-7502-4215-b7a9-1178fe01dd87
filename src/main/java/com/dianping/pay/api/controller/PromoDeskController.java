package com.dianping.pay.api.controller;

import com.dianping.api.common.enums.FeatureSwitchEnum;
import com.dianping.api.framework.Controller;
import com.dianping.api.framework.FeatureContingency;
import com.dianping.cat.Cat;
import com.dianping.lion.client.Lion;
import com.dianping.pay.api.beans.DiscountRules;
import com.dianping.pay.api.beans.GetPromoDeskRequest;
import com.dianping.pay.api.beans.PromoProduct;
import com.dianping.pay.api.biz.discount.CouponBiz;
import com.dianping.pay.api.biz.discount.CouponMapper;
import com.dianping.pay.api.biz.product.ProductBiz;
import com.dianping.pay.api.biz.promodesk.PromoDeskStrategy;
import com.dianping.pay.api.biz.promodesk.PromoDeskStrategyFactory;
import com.dianping.pay.api.biz.promodeskcoupon.PromoDeskCouponStrategy;
import com.dianping.pay.api.biz.promodeskcoupon.PromoDeskCouponStrategyFactory;
import com.dianping.pay.api.entity.promodesk.*;
import com.dianping.pay.api.enums.RequestTypeConstant;
import com.dianping.pay.api.util.CouponComparator;
import com.dianping.pay.api.util.LionConstants;
import com.dianping.pay.cashier.biz.enums.PaymentRule;
import com.dianping.pay.common.enums.ProductCode;
import com.dianping.pay.framework.utils.shopUuid.ShopUuidUtils;
import com.dianping.unified.coupon.manage.api.UnifiedCouponListService;
import com.dianping.unified.coupon.manage.api.dto.UnifiedCouponDTO;
import com.dianping.unified.coupon.manage.api.dto.UnifiedCouponListOption;
import com.dianping.unified.coupon.manage.api.dto.UnifiedCouponQueryContext;
import com.dianping.unified.coupon.manage.api.response.UnifiedCouponManageResponse;
import com.google.common.base.Predicate;
import com.google.common.collect.Collections2;
import com.google.common.collect.Ordering;
import org.apache.commons.collections.CollectionUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;

public class PromoDeskController extends Controller {

    @Resource
    private CouponBiz couponBiz;
    @Resource
    private ProductBiz productBiz;
    @Resource
    private PromoDeskStrategyFactory promoDeskStrategyFactory;
    @Resource
    private PromoDeskCouponStrategyFactory promoDeskCouponStrategyFactory;
    @Resource
    private UnifiedCouponListService unifiedCouponListService;
    @Resource
    private ShopUuidUtils shopUuidUtils;

    private static List<Integer> invalidProductCodeList;

    static {
        invalidProductCodeList = Lion.getList(LionConstants.PROMO_DESK_INVALID_PRODUCT_CODE_LIST,Integer.class);
        Lion.addConfigListener(LionConstants.PROMO_DESK_INVALID_PRODUCT_CODE_LIST, configEvent -> {
            invalidProductCodeList = Lion.getList(LionConstants.PROMO_DESK_INVALID_PRODUCT_CODE_LIST,Integer.class);
        });
    }

    public PromoDesk getPromoDesk() throws Exception {
        if (FeatureContingency.isSwitchOff(FeatureSwitchEnum.MASTER_DISCOUNT)) {
            return new PromoDesk();
        }
        GetPromoDeskRequest getPromoDeskRequest = new GetPromoDeskRequest(this);
        if(CollectionUtils.isNotEmpty(getPromoDeskRequest.getPromoProductList())){
            int productCode = getPromoDeskRequest.getPromoProductList().get(0).getProductCode();

            Cat.logEvent("PromoDeskCode", "productCode-"+productCode);
            if( invalidProductCodeList.contains(productCode)){
                String msg = productCode+"--"+ProductCode.getByCode(productCode).getMessage();
                Cat.logEvent("INVALID_INTERFACE", "com.dianping.pay.api.controller.getPromoDesk()--"+msg);
                // 无效代码清理
                throw new RuntimeException(msg+"--此无效方法已被清理下线");
            }
        }

        String requestType = this.controllerKey.contains(".pay") ? RequestTypeConstant.NATIVE : RequestTypeConstant.H5;
        shopUuidUtils.prepareUuidInRequest(getPromoDeskRequest, RequestTypeConstant.NATIVE.equals(requestType));

        getPromoDeskRequest.validate(false);
        if(getPromoDeskRequest.getClientInfo() != null){
            Cat.logEvent("PromoDeskClientVersion",String.format("platform:%s, version:%s", getPromoDeskRequest.getClientInfo().getPlatform(), getPromoDeskRequest.getClientInfo().getVersion()));
        }
        Map<Integer, DiscountRules> productCodeDiscountRule = queryDiscountRules(getPromoDeskRequest);
        DiscountRules discountRuleSummarize = summarizeDiscountRules(productCodeDiscountRule);
        PromoDeskStrategy strategy = promoDeskStrategyFactory.getStrategy(getPromoDeskRequest);
        PromoDeskCouponStrategy couponStrategy = promoDeskCouponStrategyFactory.getStrategy(getPromoDeskRequest);
        if (strategy != null) {
            PromoDesk promoDesk = new PromoDesk();
            promoDesk.setCoupon(strategy.assembleCouponPromoTool(getPromoDeskRequest, discountRuleSummarize));
            promoDesk.setShopCoupon(strategy.assembleShopCouponPromoTool(getPromoDeskRequest, discountRuleSummarize));
            promoDesk.setHongBao(strategy.assembleHongBaoPromoTool(getPromoDeskRequest, discountRuleSummarize, productCodeDiscountRule));
            promoDesk.setGiftCard(strategy.assembleGiftCardPromoTool(getPromoDeskRequest, discountRuleSummarize));
            promoDesk.setDiscount(strategy.assembleDiscountPromoTool(getPromoDeskRequest));
            promoDesk.setPoint(strategy.assemblePointPromoTool(getPromoDeskRequest, discountRuleSummarize));

            List<PromoDeskCoupon> couponList = promoDesk.getCoupon() != null ? couponBiz.getOptimalCoupons(getPromoDeskRequest, false) : null;
            List<PromoDeskCoupon> shopCouponList = queryShopCouponList(getPromoDeskRequest, couponStrategy);
            if (CollectionUtils.isEmpty(shopCouponList)) { // 用户没有商家券，关闭前台商家券的入口
                promoDesk.setShopCoupon(null);
            }
            optimizeChoices(getPromoDeskRequest, promoDesk, couponList, shopCouponList);
            promoDesk.setPromptMsg(strategy.assemblePromptMsg(getPromoDeskRequest, discountRuleSummarize));
            return promoDesk;
        } else {
            return new PromoDesk();
        }
    }

    private Map<Integer, DiscountRules> queryDiscountRules(GetPromoDeskRequest getPromoDeskRequest) {
        Cat.logEvent("INVALID_METHOD_2", "com.dianping.pay.api.controller.PromoDeskController.queryDiscountRules(com.dianping.pay.api.beans.GetPromoDeskRequest)");
        Map<Integer, DiscountRules> productCodeDiscountRule = new HashMap<Integer, DiscountRules>();
        for (PromoProduct promoProduct : getPromoDeskRequest.getPromoProductList()) {
            if (productCodeDiscountRule.get(promoProduct.getProductCode()) == null) {
                DiscountRules discountRules = new DiscountRules();
                discountRules.clear();
                productCodeDiscountRule.put(promoProduct.getProductCode(), discountRules);
            }
            productCodeDiscountRule.get(promoProduct.getProductCode()).mergeRules(productBiz.getPaymentRule(promoProduct.getProductId(), promoProduct.getProductCode(), getPromoDeskRequest.isMtClient()));
        }
        return productCodeDiscountRule;
    }

    private DiscountRules summarizeDiscountRules(Map<Integer, DiscountRules> productCodeDiscountRule) {
        Cat.logEvent("INVALID_METHOD_3", "com.dianping.pay.api.controller.PromoDeskController.summarizeDiscountRules(java.util.Map)");
        DiscountRules discountRuleSummarize = new DiscountRules();
        discountRuleSummarize.clear();
        for (DiscountRules discountRules : productCodeDiscountRule.values()) {
            discountRuleSummarize.mergeRules(discountRules);
        }
        return discountRuleSummarize;
    }

    private void optimizeChoices(GetPromoDeskRequest getPromoDeskRequest, PromoDesk promoDesk, List<PromoDeskCoupon> optimalCoupons, List<PromoDeskCoupon> optimalShopCoupons) {
        Cat.logEvent("INVALID_METHOD_1", "com.dianping.pay.api.controller.PromoDeskController.optimizeChoices(com.dianping.pay.api.beans.GetPromoDeskRequest,com.dianping.pay.api.entity.promodesk.PromoDesk,java.util.List,java.util.List)");
        if (getPromoDeskRequest.getUserId() == 0) {
            return;
        }
        if (getPromoDeskRequest.getPromoProductList().size() != 1) {
            return;
        }
        PromoProduct promoProduct = getPromoDeskRequest.getPromoProductList().get(0);
        if (promoProduct.getPrice() == null || promoProduct.getPrice().signum() <= 0) {
            return;
        }
        Set<Integer> mutexDiscountTools = new HashSet<Integer>();
        List<OptimalPromoTool> optimalPromoTools = new ArrayList<OptimalPromoTool>(CouponBiz.OPTIMALDISCOUNTSIZE);
        for (int i = 0; i < CouponBiz.OPTIMALDISCOUNTSIZE; i++) {
            OptimalPromoTool optimalPromoTool = new OptimalPromoTool();
            optimalPromoTool.setProductCode(promoProduct.getProductCode());
            optimalPromoTool.setQuantity(promoProduct.getQuantity() + i);
            BigDecimal orderPrice = promoProduct.getPrice().multiply(new BigDecimal(optimalPromoTool.getQuantity()));

            DiscountPromoEvent maxReductionEvent = findMaxReductionEvent(promoDesk, orderPrice);
            if (maxReductionEvent != null) {
                optimalPromoTool.setDiscountId(maxReductionEvent.getId());
                mutexDiscountTools.addAll(maxReductionEvent.getMutexPromoTools());
            }

            if (promoDesk.getCoupon() != null && !mutexDiscountTools.contains(PaymentRule.COUPON.code)) {
                optimalPromoTool.setCoupon(CouponBiz.getOptimalCoupon(orderPrice, optimalCoupons));
            }

            if (promoDesk.getCoupon() != null && !mutexDiscountTools.contains(PaymentRule.SHOPCOUPON.code)) {
                optimalPromoTool.setShopCoupon(CouponBiz.getOptimalCoupon(orderPrice, optimalShopCoupons));
            }

            if (promoDesk.getHongBao() != null && !mutexDiscountTools.contains(PaymentRule.HONGBAO.code)) {
                if (optimalPromoTool.getCoupon() != null || optimalPromoTool.getShopCoupon() != null) {
                    BigDecimal couponAmount = optimalPromoTool.getCoupon() != null ? optimalPromoTool.getCoupon().getPriceValue() : BigDecimal.ZERO;
                    BigDecimal shopCouponAmount = optimalPromoTool.getShopCoupon() != null ? optimalPromoTool.getShopCoupon().getPriceValue() : BigDecimal.ZERO;
                    if (promoDesk.getHongBao().getBalance() - couponAmount.add(shopCouponAmount).doubleValue() > 0.001) {
                        optimalPromoTool.setUseHongBao(true);
                        optimalPromoTool.setCoupon(null);
                        optimalPromoTool.setShopCoupon(null);
                    }
                } else {
                    optimalPromoTool.setUseHongBao(true);
                }
            }
            optimalPromoTools.add(optimalPromoTool);
        }
        if (!optimalPromoTools.isEmpty()) {
            promoDesk.setOptimalPromoTools(optimalPromoTools);
        }
    }

    private DiscountPromoEvent findMaxReductionEvent(PromoDesk promoDesk, BigDecimal orderPrice) {
        Cat.logEvent("INVALID_METHOD_5", "com.dianping.pay.api.controller.PromoDeskController.findMaxReductionEvent(com.dianping.pay.api.entity.promodesk.PromoDesk,java.math.BigDecimal)");
        DiscountPromoEvent maxReductionEvent = null;
        if (promoDesk.getDiscount() != null && CollectionUtils.isNotEmpty(promoDesk.getDiscount().getDiscountPromoEventGroups())) {
            for (DiscountPromoEventGroup eventGroup : promoDesk.getDiscount().getDiscountPromoEventGroups()) {
                for (DiscountPromoEvent event : eventGroup.getDiscountPromoEvents()) {
                    if (event.isCanUse()
                            && orderPrice.doubleValue() - event.getOrderPriceLimit() >= 0.001) { // 兼容double精度，实际上就是orderprice >= pricelimit
                        if (maxReductionEvent == null || maxReductionEvent.getPromoAmount() - event.getPromoAmount() < 0.001) {
                            maxReductionEvent = event;
                        }
                        break;
                    }
                }
            }
        }
        return maxReductionEvent;
    }

    private List<PromoDeskCoupon> queryShopCouponList(final GetPromoDeskRequest request, final PromoDeskCouponStrategy strategy) {
        Cat.logEvent("INVALID_METHOD_1", "com.dianping.pay.api.controller.PromoDeskController.queryShopCouponList(com.dianping.pay.api.beans.GetPromoDeskRequest,com.dianping.pay.api.biz.promodeskcoupon.PromoDeskCouponStrategy)");
        final PromoProduct promoProduct = request.getPromoProductList().get(0);
        UnifiedCouponQueryContext couponQueryContext = CouponMapper.toCouponQueryContext(request, promoProduct, true);
        UnifiedCouponManageResponse<List<UnifiedCouponDTO>> listResponse = unifiedCouponListService.queryCouponByContext(couponQueryContext, null, new UnifiedCouponListOption(5, 0, 0, 25));
        if (listResponse.isSuccess()) {
            List<UnifiedCouponDTO> sortedList = Ordering.from(new CouponComparator()).sortedCopy(Collections2.filter(listResponse.getResult(), new Predicate<UnifiedCouponDTO>() {
                @Override
                public boolean apply(UnifiedCouponDTO coupon) {
                    return coupon.isAvailable() && strategy.isCouponOfShop(coupon, request);
                }
            }));
            List<PromoDeskCoupon> promoDeskCoupons = CouponMapper.toPromoDeskCouponList(promoProduct.getProductCode(), sortedList);
            couponBiz.assembleCipher(promoDeskCoupons, request.getUserId());
            return promoDeskCoupons;
        }
        return Collections.emptyList();
    }
}
