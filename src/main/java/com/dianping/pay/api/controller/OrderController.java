package com.dianping.pay.api.controller;

import com.dianping.api.domain.SimpleMsg;
import com.dianping.api.framework.Controller;
import com.dianping.cat.Cat;
import com.dianping.pay.api.entity.DiscountEvent;
import com.dianping.pay.api.entity.UserWallet;

public class OrderController extends Controller {

	public static final SimpleMsg MSG_ERROR_ORDER_LIST = new SimpleMsg(RESOURCE.getItemValue("ORDER", "MSG_ERROR"), RESOURCE.getItemValue("ORDER", "MSG_ERROR_ORDER_LIST"));
	public static final SimpleMsg MSG_ERROR_GET_DELIVERY_AREA = new SimpleMsg(RESOURCE.getItemValue("ORDER", "MSG_ERROR"), RESOURCE.getItemValue("ORDER", "MSG_ERROR_GET_DELIVERY_AREA"));
	public static final SimpleMsg MSG_ERROR_SUBMIT_ORDER = new SimpleMsg(RESOURCE.getItemValue("ORDER", "MSG_ERROR"), RESOURCE.getItemValue("ORDER", "MSG_ERROR_SUBMIT_ORDER"));
	public static final SimpleMsg MSG_ERROR_SUBMIT_LOTTERY = new SimpleMsg(RESOURCE.getItemValue("ORDER", "MSG_ERROR"), RESOURCE.getItemValue("ORDER", "MSG_ERROR_SUBMIT_LOTTERY"), 0);
	public static final SimpleMsg MSG_ERROR_CHECKOUT_ORDER = new SimpleMsg(RESOURCE.getItemValue("ORDER", "MSG_ERROR"), RESOURCE.getItemValue("ORDER", "MSG_ERROR_CHECKOUT_ORDER"));
	public static final SimpleMsg MSG_ERROR_VERIFY_ORDER = new SimpleMsg(RESOURCE.getItemValue("ORDER", "MSG_ERROR"), RESOURCE.getItemValue("ORDER", "MSG_ERROR_VERIFY_ORDER"));

    public DiscountEvent getDiscount() {
        Cat.logEvent("INVALID_METHOD_5", "com.dianping.pay.api.controller.OrderController.getDiscount()");
        return new DiscountEvent();
    }

    public UserWallet getUserWallet(){
        Cat.logEvent("INVALID_METHOD_1", "com.dianping.pay.api.controller.OrderController.getUserWallet()");
        return null;
    }
}