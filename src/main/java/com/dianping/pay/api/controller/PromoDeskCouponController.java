package com.dianping.pay.api.controller;

import com.dianping.api.exception.IllegalApiParamException;
import com.dianping.api.exception.TryLaterException;
import com.dianping.api.framework.Controller;
import com.dianping.cat.Cat;
import com.dianping.combiz.spring.util.PropertiesLoaderSupportUtils;
import com.dianping.pay.api.beans.DiscountRules;
import com.dianping.pay.api.beans.GetPromoDeskCouponRequest;
import com.dianping.pay.api.beans.PromoProduct;
import com.dianping.pay.api.biz.discount.CouponBiz;
import com.dianping.pay.api.biz.discount.CouponMapper;
import com.dianping.pay.api.biz.product.ProductBiz;
import com.dianping.pay.api.biz.promodeskcoupon.PromoDeskCouponStrategy;
import com.dianping.pay.api.biz.promodeskcoupon.PromoDeskCouponStrategyFactory;
import com.dianping.pay.api.entity.promodesk.PromoDeskCoupon;
import com.dianping.pay.api.entity.promodesk.PromoDeskCouponList;
import com.dianping.pay.api.enums.RequestTypeConstant;
import com.dianping.pay.api.exception.VerifyCouponCodeException;
import com.dianping.pay.discount.service.DiscountQueryService;
import com.dianping.pay.discount.service.beans.QueryDiscountCodeRequest;
import com.dianping.pay.discount.service.beans.QueryDiscountCodeResponse;
import com.dianping.pay.discount.service.beans.ValidateDiscountResponse;
import com.dianping.pay.framework.utils.shopUuid.ShopUuidUtils;
import com.google.common.collect.Lists;
import org.apache.commons.lang.StringUtils;

import javax.annotation.Resource;
import java.util.HashSet;
import java.util.Set;

public class PromoDeskCouponController extends Controller {

    @Resource
    private DiscountQueryService discountQueryService;
    @Resource
    private CouponBiz couponBiz;
    @Resource
    private PromoDeskCouponStrategyFactory promoDeskCouponStrategyFactory;
    @Resource
    private ProductBiz productBiz;
    @Resource
    private ShopUuidUtils shopUuidUtils;

    public PromoDeskCouponList getCouponList() {
        Cat.logEvent("INVALID_METHOD_3", "com.dianping.pay.api.controller.PromoDeskCouponController.getCouponList()");
        GetPromoDeskCouponRequest request = new GetPromoDeskCouponRequest(this);
        String requestType = this.controllerKey.contains(".pay") ? RequestTypeConstant.NATIVE : RequestTypeConstant.H5;
        shopUuidUtils.prepareUuidInRequest(request, RequestTypeConstant.NATIVE.equals(requestType));

        request.validate(true);
        PromoDeskCouponStrategy strategy = promoDeskCouponStrategyFactory.getStrategy(request);


        boolean isEnd = true;
        int nextStartIndex = request.getStart() + request.getLimit();
        Set<PromoDeskCoupon> resultSet = new HashSet<PromoDeskCoupon>();
        Set<PromoDeskCoupon> unavailableSet = new HashSet<PromoDeskCoupon>(); // 不可用的抵用券列表，只有request.getStart() == 0的时候才有内容
        Set<PromoDeskCoupon> shopSet = new HashSet<PromoDeskCoupon>();
        Set<PromoDeskCoupon> unavailableShopSet = new HashSet<PromoDeskCoupon>();

        for (PromoProduct promoProduct : request.getPromoProductList()) {
            if (!promoProduct.isSelected()) {
                continue;
            }
            DiscountRules discountRules = productBiz.getDiscountRules(promoProduct);
            if (discountRules.isCanUseCoupon()) {
                boolean isEndOfThisProduct = strategy.assembleCouponSet(resultSet, unavailableSet, request, promoProduct, request.getStart(), request.getLimit());
                isEnd = isEnd && isEndOfThisProduct;
                if (!isEndOfThisProduct) { // 试着拉取更多页的抵用券
                    int morePages = PropertiesLoaderSupportUtils.getIntProperty("pay-api-mobile.promodesk.moreCouponPages", 0);
                    for (int i = 0; i < morePages; i++) {
                        if (resultSet.isEmpty()) {
                            boolean isEndAtThisPage = strategy.assembleCouponSet(resultSet, unavailableSet, request, promoProduct, nextStartIndex, request.getLimit());
                            isEnd = isEnd || isEndAtThisPage;
                            nextStartIndex += request.getLimit();
                            if (isEndAtThisPage) {
                                break;
                            }
                        }
                    }
                }
            }
            strategy.assembleShopCouponSet(shopSet, unavailableShopSet, request, promoProduct);
        }

        PromoDeskCouponList promoDeskCouponList = new PromoDeskCouponList();
        promoDeskCouponList.setIsEnd(isEnd);
        promoDeskCouponList.setStartIndex(request.getStart());
        promoDeskCouponList.setNextStartIndex(nextStartIndex);
        promoDeskCouponList.setList(Lists.newArrayList(resultSet));
        promoDeskCouponList.setUnavailableList(Lists.newArrayList(unavailableSet));
        promoDeskCouponList.setShopCouponList(Lists.newArrayList(shopSet));
        promoDeskCouponList.setUnavailableShopCouponList(Lists.newArrayList(unavailableShopSet));
        couponBiz.assembleCipher(promoDeskCouponList.getList(), request.getUserId());
        couponBiz.assembleCipher(promoDeskCouponList.getShopCouponList(), request.getUserId());
        return promoDeskCouponList;
    }

    public PromoDeskCoupon verifyCouponCode() throws TryLaterException {
        Cat.logEvent("INVALID_METHOD_4", "com.dianping.pay.api.controller.PromoDeskCouponController.verifyCouponCode()");
        throw new IllegalApiParamException("请升级到最新版本主APP", "提示", 0);
    }

    private PromoDeskCoupon doVerifyCouponCode(GetPromoDeskCouponRequest request) throws VerifyCouponCodeException {
        Cat.logEvent("INVALID_METHOD_3", "com.dianping.pay.api.controller.PromoDeskCouponController.doVerifyCouponCode(com.dianping.pay.api.beans.GetPromoDeskCouponRequest)");
        QueryDiscountCodeRequest queryDiscountCodeRequest = new QueryDiscountCodeRequest();
        queryDiscountCodeRequest.setDiscountCode(request.getCouponCode());
        QueryDiscountCodeResponse queryDiscountCodeResponse = discountQueryService.queryDiscountCode(queryDiscountCodeRequest);
        if (!queryDiscountCodeResponse.isSuccess()) {
            throw new VerifyCouponCodeException("该优惠代码不存在", false);
        }

        ValidateDiscountResponse validateDiscountResponse = discountQueryService.validateDiscount(
                CouponMapper.toDiscountValidateContext(
                        queryDiscountCodeResponse.getDiscount().getDiscountID(),
                        request,
                        request.getPromoProductList().get(0) // 目前已经不存在有多个业务的场景了，所以取列表里的第一个
                )
        );
        if (validateDiscountResponse.isValid()) {
            PromoDeskCoupon promoDeskCoupon = CouponMapper.toPromoDeskCoupon(request.getPromoProductList().get(0).getProductCode(), queryDiscountCodeResponse.getDiscount());
            if (promoDeskCoupon != null) {
                couponBiz.assembleCipher(Lists.newArrayList(promoDeskCoupon), request.getUserId());
            }
            return promoDeskCoupon;
        } else {
            throw new VerifyCouponCodeException(
                    StringUtils.defaultIfEmpty(validateDiscountResponse.getPromptText(), "本次消费不能使用该优惠代码"),
                    true
            );
        }
    }
}
