/**
 * 
 */

package com.dianping.pay.api.controller;

import com.dianping.api.exception.IllegalApiParamException;
import com.dianping.api.framework.Controller;
import com.dianping.cat.Cat;
import com.dianping.pay.api.entity.Discount;
import com.dianping.pay.api.entity.DiscountList;
import com.dianping.pay.api.entity.RedEnvelopeList;

public class CouponController extends Controller {

	/**
	 * 返回优惠券列表
	 * 
	 * @return
	 */
	public DiscountList getCouponList() {
        Cat.logEvent("INVALID_METHOD_2", "com.dianping.pay.api.controller.CouponController.getCouponList()");
        throw new IllegalApiParamException("请升级到最新版本的点评app使用", "提示", 0);
	}
	
	public Discount verifyCouponCode() throws Exception {
        Cat.logEvent("INVALID_METHOD_3", "com.dianping.pay.api.controller.CouponController.verifyCouponCode()");
        throw new IllegalApiParamException("请升级到最新版本的点评app使用", "提示", 0);
	}

    public DiscountList getUserDiscount() {
        Cat.logEvent("INVALID_METHOD_2", "com.dianping.pay.api.controller.CouponController.getUserDiscount()");
        throw new IllegalApiParamException("请升级到最新版本的点评app使用", "提示", 0);
    }

    public RedEnvelopeList getUserRedenvelope(){
        Cat.logEvent("INVALID_METHOD_2", "com.dianping.pay.api.controller.CouponController.getUserRedenvelope()");
        throw new IllegalApiParamException("请升级到最新版本的点评app使用", "提示", 0);
    }

    public DiscountList getUserCouponList() throws Exception {
        Cat.logEvent("INVALID_METHOD_5", "com.dianping.pay.api.controller.CouponController.getUserCouponList()");
        throw new IllegalApiParamException("请升级到最新版本的点评app使用", "提示", 0);
    }

    public Discount verifyUserCouponCode() throws Exception {
        Cat.logEvent("INVALID_METHOD_2", "com.dianping.pay.api.controller.CouponController.verifyUserCouponCode()");
        throw new IllegalApiParamException("请升级到最新版本的点评app使用", "提示", 0);
    }

}
