package com.dianping.pay.api.controller;

import com.dianping.api.framework.Controller;
import com.dianping.cat.Cat;
import com.dianping.pay.account.enums.AccountCatalog;
import com.dianping.pay.account.model.UserAccountStatisticsDTO;
import com.dianping.pay.api.beans.DiscountRules;
import com.dianping.pay.api.beans.GetPromoDeskChoiceRequest;
import com.dianping.pay.api.beans.GetPromoDeskRequest;
import com.dianping.pay.api.beans.PromoProduct;
import com.dianping.pay.api.biz.discount.CouponBiz;
import com.dianping.pay.api.biz.discount.CouponMapper;
import com.dianping.pay.api.biz.product.ProductBiz;
import com.dianping.pay.api.biz.user.UserAccountBiz;
import com.dianping.pay.api.entity.promodesk.OptimalPromoTool;
import com.dianping.pay.api.entity.promodesk.PromoDeskCoupon;
import com.dianping.pay.api.enums.RequestTypeConstant;
import com.dianping.pay.common.enums.ProductCode;
import com.dianping.pay.framework.utils.shopUuid.ShopUuidUtils;
import com.dianping.unified.coupon.manage.api.dto.UnifiedCouponDTO;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

public class PromoDeskOptimalController extends Controller {

    @Autowired
    private CouponBiz couponBiz;
    @Autowired
    private ProductBiz productBiz;
    @Autowired
    private UserAccountBiz userAccountBiz;
    @Resource
    private ShopUuidUtils shopUuidUtils;

    /**
     * since 7.9.4
     */
    public OptimalPromoTool getOptimalChoice() throws Exception {
        Cat.logEvent("INVALID_METHOD_4", "com.dianping.pay.api.controller.PromoDeskOptimalController.getOptimalChoice()");
        GetPromoDeskChoiceRequest request = new GetPromoDeskChoiceRequest(this);
        String requestType = this.controllerKey.contains(".pay") ? RequestTypeConstant.NATIVE : RequestTypeConstant.H5;
        shopUuidUtils.prepareUuidInRequest(request, RequestTypeConstant.NATIVE.equals(requestType));

        try {
            request.validate(true);
        } catch (IllegalArgumentException e) {
            return new OptimalPromoTool();
        }

        BigDecimal hongbaoAccountAmount = BigDecimal.ZERO;
        UserAccountStatisticsDTO accountStatistics = userAccountBiz.getAccountStatistics(request.getUserId(), AccountCatalog.RED_ENVELOPE);
        if (accountStatistics != null) {
            hongbaoAccountAmount = accountStatistics.getCatalogBalance(AccountCatalog.RED_ENVELOPE.getCode());
        }

        BigDecimal hongbaoAmount = BigDecimal.ZERO;
        PromoDeskCoupon bestCoupon = null, bestShopCoupon = null;
        for (PromoProduct promoProduct : request.getPromoProductList()) {
            if (!isValidProduct(promoProduct)) {
                continue;
            }
            DiscountRules discountRules = productBiz.getDiscountRules(promoProduct);
            if (discountRules.isCanUseHongBao()) {
                hongbaoAmount = hongbaoAccountAmount;
            }
            if (discountRules.isCanUseCoupon()) {
                PromoDeskCoupon coupon = CouponMapper.toPromoDeskCoupon(promoProduct.getProductCode(), getBestCoupon(request, promoProduct, false));
                if (coupon != null && coupon.compareTo(bestCoupon) > 0) {
                    bestCoupon = coupon;
                }
            }
            if (discountRules.isCanUseShopCoupon() && promoProduct.getProductCode() == ProductCode.MO2O2PAY.getCode()) {
                PromoDeskCoupon shopCoupon = CouponMapper.toPromoDeskCoupon(promoProduct.getProductCode(), getBestCoupon(request, promoProduct, true));
                if (shopCoupon != null && shopCoupon.compareTo(bestShopCoupon) > 0) {
                    bestShopCoupon = shopCoupon;
                }
            }
        }
        return assembleOptimalChoice(bestCoupon, bestShopCoupon, hongbaoAmount, request);
    }

    private OptimalPromoTool assembleOptimalChoice(PromoDeskCoupon coupon, PromoDeskCoupon shopCoupon, BigDecimal hongbaoAmount, GetPromoDeskChoiceRequest request) {
        Cat.logEvent("INVALID_METHOD_2", "com.dianping.pay.api.controller.PromoDeskOptimalController.assembleOptimalChoice(PromoDeskCoupon,PromoDeskCoupon,BigDecimal,GetPromoDeskChoiceRequest)");
        OptimalPromoTool result = new OptimalPromoTool();
        BigDecimal couponAmount = coupon != null ? coupon.getPriceValue() : BigDecimal.ZERO;
        BigDecimal shopCouponAmount = shopCoupon != null ? shopCoupon.getPriceValue() : BigDecimal.ZERO;
        if (hongbaoAmount != null && hongbaoAmount.compareTo(couponAmount.add(shopCouponAmount)) > 0) {
            result.setUseHongBao(true);
        } else {
            if (coupon != null && shopCoupon != null) { // 选了两张券且大于订单剩余金额时不选择券
                BigDecimal totalCouponAmount = coupon.getPriceValue().add(shopCoupon.getPriceValue());
                if (totalCouponAmount.compareTo(request.getRemainOrderAmount()) > 0) {
                    return result;
                }
            }
            result.setCoupon(coupon);
            result.setShopCoupon(shopCoupon);
            List<PromoDeskCoupon> couponsToEncrypt = new ArrayList<PromoDeskCoupon>();
            if (coupon != null) {
                couponsToEncrypt.add(coupon);
            }
            if (shopCoupon != null) {
                couponsToEncrypt.add(shopCoupon);
            }
            couponBiz.assembleCipher(couponsToEncrypt, request.getUserId());
        }
        return result;
    }

    private boolean isValidProduct(PromoProduct promoProduct) {
        Cat.logEvent("INVALID_METHOD_2", "com.dianping.pay.api.controller.PromoDeskOptimalController.isValidProduct(com.dianping.pay.api.beans.PromoProduct)");
        return promoProduct.isSelected()
                && promoProduct.getPrice() != null && promoProduct.getPrice().signum() > 0
                && promoProduct.getQuantity() > 0;
    }

    private UnifiedCouponDTO getBestCoupon(GetPromoDeskRequest request, PromoProduct product, boolean isShopCoupon) {
        Cat.logEvent("INVALID_METHOD_2", "com.dianping.pay.api.controller.PromoDeskOptimalController.getBestCoupon(com.dianping.pay.api.beans.GetPromoDeskRequest,com.dianping.pay.api.beans.PromoProduct,boolean)");
        List<UnifiedCouponDTO> optimalCouponList = couponBiz.getOptimalCoupons(request, product, isShopCoupon);
        if (CollectionUtils.isNotEmpty(optimalCouponList)) {
            return optimalCouponList.get(0);
        }
        return null;
    }
}
