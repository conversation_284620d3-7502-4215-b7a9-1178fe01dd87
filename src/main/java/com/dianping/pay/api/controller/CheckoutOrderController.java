package com.dianping.pay.api.controller;

import com.dianping.api.exception.IllegalApiParamException;
import com.dianping.api.framework.Controller;
import com.dianping.cat.Cat;
import com.dianping.pay.api.entity.CheckoutDiscountResponse;

/**
 * Created by huawei.li on 15/3/10.
 */
public class CheckoutOrderController extends Controller {
    /**
     * 检出提交订单页优惠信息
     * @return
     */
    public CheckoutDiscountResponse checkoutDiscount() throws Exception{
        Cat.logEvent("INVALID_METHOD_2", "com.dianping.pay.api.controller.CheckoutOrderController.checkoutDiscount()");
        throw new IllegalApiParamException("请升级到最新版本的点评app使用", "提示", 0);
    }
}
