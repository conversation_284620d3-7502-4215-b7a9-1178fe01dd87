package com.dianping.pay.api.controller;

import com.dianping.api.framework.Controller;
import com.dianping.api.util.DotUtils;
import com.dianping.combiz.spring.util.PropertiesLoaderSupportUtils;
import com.dianping.pay.api.beans.IssueCouponActivity;
import com.dianping.pay.api.biz.activity.IssueCouponBiz;
import com.dianping.pay.api.biz.activity.newloader.CouponIssueActivityQueryService;
import com.dianping.pay.api.biz.discount.CouponBiz;
import com.dianping.pay.api.entity.issuecoupon.BeautyIssueCouponComponent;
import com.dianping.pay.api.entity.issuecoupon.IssueCouponComponent;
import com.dianping.pay.api.entity.issuecoupon.IssueCouponMsg;
import com.dianping.pay.api.entity.issuecoupon.IssueCouponRequest;
import com.dianping.pay.api.enums.IssueCouponOptionType;
import com.dianping.pay.api.enums.RequestTypeConstant;
import com.dianping.pay.api.exception.IssueCouponException;
import com.dianping.pay.framework.utils.shopUuid.ShopUuidUtils;
import com.dianping.violet.enums.CouponRequestTypeEnum;
import com.google.common.base.Predicate;
import com.google.common.collect.Collections2;
import com.google.common.collect.Lists;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.Validate;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;

import javax.annotation.Resource;
import java.util.List;

public class IssueCouponController extends Controller {

    private static final Logger log = LoggerFactory.getLogger(IssueCouponController.class);

    @Autowired
    private IssueCouponBiz issueCouponBiz;

    @Autowired
    private CouponIssueActivityQueryService couponIssueActivityQueryService;

    @Autowired
    private CouponBiz couponBiz;

    @Resource
    private ShopUuidUtils shopUuidUtils;

    public IssueCouponComponent issueCouponComponent() throws Exception {
        if (!PropertiesLoaderSupportUtils.getBoolProperty("pay-api-mobile.issuecouponcomponent.switch", true)) {
            return new IssueCouponComponent();
        }
        IssueCouponRequest issueCouponRequest = new IssueCouponRequest(this);
        String requestType = this.controllerKey.contains(".pay") ? RequestTypeConstant.NATIVE : RequestTypeConstant.H5;

        shopUuidUtils.prepareUuidInRequest(issueCouponRequest, RequestTypeConstant.NATIVE.equals(requestType));

        String appVersion = issueCouponRequest.isDpClient() ? issueCouponRequest.getClientInfo().getVersion().getVersion() : issueCouponRequest.getClientInfo().getMtVersion();
        DotUtils.addVersionDot(appVersion, requestType, issueCouponRequest.isDpClient());
        log.info(String.format("issueCouponComponent#isdpclient=%s, token=%s, userid=%s",
                issueCouponRequest.isDpClient(),
                this.getParametersMap().getString("token"),
                issueCouponRequest.getUserId()));

        List<IssueCouponActivity> activities = couponIssueActivityQueryService.queryActivities(issueCouponRequest);
        return issueCouponBiz.toIssueCouponComponent(activities);
    }

    public BeautyIssueCouponComponent beautyIssueCouponComponent() throws Exception {
        if (!PropertiesLoaderSupportUtils.getBoolProperty("pay-api-mobile.issuecouponcomponent.switch", true)) {
            return new BeautyIssueCouponComponent();
        }
        IssueCouponRequest issueCouponRequest = new IssueCouponRequest(this, CouponRequestTypeEnum.DISPLAY.getCode());
        String requestType = this.controllerKey.contains(".pay") ? RequestTypeConstant.NATIVE : RequestTypeConstant.H5;

        shopUuidUtils.prepareUuidInRequest(issueCouponRequest, RequestTypeConstant.NATIVE.equals(requestType));

        String appVersion = issueCouponRequest.isDpClient() ? issueCouponRequest.getClientInfo().getVersion().getVersion() : issueCouponRequest.getClientInfo().getMtVersion();
        DotUtils.addVersionDot(appVersion, requestType, issueCouponRequest.isDpClient());
        log.info(String.format("beautyIssueCouponComponent#isdpclient=%s, token=%s, userid=%s",
                issueCouponRequest.isDpClient(),
                this.getParametersMap().getString("token"),
                issueCouponRequest.getUserId()));

        List<IssueCouponActivity> activities = Lists.newArrayList(Collections2.filter(
                couponIssueActivityQueryService.queryActivities(issueCouponRequest),
                new Predicate<IssueCouponActivity>() {
                    @Override
                    public boolean apply(IssueCouponActivity activity) {
                        return activity.getType() != IssueCouponOptionType.DP_PLATFORM_EVENT.getCode()
                                && activity.getType() != IssueCouponOptionType.DP_SHANHUI_EVENT.getCode();
                    }
                })
        );
        return issueCouponBiz.toBeautyIssueCouponComponent(activities);
    }

    public IssueCouponMsg issueCoupon() throws Exception {
        IssueCouponRequest issueCouponRequest = new IssueCouponRequest(this, CouponRequestTypeEnum.ISSUE.getCode());
        String requestType = this.controllerKey.contains(".pay") ? RequestTypeConstant.NATIVE : RequestTypeConstant.H5;

        shopUuidUtils.prepareUuidInRequest(issueCouponRequest, RequestTypeConstant.NATIVE.equals(requestType));

        log.info(String.format("isdpclient=%s, token=%s, userid=%s",
                issueCouponRequest.isDpClient(),
                this.getParametersMap().getString("token"),
                issueCouponRequest.getUserId()));

        if (issueCouponRequest.getUserId() <= 0) {
            throw new IssueCouponException("用户未登录", 3);
        }


        Validate.isTrue(issueCouponRequest.getShopIdL() > 0
                        || issueCouponRequest.getProductId() > 0
                        || issueCouponRequest.getCategoryId() > 0
                        || CollectionUtils.isNotEmpty(issueCouponRequest.getShopIdLList())
                , "invalid shopId/productId/categoryId");
        Validate.isTrue(issueCouponRequest.getCouponOptionId() > 0, "invalid couponOptionId");
        try {
            int couponGroupId = issueCouponBiz.doIssueCoupon(issueCouponRequest);
            return new IssueCouponMsg(0, null, "领取成功", couponBiz.getIssueCouponMsgSuccessContent(couponGroupId));
        } catch (IssueCouponException e) {
            return new IssueCouponMsg(e.getCode(), e.getMessage(), e.getMessage());
        }
    }

}
