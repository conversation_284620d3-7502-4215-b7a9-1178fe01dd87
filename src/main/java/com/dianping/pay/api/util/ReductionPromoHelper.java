package com.dianping.pay.api.util;

import com.dianping.pay.promo.common.enums.promo.SpecialPromoType;
import com.dianping.pay.promo.display.api.dto.PromoDisplayDTO;
import com.google.common.collect.Lists;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.math.BigDecimal;
import java.util.Collections;
import java.util.List;

public class ReductionPromoHelper {

    public static List<PromoDisplayDTO> getIdlePromo(List<PromoDisplayDTO> promoDisplayList) {
        if (CollectionUtils.isEmpty(promoDisplayList)) {
            return Collections.emptyList();
        }

        PromoDisplayDTO maxNormalPromo = getValidPromo(promoDisplayList);

        List<PromoDisplayDTO> result = Lists.newArrayList();
        for (PromoDisplayDTO promo : promoDisplayList) {
            if (isValid(promo) && isIdlePromo(promo) && biggerThanMaxNormal(maxNormalPromo, promo)) {
                result.add(promo);
            }
        }

        return result;
    }

    public static PromoDisplayDTO getValidPromo(List<PromoDisplayDTO> promoDisplayDTOs) {
        if (CollectionUtils.isEmpty(promoDisplayDTOs)) {
            return null;
        }
        PromoDisplayDTO result = null;
        BigDecimal maxCampaignPrice = BigDecimal.ZERO;
        for (PromoDisplayDTO dto : promoDisplayDTOs) {
            if (isValid(dto) && isNormalPromo(dto)
                    && dto.getPromoAmount().compareTo(maxCampaignPrice) > 0) {
                result = dto;
                maxCampaignPrice = dto.getPromoAmount();
            }
        }
        return result;
    }

    public static boolean isValid(PromoDisplayDTO promo) {
        if (promo == null) {
            return false;
        }
        if (StringUtils.isBlank(promo.getDescription()) && StringUtils.isBlank(promo.getTag())) {
            return false;
        }
        return promo.isEnable() && promo.getPromoAmount() != null && promo.getPromoAmount().compareTo(BigDecimal.ZERO) > 0;
    }

    public static boolean isNormalPromo(PromoDisplayDTO promo) {
        return SpecialPromoType.NORMAL.getCode() == promo.getSpecialPromoType();
    }

    public static boolean isIdlePromo(PromoDisplayDTO promo) {
        return SpecialPromoType.IDLETIMES_PROMO.getCode() == promo.getSpecialPromoType();
    }

    private static boolean isShopCartDiscountPromo(PromoDisplayDTO promo) {
        return SpecialPromoType.BUYCAR_COUDAN_DISCOUNT.getCode() == promo.getSpecialPromoType();

    }

    public static boolean biggerThanMaxNormal(PromoDisplayDTO maxNormalPromo, PromoDisplayDTO idlePromo) {
        if (maxNormalPromo == null) {
            return true;
        }
        if (!maxNormalPromo.isEnable() || !maxNormalPromo.isPriceLineThrough()) {
            return true;
        }
        return idlePromo.getPromoAmount().compareTo(maxNormalPromo.getPromoAmount()) > 0;
    }

    public static List<PromoDisplayDTO> getNormalPromo(List<PromoDisplayDTO> promoDisplayList) {
        if (CollectionUtils.isEmpty(promoDisplayList)) {
            return Collections.emptyList();
        }
        List<PromoDisplayDTO> result = Lists.newArrayList();
        for (PromoDisplayDTO promo : promoDisplayList) {
            if (isValid(promo) && isNormalPromo(promo)) {
                result.add(promo);
            }
        }
        return result;
    }


    public static List<PromoDisplayDTO> getBuycarDiscountPromo(List<PromoDisplayDTO> promoDisplayList) {
        if (CollectionUtils.isEmpty(promoDisplayList)) {
            return Collections.emptyList();
        }
        List<PromoDisplayDTO> result = Lists.newArrayList();
        for (PromoDisplayDTO promo : promoDisplayList) {
            if (isValid(promo) && isShopCartDiscountPromo(promo)) {
                result.add(promo);
            }
        }
        return result;
    }

}
