package com.dianping.pay.api.util;

import com.dianping.cat.Cat;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.core.JsonFactory;
import com.fasterxml.jackson.core.JsonGenerator;
import com.fasterxml.jackson.core.JsonParser;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.SerializationFeature;
import com.fasterxml.jackson.databind.module.SimpleModule;
import com.google.common.collect.Lists;
import com.google.gson.Gson;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;

import java.io.IOException;
import java.io.StringWriter;
import java.text.SimpleDateFormat;
import java.util.List;

@Slf4j
public class JsonUtils {

    // 复用的对象单例
    private static JsonFactory jsonFactory = new JsonFactory();

    private static ObjectMapper defaultObjectMapper = createObjectMapper();

    private static ObjectMapper dateFormatObjectMapper = createPrettyObjectMapper();
    public static final Gson gson=new Gson();

    public static <T> T deepCopy(T bean,Class<T> clz){
        return gson.fromJson(gson.toJson(bean),clz);
    }

    /**
     * 创建一个自定义的JSON ObjectMapper
     */
    private static ObjectMapper createObjectMapper() {
        ObjectMapper objectMapper = new ObjectMapper();

        SimpleModule module = new SimpleModule();
        objectMapper.registerModule(module);

        objectMapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
        objectMapper.setSerializationInclusion(JsonInclude.Include.NON_NULL);//不输出空值


        objectMapper.configure(SerializationFeature.FAIL_ON_EMPTY_BEANS, false);
        objectMapper.configure(SerializationFeature.INDENT_OUTPUT, false);
        objectMapper.configure(JsonParser.Feature.ALLOW_UNQUOTED_CONTROL_CHARS, true);
        objectMapper.configure(JsonParser.Feature.ALLOW_UNQUOTED_FIELD_NAMES, true);
        objectMapper.configure(JsonParser.Feature.ALLOW_SINGLE_QUOTES, true);

        return objectMapper;
    }

    /**
     * 创建一个自定义的JSON ObjectMapper
     */
    private static ObjectMapper createPrettyObjectMapper() {
        ObjectMapper objectMapper = createObjectMapper();
        objectMapper.setDateFormat(new SimpleDateFormat("yyyy-MM-dd HH:mm:ss"));
        return objectMapper;
    }

    /**
     * 将对象转换为JSON字符串
     */
    public static <T> String toJson(T t) {
        if (t == null) {
            return null;
        }
        StringWriter sw = new StringWriter();
        JsonGenerator gen = null;
        try {
            gen = jsonFactory.createGenerator(sw);
            defaultObjectMapper.writeValue(gen, t);
            return sw.toString();
        } catch (Exception e) {
            log.error("Json Transfer Exception Occurred!", e);
        } finally {
            if (gen != null) {
                try {
                    gen.close();
                } catch (IOException e) {
                    log.warn("Exception occurred when closing JSON generator!", e);
                }
            }
        }
        return null;
    }

    /**
     * 将对象转换为JSON字符串--时间格式化输出
     */
    public static <T> String toJsonWithDateFormat(T t) {
        if (t == null) {
            return null;
        }
        StringWriter sw = new StringWriter();
        JsonGenerator gen = null;
        try {
            gen = jsonFactory.createGenerator(sw);
            dateFormatObjectMapper.writeValue(gen, t);
            return sw.toString();
        } catch (Exception e) {
            log.error("Json Transfer Exception Occurred!", e);
        } finally {
            if (gen != null) {
                try {
                    gen.close();
                } catch (IOException e) {
                    log.warn("Exception occurred when closing JSON generator!", e);
                }
            }
        }
        return null;
    }

    /**
     * 将JSON字符串转换为指定对象
     */
    public static <T> T toObject(String jsonStr, Class<T> clz) {
        T value = null;
        if(StringUtils.isEmpty(jsonStr)) {
            return value;
        }
        try {
            value = defaultObjectMapper.readValue(jsonStr, clz);
        } catch (IOException e) {
            log.error("jsonStr:{} Transfer Exception Occurred!", jsonStr, e);
        }
        return value;
    }
    /**
     * 将JSON字符串转换为指定对象
     */
    public static <T> List<T> toObjects(String jsonStr, Class<T> clz) {
        Cat.logEvent("INVALID_METHOD_4", "com.dianping.pay.api.util.JsonUtils.toObjects(java.lang.String,java.lang.Class)");

        List<T> list = null;
        if(StringUtils.isEmpty(jsonStr)) {return list;}

        list = Lists.newArrayList();
         try {
             JsonNode jsonNode = defaultObjectMapper.readTree(jsonStr);
             if(!jsonNode.isArray()){
                 log.error(String.format("jsonStr:{} is not array!", jsonStr));
                 return list;
             }

             for (int i = 0; i < jsonNode.size(); i++) {
                 JsonNode node = jsonNode.get(i);

                 list.add(defaultObjectMapper.readValue(node.toString(), clz));
             }
         } catch (Exception e) {
            log.error("transfer jsonStr:{} to objects exception!", jsonStr, e);
         }

        return list;
    }

    /**
     * 将JSON字符串转换为指定对象
     */
    public static <T> T toObject(String jsonStr, TypeReference typeReference) {
        T value = null;
        if(StringUtils.isEmpty(jsonStr) || typeReference==null) {return value;}
        try {
            value = defaultObjectMapper.readValue(jsonStr, typeReference);
        } catch (Exception e) {
            log.error("jsonStr:{} Transfer Exception Occurred!", jsonStr, e);
        }
        return value;
    }


}
