package com.dianping.pay.api.util;

import com.dianping.cat.Cat;
import com.dianping.gmkt.data.base.api.enums.MtUnifiedCouponGroupPlatform;
import com.dianping.lion.client.Lion;
import com.dianping.pay.api.biz.activity.newloader.dto.CouponActivityContext;
import com.dianping.pay.common.enums.PayPlatform;
import com.dianping.pay.framework.utils.IdCryptoUtils;
import com.google.common.base.Charsets;
import com.sankuai.sig.botdefender.core.enums.EnumsLinkExtractType;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.apache.logging.log4j.util.Strings;

import java.net.InetAddress;
import java.net.URLEncoder;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

@Slf4j
public class UrlHelper {

    public static String getCouponMtWebUrl(String unifiedCouponId) {
        Cat.logEvent("INVALID_METHOD_2", "com.dianping.pay.api.util.UrlHelper.getCouponMtWebUrl(java.lang.String)");
        String baseUrl = Lion.getStringValue(LionConstants.MT_COUPON_WEB_URL,
                "https://g.meituan.com/av/rainbow/1737308/index.html?couponid=");
        return baseUrl + unifiedCouponId;
    }

    public static String getCouponDpWebUrl(String unifiedCouponId) {
        Cat.logEvent("INVALID_METHOD_5", "com.dianping.pay.api.util.UrlHelper.getCouponDpWebUrl(java.lang.String)");
        String baseUrl = Lion.getStringValue(LionConstants.DP_COUPON_WEB_URL,
                "https://g.dianping.com/av/rainbow/1737308/index.html?couponid=");
        return baseUrl + unifiedCouponId;
    }

    public static String getWxBaseShareUrl(String h5Url, boolean mt) {
        Cat.logEvent("INVALID_METHOD_5", "com.dianping.pay.api.util.UrlHelper.getWxBaseShareUrl(java.lang.String,boolean)");
        if (StringUtils.isEmpty(h5Url)) {
            return null;
        }
        String baseUrl;
        if (mt) {
            baseUrl = h5Url + "&product=mtwxapp";
            return "/index/pages/h5/h5?f_openId=1&f_token=1&weburl=" + encode(baseUrl);
        } else {
            baseUrl = h5Url + "&utm_source=dianping-wxapp&token=!&openId=!";
            return "/pages/webview/webview?url=" + encode(baseUrl);
        }
    }

    public static String getCouponDpUrl(String unifiedCouponId) {
        Cat.logEvent("INVALID_METHOD_3", "com.dianping.pay.api.util.UrlHelper.getCouponDpUrl(java.lang.String)");
        String baseUrl = Lion.getStringValue(LionConstants.DP_COUPON_URL,
                "dianping://web?url=https%3a%2f%2fg.dianping.com%2fav%2frainbow%2f1737308%2findex.html%3fcouponid%3d");
        return String.format("%s%s", baseUrl, unifiedCouponId);
    }

    public static String getCouponMtUrl(String unifiedCouponId) {
        Cat.logEvent("INVALID_METHOD_5", "com.dianping.pay.api.util.UrlHelper.getCouponMtUrl(java.lang.String)");
        String baseUrl = Lion.getStringValue(LionConstants.MT_COUPON_URL,
                "imeituan://www.meituan.com/web?url=https%3a%2f%2fg.meituan.com%2fav%2frainbow%2f1737308%2findex.html%3fcouponid%3d");
        return String.format("%s%s", baseUrl, unifiedCouponId);
    }


    public static String getIP() {
        Cat.logEvent("INVALID_METHOD_4", "com.dianping.pay.api.util.UrlHelper.getIP()");
        try {
            return InetAddress.getLocalHost().getHostAddress().toString();
        } catch (Exception e) {
            log.error("can't not get ip!", e);
        }
        return Strings.EMPTY;
    }

    public static String encode(String input) {
        if (org.apache.commons.lang3.StringUtils.isBlank(input)) {
            return input;
        }
        try {
            return URLEncoder.encode(input, Charsets.UTF_8.name());
        } catch (Exception e) {
            log.error("netUtils encode has error", e);
        }
        return null;
    }

    public static String getBonusUrl(CouponActivityContext ctx) {
        String baseUrl;
        StringBuilder stringBuilder = new StringBuilder();
        if (ctx.isMt()) {
            baseUrl = Lion.getStringValue(LionConstants.BONUS_MT_URL,
                    "http://test-g.meituan.com/app/gfe-deal-return-present/index.html");
            stringBuilder.append(baseUrl).append("?shopid=")
                    .append(ctx.getMtShopIdL()).append("&dealid=")
                    .append(ctx.getMtDealGroupId()).append("&type=").append(1);
            stringBuilder.append(IdCryptoUtils.genLinkEncryptParam(EnumsLinkExtractType.queries, ctx.getMtShopIdL(), "shopid"));
        } else {
            baseUrl = Lion.getStringValue(LionConstants.BONUS_DP_URL,
                    "http://g.51ping.com/app/gfe-deal-return-present/index.html");
            stringBuilder.append(baseUrl)
                    .append("?shopuuid=").append(ctx.getDpShopUuid())
                    .append("&dealid=").append(ctx.getDpDealGroupId())
                    .append("&type=").append(1);
            stringBuilder.append(IdCryptoUtils.genLinkEncryptParam(EnumsLinkExtractType.queries, ctx.getDpShopUuid(), "shopuuid"));
        }
        int payPlatform = ctx.getCouponPlatform();
        if (payPlatform == PayPlatform.tg_pc.getCode() || payPlatform == MtUnifiedCouponGroupPlatform.WEB_PAGE.getCode()) {
            return stringBuilder.toString();
        }
        if (payPlatform == PayPlatform.weixin_api.getCode() || payPlatform == MtUnifiedCouponGroupPlatform.WEIXIN_CX.getCode()) {
            return getWxBaseShareUrl(stringBuilder.toString(), ctx.isMt());
        }
        return getAppUrl(stringBuilder.toString(), ctx.getDpShopUuid(), ctx.isMt());
    }


    public static String getAppUrl(String shareUrl, String dpShopUuid, boolean mt) {
        if (StringUtils.isEmpty(shareUrl)) {
            return null;
        }
        if (mt) {
            return "imeituan://www.meituan.com/web?url=" + encode(shareUrl);
        } else {
            shareUrl = filterUrl(shareUrl, dpShopUuid);
            return "dianping://web?url=" + encode(shareUrl);
        }
    }

    private static final Pattern SHOP_ID_PATTERN = Pattern.compile("(?<=[?&]shopid=)(\\w+)");

    public static String filterUrl(String url, String dpShopUuid) {
        if (StringUtils.isBlank(url) || url.contains("shopuuid")) {
            return url;
        }

        Matcher m = SHOP_ID_PATTERN.matcher(url);
        if (m.find()) {
            int shopId = Integer.parseInt(m.group(1));

            if (StringUtils.isNotBlank(dpShopUuid)) {
                url += "&shopuuid=" + dpShopUuid;
                url += IdCryptoUtils.genLinkEncryptParam(EnumsLinkExtractType.queries, dpShopUuid, "shopuuid");
            }
        }

        return url;
    }

    public static String getUrl(String unifiedCouponId, boolean isMt) {
        Cat.logEvent("INVALID_METHOD_4", "com.dianping.pay.api.util.UrlHelper.getUrl(java.lang.String,boolean)");
        if (isMt) {
            return generateMTH5CouponDetailUrl(unifiedCouponId);
        } else {
            return generateH5CouponDetailUrl(unifiedCouponId);
        }
    }

    private static String generateMTH5CouponDetailUrl(String unifiedCouponId) {
        Cat.logEvent("INVALID_METHOD_2", "com.dianping.pay.api.util.UrlHelper.generateMTH5CouponDetailUrl(java.lang.String)");
        String couponH5DetailUrlTemplateMt = Lion.getStringValue("unified-coupon-manage-service.coupon.h5.mt.detail.url.template", "https://i.meituan.com/group/coupondetail/detail.html?couponid=");
        return couponH5DetailUrlTemplateMt+ unifiedCouponId;
    }

    private static String generateH5CouponDetailUrl(String unifiedCouponId) {
        Cat.logEvent("INVALID_METHOD_5", "com.dianping.pay.api.util.UrlHelper.generateH5CouponDetailUrl(java.lang.String)");
        String couponH5DetailUrlTemplate = Lion.getStringValue("unified-coupon-manage-service.coupon.h5.detail.url.template");
        return couponH5DetailUrlTemplate + unifiedCouponId;
    }

}
