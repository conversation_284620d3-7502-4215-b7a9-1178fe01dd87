package com.dianping.pay.api.util;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import org.apache.commons.lang3.StringUtils;

import java.util.List;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

public class RichTextBuilder {
    List<TextItem> textItemList = Lists.newArrayList();
    private RichTextBuilder.TextItem defaultTextItem;
    private RichTextBuilder.TextItem hltTextItem;
    private String regexText;
    private String sourceText;

    public RichTextBuilder(String sourceText, String regex) {
        this.defaultTextItem = new RichTextBuilder.TextItem(12, RichTextBuilder.ARGBColor.GRAY.color, RichTextBuilder.ARGBColor.TRANSPARENTWHITE.color, RichTextBuilder.TextStyle.DEFAULT.style, false, false);
        this.hltTextItem = new RichTextBuilder.TextItem(12, RichTextBuilder.ARGBColor.RED.color, RichTextBuilder.ARGBColor.TRANSPARENTWHITE.color, RichTextBuilder.TextStyle.DEFAULT.style, false, false);
        this.regexText = "";
        this.sourceText = "";
        this.regexText = regex;
        this.sourceText = sourceText;
    }

    public RichTextBuilder build() {
        if (StringUtils.isEmpty(this.sourceText)) {
            return this;
        } else {
            Pattern pattern = Pattern.compile(this.regexText);
            Matcher matcher = pattern.matcher(this.sourceText);
            boolean isFind = false;
            int lastMatchEnd = 0;

            while(true) {
                int start;
                int end;
                do {
                    if (!matcher.find()) {
                        RichTextBuilder.TextItem defaultTextItem;
                        if (isFind && lastMatchEnd < this.sourceText.length() - 1) {
                            defaultTextItem = this.getDefaultTextItem().clone();
                            defaultTextItem.setText(this.sourceText.substring(lastMatchEnd, this.sourceText.length()));
                            this.textItemList.add(defaultTextItem);
                        }

                        if (!isFind) {
                            defaultTextItem = this.getDefaultTextItem().clone();
                            defaultTextItem.setText(this.sourceText);
                            this.textItemList.add(defaultTextItem);
                        }

                        return this;
                    }

                    isFind = true;
                    start = matcher.start();
                    end = matcher.end();
                } while(start <= lastMatchEnd && start != 0);

                RichTextBuilder.TextItem hltTextItem;
                if (start != 0) {
                    hltTextItem = this.getDefaultTextItem().clone();
                    hltTextItem.setText(this.sourceText.substring(lastMatchEnd, start));
                    this.textItemList.add(hltTextItem);
                }

                hltTextItem = this.getHltTextItem().clone();
                hltTextItem.setText(this.sourceText.substring(start, end));
                this.textItemList.add(hltTextItem);
                lastMatchEnd = end;
            }
        }
    }

    public String toString() {
        return JSON.toJSONString(this.textItemList);
    }

    public RichTextBuilder prepareResource(String sourceText, String regexText) {
        this.sourceText = sourceText;
        this.regexText = regexText;
        return this;
    }

    public RichTextBuilder.TextItem getDefaultTextItem() {
        return this.defaultTextItem;
    }

    public RichTextBuilder setDefaultTextItem(RichTextBuilder.TextItem defaultTextItem) {
        this.defaultTextItem = defaultTextItem;
        return this;
    }

    public RichTextBuilder.TextItem getHltTextItem() {
        return this.hltTextItem;
    }

    public RichTextBuilder setHltTextItem(RichTextBuilder.TextItem hltTextItem) {
        this.hltTextItem = hltTextItem;
        return this;
    }

    public List<RichTextBuilder.TextItem> getTextItemList() {
        return this.textItemList;
    }

    public void setTextItemList(List<RichTextBuilder.TextItem> textItemList) {
        this.textItemList = textItemList;
    }

    public String getRegexText() {
        return this.regexText;
    }

    public void setRegexText(String regexText) {
        this.regexText = regexText;
    }

    public String getSourceText() {
        return this.sourceText;
    }

    public void setSourceText(String sourceText) {
        this.sourceText = sourceText;
    }

    public static class TextItem {
        private String text;
        private int textsize = 0;
        private String textcolor = "#FF000000";
        private String backgroundcolor = "#00FFFFFF";
        private String textstyle = "Default";
        private boolean strikethrough = false;
        private boolean underline = false;

        public TextItem() {
        }

        public TextItem(int textsize, String textcolor, String backgroundcolor, String textstyle, boolean strikethrough, boolean underline) {
            this.text = "";
            this.textsize = textsize;
            this.textcolor = textcolor;
            this.backgroundcolor = backgroundcolor;
            this.textstyle = textstyle;
            this.strikethrough = strikethrough;
            this.underline = underline;
        }

        public RichTextBuilder.TextItem clone() {
            return new RichTextBuilder.TextItem(this.textsize, this.textcolor, this.backgroundcolor, this.textstyle, this.strikethrough, this.underline);
        }

        public String getText() {
            return this.text;
        }

        public void setText(String text) {
            this.text = text;
        }

        public int getTextsize() {
            return this.textsize;
        }

        public void setTextsize(int textsize) {
            this.textsize = textsize;
        }

        public String getTextcolor() {
            return this.textcolor;
        }

        public void setTextcolor(String textcolor) {
            this.textcolor = textcolor;
        }

        public String getBackgroundcolor() {
            return this.backgroundcolor;
        }

        public void setBackgroundcolor(String backgroundcolor) {
            this.backgroundcolor = backgroundcolor;
        }

        public String getTextstyle() {
            return this.textstyle;
        }

        public void setTextstyle(String textstyle) {
            this.textstyle = textstyle;
        }

        public boolean isStrikethrough() {
            return this.strikethrough;
        }

        public void setStrikethrough(boolean strikethrough) {
            this.strikethrough = strikethrough;
        }

        public boolean isUnderline() {
            return this.underline;
        }

        public void setUnderline(boolean underline) {
            this.underline = underline;
        }
    }

    public static enum TextStyle {
        DEFAULT("Default"),
        BOLD("Bold"),
        ITALIC("Italic"),
        BOLD_ITALIC("Bold_Italic");

        private String style;

        private TextStyle(String style) {
            this.style = style;
        }

        public String getStyle() {
            return this.style;
        }
    }

    public static enum ARGBColor {
        BLACK("#FF000000"),
        WHITE("#FFFFFFFF"),
        RED("#FFFF0000"),
        GREEN("#FF00FF00"),
        BLUE("#FF0000FF"),
        GRAY("#FF999999"),
        YELLOW("#FFFFFF00"),
        TRANSPARENTWHITE("#00FFFFFF");

        private final String color;

        private ARGBColor(String color) {
            this.color = color;
        }

        public String getColor() {
            return this.color;
        }
    }
}

