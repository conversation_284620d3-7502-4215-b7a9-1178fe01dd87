package com.dianping.pay.api.util;

import com.dianping.gmkt.coupon.common.api.enums.CouponGroupValueType;
import com.dianping.gmkt.coupon.common.api.enums.MerchantCouponProductType;
import com.dianping.pay.api.beans.IssueCouponActivity;
import com.dianping.pay.api.biz.activity.newloader.dto.CouponActivityContext;
import com.dianping.pay.api.entity.issuecoupon.CouponDetailInfo;
import com.dianping.pay.api.entity.issuecoupon.ShopCartBrandProduct;
import com.dianping.pay.api.entity.issuecoupon.ShopCartProduct;
import com.dianping.pay.api.enums.ProductTypeEnum;
import com.dianping.tgc.open.entity.BizIdType;
import com.dianping.unified.coupon.manage.api.dto.DiscountCouponDTO;
import com.dianping.unified.coupon.manage.api.dto.UnifiedCouponDTO;
import com.dianping.unified.coupon.manage.api.dto.UnifiedCouponGroupDTO;
import com.google.common.base.Function;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang.StringUtils;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * <AUTHOR>
 * @date 2021/1/21
 */
public class ShopCartPromoInfoHelper {

    public static CouponDetailInfo genCouponDetailItem(UnifiedCouponDTO unifiedCouponDTO) {
        CouponDetailInfo item = new CouponDetailInfo();
        UnifiedCouponGroupDTO unifiedCouponGroupDTO = unifiedCouponDTO.getCouponGroupDTO();
        item.setCouponGroupId(StringUtils.isBlank(unifiedCouponGroupDTO.getUnifiedCouponGroupId()) ?
                String.valueOf(unifiedCouponGroupDTO.getCouponGroupId()) :
                unifiedCouponGroupDTO.getUnifiedCouponGroupId());
        item.setAmount(unifiedCouponGroupDTO.getDiscountAmount().stripTrailingZeros().toPlainString());
        item.setDiscountAmount(unifiedCouponGroupDTO.getDiscountAmount());

        item.setCanAssign(false);
        item.setTitle(unifiedCouponGroupDTO.getDisplayTitle());

        item.setCouponSuitDesc(IssueCouponUtils.formatTimePeriod(unifiedCouponDTO.getBeginTime(), unifiedCouponDTO.getEndTime()));
        item.setCouponThresholdDesc(IssueCouponUtils.formatTitle(unifiedCouponGroupDTO));
        item.setFreshExclusive(UserIdentityUtils.beNewUser(unifiedCouponGroupDTO));
        item.setCouponSrc(0);

        item.setPriceLimit(unifiedCouponGroupDTO.getPriceLimit());
        return item;
    }

    public static CouponDetailInfo genCouponDetailItem(IssueCouponActivity issueCouponActivity) {
        CouponDetailInfo item = new CouponDetailInfo();
        UnifiedCouponGroupDTO unifiedCouponGroupDTO = issueCouponActivity.getCouponGroup();
        int merchantProductType = IssueCouponUtils.getMerchantCouponProductType(unifiedCouponGroupDTO);

        if (!IssueCouponTypeUtils.isIssued(issueCouponActivity)) {
            item.setCanAssign(true);
            item.setCouponSuitDesc(IssueCouponUtils.formatCouponTimePeriod(unifiedCouponGroupDTO));
        } else {
            item.setCanAssign(false);
            UnifiedCouponDTO unifiedCouponDTO = IssueCouponTypeUtils.getIssuedUnifiedCouponDto(issueCouponActivity);
            item.setCouponSuitDesc(IssueCouponUtils.formatTimePeriod(unifiedCouponDTO.getBeginTime(), unifiedCouponDTO.getEndTime()));
        }
        item.setCouponGroupId(StringUtils.isBlank(unifiedCouponGroupDTO.getUnifiedCouponGroupId()) ?
                String.valueOf(unifiedCouponGroupDTO.getCouponGroupId()) :
                unifiedCouponGroupDTO.getUnifiedCouponGroupId());
        item.setAmount(unifiedCouponGroupDTO.getDiscountAmount().stripTrailingZeros().toPlainString());
        item.setDiscountAmount(unifiedCouponGroupDTO.getDiscountAmount());

        item.setTitle(IssueCouponUtils.getMerchantCouponTitle(merchantProductType));

        item.setCouponThresholdDesc(IssueCouponUtils.formatTitle(unifiedCouponGroupDTO));

        item.setFreshExclusive(IssueCouponUtils.isNewUserCoupon(unifiedCouponGroupDTO));
        item.setCouponSrc(1);

        item.setPriceLimit(unifiedCouponGroupDTO.getPriceLimit());
        item.setMerchantProductType(merchantProductType);
        if (unifiedCouponGroupDTO.getValueType() == CouponGroupValueType.DISCOUNT.getCode()) {
            DiscountCouponDTO discountCouponDTO = unifiedCouponGroupDTO.getDiscountCouponDTO();
            item.setAmount((double) discountCouponDTO.getDiscount() / 10 + "");
            item.setAmountCornerMark("折");
        } else {
            item.setAmount(unifiedCouponGroupDTO.getDiscountAmount().stripTrailingZeros().toPlainString());
            item.setAmountCornerMark("元");
        }
        return item;
    }

    public static Map<String, List<Integer>> genBrandCouponIds(Map<Integer, Map<String, List<Integer>>> bizTypeBizActivityIdsMap,
                                                               List<ShopCartBrandProduct> shopCartBrandProducts, List<IssueCouponActivity> issueCouponActivities, CouponActivityContext context) {

        Map<Integer, IssueCouponActivity> activityCouponGroupMap = Maps.uniqueIndex(issueCouponActivities, new Function<IssueCouponActivity, Integer>() {
            @Override
            public Integer apply(IssueCouponActivity issueCouponActivity) {
                return issueCouponActivity.getActivityId();
            }
        });

        Map<String, List<Integer>> skuActivitiesMap = bizTypeBizActivityIdsMap.containsKey(BizIdType.SKU_ID.getCode()) ?
                bizTypeBizActivityIdsMap.get(BizIdType.SKU_ID.getCode()) :
                null;

        Map<String, List<Integer>> dealActivitiesMap = bizTypeBizActivityIdsMap.containsKey(BizIdType.DEAL_GROUP_ID.getCode()) ?
                bizTypeBizActivityIdsMap.get(BizIdType.DEAL_GROUP_ID.getCode()) :
                null;

        Map<String, List<Integer>> brandCouponIdsMap = Maps.newHashMap();
        Map<Integer, Integer> mtDpDealGroupMap = context.getMtDpDealGroupMap();

        for (ShopCartBrandProduct shopCartBrandProduct : shopCartBrandProducts) {
            Set<Integer> couponGroupIds = Sets.newHashSet();
            List<ShopCartProduct> shopCartProducts = shopCartBrandProduct.getShopcartproducts();
            for (ShopCartProduct shopCartProduct : shopCartProducts) {
                Set<Integer> productCouponGroupIds = convertToCouponGroupIds(shopCartProduct, context, dealActivitiesMap, mtDpDealGroupMap, activityCouponGroupMap, skuActivitiesMap);
                if (CollectionUtils.isNotEmpty(productCouponGroupIds)) {
                    couponGroupIds.addAll(productCouponGroupIds);
                }
            }
            if (CollectionUtils.isNotEmpty(couponGroupIds)) {
                brandCouponIdsMap.put(shopCartBrandProduct.getBrandid(), Lists.newArrayList(couponGroupIds));
            }
        }

        return brandCouponIdsMap;
    }

    private static Set<Integer> convertToCouponGroupIds(ShopCartProduct shopCartProduct, CouponActivityContext context, Map<String, List<Integer>> dealActivitiesMap,
                                                        Map<Integer, Integer> mtDpDealGroupMap, Map<Integer, IssueCouponActivity> activityCouponGroupMap, Map<String, List<Integer>> skuActivitiesMap) {
        Set<Integer> couponGroupIds = Sets.newHashSet();
        Integer productId = shopCartProduct.getProductid();
        if (shopCartProduct.getProducttype() == ProductTypeEnum.DEAL_GROUP.getCode() && MapUtils.isNotEmpty(dealActivitiesMap)) {
            List<Integer> activityIds;
            if (context.isMt()) {
                Integer dpDealGroupId = MapUtils.isNotEmpty(mtDpDealGroupMap) && mtDpDealGroupMap.containsKey(productId) ?
                        mtDpDealGroupMap.get(productId) :
                        null;
                activityIds = dpDealGroupId != null && dealActivitiesMap.containsKey(String.valueOf(dpDealGroupId)) ?
                        dealActivitiesMap.get(String.valueOf(dpDealGroupId)) :
                        null;
            } else {
                activityIds = dealActivitiesMap.get(String.valueOf(productId));
            }
            setCouponGroupIds(couponGroupIds, activityIds, activityCouponGroupMap);
        }
        if (shopCartProduct.getProducttype() == ProductTypeEnum.SKU.getCode() && MapUtils.isNotEmpty(skuActivitiesMap) &&
                skuActivitiesMap.containsKey(String.valueOf(productId))) {
            List<Integer> activityIds = skuActivitiesMap.get(String.valueOf(productId));
            setCouponGroupIds(couponGroupIds, activityIds, activityCouponGroupMap);
        }
        return couponGroupIds;
    }

    private static void setCouponGroupIds(Set<Integer> couponGroupIds, List<Integer> activityIds, Map<Integer, IssueCouponActivity> activityCouponGroupMap) {
        if (CollectionUtils.isNotEmpty(activityIds)) {
            for (Integer activityId : activityIds) {
                couponGroupIds.add(activityCouponGroupMap.get(activityId).getCouponGroupId());
            }
        }
    }

    public static Map<String, List<CouponDetailInfo>> genBrandCouponInfoMap(Map<String, List<Integer>> brandCouponIdsMap, Map<String, CouponDetailInfo> couponDetailInfoMap) {
        Map<String, List<CouponDetailInfo>> brandCouponDetailInfosMap = Maps.newHashMap();
        for (Map.Entry<String, List<Integer>> entry : brandCouponIdsMap.entrySet()) {
            String brandId = entry.getKey();
            List<Integer> couponGroupIds = entry.getValue();
            List<CouponDetailInfo> couponDetailInfos = Lists.newArrayList();
            for (Integer couponGroupId : couponGroupIds) {
                if (couponDetailInfoMap.containsKey(String.valueOf(couponGroupId))) {
                    couponDetailInfos.add(couponDetailInfoMap.get(String.valueOf(couponGroupId)));
                }
            }
            if (CollectionUtils.isNotEmpty(couponDetailInfos)) {
                brandCouponDetailInfosMap.put(brandId, couponDetailInfos);
            }
        }
        return brandCouponDetailInfosMap;
    }

    public static String genCouponTag(CouponDetailInfo couponDetailInfo) {
        String baseInfo = couponDetailInfo.getPriceLimit() == null || couponDetailInfo.getPriceLimit().compareTo(new BigDecimal(0)) == 0 ?
                "无门槛减" + couponDetailInfo.getDiscountAmount().stripTrailingZeros().toPlainString() :
                "满" + couponDetailInfo.getPriceLimit().stripTrailingZeros().toPlainString() + "减" + couponDetailInfo.getDiscountAmount().stripTrailingZeros().toPlainString();
        if (couponDetailInfo.getCouponSrc() == 1) {
            if (couponDetailInfo.getMerchantProductType() == MerchantCouponProductType.ALL_PRODUCT.getValue()) {
                if (couponDetailInfo.isFreshExclusive()) {
                    return "新客" + baseInfo;
                } else {
                    return "店铺券" + baseInfo;
                }
            } else if (couponDetailInfo.getMerchantProductType() == MerchantCouponProductType.CERTAIN_LABLE.getValue()) {
                return "品类券" + baseInfo;
            } else {
                return "商品券" + baseInfo;
            }
        } else {
            return baseInfo;
        }
    }
}
