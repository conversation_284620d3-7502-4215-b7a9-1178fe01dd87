package com.dianping.pay.api.util;

import com.dianping.pay.api.beans.IssueCouponActivity;
import com.dianping.pay.api.entity.issuecoupon.CouponDetailInfo;
import com.dianping.pay.common.enums.ProductCode;
import com.dianping.unified.coupon.manage.api.dto.UnifiedCouponGroupDTO;

import java.math.BigDecimal;
import java.util.Comparator;

public class CouponDetailInfoComparator implements Comparator<CouponDetailInfo> {

    @Override
    public int compare(CouponDetailInfo obj1, CouponDetailInfo obj2) {
//        boolean isMerchant1 = obj1.getCouponSrc() == 1;
//        boolean isMerchant2 = obj2.getCouponSrc() == 1;
//        if (!isMerchant1 && isMerchant2) {
//            return 1;
//        }
//        if (!isMerchant2 && isMerchant1) {
//            return -1;
//        }
        if (obj1 == null || obj1.getDiscountAmount() == null) {
            return -1;
        }
        if (obj2 == null || obj2.getDiscountAmount() == null) {
            return -1;
        }
        return obj2.getDiscountAmount().compareTo(obj1.getDiscountAmount());
    }
}
