package com.dianping.pay.api.util;

import com.sankuai.nibmktproxy.queryclient.proxy.CouponDTO;
import com.sankuai.nibmktproxy.queryclient.proxy.GetPromotionDTO;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import java.util.Comparator;

public class ProxyPromoComparator implements Comparator<GetPromotionDTO> {

    private static Logger logger = LogManager.getLogger(ProxyPromoComparator.class);

    @Override
    public int compare(GetPromotionDTO coupon1, GetPromotionDTO coupon2) {
        // 排序顺序：已领商家 -> 已领平台 -> 未领商家 -> 未领平台

        try {
            CouponDTO couponDTO1 = coupon1.getPromotionDTO().getCouponDTO();
            CouponDTO couponDTO2 = coupon2.getPromotionDTO().getCouponDTO();

            // 先根据已领 & 未领做区分
            if (couponDTO1.canAssign && !couponDTO2.canAssign) {
                return 1;
            } else if (!couponDTO1.canAssign && couponDTO2.canAssign) {
                return -1;
            }

            // 然后根据商家券 & 平台券进行判定
            if (couponDTO1.isIsMerchantCoupon() && !couponDTO2.isIsMerchantCoupon()) {
                return -1;
            } else if (couponDTO2.isIsMerchantCoupon() && !couponDTO1.isIsMerchantCoupon()) {
                return 1;
            }

            // 然后根据价格 (不区分券类型了，以实际价格为准)
            int priceCompare = couponDTO1.getCouponValue().compareTo(couponDTO2.getCouponValue());
            if (priceCompare != 0) {
                // 价格降序
                return priceCompare;
            }
            return 0;
        } catch (Exception e) {
            logger.error("ProxyPromoComparator# compare error. coupon1: {}, coupon2: {}", coupon1, coupon2, e);
            return 0;
        }
    }
}
