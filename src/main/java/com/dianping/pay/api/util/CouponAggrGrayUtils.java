package com.dianping.pay.api.util;

import com.dianping.lion.client.Lion;
import com.google.common.collect.ImmutableSet;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;

import java.util.Collections;

@Slf4j
public class CouponAggrGrayUtils {

    public static ImmutableSet<Long> POI_COMPONENT_COUPON_AGGR_GRAY_USER;

    public static int POI_COMPONENT_COUPON_AGGR_GRAY_USER_RATE;

    static {
        POI_COMPONENT_COUPON_AGGR_GRAY_USER = ImmutableSet.copyOf(Lion.getList(LionConstants.POI_COMPONENT_COUPON_AGGR_GRAY_USER, Long.class, Collections.emptyList()));
        Lion.addConfigListener(LionConstants.POI_COMPONENT_COUPON_AGGR_GRAY_USER, configEvent -> {
            POI_COMPONENT_COUPON_AGGR_GRAY_USER = ImmutableSet.copyOf(Lion.getList(LionConstants.POI_COMPONENT_COUPON_AGGR_GRAY_USER, Long.class, Collections.emptyList()));
        });

        POI_COMPONENT_COUPON_AGGR_GRAY_USER_RATE = Lion.getIntValue(LionConstants.POI_COMPONENT_COUPON_AGGR_GRAY_USER_RATE, 0);
        Lion.addConfigListener(LionConstants.POI_COMPONENT_COUPON_AGGR_GRAY_USER_RATE, configEvent -> {
            POI_COMPONENT_COUPON_AGGR_GRAY_USER_RATE = Lion.getIntValue(LionConstants.POI_COMPONENT_COUPON_AGGR_GRAY_USER_RATE, 0);
        });
    }

    public static boolean hitCouponAggrGrayRule(Long userId) {
        try {
            // userId白 优先级高于 user比例白
            if (POI_COMPONENT_COUPON_AGGR_GRAY_USER.contains(userId)) {
                return true;
            }
            return userId % 100 < POI_COMPONENT_COUPON_AGGR_GRAY_USER_RATE;
        } catch (Exception e) {
            log.error("hitCouponAggrGrayRule exception: ", e);
        }
        return false;
    }
}
