package com.dianping.pay.api.util;

public class LionConstants {

    public static final String APP_KEY = "mapi-pay-promo-web";

    // 领券组件外层整体模块bid打点
    public static final String SIMPLE_MODULE_BID_VIEW = "mapi-pay-promo-web.simple.module.bid.view";
    // 领券组件外层模块click打点
    public static final String SIMPLE_MODULE_BID_CLICK = "mapi-pay-promo-web.simple.module.bid.click";
    // 领券组件外层查看全部券click打点
    public static final String SIMPLE_MODULE_VIEW_CLICK = "mapi-pay-promo-web.simple.module.view.all.bid.click";
    // 领券组件外层查看券信息click打点
    public static final String SIMPLE_MODULE_COUPON_INFO_CLICK = "mapi-pay-promo-web.simple.module.coupon.info.bid.click";
    // 领券组件-浮层整体浏览bid打点
    public static final String MODULE_BID_VIEW = "mapi-pay-promo-web.module.bid.view";
    // 领券组件-浮层适用品类bid打点
    public static final String MODULE_CATEGORY_BID_VIEW = "mapi-pay-promo-web.module.category.bid.view";
    // 领券组件-浮层适用品类click打点
    public static final String MODULE_CATEGORY_CLICK_VIEW = "mapi-pay-promo-web.module.category.bid.click";
    // 领券组件-浮层单个券bid打点
    public static final String MODULE_COUPON_BID_VIEW = "mapi-pay-promo-web.module.coupon.bid.view";
    // 领券组件-浮层单个券click打点
    public static final String MODULE_COUPON_CLICK_VIEW = "mapi-pay-promo-web.module.coupon.bid.click";

    public static final String SHARE_COUPON_QUERY_SWITCH = "mapi-pay-promo-web.share.coupon.query.switch";



    // 单返礼是否要跳转开关
    public static final String BONUS_REDIRECT_ENABLE = "mapi-pay-promo-web.bonus.redirect.enable";
    // 美团券跳转链接
    public static final String MT_COUPON_WEB_URL = "mapi-pay-promo-web.unified.coupon.mt.web.url";
    // 点评券跳转链接
    public static final String DP_COUPON_WEB_URL = "mapi-pay-promo-web.unified.coupon.dp.web.url";
    // 美团券跳转链接
    public static final String MT_COUPON_URL = "mapi-pay-promo-web.unified.coupon.mt.url";
    // 点评券跳转链接
    public static final String DP_COUPON_URL = "mapi-pay-promo-web.unified.coupon.dp.url";
    // 返礼美团跳转链接
    public static final String BONUS_MT_URL = "mapi-pay-promo-web.bonus.mt.url";
    // 返礼点评跳转链接
    public static final String BONUS_DP_URL = "mapi-pay-promo-web.bonus.dp.url";



    // 新样式券白名单
    public static final String DEAL_PROXY_WHITE_LIST = "mapi-pay-promo-web.deal.white.list";
    // 新样式券类目
    public static final String DEAL_PROXY_CATEGORY = "mapi-pay-promo-web.deal.promo.proxy.category";

    // 外链允许发券的渠道
    public static final String EXTERNAL_LINK_ARROW_ISSUE_CHANNEL = "mapi-pay-promo-web.external.link.arrow.issue.channel";

    public static final String LOG_SWITCH = "mapi-pay-promo-web.log.switch";

    public static final String SHOPCART_BELONG_FILTER_SWITCH = "mapi-pay-promo-web.shopcart.belong.filter.switch";

    // 使用新立减样式的门店品类list (格式为：用户类型_前台类目), 配置元素0表示全部品类满足
    public static final String NEW_PROMO_SHOP_CATEGORY_LIST = "mapi-pay-promo-web.deal.new.promo.shopCategory.list";

    // 使用新立减样式，支持按productId进行灰度，上线后这块会放开，改用NEW_PROMO_SHOP_CATEGORY_LIST 控制灰度
    public static final String NEW_PROMO_PRODUCTID_PATTERN = "mapi-pay-promo-web.deal.new.promo.productId.pattern";

    // 使用新立减样式，支持按productId进行灰度，上线后这块会放开，改用NEW_PROMO_SHOP_CATEGORY_LIST 控制灰度
    public static final String NEW_REDIS_CLUSTER_SWITCH = "mapi-pay-promo-web.new.redis.cluster.switch";

    public static final String BRNAD_COUPON_ISSUE_NEED_CREDIT = "mapi-pay-promo-web.brand.coupon.issue.need.credit";

    // 团详价格力白名单ID
    public static final String DEAL_MEMBER_WHITE_LIST = "mapi-pay-promo-web.deal.member.white.list";
    // 团详价格力白名单类目
    public static final String DEAL_MEMBER_CATEGORY = "mapi-pay-promo-web.deal.member.proxy.category";

    public static final String PROXY_PROMOTION_TIMEOUT = "mapi-pay-promo-web.proxy.promotion.timeout";

    public static final String SHOPPING_CART_DISCOUNT_SCRIPT_DESC = "mapi-pay-promo-web.shoppingCartDiscount.script";

    public static final String SHOPPING_CART_DISCOUNT_USE_RULE_TIP= "mapi-pay-promo-web.shoppingCartDiscount.useRuleTip";

    public static final String ISSUE_COUPON_NEW_TYPE_SWITCH = "mapi-pay-promo-web.issue.coupon.newType.switch";

    public static final String SHOPPING_CART_ADDITIONAL_RULE = "mapi-pay-promo-web.shoppingCartDiscount.additional.rule";

    /**
     * 领券组件投放券选单传参开关
     */
    public static final String RESOURCE_SIEVE_SWITCH = "mapi-pay-promo-web.resourcePromo.sieve.switch";

    public static final String ACTIVITY_FILTER_SWITCH = "mapi-pay-promo-web.activity.filter.switch";

    public static final String RESOURCE_SUB_TITLE_IMG_SWITCH = "mapi-pay-promo-web.resourcePromo.subTitleImg.switch";

    /**
     * 优惠过滤规则配置map
     */
    public static final String ACTIVITY_FILTER_PATTERN_MAP = "mapi-pay-promo-web.activity.filter.pattern.map";

    /**
     * 医美会员角标url
     */
    public static final String MEDICAL_BEAUTY_CORNER_MARK_URL = "mapi-pay-promo-web.medical.beauty.corner.mark.url";

    /**
     * 微信场景是否不展示取使用链接
     */
    public static final String NOAPP_NOT_SHOW_CLICK_URL_SWITCH = "mapi-pay-promo-web.noApp.notShow.clickUrl.switch";

    /**
     * 跳过价格校验switch，为false则是不进行价格校验
     */
    public static final String SKU_SKIP_PRICE_VALIDATE_SWITCH = "mapi-pay-promo-web.sku.skipPriceValidate.switch";

    /**
     * 商品新客角标url
     */
    public static final String PRODUCT_NEW_USER_CORNER_MARK_URL = "mapi-pay-promo-web.product.new.user.corner.mark.url";

    /**
     * 新立减场景id灰度
     */
    public static final String NEW_PROXY_SCENEID_GRAYUSERID_PATTERN = "mapi-pay-promo-web.proxyscendId.grayUserId.pattern";

    /**
     * 团单佣金接口迁移开关
     */
    public static final String DEAL_COMMISSION_MOVE_SWITCH = "mapi-pay-promo-web.deal.commission.move.switch";

    /**
     * sku佣金接口迁移开关
     */
    public static final String SKU_COMMISSION_MOVE_SWITCH = "mapi-pay-promo-web.sku.commission.move.switch";

    /**
     * 金融接口的秘钥
     */
    public static final String FINANCIAL_INVOKE_SECRET = "mapi-pay-promo-web.financial.invoke.secret";

    /**
     * 金融接口的商户识别码
     * 线上：11000009414355
     * 线下：1020700205
     */
    public static final String FINANCIAL_INVOKE_SELLERID = "mapi-pay-promo-web.financial.invoke.sellerId";

    /**
     * 随机膨胀券开始倒计时的天数 （普通券为24h，即1PD）
     */
    public static final String RANDOM_COUPON_COUNT_DOWN_TIME_GAP = "mapi-pay-promo-web.random.coupon.countDown.timeGap";

    /**
     * 券倒计时天数过大告警阈值 （过大会使c端展示有一定歧义）
     */
    public static final String COUPON_COUNT_DOWN_TIME_GAP_TOO_BIG_ALARM = "mapi-pay-promo-web.countDown.timeGap.tooBig.alarm";

    public static final String DZ_TUANGOU_SWITCH = "mapi-pay-promo-web.dz.tuangou.switch";

    public static final String NEW_PRODUCT_PROMOTION_ACTION_DEGRADE_SWITCH= "mapi-pay-promo-web.new.product.promotion.display.action.degrade.switch";

    public static final String NEW_PRODUCT_PROMOTION_ACTION_LOG_SWITCH= "mapi-pay-promo-web.new.product.promotion.display.action.log.switch";

    /**
     * 点评侧高价风险降级配置(命中会过滤商家立减和商家抵用券)
     * key：business
     * value：降级城市名单List
     * */
    public static final String HIGH_PRICE_DEGRADE_BUSINESS_DP_CITY_LIST = "mapi-pay-promo-web.degrade.business.dp.city";

    /**
     * 美团侧高价风险降级配置(命中会过滤商家立减和商家抵用券)
     * key：business
     * value：降级城市名单List
     * */
    public static final String HIGH_PRICE_DEGRADE_BUSINESS_MT_CITY_LIST = "mapi-pay-promo-web.degrade.business.mt.city";

    /**
     * 渠道商品详情页 商家神券对应背景图
     */
    public static final String CHANNEL_PRODUCT_SHOP_COUPON_PIC_URL = "mapi-pay-promo-web.channel.product.shopCoupon.picUrl";

    /**
     * 渠道商品详情页 商家神券对应标题
     */
    public static final String CHANNEL_PRODUCT_SHOP_COUPON_TITLE = "mapi-pay-promo-web.channel.product.shopCoupon.title";

    /**
     * 渠道商品详情页 平台补贴对应背景图
     */
    public static final String CHANNEL_PRODUCT_PLAT_COUPON_PIC_URL = "mapi-pay-promo-web.channel.product.platCoupon.picUrl";

    /**
     * 渠道商品详情页 平台补贴对应标题
     */
    public static final String CHANNEL_PRODUCT_PLAT_COUPON_TITLE = "mapi-pay-promo-web.channel.product.platCoupon.title";

    /**
     * 渠道商品详情页 到期时间为空时默认填充文案
     */
    public static final String CHANNEL_PRODUCT_END_TIME_DEFAULT_DESC = "mapi-pay-promo-web.channel.product.endTime.desc";

    /**
     * 渠道商品详情页 优惠展示降级开关
     */
    public static final String CHANNEL_PRODUCT_PROMO_DISPLAY_SWITCH = "mapi-pay-promo-web.channel.product.promo.display.switch";

    /**
     * 渠道商品详情页 日志打印开关
     */
    public static final String CHANNEL_PRODUCT_LOG_PRINT_SWITCH = "mapi-pay-promo-web.channel.product.log.print.switch";

    public static final String JUHUASUAN_FALLBACK_SWITCH = "mapi-pay-promo-web.jhs.fallBack.switch";

    public static final String PROXY_VERSION_PARAM_SWITCH = "mapi-pay-promo-web.proxy.version.param.switch";

    /**
     * 角标映射map
     */
    public static final String CORNER_MARK_URL = "mapi-pay-promo-web.corner.mark.url";

    /**
     * 金融政府消费券降级开关
     */
    public static final String FINANCIAL_GOV_CONSUME_PROMO_DEGRADE_SWITCH = "mapi-pay-promo-web.financial.gov.consume.promo.degrade.switch";

    /**
     * 资源位配置
     */
    public static final String SHOP_RESOURCE_LOCATION_CONFIG = "mapi-pay-promo-web.shop.resource.location.config";

    /**
     * 投放神券角标url
     */
    public static final String RESOURCE_CORNER_MARK_URL = "mapi-pay-promo-web.resource.corner.mark.url";

    /**
     * 医美神券角标url
     */
    public static final String MEDICAL_GOD_COUPON_CORNER_MARK_URL = "mapi-pay-promo-web.medical.godcoupon.corner.mark.url";

    /**
     * 商家券发多张券相关配置
     * */
    public static final String ISSUE_MULTI_DP_USERID_BLACK_LIST = "mapi-pay-promo-web.issue.multi.coupon.dp.userId.blackList";
    public static final String ISSUE_MULTI_MT_USERID_BLACK_LIST = "mapi-pay-promo-web.issue.multi.coupon.mt.userId.blackList";
    /**
     * 已领取商家可用券的最大数量限制
     * */
    public static final String SHOP_COUPON_AVAILABLE_COUNT = "mapi-pay-promo-web.shop.coupon.available.count";

    public static final String SHOP_RESOURCE_PARAM_CHANGE_SWITCH = "mapi-pay-promo-web.shop.resource.param.change.switch";
    public static final String TOP_AD_RESOURCE_BOOTH_ID_CONFIG_BY_BIZ = "mapi-pay-promo-web.top.ad.resource.boothId.config.by.biz";


    public static final String CAMPAIGN_RENDER_ALLOW_UNLOGIN = "mapi-pay-promo-web.campaign.render.allow.unlogin";

    /**
     * 膨胀查询是否允许未登录
     */
    public static final String CAMPAIGN_MMC_ALLOW_UN_LOGIN = "mapi-pay-promo-web.campaign.mmc.allow.unLogin";

    public static final String TRUST_WEB_USER_SWITCH = "mapi-pay-promo-web.trust.web.user.switch";


    public static final String TRUST_WEB_USER_TEST_SWITCH = "mapi-pay-promo-web.trust.web.user.ptest.switch";

    /**
     * 有效付费神券的组件icon
     */
    public static final String PAID_MMC_VALID_ICON = "mapi-pay-promo-web.paid.mmc.valid.icon";

    /**
     * 有效免费神券的组件icon
     */
    public static final String FREE_MMC_VALID_ICON = "mapi-pay-promo-web.free.mmc.valid.icon";

    /**
     * 神会员券降级开关
     */
    public static final String MMC_COUPON_DEGRADE_SWITCH = "mapi-pay-promo-web.mmc.coupon.degrade.switch";

    /**
     * 点评神会员券降级开关
     */
    public static final String DP_MMC_COUPON_DEGRADE_SWITCH = "mapi-pay-promo-web.dp.mmc.coupon.degrade.switch";

    /**
     * 小程序神会员券降级开关
     */
    public static final String MINIPROGRAM_MMC_COUPON_DEGRADE_SWITCH = "mapi-pay-promo-web.miniprogram.mmc.coupon.degrade.switch";

    /**
     *  商家权益券是否指定路由链路开关，默认是指定链路
     */
    public static final String MERCHANT_ACCOUNT_COUPON_SET_CELL_SWITCH = "mapi-pay-promo-web.merchant.account.coupon.set.cell.switch";

    /**
     * 跨域请求头配置
     */
    public static final String MERCHANT_CROSS_DOMAIN_ACCOUNT_HEADERS_CONFIG = "mapi-pay-promo-web.merchant.crossDomain.allowHeaders.config";

    /**
     * 神券会场活动根据白名单阻塞开关
     */
    public static final String MMC_CAMPAIGN_WHITE_LIST_BLOCK_SWITCH = "mapi-pay-promo-web.mmcCampaign.whiteList.block.switch";

    /**
     * 已经下线的productCode类型
     * */
    public static final String PROMO_DESK_INVALID_PRODUCT_CODE_LIST = "mapi-pay-promo-web.invalid.productCodeList";

    /**
     * 新poi页面查券打点开关
     */
    public static final String NEW_POI_PAGE_DOT_SWITCH = "mapi-pay-promo-web.newPoiPage.dot.switch";

    /**
     * 小程序查券请求打点开关
     */
    public static final String MINI_PROGRAMS_SOURCE_DOT_SWITCH = "mapi-pay-promo-web.miniPrograms.source.dot.switch";

    /**
     * poiId加密启用开关
     */
    public static final String POI_ID_CRYPTO_SWITCH = "mapi-pay-promo-web.poiId.crypto.switch";
    /**
     * getTgcCouponPromotionFuture超时时间
     */
    public static final String TGC_COUPON_PROMOTION_TIMEOUT = "mapi-pay-promo-web.proxy.tgcCouponPromotion.timeout";

    /**
     * productBaseDTOFuture超时时间
     */
    public static final String PRODUCT_BASE_TIMEOUT = "mapi-pay-promo-web.proxy.productBase.timeout";

    /**
     * getMagicalMemberTagDORawFuture超时时间
     */
    public static final String MAGICAL_MEMBER_PROMOTION_TIMEOUT = "mapi-pay-promo-web.magical.member.promotion.timeout";

    /**
     * 预付领券购及彩虹投放活动降级开关
     */
    public static final String PREPAY_LQG_RAINBOW_DEGRADE_SWITCH = "mapi-pay-promo-web.prepay.lqg.rainbow.degrade.switch";

    /**
     * 预付poi页领券购couponProductType值切换开关
     */
    public static final String PREPAY_POI_COUPON_PRODUCT_TYPE_SWITCH = "mapi-pay-promo-web.prepay.poi.coupon.product.type.switch";

    /**
     * 预付神券banner推送开关
     */
    public static final String PREPAY_GOD_COUPON_BANNER_PUSH_SWITCH = "mapi-pay-promo-web.prepay.god.coupon.banner.push.switch";

    /**
     * poi领券栏券聚合灰度用户
     */
    public static final String POI_COMPONENT_COUPON_AGGR_GRAY_USER = "mapi-pay-promo-web.poi.component.coupon.aggr.gray.user";

    /**
     * poi领券栏券聚合灰度用户百分比
     */
    public static final String POI_COMPONENT_COUPON_AGGR_GRAY_USER_RATE = "mapi-pay-promo-web.poi.component.coupon.aggr.gray.user.rate";

    /**
     * poi领券栏券改名开关
     */
    public static final String POI_RENAME_MMC_ICON_SWITCH = "mapi-pay-promo-web.poi.rename.mmc.icon.switch";

    /**
     * poi领券栏免费神券改名开关
     */
    public static final String POI_RENAME_FREE_MMC_ICON_SWITCH = "mapi-pay-promo-web.poi.rename.free.mmc.icon.switch";

    /**
     * 付费神券新icon图片url(可用)
     */
    public static final String BUY_MMC_NEW_ICON_PIC_URL = "mapi-pay-promo-web.buy.mmc.new.icon.pic.url";

    /**
     * 付费神券新icon图片高度(可用)
     */
    public static final String BUY_MMC_NEW_ICON_PIC_HEIGHT = "mapi-pay-promo-web.buy.mmc.new.icon.pic.height";

    /**
     * 付费神券新icon图片宽度(可用)
     */
    public static final String BUY_MMC_NEW_ICON_PIC_WIDTH = "mapi-pay-promo-web.buy.mmc.new.icon.pic.width";

    /**
     * 免费神券新icon图片url
     */
    public static final String FREE_MMC_NEW_ICON_PIC_URL = "mapi-pay-promo-web.free.mmc.new.icon.pic.url";

    /**
     * 免费神券新icon图片高度
     */
    public static final String FREE_MMC_NEW_ICON_PIC_HEIGHT = "mapi-pay-promo-web.free.mmc.new.icon.pic.height";

    /**
     * 免费神券新icon图片宽度
     */
    public static final String FREE_MMC_NEW_ICON_PIC_WIDTH = "mapi-pay-promo-web.free.mmc.new.icon.pic.width";

    /**
     * productcouponpromo.bin 未登录直接返空开关  true- 未登录返回空 (反爬虫）
     */
    public static final String  PRODUCT_COUPON_PROMO_BIN_ANTI_CRAWLER_SWITCH = "mapi-pay-promo-web.productcouponpromo.bin.anti.crawler.switch";

    public static final String AB_TEST_CONFIG = "mapi-pay-promo-web.ab.test.config";

}
