package com.dianping.pay.api.util;

import com.dianping.gmkt.coupon.common.api.enums.CouponBusiness;
import com.dianping.gmkt.coupon.common.api.enums.CouponGroupValueType;
import com.dianping.gmkt.coupon.common.api.utils.TgCouponBusinessUtils;
import com.dianping.pay.api.beans.IssueCouponActivity;
import com.dianping.unified.coupon.manage.api.dto.DiscountCouponDTO;
import com.dianping.unified.coupon.manage.api.dto.UnifiedCouponGroupDTO;

import java.math.BigDecimal;
import java.util.Comparator;

public class IssueCouponActivityComparator implements Comparator<IssueCouponActivity> {

    @Override
    public int compare(IssueCouponActivity obj1, IssueCouponActivity obj2) {
        if (!IssueCouponTypeUtils.allowGetCoupon(obj1) && IssueCouponTypeUtils.allowGetCoupon(obj2)) {
            return 1;
        }
        if (!IssueCouponTypeUtils.allowGetCoupon(obj2) && IssueCouponTypeUtils.allowGetCoupon(obj1)) {
            return -1;
        }
        UnifiedCouponGroupDTO dto1 = obj1.getCouponGroup(), dto2 = obj2.getCouponGroup();
        //门槛
        BigDecimal priceLimit1 = dto1.getPriceLimit() == null ? BigDecimal.ZERO : dto1.getPriceLimit();
        BigDecimal priceLimit2 = dto2.getPriceLimit() == null ? BigDecimal.ZERO : dto2.getPriceLimit();

        if (priceLimit1.compareTo(priceLimit2) < 0) {
            return -1;
        } else if (priceLimit1.compareTo(priceLimit2) > 0) {
            return 1;
        }

        // 折扣券一样，按最高折扣金额进行比较，理论目前折扣券不会存在discountAmount为空的情况
        if (dto1.getDiscountAmount() != null && dto2.getDiscountAmount() != null) {
            if(dto1.getDiscountAmount().compareTo(dto2.getDiscountAmount()) != 0){
                return dto2.getDiscountAmount().compareTo(dto1.getDiscountAmount());
            }
        }

        if (dto1.getProductCodeList().size() < dto2.getProductCodeList().size() ) {
            return 1;
        }else if(dto1.getProductCodeList().size() > dto2.getProductCodeList().size()){
            return -1 ;
        }

        //团购>预订>闪惠>课程
        int productCode1 = dto1.getProductCodeList().get(0);
        int productCode2 = dto2.getProductCodeList().get(0);

        if (TgCouponBusinessUtils.isDzTuangouCouponBusiness(productCode1)
                && !TgCouponBusinessUtils.isDzTuangouCouponBusiness(productCode2)) {
            return -1;
        }
        if (TgCouponBusinessUtils.isDzTuangouCouponBusiness(productCode2)
                && !TgCouponBusinessUtils.isDzTuangouCouponBusiness(productCode1)) {
            return 1;
        }
        if ((productCode1 == CouponBusiness.KTV.getCode() || productCode1 == CouponBusiness.JOY.getCode())
                && (productCode2 != CouponBusiness.KTV.getCode() && productCode2 != CouponBusiness.JOY.getCode())) {
            return -1;
        }
        if ((productCode2 == CouponBusiness.KTV.getCode() || productCode2 == CouponBusiness.JOY.getCode())
                && (productCode1 != CouponBusiness.KTV.getCode() && productCode1 != CouponBusiness.JOY.getCode())) {
            return 1;
        }
        if (productCode1 == CouponBusiness.MO2O2PAY.getCode()
                && productCode2 != CouponBusiness.MO2O2PAY.getCode()) {
            return -1;
        }
        if (productCode2 == CouponBusiness.MO2O2PAY.getCode()
                && productCode1 != CouponBusiness.MO2O2PAY.getCode()) {
            return 1;
        }

        if (productCode1 == CouponBusiness.EDU.getCode()
                && productCode2 != CouponBusiness.EDU.getCode()) {
            return -1;
        }
        if (productCode2 == CouponBusiness.EDU.getCode()
                && productCode1 != CouponBusiness.EDU.getCode()) {
            return 1;
        }

        return 0;
    }
}
