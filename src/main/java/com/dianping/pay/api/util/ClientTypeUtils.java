package com.dianping.pay.api.util;

import com.dianping.gmkt.coupon.common.api.enums.MtCouponPlatform;
import com.dianping.gmkt.scene.api.delivery.enums.RiskClientTypeEnum;
import com.dianping.mobile.base.datatypes.enums.ClientType;
import com.dianping.mobile.base.datatypes.enums.Platform;
import com.dianping.mobile.framework.datatypes.IMobileContext;
import com.dianping.pay.api.biz.activity.newloader.dto.CouponActivityContext;
import com.dianping.pay.api.entity.issuecoupon.IssueCouponRequest;
import com.dianping.pay.api.enums.CouponClientTypeEnum;
import com.dianping.pay.api.enums.CouponPlatformTypeEnum;
import com.dianping.pay.common.enums.PayPlatform;
import com.google.common.collect.Maps;
import com.sankuai.nib.mkt.common.base.enums.propertyValueType.ClientTypeEnum;
import org.apache.commons.lang3.StringUtils;

import java.util.HashMap;
import java.util.Map;

public class ClientTypeUtils {

    private static final Map<Integer, ClientTypeEnum> CLIENT_TYPE_MAP = Maps.newHashMap();


    private static Map<Integer, String> DPClientTypePlatformMap = new HashMap<Integer, String>() {
        {
            put(PayPlatform.dp_iphone_native.getCode(), ClientTypeEnum.DP_IPHONE.getValue());
            put(PayPlatform.dp_android_native.getCode(), ClientTypeEnum.DP_ANDROID.getValue());
            put(PayPlatform.tg_wap_m.getCode(), ClientTypeEnum.DP_I_VERSION.getValue());
            put(PayPlatform.tg_pc.getCode(), ClientTypeEnum.DP_PC.getValue());
            put(PayPlatform.weixin_api.getCode(), ClientTypeEnum.WE_CHAT_APPLET.getValue());
            put(PayPlatform.weixin_bank.getCode(), ClientTypeEnum.WE_CHAT_BANK.getValue());
        }
    };

    private static Map<Integer, String> MtClientTypePlatformMap = new HashMap<Integer, String>() {
        {
            put(MtCouponPlatform.M_MOBILE.getCode(), ClientTypeEnum.M_STATION.getValue());
            put(MtCouponPlatform.I_TOUCH.getCode(), ClientTypeEnum.I_VERSION.getValue());
            put(MtCouponPlatform.APP.getCode(), ClientTypeEnum.ANDROID.getValue());
            put(MtCouponPlatform.WEIXIN_CX.getCode(), ClientTypeEnum.WE_CHAT_APPLET.getValue());
            put(MtCouponPlatform.LITE.getCode(), ClientTypeEnum.SPEED.getValue());
            put(MtCouponPlatform.WEB_PAGE.getCode(), ClientTypeEnum.PC.getValue());
        }
    };


    private static Map<Integer, Integer> DPDealClientTypePlatformMap = new HashMap<Integer, Integer>() {
        {
            put(PayPlatform.dp_iphone_native.getCode(), com.dianping.deal.common.enums.ClientTypeEnum.dp_mainApp_ios.getType());
            put(PayPlatform.dp_android_native.getCode(), com.dianping.deal.common.enums.ClientTypeEnum.dp_mainApp_android.getType());
            put(PayPlatform.tg_wap_m.getCode(), com.dianping.deal.common.enums.ClientTypeEnum.dp_mainApp_android.getType());
            put(PayPlatform.tg_pc.getCode(), com.dianping.deal.common.enums.ClientTypeEnum.dp_web.getType());
            put(PayPlatform.weixin_api.getCode(), com.dianping.deal.common.enums.ClientTypeEnum.dp_weApp.getType());
            put(PayPlatform.weixin_bank.getCode(), com.dianping.deal.common.enums.ClientTypeEnum.dp_wxWap.getType());
        }
    };

    private static Map<Integer, Integer> MtDealClientTypePlatformMap = new HashMap<Integer, Integer>() {
        {
            put(MtCouponPlatform.I_TOUCH.getCode(), com.dianping.deal.common.enums.ClientTypeEnum.mt_wap.getType());
            put(MtCouponPlatform.APP.getCode(), com.dianping.deal.common.enums.ClientTypeEnum.mt_mainApp_android.getType());
            put(MtCouponPlatform.WEIXIN_CX.getCode(), com.dianping.deal.common.enums.ClientTypeEnum.mt_weApp.getType());
            put(MtCouponPlatform.WEB_PAGE.getCode(), com.dianping.deal.common.enums.ClientTypeEnum.mt_web.getType());
        }
    };

    public static int getDealClientType(int payPlatform, boolean isMt) {
        Integer clientType = null;
        if (isMt) {
            clientType = MtDealClientTypePlatformMap.get(payPlatform);
        } else {
            clientType = DPDealClientTypePlatformMap.get(payPlatform);
        }
        if (clientType == null) {
            if (isMt) {
                clientType = com.dianping.deal.common.enums.ClientTypeEnum.mt_mainApp_android.getType();
            } else {
                clientType = com.dianping.deal.common.enums.ClientTypeEnum.dp_mainApp_android.getType();
            }
        }
        return clientType;
    }

    public static String getClientTypeByPayPlatform(int payPlatform, boolean isMt) {
        String clientPlatform = null;
        if (isMt) {
            clientPlatform = MtClientTypePlatformMap.get(payPlatform);
        } else {
            clientPlatform = DPClientTypePlatformMap.get(payPlatform);
        }
        if (StringUtils.isBlank(clientPlatform)) {
            if (isMt) {
                clientPlatform = ClientTypeEnum.ANDROID.getValue();
            } else {
                clientPlatform = ClientTypeEnum.DP_ANDROID.getValue();
            }
        }
        return clientPlatform;
    }

    public static int getCouponPlatform(int clientType, int platform, boolean isMt) {
        if (isMt) {
            if (clientType == CouponClientTypeEnum.NATIVE.getCode()) {
                return MtCouponPlatform.APP.getCode();
            } else if (clientType == CouponClientTypeEnum.WEB.getCode() || clientType == CouponClientTypeEnum.M.getCode()) {
                return MtCouponPlatform.I_TOUCH.getCode();
            } else if (clientType == CouponClientTypeEnum.MINIPROGRAM.getCode()) {
                return MtCouponPlatform.WEIXIN_CX.getCode();
            } else if (clientType == CouponClientTypeEnum.WEIXIN.getCode()) {
                return MtCouponPlatform.I_TOUCH.getCode();
            }
            return MtCouponPlatform.APP.getCode();
        } else {
            if (clientType == CouponClientTypeEnum.NATIVE.getCode()) {
                if (platform == CouponPlatformTypeEnum.ANDROID.getCode()) {
                    return PayPlatform.dp_android_native.getCode();
                } else {
                    return PayPlatform.dp_iphone_native.getCode();
                }
            } else if (clientType == CouponClientTypeEnum.WEB.getCode() || clientType == CouponClientTypeEnum.M.getCode()) {
                return PayPlatform.tg_wap_m.getCode();
            } else if (clientType == CouponClientTypeEnum.MINIPROGRAM.getCode()) {
                return PayPlatform.weixin_api.getCode();
            } else if (clientType == CouponClientTypeEnum.WEIXIN.getCode()) {
                return PayPlatform.weixin_bank.getCode();
            }
            return PayPlatform.dp_android_native.getCode();
        }
    }


    public static int getPayPlatform(int clientType, int platform, boolean isMt) {
        if (isMt) {
            if (clientType == CouponClientTypeEnum.NATIVE.getCode()) {
                if (platform == CouponPlatformTypeEnum.ANDROID.getCode()) {
                    return PayPlatform.mt_android_native.getCode();
                } else {
                    return PayPlatform.mt_iphone_native.getCode();
                }
            } else if (clientType == CouponClientTypeEnum.WEB.getCode() || clientType == CouponClientTypeEnum.M.getCode()) {
                return PayPlatform.mt_wap_m.getCode();
            } else if (clientType == CouponClientTypeEnum.MINIPROGRAM.getCode()) {
                return PayPlatform.mt_weixin_api.getCode();
            }
            return PayPlatform.mt_android_native.getCode();
        } else {
            if (clientType == CouponClientTypeEnum.NATIVE.getCode()) {
                if (platform == CouponPlatformTypeEnum.ANDROID.getCode()) {
                    return PayPlatform.dp_android_native.getCode();
                } else {
                    return PayPlatform.dp_iphone_native.getCode();
                }
            } else if (clientType == CouponClientTypeEnum.WEB.getCode() || clientType == CouponClientTypeEnum.M.getCode()) {
                return PayPlatform.tg_wap_m.getCode();
            } else if (clientType == CouponClientTypeEnum.MINIPROGRAM.getCode()) {
                return PayPlatform.weixin_api.getCode();
            } else if (clientType == CouponClientTypeEnum.WEIXIN.getCode()) {
                return PayPlatform.weixin_bank.getCode();
            }
            return PayPlatform.dp_android_native.getCode();
        }
    }


    public static boolean isMainWeb(int payPlatform, boolean isMt) {
        if (isMt) {
            return payPlatform == MtCouponPlatform.WEB_PAGE.getCode();
        } else {
            return payPlatform == PayPlatform.tg_pc.getCode();

        }
    }

    public static boolean isMainWX(int payPlatform, boolean isMt) {
        if (isMt) {
            return payPlatform == MtCouponPlatform.WEIXIN_CX.getCode();
        } else {
            return payPlatform == PayPlatform.weixin_api.getCode();

        }
    }

    public static Integer getRiskClientType(IMobileContext context) {
        if (context.getClient() == ClientType.MAINAPP_IPHONE) {
            return RiskClientTypeEnum.DP_IPHONE.getCode();
        } else if (context.getClient() == ClientType.MAINAPP_ANDROID) {
            return RiskClientTypeEnum.DP_ANDROID.getCode();
        } else if (context.isMtClient() && context.getClient().getPlatform() == Platform.iPhone) {
            return RiskClientTypeEnum.MT_IPHONE.getCode();
        } else if (context.isMtClient() && context.getClient().getPlatform() == Platform.Android) {
            return RiskClientTypeEnum.MT_ANDROID.getCode();
        }
        return RiskClientTypeEnum.UNKNOWN.getCode();
    }

}
