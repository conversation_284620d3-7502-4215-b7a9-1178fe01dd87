package com.dianping.pay.api.util;

import com.dianping.api.constans.CommonConstants;
import com.dianping.api.constans.LionConstants;
import com.dianping.api.util.LionQueryUtils;
import com.dianping.cat.Cat;
import com.dianping.cat.message.Message;
import com.dianping.cat.message.Transaction;
import com.dianping.cat.util.MetricHelper;
import com.dianping.gmkt.scene.api.delivery.dto.res.CouponDetailDTO;
import com.dianping.gmkt.scene.api.delivery.enums.RecallTypeEnum;
import com.dianping.lion.client.Lion;
import com.dianping.pay.api.beans.*;
import com.dianping.pay.api.biz.activity.newloader.dto.CouponActivityContext;
import com.dianping.pay.api.biz.activity.newloader.dto.PromotionRequestContext;
import com.dianping.pay.promo.display.api.dto.PromoDisplayDTO;
import com.sankuai.mkt.activity.api.merchantsharecoupon.model.MerchantShareCouponExposeDTO;
import com.sankuai.mpmkt.coupon.search.api.dto.CouponDesc;
import org.apache.commons.collections4.CollectionUtils;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.atomic.AtomicInteger;

public class DotUtils {

    private static final String PROMO_CALL_FAIL = "promoCallError";

    private static final String PROMO_MONITOR = "promoMonitor";

    private static final String POI_ID_EXCEPTION = "poiIdException";

    private static final String POI_RAINBOW_COUPON = "poiRainbowCoupon";

    private static final String MINI_PROGRAMS_MONITOR = "miniProgramsCouponMonitor";

    private static final String POI_CONSISTENT_COUPON_MONITOR = "poiConsistentCouponMonitor";
    private static final String SHOP_COUPON = "shopCoupon";
    private static final String SHOP_SHARE_COUPON = "shopShareCoupon";
    private static final String SHOP_RESOURCE_COUPON = "shopResourceCoupon";
    private static final String MERCHANT_ACCOUNT_COUPON = "merchantAccountCoupon";
    private static final String TOP_COUPON = "topCoupon";
    private static final String MAGICAL_MEMBER_COUPON = "magicalMemberCoupon";
    public static final String REDUCTION_PROMO = "reductionPromo";
    public static final String RENAME_MMC_ICON = "renameMMCIcon";
    public static final String MMC_AGGREGATE = "mmcAggregate";

    public static void dotForPromoHit(String promoName, boolean isMt, boolean hit) {
        String clientType = isMt ? "MT" : "DP";
        String transactionName = promoName + "_" + clientType;
        Transaction transaction = Cat.newTransaction(PROMO_MONITOR, transactionName);
        if (hit) {
            transaction.setSuccessStatus();
        } else {
            transaction.setStatus("-1");
        }
        transaction.complete();
    }

    public static void dotForPromoCallFail(String promoName, String methodName, boolean isMt) {
        String clientType = isMt ? "MT" : "DP";
        Cat.logEvent(PROMO_CALL_FAIL, promoName + "_" + methodName + "_" + clientType);
        MetricHelper.build().name(PROMO_CALL_FAIL)
                .tag("promoName", promoName)
                .tag("methodName", methodName)
                .tag("clientType", clientType)
                .count();
        // 同时打一个promo 未命中的点
        dotForPromoHit(promoName, isMt, false);

    }

    public static void dotForPoiIdException(String methodName, String paramName) {
        Cat.logEvent("INVALID_METHOD_2", "com.dianping.pay.api.util.DotUtils.dotForPoiIdException(java.lang.String,java.lang.String)");
        Cat.logEvent(POI_ID_EXCEPTION, methodName + "_" + paramName);
    }

    /**
     * poi页领券组件彩虹券打点，已领券、待领券、新塞券的占比信息
     * 彩虹的领取状态只会存在全领取/全未领取，部分领取的情况会直接过滤不展示
     */
    public static void dotForPoiRainbowCoupon(ShopResourcePromotionDO shopResourcePromotionDO) {
        try {
            if (shopResourcePromotionDO ==  null) {
                return;
            }
            boolean showReceivedCoupon = false;   //已领券
            boolean showGetCoupon = false;        //待领券
            boolean showNewPushCoupon = false;    //新塞券
            Integer drawStatus = shopResourcePromotionDO.getDrawStatus();
            List<ResourceMaterialsDO> resourceMaterialsDOS = shopResourcePromotionDO.getResourceMaterialsDOS();
            if (CollectionUtils.isEmpty(resourceMaterialsDOS)) {
                return;
            }
            if (Objects.equals(CommonConstants.DRAWED, drawStatus)) {
                for (ResourceMaterialsDO resourceMaterialsDO : resourceMaterialsDOS) {
                    Integer recallType = resourceMaterialsDO.getRecallType();
                    if (recallType == null) {
                        continue;
                    } else if (recallType.equals(RecallTypeEnum.RECALL_DIRECT_DRAW_COUPON.getCode())) {
                        showNewPushCoupon = true;
                    } else if (recallType.equals(RecallTypeEnum.ALREADY_DRAW_COUPON.getCode())
                            || recallType.equals(RecallTypeEnum.RECALL_DRAW_COUPON.getCode())
                            || recallType.equals(RecallTypeEnum.RECALL_MULTI_COUPON.getCode())) {
                        showReceivedCoupon = true;
                    }
                }
            } else if (Objects.equals(CommonConstants.CAN_DRAW, drawStatus)) {
                for (ResourceMaterialsDO resourceMaterialsDO : resourceMaterialsDOS) {
                    Integer recallType = resourceMaterialsDO.getRecallType();
                    if (recallType == null) {
                        continue;
                    } else if (recallType.equals(RecallTypeEnum.RECALL_DRAW_COUPON.getCode())
                            || recallType.equals(RecallTypeEnum.RECALL_MULTI_COUPON.getCode())) {
                        showGetCoupon = true;
                    }
                }
            }
            Cat.logEvent(POI_RAINBOW_COUPON, "showReceivedCoupon", showReceivedCoupon ? "0" : "-1", JsonUtils.toJson(shopResourcePromotionDO));
            Cat.logEvent(POI_RAINBOW_COUPON, "showGetCoupon", showGetCoupon ? "0" : "-1", JsonUtils.toJson(shopResourcePromotionDO));
            Cat.logEvent(POI_RAINBOW_COUPON, "showNewPushCoupon", showNewPushCoupon ? "0" : "-1", JsonUtils.toJson(shopResourcePromotionDO));
        } catch (Exception ignore) {}
    }

    /**
     * poi页领券组件打点，记录不同券类型查出来的券数量
     */
    public static void dotForPoiConsistentIssueCoupon(CouponActivityContext couponActivityContext, PromotionRequestContext promotionRequestContext){
        try {
            if (!LionQueryUtils.DOT_POI_PAGE_SWITCH || couponActivityContext == null || promotionRequestContext == null){
                return;
            }
            // 商家券(店铺通用券、品类券，不包括商品券，用户非新客也会过滤新客券)
            List<IssueCouponActivity> issueCouponActivitieList = couponActivityContext.getIssueCouponActivities();
            //商家分享券
            List<MerchantShareCouponExposeDTO> couponExposeDTOS = couponActivityContext.getCouponExposeDTOS();
            //投放资源券
            ShopResourcePromotionDO shopResourcePromotionDO = couponActivityContext.getShopResourcePromotionDO();
            //商家权益券
            org.apache.commons.lang3.tuple.Pair<List<CouponDesc>, List<CouponDesc>> merchantAccountCouponPromotion = couponActivityContext.getMerchantAccountCouponPromotion();
            //top广告券
            TopAdResourcePromotionDO topAdResourcePromotionDO = couponActivityContext.getTopAdResourcePromotionDO();
            //神会员券包
            MagicalMemberTagRawDO magicalMemberTagDO = couponActivityContext.getMagicalMemberTagRawDO();

            int shopCouponNum = 0;
            int shopShareCouponNum = 0;
            int merchantAccountCouponNum = 0;
            int shopResourceCouponNum = 0;
            int topCouponNum = 0;
            int magicalMemberCouponNum = 0;

            // 商家券数量 = 已领取 + 未领取
            if (CollectionUtils.isNotEmpty(issueCouponActivitieList)){
                for (IssueCouponActivity activity : issueCouponActivitieList) {
                    int issueNum = CollectionUtils.size(activity.getIssuedCoupon());
                    int unUseNum = CollectionUtils.size(activity.getIssuedUnUseUnExpireCoupon());
                    shopCouponNum += (issueNum + unUseNum);
                }
            }
            if (CollectionUtils.isNotEmpty(couponExposeDTOS)){
                shopShareCouponNum = couponExposeDTOS.size();
            }
            // 投放资源券
            if (shopResourcePromotionDO != null && CollectionUtils.isNotEmpty(shopResourcePromotionDO.getResourceMaterialsDOS())){
                for (ResourceMaterialsDO materialsDO : shopResourcePromotionDO.getResourceMaterialsDOS()) {
                    if (CollectionUtils.isNotEmpty(materialsDO.getCouponDetailDTOS())){
                        shopResourceCouponNum += materialsDO.getCouponDetailDTOS().size();
                    }
                }
            }
            // 商家权益券数量 = 未发放 + 已发放
            if (merchantAccountCouponPromotion != null){
                int noIssuedNum = CollectionUtils.size(merchantAccountCouponPromotion.getLeft());
                int issuedNum = CollectionUtils.size(merchantAccountCouponPromotion.getRight());
                merchantAccountCouponNum = (noIssuedNum + issuedNum);
            }
            if (topAdResourcePromotionDO != null){
                topCouponNum = CollectionUtils.size(topAdResourcePromotionDO.getVoucherDetailDOList());
            }

            if (magicalMemberTagDO != null){
                magicalMemberCouponNum = CollectionUtils.size(magicalMemberTagDO.getMagicMemberCouponInfoList());
            }

            Integer poiPageSource = promotionRequestContext.getPoiPageSource();
            logEvent(SHOP_COUPON, shopCouponNum, poiPageSource);
            logEvent(SHOP_SHARE_COUPON, shopShareCouponNum, poiPageSource);
            logEvent(SHOP_RESOURCE_COUPON, shopResourceCouponNum, poiPageSource);
            logEvent(MERCHANT_ACCOUNT_COUPON, merchantAccountCouponNum, poiPageSource);
            logEvent(TOP_COUPON, topCouponNum, poiPageSource);
            logEvent(MAGICAL_MEMBER_COUPON, magicalMemberCouponNum, poiPageSource);
        } catch (Exception ignore) {}
    }

    private static void logEvent(String eventName, int quantity, Integer poiPageSource){
        String status = quantity > 0 ? Message.SUCCESS : "-1";
        Cat.logEvent(POI_CONSISTENT_COUPON_MONITOR + "_" + (poiPageSource != null ? poiPageSource : 0), eventName, status, null);
    }

    /**
     * 打点监控小程序发券场景
     * @param context
     * @param promoName
     */
    public static void dotForPromoProcessor(CouponActivityContext context, String promoName){
        try {
            if (!LionQueryUtils.DOT_MINI_PROGRAMS_ISSUE_SWITCH || context.getMiniProgramFlag() == null || context.getMiniProgramFlag() != 1){
                return;
            }
            Cat.logEvent(MINI_PROGRAMS_MONITOR, promoName);
        } catch (Exception ignore) {}
    }
    public static void dotForReduction(List<PromoDisplayDTO> promoDisplayDTOS) {
        Cat.logEvent(REDUCTION_PROMO, "BY_SKU", CollectionUtils.isNotEmpty(promoDisplayDTOS) ? "0" : "-1", null);
    }

    public static void dotForMMCAggrAndIconRename(PromotionRequestContext context, boolean mmcAggregated, boolean renameMMCIcon) {
        String clientType = context.isMiniProgram() ? "miniProgram" : (context.isMt() ? "MT" : "DP");
        Cat.logEvent(MMC_AGGREGATE, clientType, mmcAggregated ? Message.SUCCESS : "-1", null);
        Cat.logEvent(RENAME_MMC_ICON, clientType, renameMMCIcon ? Message.SUCCESS : "-1", null);
    }
}
