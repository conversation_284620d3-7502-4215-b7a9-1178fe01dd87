package com.dianping.pay.api.util;

import com.dianping.cat.Cat;
import com.google.common.collect.Lists;
import com.google.gson.Gson;
import org.apache.commons.lang.StringUtils;

/**
 * Created by yangquan02 on 18/11/7.
 */
public class JsonLabelUtil {

    private static final Gson JSON = new Gson();

    private static final String TRANSPARENT_COLOR = "#FFFFFF";
    private static final String TEXT_STYLE_DEFAULT = RichTextBuilder.TextStyle.DEFAULT.getStyle();
    private static final String TEXT_STYLE_BOLD = RichTextBuilder.TextStyle.BOLD.getStyle();
    private static final String TEXT_FINANCIAL_COUPON_TITLE = "";

    public static String pinPoolDPJson(String title, String highTitle) {
        Cat.logEvent("INVALID_METHOD_2", "com.dianping.pay.api.util.JsonLabelUtil.pinPoolDPJson(java.lang.String,java.lang.String)");
        if (StringUtils.isEmpty(title) || StringUtils.isEmpty(highTitle)) {
            return null;
        }
        return new RichTextBuilder(title, highTitle)
                .setDefaultTextItem(new RichTextBuilder.TextItem(13, "#111111", TRANSPARENT_COLOR, TEXT_STYLE_DEFAULT, false, false))
                .setHltTextItem(new RichTextBuilder.TextItem(13, "#FF6633", TRANSPARENT_COLOR, TEXT_STYLE_DEFAULT, false, false))
                .build().toString();
    }

    public static String pinPoolMTJson(String title, String highTitle) {
        Cat.logEvent("INVALID_METHOD_1", "com.dianping.pay.api.util.JsonLabelUtil.pinPoolMTJson(java.lang.String,java.lang.String)");
        if (StringUtils.isEmpty(title) || StringUtils.isEmpty(highTitle)) {
            return null;
        }
        return new RichTextBuilder(title, highTitle)
                .setDefaultTextItem(new RichTextBuilder.TextItem(11, "#333333", TRANSPARENT_COLOR, TEXT_STYLE_DEFAULT, false, false))
                .setHltTextItem(new RichTextBuilder.TextItem(11, "#FF4A4A", TRANSPARENT_COLOR, TEXT_STYLE_DEFAULT, false, false))
                .build().toString();
    }

    public static String promoDPJson(String title, String highTitle) {
        if (StringUtils.isEmpty(title) || StringUtils.isEmpty(highTitle)) {
            return null;
        }
        return new RichTextBuilder(title, highTitle)
                .setDefaultTextItem(new RichTextBuilder.TextItem(13, "#111111", TRANSPARENT_COLOR, TEXT_STYLE_DEFAULT, false, false))
                .setHltTextItem(new RichTextBuilder.TextItem(13, "#FF6633", TRANSPARENT_COLOR, TEXT_STYLE_DEFAULT, false, false))
                .build().toString();
    }

    public static String promoMTJson(String title, String highTitle) {
        if (StringUtils.isEmpty(title) || StringUtils.isEmpty(highTitle)) {
            return null;
        }
        return new RichTextBuilder(title, highTitle)
                .setDefaultTextItem(new RichTextBuilder.TextItem(11, "#333333", TRANSPARENT_COLOR, TEXT_STYLE_DEFAULT, false, false))
                .setHltTextItem(new RichTextBuilder.TextItem(11, "#333333", TRANSPARENT_COLOR, TEXT_STYLE_DEFAULT, false, false))
                .build().toString();
    }

    public static String bonusDPJson(String title, String highTitle) {
        if (StringUtils.isEmpty(title) || StringUtils.isEmpty(highTitle)) {
            return null;
        }
        return new RichTextBuilder(title, highTitle)
                .setDefaultTextItem(new RichTextBuilder.TextItem(13, "#111111", TRANSPARENT_COLOR, TEXT_STYLE_DEFAULT, false, false))
                .setHltTextItem(new RichTextBuilder.TextItem(13, "#FF6633", TRANSPARENT_COLOR, TEXT_STYLE_DEFAULT, false, false))
                .build().toString();
    }

    public static String bonusMTJson(String title, String highTitle) {
        if (StringUtils.isEmpty(title) || StringUtils.isEmpty(highTitle)) {
            return null;
        }
        return new RichTextBuilder(title, highTitle)
                .setDefaultTextItem(new RichTextBuilder.TextItem(11, "#333333", TRANSPARENT_COLOR, TEXT_STYLE_DEFAULT, false, false))
                .setHltTextItem(new RichTextBuilder.TextItem(11, "#333333", TRANSPARENT_COLOR, TEXT_STYLE_DEFAULT, false, false))
                .build().toString();
    }

    public static String couponAmountDPJson(String title, String highTitle) {
        Cat.logEvent("INVALID_METHOD_5", "com.dianping.pay.api.util.JsonLabelUtil.couponAmountDPJson(java.lang.String,java.lang.String)");
        if (StringUtils.isEmpty(title) || StringUtils.isEmpty(highTitle)) {
            return null;
        }
        return new RichTextBuilder(title, highTitle)
                .setDefaultTextItem(new RichTextBuilder.TextItem(16, "#FF6633", TRANSPARENT_COLOR, TEXT_STYLE_BOLD, false, false))
                .setHltTextItem(new RichTextBuilder.TextItem(36, "#FF6633", TRANSPARENT_COLOR, TEXT_STYLE_BOLD, false, false))
                .build().toString();
    }

    public static String couponAmountMTJson(String title, String highTitle) {
        if (StringUtils.isEmpty(title) || StringUtils.isEmpty(highTitle)) {
            return null;
        }
        return new RichTextBuilder(title, highTitle)
                .setDefaultTextItem(new RichTextBuilder.TextItem(14, "#FF6200", TRANSPARENT_COLOR, TEXT_STYLE_BOLD, false, false))
                .setHltTextItem(new RichTextBuilder.TextItem(28, "#FF6200", TRANSPARENT_COLOR, TEXT_STYLE_BOLD, false, false))
                .build().toString();
    }

    public static String couponAmountRulesDPJson(String title, String highTitle) {
        if (StringUtils.isEmpty(title) || StringUtils.isEmpty(highTitle)) {
            return null;
        }
        return new RichTextBuilder(title, highTitle)
                .setDefaultTextItem(new RichTextBuilder.TextItem(12, "#777777", TRANSPARENT_COLOR, TEXT_STYLE_DEFAULT, false, false))
                .setHltTextItem(new RichTextBuilder.TextItem(12, "#777777", TRANSPARENT_COLOR, TEXT_STYLE_DEFAULT, false, false))
                .build().toString();
    }

    public static String couponAmountRulesMTJson(String title, String highTitle) {
        if (StringUtils.isEmpty(title) || StringUtils.isEmpty(highTitle)) {
            return null;
        }
        return new RichTextBuilder(title, highTitle)
                .setDefaultTextItem(new RichTextBuilder.TextItem(12, "#FF6600", TRANSPARENT_COLOR, TEXT_STYLE_DEFAULT, false, false))
                .setHltTextItem(new RichTextBuilder.TextItem(12, "#FF6600", TRANSPARENT_COLOR, TEXT_STYLE_DEFAULT, false, false))
                .build().toString();
    }

    public static String couponTitleDPJson(String title, String highTitle) {
        if (StringUtils.isEmpty(title) || StringUtils.isEmpty(highTitle)) {
            return null;
        }
        return new RichTextBuilder(title, highTitle)
                .setDefaultTextItem(new RichTextBuilder.TextItem(14, "#111111", TRANSPARENT_COLOR, TEXT_STYLE_DEFAULT, false, false))
                .setHltTextItem(new RichTextBuilder.TextItem(14, "#111111", TRANSPARENT_COLOR, TEXT_STYLE_DEFAULT, false, false))
                .build().toString();
    }

    public static String couponTitleMTJson(String title, String highTitle) {
        if (StringUtils.isEmpty(title) || StringUtils.isEmpty(highTitle)) {
            return null;
        }
        return new RichTextBuilder(title, highTitle)
                .setDefaultTextItem(new RichTextBuilder.TextItem(14, "#333333", TRANSPARENT_COLOR, TEXT_STYLE_DEFAULT, false, false))
                .setHltTextItem(new RichTextBuilder.TextItem(14, "#333333", TRANSPARENT_COLOR, TEXT_STYLE_DEFAULT, false, false))
                .build().toString();
    }

    public static String couponAmountDescDPJson(String title, String highTitle) {
        if (StringUtils.isEmpty(title) || StringUtils.isEmpty(highTitle)) {
            return null;
        }
        return new RichTextBuilder(title, highTitle)
                .setDefaultTextItem(new RichTextBuilder.TextItem(11, "#FF6633", TRANSPARENT_COLOR, TEXT_STYLE_DEFAULT, false, false))
                .setHltTextItem(new RichTextBuilder.TextItem(11, "#FF6633", TRANSPARENT_COLOR, TEXT_STYLE_DEFAULT, false, false))
                .build().toString();
    }

    public static String couponAmountDescMTJson(String title, String highTitle) {
        if (StringUtils.isEmpty(title) || StringUtils.isEmpty(highTitle)) {
            return null;
        }
        return new RichTextBuilder(title, highTitle)
                .setDefaultTextItem(new RichTextBuilder.TextItem(12, "#FF6600", TRANSPARENT_COLOR, TEXT_STYLE_DEFAULT, false, false))
                .setHltTextItem(new RichTextBuilder.TextItem(12, "#FF6600", TRANSPARENT_COLOR, TEXT_STYLE_DEFAULT, false, false))
                .build().toString();
    }

    public static String bonusDescDPJson(String title, String highTitle) {
        if (StringUtils.isEmpty(title) || StringUtils.isEmpty(highTitle)) {
            return null;
        }
        return new RichTextBuilder(title, highTitle)
                .setDefaultTextItem(new RichTextBuilder.TextItem(12, "#000000", TRANSPARENT_COLOR, TEXT_STYLE_DEFAULT, false, false))
                .setHltTextItem(new RichTextBuilder.TextItem(12, "#111111", TRANSPARENT_COLOR, TEXT_STYLE_BOLD, false, false))
                .build().toString();
    }

    public static String bonusDescMTJson(String title, String highTitle) {
        if (StringUtils.isEmpty(title) || StringUtils.isEmpty(highTitle)) {
            return null;
        }
        return new RichTextBuilder(title, highTitle)
                .setDefaultTextItem(new RichTextBuilder.TextItem(12, "#151515", TRANSPARENT_COLOR, TEXT_STYLE_DEFAULT, false, false))
                .setHltTextItem(new RichTextBuilder.TextItem(12, "#333333", TRANSPARENT_COLOR, TEXT_STYLE_BOLD, false, false))
                .build().toString();
    }

    public static String redDPJson(String title) {
        Cat.logEvent("INVALID_METHOD_4", "com.dianping.pay.api.util.JsonLabelUtil.redDPJson(java.lang.String)");
        if (StringUtils.isEmpty(title)) {
            return null;
        }
        String highLight = getRedPacket(title);
        return new RichTextBuilder(title, highLight)
                .setDefaultTextItem(new RichTextBuilder.TextItem(13, "#111111", TRANSPARENT_COLOR, TEXT_STYLE_DEFAULT, false, false))
                .setHltTextItem(new RichTextBuilder.TextItem(13, "#FF6633", TRANSPARENT_COLOR, TEXT_STYLE_DEFAULT, false, false))
                .build().toString();
    }

    private static String getRedPacket(String title) {
        Cat.logEvent("INVALID_METHOD_3", "com.dianping.pay.api.util.JsonLabelUtil.getRedPacket(java.lang.String)");
        if (StringUtils.isEmpty(title)) {
            return null;
        }
        for (int i = 0; i < title.length(); i++) {
            if (Character.isDigit(title.charAt(i)) && title.contains("元")) {
                if (i > title.lastIndexOf("元")) {
                    return null;
                } else {
                    return title.substring(i, title.lastIndexOf("元") + 1);
                }
            }
        }
        return null;
    }

    public static String idleHoursPromoJson(boolean isMT, String content, String highlight) {
        return idleHoursPromoJson(isMT, content, highlight,
                RichTextBuilder.ARGBColor.BLACK.getColor(), TEXT_STYLE_DEFAULT);
    }

    public static String financialCouponJson(String text) {
        RichTextBuilder.TextItem titleItem = new RichTextBuilder.TextItem();
        titleItem.setTextsize(13);
        titleItem.setBackgroundcolor("#00FFFFFF");
        titleItem.setStrikethrough(false);
        titleItem.setText(TEXT_FINANCIAL_COUPON_TITLE);
        titleItem.setTextcolor("#FF111111");
        titleItem.setTextstyle(TEXT_STYLE_BOLD);
        titleItem.setUnderline(false);

        RichTextBuilder.TextItem textItem = new RichTextBuilder.TextItem();
        textItem.setTextsize(13);
        textItem.setBackgroundcolor("#00FFFFFF");
        textItem.setStrikethrough(false);
        textItem.setText(text);
        textItem.setTextcolor("#FF111111");
        textItem.setTextstyle(TEXT_STYLE_DEFAULT);
        textItem.setUnderline(false);

        return new RichTextBuilder(TEXT_FINANCIAL_COUPON_TITLE + text, text)
                .setDefaultTextItem(textItem)
                .setHltTextItem(textItem)
                .build().toString();
    }

    public static String idleHoursPromoBannerJson(boolean isMT, String content, String highlight) {
        Cat.logEvent("INVALID_METHOD_5", "com.dianping.pay.api.util.JsonLabelUtil.idleHoursPromoBannerJson(boolean,java.lang.String,java.lang.String)");
        return idleHoursPromoJson(isMT, content, highlight,
                RichTextBuilder.ARGBColor.BLACK.getColor(), TEXT_STYLE_BOLD);
    }

    public static String idleHoursPromoBannerDetailJson(boolean isMT, String content, String highlight) {
        Cat.logEvent("INVALID_METHOD_1", "com.dianping.pay.api.util.JsonLabelUtil.idleHoursPromoBannerDetailJson(boolean,java.lang.String,java.lang.String)");
        return idleHoursPromoJson(isMT, content, highlight,
                RichTextBuilder.ARGBColor.GRAY.getColor(), TEXT_STYLE_DEFAULT);
    }

    private static String idleHoursPromoJson(boolean isMT, String content, String highlight,
                                             String defaultColor, String highlightStyle) {
        if (StringUtils.isEmpty(content) || StringUtils.isEmpty(highlight)) {
            return null;
        }
        int textSize = isMT ? 11 : 13;
        RichTextBuilder.TextItem textItem = new RichTextBuilder.TextItem();
        textItem.setTextsize(textSize);
        if (StringUtils.isNotBlank(defaultColor)) {
            textItem.setTextcolor(defaultColor);
        }

        RichTextBuilder.TextItem highlightItem = new RichTextBuilder.TextItem();
        highlightItem.setTextcolor("#FF6633");
        highlightItem.setTextsize(textSize);
        highlightItem.setTextstyle(highlightStyle);

        return new RichTextBuilder(content, highlight)
                .setDefaultTextItem(textItem)
                .setHltTextItem(highlightItem)
                .build().toString();
    }

    public static String textToJson(String text) {
        Cat.logEvent("INVALID_METHOD_5", "com.dianping.pay.api.util.JsonLabelUtil.textToJson(java.lang.String)");
        text = text == null ? "" : text;
        RichTextBuilder.TextItem textItem = new RichTextBuilder("", "").getDefaultTextItem();
        textItem.setText(text);
        return JSON.toJson(Lists.newArrayList(textItem));
    }
}
