package com.dianping.pay.api.util;

import com.dianping.api.exception.IllegalApiParamException;
import com.dianping.cat.Cat;

/**
 * Created by huawei.li on 14/11/13.
 */
public class ValidateUtils {
    public static void notNull(Object o, String msg, String title, int flag){
        Cat.logEvent("INVALID_METHOD_1", "com.dianping.pay.api.util.ValidateUtils.notNull(java.lang.Object,java.lang.String,java.lang.String,int)");
        if(o==null){
            throw new IllegalApiParamException(msg,title,flag);
        }
    }
    public static void notNull(Object o, String msg, String title){
        Cat.logEvent("INVALID_METHOD_1", "com.dianping.pay.api.util.ValidateUtils.notNull(java.lang.Object,java.lang.String,java.lang.String)");
        notNull(o,msg,title,0);
    }
    public static void notNull(Object o, Exception e) throws Exception {
        if(o==null){
            throw  e;
        }
    }
    public static void notEmpty(String s, String msg, String title, int flag){
        Cat.logEvent("INVALID_METHOD_5", "com.dianping.pay.api.util.ValidateUtils.notEmpty(java.lang.String,java.lang.String,java.lang.String,int)");
        if(s==null || s.length()==0){
            throw new IllegalApiParamException(msg,title, flag);
        }
    }
    public static void notEmpty(String s, String msg, String title){
        Cat.logEvent("INVALID_METHOD_3", "com.dianping.pay.api.util.ValidateUtils.notEmpty(java.lang.String,java.lang.String,java.lang.String)");
        notEmpty(s,msg,title,0);
    }
    public static void notEmpty(String s, Exception e) throws Exception {
        Cat.logEvent("INVALID_METHOD_2", "com.dianping.pay.api.util.ValidateUtils.notEmpty(java.lang.String,java.lang.Exception)");
        if(s==null || s.length()==0){
            throw e;
        }
    }
    public static void isTrue(boolean expr, String msg, String title, int flag){
        Cat.logEvent("INVALID_METHOD_2", "com.dianping.pay.api.util.ValidateUtils.isTrue(boolean,java.lang.String,java.lang.String,int)");
        if(!expr){
            throw new IllegalApiParamException(msg,title, flag);
        }
    }
    public static void isTrue(boolean expr, String msg, String title){
        Cat.logEvent("INVALID_METHOD_3", "com.dianping.pay.api.util.ValidateUtils.isTrue(boolean,java.lang.String,java.lang.String)");
        isTrue(expr,msg,title,0);
    }
    public static void isTrue(boolean expr, Exception e) throws Exception {
        if(!expr){
            throw e;
        }
    }
}
