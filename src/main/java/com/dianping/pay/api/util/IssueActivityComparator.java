package com.dianping.pay.api.util;

import com.dianping.gmkt.coupon.common.api.enums.MerchantCouponProductType;
import com.dianping.pay.api.beans.IssueCouponActivity;
import com.dianping.unified.coupon.manage.api.dto.UnifiedCouponGroupDTO;
import com.google.common.collect.Maps;

import java.math.BigDecimal;
import java.util.Comparator;
import java.util.Map;

public class IssueActivityComparator implements Comparator<IssueCouponActivity> {


    private static Map<Integer, Integer> PRODUCTTYPE_PRIVILEGE = Maps.newHashMap();

    private static final int DEFAULT_PRIVILEGE = 4;
    static {
        PRODUCTTYPE_PRIVILEGE.put(MerchantCouponProductType.ALL_PRODUCT.getValue(), 1);
        PRODUCTTYPE_PRIVILEGE.put(MerchantCouponProductType.CERTAIN_LABLE.getValue(), 2);
        PRODUCTTYPE_PRIVILEGE.put(MerchantCouponProductType.CERTAIN_PRODUCT.getValue(), 3);
    }

    @Override
    public int compare(IssueCouponActivity obj1, IssueCouponActivity obj2) {
        UnifiedCouponGroupDTO dto1 = obj1.getCouponGroup(), dto2 = obj2.getCouponGroup();

        int productType1 = IssueCouponUtils.getMerchantCouponProductType(obj1.getCouponGroup());
        int productType2 = IssueCouponUtils.getMerchantCouponProductType(obj2.getCouponGroup());
        if (productType1 != productType2) {
            Integer privilege1 = PRODUCTTYPE_PRIVILEGE.get(productType1) != null ? PRODUCTTYPE_PRIVILEGE.get(productType1) : DEFAULT_PRIVILEGE;
            Integer privilege2 = PRODUCTTYPE_PRIVILEGE.get(productType2) != null ? PRODUCTTYPE_PRIVILEGE.get(productType2) : DEFAULT_PRIVILEGE;
            return privilege1.compareTo(privilege2);
        }

        // 根据 通用 （新客，非新客） -> 品类的顺序进行区分
        boolean isNewUserCoupon1 = IssueCouponUtils.isNewUserCoupon(obj1.getCouponGroup());
        boolean isNewUserCoupon2 = IssueCouponUtils.isNewUserCoupon(obj2.getCouponGroup());

        if (isNewUserCoupon1 && !isNewUserCoupon2) {
            return -1;
        } else if (!isNewUserCoupon1 && isNewUserCoupon2) {
            return 1;
        }

        //门槛
        BigDecimal priceLimit1 = dto1.getPriceLimit() == null ? BigDecimal.ZERO : dto1.getPriceLimit();
        BigDecimal priceLimit2 = dto2.getPriceLimit() == null ? BigDecimal.ZERO : dto2.getPriceLimit();

        if (priceLimit1.compareTo(priceLimit2) < 0) {
            return -1;
        } else if (priceLimit1.compareTo(priceLimit2) > 0) {
            return 1;
        }

        //门槛相同比较面额 （折扣券处理方式一样，用最大折扣金额进行比较）
        if (dto1.getDiscountAmount() != null && dto2.getDiscountAmount() != null) {
            if (dto1.getDiscountAmount().compareTo(dto2.getDiscountAmount()) != 0) {
                return dto2.getDiscountAmount().compareTo(dto1.getDiscountAmount());
            }
        }

        if (dto1.getProductCodeList().size() < dto2.getProductCodeList().size()) {
            return 1;
        } else if (dto1.getProductCodeList().size() > dto2.getProductCodeList().size()) {
            return -1;
        }
        return 0;
    }
}
