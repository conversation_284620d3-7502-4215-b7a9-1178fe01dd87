package com.dianping.pay.api.util;

import com.dianping.gmkt.coupon.common.api.enums.MerchantCouponProductType;
import com.dianping.pay.api.beans.IssueCouponActivity;
import com.dianping.pay.api.entity.issuecoupon.CouponDetailInfo;
import com.dianping.unified.coupon.manage.api.dto.UnifiedCouponGroupDTO;
import com.google.common.collect.Maps;

import java.math.BigDecimal;
import java.util.Comparator;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2021/1/22
 */
public class ShopCartCouponDetailComparator implements Comparator<CouponDetailInfo> {

    private static Map<Integer, Integer> PRODUCTTYPE_PRIVILEGE = Maps.newHashMap();

    private static final int DEFAULT_PRIVILEGE = 4;
    static {
        PRODUCTTYPE_PRIVILEGE.put(MerchantCouponProductType.ALL_PRODUCT.getValue(), 1);
        PRODUCTTYPE_PRIVILEGE.put(MerchantCouponProductType.CERTAIN_LABLE.getValue(), 2);
        PRODUCTTYPE_PRIVILEGE.put(MerchantCouponProductType.CERTAIN_PRODUCT.getValue(), 3);
    }

    @Override
    public int compare(CouponDetailInfo obj1, CouponDetailInfo obj2) {

        int productType1 = obj1.getMerchantProductType();
        int productType2 = obj2.getMerchantProductType();
        if (productType1 != productType2) {
            Integer privilege1 = PRODUCTTYPE_PRIVILEGE.get(productType1) != null ? PRODUCTTYPE_PRIVILEGE.get(productType1) : DEFAULT_PRIVILEGE;
            Integer privilege2 = PRODUCTTYPE_PRIVILEGE.get(productType2) != null ? PRODUCTTYPE_PRIVILEGE.get(productType2) : DEFAULT_PRIVILEGE;
            return privilege1.compareTo(privilege2);
        }

        // 根据 通用 （新客，非新客） -> 品类的顺序进行区分
        boolean isNewUserCoupon1 = obj1.isFreshExclusive();
        boolean isNewUserCoupon2 = obj2.isFreshExclusive();

        if (isNewUserCoupon1 && !isNewUserCoupon2) {
            return -1;
        } else if (!isNewUserCoupon1 && isNewUserCoupon2) {
            return 1;
        }

        //门槛
        BigDecimal priceLimit1 = obj1.getPriceLimit() == null ? BigDecimal.ZERO : obj1.getPriceLimit();
        BigDecimal priceLimit2 = obj2.getPriceLimit() == null ? BigDecimal.ZERO : obj2.getPriceLimit();

        if (priceLimit1.compareTo(priceLimit2) < 0) {
            return -1;
        } else if (priceLimit1.compareTo(priceLimit2) > 0) {
            return 1;
        }

        //门槛相同比较面额
        return obj2.getDiscountAmount().compareTo(obj1.getDiscountAmount());

    }
}
