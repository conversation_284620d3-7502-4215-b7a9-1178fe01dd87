package com.dianping.pay.api.util;

import com.dianping.api.common.enums.PageSourceEnum;
import com.dianping.api.constans.CommonConstants;
import com.dianping.cat.Cat;
import com.dianping.gmkt.coupon.common.api.enums.CouponCommonTagEnum;
import com.dianping.gmkt.coupon.common.api.enums.CouponGroupValueType;
import com.dianping.gmkt.scene.api.delivery.dto.res.CouponDetailDTO;
import com.dianping.lion.client.ConfigEvent;
import com.dianping.lion.client.ConfigListener;
import com.dianping.lion.client.Lion;
import com.dianping.pay.api.beans.ResourcePromotionDo;
import com.dianping.pay.api.biz.activity.newloader.dto.CouponActivityContext;
import com.dianping.pay.api.biz.activity.newloader.mapper.ProductPromoInfoMapper;
import com.dianping.pay.api.constants.CatEventConstants;
import com.dianping.pay.api.constants.PlusIcons;
import com.dianping.pay.api.entity.issuecoupon.CouponDetailInfo;
import com.dianping.pay.api.entity.issuecoupon.CouponPromoInfo;
import com.dianping.pay.api.entity.issuecoupon.PromoSimpleInfoItem;
import com.dianping.pay.api.enums.CornerMarkTypeEnum;
import com.dianping.pay.api.enums.LeadActionEnum;
import com.dianping.pay.api.enums.MerchantUserTypeEnum;
import com.dianping.pay.api.enums.PromoTypeEnum;
import com.fasterxml.jackson.core.type.TypeReference;
import com.google.common.collect.Lists;
import com.sankuai.nib.mkt.common.base.util.JsonUtils;
import com.sankuai.nibmkt.promotion.api.common.enums.DiscountProviderEnum;
import com.sankuai.nibmkt.promotion.api.common.enums.PromotionPropertyEnum;
import com.sankuai.nibmktproxy.queryclient.proxy.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang.math.NumberUtils;
import org.apache.commons.lang3.StringUtils;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
public class ProxyPromoInfoHelper {


    private static boolean juhuasuanFallBackSwitch;

    static {
        juhuasuanFallBackSwitch = Lion.getBoolean(LionConstants.APP_KEY, LionConstants.JUHUASUAN_FALLBACK_SWITCH, false);
        Lion.addConfigListener(LionConstants.JUHUASUAN_FALLBACK_SWITCH, new ConfigListener() {
            @Override
            public void configChanged(ConfigEvent configEvent) {
                juhuasuanFallBackSwitch = Lion.getBoolean(LionConstants.APP_KEY, LionConstants.JUHUASUAN_FALLBACK_SWITCH, false);
            }
        });
    }

    public static List<GetPromotionDTO> getPromotions(CouponActivityContext context) {
        Cat.logEvent("INVALID_METHOD_5", "com.dianping.pay.api.util.ProxyPromoInfoHelper.getPromotions(com.dianping.pay.api.biz.activity.newloader.dto.CouponActivityContext)");
        PromotionDTOResult promotionDTOResult = context.getPromotionDTOResult();
        if (promotionDTOResult == null) {
            return null;
        }
        List<GetPromotionDTO> promos = promotionDTOResult.getGetPromotionDTO();
        if (CollectionUtils.isEmpty(promos)) {
            return Lists.newArrayList();
        }
        return promos.stream().filter(ProxyPromoInfoHelper::isCouponPromo).collect(Collectors.toList());
    }

    public static List<GetPromotionDTO> getGovConsumePromotions(CouponActivityContext context) {
        Cat.logEvent("INVALID_METHOD_2", "com.dianping.pay.api.util.ProxyPromoInfoHelper.getGovConsumePromotions(com.dianping.pay.api.biz.activity.newloader.dto.CouponActivityContext)");
        if(context == null) {
            return null;
        }
        PromotionDTOResult promotionDTOResult = context.getPromotionDTOResult();
        if (promotionDTOResult == null) {
            return null;
        }
        List<GetPromotionDTO> promos = promotionDTOResult.getGetPromotionDTO();
        if (CollectionUtils.isEmpty(promos)) {
            return Lists.newArrayList();
        }
        List<GetPromotionDTO> govConsumePromotions = Lists.newArrayList();
        for (GetPromotionDTO promo : promos) {
            if (promo == null || !isFinancialGovConsumeCouponPromo(promo)) {
                continue;
            }
            govConsumePromotions.add(promo);
        }
        return govConsumePromotions;
    }

    public static boolean isJuhuasuan(CouponActivityContext context) {
        // 如果判定条件失败
        if (juhuasuanFallBackSwitch) {
            return false;
        }
        PromotionDTOResult promotionDTOResult = context.getPromotionDTOResult();
        if (promotionDTOResult == null) {
            return false;
        }
        return promotionDTOResult.getDiscountProvider() == DiscountProviderEnum.COST_EFFECTIVE.getCode();
    }

    public static CouponPromoInfo genCouponInfoItem(CouponActivityContext context) {
        PromotionDTOResult promotionDTOResult = context.getPromotionDTOResult();
        if (promotionDTOResult == null) {
            return null;
        }

        List<GetPromotionDTO> promos = promotionDTOResult.getGetPromotionDTO();

        CouponPromoInfo promoInfoItem = new CouponPromoInfo();
        promoInfoItem.setType(PromoTypeEnum.COUPON.getCode());
        promoInfoItem.setPromoTitle("抵用券");
        promoInfoItem.setIconUrl(context.isMt() ? PlusIcons.MT_COUPON : PlusIcons.DP_COUPON);
        List<PromoSimpleInfoItem> concisePromoInfoDescItems = Lists.newArrayList();

        if (CollectionUtils.isEmpty(promos)) {
            return null;
        }
        for (GetPromotionDTO promo : promos) {
            if (promo == null || !isCouponPromo(promo)) {
                continue;
            }

            CouponDTO couponDTO = promo.getPromotionDTO().getCouponDTO();
            if (couponDTO == null) {
                continue;
            }
            PromoSimpleInfoItem promoInfoDescItem = new PromoSimpleInfoItem();
            promoInfoDescItem.setStyle(1);
            promoInfoDescItem.setText(buildConciseCouponText(couponDTO));
            concisePromoInfoDescItems.add(promoInfoDescItem);
        }

        if (CollectionUtils.isEmpty(concisePromoInfoDescItems)) {
            return null;
        }
        if (concisePromoInfoDescItems.size() > 2) {
            promoInfoItem.setPromoInfoDescItems(concisePromoInfoDescItems.subList(0, 2));
        } else {
            promoInfoItem.setPromoInfoDescItems(concisePromoInfoDescItems);
        }

        promoInfoItem.setLeadAction(LeadActionEnum.TOAST_DEFAULT.getCode());
        promoInfoItem.setLeadText("去领券");
        return promoInfoItem;
    }

    public static CouponPromoInfo genFinancialGovConsumeCouponInfoItem(CouponActivityContext context) {
        PromotionDTOResult promotionDTOResult = context.getPromotionDTOResult();
        if (promotionDTOResult == null) {
            return null;
        }
        List<GetPromotionDTO> promos = promotionDTOResult.getGetPromotionDTO();
        if (CollectionUtils.isEmpty(promos)) {
            return null;
        }

        List<PromoSimpleInfoItem> concisePromoInfoDescItems = Lists.newArrayList();
        for (GetPromotionDTO promo : promos) {
            if (promo == null || !isFinancialGovConsumeCouponPromo(promo)) {
                continue;
            }
            FinancialCouponDTO couponDTO = promo.getPromotionDTO().getFinancialCouponDTO();
            PromoSimpleInfoItem promoInfoDescItem = new PromoSimpleInfoItem();
            promoInfoDescItem.setStyle(1);
            String text = buildFinancialConciseConsumeCouponText(couponDTO);
            if(StringUtils.isEmpty(text)) {
                continue;
            }
            promoInfoDescItem.setText(text);
            concisePromoInfoDescItems.add(promoInfoDescItem);
        }
        if (CollectionUtils.isEmpty(concisePromoInfoDescItems)) {
            return null;
        }

        CouponPromoInfo promoInfoItem = new CouponPromoInfo();
        promoInfoItem.setType(PromoTypeEnum.GOV_CONSUME_COUPON_PROMO.getCode());
        promoInfoItem.setPromoTitle("消费券");
        promoInfoItem.setIconUrl(context.isMt() ? PlusIcons.MT_COUPON : PlusIcons.DP_COUPON);
        //理论只返回最优的，只返回1个，不需要截断
        promoInfoItem.setPromoInfoDescItems(concisePromoInfoDescItems);
        promoInfoItem.setLeadAction(LeadActionEnum.TOAST_DEFAULT.getCode());
        promoInfoItem.setLeadText("去领券");
        return promoInfoItem;
    }

    public static boolean isCouponPromo(GetPromotionDTO promo) {
        if (promo.getPromotionDTO() == null || promo.getPromotionDTO().getCouponDTO() == null) {
            return false;
        }
        return promo.getPromotionType() != null && promo.getPromotionType().getValue() == PromotionType.COUPON.getValue();
    }

    public static boolean isFinancialGovConsumeCouponPromo(GetPromotionDTO promo) {
        if (promo.getPromotionDTO() == null
            || promo.getPromotionDTO().getFinancialCouponDTO() == null
            || promo.getPromotionType() == null
            || promo.getPromotionType().getValue() != PromotionType.FINANCIAL_COUPON.getValue()) {
            return false;
        }
        FinancialCouponDTO couponDTO = promo.getPromotionDTO().getFinancialCouponDTO();
        if (couponDTO == null
            || couponDTO.getAssignedStatus() == null
            || couponDTO.getFinancialCouponCategoryEnum() == null
            || !couponDTO.isAvailable()
            || couponDTO.getAssignedStatus() == AssignedStatusEnum.NO_STATUS
            || couponDTO.getFinancialCouponCategoryEnum() != FinancialCouponCategoryEnum.GOVERNMENT_CONSUME_COUPON) {
            return false;
        }
        return true;
    }

    public static String buildConciseCouponText(CouponDTO couponDTO) {
        if (couponDTO.getCouponValueType() == CouponGroupValueType.DISCOUNT.getCode()) {
            // X折无门槛优惠券/满Y元X折
            String discountText = (double) couponDTO.getDiscountPercent() / 10 + "";
            if (noThreshold(couponDTO)) {
                return discountText + "折无门槛优惠券";
            } else {
                String minConsumption = couponDTO.getMinConsumption();
                String thresholdAmt = toYuanStr(minConsumption);
                return "满" + thresholdAmt + "元" + discountText + "折";
            }

        } else {
            String minConsumption = couponDTO.getMinConsumption();
            String amt = toYuanStr(couponDTO.getCouponValue());
            if (noThreshold(couponDTO)) {
                return amt + "元无门槛券";
            }
            String thresholdAmt = toYuanStr(minConsumption);
            return "满" + thresholdAmt + "减" + amt + "券";
        }
    }

    public static String buildFinancialConciseConsumeCouponText(FinancialCouponDTO couponDTO) {
        Cat.logEvent("INVALID_METHOD_3", "com.dianping.pay.api.util.ProxyPromoInfoHelper.buildFinancialConciseConsumeCouponText(com.sankuai.nibmktproxy.queryclient.proxy.FinancialCouponDTO)");
        String consumeCouponValue = couponDTO.getCouponValue();
        String minConsumptionValue = couponDTO.getMinConsumption();
        if(couponDTO.getFinancialCouponSubTypeEnum() == FinancialCouponSubTypeEnum.FULL_DISCOUNT_COUPON) {
            if(StringUtils.isEmpty(consumeCouponValue) || StringUtils.isEmpty(minConsumptionValue)) {
                return null;
            }
            String discountValue = toYuanStr(consumeCouponValue);
            String discountLimitValue = toYuanStr(minConsumptionValue);
            return "满" + discountLimitValue + "减" + discountValue + "消费券";
        } else if(couponDTO.getFinancialCouponSubTypeEnum() == FinancialCouponSubTypeEnum.INSTANT_DISCOUNT_COUPON) {
            if(StringUtils.isEmpty(consumeCouponValue)) {
                return null;
            }
            String discountValue = toYuanStr(consumeCouponValue);
            return discountValue + "元无门槛消费券";
        } else {
            return null;
        }
    }

    private static String toYuanStr(String amount) {
        return toYuan(amount).stripTrailingZeros().toPlainString();
    }

    private static BigDecimal toYuan(String couponValue) {
        return new BigDecimal(couponValue).divide(BigDecimal.valueOf(100), 2, BigDecimal.ROUND_HALF_UP);
    }

    private static boolean noThreshold(CouponDTO dto) {
        return StringUtils.isBlank(dto.getMinConsumption()) || BigDecimal.ZERO.compareTo(new BigDecimal(dto.getMinConsumption())) == 0;
    }

    private static boolean noThreshold(FinancialCouponDTO dto) {
        return StringUtils.isEmpty(dto.getMinConsumption()) || dto.getFinancialCouponSubTypeEnum() == FinancialCouponSubTypeEnum.INSTANT_DISCOUNT_COUPON;
    }

    public static CouponDetailInfo genCouponDetailItem(CouponDTO dto, CouponActivityContext context, GetPromotionDTO promo) {
        CouponDetailInfo item = new CouponDetailInfo();
        item.setCouponGroupId(dto.getCouponGroupId());
        /**
         * 预付将领券购的券定义为神券
         * 只有预付才会将isGodCouponDisplay传为true
         */
        boolean isLQG = MapUtils.getBooleanValue(dto.getPromotionFieldMap(), PromotionFieldEnum.COUPON_PURCHASE_FLAG.name(), false);
        boolean isGodCouponDisplay = context != null && PageSourceEnum.BeautyMedicalProductDetail.code.equals(context.getCouponPageSource());
        if (isGodCouponDisplay && isLQG) {
            return genGodCouponDetailItem(dto, promo);
        }
        // TODO
        if (dto.getCouponValueType() == CouponGroupValueType.DISCOUNT.getCode()) {
            item.setAmount((double) dto.getDiscountPercent() / 10 + "");
            item.setAmountCornerMark("折");
        } else {
            item.setAmount(toYuanStr(dto.getCouponValue()));
            item.setAmountCornerMark("元");
        }

        item.setDiscountAmount(new BigDecimal(dto.getCouponValue()));
        if (!noThreshold(dto)) {
            item.setPriceLimit(new BigDecimal(dto.getMinConsumption()));
        }
        item.setCanAssign(dto.isCanAssign());
        item.setTitle(buildCouponTitle(dto));

        String categoryLimitText = dto.getCategoryLimitText();
        if (StringUtils.isNotBlank(categoryLimitText)) {
            item.setCouponCategory("查看适用品类");
            item.setCouponCategoryDesc("限" + categoryLimitText + "类商品");
        }

        item.setCouponSuitDesc(buildCouponSuitDesc(dto));
        item.setCouponThresholdDesc(buildCouponThresholdDesc(dto));
        item.setFreshExclusive(dto.isNewUserUse());
        item.setOptimal(dto.isOptimal());

        if (dto.isMerchantCoupon) {
            item.setCouponSrc(1);
        } else {
            item.setCouponSrc(0);
        }
        buildCornerMark(item, dto);
        return item;
    }

    private static CouponDetailInfo genGodCouponDetailItem(CouponDTO dto, GetPromotionDTO promo) {
        CouponDetailInfo item = new CouponDetailInfo();
        item.setCouponGroupId(dto.getCouponGroupId());
        item.setAmount(toYuanStr(dto.getCouponValue()));
        item.setAmountCornerMark("元");
        item.setDiscountAmount(new BigDecimal(dto.getCouponValue()));
        if (!noThreshold(dto)) {
            item.setPriceLimit(new BigDecimal(dto.getMinConsumption()));
        }
        item.setCanAssign(dto.isCanAssign());
        item.setTitle(dto.getCouponTitle());
        item.setCouponCategoryDesc("部分商品可用");
        item.setCouponSuitDesc(IssueCouponUtils.formatGodCouponTimePeriod(promo.getBeginTime(), promo.getEndTime()));
        item.setCouponThresholdDesc(buildCouponThresholdDesc(dto));
        item.setCouponSrc(0);
        item.setCornerMarkType(CornerMarkTypeEnum.OTHER_MARK.getCode());
        item.setCornerMarkUrl(Lion.getString(LionConstants.APP_KEY, LionConstants.RESOURCE_CORNER_MARK_URL));
        item.setCoupontype(3);
        return item;
    }

    public static CouponDetailInfo genFinancialGovConsumeCouponDetailItem(PromotionDTO promotionDTO) {
        Cat.logEvent("INVALID_METHOD_5", "com.dianping.pay.api.util.ProxyPromoInfoHelper.genFinancialGovConsumeCouponDetailItem(com.sankuai.nibmktproxy.queryclient.proxy.FinancialCouponDTO)");
        FinancialCouponDTO dto = promotionDTO.getFinancialCouponDTO();
        CouponDetailInfo item = new CouponDetailInfo();
        item.setCouponGroupId(dto.getCouponGroupId());
        item.setAmount(toYuanStr(dto.getCouponValue()));
        item.setAmountCornerMark("元");
        //通过couponType标志后续发券类型
        item.setCoupontype(2);
        item.setDiscountAmount(new BigDecimal(dto.getCouponValue()));
        if (!noThreshold(dto)) {
            item.setPriceLimit(new BigDecimal(dto.getMinConsumption()));
        }
        if(dto.getAssignedStatus() == AssignedStatusEnum.ALREADY_ASSIGNED) {
            item.setCanAssign(false);
        } else if(dto.getAssignedStatus() == AssignedStatusEnum.NO_ASSIGNED) {
            item.setCanAssign(true);
        }
        if(StringUtils.isEmpty(dto.getCouponTitle())) {
            item.setTitle("政府消费券");
        } else {
            item.setTitle(dto.getCouponTitle());
        }
        String categoryLimitText = dto.getCategoryLimitText();
        if (StringUtils.isNotBlank(categoryLimitText)) {
            item.setCouponCategory("查看适用品类");
            item.setCouponCategoryDesc((categoryLimitText.startsWith("限") ? "" : "限") + categoryLimitText + "类商品");
        }
       item.setCouponSuitDesc(dto.getValidDateText());
       item.setCouponThresholdDesc(buildCouponThresholdDesc(dto));
        item.setCornerMarkType(CornerMarkTypeEnum.NON_MARK.getCode());
        item.setPackageSecretKey(getPackageSecretKey(dto));
        return item;
    }

    private static String getPackageSecretKey(FinancialCouponDTO financialCouponDTO){
        String packageSecretKey = "";
        if (financialCouponDTO == null || MapUtils.isEmpty(financialCouponDTO.getPromotionFieldMap())){
            return packageSecretKey;
        }
        String financeExt = financialCouponDTO.getPromotionFieldMap().get(PromotionPropertyEnum.FINANCE_EXT.getValue());
        if (StringUtils.isNotBlank(financeExt)){
            Map<String, String> map = JsonUtils.parseObject(financeExt, new TypeReference<Map<String, String>>() {});
            if(MapUtils.isNotEmpty(map)){
                packageSecretKey = map.get(CommonConstants.PACKAGE_SECRET_KEY);
            }
            packageSecretKey = StringUtils.isEmpty(packageSecretKey) ? "" : packageSecretKey;
        }
        return packageSecretKey;
    }

    private static String buildCouponTitle(CouponDTO couponDTO) {
        if (!couponDTO.isIsMerchantCoupon()) {
            return couponDTO.getCouponTitle();
        }
        int categoryLimit = couponDTO.getCategoryLimit();
        String title;
        switch (categoryLimit) {
            case 0:
                title = "商家通用券";
                break;
            case 1:
                title = "商家品类券";
                break;
            case 2:
                title = "商家商品券";
                break;
            default:
                title = couponDTO.getCouponTitle();
        }

        // 折扣券再拼接上尾部内容
        if (couponDTO.getCouponValueType() == CouponGroupValueType.DISCOUNT.getCode()) {
            if (StringUtils.isNotBlank(couponDTO.getMaxReduce()) && NumberUtils.isNumber(couponDTO.getMaxReduce())) {
                title += String.format("·最高可减%s元", new BigDecimal(couponDTO.getMaxReduce()).stripTrailingZeros().toPlainString());
            }
        }
        return title;
    }

    private static String buildCouponSuitDesc(CouponDTO couponDTO) {
        String validDateText = couponDTO.getValidDateText();
        String compositionText = null;
        if (couponDTO.isIsMerchantCoupon()) {
            compositionText = couponDTO.getCompositionText();
        }
        String text = null;
        if (StringUtils.isNotBlank(validDateText)) {
            text = validDateText;
        }
        if (StringUtils.isNotBlank(compositionText)) {
            if (text != null) {
                text = validDateText + "，" + compositionText;
            } else {
                text = compositionText;
            }
        }
        return text;
    }

    private static String buildCouponThresholdDesc(CouponDTO couponDTO) {
        String minConsumption = couponDTO.getMinConsumption();
        if (noThreshold(couponDTO)) {
            return "无门槛";
        }
        return "满" + toYuan(minConsumption).stripTrailingZeros().toPlainString() + "元可用";
    }

    public static String buildCouponThresholdDesc(BigDecimal priceLimit) {
        if (priceLimit == null || priceLimit.compareTo(BigDecimal.ZERO) == 0) {
            return "无门槛";
        }
        return "满" + priceLimit.stripTrailingZeros().toPlainString() + "元可用";
    }

    private static String buildCouponThresholdDesc(FinancialCouponDTO couponDTO) {
        String minConsumption = couponDTO.getMinConsumption();
        if (couponDTO.getFinancialCouponSubTypeEnum() == FinancialCouponSubTypeEnum.INSTANT_DISCOUNT_COUPON
            || StringUtils.isEmpty(minConsumption)) {
            return "无门槛";
        }
        return "满" + toYuan(minConsumption).stripTrailingZeros().toPlainString() + "元可用";
    }

    public static CouponDetailInfo genCouponDetailItemByResourcePromo(ResourcePromotionDo dto) {
        CouponDetailInfo item = new CouponDetailInfo();
        CouponDetailDTO couponDetailDTO = dto.getDetailDTO();
        item.setAmount(new BigDecimal(couponDetailDTO.getAmountPrice()).stripTrailingZeros().toPlainString());
        item.setDiscountAmount(new BigDecimal(couponDetailDTO.getAmountPrice()).multiply(new BigDecimal(100)));
        if (StringUtils.isNotBlank(couponDetailDTO.getLimitPrice())) {
            item.setPriceLimit(new BigDecimal(couponDetailDTO.getLimitPrice()).multiply(new BigDecimal(100)));
        }
        item.setCanAssign(false);
        item.setTitle(couponDetailDTO.getCouponGroupName());
        item.setCouponThresholdDesc(buildCouponThresholdDesc(couponDetailDTO));
        item.setCouponSuitDesc(IssueCouponUtils.formatTimePeriod(new Date(couponDetailDTO.getUseBeginTime()), new Date(couponDetailDTO.getUseEndTime())));
        item.setFreshExclusive(true);
        item.setOptimal(false);
        return item;
    }

    private static String buildCouponThresholdDesc(CouponDetailDTO couponDTO) {
        String minConsumption = couponDTO.getLimitPrice();
        if (noThreshold(couponDTO)) {
            return "无门槛";
        }
        return "满" + new BigDecimal(minConsumption).stripTrailingZeros().toPlainString() + "元可用";
    }

    private static boolean noThreshold(CouponDetailDTO dto) {
        return StringUtils.isBlank(dto.getLimitPrice()) || BigDecimal.ZERO.compareTo(new BigDecimal(dto.getLimitPrice())) == 0;
    }

    public static void buildCornerMark(CouponDetailInfo item, CouponDTO dto) {
        if (StringUtils.isNotBlank(dto.getCouponCommonTag())) {
            Map<String, String> tagUrlMap = Lion.getMap(LionConstants.APP_KEY, LionConstants.CORNER_MARK_URL, String.class);
            if (CouponCommonTagEnum.beExclusiveSurprise(dto.getCouponCommonTag())) {
                item.setCornerMarkType(CornerMarkTypeEnum.OTHER_MARK.getCode());
                item.setCornerMarkUrl(tagUrlMap.get(CouponCommonTagEnum.EXCLUSIVE_SURPRISE.getCode()));
                return;
            }
            if (CouponCommonTagEnum.beMedicalBeautyMember(dto.getCouponCommonTag())) {
                item.setCornerMarkType(CornerMarkTypeEnum.OTHER_MARK.getCode());
                item.setCornerMarkUrl(Lion.getString(LionConstants.APP_KEY, LionConstants.MEDICAL_BEAUTY_CORNER_MARK_URL));
                item.setMerchantUserType(MerchantUserTypeEnum.MB_MEMBER_USER.getCode());
                return;
            }
            if (CouponCommonTagEnum.beProductNewUser(dto.getCouponCommonTag())) {
                item.setCornerMarkType(CornerMarkTypeEnum.OTHER_MARK.getCode());
                item.setCornerMarkUrl(Lion.getString(LionConstants.APP_KEY, LionConstants.PRODUCT_NEW_USER_CORNER_MARK_URL));
                item.setMerchantUserType(MerchantUserTypeEnum.PRODUCT_NEW_USER.getCode());
                return;
            }
        }
        if (dto.isNewUserUse()) {
            item.setCornerMarkType(CornerMarkTypeEnum.NEW_USER_MARK.getCode());
            item.setMerchantUserType(MerchantUserTypeEnum.BRAND_NEW_USER.getCode());
        } else {
            item.setCornerMarkType(CornerMarkTypeEnum.NON_MARK.getCode());
            item.setMerchantUserType(MerchantUserTypeEnum.ALL_USER.getCode());
        }
    }

    // 获取最佳优惠
    public static List<GetPromotionDTO> getBestDeductionPromotionList(PromotionDTOResult promoResult) {
        if (promoResult == null) {
            return null;
        }
        List<GetPromotionDTO> getPromotionDTOs = promoResult.getGetPromotionDTO();
        if (CollectionUtils.isEmpty(getPromotionDTOs)) {
            return null;
        }

        if (promoResult.getProductExtraInfo() == null ||
                promoResult.getProductExtraInfo().getProductDisplayShow() == null ||
                promoResult.getProductExtraInfo().getProductDisplayShow().getPrductShowMap() == null) {
            // 有优惠数据但是没有对应的文案渲染数据，打点告警
            Cat.logEvent(CatEventConstants.PROXY_RESULT_ILLEGAL, "-1");
            log.warn("[proxyData] empty. result: {}", promoResult);
            return null;
        }

        Map<Integer, PromoCombine> promoCombineMap = promoResult.getPromoCombineMap();
        if (MapUtils.isEmpty(promoCombineMap)) {
            // 有优惠数据但是没有对应的文案渲染数据，打点告警
            Cat.logEvent(CatEventConstants.PROXY_RESULT_ILLEGAL, "-1");
            log.warn("[proxyData] empty. result: {}", promoResult);
            return null;
        }
        PromoCombine promoCombine = promoCombineMap.get(MergeQueryStrategy.BEST_PROMOTION.getValue());
        if (promoCombine == null) {
            return null;
        }
        // 最佳优惠组合下的优惠列表
        List<PromotionKey> combineList = promoCombine.getCombineList();
        if (CollectionUtils.isEmpty(combineList)) {
            return null;
        }

        List<GetPromotionDTO> bestPromotionList = new ArrayList<>();
        for (PromotionKey promotionKey : combineList) {
            if (promotionKey == null || StringUtils.isBlank(promotionKey.getUniqueId())) {
                continue;
            }
            String uniqueId = promotionKey.getUniqueId();
            for (GetPromotionDTO promotionDTO : getPromotionDTOs) {
                if (promotionDTO.getPromotionType() == PromotionType.DEDUCTION && uniqueId.equals(promotionDTO.getUniqueId())) {
                    bestPromotionList.add(promotionDTO);
                }
            }
        }
        return bestPromotionList;
    }


    /**
     * 从代理返回结果中获取立减文案，这里是立减精简文案，如果包含多项立减，这里会做值聚合
     *
     * @return
     */
    public static String getDeductionTag(PromotionDTOResult promoResult) {
        if (promoResult == null || promoResult.getProductExtraInfo() == null || promoResult.getProductExtraInfo().getProductDisplayShow() == null) {
            return null;
        }
        ProductDisplayShow productDisplayShow = promoResult.getProductExtraInfo().getProductDisplayShow();
        if (productDisplayShow == null) {
            return null;
        }
        Map<Integer, ProductShowDTO> productShowMap = productDisplayShow.getPrductShowMap();
        if (MapUtils.isEmpty(productShowMap)) {
            return null;
        }
        // 弹窗描述文案
        ProductShowDTO productShowDTO = productShowMap.get(ProductShowEnum.DEDUCTION_TAG.getValue());
        if (productShowDTO == null) {
            return null;
        }
        return productShowDTO.getValue();
    }

    /**
     * 从代理返回结果中获取满件折入口层文案
     *
     * @return
     */
    public static String getFullDiscountDeductionTag(PromotionDTOResult promoResult) {
        if (promoResult == null || promoResult.getProductExtraInfo() == null || promoResult.getProductExtraInfo().getProductDisplayShow() == null) {
            return null;
        }
        Map<Integer, ProductShowDTO> productShowMap = promoResult.getProductExtraInfo().getProductDisplayShow().getPrductShowMap();
        if (MapUtils.isEmpty(productShowMap)) {
            return null;
        }
        // 入口层描述文案
        ProductShowDTO productShowDTO = productShowMap.get(ProductShowEnum.PLATFORM_DISCOUNT_BY_BUY_NUM_DEDUCTION_TAG.getValue());
        if (productShowDTO == null) {
            return null;
        }
        return "多买多减·" + productShowDTO.getValue();
    }

    /**
     * 从代理返回结果中获取满件折浮层文案
     *
     * @return
     */
    public static String getFullDiscountDeductionDesc(PromotionDTOResult promoResult) {
        if (promoResult == null || promoResult.getProductExtraInfo() == null || promoResult.getProductExtraInfo().getProductDisplayShow() == null) {
            return null;
        }
        Map<Integer, ProductShowDTO> productShowMap = promoResult.getProductExtraInfo().getProductDisplayShow().getPrductShowMap();
        if (MapUtils.isEmpty(productShowMap)) {
            return null;
        }
        // 入口层描述文案
        ProductShowDTO productShowDTO = productShowMap.get(ProductShowEnum.PLATFORM_DISCOUNT_BY_BUY_NUM_DEDUCTION_DESCRIPTION.getValue());
        if (productShowDTO == null) {
            return null;
        }
        return "多买多减：" + productShowDTO.getValue();
    }
}
