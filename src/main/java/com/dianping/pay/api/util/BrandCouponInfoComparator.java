package com.dianping.pay.api.util;

import com.dianping.cat.Cat;
import com.dianping.pay.api.entity.issuecoupon.BrandCouponInfo;
import com.dianping.pay.api.enums.BrandCouponIssueStatus;
import com.google.common.collect.Maps;

import java.math.BigDecimal;
import java.util.Comparator;
import java.util.Map;

public class BrandCouponInfoComparator implements Comparator<BrandCouponInfo> {

    private static Map<Integer, Integer> STATUS_PRIVILEGE = Maps.newHashMap();
    private static final int DEFAULT_PRIVILEGE = 5;


    static {
        STATUS_PRIVILEGE.put(BrandCouponIssueStatus.CAN_ISSUE.getStatus(), 1);
        STATUS_PRIVILEGE.put(BrandCouponIssueStatus.FUTURE_CAN_ISSUE.getStatus(), 2);
        STATUS_PRIVILEGE.put(BrandCouponIssueStatus.HAS_ISSUED.getStatus(), 3);
        STATUS_PRIVILEGE.put(BrandCouponIssueStatus.SOLD_OUT.getStatus(), 4);

    }

    /**
     * 排序规则：可领>未来可领>已领>已领光的状态展示，每一类优惠券有多张则按照金额由大到小展示。
     * @param obj1
     * @param obj2
     * @return
     */
    @Override
    public int compare(BrandCouponInfo obj1, BrandCouponInfo obj2) {
        Cat.logEvent("INVALID_METHOD_1", "com.dianping.pay.api.util.BrandCouponInfoComparator.compare(com.dianping.pay.api.entity.issuecoupon.BrandCouponInfo,com.dianping.pay.api.entity.issuecoupon.BrandCouponInfo)");

        Integer statusPrivilege1 = STATUS_PRIVILEGE.getOrDefault(obj1.getIssueStatus(), DEFAULT_PRIVILEGE);
        Integer statusPrivilege2 = STATUS_PRIVILEGE.getOrDefault(obj2.getIssueStatus(), DEFAULT_PRIVILEGE);
        BigDecimal discountAmount1 =  new BigDecimal(obj1.getDiscountAmount());
        BigDecimal discountAmount2 =  new BigDecimal(obj2.getDiscountAmount());

        if (!statusPrivilege1.equals(statusPrivilege2)) {
            return statusPrivilege1.compareTo(statusPrivilege2);
        }

        // 剩下按照金额比较
        return discountAmount2.compareTo(discountAmount1);
    }
}
