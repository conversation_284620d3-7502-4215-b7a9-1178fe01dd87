package com.dianping.pay.api.util;

import com.dianping.cat.Cat;
import com.dianping.gmkt.coupon.common.api.enums.CouponBusiness;
import com.dianping.pay.api.beans.IssueCouponActivity;
import com.dianping.pay.api.enums.IssueCouponOptionType;
import com.dianping.pay.cashier.biz.enums.PaymentRule;
import com.dianping.pay.common.enums.ProductCode;
import com.dianping.pay.promo.common.enums.UserType;
import com.dianping.pay.promo.execute.service.enums.PromoToolType;
import com.dianping.unified.coupon.manage.api.dto.UnifiedCouponCategoryDTO;
import com.dianping.unified.coupon.manage.api.dto.UnifiedCouponGroupDTO;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class BeautyIssueCouponUtils {

    private static final Map<Integer, String> categoryMapDesc = new HashMap<Integer, String>();

    static {
        categoryMapDesc.put(50, "丽人");
        categoryMapDesc.put(5001, "美发");
        categoryMapDesc.put(5002, "美甲美睫");
        categoryMapDesc.put(5003, "美容／SPA");
        categoryMapDesc.put(5004, "瑜珈／舞蹈");
        categoryMapDesc.put(5006, "瘦身纤体");
        categoryMapDesc.put(5009, "祛痘");
        categoryMapDesc.put(5010, "纹身");
        categoryMapDesc.put(5011, "脱毛");
        categoryMapDesc.put(5012, "韩式定妆");
        categoryMapDesc.put(5013, "医学美容");
        categoryMapDesc.put(5007, "其他");

        categoryMapDesc.put(5005, "丽人");
        categoryMapDesc.put(5501, "美发");
        categoryMapDesc.put(5502, "美甲美睫");
        categoryMapDesc.put(5503, "美容／SPA");
        categoryMapDesc.put(5504, "个性写真");
        categoryMapDesc.put(5505, "其他");
        categoryMapDesc.put(5007, "瑜珈／舞蹈");
    }

    public static boolean formatIncompatible(UnifiedCouponGroupDTO couponGroupDTO2) {
        List<Integer> disablePromoToolTypeList = toDisablePromoToolType(couponGroupDTO2.getPaymentRuleId());
        if (disablePromoToolTypeList.contains(PromoToolType.SHOP_PROMO.getCode())
                && disablePromoToolTypeList.contains(PromoToolType.MT_SHOP_PROMO.getCode())
                && disablePromoToolTypeList.contains(PromoToolType.DISCOUNT.getCode())
                && disablePromoToolTypeList.contains(PromoToolType.MT_DISCOUNT.getCode())) {
            return true;
        }
        return false;
    }

    public static String formatIncompatibleRule(UnifiedCouponGroupDTO couponGroupDTO2, boolean isIncompatible) {
        Cat.logEvent("INVALID_METHOD_4", "com.dianping.pay.api.util.BeautyIssueCouponUtils.formatIncompatibleRule(com.dianping.unified.coupon.manage.api.dto.UnifiedCouponGroupDTO,boolean)");
        List<Integer> disablePromoToolTypeList = toDisablePromoToolType(couponGroupDTO2.getPaymentRuleId());
        if (isIncompatible) {
            return "不可与其他优惠同享";
        }
        //平台立减可同享
        if (!disablePromoToolTypeList.contains(PromoToolType.DISCOUNT.getCode())
                && !disablePromoToolTypeList.contains(PromoToolType.MT_DISCOUNT.getCode())) {
            return "与团购立减可叠加";
        }
        //平台立减不可同享、商家立减可同享
        if (!disablePromoToolTypeList.contains(PromoToolType.SHOP_PROMO.getCode())
                && !disablePromoToolTypeList.contains(PromoToolType.MT_SHOP_PROMO.getCode())) {
            return "与商家立减可叠加";
        }
        return "";
    }

    public static String formatTitle(int type, UnifiedCouponGroupDTO couponGroupDTO2) {
        String title;
        if (couponGroupDTO2.getPriceLimit() != null && couponGroupDTO2.getPriceLimit().signum() > 0) {
            title = "满" + couponGroupDTO2.getPriceLimit().stripTrailingZeros().toPlainString() + "可用";
        } else {
            if (type == IssueCouponOptionType.BEAUTY_EVENT.getCode()) {
                title = "无门槛丽人使用";
            } else {
                title = "无门槛使用";
            }
        }
        if (type == IssueCouponOptionType.BEAUTY_EVENT.getCode()) {
            String categoryDesc = formatCategory(couponGroupDTO2);
            if (StringUtils.isNotBlank(categoryDesc)) {
                title = title + "," + categoryDesc;
            }
        }
        return title;
    }

    private static String formatCategory(UnifiedCouponGroupDTO couponGroupDTO2) {
        Cat.logEvent("INVALID_METHOD_5", "com.dianping.pay.api.util.BeautyIssueCouponUtils.formatCategory(com.dianping.unified.coupon.manage.api.dto.UnifiedCouponGroupDTO)");
        List<UnifiedCouponCategoryDTO> categoryDTOList = couponGroupDTO2.getCategoryList();
        if (CollectionUtils.isEmpty(categoryDTOList)) {
            return "";
        }
        List<String> categoryDescList = new ArrayList<String>();
        for (UnifiedCouponCategoryDTO categoryDTO : categoryDTOList) {
            String categoryDesc = categoryMapDesc.get(categoryDTO.getCategoryId());
            if (StringUtils.isNotBlank(categoryDesc)) {
                categoryDescList.add(categoryDesc);
            }
        }
        if (CollectionUtils.isEmpty(categoryDescList)) {
            return "";
        }
        return "仅限" + StringUtils.join(categoryDescList, "、") + "品类";
    }

    public static List<Integer> toDisablePromoToolType(int paymentRuleId) {
        List<Integer> disablePromos = new ArrayList<Integer>();
        for (PromoToolType promoToolType : PromoToolType.values()) {
            PaymentRule paymentRule = PaymentRule.getByCode(promoToolType.getPaymentRuleId());
            if (paymentRule == null || !PaymentRule.canUse(paymentRuleId, paymentRule)) {
                disablePromos.add(promoToolType.getCode());
            }
        }
        return disablePromos;
    }

    public static String fillCouponTagText(List<IssueCouponActivity> activities, boolean isPlatformEvent) {
        boolean isNewUser = false;
        boolean hasCouponOtherThanTuanGou = false;
        List<String> couponTagTextList = new ArrayList<String>();
        for (IssueCouponActivity activity : activities) {
            UnifiedCouponGroupDTO couponGroup = activity.getCouponGroup();
            if (CollectionUtils.isNotEmpty(couponGroup.getUserTypeList()) &&
                    (couponGroup.getUserTypeList().contains(UserType.beautyNewUser.value) || couponGroup.getUserTypeList().contains(UserType.MTBeautyNewUser.value))) {
                isNewUser = true;
            }
            String temp = formatDealPageTag(couponGroup);
            if (StringUtils.isNotBlank(temp)) {
                couponTagTextList.add(temp);
            }
            //判断是否有不适用于团购的券
            if (CollectionUtils.isNotEmpty(couponGroup.getProductCodeList()) &&
                    (!couponGroup.getProductCodeList().contains(CouponBusiness.TUANGOU.getCode()) &&
                            !couponGroup.getProductCodeList().contains(CouponBusiness.DZ_TUANGOU.getCode()))) {
                hasCouponOtherThanTuanGou = true;
            }
        }
        String couponTagText;
        if (hasCouponOtherThanTuanGou) {
            couponTagText = StringUtils.join(couponTagTextList, ",");
        } else {
            couponTagText = "团购" + StringUtils.join(couponTagTextList, ",");
        }
        if (isPlatformEvent) {
            return (isNewUser ? "新客专享-" : "") + couponTagText + "抵用券";
        } else {
            return couponTagText;
        }
    }

    private static String formatDealPageTag(UnifiedCouponGroupDTO couponGroupDTO2) {
        StringBuilder sb = new StringBuilder();
        if (couponGroupDTO2.getPriceLimit() != null && couponGroupDTO2.getPriceLimit().signum() > 0) {
            sb.append("满");
            sb.append(couponGroupDTO2.getPriceLimit().stripTrailingZeros().toPlainString());
        } else {
            sb.append("无门槛");
        }
        sb.append("减");
        sb.append(couponGroupDTO2.getDiscountAmount().stripTrailingZeros().toPlainString());
        return sb.toString();
    }
}
