package com.dianping.pay.api.util;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.text.SimpleDateFormat;
import java.util.Date;

/**
 * <AUTHOR>
 * @date 2023/5/16
 */
public class DateUtils {

    public static final Logger logger = LoggerFactory.getLogger(DateUtils.class);

    public static String DEFAULT_SDF = "yyyy-MM-dd HH:mm:ss";

    public static String dateToStr(Date date) {
        try {
            if (date == null) {
                return null;
            }
            SimpleDateFormat simpleDateFormat = new SimpleDateFormat(DEFAULT_SDF);
            return simpleDateFormat.format(date);
        } catch (Exception e) {
            logger.error("dateToStr ex, date:{}", date, e);
            return null;
        }
    }
}
