package com.dianping.pay.api.util;

import com.alibaba.fastjson.JSON;
import com.dianping.cat.Cat;
import com.dianping.gmkt.coupon.common.api.dto.DzTimeLimitRule;
import com.dianping.gmkt.coupon.common.api.enums.CouponBusiness;
import com.dianping.gmkt.coupon.common.api.enums.CouponGroupExtraKeyEnum;
import com.dianping.gmkt.coupon.common.api.enums.CouponGroupUserRuleDimension;
import com.dianping.gmkt.coupon.common.api.enums.MerchantCouponProductType;
import com.dianping.lion.client.Lion;
import com.dianping.pay.api.beans.IssueCouponActivity;
import com.dianping.pay.api.beans.JsonLabel;
import com.dianping.pay.api.entity.issuecoupon.IssueCouponRequest;
import com.dianping.pay.api.entity.issuecoupon.IssueCouponTag;
import com.dianping.pay.api.entity.issuecoupon.NewIssueMultiCouponContext;
import com.dianping.pay.coupon.service.enums.ExpireType;
import com.dianping.pay.promo.common.utils.coupon.UnifiedCouponGroupUtils;
import com.dianping.tgc.enums.WeekDayEnum;
import com.dianping.unified.coupon.manage.api.dto.UnifiedCouponDTO;
import com.dianping.unified.coupon.manage.api.dto.UnifiedCouponGroupDTO;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang.time.DateUtils;

import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.*;

@Slf4j
public class IssueCouponUtils {

    private static final Map<Integer, String> productCodeDesc = new HashMap<Integer, String>();

    static {
        productCodeDesc.put(CouponBusiness.DZ_TUANGOU.getCode(), "团购");
        productCodeDesc.put(CouponBusiness.TUANGOU.getCode(), "团购");
        productCodeDesc.put(CouponBusiness.MO2O2PAY.getCode(), "闪惠");
        productCodeDesc.put(CouponBusiness.TAKEAWAY.getCode(), "外卖");
        productCodeDesc.put(CouponBusiness.KTV.getCode(), "预订");
        productCodeDesc.put(CouponBusiness.JOY.getCode(), "预订");
        productCodeDesc.put(CouponBusiness.EDU.getCode(), "课程");

    }

    private static final Map<Integer, String> mtProductCodeDesc = new HashMap<Integer, String>();

    static {
        mtProductCodeDesc.put(CouponBusiness.DZ_TUANGOU.getCode(), "团购");
        mtProductCodeDesc.put(CouponBusiness.TUANGOU.getCode(), "团购");
        mtProductCodeDesc.put(CouponBusiness.CARD_BUSINESS_WAIMAI.getCode(), "外卖");
        mtProductCodeDesc.put(CouponBusiness.CARD_BUSINESS_JOY.getCode(), "预订");
        mtProductCodeDesc.put(CouponBusiness.CARD_BUSINESS_KTV.getCode(), "预订");
        mtProductCodeDesc.put(CouponBusiness.CARD_BUSINESS_EDU.getCode(), "课程");

    }


    public static IssueCouponTag fillCouponTagText(UnifiedCouponGroupDTO couponGroupDTO2) {
        IssueCouponTag couponTag = new IssueCouponTag();
        couponTag.setCategory(formatTagPrefix(couponGroupDTO2));
        couponTag.setTag(formatDealPageTag(couponGroupDTO2));
        return couponTag;
    }

    public static String getUniqueKey(IssueCouponRequest issueCouponRequest) {
        return System.currentTimeMillis() + "-" + issueCouponRequest.getUserId() + "-" + issueCouponRequest.getShopIdL() + "-" + issueCouponRequest.getCouponOptionId();
    }

    public static String getUniqueKey(NewIssueMultiCouponContext context, String unitId, String shopCouponGroupId) {
        return System.currentTimeMillis() + "-" + context.getUserId() + "-" + unitId + "-" + shopCouponGroupId;
    }

    public static List<String> formatIssueCouponMsgSuccessContent(UnifiedCouponGroupDTO couponGroup) {
        List<String> result = new ArrayList<String>();
        result.add(JSON.toJSONString(Lists.newArrayList(
                new JsonLabel("券种类: ", "#999999"),
                new JsonLabel(couponGroup.getDiscountAmount().stripTrailingZeros().toPlainString(), "#E06639"),
                new JsonLabel("元抵用券", "#2C2C2C")
        )));
        result.add(JSON.toJSONString(Lists.newArrayList(
                new JsonLabel("使用规则: ", "#999999"),
                new JsonLabel(formatTitle(couponGroup), "#2C2C2C")

        )));
        result.add(JSON.toJSONString(Lists.newArrayList(
                new JsonLabel("有效期: ", "#999999"),
                new JsonLabel(formatCouponDesc(couponGroup), "#2C2C2C")
        )));
        return result;
    }

    public static String formatDealPageTag(UnifiedCouponGroupDTO couponGroupDTO2) {
        StringBuilder sb = new StringBuilder();
        if (couponGroupDTO2.getPriceLimit() != null && couponGroupDTO2.getPriceLimit().signum() > 0) {
            sb.append("满");
            sb.append(couponGroupDTO2.getPriceLimit().stripTrailingZeros().toPlainString());
        } else {
            sb.append("无门槛");
        }
        sb.append("抵");
        sb.append(couponGroupDTO2.getDiscountAmount().stripTrailingZeros().toPlainString());
        return sb.toString();
    }

    public static String formatTagPrefix(UnifiedCouponGroupDTO couponGroupDTO2) {
        if (couponGroupDTO2.getProductCodeList() != null
                && couponGroupDTO2.getProductCodeList().size() == 1) {
            return formatTapPrefixByType(couponGroupDTO2);
        } else {
            return "";
        }
    }

    private static String formatTapPrefixByType(UnifiedCouponGroupDTO couponGroupDTO2) {
        if (!UnifiedCouponGroupUtils.isMtCouponGroup(couponGroupDTO2.getCouponGroupType()) && productCodeDesc.containsKey(couponGroupDTO2.getProductCodeList().get(0))) {
            return productCodeDesc.get(couponGroupDTO2.getProductCodeList().get(0));
        }
        if (UnifiedCouponGroupUtils.isMtCouponGroup(couponGroupDTO2.getCouponGroupType()) && mtProductCodeDesc.containsKey(couponGroupDTO2.getProductCodeList().get(0))) {
            return mtProductCodeDesc.get(couponGroupDTO2.getProductCodeList().get(0));
        }
        return "";
    }

    public static String formatTitle(UnifiedCouponGroupDTO couponGroupDTO2) {
        if (couponGroupDTO2.getPriceLimit() != null && couponGroupDTO2.getPriceLimit().signum() > 0) {
            return "满" + couponGroupDTO2.getPriceLimit().stripTrailingZeros().toPlainString() + "元可用";
        } else {
            return "无门槛";
        }
    }

    public static String formatPriceLimitDesc(BigDecimal priceLimit) {
        if (priceLimit != null && priceLimit.signum() > 0) {
            return "满" + priceLimit.stripTrailingZeros().toPlainString() + "元可用";
        } else {
            return "无门槛";
        }
    }

    public static String formatSubtitle(IssueCouponActivity activity) {
        if (IssueCouponTypeUtils.allowGetCoupon(activity)) {
            return formatCouponDesc(activity.getCouponGroup());
        }
        if (IssueCouponTypeUtils.isIssued(activity)) {
            UnifiedCouponDTO unifiedCouponDTO = IssueCouponTypeUtils.getIssuedUnifiedCouponDto(activity);
            return formatTimePeriod(unifiedCouponDTO.getBeginTime(), unifiedCouponDTO.getEndTime()) + "，限指定商品";
        } else {
            return formatCouponDesc(activity.getCouponGroup());
        }
    }

    private static String formatCouponDesc(UnifiedCouponGroupDTO couponGroupDTO2) {
        return formatCouponTimePeriod(couponGroupDTO2) + "，限指定商品";
    }

    public static String formatCouponTimePeriod(UnifiedCouponGroupDTO couponGroupDTO2) {
        if (couponGroupDTO2.getExpireType() == ExpireType.FLOAT.code) {
            return "领取后" + couponGroupDTO2.getFloatDay() + "天有效";
        } else if (couponGroupDTO2.getExpireType() == ExpireType.NEXT_DAY_TAKE_EFFECT.code) {
            return "领取后第二天生效，有效期" + couponGroupDTO2.getFloatDay() + "天";
        } else {
            long remainTime = couponGroupDTO2.getEndTime().getTime() - System.currentTimeMillis();
            if (remainTime / DateUtils.MILLIS_PER_DAY > 3) {
                SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy.MM.dd");
                return dateFormat.format(couponGroupDTO2.getBeginTime()) + "-" + dateFormat.format(couponGroupDTO2.getEndTime());
            } else if (remainTime / DateUtils.MILLIS_PER_DAY > 0) {
                return remainTime / DateUtils.MILLIS_PER_DAY + "天后过期";
            } else {
                return "今天过期";
            }
        }
    }

    public static String formatDzTimeLimitRule(UnifiedCouponGroupDTO unifiedCouponGroupDTO) {
        DzTimeLimitRule dzTimeLimitRule = JsonUtils.toObject(unifiedCouponGroupDTO.getExtraInfoMap().get(CouponGroupExtraKeyEnum.dzTimeLimitRule.toString()), DzTimeLimitRule.class);
        if (dzTimeLimitRule == null) {
            return null;
        }
        String weekDesc = getWeekDesc(dzTimeLimitRule.getLimitWeeks());
        if (StringUtils.isBlank(weekDesc)) {
            return null;
        }
        return "限" + weekDesc + "的场次可用";
    }

    public static String getWeekDesc(Set<Integer> limitWeeks) {
        if (CollectionUtils.isEmpty(limitWeeks)) {
            return "";
        }
        List<String> weeks = Lists.newArrayList();
        for (Integer limitWeek : limitWeeks) {
            weeks.add(WeekDayEnum.getName(limitWeek));
        }
        return StringUtils.join(weeks, "、");
    }

    public static String formatTimePeriod(Date beginDate, Date endDate) {
        long remainTime = endDate.getTime() - System.currentTimeMillis();
        if (remainTime < 0) {
            return "已过期";
        } else if (remainTime / DateUtils.MILLIS_PER_DAY > 3) {
            SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy.MM.dd");
            return dateFormat.format(beginDate) + "-" + dateFormat.format(endDate);
        } else if (remainTime / DateUtils.MILLIS_PER_DAY > 0) {
            return remainTime / DateUtils.MILLIS_PER_DAY + "天后过期";
        } else {
            return "今天过期";
        }
    }

    public static String formatDuration(long millSeconds) {
        long seconds = millSeconds / 1000;
        long hours = seconds / 3600;
        long minutes = (seconds % 3600) / 60;
        long remainingSeconds = seconds % 60;

        return String.format("%d:%02d:%02d", hours, minutes, remainingSeconds);
    }


    public static String formatMerchantAccountCouponTimePeriod(Date endDate) {
        Cat.logEvent("INVALID_METHOD_3", "com.dianping.pay.api.util.IssueCouponUtils.formatMerchantAccountCouponTimePeriod(java.util.Date)");
        long remainTime = endDate.getTime() - System.currentTimeMillis();
        if (remainTime < 0) {
            return "已过期";
        } else if (remainTime / DateUtils.MILLIS_PER_DAY < 1) {
            return "仅剩"+formatDuration(remainTime);
        } else if (remainTime / DateUtils.MILLIS_PER_DAY >= 7) {
            SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy.MM.dd");
            return dateFormat.format(endDate) + "到期";
        } else if (remainTime / DateUtils.MILLIS_PER_DAY >= 1) {
            return "仅剩"+remainTime/DateUtils.MILLIS_PER_DAY+"天";
        } else{
            return null;
        }
    }

    public static String formatSimpleTimePeriod(Date beginDate, Date endDate) {
        Cat.logEvent("INVALID_METHOD_3", "com.dianping.pay.api.util.IssueCouponUtils.formatSimpleTimePeriod(java.util.Date,java.util.Date)");
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy.MM.dd");
        return dateFormat.format(beginDate) + "-" + dateFormat.format(endDate);
    }

    public static String formatResourceTimePeriod(Date endDate, int dayGap) {
        long remainTime = endDate.getTime() - System.currentTimeMillis();
        int timeWarningGap = Lion.getIntValue(LionConstants.COUPON_COUNT_DOWN_TIME_GAP_TOO_BIG_ALARM, 10);
        if (remainTime / DateUtils.MILLIS_PER_DAY >= timeWarningGap) {
            Cat.logEvent("CounTimeGap", "too big");
            log.warn("[formatResourceTimePeriod] time too big");
        }

        if (remainTime / DateUtils.MILLIS_PER_DAY >= dayGap) {
            return remainTime / DateUtils.MILLIS_PER_DAY + "天";
        } else {
            return null;
        }
    }

    public static boolean isEnableCoupon(boolean isUpDailyLimit, boolean isMtHongbaoIssued, List<UnifiedCouponDTO> issuedCoupon) {
        Cat.logEvent("INVALID_METHOD_2", "com.dianping.pay.api.util.IssueCouponUtils.isEnableCoupon(boolean,boolean,java.util.List)");
        if (isUpDailyLimit || isMtHongbaoIssued) {
            return false;
        }
        if (CollectionUtils.isEmpty(issuedCoupon)) {
            return true;
        }

        for (UnifiedCouponDTO issuedCouponEntity : issuedCoupon) {
            if (issuedCouponEntity.isUsed()) {
                return false;
            }
            if (issuedCouponEntity.getEndTime().compareTo(new Date()) >= 0) {
                return false;
            }
        }

        return true;
    }

    public static boolean canUseCoupon(List<UnifiedCouponDTO> issuedCoupon) {
        Cat.logEvent("INVALID_METHOD_3", "com.dianping.pay.api.util.IssueCouponUtils.canUseCoupon(java.util.List)");
        if(CollectionUtils.isEmpty(issuedCoupon)){
            return false;
        }
        for(UnifiedCouponDTO unifiedCouponDTO : issuedCoupon){
            if(unifiedCouponDTO.getEndTime().compareTo(new Date()) < 0){
                // 过期了，看下一张
                continue;
            }
            // 没过期 ，没用
            if(!unifiedCouponDTO.isUsed()){
                return true;
            }
        }
        return false;
    }

    public static boolean isNewUserCoupon(UnifiedCouponGroupDTO unifiedCouponGroupDTO) {
        if (unifiedCouponGroupDTO == null) {
            return false;
        }
        Map<String, String> extra = unifiedCouponGroupDTO.getExtraInfoMap();
        if (MapUtils.isEmpty(extra) || !extra.containsKey(CouponGroupExtraKeyEnum.userEngineDimensions.toString())) {
            return false;
        }
        List<String> userDimensions = Lists.newArrayList(extra.get(CouponGroupExtraKeyEnum.userEngineDimensions.toString()).split(","));
        for (String userDimension : userDimensions) {
            if (CouponGroupUserRuleDimension.valueOf(Integer.parseInt(userDimension)) != CouponGroupUserRuleDimension.REGULAR_USER) {
                return true;
            }
        }
        return false;
    }

    public static boolean isBrandNewUserCoupon(UnifiedCouponGroupDTO unifiedCouponGroupDTO) {
        Map<String, String> extra = unifiedCouponGroupDTO.getExtraInfoMap();
        if (MapUtils.isEmpty(extra) || !extra.containsKey(CouponGroupExtraKeyEnum.userEngineDimensions.toString())) {
            return false;
        }
        List<String> userDimensions = Lists.newArrayList(extra.get(CouponGroupExtraKeyEnum.userEngineDimensions.toString()).split(","));
        return userDimensions.contains(String.valueOf(CouponGroupUserRuleDimension.BRAND_NEW_USER.getValue()));
    }

    public static boolean isProductNewUserCoupon(UnifiedCouponGroupDTO unifiedCouponGroupDTO) {
        Map<String, String> extra = unifiedCouponGroupDTO.getExtraInfoMap();
        if (MapUtils.isEmpty(extra) || !extra.containsKey(CouponGroupExtraKeyEnum.userEngineDimensions.toString())) {
            return false;
        }
        List<String> userDimensions = Lists.newArrayList(extra.get(CouponGroupExtraKeyEnum.userEngineDimensions.toString()).split(","));
        return userDimensions.contains(String.valueOf(CouponGroupUserRuleDimension.PRODUCT_NEW_USER.getValue()));
    }

    public static boolean isDzTimeLimitRule(UnifiedCouponGroupDTO unifiedCouponGroupDTO) {
        Map<String, String> extra = unifiedCouponGroupDTO.getExtraInfoMap();
        if (MapUtils.isEmpty(extra) || !extra.containsKey(CouponGroupExtraKeyEnum.dzTimeLimitRule.toString())) {
            return false;
        }
        DzTimeLimitRule dzTimeLimitRule = JsonUtils.toObject(extra.get(CouponGroupExtraKeyEnum.dzTimeLimitRule.toString()), DzTimeLimitRule.class);
        if (dzTimeLimitRule == null || CollectionUtils.isEmpty(dzTimeLimitRule.getLimitWeeks()) || dzTimeLimitRule.getLimitWeeks().size() >= 7) {
            return false;
        }
        for (Integer limitWeek : dzTimeLimitRule.getLimitWeeks()) {
            if (limitWeek < 1 || limitWeek > 7) {
                return false;
            }
        }
        return true;
    }

    public static int getMerchantCouponProductType(UnifiedCouponGroupDTO unifiedCouponGroupDTO) {
        Map<String, String> extra = unifiedCouponGroupDTO.getExtraInfoMap();
        if (MapUtils.isEmpty(extra) || !extra.containsKey(CouponGroupExtraKeyEnum.merchantProductType.toString())) {
            return MerchantCouponProductType.ALL_PRODUCT.getValue();
        }
        return Integer.parseInt(extra.get(CouponGroupExtraKeyEnum.merchantProductType.toString()));
    }


    public static String getMerchantCouponTitle(int merchantProductType) {
        if (Objects.equals(MerchantCouponProductType.CERTAIN_LABLE.getValue(), merchantProductType)){
            return "商家品类券";
        } else if (Objects.equals(MerchantCouponProductType.CERTAIN_PRODUCT.getValue(), merchantProductType)) {
            return "商家商品券";
        } else {
            return "商家通用券";
        }
    }

    public static List<UnifiedCouponDTO> buildUnUseUnExpiredCoupons(List<UnifiedCouponDTO> unifiedCouponDTOs) {

        if (CollectionUtils.isEmpty(unifiedCouponDTOs)) {
            return unifiedCouponDTOs;
        }
        List<UnifiedCouponDTO> unUseUnExpiredCoupons = Lists.newArrayListWithExpectedSize(unifiedCouponDTOs.size());
        Date now = new Date();
        for (UnifiedCouponDTO unifiedCouponDTO : unifiedCouponDTOs) {
            if (isUnifiedCouponGroupUnUseAndUnExpire(unifiedCouponDTO, now)) {
                unUseUnExpiredCoupons.add(unifiedCouponDTO);
            }
        }
        return unUseUnExpiredCoupons;

    }

    public static boolean isUnifiedCouponGroupUnUseAndUnExpire(UnifiedCouponDTO unifiedCouponDTO, Date now) {
        if (now == null) {
            now =  new Date();
        }
        if (unifiedCouponDTO == null) {
            return false;
        }
        if (unifiedCouponDTO.isUsed()) {
            return false;
        }
        return unifiedCouponDTO.getEndTime().after(now);

    }

    public static String formatGodCouponTimePeriod(int begineTime, int endTime) {
        long now = System.currentTimeMillis();
        if (now < begineTime * 1000L) {
            SimpleDateFormat begineDateFormat = new SimpleDateFormat("yy-MM-dd");
            SimpleDateFormat endDateFormat = new SimpleDateFormat("MM-dd");
            return begineDateFormat.format(new Date(begineTime * 1000L)) + "~" + endDateFormat.format(new Date(endTime * 1000L)) + "可使用";
        } else {
            long remainTime = Long.valueOf(endTime) * 1000L - System.currentTimeMillis();
            if (remainTime < 0) {
                return "已过期";
            } else if (remainTime / DateUtils.MILLIS_PER_DAY < 1) {
                return "仅剩" + formatDuration(remainTime);
            } else if (remainTime / DateUtils.MILLIS_PER_DAY >= 7) {
                SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy.MM.dd");
                return dateFormat.format(new Date(endTime * 1000L)) + "到期";
            } else if (remainTime / DateUtils.MILLIS_PER_DAY >= 1) {
                return "仅剩" + remainTime / DateUtils.MILLIS_PER_DAY + "天";
            } else {
                return null;
            }
        }

    }
}
