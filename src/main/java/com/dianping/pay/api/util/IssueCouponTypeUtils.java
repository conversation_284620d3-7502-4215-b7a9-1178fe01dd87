package com.dianping.pay.api.util;

import com.dianping.lion.client.ConfigEvent;
import com.dianping.lion.client.ConfigListener;
import com.dianping.lion.client.Lion;
import com.dianping.pay.api.beans.IssueCouponActivity;
import com.dianping.pay.api.entity.issuecoupon.BeautyIssueCouponOption;
import com.dianping.pay.api.entity.issuecoupon.IssueCouponOption;
import com.dianping.pay.api.enums.IssueCouponOptionType;
import com.dianping.unified.coupon.manage.api.dto.UnifiedCouponDTO;
import org.apache.commons.collections.CollectionUtils;

import java.util.Date;
import java.util.List;

/**
 * 用户券是否可发判断工具类，用于判断用户券是否已领、可发等
 */
public class IssueCouponTypeUtils {

    private static boolean newIssueCouponType;

    static {
        newIssueCouponType = Lion.getBooleanValue(LionConstants.ISSUE_COUPON_NEW_TYPE_SWITCH, true);
        Lion.addConfigListener(LionConstants.ISSUE_COUPON_NEW_TYPE_SWITCH, new ConfigListener() {
            @Override
            public void configChanged(ConfigEvent configEvent) {
                newIssueCouponType = Lion.getBooleanValue(LionConstants.ISSUE_COUPON_NEW_TYPE_SWITCH, true);
            }
        });
    }

    /**
     * 是否已发放
     *
     * @param activity
     * @return
     */
    public static boolean isIssued(IssueCouponActivity activity) {
        if (activity == null) {
            return false;
        }
        return newIssueCouponType ? CollectionUtils.isNotEmpty(activity.getIssuedUnUseUnExpireCoupon()) : CollectionUtils.isNotEmpty(activity.getIssuedCoupon());
    }

    /**
     * 判断是否可以领券 （不考虑日库存等限制，只看历史领券情况）
     *
     * @param unifiedCouponDTOs
     * @return
     */
    public static boolean allowGetCoupon(List<UnifiedCouponDTO> unifiedCouponDTOs) {
        if (CollectionUtils.isEmpty(unifiedCouponDTOs)) {
            return true;
        }
        if (newIssueCouponType) {
            // 新模式逻辑：如果有未使用的在线券就不能再领了
            Date now = new Date();
            for (UnifiedCouponDTO unifiedCouponDTO : unifiedCouponDTOs) {
                if (IssueCouponUtils.isUnifiedCouponGroupUnUseAndUnExpire(unifiedCouponDTO, now)) {
                    return false;
                }
            }
            return true;
        } else {
            return IssueCouponUtils.isEnableCoupon(false, false, unifiedCouponDTOs);
        }
    }

    /**
     * 当前是否可领
     *
     * @param activity
     * @return
     */
    public static boolean allowGetCoupon(IssueCouponActivity activity) {
        if (activity == null) {
            return false;
        }
        return newIssueCouponType ? activity.isEnableNew() : activity.isEnable();
    }

    /**
     * 判断当前是否可用
     *
     * @param activity
     * @return
     */
    public static boolean canUse(IssueCouponActivity activity) {
        if (activity == null) {
            return false;
        }
        return newIssueCouponType ? activity.canUseNew() : activity.canUse();
    }

    /**
     * 是否已经使用，老模式需要判断，新模式通通为false，即没有已使用这个状态
     *
     * @param activity
     * @return
     */
    public static boolean isUsed(IssueCouponActivity activity) {
        if (activity == null) {
            return false;
        }
        return !newIssueCouponType && activity.isUsed();
    }


    public static UnifiedCouponDTO getIssuedUnifiedCouponDto(IssueCouponActivity activity) {
        if (activity == null) {
            return null;
        }
        List<UnifiedCouponDTO> tempUnifiedCouponDtos = newIssueCouponType ? activity.getIssuedUnUseUnExpireCoupon() : activity.getIssuedCoupon();
        if (CollectionUtils.isNotEmpty(tempUnifiedCouponDtos)) {
            return tempUnifiedCouponDtos.get(0);
        }
        return null;
    }

    public static IssueCouponOption getIssueCouponOption(IssueCouponActivity activity) {
        if (activity == null) {
            return null;
        }
        IssueCouponOption option = new IssueCouponOption();
        option.setId(activity.getCouponGroup().getCouponGroupId());
        option.setAmount(activity.getCouponGroup().getDiscountAmount().doubleValue());
        option.setTag(IssueCouponUtils.formatTagPrefix(activity.getCouponGroup()));
        option.setTitle(IssueCouponUtils.formatTitle(activity.getCouponGroup()));
        option.setDesc(IssueCouponUtils.formatSubtitle(activity));
        if (newIssueCouponType) {
            option.setEnable(activity.isEnableNew());
        } else {
            option.setEnable(activity.isEnable());
        }
        return option;
    }

    public static BeautyIssueCouponOption getBeautyIssueCouponOption(IssueCouponActivity activity) {
        if (activity == null) {
            return null;
        }
        BeautyIssueCouponOption option = new BeautyIssueCouponOption();
        option.setId(activity.getCouponGroup().getCouponGroupId());
        option.setCouponGroupId(activity.getCouponGroup().getCouponGroupId());
        option.setAmount(activity.getCouponGroup().getDiscountAmount().doubleValue());
        option.setTag((activity.getType() == IssueCouponOptionType.BEAUTY_EVENT.getCode() ? "丽人" : "店铺") + "团购抵用券");
        option.setTitle(BeautyIssueCouponUtils.formatTitle(activity.getType(), activity.getCouponGroup()));
        option.setDesc(IssueCouponUtils.formatSubtitle(activity));
        option.setEnable(newIssueCouponType ? activity.isEnableNew() : activity.isEnable());
        option.setIncompatible(BeautyIssueCouponUtils.formatIncompatible(activity.getCouponGroup()));
        option.setIncompatibleRule(activity.getType() == IssueCouponOptionType.BEAUTY_EVENT.getCode()
                ? BeautyIssueCouponUtils.formatIncompatibleRule(activity.getCouponGroup(), option.isIncompatible())
                : "");
        option.setUsed(newIssueCouponType && activity.isUsed());
        return option;
    }

    /**
     * 判断券是是否需要展示，可展示的定义：券当前可领 or 领取的券当前可用 & 历史上已使用
     * @param activity
     * @return
     */
    public static boolean allowDisplayActivity(IssueCouponActivity activity ) {
        return IssueCouponTypeUtils.allowGetCoupon(activity) || IssueCouponTypeUtils.canUse(activity) || IssueCouponTypeUtils.isUsed(activity);
    }

}
