package com.dianping.pay.api.util;

import com.dianping.cat.Cat;

/**
 * poiid升级工具类
 * <AUTHOR>
 * @date 2022/01/30
 */
public class PoiIdUtils {
    public static int poiIdLongToInt(long poiIdLong) {
        if (poiIdLong > Integer.MAX_VALUE) {
            Cat.logEvent("POI_UPGRADE", "EXIST_LONG_POI");
        }
        return ((int) poiIdLong);
    }

    public static long getShopIdL(long shopIdL, int shopId) {
        Cat.logEvent("INVALID_METHOD_3", "com.dianping.pay.api.util.PoiIdUtils.getShopIdL(long,int)");
        if(shopIdL > 0) {
            return shopIdL;
        } else {
            return shopId;
        }
    }

    public static Long getShopIdL(Long shopIdL, Integer shopId) {
        Cat.logEvent("INVALID_METHOD_1", "com.dianping.pay.api.util.PoiIdUtils.getShopIdL(java.lang.Long,java.lang.Integer)");
        if(shopIdL != null && shopIdL > 0) {
            return shopIdL;
        }
        if(shopId != null && shopId > 0) {
            return shopId.longValue();
        }
        return null;
    }

    public static Short convertIntegerToShort(Integer integer) {
        if(integer == null) {
            return null;
        }
        return ((short) integer.intValue());
    }
}
