package com.dianping.pay.api.util;

import com.dianping.cat.Cat;
import com.sankuai.mkt.activity.api.merchantsharecoupon.model.MerchantShareCouponExposeDTO;

import java.math.BigDecimal;
import java.util.Comparator;

public class MerchantShareCouponComparator implements Comparator<MerchantShareCouponExposeDTO> {


    @Override
    public int compare(MerchantShareCouponExposeDTO dto1, MerchantShareCouponExposeDTO dto2) {
        Cat.logEvent("INVALID_METHOD_5", "com.dianping.pay.api.util.MerchantShareCouponComparator.compare(MerchantShareCouponExposeDTO,MerchantShareCouponExposeDTO)");
        BigDecimal shareCouponAmount1 = dto1.getShareCouponAmount() == null ? new BigDecimal("0") : dto1.getShareCouponAmount();
        BigDecimal shareCouponAmount2 = dto2.getShareCouponAmount() == null ? new BigDecimal("0") : dto2.getShareCouponAmount();
        return shareCouponAmount2.compareTo(shareCouponAmount1);
    }


}
