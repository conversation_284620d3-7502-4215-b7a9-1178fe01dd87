package com.dianping.pay.api.util;

import com.dianping.lion.client.Lion;
import com.dianping.pay.api.enums.QueryShopCouponTypeEnum;
import com.dianping.pay.promo.common.enums.User;
import com.dianping.shopremote.remote.dto.ShopDTO;
import org.apache.commons.collections.CollectionUtils;

import java.util.List;

public class ReductionUtils {

    private static final int ALL_SHOP_CATEGORY = 0;

    public static boolean isNewPromoStyle(int queryShopCouponType, long productId, boolean isMt, ShopDTO shopDTO) {
        try {
            // 标品先不切新样式
            if (queryShopCouponType ==  QueryShopCouponTypeEnum.BY_SPU_PRODUCT.getCode()) {
                return false;
            }
            int userType = isMt ? User.MT.getCode() : User.DP.getCode();

            // id 灰度
            String privilegePattern = Lion.getStringValue(LionConstants.NEW_PROMO_PRODUCTID_PATTERN, ".*");
            if (!String.valueOf(productId).matches(privilegePattern)) {
                return false;
            }

            // shopType 灰度
            List<String> shopCategoryList = Lion.getList(LionConstants.NEW_PROMO_SHOP_CATEGORY_LIST, String.class);
            if (CollectionUtils.isEmpty(shopCategoryList)) {
                return false;
            }
            if (shopCategoryList.contains(userType + "_" + ALL_SHOP_CATEGORY)) {
                return true;
            }
            if (shopDTO == null) {
                return false;
            }
            int shopType = shopDTO.getShopType() != null ? shopDTO.getShopType() : 0;
            return shopCategoryList.contains(userType + "_" + shopType);
        } catch (Exception ignore) {
        }
        return false;
    }

}
