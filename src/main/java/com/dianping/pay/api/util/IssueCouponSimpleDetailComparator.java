package com.dianping.pay.api.util;

import com.dianping.pay.api.entity.issuecoupon.IssueCouponSimpleDetail;

import java.math.BigDecimal;
import java.util.Comparator;

/**
 * <AUTHOR>
 * @date 2023/8/15
 */
public class IssueCouponSimpleDetailComparator implements Comparator<IssueCouponSimpleDetail> {

    @Override
    public int compare(IssueCouponSimpleDetail obj1, IssueCouponSimpleDetail obj2) {
        // 神会员券 > 非神会员券
        if (obj1.getMmcCoupon() == 1 && obj2.getMmcCoupon() == 0) {
            return -1;
        } else if (obj1.getMmcCoupon() == 0 && obj2.getMmcCoupon() == 1) {
            return 1;
        }

        //门槛
        BigDecimal priceLimit1 = obj1.getPriceLimit() == null ? BigDecimal.ZERO : new BigDecimal(obj1.getPriceLimit());
        BigDecimal priceLimit2 = obj2.getPriceLimit() == null ? BigDecimal.ZERO : new BigDecimal(obj2.getPriceLimit());

        if (obj1.getAmount() != obj2.getAmount()) {
            return obj2.getAmount() > obj1.getAmount() ? 1 : -1;
        }

        if (priceLimit1.compareTo(priceLimit2) < 0) {
            return -1;
        } else if (priceLimit1.compareTo(priceLimit2) > 0) {
            return 1;
        }

        return 0;
    }
}
