package com.dianping.pay.api.util;

import com.dianping.cat.Cat;
import com.dianping.mobile.framework.datatypes.IMobileContext;
import com.dianping.pay.api.biz.activity.newloader.dto.PromotionRequestContext;
import com.dianping.pay.api.constants.CatEventConstants;
import com.dianping.pay.api.entity.issuecoupon.IssueCouponComponentDTO;
import com.dianping.pay.api.entity.issuecoupon.IssueCouponComponentDetail;
import org.apache.commons.collections.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.HashMap;
import java.util.Map;

public class CatUtils {

    private static Logger logger = LoggerFactory.getLogger(CatUtils.class);

    private static final String clientTp = "clientTp";
    private static final String cityId = "cityId";
    private static final String bizLine = "bizLine";
    private static final String daoZong = "daoZong";
    private static final String ios = "ios";
    private static final String android = "android";
    private static final String showMagicalMember = "showMagicalMember";
    private static final String showMMCPackage = "showMMCPackage";
    private static final String showMMCoupon = "showMMCoupon";
    private static final String USER_TYPE = "userType";
    private static final String C_TYPE = "cType";

    public static void catCouponCount(PromotionRequestContext promotionRequestContext, IssueCouponComponentDTO issueCouponComponentResp) {
        try {
            // 打点神会员券+普通券请求信息
            if (issueCouponComponentResp == null || issueCouponComponentResp.getCouponViewType() == null || issueCouponComponentResp.getCouponViewType().getCouponListInfo() == null) {
                return;
            }
            IssueCouponComponentDetail couponListInfo = issueCouponComponentResp.getCouponViewType().getCouponListInfo();
            Map<String, String> tags = new HashMap<>();
            tags.put(clientTp, promotionRequestContext.getCPlatform() == 1 ? ios : android);
            tags.put(cityId, String.valueOf(promotionRequestContext.getActualCityId()));
            tags.put(bizLine, daoZong);
            tags.put(USER_TYPE, promotionRequestContext.isMt() ? "mt" : "dp");
            tags.put(C_TYPE, getCType(promotionRequestContext));
            if (CollectionUtils.isNotEmpty(couponListInfo.getMagicMemberCouponInfo()) || CollectionUtils.isNotEmpty(couponListInfo.getMagicalMemberCouponPackageIds())) {
                tags.put(showMagicalMember, "true");
                tags.put(showMMCPackage, String.valueOf(CollectionUtils.isNotEmpty(couponListInfo.getMagicalMemberCouponPackageIds())));
                tags.put(showMMCoupon, String.valueOf(CollectionUtils.isNotEmpty(couponListInfo.getMagicMemberCouponInfo())));
            } else {
                tags.put(showMagicalMember, "false");
                tags.put(showMMCPackage, "false");
                tags.put(showMMCoupon, "false");
            }
            Cat.logMetricForCount(CatEventConstants.MAGICAL_MEMBER_COUPON_QUERY, 1, tags);
        } catch (Exception e) {
            logger.info("cat mmcCoupon info exception, request:{}, resp:{}", promotionRequestContext, issueCouponComponentResp, e);
        }
    }

    private static String getCType(PromotionRequestContext promotionRequestContext) {
        if (promotionRequestContext.isMiniProgram()) {
            return "mt_weapp";
        }
        return promotionRequestContext.getCPlatform() == 1 ? "mtiphone" : "mtandroid";
    }
}
