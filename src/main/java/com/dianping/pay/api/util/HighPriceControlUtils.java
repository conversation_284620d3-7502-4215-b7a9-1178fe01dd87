package com.dianping.pay.api.util;

import com.alibaba.fastjson.JSONObject;
import com.dianping.cat.Cat;
import com.dianping.cat.message.Event;
import com.dianping.cat.util.StringUtils;
import com.dianping.lion.client.ConfigEvent;
import com.dianping.lion.client.ConfigListener;
import com.dianping.lion.client.Lion;
import com.google.common.collect.Maps;
import java.util.List;
import java.util.Map;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.MapUtils;

@Slf4j
public class HighPriceControlUtils {
    private static Map<String, List> degradeBusinessDpCityListMap = Maps.newHashMap();
    private static Map<String, List> degradeBusinessMtCityListMap = Maps.newHashMap();

    static {
        degradeBusinessDpCityListMap = Lion.getMap(LionConstants.HIGH_PRICE_DEGRADE_BUSINESS_DP_CITY_LIST, List.class, Maps.newHashMap());
        degradeBusinessMtCityListMap = Lion.getMap(LionConstants.HIGH_PRICE_DEGRADE_BUSINESS_MT_CITY_LIST, List.class, Maps.newHashMap());
        Lion.addConfigListener(LionConstants.HIGH_PRICE_DEGRADE_BUSINESS_DP_CITY_LIST, new ConfigListener() {
            @Override
            public void configChanged(ConfigEvent configEvent) {
                degradeBusinessDpCityListMap = Lion.getMap(LionConstants.HIGH_PRICE_DEGRADE_BUSINESS_DP_CITY_LIST, List.class, Maps.newHashMap());
            }
        });
        Lion.addConfigListener(LionConstants.HIGH_PRICE_DEGRADE_BUSINESS_MT_CITY_LIST, new ConfigListener() {
            @Override
            public void configChanged(ConfigEvent configEvent) {
                degradeBusinessMtCityListMap = Lion.getMap(LionConstants.HIGH_PRICE_DEGRADE_BUSINESS_MT_CITY_LIST, List.class, Maps.newHashMap());
            }
        });
    }

    public static boolean needDegradeOfDp(String business, Integer cityId) {
        try {
            if (StringUtils.isEmpty(business) || cityId == null) {
                return false;
            }
            if (MapUtils.isNotEmpty(degradeBusinessDpCityListMap)
                && degradeBusinessDpCityListMap.containsKey(business)
                && degradeBusinessDpCityListMap.get(business).contains(cityId)) {
                Cat.logEvent("ShopPromoDegradeOfDp", business, Event.SUCCESS, null);
                return true;
            }
            Cat.logEvent("ShopPromoDegradeOfDp", business, "-1", null);
        } catch(Exception e) {
            log.error("needDegradeOfDp meet error, business:{}, cityId:{}", business, cityId, e);
        }
        return false;
    }

    public static boolean needDegradeOfMt(String business, Integer cityId) {
        try {
            if (StringUtils.isEmpty(business) || cityId == null) {
                return false;
            }
            if (MapUtils.isNotEmpty(degradeBusinessMtCityListMap)
                && degradeBusinessMtCityListMap.containsKey(business)
                && degradeBusinessMtCityListMap.get(business).contains(cityId)) {
                Cat.logEvent("ShopPromoDegradeOfMt", business, Event.SUCCESS, null);
                return true;
            }
            Cat.logEvent("ShopPromoDegradeOfMt", business, "-1", null);
        } catch(Exception e) {
            log.error("needDegradeOfMt meet error, business:{}, cityId:{}", business, cityId, e);
        }
        return false;
    }

}
