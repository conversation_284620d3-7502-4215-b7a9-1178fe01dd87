package com.dianping.pay.api.util;

import com.dianping.cat.Cat;
import com.dianping.gmkt.coupon.common.api.enums.CouponGroupExtraKeyEnum;
import com.dianping.gmkt.coupon.common.api.enums.StableUserRuleUtils;
import com.dianping.gmkt.coupon.common.api.enums.UserTypeEnum;
import com.dianping.unified.coupon.manage.api.dto.UnifiedCouponGroupDTO;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;

import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2022/3/24
 */
public class UserIdentityUtils {

    public static boolean beNewUser(UnifiedCouponGroupDTO unifiedCouponGroupDTO) {
        String userListStr = unifiedCouponGroupDTO.getExtraInfoMap().get(CouponGroupExtraKeyEnum.userEngineRules.name());
        //新配置为空，直接返回
        List<Integer> newUserTypes = Lists.newArrayList();
        if (StringUtils.isNotBlank(userListStr)) {
            String[] userTags = userListStr.split(",");
            Set<String> mbMemberUserRuleIds = getMemberUserRuleIds();
            for (String userTag : userTags) {
                if (mbMemberUserRuleIds.contains(userTag)) {
                    continue;
                }
                newUserTypes.add(NumberUtils.toInt(userTag));
            }
            return CollectionUtils.isNotEmpty(newUserTypes);
        }

        List<Integer> userTypes = unifiedCouponGroupDTO.getUserTypeList();
        if (CollectionUtils.isNotEmpty(userTypes)) {
            newUserTypes.addAll(userTypes);
        }
        return CollectionUtils.isNotEmpty(newUserTypes);
    }

    public static Set<String> getMemberUserRuleIds() {
        Cat.logEvent("INVALID_METHOD_2", "com.dianping.pay.api.util.UserIdentityUtils.getMemberUserRuleIds()");
        Set<String> mbMemberUserRuleIds = Sets.newHashSet();
        mbMemberUserRuleIds.addAll(StableUserRuleUtils.getMedicalBeautyMemberUserRuleIds(UserTypeEnum.DP.code)
                .stream()
                .map(String::valueOf)
                .collect(Collectors.toList()));
        mbMemberUserRuleIds.addAll(StableUserRuleUtils.getMedicalBeautyMemberUserRuleIds(UserTypeEnum.MT.code)
                .stream()
                .map(String::valueOf)
                .collect(Collectors.toList()));
        return mbMemberUserRuleIds;
    }
}
