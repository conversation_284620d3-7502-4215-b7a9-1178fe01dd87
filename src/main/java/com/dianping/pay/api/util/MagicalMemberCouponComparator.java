package com.dianping.pay.api.util;

import com.dianping.pay.api.entity.issuecoupon.MagicMemberCouponInfo;

import java.math.BigDecimal;
import java.util.Comparator;

public class MagicalMemberCouponComparator implements Comparator<MagicMemberCouponInfo> {
    @Override
    public int compare(MagicMemberCouponInfo obj1, MagicMemberCouponInfo obj2) {
        // 可膨神券>已膨/不可膨神券
        if (obj1.getCanInflate() == 1 && obj2.getCanInflate() == 0) {
            return -1;
        } else if (obj1.getCanInflate() == 0 && obj2.getCanInflate() == 1) {
            return 1;
        }
        // 券面额大>券面额低
        if (new BigDecimal(obj1.getCouponAmount()).compareTo(new BigDecimal(obj2.getCouponAmount())) > 0) {
            return -1;
        } else if (new BigDecimal(obj1.getCouponAmount()).compareTo(new BigDecimal(obj2.getCouponAmount())) < 0) {
            return 1;
        }
        // 券门槛低>券门槛高
        if (new BigDecimal(obj1.getThresholdDesc()).compareTo(new BigDecimal(obj2.getThresholdDesc())) < 0) {
            return -1;
        } else if (new BigDecimal(obj1.getThresholdDesc()).compareTo(new BigDecimal(obj2.getThresholdDesc())) > 0) {
            return 1;
        }
        // 券有效期近>券有效期远
        if (Long.parseLong(obj1.getValidTime()) < Long.parseLong(obj2.getValidTime())) {
            return -1;
        } else if (Long.parseLong(obj1.getValidTime()) > Long.parseLong(obj2.getValidTime())){
            return 1;
        }
        // 随机
        return 0;
    }

}
