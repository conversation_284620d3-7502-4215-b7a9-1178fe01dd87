package com.dianping.pay.api.util;

import com.dianping.gmkt.coupon.common.api.enums.CouponBusiness;
import com.dianping.lion.client.Lion;

public class CouponBusinessUtils {

    public static int getDzTuangouProductCode() {
        boolean tgSwitch = Lion.getBooleanValue(LionConstants.DZ_TUANGOU_SWITCH, false);
        return tgSwitch ? CouponBusiness.DZ_TUANGOU.getCode() : CouponBusiness.TUANGOU.getCode();
    }

}
