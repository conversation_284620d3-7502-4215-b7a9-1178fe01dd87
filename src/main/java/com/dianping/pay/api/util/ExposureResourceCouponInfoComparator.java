package com.dianping.pay.api.util;

import com.dianping.pay.api.entity.issuecoupon.ExposureResourceCouponInfo;

import java.math.BigDecimal;
import java.util.Comparator;

/**
 * <AUTHOR>
 * @date 2023/8/22
 */
public class ExposureResourceCouponInfoComparator implements Comparator<ExposureResourceCouponInfo> {

    @Override
    public int compare(ExposureResourceCouponInfo obj1, ExposureResourceCouponInfo obj2) {
        //神会员券 > 非神会员券
        if (obj1.getMmcCoupon() == 1 && obj2.getMmcCoupon() == 0) {
            return -1;
        } else if (obj1.getMmcCoupon() == 0 && obj2.getMmcCoupon() == 1) {
            return 1;
        }

        //门槛
        BigDecimal priceLimit1 = obj1.getPriceLimit() == null ? BigDecimal.ZERO : new BigDecimal(obj1.getPriceLimit());
        BigDecimal priceLimit2 = obj2.getPriceLimit() == null ? BigDecimal.ZERO : new BigDecimal(obj2.getPriceLimit());
        //面额
        BigDecimal amount1 = obj1.getAmount() == null ? BigDecimal.ZERO : new BigDecimal(obj1.getAmount());
        BigDecimal amount2 = obj2.getAmount() == null ? BigDecimal.ZERO : new BigDecimal(obj2.getAmount());

        if (amount1.compareTo(amount2) < 0) {
            return 1;
        } else if (amount1.compareTo(amount2) > 0) {
            return -1;
        }

        if (priceLimit1.compareTo(priceLimit2) < 0) {
            return -1;
        } else if (priceLimit1.compareTo(priceLimit2) > 0) {
            return 1;
        }

        return 0;
    }
}
