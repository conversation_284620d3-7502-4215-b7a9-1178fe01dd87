package com.dianping.pay.api.util;

import com.dianping.cat.Cat;
import com.dianping.gmkt.base.api.enums.CatPreferMappingEnum;
import com.dianping.gmkt.wave.api.enums.ActivityChannelEnum;
import com.google.common.collect.Maps;

import java.util.Map;

/**
 * Created by ivan on 2019/1/15.
 */
public class BusinessLineCheckUtils {
    public static Map<Integer, Integer> ACTIVITYCHANNEL_BUSINESSLINE_MAP;

    static {
        ACTIVITYCHANNEL_BUSINESSLINE_MAP = Maps.newHashMap();
        ACTIVITYCHANNEL_BUSINESSLINE_MAP.put(CatPreferMappingEnum.PET_SHOPPING.code, ActivityChannelEnum.PET_SHOPPING.getCode());
        ACTIVITYCHANNEL_BUSINESSLINE_MAP.put(CatPreferMappingEnum.WEDDING.code, ActivityChannelEnum.WEDDING.getCode());
        ACTIVITYCHANNEL_BUSINESSLINE_MAP.put(CatPreferMappingEnum.JOY.code, ActivityChannelEnum.XIUYU_CENTER.getCode());
    }

    public static boolean businessLine2ActivityChannel(int businessLine, int activityChannel) {
        Cat.logEvent("INVALID_METHOD_4", "com.dianping.pay.api.util.BusinessLineCheckUtils.businessLine2ActivityChannel(int,int)");
//        return ACTIVITYCHANNEL_BUSINESSLINE_MAP.get(businessLine) == null ? false : ACTIVITYCHANNEL_BUSINESSLINE_MAP.get(businessLine) == activityChannel;
        return ACTIVITYCHANNEL_BUSINESSLINE_MAP.get(businessLine) != null && ACTIVITYCHANNEL_BUSINESSLINE_MAP.get(businessLine) == activityChannel;
    }
}
