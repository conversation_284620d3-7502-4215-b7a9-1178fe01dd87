package com.dianping.pay.api.util;

import com.dianping.cat.Cat;
import com.dianping.pay.api.beans.PlatformCoupon;

import java.math.BigDecimal;
import java.util.Comparator;

/**
 * Created by ivan on 2019/1/1.
 */
public class PlatformCouponComparator implements Comparator<PlatformCoupon> {
    @Override
    public int compare(PlatformCoupon obj1, PlatformCoupon obj2) {
        Cat.logEvent("INVALID_METHOD_1", "com.dianping.pay.api.util.PlatformCouponComparator.compare(com.dianping.pay.api.beans.PlatformCoupon,com.dianping.pay.api.beans.PlatformCoupon)");
        //排序逻辑：优惠券券门槛>优惠券额度>限制业务类型个数
        //门槛越小靠前
        BigDecimal priceLimit1 = new BigDecimal(Double.toString(obj1.getPriceLimit()));
        BigDecimal priceLimit2 = new BigDecimal(Double.toString(obj2.getPriceLimit()));
        if (priceLimit1.compareTo(priceLimit2) < 0) {
            return -1;
        } else if (priceLimit1.compareTo(priceLimit2) > 0) {
            return 1;
        }

        //券额度小的靠前
        BigDecimal couponAmount1 = new BigDecimal(Double.toString(obj1.getAmount()));
        BigDecimal couponAmount2 = new BigDecimal(Double.toString(obj2.getAmount()));
        if (couponAmount1.compareTo(couponAmount2) < 0) {
            return -1;
        } else if (couponAmount1.compareTo(couponAmount2) > 0) {
            return 1;
        }

        return 0;
    }
}
