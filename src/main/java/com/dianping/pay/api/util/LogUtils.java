package com.dianping.pay.api.util;

import com.dianping.lion.client.ConfigEvent;
import com.dianping.lion.client.ConfigListener;
import com.dianping.lion.client.Lion;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;


@Component
public class LogUtils {

    private static final Logger logger = LoggerFactory.getLogger(LogUtils.class);

    private static boolean logSwitch;

    static {
        logSwitch = Lion.getBooleanValue(LionConstants.LOG_SWITCH, false);
        Lion.addConfigListener(LionConstants.LOG_SWITCH, new ConfigListener() {
            @Override
            public void configChanged(ConfigEvent configEvent) {
                logSwitch = Lion.getBooleanValue(LionConstants.LOG_SWITCH, false);
            }
        });
    }

    public void logInfo(String message, Object... param) {
        if (logSwitch) {
            logger.info(message, param);
        }
    }

}
