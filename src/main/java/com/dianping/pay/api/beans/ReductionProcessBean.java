package com.dianping.pay.api.beans;

import com.dianping.pay.api.entity.DiscountEvent;

import java.util.List;

/**
 * Created by xiaoxiao.li on 5/4/15.
 */
public class ReductionProcessBean {
    private List<DiscountEvent> events;
    private String promptMsg;

    public List<DiscountEvent> getEvents() {
        return events;
    }

    public void setEvents(List<DiscountEvent> events) {
        this.events = events;
    }

    public String getPromptMsg() {
        return promptMsg;
    }

    public void setPromptMsg(String promptMsg) {
        this.promptMsg = promptMsg;
    }
}
