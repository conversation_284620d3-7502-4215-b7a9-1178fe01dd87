package com.dianping.pay.api.beans;

import lombok.Data;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2023/8/10
 */
@Data
public class ShopResourcePromotionDO {

    private Long activityId;

    private Long resourceLocationId;

    private Long flowId;

    private String rowKey;

    private Integer drawStatus;

    private Boolean displayBannerFlag;

    private Map<String, String> staticMaterialContext;

    private List<ResourceMaterialsDO> resourceMaterialsDOS;

}
