package com.dianping.pay.api.beans;

import lombok.Data;

@Data
public class VoucherDetailDO {
    //券类型,0:普通券；2：折扣券
    private Integer voucherValueType;

    //券状态，0未领取，1已领取（其他如不可用状态过滤）
    private Integer issueStatus;

    //用户券可用开始时间（单位：毫秒）
    private Long useBeginTime;

    //用户券可用结束时间（单位：毫秒）
    private Long useEndTime;

    //使用限制
    private double priceLimit;

    //券面额
    private double priceAmount;

    //折扣
    private double discount;

    //抵用券名称
    private String title;

    //抵用券id
    private String couponId;
}
