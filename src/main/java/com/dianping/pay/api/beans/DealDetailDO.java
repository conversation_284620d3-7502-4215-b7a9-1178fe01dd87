package com.dianping.pay.api.beans;

import java.io.Serializable;
import lombok.Data;

@Data
public class DealDetailDO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 团单二级类目
     * */
    private String dealCategoryId;

    /**
     * 团单可使用起始时间 毫秒时间戳
     * */
    private String startTimestamp;

    /**
     * 团单可使用结束时间 毫秒时间戳
     * */
    private String endTimestamp;

    /**
     * 三方码标识，1-是，0-否
     * */
    private String thirdPartyCode;
}
