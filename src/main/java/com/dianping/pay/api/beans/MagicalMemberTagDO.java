package com.dianping.pay.api.beans;

import com.dianping.pay.api.entity.issuecoupon.MagicMemberCouponInfo;
import com.sankuai.nibmkt.promotion.api.query.model.magicalmember.response.MagicalMemberTagTextDTO;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
@TypeDoc(description = "神会员返回参数")
public class MagicalMemberTagDO implements Serializable {

    @FieldDoc(
            description = "神会员标签文案"
    )
    private MagicalMemberTagTextDTO magicalMemberTagText;

    @FieldDoc(
            description = "神会员可购买券包id"
    )
    private List<String> magicalMemberCouponPackageIds;

    @FieldDoc(
            description = "神会员券信息"
    )
    private List<MagicMemberCouponInfo> magicMemberCouponInfoList;
}
