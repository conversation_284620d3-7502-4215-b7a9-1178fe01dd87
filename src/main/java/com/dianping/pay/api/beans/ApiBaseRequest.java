package com.dianping.pay.api.beans;

import com.dianping.api.domain.ClientInfo;
import com.dianping.api.framework.Controller;
import com.dianping.mobile.framework.annotation.MobileRequest;
import com.dianping.mobile.framework.datatypes.IMobileRequest;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;

/**
 * Created by huawei.li on 15/3/13.
 */
@TypeDoc(
        description = "api基本结构"
)
@MobileRequest
public class ApiBaseRequest implements IMobileRequest {
    @FieldDoc(
            description = "userid"
    )
    @MobileRequest.Param(name = "userid", required = false)
    protected Long userId = 0L;
    @FieldDoc(
            description = "客户端信息"
    )
    @MobileRequest.Param(name = "clientinfo", required = false)
    protected ClientInfo clientInfo = new ClientInfo();

    public ApiBaseRequest() {
        super();
    }

    public ApiBaseRequest(ClientInfo clientInfo) {
        this.clientInfo = clientInfo;
    }

    public ApiBaseRequest(Controller controller) {
        this.clientInfo = new ClientInfo(controller);
        this.userId = controller.getUserID();
    }

    public Long getUserId() {
        return userId;
    }

    public void setUserId(Long userId) {
        this.userId = userId;
    }

    public ClientInfo getClientInfo() {
        return clientInfo;
    }

    public void setClientInfo(ClientInfo clientInfo) {
        this.clientInfo = clientInfo;
    }
}
