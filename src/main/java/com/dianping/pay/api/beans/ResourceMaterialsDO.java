package com.dianping.pay.api.beans;

import com.dianping.gmkt.scene.api.delivery.dto.res.CouponDetailDTO;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/8/10
 */
@Data
public class ResourceMaterialsDO {

    private Integer actionType;

    private Integer recallType;

    private Integer materialType;

    private Integer platform;

    private String materialId;

    private String prizeCouponAmount;

    private String couponUserTagDescription;

    private List<CouponDetailDTO> couponDetailDTOS;

}
