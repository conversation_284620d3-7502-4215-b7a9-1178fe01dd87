package com.dianping.pay.api.beans;

import com.dianping.cat.Cat;
import com.dianping.pay.api.util.IssueCouponUtils;
import com.dianping.pay.coupon.service.CouponIssueActivity;
import com.dianping.tgc.open.entity.ActivityDTO;
import com.dianping.tgc.open.entity.ActivitySimpleDTO;
import com.dianping.tgc.open.entity.TimeLimitRuleDTO;
import com.dianping.unified.coupon.manage.api.dto.UnifiedCouponDTO;
import com.dianping.unified.coupon.manage.api.dto.UnifiedCouponGroupDTO;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.Validate;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public class IssueCouponActivity {

    private int activityId;
    private List<String> useRules;
    private int type;
    private Date activityBeginDate;
    private Date activityEndDate;
    private int couponGroupId;
    private UnifiedCouponGroupDTO couponGroup;
    private List<UnifiedCouponDTO> issuedCoupon = new ArrayList<UnifiedCouponDTO>();
    // 已发放的，且未使用未过期的券 （对应filterType = 1）
    private List<UnifiedCouponDTO> issuedUnUseUnExpireCoupon = new ArrayList<>();

    private String icon;
    private boolean isUpDailyLimit = false;
    private boolean isMtHongbaoIssued = false;
    private String beautyConfigStr;
    private boolean isDpClient;
    private boolean isShareCoupon;
    private String couponDesc;
    private int qualificationType;
    private String bizAndTimeLimitDesc;
    private Integer couponUserType;


    public IssueCouponActivity(ActivityDTO activity, int type) throws IllegalArgumentException {
        Validate.notNull(activity, "null activity");
        Validate.notNull(activity.getCouponDTO(), "null couponDTO");
        Validate.isTrue(activity.getCouponDTO().getCouponGroupID() > 0, "invalid couponGroupId");
        Validate.isTrue(type > 0, "invalid type");

        Date now = new Date();
        Validate.isTrue(now.after(activity.getStartDate()), "activity expired");
        Validate.isTrue(now.before(activity.getEndDate()), "activity expired");
        Validate.isTrue(!activity.getCouponDTO().isUpDailyLimit(), "activity reach daily limit");

        this.activityId = activity.getActivityID();
        this.useRules = activity.getCouponDTO().getUseRules();
        this.type = type;
        this.activityBeginDate = activity.getStartDate();
        this.activityEndDate = activity.getEndDate();
        this.couponGroupId = activity.getCouponDTO().getCouponGroupID();
        this.isUpDailyLimit = activity.getCouponDTO().isUpDailyLimit();
        this.couponDesc = activity.getCouponDesc();
        this.bizAndTimeLimitDesc = activity.getBizAndTimeLimitDesc();
        this.couponUserType = activity.getCouponUserType();
    }

    public IssueCouponActivity(CouponIssueActivity activity, int type) throws IllegalArgumentException {
        Validate.notNull(activity, "null activity");
        Validate.isTrue(activity.getCouponGroupId() > 0, "invalid couponGroupId");
        Validate.isTrue(type > 0, "invalid type");

        Date now = new Date();
        Validate.isTrue(now.after(activity.getBeginDate()), "activity expired");
        Validate.isTrue(now.before(activity.getEndDate()), "activity expired");

        this.activityId = activity.getActivityId();
        this.type = type;
        this.activityBeginDate = activity.getBeginDate();
        this.activityEndDate = activity.getEndDate();
        this.couponGroupId = activity.getCouponGroupId();
    }

    public IssueCouponActivity(int couponGroupId, int type, String icon, String beautyConfigStr) throws IllegalArgumentException {
        Validate.isTrue(couponGroupId > 0, "invalid couponGroupId");
        Validate.isTrue(type > 0, "invalid type");
        this.icon = icon;
        this.couponGroupId = couponGroupId;
        this.type = type;
        this.beautyConfigStr = beautyConfigStr;
    }

    public IssueCouponActivity(int couponGroupId, int type, int qualificationType) throws IllegalArgumentException {
        Validate.isTrue(couponGroupId > 0, "invalid couponGroupId");
        Validate.isTrue(type > 0, "invalid type");
        Validate.isTrue(qualificationType > 0, "invalid qualificationType");

        this.couponGroupId = couponGroupId;
        this.type = type;
        this.qualificationType = qualificationType;
    }

    public IssueCouponActivity(ActivitySimpleDTO activitySimpleDTO, int type) throws IllegalArgumentException {
        Validate.notNull(activitySimpleDTO, "invalid couponGroupId");
        Validate.isTrue(type > 0, "invalid type");

        this.couponGroupId = activitySimpleDTO.getCouponGroupId();
        this.activityId = activitySimpleDTO.getActivityID();
        this.activityBeginDate = activitySimpleDTO.getStartDate();
        this.activityEndDate = activitySimpleDTO.getEndDate();
        this.type = type;
    }

    public int getActivityId() {
        return activityId;
    }

    public int getType() {
        return type;
    }

    public Date getActivityBeginDate() {
        Cat.logEvent("INVALID_METHOD_4", "com.dianping.pay.api.beans.IssueCouponActivity.getActivityBeginDate()");
        return activityBeginDate;
    }

    public Date getActivityEndDate() {
        Cat.logEvent("INVALID_METHOD_5", "com.dianping.pay.api.beans.IssueCouponActivity.getActivityEndDate()");
        return activityEndDate;
    }

    public int getCouponGroupId() {
        return couponGroupId;
    }

    public List<UnifiedCouponDTO> getIssuedCoupon() {
        return issuedCoupon;
    }

    public void setIssuedCoupon(List<UnifiedCouponDTO> issuedCoupon) {
        this.issuedCoupon = issuedCoupon;
    }

    public List<UnifiedCouponDTO> getIssuedUnUseUnExpireCoupon() {
        return issuedUnUseUnExpireCoupon;
    }

    public void setIssuedUnUseUnExpireCoupon(List<UnifiedCouponDTO> issuedUnUseUnExpireCoupon) {
        this.issuedUnUseUnExpireCoupon = issuedUnUseUnExpireCoupon;
    }

    public UnifiedCouponGroupDTO getCouponGroup() {
        return couponGroup;
    }

    public void setCouponGroup(UnifiedCouponGroupDTO couponGroup) {
        this.couponGroup = couponGroup;
    }

    public String getIcon() {
        return icon;
    }

    public void setIcon(String icon) {
        this.icon = icon;
    }

    public void setMtHongbaoIssued(boolean isMtHongbaoIssued) {
        Cat.logEvent("INVALID_METHOD_3", "com.dianping.pay.api.beans.IssueCouponActivity.setMtHongbaoIssued(boolean)");
        this.isMtHongbaoIssued = isMtHongbaoIssued;
    }

    /** 以下情况返回true: 未达到每日领取上线且不是美团红包，未领过或者领过该券，但是都没用过且领过的券都过期了**/
    public boolean isEnable() {
        Cat.logEvent("INVALID_METHOD_1", "com.dianping.pay.api.beans.IssueCouponActivity.isEnable()");
        return IssueCouponUtils.isEnableCoupon(isUpDailyLimit, isMtHongbaoIssued, this.issuedCoupon);
    }

    /**
     * 券是否可以领取新判断标准：只判断库存是否充足 & 是否是发放状态 & 当前没有未使用未过期的券
     * @return
     */
    public boolean isEnableNew() {
        if (isUpDailyLimit || isMtHongbaoIssued) {
            return false;
        }
        return CollectionUtils.isEmpty(issuedUnUseUnExpireCoupon);
    }

    /** 所有领过的该券中有一个没有使用就是可用的**/
    public boolean canUse(){
        Cat.logEvent("INVALID_METHOD_3", "com.dianping.pay.api.beans.IssueCouponActivity.canUse()");
        return IssueCouponUtils.canUseCoupon(this.issuedCoupon);
    }

    /**
     * 新的判断是否有可用券标准：只判断当前是否有已发放、未使用、未过期的券
     * @return
     */
    public boolean canUseNew(){
        return CollectionUtils.isNotEmpty(issuedUnUseUnExpireCoupon);
    }

    public boolean isUsed() {
        if (CollectionUtils.isEmpty(issuedCoupon)) {
            return false;
        }
        for(UnifiedCouponDTO issuedCouponEntity : issuedCoupon){
            if (issuedCouponEntity.isUsed()) {
                return true;
            }
        }
        return false;
    }

    public String getPromotionTag() {
        return IssueCouponUtils.formatDealPageTag(this.couponGroup);
    }

    public String getBeautyConfigStr() {
        return beautyConfigStr;
    }

    public void setBeautyConfigStr(String beautyConfigStr) {
        this.beautyConfigStr = beautyConfigStr;
    }

    public boolean isDpClient() {
        return isDpClient;
    }

    public void setDpClient(boolean dpClient) {
        isDpClient = dpClient;
    }

    public List<String> getUseRules() {
        return useRules;
    }

    public void setUseRules(List<String> useRules) {
        this.useRules = useRules;
    }

    public String getCouponDesc() {
        return couponDesc;
    }

    public void setCouponDesc(String couponDesc) {
        this.couponDesc = couponDesc;
    }

    public int getQualificationType() {
        return qualificationType;
    }

    public void setQualificationType(int qualificationType) {
        this.qualificationType = qualificationType;
    }

    public boolean isUpDailyLimit() {
        return isUpDailyLimit;
    }

    public void setUpDailyLimit(boolean upDailyLimit) {
        isUpDailyLimit = upDailyLimit;
    }

    public String getBizAndTimeLimitDesc() {
        return bizAndTimeLimitDesc;
    }

    public void setBizAndTimeLimitDesc(String bizAndTimeLimitDesc) {
        this.bizAndTimeLimitDesc = bizAndTimeLimitDesc;
    }

    public Integer getCouponUserType() {
        return couponUserType;
    }

    public void setCouponUserType(Integer couponUserType) {
        this.couponUserType = couponUserType;
    }

    @Override
    public String toString() {
        final StringBuilder sb = new StringBuilder("IssueCouponActivity{");
        sb.append("activityId=").append(activityId);
        sb.append(", type=").append(type);
        sb.append(", activityBeginDate=").append(activityBeginDate);
        sb.append(", activityEndDate=").append(activityEndDate);
        sb.append(", couponGroupId=").append(couponGroupId);
        sb.append(", couponGroup=").append(couponGroup);
        sb.append(", issuedCoupon=").append(issuedCoupon);
        sb.append(", issuedUnUseUnExpireCoupon=").append(issuedUnUseUnExpireCoupon);
        sb.append(", icon='").append(icon).append('\'');
        sb.append(", isUpDailyLimit=").append(isUpDailyLimit);
        sb.append(", isMtHongbaoIssued=").append(isMtHongbaoIssued);
        sb.append(", beautyConfigStr='").append(beautyConfigStr).append('\'');
        sb.append(", isDpClient=").append(isDpClient);
        sb.append(", qualificationType=").append(qualificationType);
        sb.append(", bizAndTimeLimitDesc=").append(bizAndTimeLimitDesc);
        sb.append(", couponUserType=").append(couponUserType);
        sb.append('}');
        return sb.toString();
    }


}
