package com.dianping.pay.api.beans;

import com.dianping.pay.promo.execute.service.dto.PromoExecuteDetailDTO;
import org.apache.commons.collections.CollectionUtils;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

public class PromoDTOGroup implements Comparable{

    private int promoGroupId;
    private int productCode;
    private List<Integer> mutexProductCodes = new ArrayList<Integer>();
    private List<Integer> mutexTools = new ArrayList<Integer>();
    private List<PromoExecuteDetailDTO> promoDTOList = new ArrayList<PromoExecuteDetailDTO>();
    private BigDecimal maxPromoValue = BigDecimal.ZERO;

    public int getPromoGroupId() {
        return promoGroupId;
    }

    public void setPromoGroupId(int promoGroupId) {
        this.promoGroupId = promoGroupId;
    }

    public int getProductCode() {
        return productCode;
    }

    public void setProductCode(int productCode) {
        this.productCode = productCode;
    }

    public List<Integer> getMutexProductCodes() {
        return mutexProductCodes;
    }

    public void setMutexProductCodes(List<Integer> mutexProductCodes) {
        this.mutexProductCodes = mutexProductCodes;
    }

    public List<Integer> getMutexTools() {
        return mutexTools;
    }

    public void setMutexTools(List<Integer> mutexTools) {
        this.mutexTools = mutexTools;
    }

    public List<PromoExecuteDetailDTO> getPromoDTOList() {
        return promoDTOList;
    }

    public void setPromoDTOList(List<PromoExecuteDetailDTO> promoDTOList) {
        this.promoDTOList = promoDTOList;
    }

    public BigDecimal getMaxPromoValue() {
        return maxPromoValue == null ? BigDecimal.ZERO : maxPromoValue;
    }

    public void setMaxPromoValue(BigDecimal maxPromoValue) {
        this.maxPromoValue = maxPromoValue;
    }

    @Override
    public int compareTo(Object o) {
        if (o != null && o instanceof PromoDTOGroup){
            PromoDTOGroup target = (PromoDTOGroup)o;
            if (CollectionUtils.isEmpty(this.getPromoDTOList()) || !this.getPromoDTOList().get(0).isAvailable()){
                return 1;
            }
            if (CollectionUtils.isEmpty(target.getPromoDTOList()) || !target.getPromoDTOList().get(0).isAvailable()){
                return -1;
            }
            if (this.getMaxPromoValue().compareTo(target.getMaxPromoValue()) < 0 ){
                return 1;
            }
            if (this.getMaxPromoValue().compareTo(target.getMaxPromoValue()) > 0 ){
                return -1;
            }
            if (this.getPromoDTOList().get(0).getPriority() >= target.getPromoDTOList().get(0).getPriority()){
                return -1;
            }
        }
        return 1;
    }
}
