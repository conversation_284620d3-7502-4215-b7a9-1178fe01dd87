package com.dianping.pay.api.beans;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.sankuai.nibmkt.promotion.api.query.model.magicalmember.response.MagicalMemberCouponDTO;
import com.sankuai.nibmkt.promotion.api.query.model.magicalmember.response.MagicalMemberTagTextDTO;
import lombok.Data;

import java.io.Serializable;
import java.util.List;
import java.util.Map;

@Data
public class MagicalMemberTagRawDO implements Serializable {
    @FieldDoc(
            description = "神会员标签文案"
    )
    private MagicalMemberTagTextDTO magicalMemberTagText;

    @FieldDoc(
            description = "神会员可购买券包id"
    )
    private List<String> magicalMemberCouponPackageIds;

    @FieldDoc(
            description = "神会员券未加工信息"
    )
    private List<MagicalMemberCouponDTO> magicMemberCouponInfoList;

    @FieldDoc(
            description = "扩展字段"
    )
    private Map<String,String> extendedFieldsMap;
}
