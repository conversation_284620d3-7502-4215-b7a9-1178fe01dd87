package com.dianping.pay.api.beans;

import com.dianping.api.framework.Controller;

/**
 * Created by huawei.li on 15/3/18.
 */
public class PayOrderRequest extends ApiBaseRequest {
    private int orderId;
    private int productCode;
    private String paymentTool;
    private boolean useBalance;
    private boolean confirm;
    private String mobileVerifyCode;

    public PayOrderRequest(Controller controller){
        super(controller);
        this.orderId = controller.getParametersMap().getInteger("orderid");
        this.productCode = controller.getParametersMap().getInteger("productcode");
        this.paymentTool = controller.getParametersMap().getString("paymenttool");
        this.useBalance = controller.getParametersMap().getBoolean("usebalance");
        this.confirm = controller.getParametersMap().getBoolean("continueconfirm");
        this.mobileVerifyCode = controller.getParametersMap().getString("mobileverifycode");
    }
    public int getOrderId() {
        return orderId;
    }

    public void setOrderId(int orderId) {
        this.orderId = orderId;
    }

    public int getProductCode() {
        return productCode;
    }

    public void setProductCode(int productCode) {
        this.productCode = productCode;
    }

    public String getPaymentTool() {
        return paymentTool;
    }

    public void setPaymentTool(String paymentTool) {
        this.paymentTool = paymentTool;
    }

    public boolean isUseBalance() {
        return useBalance;
    }

    public void setUseBalance(boolean useBalance) {
        this.useBalance = useBalance;
    }

    public boolean isConfirm() {
        return confirm;
    }

    public void setConfirm(boolean confirm) {
        this.confirm = confirm;
    }

    public String getMobileVerifyCode() {
        return mobileVerifyCode;
    }

    public void setMobileVerifyCode(String mobileVerifyCode) {
        this.mobileVerifyCode = mobileVerifyCode;
    }
}
