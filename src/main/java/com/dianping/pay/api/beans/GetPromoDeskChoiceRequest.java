package com.dianping.pay.api.beans;

import com.dianping.api.framework.Controller;
import com.dianping.cat.Cat;
import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang.Validate;

import java.lang.reflect.Type;
import java.math.BigDecimal;
import java.util.List;

public class GetPromoDeskChoiceRequest extends GetPromoDeskRequest {

    private BigDecimal discountAmount = BigDecimal.ZERO;
    private BigDecimal pointAmount = BigDecimal.ZERO;

    public GetPromoDeskChoiceRequest(Controller controller) {
        super(controller);
        String discountAmountString = controller.getParametersMap().getString("discountamount");
        if (StringUtils.isNotBlank(discountAmountString)) {
            this.discountAmount = new BigDecimal(discountAmountString);
        }
        String pointAmountString = controller.getParametersMap().getString("pointamount");
        if (StringUtils.isNotBlank(pointAmountString)) {
            this.pointAmount = new BigDecimal(pointAmountString);
        }
    }

    public BigDecimal getRemainOrderAmount() {
        Cat.logEvent("INVALID_METHOD_5", "com.dianping.pay.api.beans.GetPromoDeskChoiceRequest.getRemainOrderAmount()");
        BigDecimal totalAmount = BigDecimal.ZERO;
        for (PromoProduct promoProduct : this.getPromoProductList()) {
            if (promoProduct.isSelected()) {
                totalAmount = totalAmount.add(promoProduct.getPrice().multiply(new BigDecimal(promoProduct.getQuantity())));
            }
        }
        return totalAmount.subtract(discountAmount).subtract(pointAmount).max(BigDecimal.ZERO);
    }

    @Override
    public void validate(boolean forceLogin) throws IllegalArgumentException {
        Cat.logEvent("INVALID_METHOD_4", "com.dianping.pay.api.beans.GetPromoDeskChoiceRequest.validate(boolean)");
        super.validate(forceLogin);
    }

    public BigDecimal getDiscountAmount() {
        return discountAmount;
    }

    public void setDiscountAmount(BigDecimal discountAmount) {
        this.discountAmount = discountAmount;
    }

    public BigDecimal getPointAmount() {
        return pointAmount;
    }

    public void setPointAmount(BigDecimal pointAmount) {
        this.pointAmount = pointAmount;
    }
}
