package com.dianping.pay.api.beans;

import java.math.BigDecimal;

public class PromoProduct {

    private int productId;
    private int productCode;
    private BigDecimal price = BigDecimal.ZERO;
    private int quantity;
    private BigDecimal noDiscountAmount = BigDecimal.ZERO;
    private boolean selected;
    private BigDecimal originalPrice = BigDecimal.ZERO;

    public PromoProduct() {
    }

    public PromoProduct(PromoProduct promoProduct) {
        this.productId = promoProduct.productId;
        this.productCode = promoProduct.productCode;
        this.price = promoProduct.price;
        this.quantity = promoProduct.quantity;
        this.noDiscountAmount = promoProduct.noDiscountAmount;
        this.originalPrice = promoProduct.originalPrice;
        this.selected = promoProduct.selected;
    }

    public int getProductId() {
        return productId;
    }

    public void setProductId(int productId) {
        this.productId = productId;
    }

    public int getProductCode() {
        return productCode;
    }

    public void setProductCode(int productCode) {
        this.productCode = productCode;
    }

    public BigDecimal getPrice() {
        return price;
    }

    public void setPrice(BigDecimal price) {
        this.price = price;
    }

    public int getQuantity() {
        return quantity;
    }

    public void setQuantity(int quantity) {
        this.quantity = quantity;
    }

    public BigDecimal getNoDiscountAmount() {
        return noDiscountAmount;
    }

    public void setNoDiscountAmount(BigDecimal noDiscountAmount) {
        this.noDiscountAmount = noDiscountAmount;
    }

    public boolean isSelected() {
        return selected;
    }

    public void setSelected(boolean selected) {
        this.selected = selected;
    }

    public BigDecimal getOriginalPrice() {
        return originalPrice;
    }

    public void setOriginalPrice(BigDecimal originalPrice) {
        this.originalPrice = originalPrice;
    }

    public BigDecimal getTotalAmount() {
        if (price != null && quantity > 0) {
            return price.multiply(new BigDecimal(quantity));
        }
        return BigDecimal.ZERO;
    }

    public BigDecimal getOriginalTotalAmount() {
        if (originalPrice != null && quantity > 0) {
            return originalPrice.multiply(new BigDecimal(quantity));
        }
        return getTotalAmount();
    }
}
