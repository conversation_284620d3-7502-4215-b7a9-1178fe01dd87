package com.dianping.pay.api.beans;

import lombok.Data;

import java.util.List;

/**
 * Created by ivan on 2019/1/1.
 */
@Data
public class PlatformCoupon {
    /**
     * 券码Id
     * */
    private String unifiedCouponGroupId;

    /**
     * 用户券id
     * */
    private String unifiedCouponId;

    /**
     * 优惠券描述
     * */
    private String desc;

    /**
     * 优惠券面额
     * */
    private double amount;

    /**
     * 优惠券使用门槛
     * */
    private double priceLimit;

    /**
     * 优惠券名称
     * */
    private String couponTitle;

    /**
     * 优惠券领取状态  0：正常  1：已领券
     * */
    private int status;

    /**
     * 优惠券使用链接
     * */
    private String toUseUrl;

    /**
     * 平台
     * */
    private boolean isDpClient;

    /**
     * 使用品类
     */
    private List<Integer> productCodes;

    /**
     * 开始时间
     */
    private long startTime;

    /**
     * 结束时间
     */
    private long endTime;

    /**
     * 有效期结束时间类型 {@link com.dianping.pay.promo.common.enums.UnifiedCouponGroupExpireType}
     */
    private int expireType;

    /**
     * 有效期结束时间是发券后X天
     */
    private int floatDay;


    /**
     * 每张券的最大领券数量 0表示不限制
     */
    private int maxPerUser;

    /**
     * 用户已领取数量
     */
    private int couponIssued;

    /**
     * 可使用优惠券数量
     */
    private int couponAvailable;

    /**
     * 已使用优惠券数量
     */
    private int couponUsed;

    /**
     * 已过期优惠券数量
     */
    private int couponExpired;

    @Override
    public String toString() {
        final StringBuilder sb = new StringBuilder("PlatformCoupon{");
        sb.append("unifiedCouponGroupId='").append(unifiedCouponGroupId).append('\'');
        sb.append(", unifiedCouponId='").append(unifiedCouponId).append('\'');
        sb.append(", desc='").append(desc).append('\'');
        sb.append(", amount=").append(amount);
        sb.append(", priceLimit=").append(priceLimit);
        sb.append(", couponTitle='").append(couponTitle).append('\'');
        sb.append(", status=").append(status);
        sb.append(", toUseUrl='").append(toUseUrl).append('\'');
        sb.append(", isDpClient=").append(isDpClient);
        sb.append(", productCodes=").append(productCodes);
        sb.append(", startTime=").append(startTime);
        sb.append(", endTime=").append(endTime);
        sb.append(", expireType=").append(expireType);
        sb.append(", floatDay=").append(floatDay);
        sb.append(", maxPerUser=").append(maxPerUser);
        sb.append(", couponIssued=").append(couponIssued);
        sb.append(", couponAvailable=").append(couponAvailable);
        sb.append(", couponUsed=").append(couponUsed);
        sb.append(", couponExpired=").append(couponExpired);
        sb.append('}');
        return sb.toString();
    }
}
