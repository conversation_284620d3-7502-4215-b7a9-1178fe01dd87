package com.dianping.pay.api.beans;

import com.dianping.api.framework.Controller;
import org.apache.commons.lang.Validate;

public class GetPromoDeskCouponRequest extends GetPromoDeskRequest {

    private int start;
    private int limit;
    private String couponCode;

    public GetPromoDeskCouponRequest(Controller controller) {
        super(controller);
        this.start = controller.getParametersMap().getInteger("start");
        this.limit = controller.getParametersMap().getInteger("limit", 25);
        this.couponCode = controller.getParametersMap().getString("code").trim();
    }

    @Override
    public void validate(boolean forceLogin) throws IllegalArgumentException {
        super.validate(forceLogin);
    }

    public int getStart() {
        return start;
    }

    public void setStart(int start) {
        this.start = start;
    }

    public int getLimit() {
        return limit;
    }

    public void setLimit(int limit) {
        this.limit = limit;
    }

    public String getCouponCode() {
        return couponCode;
    }

    public void setCouponCode(String couponCode) {
        this.couponCode = couponCode;
    }
}
