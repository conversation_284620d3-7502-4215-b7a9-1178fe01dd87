package com.dianping.pay.api.beans;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;


@Data
public class ProductItemDetailDO implements Serializable {

    private static final long serialVersionUID = 5305886271708543784L;

    /**
     * itemId
     */
    private Long id;
    /**
     * itemName
     */
    private String name;
    /**
     * 优惠后价格-无优惠时填充商品现价
     */
    private BigDecimal price;
    /**
     * 套餐sku存在单次价格
     */
    private BigDecimal singlePrice;
    /**
     * 市场价
     */
    private BigDecimal marketPrice;
    /**
     * 预付款
     */
    private String prePayPrice;

    /**
     * 券后预付款
     */
    private String prePayCouponPrice;

    /**
     * 尾付款
     */
    private String finalPayPrice;

    /**
     * 券后尾付款
     */
    private String finalPayCouponPrice;

    /**
     * 优惠前价格、只有有优惠的时候才填充该字段
     */
    private String unPromoPrice;
    /**
     * 是否为特殊的sku
     */
    private boolean isSpecial;
    /**
     * 折扣
     */
    private BigDecimal discount;
    /**
     * 优惠信息文案-包括文案和价格，如已减xx元
     */
    private String discountPrice;
    /**
     * 多sku的次数
     */
    private String count;

    /**
     * 优惠了多少钱
     */
    private String promoAmount = "";

    /**
     * 泛商品拼接满次减优惠信息文案-包括文案和价格，如3次再减¥500
     */
    private String countPromoTag;

    /**
     * 一共优惠信息文案-包括文案和价格，如共减¥1000
     */
    private String promoAmountTag;

    /**
     * 仅第一次购买
     */
    private boolean onlyFirstBuy;

    /**
     * 使用红包后价格
     */
    private BigDecimal couponPrice;

    /**
     * 原始价格，未扣除任何优惠时的价格
     */
    private BigDecimal originalPrice;

    /**
     * 商品自促活动开始的立减后价格
     */
    private BigDecimal activityPrice;

    /**
     * 价格折扣展示
     */
    private String activityPriceDiscountDesc;

    /**
     * 该item是否出促销氛围
     */
    private boolean hasActivity;

}
