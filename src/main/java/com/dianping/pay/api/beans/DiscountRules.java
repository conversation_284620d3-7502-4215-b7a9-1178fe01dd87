package com.dianping.pay.api.beans;

import com.dianping.cat.Cat;
import com.dianping.pay.cashier.biz.enums.PaymentRule;
import org.apache.commons.collections.CollectionUtils;

import java.util.List;

/**
 * Created by huawei.li on 15/3/12.
 */
public class DiscountRules {
    private boolean canUseReduction = true;
    private boolean canUseCoupon = true;
    private boolean canUseShopCoupon = true;
    private boolean canUseHongBao = true;
    private boolean canUsePoint = true;

    public void clear() {
        canUseReduction = false;
        canUseCoupon = false;
        canUseHongBao = false;
        canUsePoint = false;
        canUseShopCoupon = false;
    }
    public void addRules(int...ruleIds){
        Cat.logEvent("INVALID_METHOD_1", "com.dianping.pay.api.beans.DiscountRules.addRules(int[])");
        for(int ruleId:ruleIds){
            canUseReduction = canUseReduction && PaymentRule.canUse(ruleId,PaymentRule.REDUCTION);
            canUseCoupon = canUseCoupon && PaymentRule.canUse(ruleId,PaymentRule.COUPON);
            canUseHongBao = canUseHongBao && PaymentRule.canUse(ruleId,PaymentRule.HONGBAO);
            canUsePoint = canUsePoint && PaymentRule.canUse(ruleId,PaymentRule.REDUCTION);
            canUseShopCoupon = canUseShopCoupon && PaymentRule.canUse(ruleId,PaymentRule.SHOPCOUPON);
        }
    }
    public void addRules(List<Integer> rules){
        Cat.logEvent("INVALID_METHOD_4", "com.dianping.pay.api.beans.DiscountRules.addRules(java.util.List)");
        canUseReduction = canUseReduction && (CollectionUtils.isEmpty(rules) || rules.contains(PaymentRule.REDUCTION.code));
        canUseCoupon = canUseCoupon && (CollectionUtils.isEmpty(rules) || rules.contains(PaymentRule.COUPON.code));
        canUseHongBao = canUseHongBao && (CollectionUtils.isEmpty(rules) || rules.contains(PaymentRule.HONGBAO.code));
        canUsePoint = canUsePoint && (CollectionUtils.isEmpty(rules) || rules.contains(PaymentRule.POINT.code));
        canUseShopCoupon = canUseShopCoupon && (CollectionUtils.isEmpty(rules) || rules.contains(PaymentRule.SHOPCOUPON.code));
    }
    public void mergeRules(List<Integer> rules){
        Cat.logEvent("INVALID_METHOD_3", "com.dianping.pay.api.beans.DiscountRules.mergeRules(java.util.List)");
        canUseReduction = canUseReduction || rules.contains(PaymentRule.REDUCTION.code);
        canUseCoupon = canUseCoupon || rules.contains(PaymentRule.COUPON.code);
        canUseHongBao = canUseHongBao || rules.contains(PaymentRule.HONGBAO.code);
        canUsePoint = canUsePoint || rules.contains(PaymentRule.POINT.code);
        canUseShopCoupon = canUseShopCoupon || rules.contains(PaymentRule.SHOPCOUPON.code);
    }
    public void mergeRules(DiscountRules rules){
        Cat.logEvent("INVALID_METHOD_1", "com.dianping.pay.api.beans.DiscountRules.mergeRules(com.dianping.pay.api.beans.DiscountRules)");
        canUseReduction = canUseReduction || rules.isCanUseReduction();
        canUseCoupon = canUseCoupon || rules.isCanUseCoupon();
        canUseHongBao = canUseHongBao || rules.isCanUseHongBao();
        canUsePoint = canUsePoint || rules.isCanUsePoint();
        canUseShopCoupon = canUseShopCoupon || rules.isCanUseShopCoupon();
    }
    public boolean isCanUseReduction() {
        return canUseReduction;
    }

    public void setCanUseReduction(boolean canUseReduction) {
        this.canUseReduction = canUseReduction;
    }

    public boolean isCanUseCoupon() {
        return canUseCoupon;
    }

    public void setCanUseCoupon(boolean canUseCoupon) {
        this.canUseCoupon = canUseCoupon;
    }

    public boolean isCanUseHongBao() {
        return canUseHongBao;
    }

    public void setCanUseHongBao(boolean canUseHongBao) {
        this.canUseHongBao = canUseHongBao;
    }

    public boolean isCanUsePoint() {
        return canUsePoint;
    }

    public void setCanUsePoint(boolean canUsePoint) {
        this.canUsePoint = canUsePoint;
    }

    public boolean isCanUseShopCoupon() {
        return canUseShopCoupon;
    }

    public void setCanUseShopCoupon(boolean canUseShopCoupon) {
        this.canUseShopCoupon = canUseShopCoupon;
    }
}
