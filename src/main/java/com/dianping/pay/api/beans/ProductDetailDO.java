package com.dianping.pay.api.beans;


import com.dianping.appkit.datatypes.RichText;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

@Data
public class ProductDetailDO implements Serializable {

    private static final long serialVersionUID = -8103582395735203748L;

    /**
     * 商品名称
     */
    private String name;

    private Integer productType;

    /**
     * 是否有合作
     */
    private Boolean isCooperation;

    private Integer spuType;

    /**
     * 目前取productItems[0]的id，可能有坑，后面注意
     */
    private Integer productItemId;

    /**
     * 优惠后价格
     */
    private BigDecimal price;
    /**
     * 市场价
     */
    private BigDecimal marketPrice;
    /**
     * 预付款
     */
    private String prePayPrice;
    /**
     * 尾付款
     */
    private String finalPayPrice;
    /**
     * 预付金说明
     */
    private String prePayPriceDesc;

    /**
     * 优惠前价格
     */
    private String unPromoPrice;

    private List<String> purchaseNoticeFixWords;
    /**
     * 销售量
     */
    private Integer saleCount;

    private String saleDisplay;

    private int productId;

    /**
     * 是否被收藏（美团APP，医美预付）
     */
    private boolean collected = false;

    /**
     * 医美预付多sku
     */
    private List<ProductItemDetailDO> productItems;


}
