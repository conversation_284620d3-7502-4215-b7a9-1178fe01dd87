package com.dianping.pay.api.beans;

import com.dianping.gmkt.scene.api.delivery.dto.res.CouponDetailDTO;
import lombok.Data;

import java.util.Map;

@Data
public class ResourcePromotionDo {

    private Long activityId;

    private Long flowId;

    private Integer actionType;

    private Integer recallType;

    private Integer materialType;

    private Integer platform;

    private String materialId;

    private String rowKey;

    private Map<String, String> staticMaterialContext;

    private Integer drawStatus;

    private CouponDetailDTO detailDTO;

    private boolean currentIssue;
}
