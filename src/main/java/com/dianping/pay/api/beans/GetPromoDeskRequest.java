package com.dianping.pay.api.beans;

import com.dianping.api.framework.Controller;
import com.dianping.cat.Cat;
import com.dianping.pay.common.enums.PayPlatform;
import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang.Validate;
import org.apache.commons.lang.math.NumberUtils;

import java.lang.reflect.Type;
import java.util.List;

public class GetPromoDeskRequest extends ApiBaseRequest {

    private static Gson gson = new Gson();
    private static Type typeOfPromoProductList = new TypeToken<List<PromoProduct>>(){}.getType();

    private List<PromoProduct> promoProductList;
    private int cityId;
    private String shopId;
    private String shopuuid;
    private String mobileNo;
    private boolean isMtClient;

    public GetPromoDeskRequest(Controller controller) {
        super(controller);
        this.cityId = controller.getParametersMap().getInteger("cityid");
        this.shopId = controller.getParametersMap().getString("shopid");
        this.shopuuid = controller.getParametersMap().getString("shopuuid");
        this.mobileNo = controller.getParametersMap().getString("mobileno");
        this.isMtClient = clientInfo.getClient().isMtClient();
        String platformString = controller.getParametersMap().getString("platform");
        if (StringUtils.isNotBlank(platformString)) {
            Integer platform = Integer.valueOf(platformString);
            this.isMtClient = platform == PayPlatform.mt_iphone_native.getCode()
                    || platform == PayPlatform.mt_android_native.getCode()
                    || platform == PayPlatform.mt_wap_m.getCode();
            this.clientInfo.setPlatform(platform);
        }
        this.promoProductList = gson.fromJson(controller.getParametersMap().getString("promoproductlist"), typeOfPromoProductList);
    }

    public List<PromoProduct> getPromoProductList() {
        return promoProductList;
    }

    public void setPromoProductList(List<PromoProduct> promoProductList) {
        this.promoProductList = promoProductList;
    }

    public int getCityId() {
        return cityId;
    }

    public void setCityId(int cityId) {
        this.cityId = cityId;
    }

    @Deprecated
    public int getShopId() {
        return NumberUtils.toInt(shopId);
    }

    @Deprecated
    public void setShopId(int shopId) {
        this.shopId = String.valueOf(shopId);
    }

    public long getShopIdL() {
        return NumberUtils.toLong(shopId);
    }

    public void setShopIdL(long shopIdL) {
        this.shopId = String.valueOf(shopIdL);
    }

    public String getShopIdStr(){
        return shopId;
    }

    public String getMobileNo() {
        return mobileNo;
    }

    public void setMobileNo(String mobileNo) {
        this.mobileNo = mobileNo;
    }

    public boolean isMtClient() {
        return isMtClient;
    }

    public void setMtClient(boolean isMtClient) {
        this.isMtClient = isMtClient;
    }

    public String getShopuuid() {
        return shopuuid;
    }

    public void setShopuuid(String shopuuid) {
        this.shopuuid = shopuuid;
    }

    public void validate(boolean forceLogin) throws IllegalArgumentException {
        if (forceLogin) {
            Validate.isTrue(this.getUserId() > 0, "invalid userId");
        }
        Validate.notEmpty(promoProductList, "invalid productList");
        Validate.isTrue(cityId > 0, "invalid cityId");
        Validate.isTrue(this.getClientInfo().getPlatform() > 0, "unknown platform");
        for (PromoProduct promoProduct : promoProductList) {
            validatePromoProduct(promoProduct);
        }
    }

    private void validatePromoProduct(PromoProduct promoProduct) throws IllegalArgumentException {
        Cat.logEvent("INVALID_METHOD_3", "com.dianping.pay.api.beans.GetPromoDeskRequest.validatePromoProduct(com.dianping.pay.api.beans.PromoProduct)");
        Validate.notNull(promoProduct, "invalid product");
        Validate.isTrue(promoProduct.getProductCode() > 0, "invalid productCode");
        Validate.isTrue(promoProduct.getProductId() > 0, "invalid productId");
    }
}
