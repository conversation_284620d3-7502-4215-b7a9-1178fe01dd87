package com.dianping.pay.api.constants;

public interface CatEventConstants {

    /**
     * 调用外部代理服务，返回结果不符合预期，例如参数缺少
     */
    String PROXY_RESULT_ILLEGAL = "PROXY_RESULT_ILLEGAL";

    /**
     * 发多张券相关监控指标
     * */
    String ISSUE_MULITY_FETCHED_QUANTY = "IssueMultiFetchedQuantityAlarm";
    String ISSUE_MULITY_COUPON_QUANTITY = "IssueMultiQuantity";
    String ISSUE_MULITY_RESULT = "IssueMultiResult";
    String ISSUE_MULITY_FAIL_DETAIL = "IssueMultiFailDetail";

    /**
     * 神会员券相关监控指标
     */
    String MAGICAL_MEMBER_COUPON_QUERY = "MagicalMemberCouponQuery";

    /**
     * 查询门店后台类目打点
     */
    String QUERY_SHOP_CATEGORY = "QueryShopCategory";

    /**
     * 活动发券结果打点
     */
    String CAMPAIGN_ISSUE_RESULT = "CampaignIssueResult";

    /**
     * 活动查询结果打点
     */
    String CAMPAIGN_RENDER_RESULT = "CampaignRenderResult";

    /**
     * 时间查询结果打点
     */
    String CAMPAIGN_TIME_RESULT = "CampaignTimeResult";

    /**
     * 活动膨胀查询结果
     */
    String CAMPAIGN_QUERY_INFLATE_RESULT = "CampaignQueryInflateResult";

    /**
     * 活动查询用户信息打点
     */
    String CAMPAIGN_RENDER_USER_INFO = "CampaignRenderUserInfo";

    /**
     * 活动查询膨胀用户信息打点
     */
    String CAMPAIGN_QUERY_INFLATE_USER_INFO = "CampaignQueryInflateUserInfo";

    /**
     * 点评id转换打点
     */
    String DP_ID_2_MT_ERROR = "dpId2MtError";

    /**
     * 点评id体系转换eventName前缀
     */
    String DP_ID_2_MT_SHOP_EVENT_NAME_PRE = "dp_2_mt_shopId";
    String DP_ID_2_MT_USER_EVENT_NAME_PRE = "dp_2_mt_userId";
    String DP_ID_2_MT_CITY_EVENT_NAME_PRE = "dp_2_mt_cityId";
    String DP_ID_2_MOBILE_EVENT_NAME_PRE = "dp_2_mobile";
    String DP_ID_2_SHOP_CATEGORY_EVENT_NAME_PRE = "dp_2_shop_category";

    /**
     * 点评poi领劵栏神劵标签控制
     */
    String DP_MMC_MAGIC_CONTROL_EVENT_NAME = "dp_mmc_magic_control";

}
