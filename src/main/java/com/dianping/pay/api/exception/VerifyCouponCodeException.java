package com.dianping.pay.api.exception;

import com.dianping.cat.Cat;

public class VerifyCouponCodeException extends RuntimeException {

    private boolean codeExists;

    public VerifyCouponCodeException(String msg, boolean codeExists) {
        super(msg);
        this.codeExists = codeExists;
    }

    public boolean isCodeExists() {
        Cat.logEvent("INVALID_METHOD_2", "com.dianping.pay.api.exception.VerifyCouponCodeException.isCodeExists()");
        return codeExists;
    }
}
