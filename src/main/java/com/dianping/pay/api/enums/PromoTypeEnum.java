package com.dianping.pay.api.enums;

import lombok.Getter;

@Getter
public enum PromoTypeEnum {
    /**
     * 1是立减，2是拼团，3是抵用券，4是反礼，5是红包分享, 6是闲时优惠
     */
    REDUCTION(1, "立减"),
    PINTUAN(2, "拼团"),
    COUPON(3, "抵用券"),
    BONUS(4, "返礼"),
    RED_PACK(5, "红包分享"),
    IDLE_HOURS(6, "闲时优惠"),
    RESOURCES_PROMOTION(7, "投放优惠"),
    BEAUTY_COMBINE_PROMO(8, "丽人新样式聚合优惠"),
    SHOPPING_CART_DISCOUNT(9, "购物车满件折"),
    FINALCIAL_COUPON_PROMO(10, "金融券"),
    GOV_CONSUME_COUPON_PROMO(11, "政府消费券"),

    ;

    private int code;
    private String desc;

    PromoTypeEnum(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }
}
