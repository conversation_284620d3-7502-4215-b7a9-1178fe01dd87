package com.dianping.pay.api.enums;

import lombok.Getter;

/**
 * <AUTHOR>
 * @date 2022/7/28
 */
@Getter
public enum MerchantUserTypeEnum {

    ALL_USER(0, "全部用户"),
    BRAND_NEW_USER(1, "品牌新客"),
    MB_MEMBER_USER(2, "医美会员"),
    PRODUCT_NEW_USER(3, "商品新客"),
    ;

    private int code;
    private String desc;

    MerchantUserTypeEnum(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static MerchantUserTypeEnum fromCode(int code) {
        for (MerchantUserTypeEnum value : values()) {
            if (value.getCode() == code) {
                return value;
            }
        }
        return null;
    }
}
