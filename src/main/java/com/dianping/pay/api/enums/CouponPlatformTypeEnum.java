package com.dianping.pay.api.enums;

public enum CouponPlatformTypeEnum {

    PC(0, "pc"),
    IPHONE(1, "iphone"),
    ANDROID(2, "android"),
    IPAD(3, "ipad"),
    WINPHONE(4, "winphone");

    private int code;
    private String desc;

    public int getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }

    CouponPlatformTypeEnum(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static CouponPlatformTypeEnum getByDesc(String desc) {
        if (desc == null) {
            return null;
        }
        for (CouponPlatformTypeEnum couponPlatformTypeEnum : CouponPlatformTypeEnum.values()) {
            if (couponPlatformTypeEnum.getDesc().equals(desc)) {
                return couponPlatformTypeEnum;
            }
        }
        return null;
    }

}
