package com.dianping.pay.api.enums;


/**
 * <AUTHOR>
 * 品牌券领取状态
 */
public enum BrandCouponIssueStatus {
    FUTURE_CAN_ISSUE(0, "未来可领"),
    CAN_ISSUE(1, "可领取"),
    HAS_ISSUED(2, "已领取"),
    SOLD_OUT(3, "已领光"),
    ;
    private int status;
    private String value;

    BrandCouponIssueStatus(int status, String value) {
        this.status = status;
        this.value = value;
    }

    public int getStatus() {
        return status;
    }

    public void setStatus(int status) {
        this.status = status;
    }

    public String getComment() {
        return value;
    }

    public void setComment(String comment) {
        this.value = comment;
    }
}
