package com.dianping.pay.api.enums;

import lombok.Getter;

/**
 * <AUTHOR>
 * @date 2022/3/24
 */
@Getter
public enum CornerMarkTypeEnum {

    NON_MARK(0, "无角标"),
    NEW_USER_MARK(1, "新客标签"),
    OTHER_MARK(2, "其他标签，展示样式根据cornerMarkUrl来决定的"),
    ;
    private int code;
    private String desc;

    CornerMarkTypeEnum(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }

}
