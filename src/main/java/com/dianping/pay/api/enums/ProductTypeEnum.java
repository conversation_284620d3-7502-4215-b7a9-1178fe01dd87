package com.dianping.pay.api.enums;

/**
 * <AUTHOR>
 * @date 2021/1/19
 */
public enum ProductTypeEnum {

    DEAL_GROUP(0, "团单"),
    SKU(1, "sku"),
    SPU_GROUP(3, "标品"),
    SPU(4, "SPU"),
    ;

    private int code;

    private String desc;

    public int getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }

    ProductTypeEnum(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static ProductTypeEnum getByCode(Integer code) {
        if (code == null) {
            return null;
        }
        for (ProductTypeEnum productTypeEnum : ProductTypeEnum.values()) {
            if (productTypeEnum.getCode() == code) {
                return productTypeEnum;
            }
        }
        return null;
    }

}
