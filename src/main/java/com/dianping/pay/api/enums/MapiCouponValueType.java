package com.dianping.pay.api.enums;

public enum MapiCouponValueType {

    COMMON_COUPON(0, "满减券"),
    DISCOUNT_COUPON(1, "折扣券"),
    ;

    private int code;
    private String desc;

    public int getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }

    MapiCouponValueType(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static MapiCouponValueType getByDesc(String desc) {
        if (desc == null) {
            return null;
        }
        for (MapiCouponValueType couponClientTypeEnum : MapiCouponValueType.values()) {
            if (couponClientTypeEnum.getDesc().equals(desc)) {
                return couponClientTypeEnum;
            }
        }
        return null;
    }

}
