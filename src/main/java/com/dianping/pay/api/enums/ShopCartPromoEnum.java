package com.dianping.pay.api.enums;

/**
 * <AUTHOR>
 * @date 2021/1/19
 */
public enum ShopCartPromoEnum {

    SHOP_COUPON(1, "商家券"),

    PLATFORM_COUPON(2, "平台券"),

    ;

    private int code;

    private String desc;

    public int getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }

    ShopCartPromoEnum(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }

}
