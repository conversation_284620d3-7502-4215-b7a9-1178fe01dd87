package com.dianping.pay.api.enums;

import lombok.Getter;

/**
 * 坐标系，默认值设置成 GCJ02（code=0），防止前端传错
 */
@Getter
public enum GPSCoordEnum {
    MARS(0, "火星坐标系，AKA GCJ-02"),
    GPS(1, "地球坐标系，AKA WGS84"),
    ;

    private final int code;
    private final String desc;

    GPSCoordEnum(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static GPSCoordEnum getByCode(Integer code) {
        if (code == null) {
            return null;
        }
        for (GPSCoordEnum gpsCoordEnum : GPSCoordEnum.values()) {
            if (gpsCoordEnum.getCode() == code) {
                return gpsCoordEnum;
            }
        }
        return null;
    }
}
