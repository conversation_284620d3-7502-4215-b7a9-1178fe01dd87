package com.dianping.pay.api.enums;

public enum ChannelProductEnum {

    //分销渠道
    ODP(0, "odp");

    private int code;
    private String desc;

    public int getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }

    ChannelProductEnum(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static ChannelProductEnum getByDesc(String desc) {
        if (desc == null) {
            return null;
        }
        for (ChannelProductEnum channelProductEnum : ChannelProductEnum.values()) {
            if (channelProductEnum.getDesc().equals(desc)) {
                return channelProductEnum;
            }
        }
        return null;
    }
}
