package com.dianping.pay.api.enums;

public enum CouponClientTypeEnum {

    WEB(0, "web"),
    NATIVE(1, "native"),
    M(2, "m"),
    WEIXIN(3, "weixin"),
    MINIPROGRAM(4, "miniprogram");;

    private int code;
    private String desc;

    public int getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }

    CouponClientTypeEnum(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static CouponClientTypeEnum getByDesc(String desc) {
        if (desc == null) {
            return null;
        }
        for (CouponClientTypeEnum couponClientTypeEnum : CouponClientTypeEnum.values()) {
            if (couponClientTypeEnum.getDesc().equals(desc)) {
                return couponClientTypeEnum;
            }
        }
        return null;
    }

}
