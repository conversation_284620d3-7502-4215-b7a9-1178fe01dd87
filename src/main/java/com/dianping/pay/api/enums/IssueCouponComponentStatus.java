package com.dianping.pay.api.enums;

/**
 * Created by drintu on 18/6/11.
 */

public enum IssueCouponComponentStatus {
    ISSUE_ENABLE(0,"可领"),
    ISSUED_MAX_PER_USER(1,"用户领券超过限制"),
    UNAVAILABLE(2,"券活动不可用"),
    USED(3,"券已使用"),
    ;
    private int status;
    private String comment;

    IssueCouponComponentStatus(int status,String comment) {
        this.status = status;
        this.comment = comment;
    }

    public int getStatus() {
        return status;
    }

    public void setStatus(int status) {
        this.status = status;
    }

    public String getComment() {
        return comment;
    }

    public void setComment(String comment) {
        this.comment = comment;
    }
}
