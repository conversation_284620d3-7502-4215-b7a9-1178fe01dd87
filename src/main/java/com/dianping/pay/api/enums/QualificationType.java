package com.dianping.pay.api.enums;

/**
 * <AUTHOR>
 * @date 2021/1/13
 */
public enum QualificationType {

    DEFAULT_ISSUE_QUALIFICATION(0, "默认领券资格，只要当前没有可用券就可以继续领取"),
    NOT_USE_ISSUE_QUALIFICATION(1, "用户此券批次下没可用的券时可领"),

    ;

    QualificationType(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    private int code;

    private String desc;

    public int getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }
}
