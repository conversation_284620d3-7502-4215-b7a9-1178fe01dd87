package com.dianping.pay.api.enums;


public enum QueryShopCouponTypeEnum {

    BY_SHOP(0, "根据门店查询商家券/立减"),

    BY_DEAL(1, "根据团购查询商家券/立减"),

    BY_SKU(2, "根据预付商品查询商家券/立减"),

    BY_DEALS(3, "批量根据团购查询商家券/立减"),

    BY_SKUS(4, "批量根据预付商品查询商家券/立减"),

    BY_SPU_PRODUCT(5, "根据标品查询商家券/立减"),

    BY_SPU(6, "根据泛商品SPU查询商家券/立减"),

    ;

    private int code;

    private String desc;

    public int getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }

    QueryShopCouponTypeEnum(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }

}
