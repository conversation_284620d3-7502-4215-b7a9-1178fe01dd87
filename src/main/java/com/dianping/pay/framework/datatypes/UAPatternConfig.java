package com.dianping.pay.framework.datatypes;

import com.dianping.pay.framework.datatypes.enums.Platform;

import java.util.regex.Pattern;

/**
 * Created by yun<PERSON> on 16/2/14.
 */
public class UAPatternConfig {
    private String ua;
    private Pattern uaPattern;
    private Platform platform;
    private int productId;
    private String productName;

    public String getUa() {
        return ua;
    }

    public void setUa(String ua) {
        this.ua = ua;
    }

    public Platform getPlatform() {
        return platform;
    }

    public void setPlatform(Platform platform) {
        this.platform = platform;
    }

    public int getProductId() {
        return productId;
    }

    public void setProductId(int productId) {
        this.productId = productId;
    }

    public String getProductName() {
        return productName;
    }

    public void setProductName(String productName) {
        this.productName = productName;
    }

    public Pattern getUaPattern() {
        return uaPattern;
    }

    public void setUaPattern(Pattern uaPattern) {
        this.uaPattern = uaPattern;
    }
}
