package com.dianping.pay.framework.datatypes.enums;

import com.dianping.cat.Cat;
import com.dianping.mobile.base.datatypes.enums.Product;

import java.io.Serializable;


/**
 * <AUTHOR>
 *
 */
public class ClientType implements Serializable {
	
	private static final long serialVersionUID = -7955532557190354500L;

	/**
	 * 主app iphone
	 */
	public static final ClientType MAINAPP_IPHONE = new ClientType(Platform.iPhone, Product.API);
	
	/**
	 * 主app android
	 */
	public static final ClientType MAINAPP_ANDROID = new ClientType(Platform.Android, Product.API);

	/**
	 * 主app H5
	 */
	public static final ClientType MAINAPP_H5 = new ClientType(Platform.H5, Product.API);

	/**
	 * 主app winphone
	 */
	public static final ClientType MAINAPP_WINPHONE = new ClientType(Platform.WinPhone, Product.API);

	/**
	 * 主app ipadhd
	 */
	public static final ClientType MAINAPP_IPADHD = new ClientType(Platform.iPadHd, Product.API);

	/**
	 * 主app win8pad
	 */
	public static final ClientType MAINAPP_WIN8PAD = new ClientType(Platform.Win8Pad, Product.API);

	/**
	 * 周边快查 iphone
	 */
	public static final ClientType YPAPP_IPHONE = new ClientType(Platform.iPhone, Product.YELLOWPAGE);

	/**
	 * 周边快查 android
	 */
	public static final ClientType YPAPP_ANDROID = new ClientType(Platform.Android, Product.YELLOWPAGE);

	/**
	 * 周边快查国际版 android
	 */
	public static final ClientType YPAPP_IN_ANDROID = new ClientType(Platform.Android, Product.YELLOWPAGE_IN);

	/**
	 * 点评团 iphone
	 */
	public static final ClientType TUANAPP_IPHONE = new ClientType(Platform.iPhone, Product.GROUPON);

	/**
	 * 点评团 android
	 */
	public static final ClientType TUANAPP_ANDROID = new ClientType(Platform.Android, Product.GROUPON);

	/**
	 * 外卖商户端app iphone
	 */
	public static final ClientType WMAPP_IPHONE = new ClientType(Platform.iPhone, Product.WAIMAI);

	/**
	 * 外卖商户端app android
	 */
	public static final ClientType WMAPP_ANDROID = new ClientType(Platform.Android, Product.WAIMAI);
	/**
	 * apollo crm app iphone
	 */
	public static final ClientType CRMAPP_IPHONE = new ClientType(Platform.iPhone, Product.APOLLOCRM);

	/**
	 * apollo crm app android
	 */
	public static final ClientType CRMAPP_ANDROID = new ClientType(Platform.Android, Product.APOLLOCRM);

	/**
	 * 商家平台app android
	 */
	public static final ClientType SHOPAPP_ANDROID = new ClientType(Platform.Android, Product.SHOP);

	/**
	 * 商家平台app iphone
	 */
	public static final ClientType SHOPAPP_IPHONE = new ClientType(Platform.iPhone, Product.SHOP);

	/**
	 * 点评管家app android
	 */
	public static final ClientType DPMERCHANT_ANDROID = new ClientType(Platform.Android, Product.DPMERCHANT);

	/**
	 * 点评管家app iphone
	 */
	public static final ClientType DPMERCHANT_IPHONE = new ClientType(Platform.iPhone, Product.DPMERCHANT);

	/**
	 * 结婚 iPhone
	 */
	public static final ClientType WED_IPHONE = new ClientType(Platform.iPhone, Product.WED);

	/**
	 * 结婚 android
	 */
	public static final ClientType WED_ANDROID = new ClientType(Platform.Android, Product.WED);

	/**
	 * 结婚商家端 iPhone
	 */
	public static final ClientType WED_MERCHANT_IPHONE = new ClientType(Platform.iPhone, Product.WED_MERCHANT);

	/**
	 * 结婚商家端 android
	 */
	public static final ClientType WED_MERCHANT_ANDROID = new ClientType(Platform.Android, Product.WED_MERCHANT);

    /**
     * 商户pos机 android
     */
    public static final ClientType MERCHANTPOS_ANDROID = new ClientType(Platform.Android, Product.MERCHANTPOS);

    /**
     * 商户pos机 iphone
     */
    public static final ClientType MERCHANTPOS_IPHONE = new ClientType(Platform.iPhone, Product.MERCHANTPOS);

    /**
     * 商家平台加里森 android
     */
    public static final ClientType SHOPAPP_GARRISON_ANDROID = new ClientType(Platform.Android, Product.SHOPGARRISON);

    /**
     * 商家微收银 android
     */
    public static final ClientType MERCHANTHOBBIT_ANDROID = new ClientType(Platform.Android, Product.MERCHANTHOBBIT);

	private int value;
	private Platform platform;
	private Product product;
	
	private String description; 

    // for deserialize
	private ClientType() { }
	public ClientType(Platform platform, Product product) {
		this.platform = platform;
		this.product = product;
		this.value = platform.value + product.value;
	}

	public int getValue() {
        Cat.logEvent("INVALID_METHOD_2", "com.dianping.pay.framework.datatypes.enums.ClientType.getValue()");
        return value;
	}

	public Platform getPlatform() {
        Cat.logEvent("INVALID_METHOD_1", "com.dianping.pay.framework.datatypes.enums.ClientType.getPlatform()");
        return platform;
	}

	public Product getProduct() {
		return product;
	}

	@Override
	public boolean equals(Object obj) {
		if (obj instanceof ClientType) {
			ClientType other = (ClientType) obj;
			return platform == other.getPlatform()
					&& product.name == other.getProduct().name;
		}
		return false;
	}
	
	@Override
	public int hashCode() {
		return (platform.hashCode() << 16) + product.hashCode();
	}
	
	@Override
	public String toString() {
		String des = description;
		if (des == null) {
			des = (platform != null ? platform.name : "unknown")
					+ "_"
					+ (product != null ? product.name : "unknown");
			description = des;
		}
		return des;
	}
	
}
