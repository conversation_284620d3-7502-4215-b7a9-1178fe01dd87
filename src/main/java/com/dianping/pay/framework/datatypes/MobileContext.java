package com.dianping.pay.framework.datatypes;

import com.dianping.cat.Cat;
import com.dianping.mobile.base.datatypes.enums.DeviceType;
import com.dianping.mobile.framework.cache.CacheContext;
import com.dianping.mobile.framework.datatypes.AppType;
import com.dianping.mobile.framework.datatypes.MobileHeader;
import com.dianping.pay.framework.datatypes.enums.ClientType;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.fileupload.FileItem;

import javax.servlet.http.HttpServletRequest;
import java.util.HashMap;
import java.util.Map;
import java.util.Set;

public class MobileContext implements IMobileContext {
    private static final String KEY_UPLOAD_FILE_APP_REQUEST = "be_data";

    private HttpServletRequest request = null;

    private CacheContext cacheContext = null;

    private Map<String, String> rawParameterMap = new HashMap<String, String>();

    public MobileContext() {

    }

    public MobileContext(HttpServletRequest request) {
        this.request = request;
    }
    /**
     * 获取原始的httpRequest
     */
    @Override
    public HttpServletRequest getRequest() {
        return request;
    }


    @Override
    public Set<String> getParameterKeys() {
        Cat.logEvent("INVALID_METHOD_4", "com.dianping.pay.framework.datatypes.MobileContext.getParameterKeys()");
        return paramtersMap.keySet();
    }
    @Override
    public String getParameter(String paramName) {
        return paramtersMap.get(paramName);
    }

    public void setParamtersMap(Map<String, String> paramtersMap) {
        this.paramtersMap = paramtersMap;
    }
    @Override
    public MobileHeader getHeader() {
        return this.header;
    }

    public void setHeader(MobileHeader header) {
        this.header = header;
    }

    public void setUserIp(String userIp) {
        this.userIp = userIp;
    }
    @Override
    public String getUserIp() {
        return userIp;
    }

    public void setClient(ClientType client) {
        this.client = client;
    }
    @Override
    public ClientType getClient() {
        return client;
    }

    public void setVersion(String version) {
        this.version = version;
    }
    @Override
    public String getVersion() {
        return version;
    }

    public void setSource(String source) {
        this.source = source;
    }
    @Override
    public String getSource() {
        return source;
    }

    public void setCellPhoneType(String cellPhoneType) {
        this.cellPhoneType = cellPhoneType;
    }
    @Override
    public String getCellPhoneType() {
        return cellPhoneType;
    }

    public void setUserAgent(String userAgent) {
        this.userAgent = userAgent;
    }

    @Override
    public String getUserAgent() {
        return this.userAgent;
    }

    @Override
    public long getUserId() {
        return userId;
    }
    @Override
    public void setUserId(long userId) {
        this.userId = userId;
    }

    @Override
    public Map<String, FileItem> getMultiParamMap() {
        return multiParamMap;
    }

    public void setMultiParamMap(Map<String, FileItem> multiParamMap) {
        this.multiParamMap = multiParamMap;
    }

    @Override
    public String getOs() {
        return os;
    }

    public void setOs(String os) {
        this.os = os;
    }

    @Override
    public String getProtocolVersion() {
        return protocolVersion;
    }
    public void setProtocolVersion(String protocolVersion) {
        this.protocolVersion = protocolVersion;
    }

    @Override
    public boolean isPost() {
        return isPost;
    }

    public void setPost(boolean isPost) {
        this.isPost = isPost;
    }

    @Override
    public String getRequestId() {
        return requestId;
    }

    public void setRequestId(String requestId) {
        this.requestId = requestId;
    }

    @Override
    public String getRequestIdReffer() {
        Cat.logEvent("INVALID_METHOD_2", "com.dianping.pay.framework.datatypes.MobileContext.getRequestIdReffer()");
        return refferRequestId;
    }

    public void setRefferRequestId(String refferRequestId) {
        Cat.logEvent("INVALID_METHOD_1", "com.dianping.pay.framework.datatypes.MobileContext.setRefferRequestId(java.lang.String)");
        this.refferRequestId = refferRequestId;
    }

    private MobileHeader header;
    private Map<String, String> paramtersMap = new HashMap<String, String>();
    private String userIp;
    private ClientType client;
    private String version;
    private String protocolVersion;
    private String source;
    private String cellPhoneType;
    private String userAgent;
    private long userId = 0L;
    private Map<String, FileItem> multiParamMap;
    private String os;
    private boolean isPost;
    private String requestId;
    private String refferRequestId;
    private String actionKey;
    private AppType appType;
    private DeviceType deviceType;

    public void setActionKey(String actionKey) {
        this.actionKey = actionKey;
    }

    @Override
    public String getActionKey() {
        return actionKey;
    }

    @Override
    public byte[] getPicFile() {
        Cat.logEvent("INVALID_METHOD_4", "com.dianping.pay.framework.datatypes.MobileContext.getPicFile()");
        FileItem file = null;
        if(multiParamMap != null && !multiParamMap.isEmpty()) {
            for(String key : multiParamMap.keySet()) {
                //客户端一般会传 key=be_data 参数数据 和 key=photo的 图片数据，暂时只支持一张图片，这里会有坑。
                if(!KEY_UPLOAD_FILE_APP_REQUEST.equalsIgnoreCase(key)) {
                    file = multiParamMap.get(key);
                    break;
                }
            }
        }
        return file == null ? null : file.get();
    }

    @Override
    public CacheContext getCacheContext() {
        return cacheContext;
    }

    public void setCacheContext(CacheContext cacheContext) {
        this.cacheContext = cacheContext;
    }

    @Override
    public AppType getAppType() {
        return appType;
    }

    public void setAppType(AppType appType) {
        this.appType = appType;
    }

    public void addRawParameterMap(String key, String value) {
        try {
            this.rawParameterMap.put(key, value);
        } catch (Exception e) {
        }

    }

    @Override
    public boolean isMtClient() {
        int mtProductStart = 200000;
        return getClient().getProduct().value() >= mtProductStart;
    }

    public String getRawParameter(String key) {
        Cat.logEvent("INVALID_METHOD_3", "com.dianping.pay.framework.datatypes.MobileContext.getRawParameter(java.lang.String)");
        return MapUtils.getString(rawParameterMap, key);
    }

    @Override
    public DeviceType getDeviceType() {
        return deviceType;
    }

    public void setDeviceType(DeviceType deviceType) {
        this.deviceType = deviceType;
    }
}
