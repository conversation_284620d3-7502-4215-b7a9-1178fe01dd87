package com.dianping.pay.framework.utils.account;

import com.dianping.account.utils.util.HttpUtils;
import com.dianping.pay.framework.datatypes.IMobileContext;
import org.apache.commons.lang.StringUtils;

import java.util.HashMap;
import java.util.Map;

public class TokenUtils {

    public static String getToken(IMobileContext context) {
        String headerToken = context.getHeader().getToken();
        if (isValidToken(headerToken)){
            return headerToken;
        }
        String urlToken = context.getParameter("token");
        if (isValidToken(urlToken)){
            return urlToken;
        }
        return null;
    }

    public static String getNewToken(IMobileContext context) {
        String headerToken = context.getHeader().getNewToken();
        if (isValidToken(headerToken)){
            return headerToken;
        }
        String urlToken = context.getParameter("newtoken");
        if (isValidToken(urlToken)){
            return urlToken;
        }
        return null;
    }

    public static String getCookieToken(IMobileContext context, String key) {
        String cookieToken = HttpUtils.getCookie(context.getRequest(), key);
        if (isValidToken(cookieToken)) {
            return cookieToken;
        }
        return null;
    }

    public static boolean isDpToken(String token) {
        // 点评token是0-f
        return isValidToken(token) && AccountStringUtils.isHexNumberRex(token);
    }

    public static boolean isMtToken(String token) {
        // 美团token是base64
        return isValidToken(token) && !AccountStringUtils.isHexNumberRex(token);
    }

    public static Map<String, String> getTokenQueryParam(IMobileContext context) {
        Map<String, String> params = new HashMap<String, String>();
        params.put("visitid", HttpUtils.getVisitId(context.getRequest()));
        return params;
    }

    private static boolean isValidToken(String token) {
        return StringUtils.isNotBlank(token) && token.length() >= 10;
    }
}
