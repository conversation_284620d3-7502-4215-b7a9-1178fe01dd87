package com.dianping.pay.framework.utils.account.parser;

import com.dianping.account.UserAccountService;
import com.dianping.account.dto.VirtualBindUserInfoDTO;
import com.dianping.account.utils.constants.CommonWebConstants;
import com.dianping.account.utils.util.HttpUtils;
import com.dianping.account.validation.AccountValidationService;
import com.dianping.account.validation.dto.AccountValidationResult;
import com.dianping.pay.framework.datatypes.IMobileContext;
import com.dianping.pay.framework.utils.PayPlatformUtils;
import com.dianping.pay.framework.utils.SpringLocator;
import com.dianping.pay.framework.utils.account.TokenUtils;
import com.dianping.pay.framework.utils.account.http.MtIsidParseUtils;
import com.dianping.pay.framework.utils.account.http.MtUserConvertUtils;
import org.apache.commons.lang.StringUtils;

public class CookieTokenParser implements IAccountParser {

    private static CookieTokenParser instance = new CookieTokenParser();

    private UserAccountService userAccountService = SpringLocator.getBean(UserAccountService.class);
    private AccountValidationService accountValidationService = SpringLocator.getBean(AccountValidationService.class);

    private CookieTokenParser() {}

    @Override
    public long parseUserId(IMobileContext context) {
        String dper = TokenUtils.getCookieToken(context, CommonWebConstants.COOKIE_KEY_DPER);
        String cookieToken = TokenUtils.getCookieToken(context, CommonWebConstants.COOKIE_KEY_TOKEN);
        String isid = TokenUtils.getCookieToken(context, "isid"); // 美团i站token
        if (PayPlatformUtils.isMtClient(context)) {
            if (TokenUtils.isDpToken(dper)) {
                AccountValidationResult response = accountValidationService.validateDper(
                        dper
                );
                if (response != null && response.isValid()) {
                    return MtUserConvertUtils.toMtUserId(response.getUserId());
                }
            } else if (TokenUtils.isMtToken(cookieToken)) {
                VirtualBindUserInfoDTO user = userAccountService.loadUserByToken(
                        cookieToken,
                        HttpUtils.getUserIp(context.getRequest()),
                        TokenUtils.getTokenQueryParam(context)
                );
                if (user != null) {
                    return user.getMtid();
                }
            } else if (StringUtils.isNotBlank(isid)) {
                return MtIsidParseUtils.parseIsid(isid);
            }
        } else {
            if (TokenUtils.isDpToken(dper)) {
                AccountValidationResult response = accountValidationService.validateDper(
                        dper
                );
                if (response != null && response.isValid()) {
                    return response.getUserId();
                }
            } else if (TokenUtils.isDpToken(cookieToken)) {
                VirtualBindUserInfoDTO user = userAccountService.loadUserByToken(
                        cookieToken,
                        HttpUtils.getUserIp(context.getRequest()),
                        TokenUtils.getTokenQueryParam(context)
                );
                if (user != null) {
                    return user.getDpid();
                }
            }
        }
        return 0;
    }

    public static IAccountParser getInstance() {
        return instance;
    }
}
