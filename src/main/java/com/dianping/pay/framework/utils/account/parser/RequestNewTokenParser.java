package com.dianping.pay.framework.utils.account.parser;

import com.dianping.account.validation.AccountValidationService;
import com.dianping.account.validation.dto.AccountValidationResult;
import com.dianping.pay.framework.datatypes.IMobileContext;
import com.dianping.pay.framework.utils.PayPlatformUtils;
import com.dianping.pay.framework.utils.SpringLocator;
import com.dianping.pay.framework.utils.account.TokenUtils;

public class RequestNewTokenParser implements IAccountParser {

    private static RequestNewTokenParser instance = new RequestNewTokenParser();
    private AccountValidationService accountValidationService = SpringLocator.getBean(AccountValidationService.class);

    private RequestNewTokenParser() {}

    @Override
    public long parseUserId(IMobileContext context) {
        String urlToken = TokenUtils.getNewToken(context);
        if (!PayPlatformUtils.isMtClient(context)) {
            if (TokenUtils.isDpToken(urlToken)) {
                AccountValidationResult response = accountValidationService.validateDper(urlToken);
                if (response != null && response.isValid()) {
                    return response.getUserId();
                }
            }
        }
        return 0;
    }

    public static IAccountParser getInstance() {
        return instance;
    }
}
