package com.dianping.pay.framework.utils;

import com.dianping.cat.Cat;
import com.dianping.pay.common.enums.PayPlatform;
import com.dianping.pay.framework.datatypes.IMobileContext;
import com.dianping.pay.framework.datatypes.enums.ClientType;
import com.dianping.pay.framework.datatypes.enums.Platform;
import com.dianping.pay.promo.common.enums.User;
import com.google.common.collect.Lists;
import org.apache.commons.lang.math.NumberUtils;

import java.util.List;

public class PayPlatformUtils {

    private static List<Integer> MT_PLATFORMS = Lists.newArrayList(
            PayPlatform.mt_pc.getCode(),
            PayPlatform.mt_wap_m.getCode(),
            PayPlatform.mt_iphone_native.getCode(),
            PayPlatform.mt_android_native.getCode(),
            PayPlatform.mt_weixin_api.getCode(),
            User.MT.getCode()
    );

    private static List<Integer> DP_APP_PLATFORMS = Lists.newArrayList(
            PayPlatform.dp_iphone_native.getCode(),
            PayPlatform.dp_android_native.getCode()
    );

    /**
     * convert to PayPlayform.code, return zero if the conversion fails.
     * @see com.dianping.pay.common.enums.PayPlatform
     */
    public static int getPayPlatform(IMobileContext context) {
        if (context.getClient() == ClientType.MAINAPP_IPHONE) {
            return PayPlatform.dp_iphone_native.getCode();
        } else if (context.getClient() == ClientType.MAINAPP_ANDROID) {
            return PayPlatform.dp_android_native.getCode();
        } else if (context.isMtClient() && context.getClient().getPlatform() == Platform.iPhone) {
            return PayPlatform.mt_iphone_native.getCode();
        } else if (context.isMtClient() && context.getClient().getPlatform() == Platform.Android) {
            return PayPlatform.mt_android_native.getCode();
        } else if (context.getClient() == ClientType.MAINAPP_H5) {
            return NumberUtils.toInt(context.getParameter("platform"));
        } else {
            return 0;
        }
    }

    public static boolean isMtClient(int payPlatform) {
        return MT_PLATFORMS.contains(payPlatform);
    }

    public static boolean isMtClient(IMobileContext mobileContext) {
        return isMtClient(getPayPlatform(mobileContext));
    }

    public static boolean isDpApp(int payPlatform) {
        Cat.logEvent("INVALID_METHOD_4", "com.dianping.pay.framework.utils.PayPlatformUtils.isDpApp(int)");
        return DP_APP_PLATFORMS.contains(payPlatform);
    }
}
