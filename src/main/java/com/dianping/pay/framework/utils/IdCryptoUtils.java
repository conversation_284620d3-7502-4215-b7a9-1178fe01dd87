package com.dianping.pay.framework.utils;

import com.dianping.api.util.LionQueryUtils;
import com.dianping.pay.api.entity.issuecoupon.IssuecouponcomponentRequest;
import com.dianping.pay.api.entity.issuecoupon.ProductcouponpromoRequest;
import com.dianping.pay.api.entity.issuecoupon.UnifiedissuecouponRequest;
import com.dianping.pay.api.entity.issuecoupon.UnifiedissuemulticouponRequest;
import com.dianping.pay.api.util.LionConstants;
import com.sankuai.meituan.common.json.JSONUtil;
import com.sankuai.sig.botdefender.core.crypt.dto.DecryptRequest;
import com.sankuai.sig.botdefender.core.crypt.dto.DecryptResult;
import com.sankuai.sig.botdefender.core.crypt.dto.EncryptRequest;
import com.sankuai.sig.botdefender.core.crypt.dto.EncryptResult;
import com.sankuai.sig.botdefender.core.crypt.utils.SigCryptUtils;
import com.sankuai.sig.botdefender.core.enums.EnumsLinkExtractType;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

/**
 * <AUTHOR>
 * @Created in 2024/10/18 10:08
 * poiId加密解密
 */
@Slf4j
public class IdCryptoUtils {

    public static void decryptByRequest(UnifiedissuecouponRequest request){
        if (!LionQueryUtils.POI_ID_CRYPTO_SWITCH){
            return;
        }
        try {
            String shopIdStringEncrypt = request.getShopidstringencrypt();
            if (StringUtils.isNotBlank(shopIdStringEncrypt)){
                //解密结果和明文不一致时会返回明文
                String decrypt = decrypt(request.getShopidStr(), shopIdStringEncrypt);
                request.setShopidstring(decrypt);
            }
            String shopUuidEncrypt = request.getShopuuidencrypt();
            if (StringUtils.isNotBlank(shopUuidEncrypt)){
                String decrypt = decrypt(request.getShopuuid(), shopUuidEncrypt);
                request.setShopuuid(decrypt);
            }
        } catch (Exception e) {
            log.error("[decryptByRequest UnifiedissuecouponRequest] req{}", JSONUtil.toJSONString(request), e);
        }
    }

    public static void decryptByRequest(ProductcouponpromoRequest request){
        if (!LionQueryUtils.POI_ID_CRYPTO_SWITCH){
            return;
        }
        try {
            String shopIdStringEncrypt = request.getShopidstringencrypt();
            if (StringUtils.isNotBlank(shopIdStringEncrypt)){
                //解密结果和明文不一致时会返回明文
                String decrypt = decrypt(request.getShopidstring(), shopIdStringEncrypt);
                request.setShopidstring(decrypt);
            }
            String shopUuidEncrypt = request.getShopuuidencrypt();
            if (StringUtils.isNotBlank(shopUuidEncrypt)){
                String decrypt = decrypt(request.getShopuuid(), shopUuidEncrypt);
                request.setShopuuid(decrypt);
            }

        } catch (Exception e) {
            log.error("[decryptByRequest ProductcouponpromoRequest] req:{}", JSONUtil.toJSONString(request), e);
        }
    }

    public static void decryptByRequest(UnifiedissuemulticouponRequest request){
        if (!LionQueryUtils.POI_ID_CRYPTO_SWITCH){
            return;
        }
        try {
            String shopIdStringEncrypt = request.getShopidencrypt();
            if (StringUtils.isNotBlank(shopIdStringEncrypt)){
                //解密结果和明文不一致时会返回明文
                String decrypt = decrypt(request.getShopidStr(), shopIdStringEncrypt);
                request.setShopid(decrypt);
            }
            String shopUuidEncrypt = request.getShopuuidencrypt();
            if (StringUtils.isNotBlank(shopUuidEncrypt)){
                String decrypt = decrypt(request.getShopuuid(), shopUuidEncrypt);
                request.setShopuuid(decrypt);
            }

        } catch (Exception e) {
            log.error("[decryptByRequest UnifiedissuemulticouponRequest] req:{}", JSONUtil.toJSONString(request), e);
        }
    }

    public static void decryptByRequest(IssuecouponcomponentRequest request){
        if (!LionQueryUtils.POI_ID_CRYPTO_SWITCH){
            return;
        }
        try {
            String shopIdStringEncrypt = request.getShopidstringencrypt();
            if (StringUtils.isNotBlank(shopIdStringEncrypt)){
                //解密结果和明文不一致时会返回明文
                String decrypt = decrypt(request.getShopidstring(), shopIdStringEncrypt);
                request.setShopidstring(decrypt);
            }
            String shopUuidEncrypt = request.getShopuuidencrypt();
            if (StringUtils.isNotBlank(shopUuidEncrypt)){
                String decrypt = decrypt(request.getShopuuid(), shopUuidEncrypt);
                request.setShopuuid(decrypt);
            }
        } catch (Exception e) {
            log.error("[decryptByRequest UnifiedissuecouponRequest] req{}", JSONUtil.toJSONString(request), e);
        }
    }

    /**
     * 生成调链的加密参数
     * @param extractType 跳链参数类型
     * @param assetId 需加密id
     * @param fieldName 需加密字段名
     * @return
     */
    public static String genLinkEncryptParam(EnumsLinkExtractType extractType, Object assetId, String fieldName){
        if (!LionQueryUtils.POI_ID_CRYPTO_SWITCH){
            return StringUtils.EMPTY;
        }
        //SigCryptUtils.encryptLink()
        String encryptId = assetId != null ? encrypt(assetId.toString()) : StringUtils.EMPTY;
        switch (extractType){
            case queries:
                return String.format("&%sEncrypt=%s", fieldName, encryptId);
            case path:
                //path入参目前默认使用字段: poiIdEncrypt
                return "?poiIdEncrypt=" + encryptId;
            default:
                break;
        }
        return StringUtils.EMPTY;
    }

    /**
     * 根据解密请求解密资产ID
     * @param assetId 传入明文（可为Null）
     * @param assetIdEncrypt 传入密文
     * @return
     */
    private static String decrypt(String assetId, String assetIdEncrypt){
        DecryptRequest decryptRequest = DecryptRequest.builder()
                .assetId(assetId)
                .encryptAssetId(assetIdEncrypt)
                .build();
        DecryptResult decryptResult = SigCryptUtils.decryptByRequest(decryptRequest);
        return decryptResult.getAssetId();
    }

    private static String encrypt(String assetId){
        if (StringUtils.isEmpty(assetId)){
            return StringUtils.EMPTY;
        }
        EncryptRequest encryptRequest =
                EncryptRequest.builder()
                        .assetId(assetId)
                        .param("{\"ak\":\""+ LionConstants.APP_KEY+"\"}").build();
        EncryptResult encryptResult = SigCryptUtils.encryptByRequest(encryptRequest);
        return encryptResult.getEncryptAssetId();
    }
}
