package com.dianping.pay.framework.utils;

import com.dianping.combiz.spring.util.PropertiesLoaderSupportUtils;
import com.sankuai.meituan.security.AuthUtil;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.httpclient.HttpStatus;
import org.apache.commons.lang.StringUtils;
import org.apache.http.HttpEntity;
import org.apache.http.HttpResponse;
import org.apache.http.NameValuePair;
import org.apache.http.client.entity.UrlEncodedFormEntity;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.conn.ClientConnectionManager;
import org.apache.http.conn.params.ConnManagerParams;
import org.apache.http.conn.params.ConnPerRoute;
import org.apache.http.conn.routing.HttpRoute;
import org.apache.http.conn.scheme.PlainSocketFactory;
import org.apache.http.conn.scheme.Scheme;
import org.apache.http.conn.scheme.SchemeRegistry;
import org.apache.http.conn.ssl.SSLSocketFactory;
import org.apache.http.impl.client.DefaultHttpClient;
import org.apache.http.impl.conn.tsccm.ThreadSafeClientConnManager;
import org.apache.http.message.BasicNameValuePair;
import org.apache.http.params.BasicHttpParams;
import org.apache.http.params.HttpConnectionParams;
import org.apache.http.params.HttpParams;
import org.apache.http.util.EntityUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.IOException;
import java.net.URI;
import java.text.SimpleDateFormat;
import java.util.*;

public class HttpClient {

    private static final Logger LOGGER = LoggerFactory.getLogger(HttpClient.class);
    static final String CONTENT_TYPE_NAME = "Content-Type";
    static final String AUZ_NAME = "Authorization";
    static final String DATE_NAME = "Date";
    protected static org.apache.http.client.HttpClient ACCOUNT_HTTP_CLIENT;

    static {
        HttpParams params = new BasicHttpParams();
        // Http Connection Params
        HttpConnectionParams.setConnectionTimeout(params, 1500);    // 1.5s
        HttpConnectionParams.setSoTimeout(params, 2500);  // 2.5s
        HttpConnectionParams.setTcpNoDelay(params, true);
        HttpConnectionParams.setStaleCheckingEnabled(params, true);
        // Http Connection Manager Params
        ConnManagerParams.setTimeout(params, 1500); // 1.5s
        ConnManagerParams.setMaxTotalConnections(params, 200);
        ConnManagerParams.setMaxConnectionsPerRoute(params, new ConnPerRoute() {
            @Override
            public int getMaxForRoute(HttpRoute route) { return 200; } // 只有美团独用，= MaxTotalConnections
        });

        SchemeRegistry registry = new SchemeRegistry();
        registry.register(new Scheme("http", PlainSocketFactory.getSocketFactory(), 80));
        registry.register(new Scheme("https", SSLSocketFactory.getSocketFactory(), 443));
        ClientConnectionManager connectionManager = new ThreadSafeClientConnManager(params, registry);
        ACCOUNT_HTTP_CLIENT = new DefaultHttpClient(connectionManager, params);
    }

    private static final String CHARSET = "UTF-8";
    private static final String AUZ_CLIENT_ID = "dp-paycenter";
    private static final String CONTENT_TYPE_VALUE = "application/x-www-form-urlencoded";
    private static final String GET_METHOD = "GET";
    private static final String POST_METHOD = "POST";

    public static String executePost(Map<String, String> param, String url, String auzUri) {
        try {
            HttpPost httpPost = buildHttpPostReq(param, url, auzUri);
            if (httpPost != null) {
                return resolveHttpResponse(ACCOUNT_HTTP_CLIENT.execute(httpPost), url);
            }
        } catch (Exception e) {
            LOGGER.error("executePost error", e);
        }
        return null;
    }

    public static String executeGet(Map<String, String> param, String url, String auzUri) {
        try {
            HttpGet httpGet = buildHttpGetReq(param, url, auzUri);
            if (httpGet != null) {
                return resolveHttpResponse(ACCOUNT_HTTP_CLIENT.execute(httpGet), url);
            }
        } catch (Exception e) {
            LOGGER.error("executePost error", e);
        }
        return null;
    }

    private static HttpPost buildHttpPostReq(Map<String, String> param, String url, String auzUri) {
        HttpPost httpPost = null;
        try{
            List<NameValuePair> nvps = new ArrayList<NameValuePair>(param.size() + 1);
            for(Map.Entry<String, String> entry : param.entrySet()) {
                nvps.add(new BasicNameValuePair(entry.getKey(), entry.getValue()));
            }
            httpPost = new HttpPost(url);
            httpPost.setEntity(new UrlEncodedFormEntity(nvps, CHARSET));
            if (StringUtils.isNotBlank(auzUri)) {
                String date = getDate();
                httpPost.setHeader(CONTENT_TYPE_NAME, CONTENT_TYPE_VALUE);
                httpPost.setHeader(AUZ_NAME, AuthUtil.getAuthorization(auzUri, POST_METHOD, date, AUZ_CLIENT_ID, PropertiesLoaderSupportUtils.getProperty("pay-coupon.http.meituan.codes.authorization.secret")));
                httpPost.setHeader(DATE_NAME, date);
            }
            LOGGER.info("build http post req: " + nvps);
        }catch (Exception e) {
            LOGGER.error("build http post req error", e);
        }
        return httpPost;
    }

    private static HttpGet buildHttpGetReq(Map<String, String> param, String url, String auzUri) {
        HttpGet httpGet = null;
        try{
            if(MapUtils.isNotEmpty(param)) {
                List<NameValuePair> nvps = new ArrayList<NameValuePair>(param.size() + 1);
                for(Map.Entry<String, String> entry : param.entrySet()) {
                    nvps.add(new BasicNameValuePair(entry.getKey(), entry.getValue()));
                }
                url = appendGetParams(url, nvps);
            }
            httpGet = new HttpGet(url);
            if (StringUtils.isNotBlank(auzUri)) {
                String date = getDate();
                httpGet.setHeader(AUZ_NAME, AuthUtil.getAuthorization(auzUri, GET_METHOD, date, AUZ_CLIENT_ID, PropertiesLoaderSupportUtils.getProperty("pay-coupon.http.meituan.codes.authorization.secret")));
                httpGet.setHeader(DATE_NAME, date);
            }
            LOGGER.info("build http get req: " + url);
        }catch (Exception e) {
            LOGGER.error("build http get req error:" + url, e);
        }
        return httpGet;
    }

    private static String appendGetParams(String url, List<NameValuePair> nvps) {
        URI uri = URI.create(url);
        String query = uri.getQuery();
        if(query == null) {
            url += buildQueryParams("?", nvps);
        }else if(query.isEmpty()) {
            url += buildQueryParams("", nvps);
        }else {
            url += buildQueryParams(url.endsWith("&") ? "" : "&", nvps);
        }
        return url;
    }

    private static String buildQueryParams(String prefix, List<NameValuePair> nvps) {
        StringBuilder builder = new StringBuilder();
        builder.append(prefix);
        if(CollectionUtils.isNotEmpty(nvps)) {
            for(NameValuePair nvp : nvps) {
                builder.append(nvp.getName()).append("=").append(nvp.getValue()).append("&");
            }
            builder.setLength(builder.length() - 1);
        }
        return builder.toString();
    }

    private static String getDate() {
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("EEE, d MMM yyyy HH:mm:ss zzz", Locale.ENGLISH);
        simpleDateFormat.setTimeZone(TimeZone.getTimeZone("GMT"));
        return simpleDateFormat.format(new Date());
    }

    private static String resolveHttpResponse(HttpResponse response, String bisIdentity) throws IOException {
        if(response == null) {
            LOGGER.error("http response is null for: " + bisIdentity);
            return null;
        }
        int statusCode = response.getStatusLine().getStatusCode();
        if(HttpStatus.SC_OK != statusCode) {
            LOGGER.error(String.format("unexpected http response status [%d] for: %s", statusCode, bisIdentity));
            if (response.getEntity() != null) {
                LOGGER.error(String.format("error msg: %s", EntityUtils.toString(response.getEntity(), CHARSET)));
            }
            return null;
        }
        HttpEntity resEntity = response.getEntity();
        if(resEntity == null) {
            LOGGER.error("response body is null for " + bisIdentity);
            return null;
        }
        return EntityUtils.toString(resEntity, CHARSET);
    }
}
