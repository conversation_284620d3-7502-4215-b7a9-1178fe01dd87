package com.dianping.pay.framework.utils.account.http;

import com.dianping.combiz.spring.util.PropertiesLoaderSupportUtils;
import com.dianping.pay.framework.utils.HttpClient;
import com.google.gson.Gson;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;


import java.util.HashMap;
import java.util.Map;

public class MtUserConvertUtils {

    private static final Logger LOGGER = LoggerFactory.getLogger(MtUserConvertUtils.class);

    private static final String MT_USER_QUERY_URI = "/user/v1/getmtuserid2";

    public static int toMtUserId(long dpUserId) {
        int mtUserId = 0;
        try {
            if (dpUserId > 0) {
                String url = PropertiesLoaderSupportUtils.getProperty("pay-api-mobile.getmtuserid.url");
                Map<String, String> param = new HashMap<String, String>();
                param.put("dpUserId", String.valueOf(dpUserId));
                String jsonResult = HttpClient.executePost(param, url + MT_USER_QUERY_URI, MT_USER_QUERY_URI);
                if (StringUtils.isNotBlank(jsonResult)) {
                    MtUserQueryResponse response = new Gson().fromJson(jsonResult, MtUserQueryResponse.class);
                    if (response.data != null) {
                        mtUserId = response.data.mtUserId;
                    } else {
                        LOGGER.warn(String.format("toMtUserId failed: %s", jsonResult));
                    }
                }
            }
        } catch (Exception e) {
            LOGGER.error("toMtUserId error", e);
        }
        LOGGER.info(String.format("dpUserIdtoMtUserId result: %s -> %s", dpUserId, mtUserId));
        return mtUserId;
    }

    private class MtUserQueryResponse {
        MtUserQueryData data;
    }

    private class MtUserQueryData {
        int mtUserId;
    }
}
