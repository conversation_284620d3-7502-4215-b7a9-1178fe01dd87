package com.dianping.pay.framework.utils.shopUuid.enums;

public enum ShopUuidStatusEnum {


    SHOPID_ONLY(0, "只接受shopId"),
    SHOPID_UUID(1, "同时接受shopId，和shopUuid，优先取shopUuid"),
    UUID_ONLY(2, "只接受shopUuid");

    public int code;

    public String desc;

    ShopUuidStatusEnum(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static ShopUuidStatusEnum fromCode(int code) {
        for (ShopUuidStatusEnum requestTypeEnum : ShopUuidStatusEnum.values()) {
            if (requestTypeEnum.code == code) {
                return requestTypeEnum;
            }
        }
        return SHOPID_ONLY;
    }
}
