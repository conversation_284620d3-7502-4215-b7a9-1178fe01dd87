package com.dianping.pay.framework.utils.account;

import com.dianping.pay.framework.datatypes.IMobileContext;
import com.dianping.pay.framework.utils.account.parser.CookieTokenParser;
import com.dianping.pay.framework.utils.account.parser.IAccountParser;
import com.dianping.pay.framework.utils.account.parser.RequestNewTokenParser;
import com.dianping.pay.framework.utils.account.parser.RequestTokenParser;
import com.google.common.collect.Lists;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.List;

/**
 * @see com.dianping.validationbiz.filter.AccountValidationFilter
 *
 * TODO 同时返回点评美团的userId
 * TODO 降级，localValidation
 * TODO cat打点
 */
public class AccountUtils {

    private static final Logger LOGGER = LoggerFactory.getLogger(AccountUtils.class);

    private static List<IAccountParser> parsers = Lists.newArrayList(
            RequestTokenParser.getInstance(),
            RequestNewTokenParser.getInstance(),
            CookieTokenParser.getInstance()
    );

    /**
     * 根据请求解析userId
     * 点评token返回点评userId
     * 美团token返回美团userId
     * 解析失败返回0
     */
    public static long parseUserId(IMobileContext context) {
        for (IAccountParser parser : parsers) {
            try {
                long userId = parser.parseUserId(context);
                if (userId > 0) {
                    return userId;
                }
            } catch (Exception e) {
                LOGGER.error("exception during parseUserId", e);
            }
        }
        return 0;
    }
}
