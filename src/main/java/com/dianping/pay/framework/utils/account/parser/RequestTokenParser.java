package com.dianping.pay.framework.utils.account.parser;

import com.dianping.account.UserAccountService;
import com.dianping.account.dto.VirtualBindUserInfoDTO;
import com.dianping.account.utils.util.HttpUtils;
import com.dianping.account.validation.AccountValidationService;
import com.dianping.account.validation.dto.AccountValidationResult;
import com.dianping.pay.framework.datatypes.IMobileContext;
import com.dianping.pay.framework.utils.PayPlatformUtils;
import com.dianping.pay.framework.utils.SpringLocator;
import com.dianping.pay.framework.utils.account.TokenUtils;

public class RequestTokenParser implements IAccountParser {

    private static RequestTokenParser instance = new RequestTokenParser();
    private UserAccountService userAccountService = SpringLocator.getBean(UserAccountService.class);
    private AccountValidationService accountValidationService = SpringLocator.getBean(AccountValidationService.class);

    private RequestTokenParser() {}

    @Override
    public long parseUserId(IMobileContext context) {
        String urlToken = TokenUtils.getToken(context);
        if (PayPlatformUtils.isMtClient(context)) {
            if (TokenUtils.isMtToken(urlToken)) {
                VirtualBindUserInfoDTO user = userAccountService.loadUserByToken(
                        urlToken,
                        HttpUtils.getUserIp(context.getRequest()),
                        TokenUtils.getTokenQueryParam(context)
                );
                if (user != null) {
                    return user.getMtid();
                }
            }
        } else {
            if (TokenUtils.isDpToken(urlToken)) {
                AccountValidationResult response = accountValidationService.validateDper(urlToken);
                if (response != null && response.isValid()) {
                    return response.getUserId();
                }
            }
        }
        return 0;
    }

    public static IAccountParser getInstance() {
        return instance;
    }
}
