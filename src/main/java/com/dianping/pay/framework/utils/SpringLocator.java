package com.dianping.pay.framework.utils;

import com.dianping.cat.Cat;
import org.springframework.beans.BeansException;
import org.springframework.beans.factory.BeanFactoryUtils;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;

/**
 * <AUTHOR>
 *
 */
public class SpringLocator implements ApplicationContextAware {

    private static ApplicationContext applicationContext;

    @SuppressWarnings("unchecked")
    public static <T> T getBean(String name) {
        Cat.logEvent("INVALID_METHOD_1", "com.dianping.pay.framework.utils.SpringLocator.getBean(java.lang.String)");
        return (T) applicationContext.getBean(name);
    }

    @SuppressWarnings("unchecked")
    public static <T> T getBean(Class<T> clazz) {
        return (T) BeanFactoryUtils.beanOfType(applicationContext, clazz);
    }

    public void setApplicationContext(ApplicationContext applicationContext) throws BeansException {
        SpringLocator.applicationContext = applicationContext;
    }

    public static ApplicationContext getApplicationContext() {
        return applicationContext;
    }

}

