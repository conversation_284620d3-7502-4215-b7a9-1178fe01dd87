package com.dianping.pay.framework.core;

import com.dianping.avatar.log.AvatarLogger;
import com.dianping.avatar.log.AvatarLoggerFactory;
import com.dianping.cat.Cat;
import com.dianping.cat.message.Message;
import com.dianping.mobile.base.datatypes.enums.Product;
import com.dianping.mobile.framework.core.DefaultHeadParamBuilder;
import com.dianping.mobile.framework.datatypes.AppType;
import com.dianping.mobile.framework.datatypes.MobileHeader;
import com.dianping.mobile.framework.exception.ApplicationDecryptException;
import com.dianping.mobile.framework.exception.ApplicationRuntimeException;
import com.dianping.mobile.framework.exception.IllegalNewTokenException;
import com.dianping.mobile.framework.exception.IllegalUserAgentException;
import com.dianping.mobile.framework.io.MobileIOUtil;
import com.dianping.mobile.framework.io.NoPaddingEncryptor;
import com.dianping.mobile.framework.io.Pkcs5Encryptor;
import com.dianping.mobile.framework.io.ResponseContent;
import com.dianping.ops.remote.RemoteIpGetter;
import com.dianping.pay.framework.datatypes.MobileContext;
import com.dianping.pay.framework.datatypes.UAPatternConfig;
import com.dianping.pay.framework.datatypes.enums.ClientType;
import com.dianping.pay.framework.datatypes.enums.Platform;
import com.dianping.pay.framework.utils.account.AccountUtils;
import com.dianping.phoenix.environment.PhoenixContext;
import org.apache.commons.fileupload.FileItem;
import org.apache.commons.fileupload.disk.DiskFileItemFactory;
import org.apache.commons.fileupload.servlet.FileCleanerCleanup;
import org.apache.commons.fileupload.servlet.ServletFileUpload;
import org.apache.commons.io.FileCleaningTracker;
import org.apache.commons.lang.StringUtils;

import javax.servlet.ServletContext;
import javax.servlet.http.HttpServletRequest;
import java.io.*;
import java.net.URLDecoder;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

public class DefaultMobileContextBuilder implements IMobileContextBuilder {

    private static final AvatarLogger LOG = AvatarLoggerFactory.getLogger(DefaultMobileContextBuilder.class);
    private static NoPaddingEncryptor noPaddingEncryptor = new NoPaddingEncryptor();
    private static Pkcs5Encryptor pkcs5Encryptor = new Pkcs5Encryptor();
    private static final String KEY_UPLOAD_FILE_APP_REQUEST = "be_data";
    private IUserAgentParser userAgentParser = new DefaultUserAgentParser();

    @Override
    public void buildContext(MobileContext context, ServletContext servletContext, HttpServletRequest request, boolean isPost, boolean postCompressed, boolean isCheckToken) throws IllegalUserAgentException, IllegalNewTokenException, ApplicationDecryptException {
        if (context == null) {
            throw new ApplicationRuntimeException("buildContext MobileContext == null");
        }
        try {
            request.setCharacterEncoding(ResponseContent.ENCODING);

            /**
             * hints：要获得http里head的参数可采用，HeadParamUtil工具类直接获得
             */
            parseHeader(context, request);

            parseRequestIp(context, request);

            parseUserAgent(context);

            parseParamters(context, servletContext, request, isPost, postCompressed);
            parseUserId(context);

        } catch (UnsupportedEncodingException e) {
            LOG.error("buildContext UnsupportedEncodingException", e);
            throw new ApplicationRuntimeException("buildContext UnsupportedEncodingException", e);
        }
    }

    private void parseHeader(MobileContext context, HttpServletRequest request) throws IllegalUserAgentException {
        String deviceId = request.getHeader("pragma-device") == null ? StringUtils.EMPTY : request.getHeader("pragma-device"); // symbian kjava etc. client have no deviceId
        String dpid = request.getHeader("pragma-dpid") == null ? StringUtils.EMPTY : request.getHeader("pragma-dpid");    //客户端6.1 开始传dpid
        Cat.getProducer().logEvent("Mobile", "Device", Message.SUCCESS, deviceId);
        String token = request.getHeader("pragma-token");

        String userAgent = request.getHeader("User-Agent");
        String pragma = request.getHeader("pragma");
        String pragmaOS = request.getHeader("pragma-os");
        String newToken = request.getHeader("pragma-newtoken");
        String wifi = request.getHeader("wifi");
        String uuid = request.getHeader("pragma-uuid");
        String appTypeClient = request.getHeader("pragma-apptype");

        if (pragmaOS != null && pragmaOS.toLowerCase().contains("mapi")) {
            userAgent = pragmaOS;
        } else if (pragma != null && pragma.toLowerCase().contains("mapi")) {
            userAgent = pragma;
        }
        if (userAgent == null) {
            LOG.warn("useragent== null");
            throw new IllegalUserAgentException("useragent== null");
        }

        Map<String, String> headParamMap = new HashMap<String, String>();
        DefaultHeadParamBuilder.getInstance().buildHeadParamMap(request, headParamMap);
        context.setHeader(new MobileHeader(userAgent, token, deviceId, newToken, dpid, wifi, uuid, headParamMap));

        String requestId = PhoenixContext.getInstance().getRequestId();
        if (requestId != null) {
            String refferRequestId = PhoenixContext.getInstance().getReferRequestId();
            context.setRequestId(requestId);
            context.setRefferRequestId(refferRequestId);
        }

        if ("com.dianping.tuan".equals(appTypeClient) || "com.dianping.t".equals(appTypeClient)
                || "com.dianping.ba.tuan".equals(appTypeClient)) {
            context.setAppType(AppType.TUANAPP);
        } else {
            // 默认为主app
            context.setAppType(AppType.MAINAPP);
        }

        // add mtid fot meituan
        context.getHeader().setUnionId(
                StringUtils.defaultString(request.getHeader("pragma-unionid")));
    }

    private void parseRequestIp(MobileContext context, HttpServletRequest request) {
        context.setUserIp(RemoteIpGetter.getFirstGlobalAddr(request));
    }

    private void parseUserAgent(MobileContext context) throws IllegalUserAgentException {
        String userAgent = context.getHeader().getUserAgent();
        context.setUserAgent(userAgent);
        String ua = userAgent.toLowerCase();
        for (ClientType client : userAgentParser.getKeys()) {
            Pattern pattern = userAgentParser.getPattern(client);
            Matcher matcher = pattern.matcher(ua);
            if (matcher.find() && matcher.groupCount() == 3) {
                context.setClient(client);
                setContext(context, matcher, client);
                break;
            }
        }

        // add dynamic config
        if (null == context.getClient()) {
            for (UAPatternConfig uaPatternConfig : ProductConfigHelperMain.getUAPatterns()) {
                Pattern pattern = uaPatternConfig.getUaPattern();
                Matcher matcher = pattern.matcher(ua);
                if (matcher.find() && matcher.groupCount() == 3) {
                    Product product =
                            Product.getProductByProdId(uaPatternConfig.getProductId());
                    ClientType client = new ClientType(uaPatternConfig.getPlatform(), product);
                    setContext(context, matcher, client);
                    break;
                }
            }
        }

        if (context.getClient() == null) {
            final String msg = "clientType unkown, user agent:" + ua;
            LOG.error(msg);
            if (context.getRequest().getRequestURI().endsWith(".jsonp")) {
                context.setClient(ClientType.MAINAPP_H5);
            } else {
                throw new IllegalUserAgentException(msg);
            }
        } else {
            String[] s = ua.split(";");
            if (s != null && s.length == 2) {
                context.setOs(s[1].trim().substring(0, s[1].trim().length() - 1));
            } else {
                context.setOs("");
            }
        }
    }

    private void setContext(MobileContext context, Matcher matcher, ClientType client) {
        Cat.logEvent("INVALID_METHOD_4", "com.dianping.pay.framework.core.DefaultMobileContextBuilder.setContext(MobileContext,Matcher,ClientType)");
        context.setClient(client);
        String protocolVersion = matcher.group(1).trim();
        String clientVersion = matcher.group(2).trim();
        String source = matcher.group(3).trim();
        context.setProtocolVersion(protocolVersion);
        context.setVersion(clientVersion);
        String[] ss = source.split(" ");
        if (ss.length >= 2) {
            context.setSource(ss[0]);
            context.setCellPhoneType(ss[ss.length - 1]);
        } else {
            context.setSource(source);
        }

        context.setDeviceType(ProductConfigHelperMain.getDeviceType(context.getCellPhoneType()));
    }

    private void parseParamters(MobileContext context, ServletContext servletContext, HttpServletRequest request, boolean isPost, boolean postCompressed) throws ApplicationDecryptException {
        Map<String, String> parameterMap = new HashMap<String, String>();

        context.setPost(isPost);
        if (isPost) {
            if (ServletFileUpload.isMultipartContent(request)) {
                context.setMultiParamMap(parseMultiPart(servletContext, request, parameterMap));
            }

            try {
                InputStream reqis = request.getInputStream();
                if (reqis != null) {
                    parsePostParamters(parameterMap, reqis, context.getClient(), postCompressed, context);
                }
            } catch (IOException e) {
                LOG.warn("parse inputStream exception", e);
            }

            try {
                if (context.getMultiParamMap() != null && context.getMultiParamMap().get(KEY_UPLOAD_FILE_APP_REQUEST) != null) {
                    InputStream is = context.getMultiParamMap().get(KEY_UPLOAD_FILE_APP_REQUEST).getInputStream();
                    if (is != null) {
                        parsePostParamters(parameterMap, is, context.getClient(), postCompressed, context);
                    }
                }
            } catch (IOException e) {
                LOG.warn("parse be_data exception", e);
            }
        }
        parseGetParamters(parameterMap, request, context);
        context.setParamtersMap(parameterMap);
    }

    private void parsePostParamters(Map<String, String> parameterMap, InputStream is, ClientType clientType,
                                    boolean postCompressed, MobileContext context) throws ApplicationDecryptException {
        Cat.logEvent("INVALID_METHOD_3", "com.dianping.pay.framework.core.DefaultMobileContextBuilder.parsePostParamters(Map,InputStream,ClientType,boolean,MobileContext)");
        byte[] bytes = inputStream2Bytes(is);
        if (bytes != null && bytes.length > 0) {
            try {
                byte[] post = null;
                if (clientType != null && (clientType.getPlatform() == Platform.WinPhone
                        || clientType.getPlatform() == Platform.Win8Pad)) {
                    post = pkcs5Encryptor.decrypt(bytes);
                } else {
                    post = noPaddingEncryptor.decrypt(bytes, postCompressed);
                }
                if (postCompressed) {
                    post = MobileIOUtil.uncompress(post);
                }
                if (post != null && post.length > 0) {
                    String data = new String(post, ResponseContent.ENCODING);
                    String[] keyValue = data.split("&");
                    for (int i = 0; i < keyValue.length; i++) {
                        String[] kv = keyValue[i].split("[=]", 2);
                        if (kv.length == 2) {
                            try {
                                context.addRawParameterMap(URLDecoder.decode(kv[0], ResponseContent.ENCODING).toLowerCase(), kv[1]);
                                parameterMap.put(URLDecoder.decode(kv[0], ResponseContent.ENCODING).toLowerCase(), URLDecoder.decode(kv[1], ResponseContent.ENCODING));
                            } catch (Exception e) {
                                if (StringUtils.isNotBlank(kv[0]) && StringUtils.isNotBlank(kv[1])) {
                                    parameterMap.put(kv[0].toLowerCase(), kv[1]);

                                    context.addRawParameterMap(kv[0].toLowerCase(), kv[1]);
                                }
                                //LOG.error(kv[0] + "," + kv[1], e);
                            }
                        }
                    }
                }
            } catch (ApplicationDecryptException e) {
                throw new ApplicationDecryptException("parsePostParamters decryptByNoPading", e);
            } catch (UnsupportedEncodingException e) {
                throw new ApplicationRuntimeException("parsePostParamters UnsupportedEncodingException", e);
            }
        }
    }

    private byte[] inputStream2Bytes(InputStream inputStream) {
        Cat.logEvent("INVALID_METHOD_5", "com.dianping.pay.framework.core.DefaultMobileContextBuilder.inputStream2Bytes(java.io.InputStream)");
        ByteArrayOutputStream baos = new ByteArrayOutputStream();
        try {
            if (inputStream != null) {
                byte[] buffer = new byte[1024];//need to check the common size of the request
                int bytesRead = -1;
                while ((bytesRead = inputStream.read(buffer)) > 0) {
                    baos.write(buffer, 0, bytesRead);
                }
            }
            return baos.toByteArray();
        } catch (IOException e) {
            throw new ApplicationRuntimeException("parsePostParamters inputStream2Bytes", e);
        }
    }

    private void parseGetParamters(Map<String, String> paramgersMap, HttpServletRequest request, MobileContext context) {
        @SuppressWarnings("unchecked")
        Enumeration<String> params = request.getParameterNames();
        while (params.hasMoreElements()) {
            String key = params.nextElement();
            String decodedKey = null;
            try {
                decodedKey = URLDecoder.decode(key, ResponseContent.ENCODING).toLowerCase();
            } catch (UnsupportedEncodingException e) {
                LOG.warn(e);
            }
            String value = request.getParameter(key);
            String decodedValue;
            try {
                decodedValue = URLDecoder.decode(value, ResponseContent.ENCODING);
            } catch (Exception e) {

                decodedValue = value;
                LOG.warn(e);
            }
            if (decodedKey != null) {
                paramgersMap.put(decodedKey, decodedValue);
                context.addRawParameterMap(decodedKey, value);
            } else {
                paramgersMap.put(key, decodedValue);
                context.addRawParameterMap(key, value);
            }
        }
    }

    private void parseUserId(MobileContext context) throws IllegalNewTokenException {
        //todo mark tujun
        long parsedUserId = AccountUtils.parseUserId(context);
        context.setUserId(parsedUserId);
        Cat.getProducer().logEvent("Mobile", "user", Message.SUCCESS, String.valueOf(parsedUserId));
    }

    public void setUserAgentParser(IUserAgentParser userAgentParser) {
        Cat.logEvent("INVALID_METHOD_3", "com.dianping.pay.framework.core.DefaultMobileContextBuilder.setUserAgentParser(com.dianping.pay.framework.core.IUserAgentParser)");
        this.userAgentParser = userAgentParser;
    }

    private Map<String, FileItem> parseMultiPart(ServletContext servletContext, HttpServletRequest req,
                                                 Map<String, String> parameterMap) {
        Cat.logEvent("INVALID_METHOD_3", "com.dianping.pay.framework.core.DefaultMobileContextBuilder.parseMultiPart(javax.servlet.ServletContext,javax.servlet.http.HttpServletRequest,java.util.Map)");
        if (!ServletFileUpload.isMultipartContent(req)) {
            return null;
        }
        try {
            HashMap<String, FileItem> files = new HashMap<String, FileItem>();
            // Create a new file upload handler
            ServletFileUpload upload = newServletFileUpload(servletContext);
            // Parse the request
            @SuppressWarnings("unchecked")
            List items = upload.parseRequest(req);
            // Process the uploaded items
            @SuppressWarnings("unchecked")
            Iterator iter = items.iterator();
            while (iter.hasNext()) {
                FileItem item = (FileItem) iter.next();
                String name = item.getFieldName();

                if (item.isFormField()) {// Process a regular form field
                    String value = item.getString("utf-8");
                    parameterMap.put(name.toLowerCase(), value);
                } else {
                    files.put(name, item);
                }
            }
            return files;
        } catch (Exception e) {
            throw new ApplicationRuntimeException("parse multi part parameter error", e);
        }
    }

    /**
     * TODO: can  DiskFileItemFactory factory  be singleton???
     */
    private ServletFileUpload newServletFileUpload(ServletContext context) {
        Cat.logEvent("INVALID_METHOD_5", "com.dianping.pay.framework.core.DefaultMobileContextBuilder.newServletFileUpload(javax.servlet.ServletContext)");
        // Create a factory for disk-based file items
        DiskFileItemFactory factory = new DiskFileItemFactory();
        // Set factory constraints
        factory.setSizeThreshold(5 * 1024 * 1024);//yourMaxMemorySize  TODO remove hardcode
        factory.setRepository(new File(context.getRealPath("/upload_temp"))); //yourTempDirectory
        // Create a factory for disk-based file items
        FileCleaningTracker fileCleaningTracker = FileCleanerCleanup.getFileCleaningTracker(context);
        factory.setFileCleaningTracker(fileCleaningTracker);


        ServletFileUpload upload = new ServletFileUpload(factory);
        // Set overall request size constraint
        upload.setSizeMax(5 * 1024 * 1024);//yourMaxRequestSize  TODO remove hardcode

        return upload;
    }
}
