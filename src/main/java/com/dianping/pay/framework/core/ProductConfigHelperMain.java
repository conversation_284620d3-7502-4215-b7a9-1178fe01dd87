package com.dianping.pay.framework.core;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.dianping.avatar.log.AvatarLogger;
import com.dianping.avatar.log.AvatarLoggerFactory;
import com.dianping.cat.Cat;
import com.dianping.lion.client.ConfigCache;
import com.dianping.lion.client.ConfigChange;
import com.dianping.mobile.base.datatypes.enums.DeviceType;
import com.dianping.mobile.framework.exception.IllegalUserAgentException;
import com.dianping.pay.framework.datatypes.UAPatternConfig;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.regex.Pattern;

/**
 * Created by yun<PERSON> on 15/11/24.
 * <p/>
 * product相关系统配置信息帮助类
 */
@Component
public final class ProductConfigHelperMain {
    private static final AvatarLogger LOG = AvatarLoggerFactory.getLogger(ProductConfigHelperMain.class);

    private static final String USER_AGENT_PATTERN_KEY = "mapi-shell.support.ua.pattern";
    private static final String DEVICE_TYPE_ENUM = "mapi-shell.support.device.type.enum";

    private static volatile List<UAPatternConfig> uAPatterns;
    private static volatile Map<String, DeviceType> deviceTypeMapCache = new HashMap<String, DeviceType>();

    public static List<UAPatternConfig> getUAPatterns() {
        return uAPatterns;
    }

    public static DeviceType getDeviceType(String deviceKey) {
        Cat.logEvent("INVALID_METHOD_4", "com.dianping.pay.framework.core.ProductConfigHelperMain.getDeviceType(java.lang.String)");
        DeviceType deviceType;
        if (StringUtils.isEmpty(deviceKey) || null == (deviceType = deviceTypeMapCache.get(deviceKey))) {
            return DeviceType.UNKNOWN;
        }
        return deviceType;
    }

    private void loadUAPatternsConfig() throws Exception{
        String uAConfig = "";
        List<UAPatternConfig> patterns;
        try {
            uAConfig = getProperty(USER_AGENT_PATTERN_KEY);
            patterns = JSON.parseObject(uAConfig,
                    new TypeReference<List<UAPatternConfig>>() {
                    }.getType());

            for (UAPatternConfig ua : patterns) {
                ua.setUaPattern(Pattern.compile(ua.getUa(), Pattern.CASE_INSENSITIVE));
            }
        } catch (Exception e) {
            throw new IllegalUserAgentException("UA pattern failed", e);
        }

        if (CollectionUtils.isEmpty(patterns)) {
            throw new IllegalUserAgentException("UA pattern is empty");
        }

        uAPatterns = patterns;
    }

    private static void loadDeviceTypes() {
        String deviceTypeConfigs;
        Map<String, DeviceType> deviceTypeMap;
        try {
            deviceTypeConfigs = getProperty(DEVICE_TYPE_ENUM);
            deviceTypeMap = JSON.parseObject(deviceTypeConfigs,
                    new TypeReference<Map<String, DeviceType>>() {
                    }.getType());
        } catch (Exception e) {
            throw new IllegalArgumentException("DeviceType format is not ok");
        }

        if (null == deviceTypeMap) {
            return;
        }

        deviceTypeMapCache = deviceTypeMap;
    }

    /**
     * 初始化时 调用，失败时项目不能启动
     */
    @PostConstruct
    public void init() throws Exception{

        // User Agent
        loadUAPatternsConfig();

        // Device type
        loadDeviceTypes();

        ConfigCache.getInstance().addChange(new ConfigChange() {
            // 当lion的值发现变化时再触发一次
            @Override
            public void onChange(String key, String value) {

                try {
                    // User Agent
                    if (StringUtils.equalsIgnoreCase(USER_AGENT_PATTERN_KEY, key)) {
                        loadUAPatternsConfig();
                    }
                } catch (Exception e)  {
                    LOG.warn("product config format not correct.");
                }

                if (StringUtils.equalsIgnoreCase(USER_AGENT_PATTERN_KEY, key)) {
                    loadDeviceTypes();
                }
            }
        });
    }

    /**
     * @return
     */
    private static String getProperty(String lionKey) {
        return ConfigCache.getInstance().getProperty(lionKey);
    }
}
