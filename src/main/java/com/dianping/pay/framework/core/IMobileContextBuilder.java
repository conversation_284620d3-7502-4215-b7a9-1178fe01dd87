package com.dianping.pay.framework.core;

import com.dianping.mobile.framework.exception.ApplicationDecryptException;
import com.dianping.mobile.framework.exception.IllegalNewTokenException;
import com.dianping.mobile.framework.exception.IllegalUserAgentException;
import com.dianping.pay.framework.datatypes.MobileContext;

import javax.servlet.ServletContext;
import javax.servlet.http.HttpServletRequest;

public interface IMobileContextBuilder {

	void buildContext(MobileContext mobileContext, ServletContext servletContext,
                      HttpServletRequest request, boolean isPost, boolean postCompressed, boolean isCheckToken)
			throws IllegalUserAgentException, IllegalNewTokenException, ApplicationDecryptException;
}
