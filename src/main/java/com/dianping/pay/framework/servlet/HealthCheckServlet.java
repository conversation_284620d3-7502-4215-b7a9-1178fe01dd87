package com.dianping.pay.framework.servlet;

import com.dianping.cat.Cat;

import javax.servlet.ServletException;
import javax.servlet.http.HttpServlet;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.PrintWriter;

public class HealthCheckServlet extends HttpServlet {

    @Override
    protected void doGet(HttpServletRequest req, HttpServletResponse resp) throws ServletException, IOException {
        Cat.logEvent("INVALID_METHOD_4", "com.dianping.pay.framework.servlet.HealthCheckServlet.doGet(javax.servlet.http.HttpServletRequest,javax.servlet.http.HttpServletResponse)");
        doPost(req, resp);
    }

    @Override
    protected void doPost(HttpServletRequest req, HttpServletResponse resp) throws ServletException, IOException {
        Cat.logEvent("INVALID_METHOD_5", "com.dianping.pay.framework.servlet.HealthCheckServlet.doPost(javax.servlet.http.HttpServletRequest,javax.servlet.http.HttpServletResponse)");
        resp.setContentType("text/html");
        PrintWriter out = resp.getWriter();
        out.print("it works!");
    }
}
