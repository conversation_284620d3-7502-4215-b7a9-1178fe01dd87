package com.dianping.api.netio;

import java.util.List;

import com.dianping.api.common.util.KeyValuePair;
import com.dianping.api.domain.ClientInfo;
import com.dianping.api.domain.Version;

/**
 * 
 * all the class needs to be send to client should extends this class;
 * 
 * <AUTHOR>
 * 
 */
public abstract class DPEncoder {

	public abstract int getClassId();

	/**
	 * @param client
	 * @param version
	 * @return
	 */
	public abstract List<KeyValuePair> getSerializeList(ClientInfo clientInfo, Version version);
}
