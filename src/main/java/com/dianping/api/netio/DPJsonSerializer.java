
package com.dianping.api.netio;

import com.dianping.api.common.util.KeyValuePair;
import com.dianping.api.domain.ClientInfo;
import com.dianping.avatar.log.AvatarLogger;
import com.dianping.avatar.log.AvatarLoggerFactory;
import com.fasterxml.jackson.core.JsonEncoding;
import com.fasterxml.jackson.core.JsonFactory;
import com.fasterxml.jackson.core.JsonGenerator;

import java.beans.PropertyDescriptor;
import java.io.IOException;
import java.io.OutputStream;
import java.io.Writer;
import java.lang.reflect.Array;
import java.lang.reflect.Method;
import java.util.Date;
import java.util.Iterator;
import java.util.List;

/**
 * <AUTHOR>
 */
public class DPJsonSerializer {
	private static AvatarLogger log = AvatarLoggerFactory.getLogger(DPJsonSerializer.class);
	private static final JsonFactory jsonFactory = new JsonFactory();

	private ClientInfo clientInfo;
	private JsonGenerator g;

	public DPJsonSerializer(OutputStream stream, ClientInfo clientInfo) throws IOException {
		this.clientInfo = clientInfo;
		this.g = jsonFactory.createGenerator(stream, JsonEncoding.UTF8);
		this.g.useDefaultPrettyPrinter();
	}
	
	public DPJsonSerializer(Writer writer, ClientInfo clientInfo) throws IOException {
		this.clientInfo = clientInfo;
		this.g = jsonFactory.createGenerator(writer);
		this.g.useDefaultPrettyPrinter();
	}

	public void serialize(Object obj) {
		try {
			if (obj == null) {
				g.writeNull();
			} else if (obj instanceof Boolean) {
				g.writeBoolean(((Boolean) obj).booleanValue());
			} else if (obj instanceof Integer) {
				g.writeNumber(((Integer) obj).intValue());
			} else if (obj instanceof Long) {
				g.writeNumber(((Long) obj).longValue());
			} else if (obj instanceof Double) {
				g.writeNumber(((Double) obj).doubleValue());
			} else if (obj instanceof Date) {
				g.writeNumber(((Date) obj).getTime());
			} else if (obj instanceof String) {
				g.writeString(((String) obj));
			} else if (obj.getClass().isArray()) {
				int length = Array.getLength(obj);
				Object[] array = new Object[length];
				System.arraycopy(obj, 0, array, 0, length);
				g.writeStartArray();
				for (Object element : array) {
					this.serialize(element);
				}
				g.writeEndArray();
			} else if (obj instanceof List) {
				List<?> list = (List<?>) obj;
				g.writeStartArray();
				Iterator<?> iterator = list.iterator();
				while (iterator.hasNext()) {
					Object element = iterator.next();
					this.serialize(element);
				}
				g.writeEndArray();
			} else if (obj instanceof DPEncoder) {
				DPEncoder encoder = (DPEncoder) obj;
				int classID = encoder.getClassId();
				assert (classID != -1);
				g.writeStartObject();
				for (KeyValuePair pair : encoder.getSerializeList(clientInfo,
						clientInfo.getVersion())) {
					if (!pair.isNeedSerialize(clientInfo, clientInfo.getVersion())) {
						continue;
					}

					try {
						PropertyDescriptor pd = new PropertyDescriptor(pair.getKey(),
								obj.getClass());
						Method method = pd.getReadMethod();
						Object value = method.invoke(obj);
						g.writeFieldName(pair.getKey());
						this.serialize(value);
					} catch (Exception e) {
						log.error("encode bean error!", e);
					}
				}
				g.writeEndObject();
			}
		} catch (IOException ex) {
		}
	}
	
	public void close() throws IOException {
		g.close();
	}

	public void flush() throws IOException{
		g.flush();
	}
}
