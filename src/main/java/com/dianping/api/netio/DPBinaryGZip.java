
package com.dianping.api.netio;

import java.io.ByteArrayOutputStream;
import java.util.zip.GZIPOutputStream;

import com.dianping.avatar.log.AvatarLogger;
import com.dianping.avatar.log.AvatarLoggerFactory;

/**
 * <AUTHOR>
 *
 */
public final class DPBinaryGZip {
	
	private static AvatarLogger LOG = AvatarLoggerFactory.getLogger(DPBinaryGZip.class);
	
    private DPBinaryGZip() {
    }
	
	public static byte[] compress(byte[] source) {
		byte[] result = null;
		/*
		try {
			ByteArrayOutputStream bos = new ByteArrayOutputStream();//TODO: initial size base on request
			GZIPOutputStream gzip = new GZIPOutputStream(bos);
			gzip.write(source);
			gzip.finish();
			gzip.close();
			result = bos.toByteArray();
			bos.close();
//			if(source.length/result.length < 1) {
//				LOG.info("source length="+ source.length + " result length=" + result.length);
//			}
		} catch (Exception e) {
			LOG.error("compress errot",e);
		}
		*/
		try (ByteArrayOutputStream bos = new ByteArrayOutputStream();
			 GZIPOutputStream gzip = new GZIPOutputStream(bos)) {
			gzip.write(source);
			gzip.finish();
			result = bos.toByteArray();
		} catch (Exception e) {
			LOG.error("compress error", e);
		}
		return result;
	}

}
