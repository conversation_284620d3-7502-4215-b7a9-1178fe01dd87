
package com.dianping.api.netio;

import java.io.UnsupportedEncodingException;

import javax.crypto.Cipher;
import javax.crypto.spec.IvParameterSpec;
import javax.crypto.spec.SecretKeySpec;
 
import com.dianping.avatar.log.AvatarLogger;
import com.dianping.avatar.log.AvatarLoggerFactory;
import com.dianping.cat.Cat;

/**
 * <AUTHOR>
 */
public class DPBinaryEncrypt {
	
	private static byte[] POST_ENCRYPT_KEY;   
	private static byte[] POST_ENCRYPT_IV;
	private static final AvatarLogger LOG = AvatarLoggerFactory.getLogger(DPBinaryEncrypt.class);
	 
	static{
		 try {
			POST_ENCRYPT_KEY  = "D7C6F71A12153EE5".getBytes("UTF-8");
			POST_ENCRYPT_IV   = "55C930D827BDABFD".getBytes("UTF-8");		
		} catch (UnsupportedEncodingException e) {
			LOG.error("UTF-8 encode error!!");
		}		 	 
	}
	
	private DPBinaryEncrypt() {
	}
	
	public static byte[] decryptUserToken(byte[]cryptograph) throws Exception
	{
        Cat.logEvent("INVALID_METHOD_3", "com.dianping.api.netio.DPBinaryEncrypt.decryptUserToken(byte[])");
        return DPBinaryEncrypt.decrypt(cryptograph,
									   DPBinaryEncrypt.POST_ENCRYPT_IV,
									   DPBinaryEncrypt.POST_ENCRYPT_KEY, NOPADING);
	}
	
	public static byte[] decrypt(byte[]cryptograph, int padingMode) throws Exception
	{
		return DPBinaryEncrypt.decrypt(cryptograph,
									   DPBinaryEncrypt.POST_ENCRYPT_KEY,
									   DPBinaryEncrypt.POST_ENCRYPT_IV, padingMode);
	}
	
	public static byte[] decrypt(byte[] bytes, byte[] key, byte[] iv, int padingMode) throws Exception {
		try {
			if(padingMode == NOPADING) {
				Cipher cipher = Cipher.getInstance("AES/CBC/NoPadding");
				cipher.init(Cipher.DECRYPT_MODE, 
							new SecretKeySpec(key, "AES"),
							new IvParameterSpec(iv));

				byte[] decrBuffer = cipher.doFinal(bytes);
				int blank = 0;
				for (int i = decrBuffer.length - 1; i >= 0; i--) {
					if (decrBuffer[i] == '\0') {
						blank++;
					}
					else {
						break;
					}
				}
				byte[]result = decrBuffer;
				if(blank>0){
					byte[] decrBytes = new byte[decrBuffer.length - blank];
					System.arraycopy(decrBuffer, 0, decrBytes, 0, decrBytes.length);
					result = decrBytes;
				}
				return result;
			} else {
				Cipher cipher = Cipher.getInstance("AES/CBC/PKCS5Padding");
				cipher.init(Cipher.DECRYPT_MODE, 
							new SecretKeySpec(key, "AES"),
							new IvParameterSpec(iv));

				return cipher.doFinal(bytes);
			}			
		} catch (Exception e) {
			LOG.info("decrypt error!");
			throw new Exception("decrpyt error",e);			 
		}
	}

	
	public static byte[]encryptUserToKen(byte[]contents){
        Cat.logEvent("INVALID_METHOD_5", "com.dianping.api.netio.DPBinaryEncrypt.encryptUserToKen(byte[])");
        return DPBinaryEncrypt.encrypt(	contents,
				DPBinaryEncrypt.POST_ENCRYPT_IV,
				DPBinaryEncrypt.POST_ENCRYPT_KEY, NOPADING);
	}
	 
	public static byte[]encrypt(byte[]contents, int padingMode)
	{
		return DPBinaryEncrypt.encrypt(	contents,
										DPBinaryEncrypt.POST_ENCRYPT_KEY,
										DPBinaryEncrypt.POST_ENCRYPT_IV, padingMode);
	}
	
	/**
	 * using 0 to pad
	 * @param bytes
	 * @param key
	 * @param iv
	 * @return
	 */
	public static byte[] encrypt(byte[] bytes, byte[] key, byte[] iv, int padingMode) {
		Cipher cipher = null;
		try {
			if(padingMode == NOPADING) {
				cipher = Cipher.getInstance("AES/CBC/NoPadding");
				cipher.init(Cipher.ENCRYPT_MODE, new SecretKeySpec(key, "AES"),
						new IvParameterSpec(iv));
					byte[] pbytes;
					if (bytes.length % 16 == 0) {
						pbytes = bytes;
					}
					else {
						pbytes = new byte[bytes.length + (16 - bytes.length % 16)];
						System.arraycopy(bytes, 0, pbytes, 0, bytes.length);
					}
					return cipher.doFinal(pbytes);
			} else {
				cipher = Cipher.getInstance("AES/CBC/PKCS5Padding");
				cipher.init(Cipher.ENCRYPT_MODE, new SecretKeySpec(key, "AES"),
						new IvParameterSpec(iv));
					byte[] pbytes = bytes;
					return cipher.doFinal(pbytes);
			}
		} catch (Exception e) {
			LOG.error("encrypt error!", e);
			return null;
		}
	}
	
	public static final int NOPADING = 1;
	public static final int PKCS5 = 2;
	
	public static String toHex(byte[] hexbyte) {
        Cat.logEvent("INVALID_METHOD_1", "com.dianping.api.netio.DPBinaryEncrypt.toHex(byte[])");
        StringBuilder stringBuilder = new StringBuilder("");
		for (int i = 0; i < hexbyte.length; i++) {
			int v = hexbyte[i] & 0xFF;
			
			String hv = Integer.toHexString(v);
			if (hv.length() < 2) { 
				stringBuilder.append(0);
			}
			stringBuilder.append(hv);
		}
		return stringBuilder.toString().toLowerCase();
	}

	public static byte[] fromHex(String hexString) {
        Cat.logEvent("INVALID_METHOD_2", "com.dianping.api.netio.DPBinaryEncrypt.fromHex(java.lang.String)");
        int length = hexString.length() / 2;
		char[] hexChars = hexString.toCharArray();
		byte[] d = new byte[length];
		for (int i = 0; i < length; i++) {
			int pos = i * 2;
			d[i] = (byte) (charToByte(hexChars[pos]) << 4 | charToByte(hexChars[pos + 1]));
		}
		return d;
	}

	public static byte charToByte(char c) {
        Cat.logEvent("INVALID_METHOD_5", "com.dianping.api.netio.DPBinaryEncrypt.charToByte(char)");
        if(c>='0' && c<='9') {
			return (byte)( c-'0');
		} else {
			return (byte)( c - 'a' + 10) ;
		}
	}
}
