
package com.dianping.api.netio;

import com.dianping.cat.Cat;

import java.util.HashMap;

/**
 * <AUTHOR>
 *
 */

public class ParameterMap extends HashMap<String,String> {
	
	private static final long serialVersionUID = -7376085741023688839L;
	
	
	/**
	 *  
	 * @param key
	 * @return
	 * if key doesn't exist, return ""
	 */
	public String getString(String key) {
		String value = get(key);
		if (value != null) {
			return value;
		}
		else {
			return "";
		}
	}

	/**
	 * default value is 0
	 * @param key
	 * @return
	 */
	public int getInteger(String key) {
		return getInteger(key, 0);
	}

	public int getInteger(String key, String defaultValue) {
        Cat.logEvent("INVALID_METHOD_4", "com.dianping.api.netio.ParameterMap.getInteger(java.lang.String,java.lang.String)");
        return getInteger(key, Integer.parseInt(defaultValue));
	}
	public int getInteger(String key, int defaultValue) {
		try {
			return Integer.parseInt(get(key));
		} catch (NumberFormatException ex) {
			return defaultValue;
		}
	}

	/**
	 * default value is 0
	 * @param key
	 * @return
	 */
	public double getDouble(String key) {
        Cat.logEvent("INVALID_METHOD_2", "com.dianping.api.netio.ParameterMap.getDouble(java.lang.String)");
        return getDouble(key, 0);
	}

	public double getDouble(String key, double defaultValue) {
        Cat.logEvent("INVALID_METHOD_2", "com.dianping.api.netio.ParameterMap.getDouble(java.lang.String,double)");
        try {
			String value = get(key);
			if(value == null) {
				return defaultValue;
			} else {
				return Double.parseDouble(value);
			}
		} catch (NumberFormatException ex) {
			return defaultValue;
		}
	}
	
	public long getLong(String key) {
        Cat.logEvent("INVALID_METHOD_4", "com.dianping.api.netio.ParameterMap.getLong(java.lang.String)");
        return getLong(key, 0);
	}
	
	public long getLong(String key, long defaultValue) {
        Cat.logEvent("INVALID_METHOD_4", "com.dianping.api.netio.ParameterMap.getLong(java.lang.String,long)");
        try {
			String value = get(key);
			if(value == null) {
				return defaultValue;
			} else {
				return Long.parseLong(value);
			}
		} catch(NumberFormatException e) {
			return defaultValue;
		}
	}
	
	/**
	 * default value is false
	 * @param key
	 * @return
	 */
	public boolean getBoolean(String key) {
        Cat.logEvent("INVALID_METHOD_1", "com.dianping.api.netio.ParameterMap.getBoolean(java.lang.String)");
        return getBoolean(key, false);
	}

	public boolean getBoolean(String key, boolean defaultValue) {
        Cat.logEvent("INVALID_METHOD_1", "com.dianping.api.netio.ParameterMap.getBoolean(java.lang.String,boolean)");
//		try {
//			int value = Integer.parseInt(get(key));
//			if(value==1)
//				return true;
//			if(value==0)
//				return false;
//			return Boolean.parseBoolean(get(key));
//		} catch (NumberFormatException ex) {
//			return defaultValue;
//		}
		try {
			String s = get(key);
			if(s != null) {
				s = s.toLowerCase();
				return (s.equals("true") || s.equals("yes") || s.equals("y") || Integer.parseInt(s) > 0);
			}
		} catch(Exception e) {
			
		}
		return defaultValue;
	}

}
