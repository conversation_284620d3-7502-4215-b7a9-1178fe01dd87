
package com.dianping.api.netio;
 
/**
 *  
 * <AUTHOR>
 *
 */
public class ResponseContent {
	public static final String ENCODING 		 = "utf-8";
	public static final String BINARYCONTENTTYPE = "application/binary; charset="+  ENCODING;
	public static final String XMLCONTENTTYPE    = "application/xml; charset="+  ENCODING;
    public static final String JSONCONTENTTYPE = "application/json; charset=" + ENCODING;
	
	
	private int statusCode;
	private String contentType;
	private byte[] content;

	public int getStatusCode() {
		return statusCode;
	}

	public void setStatusCode(int statusCode) {
		this.statusCode = statusCode;
	}

	public String getContentType() {
		return contentType;
	}

	public void setContentType(String contentType) {
		this.contentType = contentType;
	}

	public byte[] getContent() {
		return content;
	}

	public void setContent(byte[] content) {
		this.content = content;
	}

}
