
package com.dianping.api.netio;

import java.io.ByteArrayOutputStream;

import com.dianping.api.domain.ClientInfo;
import com.dianping.api.domain.SimpleMsg;
import com.dianping.api.domain.SuccessMsg;
import com.dianping.api.domain.Version;
import com.dianping.cat.Cat;


/**
 *  
 * <AUTHOR>
 *
 */
public final class Formatter {
	
	private static final int STATUS_ERROR = 400;
	private static final int STATUS_SUCCESS = 200;
    private static final int STATUS_BIZ_ERROR = 450;
//	private static final int STATUS_UNAUTHORIZED = 401;

	private Formatter() {
	}
	
	/**
	 * the data send to client needs to be encode, compress and encrypt; 
	 * @param data
	 * @return
	 */
	public static ResponseContent responseFormatter(Object data, int padingMode, ClientInfo clientInfo, Version v) {
        Cat.logEvent("INVALID_METHOD_5", "com.dianping.api.netio.Formatter.responseFormatter(java.lang.Object,int,com.dianping.api.domain.ClientInfo,com.dianping.api.domain.Version)");
        ResponseContent result = new ResponseContent();
		result.setStatusCode(STATUS_SUCCESS);
		result.setContentType(ResponseContent.BINARYCONTENTTYPE);
		if (data instanceof SimpleMsg && !(data instanceof SuccessMsg)) {
			result.setStatusCode(STATUS_ERROR);
		}

		result.setContent(toBytes(data, padingMode, clientInfo, v));
		return result;
	}

	public static ResponseContent responseFormatter(Object data, int statusCode, int padingMode, ClientInfo clientInfo, Version v) {
		ResponseContent result = new ResponseContent();
		result.setStatusCode(statusCode);
		result.setContentType(ResponseContent.BINARYCONTENTTYPE);
        if (data instanceof SimpleMsg && !(data instanceof SuccessMsg)) {
            if(STATUS_BIZ_ERROR == statusCode){
                result.setStatusCode(STATUS_BIZ_ERROR);
            }else{
                result.setStatusCode(STATUS_ERROR);
            }
        }else if(statusCode==0){
            result.setStatusCode(STATUS_SUCCESS);
        }

		result.setContent(toBytes(data, padingMode, clientInfo, v));
		return result;
	}
	
	public static byte[] toBytes(Object data, int padingMode, ClientInfo clientInfo, Version v){
		byte[] encodedBytes;
		ByteArrayOutputStream baos = new ByteArrayOutputStream();
		DPBinarySerializer dpbs = new DPBinarySerializer(baos);
		dpbs.encoder(data, clientInfo, v);
		encodedBytes = baos.toByteArray();
		byte[]compressedBytes = DPBinaryGZip.compress(encodedBytes);
		return DPBinaryEncrypt.encrypt(compressedBytes, padingMode);
	}
	
	  
}
