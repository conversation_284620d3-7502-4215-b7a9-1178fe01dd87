
package com.dianping.api.netio;


import java.beans.PropertyDescriptor;
import java.io.DataOutputStream;
import java.io.IOException;
import java.io.OutputStream;
import java.lang.reflect.Array;
import java.lang.reflect.Method;
import java.util.Date;
import java.util.Iterator;
import java.util.List;

import com.dianping.api.common.util.KeyValuePair;
import com.dianping.api.domain.ClientInfo;
import com.dianping.api.domain.Version;
import com.dianping.avatar.log.AvatarLogger;
import com.dianping.avatar.log.AvatarLoggerFactory;
import com.dianping.cat.Cat;


/**
 *  
 * <AUTHOR>
 *
 */
public class DPBinarySerializer {
	private static AvatarLogger log = AvatarLoggerFactory.getLogger(DPBinarySerializer.class);
	private DataOutputStream output;

	public static int getHashCode(String value) {
        Cat.logEvent("INVALID_METHOD_4", "com.dianping.api.netio.DPBinarySerializer.getHashCode(java.lang.String)");
        int hash = 0;
		for (char c : value.toCharArray()) {
			hash = 31 * hash + c;
		}
		return hash;
	}

	public static byte[] getHashByte(int length) {
        Cat.logEvent("INVALID_METHOD_5", "com.dianping.api.netio.DPBinarySerializer.getHashByte(int)");
        int lenBytes = (0xffff & length) ^ (length >> 16);
		return new byte[] { (byte) (lenBytes >> 8), (byte) (lenBytes) };
	}

	public DPBinarySerializer(OutputStream os) {
		this.output = new DataOutputStream(os);
	}

	public void close() {
		try {
			this.output.close();
		} catch (IOException e) {
		}
	}

	public void flush() {
        Cat.logEvent("INVALID_METHOD_5", "com.dianping.api.netio.DPBinarySerializer.flush()");
        try {
			this.output.flush();
		} catch (IOException e) {
		}
	}
	
	 
	public void encoder(Object object, ClientInfo clientInfo, Version v) {
		try {
			if (object == null) {
				this.writeNull();
			} else if (object instanceof Boolean) {
				this.writeBoolean((Boolean) object);
			} else if (object instanceof Integer) {
				this.writeInt((Integer) object);
			} else if (object instanceof Long) {
				this.writeLong((Long) object);
			} else if (object instanceof Double) {
				this.writeDouble((Double) object);
			} else if (object instanceof Date) {
				this.writeUTCDate((Date) object);
			} else if (object instanceof String) {
				this.writeString((String) object);
			} else if (object.getClass().isArray()) {
				int length = Array.getLength(object);
				this.writeArray(length);
				Object[] array = new Object[length];
				System.arraycopy(object, 0, array, 0, length);
				for (Object o : array) {
					this.encoder(o, clientInfo, v);
				}
			} else if (object instanceof List) {
				List<?> list = (List<?>) object;
				this.writeArray(list.size());
				Iterator<?> iterator = list.iterator();
				while (iterator.hasNext()) {
					Object obj = iterator.next();
					this.encoder(obj, clientInfo, v);
				}
			} else if(object instanceof DPEncoder){//Object
				DPEncoder encoder = (DPEncoder)object;
				int classID = encoder.getClassId();
				assert(classID!=-1);
				writeObjectBegin(this.output,classID);			
				List<KeyValuePair> list= encoder.getSerializeList(clientInfo, v);
				
				for(KeyValuePair pair : list){
					if(!pair.isNeedSerialize(clientInfo, v)) {
						continue;
					}
					
					try {			 
						PropertyDescriptor pd = new PropertyDescriptor(pair.getKey(),object.getClass());
						Method method = pd.getReadMethod(); 
						Object value = method.invoke(object);
						 
						//Object value=BeanUtils.getProperty(object, entry.getKey());  
						writeObjectMember(this.output, pair.getValue());
						this.encoder(value, clientInfo, v);								
					} catch (Exception e) {						
						log.error("encode bean error!",e);				
					}	
				}				
				this.writeObjectEnd();
			}			
		} catch (IOException ex) {
		}
	}
 

	public static void writeNull(DataOutputStream output) throws IOException{
		output.write(DPBinaryProtocol.BIN_NULL_VALUE);		
	}
	private void writeNull() throws IOException {
		writeNull(this.output);
	}
	 
	
	public static void writeBoolean(DataOutputStream output,boolean value) throws IOException {
		if (value) {
			output.write(DPBinaryProtocol.BIN_BOOLEAN_TRUE);
		} else {
			output.write(DPBinaryProtocol.BIN_BOOLEAN_FALSE);
		}
	}	
	private void writeBoolean(boolean value) throws IOException {
		writeBoolean(this.output,value);
	}
	
	
	public static void writeInt(DataOutputStream output, int value) throws IOException{
		output.write(DPBinaryProtocol.BIN_INTEGER_TYPE);
		output.writeInt(value);		
	}
	private void writeInt(int value) throws IOException {
		writeInt(this.output,value);	 
	}
	 
	public static void writeLong(DataOutputStream output, long value) throws IOException{
        Cat.logEvent("INVALID_METHOD_1", "com.dianping.api.netio.DPBinarySerializer.writeLong(java.io.DataOutputStream,long)");
        output.write(DPBinaryProtocol.BIN_LONG_TYPE);
		output.writeLong(value);
	}
	private void writeLong(long value) throws IOException {
        Cat.logEvent("INVALID_METHOD_4", "com.dianping.api.netio.DPBinarySerializer.writeLong(long)");
        writeLong(this.output,value);
	}

	
	public static void writeDouble(DataOutputStream output, double value) throws IOException {
		output.write((byte) (DPBinaryProtocol.BIN_DOUBLE_TYPE));
		output.writeDouble(value);
	}
	private void writeDouble(double value) throws IOException {
		writeDouble(this.output,value);
	}
   
	public static void writeUTCDate(DataOutputStream output, Date dateTime) throws IOException {
        Cat.logEvent("INVALID_METHOD_5", "com.dianping.api.netio.DPBinarySerializer.writeUTCDate(java.io.DataOutputStream,java.util.Date)");
        int seconds =  (int) (dateTime.getTime()/1000);
		output.write((byte) (DPBinaryProtocol.BIN_UTCDATE_TYPE));
		output.writeInt(seconds);
	}

	private void writeUTCDate(Date dateTime) throws IOException {
        Cat.logEvent("INVALID_METHOD_1", "com.dianping.api.netio.DPBinarySerializer.writeUTCDate(java.util.Date)");
        writeUTCDate(output,dateTime);
	}

	public static void writeString(DataOutputStream output, String value) throws IOException {
		output.write((byte) (DPBinaryProtocol.BIN_STRING_TYPE));
		byte[] buffer = value.getBytes("utf-8");
		int length = buffer.length;
		output.writeShort(length);
		output.write(buffer, 0, length);
	}
	
	private void writeString(String value) throws IOException {
		writeString(this.output, value);
	}

	public static void writeArray(DataOutputStream output, int count) throws IOException {
		output.write((byte) (DPBinaryProtocol.BIN_ARRAY_TYPE));	
		output.writeShort(count);
	}
	
	private void writeArray(int count) throws IOException {
		writeArray(this.output,count);
	}

	
	public static void writeObjectBegin(DataOutputStream output, int objectID) throws IOException {
		output.write((byte) (DPBinaryProtocol.BIN_OBJECT_TYPE_BEGIN));		 
		output.writeShort(objectID);
	}
	 
	
	public static void writeObjectMember(DataOutputStream output, int fieldID) throws IOException {
		output.write((byte) (DPBinaryProtocol.BIN_OBJECT_MEMBER));		 
		output.writeShort(fieldID); 
	}
	 
	public static void writeObjectEnd(DataOutputStream output) throws IOException {
		output.write((byte) (DPBinaryProtocol.BIN_OBJECT_TYPE_END));
	}
	private void writeObjectEnd() throws IOException {
		writeObjectEnd(this.output);
	}

}
