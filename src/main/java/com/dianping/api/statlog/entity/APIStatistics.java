package com.dianping.api.statlog.entity;

import java.io.UnsupportedEncodingException;

import com.dianping.cat.Cat;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

public class APIStatistics {
	
	private static Logger log = LoggerFactory.getLogger(APIStatistics.class);

	private static Logger statLog = LoggerFactory.getLogger("api-Statistics-log");

	private StringBuilder buffer = new StringBuilder(4096);

	public void addElement(String key, String value) {
        Cat.logEvent("INVALID_METHOD_3", "com.dianping.api.statlog.entity.APIStatistics.addElement(java.lang.String,java.lang.String)");
        if (buffer.length() > 0) {
			buffer.append("&");
		}
		buffer.append(generateElement(key, value));
	}

	public static String generateElement(String key, String value) {
        Cat.logEvent("INVALID_METHOD_4", "com.dianping.api.statlog.entity.APIStatistics.generateElement(java.lang.String,java.lang.String)");
        if (value == null) {
			value = "";
		}
		if (key == null) {
			key = "";
		}
		try {
			return java.net.URLEncoder.encode(key, "utf-8") + "=" + java.net.URLEncoder.encode(value, "utf-8");
		} catch (UnsupportedEncodingException e) {
			log.error("url encode error, input key: " + key + ",input value: " + value, e);
			return "";
		}
	}

	public String getStatString() {
        Cat.logEvent("INVALID_METHOD_5", "com.dianping.api.statlog.entity.APIStatistics.getStatString()");
        return buffer.toString();
	}

	public void output() {
        Cat.logEvent("INVALID_METHOD_4", "com.dianping.api.statlog.entity.APIStatistics.output()");
        statLog.info(getStatString());
	}
}
