package com.dianping.api.statlog.service;

import java.io.BufferedReader;
import java.io.File;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.util.HashMap;
import java.util.HashSet;
import java.util.Map;
import java.util.Set;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

import com.dianping.api.domain.Version;
import com.dianping.api.framework.BaseService;
import com.dianping.api.statlog.entity.APIStatistics;
import com.dianping.api.statlog.entity.Result;
import com.dianping.api.statlog.entity.SourceSource2;
import com.dianping.api.statlog.entity.StatLogConstant;
import com.dianping.api.util.LogUtil;
import com.dianping.api.util.StringUtil;
import com.dianping.cat.Cat;

public class ApilogToClientService extends BaseService{
	
	private static final Pattern ANDROID_REGEX  = Pattern.compile("\\(com.dianping.v1 ([0-9\\.]+)(.*);(?:.*)\\)", Pattern.CASE_INSENSITIVE);
	private Version v47 = new Version("4.7");
	private Version v48 = new Version("4.8");
	public static final String[] GENERATE_PARAMETERS = {
		 "trainid", "deviceid", "sessionid", "version", "source", "source2", "addtime",  
		 "bin", "parametersmap"
	};
	private Set<String> generateParametersSet = new HashSet<String>();
	private Map<String, SourceSource2> sourceSource2Map = new HashMap<String, SourceSource2>();
	
	public void init(){
        Cat.logEvent("INVALID_METHOD_5", "com.dianping.api.statlog.service.ApilogToClientService.init()");
        initParametersSet();
		initSourceMap();
	}
	
	private void initParametersSet(){
        Cat.logEvent("INVALID_METHOD_3", "com.dianping.api.statlog.service.ApilogToClientService.initParametersSet()");
        for(String s:GENERATE_PARAMETERS){
			generateParametersSet.add(s);
		}
	}
	
	private void initSourceMap(){
        Cat.logEvent("INVALID_METHOD_2", "com.dianping.api.statlog.service.ApilogToClientService.initSourceMap()");
		/*
		try{
			InputStream input = Thread.currentThread().getContextClassLoader()
		    .getResourceAsStream("constant" + File.separator + "omission_client.properties");
			BufferedReader in = new BufferedReader(new InputStreamReader(input));
			String line = null;
			while((line = in.readLine())!=null){
				line = line.toLowerCase();
				if(line==null||"".equals(line)){
					continue;
				}
				String[] s = line.split(",");
				String source = s[0].trim();
				String source2 = s[1].trim();
				SourceSource2 sourceSource2 = new SourceSource2(source, source2);
				sourceSource2Map.put(source + " " + source2, sourceSource2);
			}
			in.close();
		} catch(Exception e){
			log.error("cant't get source source2 config", e);
		}
		*/
		try(InputStream input = Thread.currentThread().getContextClassLoader()
				.getResourceAsStream("constant" + File.separator + "omission_client.properties");
			BufferedReader in = new BufferedReader(new InputStreamReader(input))){
			String line = null;
			while((line = in.readLine())!=null){
				line = line.toLowerCase();
				if(line==null||"".equals(line)){
					continue;
				}
				String[] s = line.split(",");
				String source = s[0].trim();
				String source2 = s[1].trim();
				SourceSource2 sourceSource2 = new SourceSource2(source, source2);
				sourceSource2Map.put(source + " " + source2, sourceSource2);
			}
		} catch(Exception e){
			log.error("cant't get source source2 config", e);
		}
	}
	
	public boolean lineJudge(String line, String logDateYMD){
        Cat.logEvent("INVALID_METHOD_4", "com.dianping.api.statlog.service.ApilogToClientService.lineJudge(java.lang.String,java.lang.String)");
        //解析参数
		Result result = new Result();
		Map<String, String> parameterMap = LogUtil.urlEncodedLineparse(line, result);
		if(result.getValue()<=0){
			return false;
		}
		//判定useragent
		String useragent = parameterMap.get("useragent");
		if(StringUtil.isNullOrEmpty(useragent)){
			return false;
		}
		String source2 = null;
		Matcher m = ANDROID_REGEX.matcher(useragent);
		if(StringUtil.isNullOrEmpty(useragent)||!m.find()||!(m.groupCount()==2))
				return false;
		// Mapi 1.0(com.dianping.v1 4.5 androidmarket Nexus_One;Android 2.2)
		String inBrackets_left = useragent.substring(useragent.indexOf('(') + 1);
		String inBrackets = inBrackets_left.substring(0, inBrackets_left.indexOf(';')).trim();
		String[] inBString = inBrackets.split(" ");
		if(inBString.length!=4)
			return false;
		source2 = inBString[3];
		String versionString = inBString[1];
		String source = inBString[2];
		Version version = new Version(versionString);
		
		//判定client
		String clientString = parameterMap.get("client");
		if(StringUtil.isNullOrEmpty(clientString)){
			return false;
		}
		int client = LogUtil.parseInt(clientString);
		if(client!=1){
			return false;
		}
		
		//判定 version
		if(!(Version.compareTo(version, v47)>=0 && Version.compareTo(version, v48)<0)){
			return false;
		}
		//判定 source 和 source2
		if(StringUtil.isNullOrEmpty(source) || StringUtil.isNullOrEmpty(source2)){
			return false;
		}
		SourceSource2 sourceSource2 = sourceSource2Map.get(source + " " + source2);
		if(sourceSource2==null || !sourceSource2.getSource().equals(source) || !sourceSource2.getSource2().equals(source2)){
			return false;
		}
		//判定config.bin
		String bin = parameterMap.get("bin");
		if(!StringUtil.isNullOrEmpty(bin) && bin.equals("config.bin")){
			return false;
		}
		return true;
	}
	
	//只针对android, 4.7% 版本， 并需要相关的 source 和  source2(phoneModel)
	public String apiLogToClientLog(String line, String dateStringYMD){
        Cat.logEvent("INVALID_METHOD_2", "com.dianping.api.statlog.service.ApilogToClientService.apiLogToClientLog(java.lang.String,java.lang.String)");
        //取得参数列表
		Result result = new Result();
		Map<String, String> parameterMap = LogUtil.urlEncodedLineparse(line, result);
		if(result.getValue()<=0)
			return null;
		
		//检验是否有useragent
		String useragent = parameterMap.get("useragent");
		if(StringUtil.isNullOrEmpty(useragent))
			return null;
		
		//从client 得到 trainid
		String clientString = parameterMap.get("client");
		int client = LogUtil.parseInt(clientString);
		int trainid = LogUtil.clientToTrainid(client);
		if(trainid!=7)
			return null;
		
		//从useragent 提取 version, source, phoneModel(source2)
		Matcher m = ANDROID_REGEX.matcher(useragent);
		if(StringUtil.isNullOrEmpty(useragent)||!m.find()||!(m.groupCount()==2))
				return null;
		// 举例：Mapi 1.0(com.dianping.v1 4.5 androidmarket Nexus_One;Android 2.2)
		String inBrackets_left = useragent.substring(useragent.indexOf('(') + 1);
		String inBrackets = inBrackets_left.substring(0, inBrackets_left.indexOf(';')).trim();
		String[] inBString = inBrackets.split(" ");
		if(inBString.length!=4)
			return null;
		String version = inBString[1];
		String source = inBString[2];
		String source2 = inBString[3];
		
		//取得deviceid, starttime(时间格式：yyyy-MM-dd hh:mm:ss), 并且根据deviceid 和 starttime 生成 sessionid
		String deviceid = parameterMap.get("deviceid");
		String sessionid = deviceid + dateStringYMD;
		//sessionid 长度限制为36
		if(!StringUtil.isNullOrEmpty(deviceid) && deviceid.length()>25){
			sessionid = deviceid.substring(0, 25) + "_" + dateStringYMD;
		}
		
		//生成url 相关的参数
		String bin = parameterMap.get("bin");
		String urlParameterString = parameterMap.get("parametersmap");
		String url = StatLogConstant.URL_DOMAIN + bin;
		if(!StringUtil.isNullOrEmpty(urlParameterString)){
			url = url + "?" + urlParameterString;
		}
		
		//放入参数
		//首先放入 trainid, deviceid, sessionid(使用deviceid代替), version, source, source2, url   这些参数需要处理
		APIStatistics encoderLine = new APIStatistics();
		encoderLine.addElement("trainid", String.valueOf(trainid));
		encoderLine.addElement("deviceid", deviceid);
		encoderLine.addElement("sessionid", sessionid);
		encoderLine.addElement("version", version);
		encoderLine.addElement("source", source);
		encoderLine.addElement("source2", source2);
		encoderLine.addElement("addtime", parameterMap.get("starttime"));
		encoderLine.addElement("url", url);
		
		//放入其他参数，这些参数不在 GENERATE_PARAMETERS 中
		for(String key:parameterMap.keySet()){
			if(!generateParametersSet.contains(key)){
				String value = parameterMap.get(key);
				if(!StringUtil.isNullOrEmpty(value)){
					encoderLine.addElement(key, value);
				}
			}
		}
		return encoderLine.getStatString();
	}
	
}
