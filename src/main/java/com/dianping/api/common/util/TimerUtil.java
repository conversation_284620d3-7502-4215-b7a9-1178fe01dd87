package com.dianping.api.common.util;

import com.dianping.cat.Cat;

import java.util.Timer;

public final class TimerUtil {
	private static Timer dbTimer = new Timer("update data from DB", true);
	private static Timer uriTimer = new Timer("update data from uri", true);
	
	private TimerUtil() {
	}
	
	public static Timer getDBTimerInstance() {
        Cat.logEvent("INVALID_METHOD_1", "com.dianping.api.common.util.TimerUtil.getDBTimerInstance()");
        return dbTimer;
	}
	
	public static Timer getUriTimerInstance() {
        Cat.logEvent("INVALID_METHOD_4", "com.dianping.api.common.util.TimerUtil.getUriTimerInstance()");
        return uriTimer;
	}
	
}
