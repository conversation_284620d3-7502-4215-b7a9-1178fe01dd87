package com.dianping.api.common.util;

import com.dianping.cat.Cat;
import org.apache.commons.mail.HtmlEmail;

public final class APIMailHelper {
	
	private APIMailHelper() {
	}

	public static void sendHtmlEmail(String subject, String body, String fromAddress, String fromName, String[] to) throws Exception {
        Cat.logEvent("INVALID_METHOD_2", "com.dianping.api.common.util.APIMailHelper.sendHtmlEmail(java.lang.String,java.lang.String,java.lang.String,java.lang.String,java.lang.String[])");
        HtmlEmail email = new HtmlEmail();
		email.setHostName("mail.51ping.com");
		email.setSmtpPort(25);
		email.setAuthentication("<EMAIL>", "NaGiosMail");
		email.setCharset("UTF-8");
		email.setFrom(fromAddress, fromName);
		for (String toAddr : to) {
			email.addTo(toAddr);
		}
		email.setSubject(subject);
		if (body != null && !body.isEmpty()) {
			email.setHtmlMsg(body);
			email.setTextMsg("Your email client does not support HTML messages.");
		}

		try {
			email.send();
		} catch (Exception e) {
			throw new Exception(e);
		}
	}

}
