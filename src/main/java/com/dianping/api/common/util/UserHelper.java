package com.dianping.api.common.util;

import java.io.UnsupportedEncodingException;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

import com.dianping.cat.Cat;
import org.apache.commons.lang3.StringUtils;

import com.dianping.api.netio.DPBinaryEncrypt;
import com.dianping.api.servlet.TokenNotValidException;
import com.dianping.api.util.DataPair;
import com.dianping.api.util.StringUtil;
import com.dianping.avatar.log.AvatarLogger;
import com.dianping.avatar.log.AvatarLoggerFactory;

public final class UserHelper {

	private static final String emailStr = "^([\\w-\\.]+)@((\\[[0-9]{1,3}\\.[0-9]{1,3}\\.[0-9]{1,3}\\.)|(([\\w-]+\\.)+))([a-zA-Z]{2,4}|[0-9]{1,3})(\\]?)$";//"\\w+([-+.]\\w+)*@\\w+([-.]\\w+)*\\.\\w+([-.]\\w+)*";
	private static final String cellStr = "^1[0-9]{10}$";//"^[1][0-9]+\\d{9}";
	private static final String nickNameStr = "^[\\w\\u4E00-\\u9FA5\\uF900-\\uFA2D]*$";
	private static final Pattern emailRegex = Pattern.compile(emailStr, Pattern.CASE_INSENSITIVE);
	private static final Pattern cellRegex = Pattern.compile(cellStr, Pattern.CASE_INSENSITIVE);
	private static final Pattern nickNameRegex = Pattern.compile(nickNameStr, Pattern.CASE_INSENSITIVE);
	private static final Pattern JFfUSER_REGEX = Pattern.compile("^(\\d{12})$", Pattern.CASE_INSENSITIVE); //是否为12位纯数字的会员卡号 
	
	private static final String emptyGif = "/u/empty.gif";
	private static final String nofaceGif = "/comm/userface/noface.gif";
	private static final String[] emptyUserFace = new String[] { emptyGif, nofaceGif };

	private static final String dpfileUrl = "http://p.dpfile.com";//FIXME

	private static AvatarLogger LOG = AvatarLoggerFactory.getLogger(UserHelper.class);
	
	private UserHelper() {
	}
	
	public static String formatUserFace(String userFace) {
        Cat.logEvent("INVALID_METHOD_2", "com.dianping.api.common.util.UserHelper.formatUserFace(java.lang.String)");

        if (StringUtil.isNullOrEmpty(userFace)) {
			return "";
		}
		userFace = userFace.trim().toLowerCase();
		if (!StringUtils.startsWith(userFace, "http://")) {
			userFace = dpfileUrl + userFace;
		}

		Boolean any = false;
		for (int i = 0; i < emptyUserFace.length; i++) {
			if (StringUtils.containsIgnoreCase(userFace, emptyUserFace[i])) {
				any = true;
			}
		}
		return any ? "" : userFace.toLowerCase();
	}

	public static String createToken(long userID) {
        Cat.logEvent("INVALID_METHOD_3", "com.dianping.api.common.util.UserHelper.createToken(long)");
        if (userID > 0) {
			Date now = new Date(System.currentTimeMillis());
			SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
			String token = Long.toString(userID) + "|" + formatter.format(now);
			byte[] bytes;
			try {
				bytes = DPBinaryEncrypt.encryptUserToKen(token.getBytes("UTF-8"));
			} catch (UnsupportedEncodingException e) {// it won't happen 
				bytes = null;
			}
			if (bytes != null && bytes.length > 0) {
				return DPBinaryEncrypt.toHex(bytes);
			}
		}
		return "";
	}

	public static int buildFeedFlag(List<Integer> feedTypes) {
        Cat.logEvent("INVALID_METHOD_5", "com.dianping.api.common.util.UserHelper.buildFeedFlag(java.util.List)");
        int result = 0;
		if (feedTypes != null && feedTypes.size() > 0) {
			for (int feedtype : feedTypes) {
				result += feedtype;
			}
		}
		return result;
	}

	public static int parseToken(String token) {
        Cat.logEvent("INVALID_METHOD_1", "com.dianping.api.common.util.UserHelper.parseToken(java.lang.String)");
        return parseToken(token, false);
	}
	
	public static int parseToken(String token, boolean mustHave) {
        Cat.logEvent("INVALID_METHOD_1", "com.dianping.api.common.util.UserHelper.parseToken(java.lang.String,boolean)");
        if (!StringUtils.isBlank(token)) {
			byte[] bytes = DPBinaryEncrypt.fromHex(token);
			byte[] data = null;
			try {
				data = DPBinaryEncrypt.decryptUserToken(bytes);
			} catch (Exception e) {
				LOG.warn("decryptUserToken failed" + token, e);
			}
			if (data != null && data.length > 0) {
				try {
					String temp = new String(data, "UTF-8");
					String[] user = temp.trim().split("\\|");
					if (user != null && user.length == 2) {
						return Integer.parseInt(user[0]);
					}
				} catch (UnsupportedEncodingException e) {
					LOG.warn("parse user token wrong" + token, e);
				}
			}
		}
		
		if (mustHave) {
			throw new TokenNotValidException();
		} else {
			return 0;
		}
	}
	
	public static DataPair<Integer, String> parseFullToken(String token) {
        Cat.logEvent("INVALID_METHOD_5", "com.dianping.api.common.util.UserHelper.parseFullToken(java.lang.String)");
        if (!StringUtil.isNullOrEmpty(token)) {
			byte[] bytes = DPBinaryEncrypt.fromHex(token);
			byte[] data = null;
			try {
				data = DPBinaryEncrypt.decryptUserToken(bytes);
			} catch (Exception e) {
				LOG.warn("decryptUserToken failed" + token, e);
			}
			if (data != null && data.length > 0) {
				try {
					String temp = new String(data, "UTF-8");
					String[] user = temp.trim().split("\\|");
					if (user != null && user.length == 2
							&& user[0] != null && user[1] != null && !user[1].isEmpty()) {
						return new DataPair<Integer, String>(Integer.parseInt(user[0]), user[1]);
					} else {
						return null;
					}
				} catch (UnsupportedEncodingException e) {
					LOG.warn("parse user token wrong" + token, e);
				}
			}
		}
		return null;
	}

	public static boolean isEmail(String email) {
        Cat.logEvent("INVALID_METHOD_4", "com.dianping.api.common.util.UserHelper.isEmail(java.lang.String)");
        Matcher m = emailRegex.matcher(email);
		return m.find();
	}

	public static boolean isCellPhone(String cell) {
        Cat.logEvent("INVALID_METHOD_3", "com.dianping.api.common.util.UserHelper.isCellPhone(java.lang.String)");
        Matcher m = cellRegex.matcher(cell);
		return m.find();
	}
	
	public static boolean isJFUser(String cardNo) {
        Cat.logEvent("INVALID_METHOD_2", "com.dianping.api.common.util.UserHelper.isJFUser(java.lang.String)");
        Matcher m = JFfUSER_REGEX.matcher(cardNo);
		return m.find();
	}

	public static boolean validateNickname(String nickName) {
        Cat.logEvent("INVALID_METHOD_1", "com.dianping.api.common.util.UserHelper.validateNickname(java.lang.String)");
        if(nickName == null || nickName.equals("")) {
			return false;
		} else {
			Matcher m = nickNameRegex.matcher(nickName);
			return m.find();
		}
	}
	
	// 4.6  phoneNo to phToken
	
	public static String createPhToken(String phone) {
        Cat.logEvent("INVALID_METHOD_4", "com.dianping.api.common.util.UserHelper.createPhToken(java.lang.String)");
        if (!StringUtil.isNullOrEmpty(phone)) {
			Date now = new Date(System.currentTimeMillis());
			SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
			String token = phone + "|" + formatter.format(now);		 
			byte[] bytes;
			try {
				bytes = DPBinaryEncrypt.encryptUserToKen(token.getBytes("UTF-8"));
			} catch (UnsupportedEncodingException e) {// it won't happen 
				bytes = null;
			}
			if (bytes != null && bytes.length > 0) {
				return DPBinaryEncrypt.toHex(bytes);
			}
		}
		return "";
	}
	
	public static String parsePhToken(String phToken) {
        Cat.logEvent("INVALID_METHOD_5", "com.dianping.api.common.util.UserHelper.parsePhToken(java.lang.String)");
        if (!StringUtil.isNullOrEmpty(phToken)) {
			byte[] bytes = DPBinaryEncrypt.fromHex(phToken);
			byte[] data = null;
			try {
				data = DPBinaryEncrypt.decryptUserToken(bytes);
			} catch (Exception e) {
				LOG.error("parse phToken wrong" + phToken);
			}
			if (data != null && data.length > 0) {
				try {
					String temp = new String(data, "UTF-8");
					String[] s = temp.trim().split("\\|");
					if (s != null && s.length == 2) {
						return s[0];
					}
				} catch (UnsupportedEncodingException e) {
					LOG.error("parse phToken wrong" + phToken, e);
				}
			}

		}
		return "";
	}
}
