/**
 * 
 */
package com.dianping.api.common.util;

import com.dianping.api.domain.Enum.ClientType;
import com.dianping.api.domain.ClientInfo;
import com.dianping.api.domain.Version;

/**
 * <AUTHOR>
 *
 */
public class KeyValuePair {

	/**
	 * 字段的javabean属性名字 
	 */
	private final String key;
	
	/**
	 * 字段的encode编码
	 */
	private final int value;
	
	/**
	 * if it is not null, client type should match the value
	 */
	private ClientType[] clientTypes; 
	
	/**
	 * if it is not null, client version should larger or equal than the value 
	 */
	private Version version;
	
	public KeyValuePair(String key,int value){
		this.key=key;
		this.value=value;
	}
	
	/**
	 * both clientType and version should meet the condition if their value is not null 
	 * @param key
	 * @param value
	 * @param clientType
	 * @param version
	 */
	public KeyValuePair(String key,int value, ClientType[]clientType, Version version){
		this.key=key;
		this.value=value;
		this.clientTypes = clientType;
		this.version = version;
	}
	
	
	
	public String getKey(){
		return this.key;
	}
	
	public int getValue(){
		return this.value;
	}
	
	public String toString(){
		return  " key= "+key+" value= "+value;
	}
	
	public boolean isNeedSerialize(ClientInfo clientInfo, Version v){
		if(this.clientTypes != null){
			boolean isOk = false;
			for(ClientType c : this.clientTypes){
				if(c.value == clientInfo.getClient().value){
					isOk = true;
					break;
				}
			}
			if(!isOk)
				return false;
		}
		
		if(this.version != null && Version.compareTo(v, this.version)<0){
			return false;
		}
		 
		return true;
	}
	
	/**
	 * 该方法返回的数据如果不是null会取代通过反射得到的对应属性的值 
	 * @param clientType
	 * @param v
	 * @return
	 */
	/*
	public Object getFieldValue(ClientType clientType, Version v ){
		return null;
	}*/
}
