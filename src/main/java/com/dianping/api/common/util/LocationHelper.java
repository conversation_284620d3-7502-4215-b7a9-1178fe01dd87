 
package com.dianping.api.common.util;

import java.io.UnsupportedEncodingException;
import java.net.URI;
import java.net.URISyntaxException;
import java.net.URLEncoder;
import java.security.InvalidKeyException;
import java.security.NoSuchAlgorithmException;
import java.util.ArrayList;
import java.util.List;

import javax.crypto.Mac;
import javax.crypto.spec.SecretKeySpec;

import com.dianping.cat.Cat;
import org.apache.commons.codec.binary.Base64;

import com.dianping.api.common.entity.Point;

/**
 * 
 * <AUTHOR>
 *
 */
public final class LocationHelper {

	public static double MIN_LATITUDE = Double.valueOf("-90.0000");
	public static double MAX_LATITUDE = Double.valueOf("90.0000");
	public static double MIN_LONGITUDE = Double.valueOf("-180.0000");
	public static double MAX_LONGITUDE = Double.valueOf("180.0000");
	
	private LocationHelper() {
	}
	
	public static boolean isValidLatitude(double latitude) {
        Cat.logEvent("INVALID_METHOD_2", "com.dianping.api.common.util.LocationHelper.isValidLatitude(double)");
        return (latitude >= MIN_LATITUDE && latitude <= MAX_LATITUDE);
	}
	
	public static boolean isValidLongitude(double longitude) {
        Cat.logEvent("INVALID_METHOD_4", "com.dianping.api.common.util.LocationHelper.isValidLongitude(double)");
        return (longitude >= MIN_LONGITUDE && longitude <= MAX_LONGITUDE);
	}
	
	public static boolean isValidPoint(Point point) {
        Cat.logEvent("INVALID_METHOD_4", "com.dianping.api.common.util.LocationHelper.isValidPoint(com.dianping.api.common.entity.Point)");
        return point != null && isValidPoint(point.getLat(), point.getLng());
	}

	public static boolean isValidPoint(double lat, double lng) {
        Cat.logEvent("INVALID_METHOD_2", "com.dianping.api.common.util.LocationHelper.isValidPoint(double,double)");
        return (Double.compare(lat, 0.0) != 0 || Double.compare(lng, 0.0) != 0) && isValidLatitude(lat) && isValidLongitude(lng);
	}
	
	public static final int IMPL_LOCVERSION = 6;
	
	public static final String ACTION_LOC = "loc";
	public static final String ACTION_REFRESH = "refresh";
	public static final String ACTION_SELECT = "select";
	
	private static final double EARTHRADIUS = 6371000;
	
	private static  byte[] googleWebServiceKey = null;
	
	public static final String BASE_REVERSEGEOCODING_URL1 = "http://ditu.google.cn/maps/api/geocode/xml?latlng=#0#,#1#&language=zh-CN&sensor=true&client=free-dianping";
	public static final String BASE_REVERSEGEOCODING_URL2 = "http://maps.google.com/maps/api/geocode/xml?latlng=#0#,#1#&language=zh-CN&sensor=true&client=free-dianping";										 
	public static final String BASE_REVERSEGEOCODING_URL3 = "http://location.dianping.com/maps/api/geocode/xml?latlng=#0#,#1#&language=zh-CN&sensor=true&client=free-dianping"; 
	public static  String baseReverseGeocodingUrl = BASE_REVERSEGEOCODING_URL1;
	
	//一天请求限制1000次的key
//	private static String searchLocationUrl1 = "https://maps.googleapis.com/maps/api/place/search/json?location=#0#,#1#&radius=#2#&keyword=#3#&language=zh-CN&sensor=false&key=AIzaSyBa3lfHgYbVi-CkRhkVlfCDAbW-lYwretU";
	
	//一天请求限制1000 000次的key
	private static final String SEARCH_LOCATION_URL = "https://maps.googleapis.com/maps/api/place/search/json?location=#0#,#1#&radius=#2#&keyword=#3#&language=zh-CN&sensor=false&key=AIzaSyCmrxRkKp8Dm3N3XKBOGtaqVZyvVl-t6Xg";
	
	static {
		googleWebServiceKey = Base64.decodeBase64("ijRR93ybVLxz5caQr092OYKxqqU=");
	}
	
	public static int calculateDistance(Point a, Point b){
        Cat.logEvent("INVALID_METHOD_4", "com.dianping.api.common.util.LocationHelper.calculateDistance(com.dianping.api.common.entity.Point,com.dianping.api.common.entity.Point)");
        double lat1 = a.getLat() / 180 * Math.PI;
		double lon1 = a.getLng() / 180 * Math.PI;
		double lat2 = b.getLat() / 180 * Math.PI;
		double lon2 = b.getLng() / 180 * Math.PI;
		double dlat = lat2 - lat1;
		double dlon = lon2 - lon1;
		double tmpA = Math.sin(dlat / 2) * Math.sin(dlat / 2) + Math.cos(lat1)
				* Math.cos(lat2) * Math.sin(dlon / 2) * Math.sin(dlon / 2);
		double tempC = 2.0 * Math.atan2(Math.sqrt(tmpA), Math.sqrt(1.0 - tmpA));
		return (int)(Math.ceil(EARTHRADIUS * tempC));
		
	}
	
	public static String getReverseGeocodingUrl(double lat, double lng) {
        Cat.logEvent("INVALID_METHOD_2", "com.dianping.api.common.util.LocationHelper.getReverseGeocodingUrl(double,double)");
        return getReverseGeocodingUrl(lat, lng, 0);
	}
	
	public static String getSearchLocationUrlWithSignatrue(double lat, double lng, int range, String keyword) throws UnsupportedEncodingException {
        Cat.logEvent("INVALID_METHOD_1", "com.dianping.api.common.util.LocationHelper.getSearchLocationUrlWithSignatrue(double,double,int,java.lang.String)");

        // 因为 keyword  可能是中文，  httpclient  getMethod  参数  url 不支持 中文 需要encode
		keyword = URLEncoder.encode(keyword, "utf-8");
		return SEARCH_LOCATION_URL.replaceAll("#0#", lat+"").replaceAll("#1#",lng+"").replaceAll("#2#", range+"").replaceAll("#3#", keyword);
		
	}
	
	public static String getReverseGeocodingUrl(double lat, double lng, int type) {
        Cat.logEvent("INVALID_METHOD_2", "com.dianping.api.common.util.LocationHelper.getReverseGeocodingUrl(double,double,int)");
        try {
			URI uri = null;
			if(type == 0) {
				uri = new URI(baseReverseGeocodingUrl.replaceAll("#0#", lat+"").replaceAll("#1#",lng+""));
			} else {
				uri = new URI(BASE_REVERSEGEOCODING_URL3.replaceAll("#0#", lat+"").replaceAll("#1#",lng+""));
			}
			
			String s = uri.getPath()+"?"+uri.getQuery();
			
			byte[] encodedPathAndQueryBytes = s.getBytes("US-ASCII");
			//uri.LocalPath="/maps/api/geocode/xml"	string
			//uri.Query="?latlng=34.45,124.68&language=zh-CN&sensor=true&&client=free-dianping"	string

			SecretKeySpec signingKey = new SecretKeySpec(googleWebServiceKey, "HmacSHA1");
			Mac mac = Mac.getInstance("HmacSHA1");
			mac.init(signingKey);
			byte[] rawHmac = mac.doFinal(encodedPathAndQueryBytes);			
			String signature = new String(Base64.encodeBase64(rawHmac));
			signature = signature.replace('+','-').replace('/','_');
			return uri.getScheme() + "://" + uri.getHost() + uri.getPath() + "?" + uri.getQuery() + "&signature=" + signature;
		} catch (UnsupportedEncodingException e) {
			
		} catch (NoSuchAlgorithmException e) {
			
		} catch (InvalidKeyException e) {
				
		} catch (URISyntaxException e) {
			
		}
		return "";
	}
	
	public static List<Point> getRectangle(Point point, int range){
        Cat.logEvent("INVALID_METHOD_2", "com.dianping.api.common.util.LocationHelper.getRectangle(com.dianping.api.common.entity.Point,int)");
        Point leftBottom = new Point();
        Point rightTop = new Point();
        List<Point> result = new ArrayList<Point>();
        //采用平面算法
        double delta = (double)range * 180 / 6371000 / Math.PI;
        leftBottom.setLat( point.getLat() - delta);
        leftBottom.setLng(point.getLng() - delta);
        rightTop.setLat(point.getLat() + delta);
        rightTop.setLng(point.getLng()+delta);
        result.add(leftBottom);
        result.add(rightTop);
        return result;
	}
	

}
