/**
 * Project: api-war
 * 
 * File Created at 2011-6-20
 * $Id$
 * 
 * Copyright 2010 dianping.com.
 * All rights reserved.
 *
 * This software is the confidential and proprietary information of
 * Dianping Company. ("Confidential Information").  You shall not
 * disclose such Confidential Information and shall use it only in
 * accordance with the terms of the license agreement you entered into
 * with dianping.com.
 */
package com.dianping.api.common.util;

import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.PrintWriter;
import java.io.UnsupportedEncodingException;
import java.util.List;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

import javax.crypto.Cipher;
import javax.crypto.spec.IvParameterSpec;
import javax.crypto.spec.SecretKeySpec;

import com.dianping.cat.Cat;
import org.apache.commons.httpclient.HttpClient;
import org.apache.commons.httpclient.HttpException;
import org.apache.commons.httpclient.NameValuePair;
import org.apache.commons.httpclient.methods.PostMethod;
import org.apache.commons.lang.ArrayUtils;
import org.apache.commons.lang3.StringUtils;
import org.json.JSONObject;

import com.dianping.api.netio.DPBinaryEncrypt;
import com.dianping.api.util.StringUtil;
import com.dianping.avatar.log.AvatarLogger;
import com.dianping.avatar.log.AvatarLoggerFactory;

/**
 * 
 * <AUTHOR>
 *
 */
public final class UploadPhotoHelper {
	
	protected static AvatarLogger log = AvatarLoggerFactory.getLogger(UploadPhotoHelper.class);
	
	private static final String SPACE_INSTEAD = "~$#!^";
    private static final String SPACE2_INSTEAD = "^#~$!";
    private static final String SPACE2 = String.valueOf((char) 0);
    private static final String SPACELIST = " " + SPACE2;
	
	private static final String resultStr = "\\{c:800, m:\"(.*?)\"\\}";
	private static final Pattern resultRegex = Pattern.compile(resultStr, Pattern.CASE_INSENSITIVE);
	
	
	private static final byte[] SecurityKey = StringUtil.stringToBytesASCII("w@w.DianP1N9.C0m");
	private static final byte[] SecurityIV  = StringUtil.stringToBytesASCII("M0c>(n!pNAId>W2W");

	private UploadPhotoHelper() {
	}
	
	public static String encryptToken(String plainText) {
        Cat.logEvent("INVALID_METHOD_1", "com.dianping.api.common.util.UploadPhotoHelper.encryptToken(java.lang.String)");
        byte[] plainByte = null;
		try
		{			
			String string = StringUtils.strip(plainText, new String( new char[]{' ','\0'}));
			plainByte = string.getBytes("utf-8");			
		}catch(Exception e) //it won't happen
		{
			return "";
		}

		byte[] encrypted = 	DPBinaryEncrypt.encrypt(plainByte, SecurityKey, SecurityIV, DPBinaryEncrypt.NOPADING);	

		if (encrypted != null && encrypted.length > 0)
		{
			try
			{
				return DPBinaryEncrypt.toHex(encrypted);
			}
			catch(Exception e){
				log.error("encrypt erro for upload photo token", e);
			}
		}
		return "";
	}

	
	public static String praseUploadResult(String result) {
        Cat.logEvent("INVALID_METHOD_1", "com.dianping.api.common.util.UploadPhotoHelper.praseUploadResult(java.lang.String)");
        Matcher m = resultRegex.matcher(result);
		if(m.find()){
			if(m.groupCount()==1){
				return m.group(1);
			}
		}
		return "";
		//return m.find() && m.groupCount() == 1? m.group(1) : "";
	}
	
	public static int praseUploadJsonResult(String result) {
        Cat.logEvent("INVALID_METHOD_3", "com.dianping.api.common.util.UploadPhotoHelper.praseUploadJsonResult(java.lang.String)");
        try {
			JSONObject json = new JSONObject(result);
			String intStr = json.getString("picID");
			int picId = Integer.parseInt(decrypt(intStr));
			return picId;
		} catch (Exception e) {
			return -1;
		}
	}
	
	// avatar-combiz-encrypt
	public static String encrypt(String plainText) {
        Cat.logEvent("INVALID_METHOD_1", "com.dianping.api.common.util.UploadPhotoHelper.encrypt(java.lang.String)");
        if(!StringUtil.isNullOrEmpty(plainText)) {
        	plainText = plainText.replace(" ", SPACE_INSTEAD).replace(SPACE2, SPACE2_INSTEAD);
            try {
                byte[] bytes = plainText.getBytes("UTF-8");
                byte[] encryptedBytes = encrypt(bytes, SecurityKey, SecurityIV);
                if (!ArrayUtils.isEmpty(encryptedBytes)) {
                    return parseByte2Hex(encryptedBytes);
                }
            } catch (UnsupportedEncodingException e) {
                // do nothing
            }
        }
        return "";
    }
	
	// avatar-combiz-decrypt
	public static String decrypt(String cipherText) {
        Cat.logEvent("INVALID_METHOD_5", "com.dianping.api.common.util.UploadPhotoHelper.decrypt(java.lang.String)");
        if(!StringUtil.isNullOrEmpty(cipherText)) {
        	try {
                byte[] cipherBytes = parseHex2Byte(cipherText);
                byte[] decryptedBytes = decrypt(cipherBytes, SecurityKey, SecurityIV);
                if (!ArrayUtils.isEmpty(decryptedBytes)) {
                    String decrypted = new String(decryptedBytes, "UTF-8");
                    return StringUtils.strip(decrypted, SPACELIST).replace(SPACE_INSTEAD, " ").replace(SPACE2_INSTEAD,
                            SPACE2);
                }
            } catch (UnsupportedEncodingException e) {
                // do nothing
            }
        }
        return "";
    }
	
	private static byte[] encrypt(byte[] bytes, byte[] key, byte[] iv) {
        Cat.logEvent("INVALID_METHOD_4", "com.dianping.api.common.util.UploadPhotoHelper.encrypt(byte[],byte[],byte[])");
        try {
            Cipher cipher = Cipher.getInstance("AES/CBC/NoPadding");
            SecretKeySpec KeySpec = new SecretKeySpec(key, "AES");
            cipher.init(Cipher.ENCRYPT_MODE, KeySpec, new IvParameterSpec(iv));
            return cipher.doFinal(padWithZeros(bytes));
        } catch (Exception e) {
            return null;
        }
    }
	
	private static byte[] padWithZeros(byte[] input) {
        Cat.logEvent("INVALID_METHOD_1", "com.dianping.api.common.util.UploadPhotoHelper.padWithZeros(byte[])");
        int rest = input.length % 16;
        if (rest > 0) {
            byte[] result = new byte[input.length + (16 - rest)];
            System.arraycopy(input, 0, result, 0, input.length);
            return result;
        }
        return input;
    }
	
	private static String parseByte2Hex(byte[] bytes) {
        Cat.logEvent("INVALID_METHOD_5", "com.dianping.api.common.util.UploadPhotoHelper.parseByte2Hex(byte[])");
        StringBuffer sb = new StringBuffer();
        for (int i = 0; i < bytes.length; i++) {
            String hex = Integer.toHexString(bytes[i] & 0xFF);
            if (hex.length() == 1) {
                hex = '0' + hex;
            }
            sb.append(hex.toLowerCase());
        }
        return sb.toString();
    }
	
	private static byte[] decrypt(byte[] bytes, byte[] key, byte[] iv) {
        Cat.logEvent("INVALID_METHOD_2", "com.dianping.api.common.util.UploadPhotoHelper.decrypt(byte[],byte[],byte[])");
        try {
            Cipher cipher = Cipher.getInstance("AES/CBC/NoPadding");
            SecretKeySpec KeySpec = new SecretKeySpec(key, "AES");
            cipher.init(Cipher.DECRYPT_MODE, KeySpec, new IvParameterSpec(iv));
            return cipher.doFinal(bytes);
        } catch (Exception e) {
        	log.error(e, e);
            return null;
        }
    }
	
	private static byte[] parseHex2Byte(String hexText) {
        Cat.logEvent("INVALID_METHOD_2", "com.dianping.api.common.util.UploadPhotoHelper.parseHex2Byte(java.lang.String)");
        if (StringUtil.isNullOrEmpty(hexText)) {
            return null;
        }
        byte[] result = new byte[hexText.length() / 2];
        for (int i = 0; i < hexText.length() / 2; i++) {
            int high = Integer.parseInt(hexText.substring(i * 2, i * 2 + 1), 16);
            int low = Integer.parseInt(hexText.substring(i * 2 + 1, i * 2 + 2), 16);
            result[i] = (byte) (high * 16 + low);
        }
        return result;
    }
	
	
	@SuppressWarnings("deprecation")
	public static byte[] uploadFile(PostMethod postMethod, List<NameValuePair> header, String fileFeidName, String fileName, byte[] photoData) throws HttpException, IOException {
        Cat.logEvent("INVALID_METHOD_3", "com.dianping.api.common.util.UploadPhotoHelper.uploadFile(org.apache.commons.httpclient.methods.PostMethod,java.util.List,java.lang.String,java.lang.String,byte[])");

        //把参数 和 文件内容 按照一定格式 按行写入流
		ByteArrayOutputStream stream = new ByteArrayOutputStream();
		PrintWriter writer = new PrintWriter(stream);
        
		if(photoData!=null && photoData.length > 0) {
			String boundary = "-----------------------------" + Long.toHexString(System.currentTimeMillis());
			postMethod.setRequestHeader("Content-Type","multipart/form-data; boundary=" + boundary);
			
			for(NameValuePair p : header) {
				writer.println("--" + boundary);
				String key = "Content-Disposition: form-data; name=\"#0#\"";
				key = key.replaceAll("#0#", p.getName());
				writer.println(key);
				writer.println();
				writer.println(p.getValue());
			}
			writer.flush();
			writer.println("--" + boundary);
//			String keyFile = "Content-Disposition: form-data; name=\"#0#\";filename=\"#1#\"";
			String keyFile = "Content-Disposition: form-data; name=\"#0#\";filename=\"";
			keyFile = keyFile.replaceAll("#0#", fileFeidName);
			keyFile=keyFile+fileName+"\"";
			writer.println(keyFile);
			writer.println("Content-Type: application/octet-stream");
			writer.println();
			writer.flush();
			stream.write(photoData, 0, photoData.length);
			writer.flush();
			writer.println();
			writer.println("--" + boundary + "--");
			writer.flush();
			
		} else {
			postMethod.setRequestHeader("Content-Type","application/x-www-form-urlencoded");
			writer.println(getValuesByte(header));
		}
		
		//设置Post方法内容体
		byte[] uploadData = stream.toByteArray();
		InputStream body = new ByteArrayInputStream(uploadData);
		
		postMethod.setRequestBody(body);
		HttpClient httpClient = new HttpClient();
		httpClient.getHttpConnectionManager().getParams().setConnectionTimeout(1000);  
		httpClient.getHttpConnectionManager().getParams().setSoTimeout(1000);
		
		byte[] response = null;
		
		//执行 post 方法
		httpClient.executeMethod(postMethod);
    	
    	response = postMethod.getResponseBody();
    	
		
		return response;
	}
	
	private static String getValuesByte(List<NameValuePair> data) {
        Cat.logEvent("INVALID_METHOD_2", "com.dianping.api.common.util.UploadPhotoHelper.getValuesByte(java.util.List)");
        StringBuilder builder = new StringBuilder();
		for (NameValuePair p : data)
		{
			if (builder.length() > 0) { 
				builder.append("&");
			}
			String format = "#0#=#1#";
			format.replaceAll("#0#", urlEncode(p.getName())).replaceAll("#1#",urlEncode(p.getValue()));
			builder.append(format);
		}
		return builder.toString();
	}
	
	private static String urlEncode(String str) {
        Cat.logEvent("INVALID_METHOD_1", "com.dianping.api.common.util.UploadPhotoHelper.urlEncode(java.lang.String)");
        String result = null;
		try {
			byte[] bytes = str.getBytes("UTF-8");
			byte[] bytesNew = urlEncodeBytesToBytesInternal(bytes, 0, bytes.length, false);
			result = new String(bytesNew,"US-ASCII");
				
		} catch (UnsupportedEncodingException e) {
			
		}
		
		return result;
	}
	
	private static byte[] urlEncodeBytesToBytesInternal(byte[] bytes, int offset, int count, boolean alwaysCreateReturnValue) {
        Cat.logEvent("INVALID_METHOD_2", "com.dianping.api.common.util.UploadPhotoHelper.urlEncodeBytesToBytesInternal(byte[],int,int,boolean)");
        int num = 0;
		int num2 = 0;
		for (int i = 0; i < count; i++)
		{
			char ch = (char)bytes[offset + i];
			if (ch == ' ')
			{
				num++;
			}
			else if (!isSafe(ch))
			{
				num2++;
			}
		}
		if ((!alwaysCreateReturnValue && (num == 0)) && (num2 == 0))
		{
			return bytes;
		}
		byte[] buffer = new byte[count + (num2 * 2)];
		int num4 = 0;
		for (int j = 0; j < count; j++)
		{
			byte num6 = bytes[offset + j];
			char ch2 = (char)num6;
			if (isSafe(ch2))
			{
				buffer[num4++] = num6;
			}
			else if (ch2 == ' ')
			{
				buffer[num4++] = 43;
			}
			else
			{
				buffer[num4++] = 37;
				buffer[num4++] = (byte)intToHex((num6 >> 4) & 15);
				buffer[num4++] = (byte)intToHex(num6 & 15);
			}
		}
		return buffer;
	}
	
	private static boolean isSafe(char ch) {
        Cat.logEvent("INVALID_METHOD_4", "com.dianping.api.common.util.UploadPhotoHelper.isSafe(char)");
        if ((((ch >= 'a') && (ch <= 'z')) || ((ch >= 'A') && (ch <= 'Z'))) || ((ch >= '0') && (ch <= '9')))
		{
			return true;
		}
		switch (ch)
		{
			case '\'':
			case '(':
			case ')':
			case '*':
			case '-':
			case '.':
			case '_':
			case '!':
				return true;
		}
		return false;
	}
	
	private static char intToHex(int n) {
        Cat.logEvent("INVALID_METHOD_1", "com.dianping.api.common.util.UploadPhotoHelper.intToHex(int)");
        if (n <= 9)
		{
			return (char)(n + 48);
		}
		return (char)((n - 10) + 97);
	}
	
}
