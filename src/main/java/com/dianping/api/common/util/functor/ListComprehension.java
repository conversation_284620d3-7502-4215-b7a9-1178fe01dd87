package com.dianping.api.common.util.functor;

import com.dianping.cat.Cat;

import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;

/**
 * 锟斤拷ListComprehenssion.java锟斤拷实锟斤拷锟斤拷锟斤拷锟斤拷TODO 锟斤拷实锟斤拷锟斤拷锟斤拷
 * 
 * <AUTHOR> 2011-4-15 锟斤拷锟斤拷06:25:01
 */
public final class ListComprehension {

	private ListComprehension() {
	}
	
    /**
     * List Comprehession
     * <p>
     * If you have not ever heard about this notion, plz google it first
     * 锟斤拷锟斤拷涌诘募锟斤拷锟斤拷锟絣azy evaluation锟斤拷锟斤拷
     * 
     * @param procedure 要锟斤拷取锟侥讹拷锟斤拷锟斤拷锟斤拷锟斤拷锟斤拷锟斤拷指锟斤拷
     * @param iter 锟斤拷锟斤拷锟絣ist
     * @param predicate 锟斤拷锟斤拷锟斤拷锟斤拷锟斤拷同为锟斤拷锟斤拷指锟斤拷
     * @return
     */
    public static <E, T> Iterable<E> listComprehension(final Iterable<T> iter, final Procedure<E, T> procedure,
                                                       final Predicate<T> predicate) {
        Cat.logEvent("INVALID_METHOD_2", "com.dianping.api.common.util.functor.ListComprehension.listComprehension(java.lang.Iterable,com.dianping.api.common.util.functor.Procedure,com.dianping.api.common.util.functor.Predicate)");
        return new Iterable<E>() {

            public Iterator<E> iterator() {

                return new Iterator<E>() {

                    private Iterator<T> parentItr;

                    private E           next;

                    {
                        this.parentItr = iter.iterator();
                        genNext();
                    }

                    public boolean hasNext() {
                        return next != null;
                    }

                    public E next() {
                        E element = next;
                        next = null;
                        genNext();
                        return element;
                    }

                    private void genNext() {
                        for (; parentItr.hasNext();) {
                            T t = parentItr.next();
                            if (predicate.filter(t)) {
                                next = procedure.procedure(t);
                                break;
                            }
                        }
                    }

                    /**
                     * 锟斤拷锟斤不锟斤拷要remove()
                     */
                    public void remove() {
                        throw new UnsupportedOperationException();
                    }

                };
            }

        };
    }

    /**
     * 锟斤拷锟截诧拷锟结供Procedure锟斤拷锟斤拷锟
     * 
     * @param <E>
     * @param <T>
     * @param iter
     * @param predicate
     * @return
     */
    public static <T> Iterable<T> listComprehension(Iterable<T> iter, Predicate<T> predicate) {
        Cat.logEvent("INVALID_METHOD_4", "com.dianping.api.common.util.functor.ListComprehension.listComprehension(java.lang.Iterable,com.dianping.api.common.util.functor.Predicate)");
        return listComprehension(iter, new Procedure<T, T>() {

            public T procedure(T e) {
                return e;
            }
        }, predicate);
    }

    /**
     * 锟斤拷锟截诧拷锟结供Predicate锟斤拷锟斤拷锟
     * 
     * @param <E>
     * @param <T>
     * @param iter
     * @param f
     * @return
     */
    public static <E, T> Iterable<E> listComprehension(Iterable<T> iter, Procedure<E, T> procedure) {
        Cat.logEvent("INVALID_METHOD_3", "com.dianping.api.common.util.functor.ListComprehension.listComprehension(java.lang.Iterable,com.dianping.api.common.util.functor.Procedure)");
        return listComprehension(iter, procedure, new Predicate<T>() {

            public boolean filter(T e) {
                return true;
            }
        });
    }
    
    public static <E>  List<E> evaluate(Iterable<E> iter){
        Cat.logEvent("INVALID_METHOD_4", "com.dianping.api.common.util.functor.ListComprehension.evaluate(java.lang.Iterable)");
        ArrayList<E> al = new ArrayList<E>();
        for(E e : iter){
            al.add(e);
        }
        return al;
    }
}
