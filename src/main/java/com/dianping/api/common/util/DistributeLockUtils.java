package com.dianping.api.common.util;

import com.dianping.cat.Cat;
import com.dianping.lion.client.Lion;
import com.dianping.pay.api.util.LionConstants;
import com.dianping.squirrel.client.StoreKey;
import com.dianping.squirrel.client.impl.redis.RedisStoreClient;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;

@Slf4j
@Component
public class DistributeLockUtils {

    @Autowired
    @Qualifier("redisClient")
    private RedisStoreClient redisStoreClient;

    @Autowired
    @Qualifier("newRedisClient")
    private RedisStoreClient newRedisStoreClient;

    private static final String COUPON_ISSUE_DISTRIBUTE_LOCK = "UcIssueLock";

    private static final String USER_COUPON_DISTRIBUTE_LOCK_EXPIRE_SECONDS = "mapi-pay-promo-web.issueCoupon.distributeLock.expireSeconds";

    private static final String USER_COUPON_DISTRIBUTE_LOCK_SWITCH = "mapi-pay-promo-web.issueCoupon.distributeLock.switch";


    private static final String TOKEN = "1";

    public boolean tryLock(String userId, int couponGroupId) {
        if (!Lion.getBooleanValue(USER_COUPON_DISTRIBUTE_LOCK_SWITCH, true)) {
            return true;
        }
        StoreKey distributeLockStoreKey = getDistributeLockStoreKey(userId, couponGroupId);
        boolean lockSucc = false;
        try {
            if (Lion.getBooleanValue(LionConstants.NEW_REDIS_CLUSTER_SWITCH, false)){
                lockSucc = newRedisStoreClient.setnx(distributeLockStoreKey, TOKEN, getLockExpireTime());
            }  else {
                lockSucc = redisStoreClient.setnx(distributeLockStoreKey, TOKEN, getLockExpireTime());
            }
        } catch (Exception e) {
            log.error("tryLock exception userId:{}, couponGroupId:{}", userId, couponGroupId, e);
        }
        Cat.logEvent("tryLockResult", String.valueOf(lockSucc));
        return lockSucc;
    }

    private StoreKey getDistributeLockStoreKey(String userId, int couponGroupId) {
        return new StoreKey(COUPON_ISSUE_DISTRIBUTE_LOCK, userId, couponGroupId);
    }

    private int getLockExpireTime() {
        return Lion.getIntValue(USER_COUPON_DISTRIBUTE_LOCK_EXPIRE_SECONDS, 3);
    }

}
