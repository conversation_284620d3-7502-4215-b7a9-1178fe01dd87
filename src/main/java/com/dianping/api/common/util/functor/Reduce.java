package com.dianping.api.common.util.functor;

import com.dianping.cat.Cat;

import java.util.List;

/**
 * 锟斤拷Functor.java锟斤拷实锟斤拷锟斤拷锟斤拷锟斤拷锟斤拷锟斤拷式锟斤拷装
 * 
 * <AUTHOR> 2011-4-14 锟斤拷锟斤拷02:09:55
 */
public final class Reduce {

	private Reduce() {
	}
	
    /**
     * reduce锟斤拷锟斤拷
     * <p>
     * Apply a function of two arguments cumulatively to the items of a sequence, from left to right, so as to reduce
     * the sequence to a single value. For example, reduce(lambda x, y: x+y, [1, 2, 3, 4, 5]) calculates
     * ((((1+2)+3)+4)+5). If initial is present, it is placed before the items of the sequence in the calculation, and
     * serves as a default when the sequence is empty.
     * 
     * @param cumulate reduce锟斤拷锟斤拷
     * @param list 锟斤拷锟斤拷list
     * @param init 锟斤拷始值
     * @return
     */
    public static <C, I> C reduce(Cumulate<C, I> cumulate, List<I> list, C init) {
        return list.size() == 0 ? init : reduce(cumulate, list.subList(1, list.size()),
                                                cumulate.cumulate(init, list.get(0)));
    }

    /**
     * reduce锟斤拷锟斤拷
     * <p>
     * 
     * @param <C>
     * @param <I>
     * @param cumulate
     * @param list
     * @return
     */
    public static <I> I reduce(Cumulate<I, I> cumulate, List<I> list) {
        Cat.logEvent("INVALID_METHOD_2", "com.dianping.api.common.util.functor.Reduce.reduce(com.dianping.api.common.util.functor.Cumulate,java.util.List)");
        return reduce(cumulate, list.subList(1, list.size()), list.get(0));
    }

}
