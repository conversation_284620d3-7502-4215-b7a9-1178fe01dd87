package com.dianping.api.common.util;

import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;

import com.dianping.avatar.log.AvatarLogger;
import com.dianping.avatar.log.AvatarLoggerFactory;
import com.dianping.cat.Cat;

/**
 * <AUTHOR>  <EMAIL>
 *
 */
public final class Encryption {
	
	private static final AvatarLogger LOG = AvatarLoggerFactory.getLogger(Encryption.class);
	
	private Encryption() {
	}
	
	// 拷贝自client代码,MD5签名
	public static String encryptionWithMD5(String content) {
        Cat.logEvent("INVALID_METHOD_4", "com.dianping.api.common.util.Encryption.encryptionWithMD5(java.lang.String)");
        return toHexString(toMD5(content.getBytes()));
	}
	
	public static String encryptionWithMD5(byte[] content){
        Cat.logEvent("INVALID_METHOD_4", "com.dianping.api.common.util.Encryption.encryptionWithMD5(byte[])");
        return toHexString(toMD5(content));
	}
	
	private static String toHexString(byte[] byteArray){
        Cat.logEvent("INVALID_METHOD_3", "com.dianping.api.common.util.Encryption.toHexString(byte[])");
        StringBuffer md5StrBuff = new StringBuffer();
		for (int i = 0; i < byteArray.length; i++) {
			String h = Integer.toHexString(0xFF & byteArray[i]);
			while (h.length() < 2) {
				h = "0" + h;
			}
			md5StrBuff.append(h);
		}
		return md5StrBuff.toString();
	}
	
	public static byte[] toMD5(byte[] content) {
        Cat.logEvent("INVALID_METHOD_5", "com.dianping.api.common.util.Encryption.toMD5(byte[])");
        MessageDigest md = null;
		try {
			md = MessageDigest.getInstance("MD5");
			md.update(content);
			return md.digest();	
		} catch (NoSuchAlgorithmException e) {
			LOG.error("MD5 error");
			return null;
		}				
	}
}
