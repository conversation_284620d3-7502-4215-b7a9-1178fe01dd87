/**
 * 
 */
package com.dianping.api.common.util;

import com.dianping.cat.Cat;

/**
 * <AUTHOR>
 *
 */
public final class CacheSyncUtil {
	
	private CacheSyncUtil() {
	}
	
	public static String getSyncMsg(String key,String value){
        Cat.logEvent("INVALID_METHOD_1", "com.dianping.api.common.util.CacheSyncUtil.getSyncMsg(java.lang.String,java.lang.String)");
        return "mApi"+"|"+key+"|"+value;
	}

	public static String getSyncMsg(String key){
        Cat.logEvent("INVALID_METHOD_3", "com.dianping.api.common.util.CacheSyncUtil.getSyncMsg(java.lang.String)");
        return "mApi"+"|"+key;
	}
}
