package com.dianping.api.common.entity;

import java.io.Serializable;

public class UserInfo implements Serializable {
	
	/**
	 * 
	 */
	private static final long serialVersionUID = 8993480800702396594L;
	private long userID;
	private int level; //会员等级
	private int checkInCount; //签到统计
	private int score; 	//会员积分
	private int honeyCount;  //密友统计
	private int badgeCount;  //徽章统计
	private int mayorCount;
	private int notificationCount;
 
	public long getUserID() {
		return userID;
	}



	public void setUserID(long userID) {
		this.userID = userID;
	}



	public int getLevel() {
		return level;
	}



	public void setLevel(int level) {
		this.level = level;
	}



	public int getCheckInCount() {
		return checkInCount;
	}



	public void setCheckInCount(int checkInCount) {
		this.checkInCount = checkInCount;
	}



	public int getScore() {
		return score;
	}



	public void setScore(int score) {
		this.score = score;
	}



	public int getHoneyCount() {
		return honeyCount;
	}



	public void setHoneyCount(int honeyCount) {
		this.honeyCount = honeyCount;
	}



	public int getBadgeCount() {
		return badgeCount;
	}



	public void setBadgeCount(int badgeCount) {
		this.badgeCount = badgeCount;
	}



	public int getMayorCount() {
		return mayorCount;
	}



	public void setMayorCount(int mayorCount) {
		this.mayorCount = mayorCount;
	}



	public int getNotificationCount() {
		return notificationCount;
	}



	public void setNotificationCount(int notificationCount) {
		this.notificationCount = notificationCount;
	}

}
