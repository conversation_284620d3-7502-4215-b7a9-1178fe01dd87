/**
 * 
 */
package com.dianping.api.common.entity;

/**
 * <AUTHOR>
 *
 */
public class CategoryNav {
	
	 private int categoryID;
	 private int categoryLevel;
	 private int shopCount;

     public CategoryNav(int categoryID, int categoryLevel){
    	 this.categoryID=categoryID;
    	 this.categoryLevel=categoryLevel;
     }
     public CategoryNav(int categoryID, int categoryLevel, int shopCount){
    	 this.categoryID=categoryID;
    	 this.categoryLevel=categoryLevel;
    	 this.shopCount=shopCount;
    	 
     }
	public int getCategoryID() {
		return categoryID;
	}
	public void setCategoryID(int categoryID) {
		this.categoryID = categoryID;
	}
	public int getCategoryLevel() {
		return categoryLevel;
	}
	public void setCategoryLevel(int categoryLevel) {
		this.categoryLevel = categoryLevel;
	}
	public int getShopCount() {
		return shopCount;
	}
	public void setShopCount(int shopCount) {
		this.shopCount = shopCount;
	}
     

}
