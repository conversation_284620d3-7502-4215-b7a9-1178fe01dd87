package com.dianping.api.common.entity;

import com.dianping.api.common.util.KeyValuePair;
import com.dianping.api.domain.ClientInfo;
import com.dianping.api.domain.Version;
import com.dianping.api.netio.DPEncoder;
import com.dianping.cat.Cat;
import com.google.common.collect.Lists;

import java.io.Serializable;
import java.util.List;

/**
 * User: yaou.li
 * Date: 14-5-14
 * Time: 下午7:38
 * To change this template use File | Settings | File Templates.
 */
public class CardCategory extends DPEncoder implements Serializable {

    private static final long serialVersionUID = -1247342002823728643L;

    private int cardType;
    private String cardName;
    private int rank;
    private List<BankElement> bankList;
    private String cardTip;

    private static final List<KeyValuePair> LIST = Lists.newArrayList();
    static {
        LIST.add(new KeyValuePair("cardType",0xd9d6));
        LIST.add(new KeyValuePair("cardName",0xc682));
        LIST.add(new KeyValuePair("rank",0xc06a));
        LIST.add(new KeyValuePair("bankList",0x841d));
        LIST.add(new KeyValuePair("cardTip",0x4f77));
    }

    @Override
    public int getClassId() {
        Cat.logEvent("INVALID_METHOD_5", "com.dianping.api.common.entity.CardCategory.getClassId()");
        return 0xc83c;
    }

    @Override
    public List<KeyValuePair> getSerializeList(ClientInfo clientInfo, Version version) {
        Cat.logEvent("INVALID_METHOD_3", "com.dianping.api.common.entity.CardCategory.getSerializeList(com.dianping.api.domain.ClientInfo,com.dianping.api.domain.Version)");
        return LIST;
    }

    public int getCardType() {
        return cardType;
    }

    public void setCardType(int cardType) {
        this.cardType = cardType;
    }

    public String getCardName() {
        return cardName;
    }

    public void setCardName(String cardName) {
        this.cardName = cardName;
    }

    public int getRank() {
        return rank;
    }

    public void setRank(int rank) {
        this.rank = rank;
    }

    public List<BankElement> getBankList() {
        return bankList;
    }

    public void setBankList(List<BankElement> bankList) {
        this.bankList = bankList;
    }

    public String getCardTip() {
        return cardTip;
    }

    public void setCardTip(String cardTip) {
        this.cardTip = cardTip;
    }
}
