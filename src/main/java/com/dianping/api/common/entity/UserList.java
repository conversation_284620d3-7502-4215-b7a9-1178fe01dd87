
package com.dianping.api.common.entity;

import java.io.Serializable; 
import com.dianping.api.domain.ResultList;
import com.dianping.cat.Cat;

/**
 * <AUTHOR>
 *
 */
public final class UserList extends ResultList<UserProfile> implements Serializable {
 
	private static final long serialVersionUID = 8170371274309967860L;
	   

	/* (non-Javadoc)
	 * @see com.dianping.api.netio.DPEncoder#getClassId()
	 */
	@Override
	public int getClassId() {
        Cat.logEvent("INVALID_METHOD_1", "com.dianping.api.common.entity.UserList.getClassId()");
        return 0x64dd;
	}
 
}
