/**
 * 
 */
package com.dianping.api.common.entity;

import java.io.Serializable;

/**
 * <AUTHOR>
 *
 */
public class GsmStation extends BaseStation implements Serializable {
	
	public GsmStation() {
		
	}
	
	public GsmStation(int mcc, int mnc, int cid, int lac, int asu) {
		this.mcc = mcc;
		this.mnc = mnc;
		this.cid = cid;
		this.lac = lac;
		this.asu = asu;
	}
	
	public int getMnc() {
		return mnc;
	}
	public void setMnc(int mnc) {
		this.mnc = mnc;
	}
	public int getCid() {
		return cid;
	}
	public void setCid(int cid) {
		this.cid = cid;
	}
	public int getLac() {
		return lac;
	}
	public void setLac(int lac) {
		this.lac = lac;
	}
	public int getAsu() {
		return asu;
	}
	public void setAsu(int asu) {
		this.asu = asu;
	}
	private static final long serialVersionUID = -5465256296905475804L;
	
	private int mnc;
	private int cid;
	private int lac;
	private int asu;
	
}
