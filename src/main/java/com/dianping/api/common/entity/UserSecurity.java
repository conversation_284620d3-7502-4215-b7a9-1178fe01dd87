package com.dianping.api.common.entity;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

import com.dianping.api.common.util.KeyValuePair;
import com.dianping.api.domain.ClientInfo;
import com.dianping.api.domain.SimpleMsg;
import com.dianping.api.domain.Version;
import com.dianping.api.netio.DPEncoder;
import com.dianping.cat.Cat;

public class UserSecurity extends DPEncoder implements Serializable {

	private static final long serialVersionUID = 2610352826092532404L;
	
	private static final List<KeyValuePair> LIST;
	
	static {
		LIST = new ArrayList<KeyValuePair>();
		LIST.add(new KeyValuePair("succeed", 0x25cf));
		LIST.add(new KeyValuePair("message", 0xbc4));
		LIST.add(new KeyValuePair("validation", 0x50a0));
		LIST.add(new KeyValuePair("userProfile", 0x2dc4));
		LIST.add(new KeyValuePair("token", 0xcd0a));
		LIST.add(new KeyValuePair("newToken", 0xdd0c));
	}
	
	private boolean succeed;
	
	private SimpleMsg message;
	
	private UserProfile userProfile;
	
	private String token;
	
	private String newToken;
	

	@Override
	public int getClassId() {
        Cat.logEvent("INVALID_METHOD_4", "com.dianping.api.common.entity.UserSecurity.getClassId()");
        return 0x34d3;
	}

	@Override
	public List<KeyValuePair> getSerializeList(ClientInfo client, Version version) {
        Cat.logEvent("INVALID_METHOD_5", "com.dianping.api.common.entity.UserSecurity.getSerializeList(com.dianping.api.domain.ClientInfo,com.dianping.api.domain.Version)");
        return LIST;
	}

	public boolean isSucceed() {
		return succeed;
	}

	public void setSucceed(boolean succeed) {
		this.succeed = succeed;
	}

	public SimpleMsg getMessage() {
		return message;
	}

	public void setMessage(SimpleMsg message) {
		this.message = message;
	}

	public UserProfile getUserProfile() {
		return userProfile;
	}

	public void setUserProfile(UserProfile userProfile) {
		this.userProfile = userProfile;
	}

	public String getToken() {
		return token;
	}

	public void setToken(String token) {
		this.token = token;
	}

	public String getNewToken() {
		return newToken;
	}

	public void setNewToken(String newToken) {
		this.newToken = newToken;
	}

}
