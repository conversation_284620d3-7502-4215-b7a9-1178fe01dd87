/**
 * Project: api-war
 * 
 * File Created at 2011-6-9
 * $Id$
 * 
 * Copyright 2010 dianping.com.
 * All rights reserved.
 *
 * This software is the confidential and proprietary information of
 * Dianping Company. ("Confidential Information").  You shall not
 * disclose such Confidential Information and shall use it only in
 * accordance with the terms of the license agreement you entered into
 * with dianping.com.
 */
package com.dianping.api.common.entity;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

import com.dianping.api.common.util.KeyValuePair;
import com.dianping.api.domain.ClientInfo;
import com.dianping.api.domain.Version;
import com.dianping.api.netio.DPEncoder;
import com.dianping.cat.Cat;

/**
 * Comment of FormItem
 * <AUTHOR>
 *
 */
public final class FormItem extends DPEncoder implements Serializable {

	/**
	 * 
	 */
	private static final long serialVersionUID = -9056346005560957898L;
	
	 
	public int getClassId()
	{
        Cat.logEvent("INVALID_METHOD_2", "com.dianping.api.common.entity.FormItem.getClassId()");
        return 0x7e06;
	}
	static final List<KeyValuePair> list=new ArrayList<KeyValuePair>();
	static{
		
		list.add(new KeyValuePair("title", 0x36e9));
		list.add(new KeyValuePair("value", 0xa5b8));
		list.add(new KeyValuePair("type", 0x372));
		list.add(new KeyValuePair("item", 0xef11));
		list.add(new KeyValuePair("param", 0x2cf8));	
	}
	
	private String title;
	private String value;
	private int type; 
	private List<Pair> item; 
	private String param;
	
	public String getTitle() {
		return title;
	}



	public void setTitle(String title) {
		this.title = title;
	}



	public String getValue() {
		return value;
	}



	public void setValue(String value) {
		this.value = value;
	}



	public int getType() {
		return type;
	}



	public void setType(int type) {
		this.type = type;
	}



	public List<Pair> getItem() {
		return item;
	}



	public void setItem(List<Pair> item) {
		this.item = item;
	}



	public String getParam() {
		return param;
	}



	public void setParam(String param) {
		this.param = param;
	}
 
	@Override
	public List<KeyValuePair> getSerializeList(ClientInfo clientInfo, Version version){
        Cat.logEvent("INVALID_METHOD_3", "com.dianping.api.common.entity.FormItem.getSerializeList(com.dianping.api.domain.ClientInfo,com.dianping.api.domain.Version)");
        return list;
	}
}
