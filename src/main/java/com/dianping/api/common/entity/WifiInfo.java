/**
 * 
 */
package com.dianping.api.common.entity;

import java.io.Serializable;

/**
 * <AUTHOR>
 *
 */
public class WifiInfo implements Serializable {
	
	public WifiInfo() {
		
	}
	
	public WifiInfo(String sid, String mac, int dBm) {
		this.sid = sid;
		this.mac = mac;
		this.dBm = dBm;
	}
	
	public String getSid() {
		return sid;
	}

	public void setSid(String sid) {
		this.sid = sid;
	}

	public String getMac() {
		return mac;
	}

	public void setMac(String mac) {
		this.mac = mac;
	}

	public int getdBm() {
		return dBm;
	}

	public void setdBm(int dBm) {
		this.dBm = dBm;
	}

	private static final long serialVersionUID = 2712266826676682907L;
	
	private String sid;
	private String mac;
	private int dBm;

}
