
package com.dianping.api.common.entity;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

import com.dianping.api.common.util.KeyValuePair;
import com.dianping.api.domain.Enum.ClientType;
import com.dianping.api.domain.ClientInfo;
import com.dianping.api.domain.Version;
import com.dianping.api.netio.DPEncoder;
import com.dianping.api.util.StringUtil;

public class City extends DPEncoder implements Serializable {

	private static final long serialVersionUID = -1247342002810728643L;

	private int id;
	private String name;
	private String areaCode;
	private boolean isPromo;
	private boolean isTuan;
	private double lat;
	private double lng;
	private int firstChar;
	private String abbrCode;
	
	private boolean isTop;
	private boolean isMultiCategoryCity;
	private boolean isLocalPromoCity;	
	private int tuanGouFlag;
	private boolean isRankIndexCity;
	private boolean isLocalDish;

	// 4.6
	private int flag;
	//4.9
	private int brandpromoFlag;
		
	//mapType
	private int mapType;
	
	/**
	 * 有附近优惠的城市，在附近优惠页上方增加此模块；
	 *	无附近优惠的城市，在最热优惠页上方增加此模块；
	 *	无优惠券的城市，在首页九宫格中增加”优惠券“的入口，点击进入到图4-2麦当劳优惠券列表页；
     *  so  I think it's always true
	 */
	private boolean isMcPromoCity=true;
	/**
	 * 0xdab: ObjectBegin
	 * case 0x91b: // Id
				this.id = u.readInt();
				break;
			case 0xee8f: // Name
				this.name = u.readString();
				break;
			case 0xe8b9: // AreaCode
				this.areaCode = u.readString();
				break;
			case 0xd364: // IsPromo
				this.isPromo = u.readBoolean();
				break;
			case 0xc5d6: // IsTuan
				this.isTuan = u.readBoolean();
				break;
			case 0x297e: // Lat
				this.latitude = u.readDouble();
				break;
			case 0x2b04: // Lng
				this.longitude = u.readDouble();
				break;
			case 0x5d5e: // FirstChar
				this.firstChar = u.readInt();
				break;
			case 0x4791: // IsTop
				this.isTop = u.readBoolean();
				break;
			case 0x806: // isLocalPromoCity
				this.isLocalPromoCity = u.readBoolean();
				break;
		
	 */
	
	private boolean isNearByEnabled;
	
	public boolean isNearByEnabled() {
		return isNearByEnabled;
	}
	
	public boolean isIsNearByEnabled() {
		return isNearByEnabled;
	}

	public void setIsNearByEnabled(boolean isNearByEnabled) {
		this.isNearByEnabled = isNearByEnabled;
	}

	@Override
	public int getClassId() {		
		return 0xdab;
	}
	

	@Override
	public List<KeyValuePair> getSerializeList(ClientInfo clientInfo, Version version){
		return list;
	}
	
	/*@Override
	public List<KeyValuePair> getSerializeList(ClientInfo clientInfo, Version version ){ 
		if(ClientType.iPhone.equals(client) || ClientType.Android.equals(client)){
			if (version != null
					&& Version.compareTo(version, new Version("4.9")) >= 0) {
				return newlist;
			} else {
				return oldlist;
			}
		}
		return oldlist;
	}*/

	

	static final List<KeyValuePair> list=new ArrayList<KeyValuePair>();
	static{
		list.add(new KeyValuePair("id", 0x91b));
		list.add(new KeyValuePair("name", 0xee8f));
		list.add(new KeyValuePair("areaCode", 0xe8b9));
		list.add(new KeyValuePair("isPromo", 0xd364));
		list.add(new KeyValuePair("isTuan", 0xc5d6));
		list.add(new KeyValuePair("lat", 0x297e));
		list.add(new KeyValuePair("lng", 0x2b04));
		list.add(new KeyValuePair("firstChar", 0x5d5e));
		list.add(new KeyValuePair("isTop", 0x4791));
		list.add(new KeyValuePair("isMultiCategoryCity", 0x6ee1));
		list.add(new KeyValuePair("isLocalPromoCity", 0x806));
		list.add(new KeyValuePair("isRankIndexCity", 0x93dd));
		list.add(new KeyValuePair("isLocalDish", 0xfcb2));
		list.add(new KeyValuePair("flag", 0x73ad));
	 	list.add(new KeyValuePair("brandpromoFlag", 0x73ad, 
	 			                  new ClientType[]{ClientType.Android,ClientType.iPhone},
	 			                  new Version("4.9")));
	 	list.add(new KeyValuePair("isNearByEnabled", 0x7e94));
	}
	
	public int getFirstChar() {
		return this.firstChar;
	}

	public void setFirstChar(int firstChar) {
		this.firstChar = firstChar;
	}

	public String getAbbrCode() {
		return abbrCode;
	}

	public void setAbbrCode(String abbrCode) {
		this.abbrCode = abbrCode.toUpperCase();
		this.firstChar = StringUtil.isNullOrEmpty(abbrCode) ? 0 : (int) abbrCode.charAt(0);
	}

	public int getId() {
		return id;
	}

	public void setId(int id) {
		this.id = id;
	}

	public String getName() {
		return name;
	}

	public void setName(String name) {
		this.name = name;
	}

	public String getAreaCode() {
		return areaCode;
	}

	public void setAreaCode(String areaCode) {
		this.areaCode = areaCode;
	}

	public boolean isIsPromo() {
		return isPromo;
	}

	public void setIsPromo(boolean isPromo) {
		this.isPromo = isPromo;
		if(isPromo) {
			this.flag = this.flag | 0x1;
		}
	}

	public boolean isIsTuan() {
		return isTuan;
	}

	public void setIsTuan(boolean isTuan) {
		this.isTuan = isTuan;
	}

	public double getLat() {
		return lat;
	}

	public void setLat(double lat) {
		this.lat = lat;
	}

	public double getLng() {
		return lng;
	}

	public void setLng(double lng) {
		this.lng = lng;
	}

	public boolean isIsTop() {
		return isTop;
	}

	public void setIsTop(boolean isTop) {
		this.isTop = isTop;
	}

	public boolean isIsMultiCategoryCity() {
		return isMultiCategoryCity;
	}

	public void setIsMultiCategoryCity(boolean isMultiCategoryCity) {
		this.isMultiCategoryCity = isMultiCategoryCity;
		if(isMultiCategoryCity) {
			this.flag = this.flag | 0x8;
		}
	}

	public boolean isIsLocalPromoCity() {
		return isLocalPromoCity;
	}

	public void setIsLocalPromoCity(boolean isLocalPromoCity) {
		this.isLocalPromoCity = isLocalPromoCity;
	}
	public boolean isIsLocalDish() {
		return isLocalDish;
	}

	public void setIsLocalDish(boolean isLocalDish) {
		this.isLocalDish = isLocalDish;
	}

	public boolean isIsRankIndexCity() {
		return isRankIndexCity;
	}

	public void setIsRankIndexCity(boolean isRankIndexCity) {
		this.isRankIndexCity = isRankIndexCity;
	}

	public int getTuanGouFlag() {
		return tuanGouFlag;
	}

	public int getBrandpromoFlag() {
		return brandpromoFlag;
	}

	public void setBrandpromoFlag(int brandpromoFlag) {
		this.brandpromoFlag = brandpromoFlag;
	}

	public void setTuanGouFlag(int tuanGouFlag) {
		this.tuanGouFlag = tuanGouFlag;
		if(tuanGouFlag == 1 || tuanGouFlag == 2){
			isTuan = true;
		} else {
			isTuan = false;
		}
		if(isTuan) {
			this.flag = this.flag | 0x2; 
			this.flag = this.flag | 0x100; // 4.6 add localTuan
		}
	}
	
	public boolean isMcPromoCity() {
		return isMcPromoCity;
	}

	public void setMcPromoCity(boolean isMcPromoCity) {
		this.isMcPromoCity = isMcPromoCity;
		if(isMcPromoCity){
			this.flag = this.flag | 0x200;
		}
	}

	public int getFlag() {
		return flag;
	}

	public void setFlag(int flag) {
		this.flag = flag;
	}
	
	public int getMapType() {
		return mapType;
	}

	public void setMapType(int mapType) {
		this.mapType = mapType;
	}
 
}
