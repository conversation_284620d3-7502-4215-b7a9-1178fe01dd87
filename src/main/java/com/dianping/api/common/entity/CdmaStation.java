/**
 * 
 */
package com.dianping.api.common.entity;

import java.io.Serializable;

/**
 * <AUTHOR>
 *
 */
public class CdmaStation extends BaseStation implements Serializable {
	
	public CdmaStation() {
		
	}
	
	public CdmaStation(int mcc, int sid, int bid, int nid, double lat, double lng) {
		this.mcc = mcc;
		this.sid = sid;
		this.bid = bid;
		this.nid = nid;
		this.lat = lat;
		this.lng = lng;
	}
	
	public int getSid() {
		return sid;
	}
	public void setSid(int sid) {
		this.sid = sid;
	}
	public int getBid() {
		return bid;
	}
	public void setBid(int bid) {
		this.bid = bid;
	}
	public int getNid() {
		return nid;
	}
	public void setNid(int nid) {
		this.nid = nid;
	}
	public double getLat() {
		return lat;
	}
	public void setLat(double lat) {
		this.lat = lat;
	}
	public double getLng() {
		return lng;
	}
	public void setLng(double lng) {
		this.lng = lng;
	}
	private static final long serialVersionUID = 3978938798356756351L;
	
	private int sid;
	private int bid;
	private int nid;
	private double lat;
	private double lng;

}
