package com.dianping.api.common.entity;

import java.io.Serializable;
import java.util.List;

import com.dianping.api.common.util.KeyValuePair;
import com.dianping.api.domain.ClientInfo;
import com.dianping.api.domain.Version;
import com.dianping.api.netio.DPEncoder;
import com.dianping.api.util.ApiUtil;
import com.google.common.collect.Lists;

public class PaymentTool extends DPEncoder implements Serializable {
	private static final long serialVersionUID = 3141927254858476679L;
	private String id;
	private String title;
	private String subTitle;
	private String highlightTitle;
	private boolean isLastUsed;
	private boolean isDefault;
	private double eventDiscountAmount;
	private double eventLimitAmount;
	private int displayMode;
    private boolean isRedirectBankList;
    private int preposeBankCashierID;
    private String bankName;
    private String iconUrl;

	@Override
	public int getClassId() {
		return 0x4307;
	}
	private static final List<KeyValuePair> LIST = Lists.newArrayList();
	static {
		LIST.add(new KeyValuePair("id",0x91b));
		LIST.add(new KeyValuePair("title",0x36e9));
		LIST.add(new KeyValuePair("subTitle",0x475e));
		LIST.add(new KeyValuePair("highlightTitle",0xa056));
		LIST.add(new KeyValuePair("isLastUsed",0xca50));
		LIST.add(new KeyValuePair("isDefault",0x8d60));
		LIST.add(new KeyValuePair("eventDiscountAmount",0x7293));
		LIST.add(new KeyValuePair("eventLimitAmount",0x99ac));
		LIST.add(new KeyValuePair("displayMode",0xf4f0));
        LIST.add(new KeyValuePair("isRedirectBankList",0x9238));
        LIST.add(new KeyValuePair("preposeBankCashierID",0x89c9));
        LIST.add(new KeyValuePair("bankName",0x4e00));
        LIST.add(new KeyValuePair("iconUrl",0xeef0));
	}
	@Override
	public List<KeyValuePair> getSerializeList(ClientInfo clientInfo,
			Version version) {
		return LIST;
	}

	public String getId() {
		return id;
	}

	public void setId(String id) {
		this.id = id;
	}

	public String getTitle() {
		return title;
	}

	public void setTitle(String title) {
		this.title = title;
	}

	public String getSubTitle() {
		return subTitle;
	}

	public void setSubTitle(String subTitle) {
		this.subTitle = subTitle;
	}

	public String getHighlightTitle() {
		return highlightTitle;
	}

	public void setHighlightTitle(String highlightTitle) {
		this.highlightTitle = highlightTitle;
	}

	public boolean getIsLastUsed() {
		return isLastUsed;
	}

	public void setIsLastUsed(boolean isLastUsed) {
		this.isLastUsed = isLastUsed;
	}

	public boolean getIsDefault() {
		return isDefault;
	}

	public void setIsDefault(boolean isDefault) {
		this.isDefault = isDefault;
	}

	public double getEventDiscountAmount() {
		return eventDiscountAmount;
	}

	public void setEventDiscountAmount(double eventDiscountAmount) {
		this.eventDiscountAmount = eventDiscountAmount;
	}

	public double getEventLimitAmount() {
		return eventLimitAmount;
	}

	public void setEventLimitAmount(double eventLimitAmount) {
		this.eventLimitAmount = eventLimitAmount;
	}

	public int getDisplayMode() {
		return displayMode;
	}

	public void setDisplayMode(int displayMode) {
		this.displayMode = displayMode;
	}

    public boolean isIsRedirectBankList() {
        return isRedirectBankList;
    }

    public void setIsRedirectBankList(boolean isRedirectBankList) {
        this.isRedirectBankList = isRedirectBankList;
    }

    public int getPreposeBankCashierID() {
        return preposeBankCashierID;
    }

    public void setPreposeBankCashierID(int preposeBankCashierID) {
        this.preposeBankCashierID = preposeBankCashierID;
    }

    public String getBankName() {
        return bankName;
    }

    public void setBankName(String bankName) {
        this.bankName = bankName;
    }

    public String getIconUrl() {
        return iconUrl;
    }

    public void setIconUrl(String iconUrl) {
        this.iconUrl = iconUrl;
    }
}
