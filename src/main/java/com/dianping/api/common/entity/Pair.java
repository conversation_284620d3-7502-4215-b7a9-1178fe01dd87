package com.dianping.api.common.entity;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

import com.dianping.api.common.util.KeyValuePair;
import com.dianping.api.domain.ClientInfo;
import com.dianping.api.domain.Version;
import com.dianping.api.netio.DPEncoder;
import com.dianping.cat.Cat;

/**
 * 
 * <AUTHOR>
 * 
 */
public class Pair extends DPEncoder implements Serializable{

	/**
	 * 
	 */
	private static final long serialVersionUID = -9069184801436303085L;
	
	private String id;
	private String name;
	private int type;
	private boolean isUsed;
	private String userDate;
	 
	static final List<KeyValuePair> list=new ArrayList<KeyValuePair>();
	static{
		list.add(new KeyValuePair("id", 0x91b));
		list.add(new KeyValuePair("name", 0xee8f));		
		list.add(new KeyValuePair("type", 0x372));		 
	}
	public Pair() {

	}

	public Pair(String id, String name, int type) {
		this.id = id;
		this.name = name;
		this.type = type;
	}

	public String getId() {
		return id;
	}

	public void setId(String id) {
		this.id = id;
	}

	public String getName() {
		return name;
	}

	public void setName(String name) {
		this.name = name;
	}

	public int getType() {
		return type;
	}

	public void setType(int type) {
		this.type = type;
	}
	

	public boolean isUsed() {
		return isUsed;
	}

	public void setUsed(boolean isUsed) {
		this.isUsed = isUsed;
	}



	public String getUserDate() {
		return userDate;
	}

	public void setUserDate(String userDate) {
		this.userDate = userDate;
	}

	public static long getSerialversionuid() {
        Cat.logEvent("INVALID_METHOD_5", "com.dianping.api.common.entity.Pair.getSerialversionuid()");
        return serialVersionUID;
	}

	 
	public List<KeyValuePair> getSerializeList(ClientInfo clientInfo, Version version){
        Cat.logEvent("INVALID_METHOD_4", "com.dianping.api.common.entity.Pair.getSerializeList(com.dianping.api.domain.ClientInfo,com.dianping.api.domain.Version)");
        return list;
	}

	/* (non-Javadoc)
	 * @see com.dianping.api.netio.DPEncoder#getClassId()
	 */
	@Override
	public int getClassId() {
        Cat.logEvent("INVALID_METHOD_3", "com.dianping.api.common.entity.Pair.getClassId()");
        return 0xd6df;
	}
}
