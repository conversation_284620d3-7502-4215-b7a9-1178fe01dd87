/**
 * Project: api-war
 * 
 * File Created at 2011-5-25
 * $Id$
 * 
 * Copyright 2010 dianping.com.
 * All rights reserved.
 *
 * This software is the confidential and proprietary information of
 * Dianping Company. ("Confidential Information").  You shall not
 * disclose such Confidential Information and shall use it only in
 * accordance with the terms of the license agreement you entered into
 * with dianping.com.
 */
package com.dianping.api.common.entity;

/**
 * TODO Comment of Point
 * 
 * <AUTHOR>
 * 
 */
public class Point {

	private double lat;
	private double lng;

	public Point() {
	}

	public Point(double lat, double lng) {
		this.lat = lat;
		this.lng = lng;
	}

	public double getLat() {
		return lat;
	}

	public void setLat(double lat) {
		this.lat = lat;
	}

	public double getLng() {
		return lng;
	}

	public void setLng(double lng) {
		this.lng = lng;
	}

	public String toString() {
		return "lat==" + lat + ",lng==" + lng;
	}
}
