/**
 * Project: api-war
 * 
 * File Created at 2011-5-30
 * $Id$
 * 
 * Copyright 2010 dianping.com.
 * All rights reserved.
 *
 * This software is the confidential and proprietary information of
 * Dianping Company. ("Confidential Information").  You shall not
 * disclose such Confidential Information and shall use it only in
 * accordance with the terms of the license agreement you entered into
 * with dianping.com.
 */
package com.dianping.api.common.entity;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

import com.dianping.api.common.util.KeyValuePair;
import com.dianping.api.domain.ClientInfo;
import com.dianping.api.domain.Version;
import com.dianping.api.netio.DPEncoder;

/**
 * <AUTHOR>
 * 
 */
public final class UserScore extends DPEncoder implements Serializable {

	private static final long serialVersionUID = -6248661841529255482L;

	static final List<KeyValuePair> list = new ArrayList<KeyValuePair>();
	static {
		list.add(new KeyValuePair("level", 0xaf3a));
		list.add(new KeyValuePair("scoreStart", 0x6980));
		list.add(new KeyValuePair("scoreEnd", 0x33e0));
		list.add(new KeyValuePair("weeklyScore", 0x975a));
		list.add(new KeyValuePair("weeklyHoneyRank", 0xc44b));
		list.add(new KeyValuePair("weeklyCityRank", 0xb8c9));
		list.add(new KeyValuePair("totalScore", 0xf5db));
		list.add(new KeyValuePair("totalHoneyRank", 0x7af3));
		list.add(new KeyValuePair("totalCityRank", 0x4b0c));
	}

	private int level;
	private int scoreStart;
	private int scoreEnd;
	private int weeklyScore;
	private int weeklyHoneyRank;
	private int weeklyCityRank;
	private int totalScore;
	private int totalHoneyRank;
	private int totalCityRank;

	public int getLevel() {
		return level;
	}

	public void setLevel(int level) {
		this.level = level;
	}

	public int getScoreStart() {
		return scoreStart;
	}

	public void setScoreStart(int scoreStart) {
		this.scoreStart = scoreStart;
	}

	public int getScoreEnd() {
		return scoreEnd;
	}

	public void setScoreEnd(int scoreEnd) {
		this.scoreEnd = scoreEnd + 1;
	}

	public int getWeeklyScore() {
		return weeklyScore;
	}

	public void setWeeklyScore(int weeklyScore) {
		this.weeklyScore = weeklyScore;
	}

	public int getWeeklyHoneyRank() {
		return weeklyHoneyRank;
	}

	public void setWeeklyHoneyRank(int weeklyHoneyRank) {
		this.weeklyHoneyRank = weeklyHoneyRank;
	}

	public int getWeeklyCityRank() {
		return weeklyCityRank;
	}

	public void setWeeklyCityRank(int weeklyCityRank) {
		this.weeklyCityRank = weeklyCityRank;
	}

	public int getTotalScore() {
		return totalScore;
	}

	public void setTotalScore(int totalScore) {
		this.totalScore = totalScore;
	}

	public int getTotalHoneyRank() {
		return totalHoneyRank;
	}

	public void setTotalHoneyRank(int totalHoneyRank) {
		this.totalHoneyRank = totalHoneyRank;
	}

	public int getTotalCityRank() {
		return totalCityRank;
	}

	public void setTotalCityRank(int totalCityRank) {
		this.totalCityRank = totalCityRank;
	}

	@Override
	public List<KeyValuePair> getSerializeList(ClientInfo clientInfo, Version version) {
		return list;
	}

	/*
	 * (non-Javadoc)
	 * 
	 * @see com.dianping.api.netio.DPEncoder#getClassId()
	 */
	@Override
	public int getClassId() {
		return 0xa399;
	}

}
