package com.dianping.api.common.entity;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import com.dianping.api.common.util.KeyValuePair;
import com.dianping.api.common.util.UserHelper;
import com.dianping.api.domain.Enum.ClientType;
import com.dianping.api.domain.ClientInfo;
import com.dianping.api.domain.Version;
import com.dianping.api.netio.DPEncoder;
import com.dianping.api.util.ApiUtil;

public class UserProfile  extends DPEncoder implements Serializable{
	
	private static final long serialVersionUID = -7327220914020687376L;
	
	private long userID;
	private String nickName;
	private String avatar;
	private String avatar96x96;
	private String avatar150x150;
	private int userPower;
	private int cityID;
	private boolean isHoney;
	private boolean isFans;
	private boolean isInvited;
	private Date time=new Date();
	private String token;
	private String newToken;
	private int mana;
	private String email;
	private int dcash;
	

	private String snsNickName;
	private int snsImportedFlags;
	
	private Boolean userSex;
	private int gender;
	
	public String getSnsNickName() {
		return snsNickName;
	}

	public void setSnsNickName(String snsNickName) {
		this.snsNickName = snsNickName;
	}

	public int getSnsImportedFlags() {
		return snsImportedFlags;
	}

	public void setSnsImportedFlags(int snsImportedFlags) {
		this.snsImportedFlags = snsImportedFlags;
	}
	public String getAvatar96x96() {
		return avatar96x96;
	}

	public void setAvatar96x96(String avatar96x96) {
		this.avatar96x96 = avatar96x96;
	}

	public String getAvatar150x150() {
		return avatar150x150;
	}

	public void setAvatar150x150(String avatar150x150) {
		this.avatar150x150 = avatar150x150;
	}
	
	public String getEmail() {
		return email;
	}

	public void setEmail(String email) {
		this.email = email;
	}

	private int level; //会员等级
	private int checkInCount; //签到统计
	private int score; 	//会员积分
	private int honeyCount;  //密友统计
	private int fansCount;
	
	private int badgeCount;  //徽章统计

	private int mayorCount;
	private int feedFlag; //共享到其他网站的标志位
	
	private String password;// for UserService.validateUser()
	
	// 4.6 feedbackService need this property
	private String cellPhone;
	private String cellPhoneUnmasked;

	//4.7 add
	private int favoCount;
	private int reviewCount;
	private int photoCount;
	private int couponCount;
	//tuangou app
	private String phone;
	// 2012.09.03 security
	private String phoneUnmasked;
	private boolean isLocked;
	private String balance;
	private int favoriteCount;
	private int discountCount;
	private boolean supportOnekey;
	
	public int getFavoriteCount() {
		return favoriteCount;
	}

	public void setFavoriteCount(int favoriteCount) {
		this.favoriteCount = favoriteCount;
	}

	public int getDiscountCount() {
		return discountCount;
	}

	public void setDiscountCount(int discountCount) {
		this.discountCount = discountCount;
	}

	public boolean isSupportOnekey() {
		return supportOnekey;
	}

	public void setSupportOnekey(boolean supportOnekey) {
		this.supportOnekey = supportOnekey;
	}

	static final List<KeyValuePair> LIST=new ArrayList<KeyValuePair>();
	static{
		LIST.add(new KeyValuePair("userID", 0x8dd6));
		LIST.add(new KeyValuePair("nickName", 0x88ac));
//		list.add(new KeyValuePair("avatar", 0xd8ee));
		LIST.add(new KeyValuePair("userPower", 0xf903));
		LIST.add(new KeyValuePair("cityID", 0xa7b4));
		LIST.add(new KeyValuePair("isHoney", 0x2e1f));
		LIST.add(new KeyValuePair("isFans", 0x232d));
		LIST.add(new KeyValuePair("isInvited", 0xd066));
		LIST.add(new KeyValuePair("time", 0xc6ca));
		LIST.add(new KeyValuePair("token", 0xcd0a));
		LIST.add(new KeyValuePair("newToken", 0xdd0c));
		LIST.add(new KeyValuePair("mana", 0x7a43));
		LIST.add(new KeyValuePair("level", 0xaf3a));
		LIST.add(new KeyValuePair("checkInCount", 0xe8d7));
		LIST.add(new KeyValuePair("score", 0x4ab2));
		LIST.add(new KeyValuePair("honeyCount", 0xcfd));
		LIST.add(new KeyValuePair("fansCount", 0xeb18));
		LIST.add(new KeyValuePair("badgeCount", 0xdafe));
		LIST.add(new KeyValuePair("mayorCount", 0xa3b4));
		LIST.add(new KeyValuePair("feedFlag", 0x4629));
		LIST.add(new KeyValuePair("email", 0x5883));
		LIST.add(new KeyValuePair("favoCount", 0xf2ee));
		LIST.add(new KeyValuePair("reviewCount", 0x5a9c));
		LIST.add(new KeyValuePair("photoCount", 0x5919));
		LIST.add(new KeyValuePair("couponCount", 0x9083));
		LIST.add(new KeyValuePair("snsNickName", 0xe72));
		LIST.add(new KeyValuePair("snsImportedFlags", 0x5a48));
		
		LIST.add(new KeyValuePair("dcash",0x9f3e));

		LIST.add(new KeyValuePair("phone", 0x49d6));
		LIST.add(new KeyValuePair("isLocked", 0x6269));
		LIST.add(new KeyValuePair("balance", 0x49bd));
		LIST.add(new KeyValuePair("favoriteCount", ApiUtil.getHash("FavoriteCount")));
		LIST.add(new KeyValuePair("discountCount", ApiUtil.getHash("DiscountCount")));
		LIST.add(new KeyValuePair("supportOnekey", ApiUtil.getHash("SupportOnekey")));

		LIST.add(new KeyValuePair("gender",0xa00b));
	}

	static final List<KeyValuePair> LISTWITHAVATAR96X96=new ArrayList<KeyValuePair>();
	static{
		LISTWITHAVATAR96X96.addAll(LIST);
		LISTWITHAVATAR96X96.add(new KeyValuePair("avatar96x96", 0xd8ee));
	}
	
	static final List<KeyValuePair> LISTWITHAVATAR150X150=new ArrayList<KeyValuePair>();
	static{
		LISTWITHAVATAR150X150.addAll(LIST);
		LISTWITHAVATAR150X150.add(new KeyValuePair("avatar150x150", 0xd8ee));
	}
	
	public long getUserID() {
		return userID;
	}

	public void setUserID(long userID) {
		this.userID = userID;
	}

	public String getNickName() {
		return nickName;
	}

	public void setNickName(String nickName) {
		this.nickName = nickName;
	}

	public String getAvatar() {
		return avatar;
	}

	public void setAvatar(String avatar) {
		this.avatar = UserHelper.formatUserFace(avatar);
	}

	public int getUserPower() {
		return userPower;
	}

	public void setUserPower(int userPower) {
		this.userPower = userPower;
	}

	public int getCityID() {
		return cityID;
	}

	public void setCityID(int cityID) {
		this.cityID = cityID;
	}

	public boolean isIsHoney() {
		return isHoney;
	}

	public void setIsHoney(boolean isHoney) {
		this.isHoney = isHoney;
	}
	
	public boolean isIsFans() {
		return isFans;
	}
	
	public void setIsFans(boolean isFans) {
		this.isFans = isFans;
	}
	
	public boolean isIsInvited() {
		return isInvited;
	}

	public void setIsInvited(boolean isInvited) {
		this.isInvited = isInvited;
	}

	public Date getTime() {
		return time;
	}

	public void setTime(Date time) {
		this.time = time;
	}
	
	public String getToken() {
		return token;
	}

	public void setToken(String token) {
		this.token = token;
	}
	
	public int getMana() {
		return mana;
	}

	public void setMana(int mana) {
		this.mana = mana;
	}

	public int getLevel() {
		return level;
	}

	public void setLevel(int level) {
		this.level = level;
	}

	public int getCheckInCount() {
		return checkInCount;
	}

	public void setCheckInCount(int checkInCount) {
		this.checkInCount = checkInCount;
	}

	public int getScore() {
		return score;
	}

	public void setScore(int score) {
		this.score = score;
	}

	public int getHoneyCount() {
		return honeyCount;
	}

	public void setHoneyCount(int honeyCount) {
		this.honeyCount = honeyCount;
	}
	
	public int getFansCount() {
		return fansCount;
	}

	public void setFansCount(int fansCount) {
		this.fansCount = fansCount;
	}

	public int getBadgeCount() {
		return badgeCount;
	}

	public void setBadgeCount(int badgeCount) {
		this.badgeCount = badgeCount;
	}

	public int getMayorCount() {
		return mayorCount;
	}

	public void setMayorCount(int mayorCount) {
		this.mayorCount = mayorCount;
	}

	public int getFeedFlag() {
		return feedFlag;
	}

	public void setFeedFlag(int feedFlag) {
		this.feedFlag = feedFlag;
	}

	public void setPassword(String password) {
		this.password = password;
	}

	public String getPassword() {
		return password;
	}

	
	public String getCellPhone() {
		return cellPhone;
	}

	public void setCellPhone(String cellPhone) {
		this.cellPhone = cellPhone;
	}
 
	 
	public int getFavoCount() {
		return favoCount;
	}

	public void setFavoCount(int favoCount) {
		this.favoCount = favoCount;
	}

	public int getReviewCount() {
		return reviewCount;
	}

	public void setReviewCount(int reviewCount) {
		this.reviewCount = reviewCount;
	}

	public int getPhotoCount() {
		return photoCount;
	}

	public void setPhotoCount(int photoCount) {
		this.photoCount = photoCount;
	}

	public int getCouponCount() {
		return couponCount;
	}

	public void setCouponCount(int couponCount) {
		this.couponCount = couponCount;
	}

	public int getDcash() {
		return dcash;
	}

	public void setDcash(int dcash) {
		this.dcash = dcash;
	}
	
	
	
	public String getPhone() {
		return phone;
	}

	public void setPhone(String phone) {
		this.phone = phone;
	}



	public boolean isIsLocked() {
		return isLocked;
	}

	public void setIsLocked(boolean isLocked) {
		this.isLocked = isLocked;
	}

	public String getBalance() {
		return balance;
	}

	public void setBalance(String balance) {
		this.balance = balance;
	}

	@Override
	public List<KeyValuePair> getSerializeList(ClientInfo clientInfo, Version version){
		if(clientInfo.getClient() == ClientType.WinPhone){
			return LISTWITHAVATAR150X150;
		}else{
			return LISTWITHAVATAR96X96;
		}
	}

	/* (non-Javadoc)
	 * @see com.dianping.api.netio.DPEncoder#getClassId()
	 */
	@Override
	public int getClassId() {
 		return 0x2dc4;
	}

	public String getPhoneUnmasked() {
		return phoneUnmasked;
	}

	public void setPhoneUnmasked(String phoneUnmasked) {
		this.phoneUnmasked = phoneUnmasked;
	}

	public String getCellPhoneUnmasked() {
		return cellPhoneUnmasked;
	}

	public void setCellPhoneUnmasked(String cellPhoneUnmasked) {
		this.cellPhoneUnmasked = cellPhoneUnmasked;
	}

	public String getNewToken() {
		return newToken;
	}

	public void setNewToken(String newToken) {
		this.newToken = newToken;
	}
	
	public Boolean getUserSex() {
		return userSex;
	}

	public void setUserSex(Boolean userSex) {
		this.userSex = userSex;
		if(userSex == null) {
			gender = 0;
		} else if(userSex.booleanValue()) {
			gender = 1;
		} else {
			gender = 2;
		}
	}
	
	public static final int GENDER_MALE = 1;
	public static final int GENDER_FEMALE = 2;
	/**
	 * @return 1 男 2女 0未填写
	 */
	public int getGender() {
		return gender;
	}

	/**
	 * @return 1 男 2女 0未填写
	 */
	public void setGender(int gender) {
		this.gender = gender;
	}
	 
}


