package com.dianping.api.common.entity;

import com.dianping.api.common.util.KeyValuePair;
import com.dianping.api.domain.ClientInfo;
import com.dianping.api.domain.Version;
import com.dianping.api.netio.DPEncoder;
import com.google.common.collect.Lists;

import java.io.Serializable;
import java.util.List;

/**
 * User: yaou.li
 * Date: 14-5-14
 * Time: 下午7:46
 * To change this template use File | Settings | File Templates.
 */
public class BankElement extends DPEncoder implements Serializable {

    private static final long serialVersionUID = -1249342002823728643L;

    private String bankCode;
    private String bankName;
    private String paymentID;
    private String tip;
    private String icon;
    private String iconUrl;
    private String rankLetter;
    private int rank;

    private static final List<KeyValuePair> LIST = Lists.newArrayList();
    static {
        LIST.add(new KeyValuePair("bankCode",0x9b6a));
        LIST.add(new KeyValuePair("bankName",0x4e00));
        LIST.add(new KeyValuePair("paymentID",0x83c8));
        LIST.add(new KeyValuePair("tip",0x487a));
        LIST.add(new KeyValuePair("icon",0xb0bb));
        LIST.add(new KeyValuePair("iconUrl",0xeef0));
        LIST.add(new KeyValuePair("rankLetter",0xd474));
        LIST.add(new KeyValuePair("rank",0xc06a));
    }

    @Override
    public int getClassId() {
        return 0xe6b;
    }

    @Override
    public List<KeyValuePair> getSerializeList(ClientInfo clientInfo, Version version) {
        return LIST;
    }

    public String getBankCode() {
        return bankCode;
    }

    public void setBankCode(String bankCode) {
        this.bankCode = bankCode;
    }

    public String getBankName() {
        return bankName;
    }

    public void setBankName(String bankName) {
        this.bankName = bankName;
    }

    public String getPaymentID() {
        return paymentID;
    }

    public void setPaymentID(String paymentID) {
        this.paymentID = paymentID;
    }

    public String getTip() {
        return tip;
    }

    public void setTip(String tip) {
        this.tip = tip;
    }

    public String getIcon() {
        return icon;
    }

    public void setIcon(String icon) {
        this.icon = icon;
    }

    public String getIconUrl() {
        return iconUrl;
    }

    public void setIconUrl(String iconUrl) {
        this.iconUrl = iconUrl;
    }

    public String getRankLetter() {
        return rankLetter;
    }

    public void setRankLetter(String rankLetter) {
        this.rankLetter = rankLetter;
    }

    public int getRank() {
        return rank;
    }

    public void setRank(int rank) {
        this.rank = rank;
    }
}
