package com.dianping.api.common.enums;

/**
 * Created by huawei.li on 15/5/13.
 */
public enum FeatureSwitchEnum {
    MASTER_DISCOUNT("pay-api-mobile.coreprocess.switch.loaddiscounts",true),
    REDUCTION("pay-api-mobile.coreprocess.switch.loadreduction",true),
    COUPON("pay-api-mobile.coreprocess.switch.loadcoupon",true),
    POINT("pay-api-mobile.coreprocess.switch.loadpoint",true),
    ACCOUNT("pay-api-mobile.coreprocess.switch.loadaccount",true),
    ;
    public final String lionKey;
    public final boolean defaultValue;
    private FeatureSwitchEnum(String lionKey, boolean defaultValue){
        this.lionKey = lionKey;
        this.defaultValue = defaultValue;
    }
}
