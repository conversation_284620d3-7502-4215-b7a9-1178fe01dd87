package com.dianping.api.domain;

import com.dianping.cat.Cat;
import org.apache.commons.lang3.StringUtils;

import com.dianping.api.util.StringUtil;

public class Version {
	private String version;  //major version + "."+minor version +"." +build + ...
	private int[] versionNum;
    public static final Version CARD_VERSION = new Version("5.9.5");
    public static final Version M2_VERSION = new Version("8.1.0");
	public Version(String version) {	
		boolean isValid = true;
		
			if(!StringUtil.isNullOrEmpty(version)) {
				String[] s = version.split("\\.");
				int[] versions = new int[s.length];
				for(int i=0; i< s.length; i++) {
					int num = Integer.parseInt(s[i]);
					if(num < 0 ) {
						isValid = false;
					} else {
						versions[i] = num;
					}
				}
				if(isValid) {
					this.version = version;
					this.versionNum = versions;
				} else {
					throw new IllegalArgumentException();
				}
			} else {
				throw new IllegalArgumentException();
			}

	}
	 
	public String getVersion() {
		return this.version;
	}
	
	public String toString() {
		return this.version;
	}

	public int[] getVersionNum() {
		return versionNum;
	}

	public void setVersionNum(int[] versionNum) {
		this.versionNum = versionNum;
	}

	/** return 1,versionA>versionB
	 *  return 0,versionA=versionB
	 *  return -1,versionA<versionB	  	
	 * @param versionA
	 * @param versionB
	 * @return
	 */
	public static int compareTo(String versionA, String versionB) {
        Cat.logEvent("INVALID_METHOD_4", "com.dianping.api.domain.Version.compareTo(java.lang.String,java.lang.String)");
        if (StringUtils.isBlank(versionA)) {
			return -1;
		} else if (StringUtils.isBlank(versionB)) {
			return 1;
		}
		return compareTo(new Version(versionA), new Version(versionB));	
	}
	
	/** 
	 * 
	 * return 1,versionA>versionB
	 * return 0,versionA=versionB
	 * return -1,versionA<versionB	 
	 * @param versionA
	 * @param versionB
	 * @return
	 */
	public static int compareTo(Version versionA,Version versionB) {
        Cat.logEvent("INVALID_METHOD_2", "com.dianping.api.domain.Version.compareTo(com.dianping.api.domain.Version,com.dianping.api.domain.Version)");
        if (versionA == null) {
			return -1;
		} else if (versionB == null) {
			return 1;
		}
		int[] versionASplit = versionA.getVersionNum();
		int[] versionBSplit = versionB.getVersionNum();
		
		if(versionASplit.length != versionBSplit.length) {
			if(versionASplit.length > versionBSplit.length) {
				for(int l = 0; l < versionBSplit.length; l++) {
					if(versionASplit[l] > versionBSplit[l]) {
						return 1;
					} else if(versionASplit[l] < versionBSplit[l]) {
						return -1;
					} else {
						continue;
					}
				}
				for(int i = versionASplit.length - 1; i >= versionBSplit.length; i--) {
					if(versionASplit[i] != 0 ) {
						return 1;
					}
				}
				return 0;
			} else {
				for(int h = 0; h < versionASplit.length; h++) {
					if(versionASplit[h] > versionBSplit[h]) {
						return 1;
					} else if(versionASplit[h] < versionBSplit[h]) {
						return -1;
					} else {
						continue;
					}
				}
				for(int j = versionBSplit.length - 1; j >= versionASplit.length; j--) {
					if(versionBSplit[j] != 0) {
						return -1;
					}
				}
				return 0;
			}
		} else {
			for(int k = 0; k < versionASplit.length; k++) {
				if(versionASplit[k] > versionBSplit[k]) {
					return 1;
				} else if(versionASplit[k] < versionBSplit[k]) {
					return -1;
				} else {
					continue;
				}
			}
			return 0;
		}		
	}
}
