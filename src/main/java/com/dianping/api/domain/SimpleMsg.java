package com.dianping.api.domain;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

import com.dianping.api.common.util.KeyValuePair;
import com.dianping.api.netio.DPEncoder;

public class SimpleMsg extends DPEncoder implements Serializable {

	private static final long serialVersionUID = -6132712792499880629L;

	/**
	 * client only have two state: 1 and not 1 
	 * here use ICON_ALERT to represent not ICON_INFO
	 */
	public final static int ICON_INFO  = 1; 
	public final static int ICON_ALERT = 0; 
	
	protected String title;
	protected String content;
	protected int icon = ICON_ALERT;
    protected int returnID;
	
	/**
	 * The flag is application dependent, different business module have different meaning.
	 * business module can use it to transfer extra info for they own purpose. 
	 */
	protected int flag;
 
	private static final List<KeyValuePair> list=new ArrayList<KeyValuePair>();
	static{
		list.add(new KeyValuePair("title", 0x36e9));
		list.add(new KeyValuePair("content",0x57b6));
		list.add(new KeyValuePair("icon",0xb0bb));		
		list.add(new KeyValuePair("flag",0x73ad));			 
		list.add(new KeyValuePair("returnID",0xefe5));
	}
	
	public SimpleMsg() {
	}

	public SimpleMsg(String title, String content) {
		this.title = title;
		this.content = content;
	}
	
	public SimpleMsg(String title, String content,int flag){
		this.title = title;
		this.content = content;
		this.flag = flag;
	}
 

	public String getTitle() {
		return title;
	}

	public void setTitle(String title) {
		this.title = title;
	}

	public String getContent() {
		return content;
	}

	public void setContent(String content) {
		this.content = content;
	}

	public int getIcon() {
		return icon;
	}

	public void setIcon(int icon) {
		this.icon = icon;
	}

	public int getFlag() {
		return flag;
	}

	public void setFlag(int flag) {
		this.flag = flag;
	}

    public int getReturnID() {
        return returnID;
    }

    public void setReturnID(int returnID) {
        this.returnID = returnID;
    }

    @Override
	public List<KeyValuePair> getSerializeList(ClientInfo clientInfo, Version version){
		return list;
	}

	/* (non-Javadoc)
	 * @see com.dianping.api.netio.DPEncoder#getClassId()
	 */
	@Override
	public int getClassId() {		 
		return 0x909d;
	}
	
}
