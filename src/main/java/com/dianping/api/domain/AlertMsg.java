package com.dianping.api.domain;

import com.dianping.api.common.util.KeyValuePair;
import com.dianping.cat.Cat;

import java.util.ArrayList;
import java.util.List;

/**
 * Created by hodophobia on 14-8-21.
 */
public class AlertMsg extends SimpleMsg {
    private static final long serialVersionUID = -291623998282144737L;
    private List<ResultAction> actionList;
    private static final List<KeyValuePair> LIST=new ArrayList<KeyValuePair>();
    static{
        LIST.add(new KeyValuePair("title", 0x36e9));
        LIST.add(new KeyValuePair("content",0x57b6));
        LIST.add(new KeyValuePair("icon",0xb0bb));
        LIST.add(new KeyValuePair("flag",0x73ad));
        LIST.add(new KeyValuePair("returnID",0xefe5));
        LIST.add(new KeyValuePair("actionList",0x913b));
    }
    public AlertMsg(){
        super();
    }
    public AlertMsg(String title,String content,int flag,List<ResultAction> actionList){
        super(title,content,flag);
        this.actionList = actionList;
    }
    public List<ResultAction> getActionList() {
        return actionList;
    }

    public void setActionList(List<ResultAction> actionList) {
        this.actionList = actionList;
    }
    @Override
    public List<KeyValuePair> getSerializeList(ClientInfo clientInfo, Version version){
        Cat.logEvent("INVALID_METHOD_3", "com.dianping.api.domain.AlertMsg.getSerializeList(com.dianping.api.domain.ClientInfo,com.dianping.api.domain.Version)");
        return LIST;
    }

    /* (non-Javadoc)
     * @see com.dianping.api.netio.DPEncoder#getClassId()
     */
    @Override
    public int getClassId() {
        Cat.logEvent("INVALID_METHOD_5", "com.dianping.api.domain.AlertMsg.getClassId()");
        return 0x5886;
    }
}
