package com.dianping.api.domain;

import java.util.ArrayList;
import java.util.List;

import com.dianping.api.common.util.KeyValuePair;
import com.dianping.api.netio.DPEncoder;

public abstract class ResultList<LType> extends DPEncoder{
	
	protected int recordCount; 
	protected int startIndex; 
	protected boolean isEnd; 
	protected List<LType> list;	
	protected int nextStartIndex;
	private boolean judgeSetList = false;
	private boolean judgeSetStart = false;
	private String emptyMsg;
	/**
	 * used to identify the unique query;  
	 */
	private String queryId = "-1";
	
	public String getQueryId() {
		return queryId;
	}


	public void setQueryId(String nextStartIndex) {
		this.queryId = nextStartIndex;
	}

	 
	public int getNextStartIndex() {
		return nextStartIndex;
	}


	public void setNextStartIndex(int nextStartIndex) {
		this.nextStartIndex = nextStartIndex;
	}
 
 
	protected static final List<KeyValuePair> RESULTLIST=new ArrayList<KeyValuePair>();
	
	static{
		RESULTLIST.add(new KeyValuePair("recordCount", 0x177d));
		RESULTLIST.add(new KeyValuePair("startIndex", 0xaa64));		
		RESULTLIST.add(new KeyValuePair("isEnd", 0xf0b));
		RESULTLIST.add(new KeyValuePair("list", 0x249a));
		RESULTLIST.add(new KeyValuePair("emptyMsg",0xa465));
		RESULTLIST.add(new KeyValuePair("nextStartIndex", 0x7503));
		RESULTLIST.add(new KeyValuePair("nextStartIndex", 0x5703));		
		RESULTLIST.add(new KeyValuePair("queryId",0x2d87,null,new Version("4.9"))); 
	}
	
	protected ResultList(){
		this.recordCount = 0;
		this.list = new ArrayList<LType>();
	}
	
	
	public int getRecordCount() {
		return recordCount;
	}


	/**
	 * FIXME: the recordCount should means the total record set size, not just current pagination query return size;
	 * @param recordCount
	 */
	public void setRecordCount(int recordCount) {
		this.recordCount = recordCount;
	}



	public int getStartIndex() {
		return startIndex;
	}



	public void setStartIndex(int startIndex) {
		this.startIndex = startIndex;
		judgeSetStart = true;
		autoChangeNextStartIndex();
	}

	private void autoChangeNextStartIndex(){
		if(judgeSetStart && judgeSetList
				   && (this.nextStartIndex<=this.startIndex)
				   && this.list!=null
				   && this.list.size()>0){
			this.nextStartIndex = this.startIndex + this.list.size();
		}		
	}

	public boolean isIsEnd() {
		return isEnd;
	}



	public void setIsEnd(boolean isEnd) {
		this.isEnd = isEnd;
	}



	public List<LType> getList() {
		return list;
	}


	public void setList(List<LType> list) {
		this.list = list;	
		this.judgeSetList = true;
		autoChangeNextStartIndex();
	}

	 @Override
	public List<KeyValuePair> getSerializeList(ClientInfo clientInfo, Version version ){ 		
		 return RESULTLIST;
	}
	
	
	public String getEmptyMsg() {
		return emptyMsg;
	}


	public void setEmptyMsg(String emptyMsg) {
		this.emptyMsg = emptyMsg;
	}	
	
	
}
