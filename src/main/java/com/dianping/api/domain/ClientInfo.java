package com.dianping.api.domain;

import com.dianping.api.domain.Enum.AppType;
import com.dianping.api.domain.Enum.ClientType;
import com.dianping.api.framework.Controller;
import com.dianping.api.util.ApiUtil;

public class ClientInfo {

	private ClientType client;
	private AppType app;
	//个人理解：此处version本意为app版本，但是从实际set的值看，如果是点评用户，则该值为app版本，如果是美团用户，该值为mapi的版本，app版本值应该为source
	private Version version;
	private String mtVersion;
	private String osVersion;
	private String deviceId;
    private String extra;
    private String dpId;
    private String mtId;
    private String originalUA;
    private String cx;
    private String ip;
    private int platform;

    public ClientInfo() {
    }

    public ClientInfo(ClientType client, AppType app, Version version, String osVersion, String deviceId) {
		this.client = client;
		this.app = app;
		this.version = version;
		this.osVersion = osVersion;
		this.deviceId = deviceId;
	}
    public ClientInfo(ClientType client, AppType app, Version version, String osVersion, String deviceId,String dpId) {
        this.client = client;
        this.app = app;
        this.version = version;
        this.osVersion = osVersion;
        this.deviceId = deviceId;
        this.dpId = dpId;
    }
    public ClientInfo(Controller controller) {
        if(controller!=null){
            this.client = controller.getClient();
            this.app = controller.getAppType();
            this.version = controller.getVersion();
            this.mtVersion = controller.getSource();
            this.osVersion = controller.getOsVersion();
            this.deviceId = controller.getDeviceId();
            this.dpId = controller.getDpId();
            this.mtId = controller.getMtId();
            this.originalUA = controller.getOriginalUA();
            this.cx = controller.getCx();
            this.ip = controller.getUserIP();
            this.platform = ApiUtil.paymentPlatform(controller.getClient(), controller.getAppType());
        }else{
            this.client = ClientType.UnKnow;
            this.app = AppType.Unknown;
        }
    }

	public ClientType getClient() {
		return client;
	}

	public void setClient(ClientType client) {
		this.client = client;
	}

	public AppType getApp() {
		return app;
	}

	public void setApp(AppType app) {
		this.app = app;
	}

	public Version getVersion() {
		return version;
	}

	public void setVersion(Version version) {
		this.version = version;
	}

	public String getOsVersion() {
		return osVersion;
	}

	public void setOs(String osVersion) {
		this.osVersion = osVersion;
	}

	public String getDeviceId() {
		return deviceId;
	}

	public void setDeviceId(String deviceId) {
		this.deviceId = deviceId;
	}

    public String getExtra() {
        return extra;
    }

    public void setExtra(String extra) {
        this.extra = extra;
    }

    public String getDpId() {
        return dpId;
    }

    public void setDpId(String dpId) {
        this.dpId = dpId;
    }

    public String getOriginalUA() {
        return originalUA;
    }

    public void setOriginalUA(String originalUA) {
        this.originalUA = originalUA;
    }

    public String getCx() {
        return cx;
    }

    public void setCx(String cx) {
        this.cx = cx;
    }

    public void setOsVersion(String osVersion) {
        this.osVersion = osVersion;
    }

    public String getIp() {
        return ip;
    }

    public void setIp(String ip) {
        this.ip = ip;
    }

    public int getPlatform() {
        return platform;
    }

    public void setPlatform(int platform) {
        this.platform = platform;
    }

    public String getMtId() {
        return mtId;
    }

    public void setMtId(String mtId) {
        this.mtId = mtId;
    }

    public String getMtVersion() {
        return mtVersion;
    }

    public void setMtVersion(String mtVersion) {
        this.mtVersion = mtVersion;
    }
}
