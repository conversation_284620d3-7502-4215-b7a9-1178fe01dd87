
package com.dianping.api.domain;

/**
 * <AUTHOR>
 *
 */
public class EnumConstants {
	
	public EnumConstants() { }
	
	//queue type	 
	public static final int QUEUETYPE_MOBILE=1;	
	public static final int QUEUETYPE_FEED  =2;
	
	//client type	
	public static final int CLIENTTYPE_ANDROID    =1;
	public static final int CLIENTTYPE_IPHONE     =2;	
	public static final int CLIENTTYPE_NOKIAWIDGET=3;	
	public static final int CLIENTTYPE_SYMBIANS60 =4;	
	public static final int CLIENTTYPE_KJAVA	  =5;	
	public static final int CLIENTTYPE_UNKNOWN	  =0;
	
	//shop type
	public static final int SHOPTYPE_FOOD	  =10;	
	public static final int SHOPTYPE_SHOPPING =20;	
	public static final int SHOPTYPE_LIFT	  =30;	
	public static final int SHOPTYPE_SERVICE  =40;	
	public static final int SHOPTYPE_SPORTS   =45;	
	public static final int SHOPTYPE_BEAUTY   =50;	
	public static final int SHOPTYPE_WEDDING  =55;	
	public static final int SHOPTYPE_HOTEL    =60;
	public static final int SHOPTYPE_CAR      =65;	
	public static final int SHOPTYPE_BABY     =70;	
	public static final int SHOPTYPE_OTHER    =80;	
	
	//shop status type
	public static final int SHOPSTATUS_NORMAL = 0;
	public static final int SHOPSTATUS_CLOSED = 1;
	public static final int SHOPSTATUS_NOTOPEN = 2;
	public static final int SHOPSTATUS_STOPOPEN = 3;
	public static final int SHOPSTATUS_MOBILEONLY = 4;
	
	
	//check in status	
	public static final int CHECKINSTATUS_PRIVATE=0;
	public static final int CHECKINSTATUS_TRANSITION=1;
	public static final int CHECKINSTATUS_SHARED =2;	
	public static final int CHECKINSTATUS_ALL    =-2;
	
	//check in add verify
	
	public static final int CHECKIN_ADDVERIFY_ABLE            =-1;	
	public static final int CHECKIN_ADDVERIFY_OUTOFRANGE      =-2;	
  //public static final int CHECKIN_ADDVERIFY_SOFAST          =0;	
	public static final int CHECKIN_ADDVERIFY_DISABLESHOP     =1;	
	public static final int CHECKIN_ADDVERIFY_CHECKINSHOPLIMIT=2;	
	
	//user feed type	
	public static final int USERFEEDTYPE_SINAMINIBLOG = 1; // 新浪微博
	public static final int USERFEEDTYPE_KAIXIN = 2; // 开心
	public static final int USERFEEDTYPE_QQMINIBLOG = 4; // 腾讯微博
	public static final int USERFEEDTYPE_RENREN = 8; // 人人网
	public static final int USERFEEDTYPE_ALIPAY = 16; // 支付宝
	public static final int USERFEEDTYPE_QZONE = 32; // qq账号 登录 以及分享到qzone
	
	//oauth type
	public static final int OAUTH_V1 = 1;
	public static final int OAUTH_V2 = 2;
	
	//notification content style	
	public static final int NOTIFICATION_CONTENTSTYLE_BOLD		 =0x08000000;	
	public static final int NOTIFICATION_CONTENTSTYLE_ITALIC       =0x04000000;	
	public static final int NOTIFICATION_CONTENTSTYLE_UNDERLINE    =0x02000000;	
	public static final int NOTIFICATION_CONTENTSTYLE_STRIKETHROUGH=0x01000000;	
	public static final int NOTIFICATION_CONTENTSTYLE_BLACK        =0x00000000;	
	public static final int NOTIFICATION_CONTENTSTYLE_COFFEE       =0x00663300;	
	public static final int NOTIFICATION_CONTENTSTYLE_GRAY         =0x00999999;
	 
	
	//score item
	public static final int SCOREITEM_SCORE1	=1;	
	public static final int SCOREITEM_SCORE2	=2;
	public static final int SCOREITEM_SCORE3	=3;	
	public static final int SCOREITEM_AVEPRICE=4;
	 
	//push type
	public static final int PushType_Events 		 = 1;
    public static final int PushType_Announcement  = 2;
    public static final int PushType_VersionUpdate = 4;
    public static final int PushType_Notification  = 8;
    public static final int PushType_Tuangou       =16;
    
    //BadgeTaskCountType
    
    public static final int BADGETASK_RANGECOUNT=1;
    
    public static final int BADGETASK_RANGEDISTINCTSHOPCOUNT=2;
    
    public static final int BADGETASK_RANGEDISTINCTDATECOUNT=3;
    
    public static final int BADGETASK_COUNT=4;
    
    public static final int BADGETASK_DISTINCTSHOPCOUNT=5;
	
    public static final int BADGETASK_DISTINCTDATECOUNT=6;
    
    
    // userService requestCode resultEnum
    public static final int REQUESTCODE_SUCCESS = 1;
    public static final int REQUESTCODE_FAILED_USEREXIST = 2;
    public static final int REQUESTCODE_FAILED_FREQUENTOP = 3;
    
    // userService verifyCode resultEnum
    public static final int VERIFYCODE_SUCCESS = 1;
    public static final int VERIFYCODE_FAILED_JFUSER = 2;
    public static final int VERIFYCODE_FAILED_NOTMATCH = 3;
    public static final int VERIFYCODE_FAILED_EXPIRED = 4;
}
