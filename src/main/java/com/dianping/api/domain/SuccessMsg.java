package com.dianping.api.domain;

import com.dianping.api.common.util.KeyValuePair;
import com.dianping.cat.Cat;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

public class SuccessMsg extends SimpleMsg implements Serializable {

	private static final long serialVersionUID = -1714466438519140938L;
    private List<ResultAction> actionList;

	public SuccessMsg() {
		this.icon = ICON_INFO;		 
	}

	public SuccessMsg(String title, String content) {
		super(title,content);
		this.icon = ICON_INFO;
	}
	
	public SuccessMsg(String title, String content,int flag, int returnID){
		this.title = title;
		this.content = content;
		this.flag = flag;
		this.icon = ICON_INFO;
        this.returnID = returnID;
	}

    private static final List<KeyValuePair> LIST=new ArrayList<KeyValuePair>();
    static{
        LIST.add(new KeyValuePair("title", 0x36e9));
        LIST.add(new KeyValuePair("content",0x57b6));
        LIST.add(new KeyValuePair("icon",0xb0bb));
        LIST.add(new KeyValuePair("flag",0x73ad));
        LIST.add(new KeyValuePair("returnID",0xefe5));
        LIST.add(new KeyValuePair("actionList",0x913b));
    }

    public List<ResultAction> getActionList() {
        return actionList;
    }

    public void setActionList(List<ResultAction> actionList) {
        this.actionList = actionList;
    }

    /* (non-Javadoc)
         * @see com.dianping.api.netio.DPEncoder#getClassId()
         */
	@Override
	public int getClassId() {
        Cat.logEvent("INVALID_METHOD_5", "com.dianping.api.domain.SuccessMsg.getClassId()");
        return 0x6389;
	}

    @Override
    public List<KeyValuePair> getSerializeList(ClientInfo clientInfo, Version version){
        Cat.logEvent("INVALID_METHOD_2", "com.dianping.api.domain.SuccessMsg.getSerializeList(com.dianping.api.domain.ClientInfo,com.dianping.api.domain.Version)");
        return LIST;
    }


}
