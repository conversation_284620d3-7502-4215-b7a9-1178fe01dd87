package com.dianping.api.domain;

import java.util.regex.Pattern;

public final class Enum {
	
	private Enum() {
	}
	
	public enum QueueType {
		Mobile(1), Feed(2);
		private final int intValue;
		QueueType(int intValue) {
			this.intValue = intValue;
		}
		public int getIntValue() {
			return intValue;
		}
	}

	static final Pattern IPHONE_REGEX 	      = Pattern.compile("\\(dpscope ([0-9\\.]+)(.*);(.*)\\)", Pattern.CASE_INSENSITIVE);
	static final Pattern ANDROID_REGEX        = Pattern.compile("\\(com\\.dianping\\.v1 ([0-9\\.]+)(.*);(.*)\\)", Pattern.CASE_INSENSITIVE);
//	static final Pattern SYMBIANS60V3_REGEX   = Pattern.compile("\\(dianping_s60v3 ([0-9\\.]+)(.*);(?:.*)\\)", Pattern.CASE_INSENSITIVE);
//	static final Pattern SYMBIANS60V5_REGEX   = Pattern.compile("\\(dianping_s60v5 ([0-9\\.]+)(.*);(?:.*)\\)", Pattern.CASE_INSENSITIVE);
//	static final Pattern SYMBIAN3_REGEX       = Pattern.compile("\\(dianping_Symbian3 ([0-9\\.]+)(.*);(?:.*)\\)", Pattern.CASE_INSENSITIVE);
//	static final Pattern KJAVA_REGEX           = Pattern.compile("\\(com.dianping.kjava ([0-9\\.]+)(.*);(?:.*)\\)", Pattern.CASE_INSENSITIVE);
	static final Pattern WP_REGEX              = Pattern.compile("\\(com\\.dianping\\.wp ([0-9\\.]+)(.*);(.*)\\)", Pattern.CASE_INSENSITIVE);
//	static final Pattern BLACKBERRY_REGEX      = Pattern.compile("\\(com.dianping.bb ([0-9\\.]+)(.*);(?:.*)\\)", Pattern.CASE_INSENSITIVE);	 
//	static final Pattern BADA_REGEX            = Pattern.compile("\\(bada ([0-9\\.]+)(.*);(?:.*)\\)", Pattern.CASE_INSENSITIVE);   
	static final Pattern IPADHD_REGEX         = Pattern.compile("\\(dpscopehd ([0-9\\.]+)(.*);(.*)\\)",Pattern.CASE_INSENSITIVE);
//	static final Pattern MEEGO_REGEX          = Pattern.compile("\\(dianping_Meego ([0-9\\.]+)(.*);(?:.*)\\)",Pattern.CASE_INSENSITIVE);
	static final Pattern WIN8_REGEX           = Pattern.compile("\\(dianping.mobile.win8 ([0-9\\.]+)(.*);(.*)\\)", Pattern.CASE_INSENSITIVE);
 
	static final Pattern TG_IPHONE_REGEX 	      = Pattern.compile("\\(dptuan ([0-9\\.]+)(.*);(.*)\\)", Pattern.CASE_INSENSITIVE);
	static final Pattern TG_ANDROID_REGEX        = Pattern.compile("\\(com\\.dianping\\.t ([0-9\\.]+)(.*);(.*)\\)", Pattern.CASE_INSENSITIVE);

    static final Pattern TUAN_IPHONE_REGEX = Pattern.compile("(com\\.dianping(\\.ba)?\\.tuan)", Pattern.CASE_INSENSITIVE);
    static final Pattern TUAN_ANDROID_REGEX = Pattern.compile("(com\\.dianping\\.t)$", Pattern.CASE_INSENSITIVE);
    static final Pattern MT_IPHONE_REGEX = Pattern.compile("mapi\\s*([0-9\\.]+)\\s*\\(mtscope ([0-9\\.]+)([^\\n]*);(?:.*)\\)", Pattern.CASE_INSENSITIVE);
    static final Pattern MT_ANDROID_REGEX = Pattern.compile("mapi\\s*([0-9\\.]+)\\s*\\(com.sankuai.meituan ([0-9\\.]+)(.*);(?:.*)\\)", Pattern.CASE_INSENSITIVE);

	public enum ClientType {
		Android(1,"Android",  ANDROID_REGEX), 
		iPhone(2, "iPhone",  IPHONE_REGEX), 
//		NokiaWidget(3,"NokiaWidget", null), 
//		SymbianS60v3(4,"SymbianS60v3", SYMBIANS60V3_REGEX), 
//		KJava(5,"KJava", KJAVA_REGEX), 
//		SymbianS60v5(6,"SymbianS60v5",SYMBIANS60V5_REGEX),
//		BlackBerry(7, "BlackBerry", BLACKBERRY_REGEX),
//		Bada(8, "Bada", BADA_REGEX),
//		Symbian3(9, "Symbian3", SYMBIAN3_REGEX),
		iPadHd(10, "iPadHd", IPADHD_REGEX),
		WinPhone(11, "WinPhone", WP_REGEX),
//		Meego(12, "Meego", MEEGO_REGEX),
		Win8Pad(13, "Win8Pad", WIN8_REGEX),
		UnKnow(0,"unknow",null),

		TG_Android(101, "TG_Android", TG_ANDROID_REGEX),
		TG_iPhone(102, "TG_iPhone", TG_IPHONE_REGEX),
        TUAN_iPhone(103, "TUAN_iPhone", TUAN_IPHONE_REGEX),
        TUAN_ANDROID(104, "TUAN_Android", TUAN_ANDROID_REGEX),
        MT_iPhone(201, "MT_iPhone", MT_IPHONE_REGEX),
        MT_Android(202, "MT_Android", MT_ANDROID_REGEX),
		MT_Weixin_Api(203, "MT_Weixin_Api", null),
		MT_WAP(204, "MT_WAP", null);

		public final int value;
		public final String key;
		public final Pattern pattern;

		ClientType(int intValue,String key, Pattern regex) {
			this.value = intValue;
			this.key = key;
			if(regex!=null) {
				this.pattern =  regex;
			}
			else {
				this.pattern = null;
			}
		}

        public boolean isMtClient() {
            return this == MT_iPhone || this == MT_Android || this == MT_Weixin_Api || this == MT_WAP;
        }
	}

    public enum ProductType {
        TUANGOU(0),
        TUANGOUCARD(1);

        private ProductType(int value) {
            this.value = value;
        }

        private int value;

        public int getValue() {
            return value;
        }
    }
	
	public enum AppType {
		Unknown(0), Main(1), TG(2);

		public final int value;

		AppType(int intValue) {
			this.value = intValue;
		}
	}

	public enum ShopType {
		Food(10), Shopping(20), Life(30), Service(40), Sports(45), Beauty(50), Wedding(
				55), Hotel(60), Car(65), Baby(70), Other(80);
		public final int intValue;

		ShopType(int intValue) {
			this.intValue = intValue;
		}

		public static ShopType getInstance(int value) {
			switch (value) {
			case 10:
				return Food;
			case 20:
				return Shopping;
			case 30:
				return Life;
			case 40:
				return Service;
			case 45:
				return Sports;
			case 50:
				return Beauty;
			case 55:
				return Wedding;
			case 60:
				return Hotel;
			case 65:
				return Car;
			case 70:
				return Baby;
			case 80:
				return Other;
			default:
				return null;
			}
		}
	}

	public enum CheckInStatus {
		Private(0), Shared(2), Transition(1), All(-2);
		public final int intValue;

		CheckInStatus(int intValue) {
			this.intValue = intValue;
		}
	}

	public enum CheckInAddVerify {
		Able(-1), OutOfRange(-2), SoFast(0);
		public final int intValue;

		CheckInAddVerify(int intValue) {
			this.intValue = intValue;
		}

		public static CheckInAddVerify getInstance(int value) {
			switch (value) {
			case -1:
				return Able;
			case -2:
				return OutOfRange;
			case 0:
				return SoFast;
			default:
				return null;
			}
		}
	}

	public enum UserFeedType {
		SinaMiniBlog(1), Kaixin(2),QQ(4), RenRen(8),Qzone(32);
		public final int intValue;

		UserFeedType(int intValue) {
			this.intValue = intValue;
		}

		public static UserFeedType getInstance(int value) {
			switch (value) {
			case 1:
				return SinaMiniBlog;
			case 2:
				return Kaixin;
			case 4:
				return QQ;
			case 8:
				return RenRen;
			case 32:
				return Qzone;
			default:
				return null;
			}
		}
	}

	public enum NotificationContentStyle {
		Bold(0x08000000), Italic(0x04000000), Underline(0x02000000), Strikethrough(
				0x01000000), Black(0x00000000), Coffee(0x00663300), Gray(
				0x00999999);
		public final int intValue;

		NotificationContentStyle(int intValue) {
			this.intValue = intValue;
		}
	}
	
	public enum CheckInQueuing{
		Q_NOparam(0), Q_0(1), Q_1_5(2), Q_5_10(3), Q_10_20(4),Q_up_20(5);
		private static String[] feedString ={null, "现在没有人排队", "现在有5人以下排队", "现在有6-10人排队", "现在有11-20人排队"
			, "现在有20人以上排队"};
		public final int intValue;
		CheckInQueuing(int intValue){
			this.intValue=intValue;
		}
		
		public static String getFeedQueueString(int value){
			if(value<0||value>=feedString.length){
				return null;
			} else{
				return feedString[value];
			}
		}
		
		public static String getQueueString(int value){
			switch(value){
			case 0: 
				return null;
			case 1: 
				return "无人排队";
			case 2:
				return "5人以下排队";
			case 3:
				return "6-10人排队";
			case 4:
				return "11-20人排队";
			case 5:
				return "20人以上排队";
			default:
				return null;
			}
		}
		public static CheckInQueuing getInstance(int value){
			switch(value){
			case 0:
				return Q_NOparam;
			case 1:
				return Q_0;
			case 2:
				return Q_1_5;
			case 3: 
				return Q_5_10;
			case 4: 
				return Q_10_20;
			case 5:
				return Q_up_20;
			default:
				return null;
			}
		}	
	}

	public enum LocaldishCity{
		Shanghai(1), Beijing(2), Hangzhou(3), Guangzhou(4), Nanjing(5), Suzhou(6), Shenzhen(7), Chengdu(8), Chongqing(9), Tianjing(10), Wuhan(16), Xian(17),Shenyang(18);
		public final int intValue;
		LocaldishCity(int intValue){
			this.intValue = intValue;
		}
		
		public static LocaldishCity getInstance(int value){
			for(LocaldishCity city :LocaldishCity.values()){
				if(city.intValue==value){
					return city;
				}	
			}	
			return null;
		}
		public static boolean isLocaldishCity(int value){
			return getInstance(value)!=null?true:false;
		}
	}
	
	public enum MobilePicType{
		no_param(0),dish(1),environment(2),recommendDish(3),others(10);
		private static final String[] DISHTAG = {"类型", "菜"};
		private static final String[] ENVIRONMENTTAG = {"类型", "环境"};
		private static final String[] OTHERSTAG = {"类型", "其他"};
		public final int value;
		public static MobilePicType getInstance(int value){
			switch(value){
			case 1:
				return dish;
			case 2:
				return environment;
			case 3:
				return recommendDish;
			case 10:
				return others;
			default:
				return no_param;
			}
		}
		MobilePicType(int value){
			this.value = value;
		}
		public static String[] getTags(MobilePicType type){
			switch(type){
			case dish:
			case recommendDish:
				return DISHTAG;
			case environment:
				return ENVIRONMENTTAG;
			case others:
				return OTHERSTAG;
			default:
				return null;
			}
		}
	}
	
	public enum CoordType {
		Gps("gps"),
		NetWork("network"),
		Cell("cell"),
		Wifi("wifi"),
		Cell2("cell2"),
		Illegal("illegal");
		
		public final String typeName;
		
		CoordType(String name) {
			this.typeName = name;
		}
		
		public String getTypeName() {
			return this.typeName;
		}
	}

}
