package com.dianping.api.domain;

import com.dianping.api.common.util.KeyValuePair;
import com.dianping.api.netio.DPEncoder;
import com.dianping.cat.Cat;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

/**
 * Created by hodophobia on 14-8-14.
 */
public class ResultAction extends DPEncoder implements Serializable {
    private static final long serialVersionUID = 4996379189911892882L;
    private int type;
    private String name;
    private String content;

    private static final List<KeyValuePair> list=new ArrayList<KeyValuePair>();
    static{
        list.add(new KeyValuePair("type", 0x372));
        list.add(new KeyValuePair("content",0x57b6));
        list.add(new KeyValuePair("name",0xee8f));
    }

    @Override
    public int getClassId() {
        Cat.logEvent("INVALID_METHOD_4", "com.dianping.api.domain.ResultAction.getClassId()");
        return 0xc99f;
    }

    @Override
    public List<KeyValuePair> getSerializeList(ClientInfo clientInfo, Version version) {
        Cat.logEvent("INVALID_METHOD_2", "com.dianping.api.domain.ResultAction.getSerializeList(com.dianping.api.domain.ClientInfo,com.dianping.api.domain.Version)");
        return list;
    }

    public int getType() {
        return type;
    }

    public void setType(int type) {
        this.type = type;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content;
    }
}
