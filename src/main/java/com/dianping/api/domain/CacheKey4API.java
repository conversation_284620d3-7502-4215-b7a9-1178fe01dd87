package com.dianping.api.domain;

import com.dianping.avatar.cache.CacheKey;
import com.dianping.avatar.log.AvatarLogger;
import com.dianping.avatar.log.AvatarLoggerFactory;
import com.dianping.cat.Cat;

/**
 * <AUTHOR>
 * 
 */
public final class CacheKey4API extends CacheKey {

	private static final long serialVersionUID = -1783424835100504943L;
	private static AvatarLogger log = AvatarLoggerFactory.getLogger(CacheKey4API.class);
	private static final String VERSION = "v0";
	private static final String KEY_CATEGORY_FORMAT = "%s.%s";

	/**
	 * duration = 1000 hours, means usually we won't change the value.
	 */
	public static final int TYPE_WEB = 0;
	/**
	 * duration = 1 hours, the data should not be too large
	 */
	public static final int TYPE_MC = 1;

	/**
	 * duration = 10 min
	 */
	public static final int TYPE_MC_10M = 2;

	/**
	 * local cache for deallist.bin. local, 热点
	 */
	public static final int TYPE_WEB_HOT = 3;

	/**
	 * @param category
	 * @param params
	 */
	private CacheKey4API(String category, Object... params) {
		super(category, params);
	}
	
	/**
	 * before <add name="mApi" duration="4" index="{0}.{1}"
	 * indexDesc="Para1|Para2"/>
	 * 
	 * @param type
	 * @param params
	 * @return
	 */
	public static CacheKey4API newInstance(int type, Object... params) {
        Cat.logEvent("INVALID_METHOD_2", "com.dianping.api.domain.CacheKey4API.newInstance(int,java.lang.Object[])");
        params = processParams(params);
		if (type == TYPE_WEB) {
			return newInstance_web(params);
		} else if (type == TYPE_MC) {
			return newInstance_mc(params);
		} else if (type == TYPE_MC_10M) {
			return newInstance_mc10m(params);
		} else if (type == TYPE_WEB_HOT) {
			return newInstance_web_hot(params);
		} else {
			log.error("cachekey type wrong!!!!");
		}
		return null;
	}

	private static Object[] processParams(Object... params) {
        Cat.logEvent("INVALID_METHOD_2", "com.dianping.api.domain.CacheKey4API.processParams(java.lang.Object[])");
        if (params != null && params.length > 0) {
			params[0] = String.format(KEY_CATEGORY_FORMAT, params[0], VERSION);
		}

		return params;
	}

	private static CacheKey4API newInstance_mc(Object... params) {
        Cat.logEvent("INVALID_METHOD_2", "com.dianping.api.domain.CacheKey4API.newInstance_mc(java.lang.Object[])");
        // return new CacheKey4API("mApi-java-mc", parseParams(params));
		// return new CacheKey4API("mApi", parseParams(params));
		if (params != null && params.length > 2) {
			log.error("cachekey parameter lengh wrong!!!!");
		}
		return new CacheKey4API("tuangou-mobile-mc", params);
	}

	private static CacheKey4API newInstance_mc10m(Object... params) {
        Cat.logEvent("INVALID_METHOD_3", "com.dianping.api.domain.CacheKey4API.newInstance_mc10m(java.lang.Object[])");
        return new CacheKey4API("tuangou-mobile-mc-10m", parseParams(params));
	}

	private static CacheKey4API newInstance_web(Object... params) {
        Cat.logEvent("INVALID_METHOD_1", "com.dianping.api.domain.CacheKey4API.newInstance_web(java.lang.Object[])");
        return new CacheKey4API("tuangou-mobile-web", parseParams(params));
	}

	private static CacheKey4API newInstance_web_hot(Object... params) {
        Cat.logEvent("INVALID_METHOD_1", "com.dianping.api.domain.CacheKey4API.newInstance_web_hot(java.lang.Object[])");
        return new CacheKey4API("tuangou-mobile-web-hot", parseParams(params));
	}

	private static String parseParams(Object... params) {
        Cat.logEvent("INVALID_METHOD_5", "com.dianping.api.domain.CacheKey4API.parseParams(java.lang.Object[])");
        int length = params.length;
		if (length != 1 && length != 2) {
			log.error("cachekey parameter lengh wrong!!!!");
			return null;
		}

		String param = (String) params[0];
		if (length == 2) {
			param = params[0] + "=" + params[1];
		}

		return param;
	}
}
