package com.dianping.api.servlet;

import com.dianping.api.domain.ClientInfo;
import com.dianping.api.domain.Enum.AppType;
import com.dianping.api.domain.Enum.ClientType;
import com.dianping.api.domain.SimpleMsg;
import com.dianping.api.domain.SuccessMsg;
import com.dianping.api.domain.Version;
import com.dianping.api.exception.IllegalApiParamException;
import com.dianping.api.exception.TryLaterException;
import com.dianping.api.framework.Controller;
import com.dianping.api.netio.DPJsonSerializer;
import com.dianping.api.netio.ParameterMap;
import com.dianping.api.netio.ResponseContent;
import com.dianping.api.servlet.exception.ControllerNotFoundException;
import com.dianping.api.util.DotUtils;
import com.dianping.avatar.log.AvatarLogger;
import com.dianping.avatar.log.AvatarLoggerFactory;
import com.dianping.avatar.tracker.ExecutionContextHolder;
import com.dianping.avatar.tracker.TrackerContext;
import com.dianping.cat.Cat;
import com.dianping.cat.message.Event;
import com.dianping.cat.message.MessageProducer;
import com.dianping.cat.message.Transaction;
import com.dianping.ops.remote.RemoteIpGetter;
import com.dianping.pay.api.exception.IssueCouponException;
import com.dianping.pay.common.enums.PayPlatform;
import com.dianping.pay.framework.core.DefaultMobileContextBuilder;
import com.dianping.pay.framework.datatypes.MobileContext;
import com.dianping.pay.framework.utils.PayPlatformUtils;
import org.apache.commons.lang.math.NumberUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.NoSuchBeanDefinitionException;
import org.springframework.web.context.WebApplicationContext;
import org.springframework.web.context.support.WebApplicationContextUtils;

import javax.servlet.ServletException;
import javax.servlet.http.HttpServlet;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.util.Enumeration;
import java.util.HashMap;

public class ApiMainJsonpServlet extends HttpServlet {

	private static final long serialVersionUID = 8266084862532226791L;
	private AvatarLogger LOG = AvatarLoggerFactory.getLogger("JSONAPI");

	private static final SimpleMsg MSG_BIZ_ERROR = new SimpleMsg("提示", "服务器暂不可用，请稍候再试~");
	private static final SimpleMsg MSG_FRAMEWORK_ERROR = new SimpleMsg("提示", "服务器暂不可用，请稍候再试!");
	private static final SimpleMsg MSG_JSON_ERROR = new SimpleMsg("提示", "服务器暂不可用，请稍候再试。");
	
	private static final SimpleMsg MSG_401 = new SimpleMsg("提示", "验证失败");
	private static final SimpleMsg MSG_404 = new SimpleMsg("错误", "您请求的地址不存在");

	private WebApplicationContext context;
	
	public void init() {
		LOG.info("main json servlet initialized");
		context = WebApplicationContextUtils.getRequiredWebApplicationContext(getServletContext());
	}
	
	@Override
	protected void doGet(HttpServletRequest request, HttpServletResponse response)
			throws ServletException, IOException {
		handleRequest(request, response, false);
	}
	
	@Override
	protected void doPost(HttpServletRequest request, HttpServletResponse response)
			throws ServletException, IOException {
        Cat.logEvent("INVALID_METHOD_1", "com.dianping.api.servlet.ApiMainJsonpServlet.doPost(javax.servlet.http.HttpServletRequest,javax.servlet.http.HttpServletResponse)");
        handleRequest(request, response, true);
	}
	
	protected void handleRequest(HttpServletRequest request, HttpServletResponse response,
			boolean isPost) throws ServletException, IOException {

        MobileContext context = new MobileContext(request);

        MessageProducer cat = Cat.getProducer();
		if (request != null) {
			DotUtils.addRefererDot(request);
		}

		Controller controller = null;
		int status = -1;
		Object result = null;
		try {
            request.setCharacterEncoding("UTF-8");

            controller = parseController(request);
            String controllerKey = controller.getControllerKey();
            controller.setJsonpCallback(request.getParameter("callback"));

            parseUserIp(request, controller);
            parseUserAgent(request, controller);
            int platform = NumberUtils.toInt(request.getParameter("platform"));
            if (platform == PayPlatform.mt_iphone_native.getCode()) {
                controller.setClient(ClientType.MT_iPhone);
            } else if (platform == PayPlatform.mt_android_native.getCode()) {
                controller.setClient(ClientType.MT_Android);
            } else if (PayPlatformUtils.isMtClient(platform)) {
            	controller.setClient(ClientType.MT_WAP);
			} else {
                controller.setClient(ClientType.iPhone);
            }
            controller.setAppType(AppType.TG);
            controller.setVersion(Version.M2_VERSION);
            controller.setJsonClient(true);
            controller.setSource("appstore");
            controller.setOsVersion("7.0");
            parseParameterMap(request, controller);

            controller.setDpId(controller.getParametersMap().getString("dpid"));
            controller.setCx(controller.getParametersMap().getString("cx"));

            new DefaultMobileContextBuilder().buildContext(context, this.getServletContext(), request, isPost, false, false);
            long userId = context.getUserId();
            controller.setWeixinUtm(ApiMainJsonServlet.getUtm(request));
            setMasterSlave(request, userId);
            controller.setUserID(userId);

            // request transaction
            Transaction t1 = cat.newTransaction("JSON Action", controllerKey);
            try {
                result = controller.doRequest();
                if (result == null) {
                    status = 400;
                    result = MSG_BIZ_ERROR;
                } else if (result instanceof SimpleMsg && !(result instanceof SuccessMsg)) {
                    status = 400;
                } else {
                    status = 200;
                }
                t1.setStatus(Transaction.SUCCESS);
            } catch (TokenNotValidException e) {
                result = MSG_401;
                status = 401;
                t1.setStatus(e);
            } catch (IllegalApiParamException e) {
                LOG.warn("IllegalApiParamException:" + e.getMessage());
                status = 450;
                result = new SimpleMsg(e.getTitle(), e.getMessage(), e.getFlag());
                t1.setStatus(e.getClass().getSimpleName());
            } catch (TryLaterException e) {
                LOG.warn("TryLaterException:" + e.getMessage());
                status = 450;
                result = new SimpleMsg("错误", "您尝试的次数太多了，请休息一下~~~", 0);
                t1.setStatus(e.getClass().getSimpleName());
            } catch(IssueCouponException e){
                if(e.getCode() == 3){
                    status = 403;
                    result = "用户未登录";
                }else{
                    status = 400;
                    result = MSG_BIZ_ERROR;
                }
                t1.setStatus(e);
                cat.logError(e);
            }catch (Exception e) {
				result = MSG_BIZ_ERROR;
				status = 400;
				t1.setStatus(e);
				cat.logError(e);
			} finally {
				t1.complete();
			}
		} catch (ControllerNotFoundException e) {
			result = MSG_404;
			status = 404;
		} catch (Exception e) {
			result = MSG_FRAMEWORK_ERROR;
			status = 400;
			cat.logError(e);
		} finally {
			cat.logEvent("JsonStatusCode", Integer.toString(status), Event.SUCCESS, null);

			// response transaction
			Transaction t2 = cat.newTransaction("Response", "JSON");
			try {
				responseJson(status, result, response, controller);
				t2.setStatus(Transaction.SUCCESS);
			} catch (Exception e) {
				t2.setStatus(e);
				cat.logError(e);
				// TODO: default response
			} finally {
				t2.complete();
			}
		}
	}
	
	private Controller parseController(HttpServletRequest request)
			throws ControllerNotFoundException {
		String uri = request.getRequestURI();
		String contextPath = request.getContextPath();
		Controller controller = null;
		if (!StringUtils.endsWith(uri, ".jsonp") || uri.length() <= contextPath.length() + 1) {
			throw new ControllerNotFoundException("invalid request uri: " + uri);
		}
        if (StringUtils.isBlank(request.getParameter("callback"))) {
            throw new ControllerNotFoundException("missing callback param");
        }

        String controllerKey = ServletUtils.lastPathOfUrl(uri.substring(contextPath.length() + 1).toLowerCase());
		int p = controllerKey.lastIndexOf('/');
		if (p > 0) {
			controllerKey = controllerKey.substring(p + 1);
		}
		String controllerBean = null;
		if (controllerKey.endsWith(".jsonp")) {
			controllerBean = controllerKey.substring(0, controllerKey.length() - 6) + ".pay";
		} else {
			controllerBean = controllerKey;
		}
		
		try {
			controller = (Controller) context.getBean(controllerBean);
		} catch (NoSuchBeanDefinitionException e) {
			throw new ControllerNotFoundException("controller not found: " + controllerKey);
		}
		
		controller.setContext(context);
		controller.setControllerKey(controllerKey);
		return controller;
	}

	private void setMasterSlave(HttpServletRequest request, long userId) {
		TrackerContext trackerContext = new TrackerContext();
		if (userId > 0) {
			Cat.getProducer().logEvent("User", "LogIn", Event.SUCCESS, "");
			trackerContext.setAuthenticated(true); // 登录用户 读主库
		} else {
			Cat.getProducer().logEvent("User", "NotLogIn", Event.SUCCESS, "");
			trackerContext.setAuthenticated(false); // 非登入用户 读从库
		}
		trackerContext.setExtension(new HashMap<String, Object>());
		ExecutionContextHolder.setTrackerContext(trackerContext);
	}
	
	private ParameterMap parseParameterMap(HttpServletRequest request, Controller controller) {
		ParameterMap parametersMap = new ParameterMap();
		parseParam(request, parametersMap);
		controller.setParametersMap(parametersMap);
		return parametersMap;
	}
	
	private String parseUserAgent(HttpServletRequest request, Controller controller) {
		String userAgent = request.getHeader("User-Agent");
		String pragma = request.getHeader("pragma");
		String pragmaOS = request.getHeader("pragma-os");
		
		if (StringUtils.containsIgnoreCase(pragmaOS, "mapi")) {
			userAgent = pragmaOS;
		} else if (StringUtils.containsIgnoreCase(pragma, "mapi")) {
			userAgent = pragma;
		}
		if (userAgent == null) {
			userAgent = StringUtils.EMPTY;
		}
		controller.setUseragent(userAgent);
		return userAgent;
	}
	
	private String parseUserIp(HttpServletRequest request, Controller controller) {
		String ip = RemoteIpGetter.getFirstGlobalAddr(request);
		controller.setUserIP(ip);
		return ip;
	}
	
	private ParameterMap parseParam(HttpServletRequest request, ParameterMap parametersMap)
	{
		@SuppressWarnings("unchecked")
		Enumeration<String> params = request.getParameterNames();
		while (params.hasMoreElements()) {
			String key = params.nextElement();
			parametersMap.put(key.toLowerCase(), request.getParameter(key));
		}
		return parametersMap;
	}

	private static final byte[] ZERO_BYTE = new byte[0];
	private void responseJson(int status, Object result, HttpServletResponse response, Controller controller) throws IOException {
		JsonEnvelope envelope = new JsonEnvelope(status, result);
		ClientInfo clientInfo = new ClientInfo(controller);
        String callback = "callback";
        if(controller!=null){
            callback = controller.getJsonpCallback();
        }
		byte[] byteResult = ZERO_BYTE;
        /*
		try {
			ByteArrayOutputStream baos = new ByteArrayOutputStream();
			DPJsonSerializer dp = new DPJsonSerializer(baos, clientInfo);
			dp.serialize(envelope);
			dp.close();
			byteResult = renderJsonpResult(baos.toByteArray(), callback);
			baos.close();
		} catch (Exception e) {
			envelope.setCode(400);
			envelope.setData(MSG_JSON_ERROR);
			LOG.error("error serializing as json", e);
			ByteArrayOutputStream baos = new ByteArrayOutputStream();
			DPJsonSerializer dp = new DPJsonSerializer(baos, clientInfo);
			dp.serialize(envelope);
			dp.close();
            byteResult = renderJsonpResult(baos.toByteArray(), callback);
			baos.close();
//			byteResult = mapper.writeValueAsString(new JsonEnvelope(400, MSG_JSON_ERROR)).getBytes(ResponseContent.ENCODING);
		}
		*/
		ByteArrayOutputStream baos = null;
		DPJsonSerializer dp = null;
		try {
			baos = new ByteArrayOutputStream();
			dp = new DPJsonSerializer(baos, clientInfo);
			dp.serialize(envelope);
			dp.flush();
			byteResult = renderJsonpResult(baos.toByteArray(), callback);
		} catch (Exception e) {
			envelope.setCode(400);
			envelope.setData(MSG_JSON_ERROR);
			LOG.error("error serializing as json", e);
			baos = new ByteArrayOutputStream();
			dp = new DPJsonSerializer(baos, clientInfo);
			dp.serialize(envelope);
			dp.flush();
			byteResult = renderJsonpResult(baos.toByteArray(), callback);
//			byteResult = mapper.writeValueAsString(new JsonEnvelope(400, MSG_JSON_ERROR)).getBytes(ResponseContent.ENCODING);
		}finally {
			if(dp!=null){
				dp.close();
			}
			if (baos != null) {
				baos.close();
			}
		}
		if (status == 404) {
			response.setStatus(404);
		} else {
			response.setStatus(200);
		}
		response.setCharacterEncoding(ResponseContent.ENCODING);
		response.setContentType(ResponseContent.JSONCONTENTTYPE);
		response.setContentLength(byteResult.length);
		response.getOutputStream().write(byteResult);
	}

    private byte[] renderJsonpResult(byte[] jsonResult, String callback) throws IOException{
        ByteArrayOutputStream outputStream = new ByteArrayOutputStream( );
        outputStream.write((callback + "(").getBytes());
        outputStream.write(jsonResult);
        outputStream.write(")".getBytes());
        return outputStream.toByteArray();
    }
}
 