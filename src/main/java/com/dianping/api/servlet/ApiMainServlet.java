package com.dianping.api.servlet;

import com.dianping.api.domain.ClientInfo;
import com.dianping.api.domain.Enum.AppType;
import com.dianping.api.domain.Enum.ClientType;
import com.dianping.api.domain.SimpleMsg;
import com.dianping.api.exception.IllegalApiParamException;
import com.dianping.api.exception.MobileNotBoundException;
import com.dianping.api.exception.TryLaterException;
import com.dianping.api.framework.ApiException;
import com.dianping.api.framework.ApiResponse;
import com.dianping.api.framework.Controller;
import com.dianping.api.netio.DPBinaryEncrypt;
import com.dianping.api.netio.Formatter;
import com.dianping.api.netio.ParameterMap;
import com.dianping.api.netio.ResponseContent;
import com.dianping.api.servlet.exception.ControllerNotFoundException;
import com.dianping.api.servlet.exception.UnknownClientException;
import com.dianping.api.util.ApiUtil;
import com.dianping.api.util.DotUtils;
import com.dianping.api.util.UserUtils;
import com.dianping.avatar.log.AvatarLogger;
import com.dianping.avatar.log.AvatarLoggerFactory;
import com.dianping.avatar.tracker.ExecutionContextHolder;
import com.dianping.avatar.tracker.TrackerContext;
import com.dianping.cat.Cat;
import com.dianping.cat.CatConstants;
import com.dianping.cat.message.Event;
import com.dianping.cat.message.Message;
import com.dianping.cat.message.MessageProducer;
import com.dianping.cat.message.Transaction;
import com.dianping.ops.remote.RemoteIpGetter;
import com.dianping.pay.api.exception.IssueCouponException;
import com.dianping.pay.api.util.ValidateUtils;
import org.apache.commons.fileupload.FileItem;
import org.apache.commons.fileupload.disk.DiskFileItemFactory;
import org.apache.commons.fileupload.servlet.FileCleanerCleanup;
import org.apache.commons.fileupload.servlet.ServletFileUpload;
import org.apache.commons.io.FileCleaningTracker;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.context.WebApplicationContext;
import org.springframework.web.context.support.WebApplicationContextUtils;

import javax.servlet.ServletContext;
import javax.servlet.ServletException;
import javax.servlet.http.HttpServlet;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.*;
import java.net.URLDecoder;
import java.util.*;

public class ApiMainServlet extends HttpServlet {

	private static final long serialVersionUID = 1L;

	private static final SimpleMsg NULL_MSG  = new SimpleMsg("提醒", "服务器暂不可用，请稍后再试!");
	private static final SimpleMsg ERROR_MSG = new SimpleMsg("提示", "服务器暂不可用，请稍后再试~");
	private static final SimpleMsg AUTH_MSG = new SimpleMsg("账号失效", "请注销后重新登录");
	private static final SimpleMsg AUTH_MSG_400 = new SimpleMsg("账号失效", "请注销后重新登录");

	private static final ResponseContent NULL_MSG_RESPONSE = Formatter.responseFormatter(NULL_MSG, 418, DPBinaryEncrypt.NOPADING,null, null);
	private static final ResponseContent ERROR_MSG_RESPONSE = Formatter.responseFormatter(ERROR_MSG, 400, DPBinaryEncrypt.NOPADING, null, null);
	private static final ResponseContent AUTH_MSG_RESPONSE = Formatter.responseFormatter(AUTH_MSG, 401, DPBinaryEncrypt.NOPADING, null, null);

	private static final ResponseContent NULL_MSG_RESPONSE_WINPHONE = Formatter.responseFormatter(NULL_MSG, 418, DPBinaryEncrypt.PKCS5,null,null);
	private static final ResponseContent ERROR_MSG_RESPONSE_WINPHONE = Formatter.responseFormatter(ERROR_MSG, 400, DPBinaryEncrypt.PKCS5,null,null);
	private static final ResponseContent AUTH_MSG_RESPONSE_WINPHONE = Formatter.responseFormatter(AUTH_MSG, 401, DPBinaryEncrypt.PKCS5,null,null);

	private AvatarLogger LOG = AvatarLoggerFactory.getLogger(ApiMainServlet.class);

	private WebApplicationContext context;

	public void init() {
		LOG.info("main servlet is loading");
		context = WebApplicationContextUtils.getRequiredWebApplicationContext(getServletContext());
	}

	@Override
	protected void doGet(HttpServletRequest request,
			HttpServletResponse response) throws ServletException, IOException {
        Cat.logEvent("INVALID_METHOD_5", "com.dianping.api.servlet.ApiMainServlet.doGet(javax.servlet.http.HttpServletRequest,javax.servlet.http.HttpServletResponse)");
        handleRequest(request, response, false);
	}

	/**
	 * iPhone:　application/x-www-form-urlencoded　密文
	 * android:　null 密文
	 * nokia v3:　application/x-www-form-urlencoded  明文
	 * nokia v5:
	 * kjava/blackberry/bada:
	 *
	 * dotnet 当 HTTP 请求 Content-Type 值为“application/x-www-form-urlencoded”或“multipart/form-data”时，会填充 Form 属性。
	 */
	@Override
	protected void doPost(HttpServletRequest request,
			HttpServletResponse response) throws ServletException, IOException {
		handleRequest(request, response, true);
	}

	protected void handleRequest(HttpServletRequest request,
			HttpServletResponse response, boolean isPost)
			throws ServletException, IOException {

		MessageProducer cat = Cat.getProducer();
		if (request != null) {
			DotUtils.addRefererDot(request);
		}
		long start = System.currentTimeMillis();
		//其他参数
		String controllerKey=null;
		Controller controller = null;
		//正常会返回200和400，异常的时候返回401; 200表示业务逻辑成功，400表示业务逻辑失败，401表示认证失败
        int status = 200;
        ApiResponse apiResponse = new ApiResponse();
        apiResponse.setStatus(200);
		try{
			request.setCharacterEncoding ("UTF-8");

			controller = parseController(request);// -----------parse controller
            ValidateUtils.notNull(controller, new ControllerNotFoundException("controller not found"));
			controllerKey = controller.getControllerKey();
			parseRequestIP(request, controller);	//--------parse request ip
			parseUserAgent(request, controller);//------------parse useragent
			parseClientType(request, controller);//-------parse clienttype,version,source
			parseParameterMap(request, controller, isPost);	//----------parse get&post parameters

			String deviceId = request.getHeader("pragma-device");
			controller.setDeviceId(deviceId);	 //---parse device id
            controller.setDpId(request.getHeader("pragma-dpid"));
            controller.setMtId(request.getHeader("pragma-uuid"));
            controller.setCx(controller.getParametersMap().getString("cx"));
			cat.logEvent("Mobile", "Device", Message.SUCCESS, deviceId);

            long userId = UserUtils.parseAuthInfo(request, controller, false);//------parse userid
            setMasterSlave(request, userId);//-------slave master setup based on userid
            LOG.info("threadId="+Thread.currentThread().getId()+" input:"+controller.toString());
            //valdiate client info
            if(controller.getClient() == null || controller.getClient() == ClientType.UnKnow || controller.getVersion() == null){
                throw new UnknownClientException();
            }
            //开始执行方法
            Transaction t1 = cat.newTransaction(CatConstants.TYPE_ACTION, controller.getControllerKey());
            try {
                ValidateUtils.isTrue(userId>=0,new TokenNotValidException());
                apiResponse.setResult(controller.doRequest());
                t1.setStatus(Transaction.SUCCESS);
            } catch (MobileNotBoundException e){
                LOG.warn("MobileNotBoundException");
                apiResponse.setResult(new SimpleMsg("错误", "请绑定手机"));
            } catch (TokenNotValidException e) {
                LOG.warn("TokenNotValidException:"+e.getMessage());
                apiResponse.setStatus(401);
                apiResponse.setResult(AUTH_MSG_400);
                t1.setStatus(e.getClass().getSimpleName());
            } catch (IllegalApiParamException e){
                LOG.warn("IllegalApiParamException:" + e.getMessage());
                apiResponse.setStatus(450);
                apiResponse.setResult(new SimpleMsg(e.getTitle(),e.getMessage(),e.getFlag()));
                t1.setStatus(e.getClass().getSimpleName());
            } catch (TryLaterException e){
                LOG.warn("TryLaterException:" + e.getMessage());
                apiResponse.setStatus(450);
                apiResponse.setResult(new SimpleMsg("错误","您尝试的次数太多了，请休息一下~~~",0));
                t1.setStatus(e.getClass().getSimpleName());
            } catch (IllegalArgumentException e){
                LOG.warn("IllegalArgumentException:" + e.getMessage());
                apiResponse.setStatus(450);
                apiResponse.setResult(new SimpleMsg("错误", e.getMessage()));
                t1.setStatus(e.getClass().getSimpleName());
            } catch (IssueCouponException e){
                LOG.warn("IssueCouponException:" + e.getMessage());
                apiResponse.setStatus(450);
                apiResponse.setResult(new SimpleMsg("错误", e.getMessage()));
                t1.setStatus(e.getClass().getSimpleName());
            } catch (Exception e) {
                LOG.error("request error:",e);
                cat.logError(e);
                apiResponse.setStatus(400);
                apiResponse.setResult(ERROR_MSG);
                t1.setStatus(e.getClass().getSimpleName());
            } finally {
                t1.complete();
            }

            if (apiResponse.getResult() == null) {
                apiResponse.setStatus(400);
                apiResponse.setResult(NULL_MSG);
            }

            //返回结果组装
			response.setCharacterEncoding(ResponseContent.ENCODING);
			Transaction t2;
			if(controller.isNeedFormat()){
				t2 = cat.newTransaction("Response", "Format");
			}else{
				t2 = cat.newTransaction("Response", "Plain");
			}
			t2.setStatus(Transaction.SUCCESS);
			try {
				if (controller.isNeedFormat()) {
					status = responseFormat(response, apiResponse, controller);
				} else {
					status = responsePlain(response, apiResponse, controller);
					Cat.getProducer().logEvent("Response", "Status", Message.SUCCESS, "200");
				}
			} catch (Exception e) {
				cat.logError(e);
				Cat.getProducer().logEvent("Response", "Status", Message.SUCCESS, "400");
				t2.setStatus(e);
				throw e;
			} finally {
				t2.complete();
			}

		} catch (UnknownClientException e){
            status = 400;
            Cat.getProducer().logEvent("UnknownClientException", e.getClass().getSimpleName(), Event.SUCCESS, null);
            request.setAttribute("cat-state", e.getClass().getName());
            LOG.warn("UnknownClientException controller =" + controllerKey);
            buildErrorMsg(response, controller);
        } catch (Exception e) {
            status = 400;
			Cat.getProducer().logEvent("ServerExceptions", e.getClass().getSimpleName(), Event.SUCCESS, null);
			request.setAttribute("cat-state", e.getClass().getName());
			Cat.getProducer().logError(e);
			LOG.error("controller =" + controllerKey, e);
            buildErrorMsg(response, controller);
		} finally {
			long time = System.currentTimeMillis() - start;
			LOG.info("threadId="+Thread.currentThread().getId()+" elapse="+time+" resstatus:"+status);
			Cat.getProducer().logEvent("StatusCode", Integer.toString(status), Event.SUCCESS, null);
		}
	}

    private void buildErrorMsg(HttpServletResponse response, Controller controller) throws IOException {
        Cat.logEvent("INVALID_METHOD_4", "com.dianping.api.servlet.ApiMainServlet.buildErrorMsg(javax.servlet.http.HttpServletResponse,com.dianping.api.framework.Controller)");
        response.setCharacterEncoding(ResponseContent.ENCODING);
        if(controller != null) {
            if(controller.getClient().value == ClientType.WinPhone.value || controller.getClient().value == ClientType.Win8Pad.value) {
                response.setContentType(ERROR_MSG_RESPONSE_WINPHONE.getContentType());
            } else {
                response.setContentType(ERROR_MSG_RESPONSE.getContentType());
            }
            response.setStatus(400);
            response.setContentLength(ERROR_MSG_RESPONSE.getContent().length);
            response.getOutputStream().write(ERROR_MSG_RESPONSE.getContent());
        }
    }


    private Controller parseController(HttpServletRequest request) {
		String uri = request.getRequestURI();
		String contextPath = request.getContextPath();
		Controller controller = null;
		if (uri.length() <= contextPath.length() + 1) {
			LOG.error("request URI is wrong:" + uri);
			return null;
		}

        String controllerKey = ServletUtils.lastPathOfUrl(uri.substring(contextPath.length() + 1).toLowerCase());
        int p = controllerKey.lastIndexOf('/');
        if (p > 0) {
            controllerKey = controllerKey.substring(p + 1);
        }
		try {
			controller = (Controller) context.getBean(controllerKey);
		} catch (Exception e) {
			LOG.error("can't find controller:" + controllerKey, e);
			return null;
		}
		if (controller != null) {// spring will make this true
			controller.setContext(context);
			controller.setControllerKey(controllerKey);
		}
		return controller;
	}

	private void setMasterSlave(HttpServletRequest request, long userId) {
		TrackerContext trackerContext = new TrackerContext();
		if (userId > 0) {
			Cat.getProducer().logEvent("User", "LogIn", Event.SUCCESS, "");
			trackerContext.setAuthenticated(true); // 登录用户 读主库
		} else {
			Cat.getProducer().logEvent("User", "NotLogIn", Event.SUCCESS, "");
			trackerContext.setAuthenticated(false); // 非登入用户 读从库
		}
		trackerContext.setExtension(new HashMap<String, Object>());
		ExecutionContextHolder.setTrackerContext(trackerContext);
	}


	/**
	 *
	 * @param request
	 * @param controller
	 * @return 0: not logged in; -1: auth failed; UserID: auth succeed
	 */


	private ParameterMap parseParameterMap(HttpServletRequest request, Controller controller, boolean isPost) throws IOException{
		ParameterMap parametersMap = new ParameterMap();

		InputStream inputStream = null;
		if(isPost){
			if(ServletFileUpload.isMultipartContent(request)) {//file upload
				controller.setFiles(parseMultiPart(request, parametersMap));
			}

			inputStream = controller.getRequestInputStream();
			if(inputStream == null) {
				inputStream = request.getInputStream();
			}

			try {
				parsePost(inputStream, parametersMap, controller.getClient(),false);
				parseParam(request, parametersMap);
			} catch (ApiException e) {
				this.LOG.error("parsing post error, controller="+controller.getControllerKey()+ ", ua="+controller.getUseragent() ,e);
			}
		}else{
			parseParam(request, parametersMap);
		}
		controller.setParametersMap(parametersMap);
		return parametersMap;
	}

	private ClientType parseClientType(HttpServletRequest request, Controller controller){
		String useragent = controller.getUseragent();
		String lowerCaseUseragent = useragent.toLowerCase();
//		String controllerKey = controller.getControllerKey();
		ClientType client = ApiUtil.parseVersionSource(lowerCaseUseragent,controller);

		if (client == ClientType.UnKnow) {
			LOG.info("client unknow :" + lowerCaseUseragent);
			controller.setClient(ClientType.UnKnow);
			controller.setAppType(AppType.Unknown);
		} else if (client == ClientType.TG_Android) {
			controller.setClient(ClientType.Android);
			controller.setAppType(AppType.TG);
		} else if (client == ClientType.TG_iPhone) {
			controller.setClient(ClientType.iPhone);
			controller.setAppType(AppType.TG);
		} else {
            String appType = request.getHeader("pragma-apptype");
            if(StringUtils.isNotEmpty(appType)) {
                client = ApiUtil.parseNewTuanApp(appType,client);
            }
			controller.setClient(client);
			controller.setAppType(AppType.Main);
		}
		return client;
	}

	private String parseUserAgent(HttpServletRequest request, Controller controller){
		String useragent = request.getHeader("User-Agent");
		String pragma = request.getHeader("pragma");
		String pragmaOS = request.getHeader("pragma-os");

		if(pragmaOS!=null && pragmaOS.toLowerCase().contains("mapi")) {
			useragent = pragmaOS;
		}
		else if(pragma!=null && pragma.toLowerCase().contains("mapi")) {
			useragent = pragma;
		}

		if(useragent == null) {
			useragent = "";
		}
		controller.setOriginalUA(useragent);
		controller.setUseragent(useragent);
		return useragent;

	}

	private String parseRequestIP( HttpServletRequest request , Controller controller){
		String ip = RemoteIpGetter.getFirstGlobalAddr(request);
		controller.setUserIP(ip);
		return ip;
	}

	private Map<String, FileItem> parseMultiPart(HttpServletRequest req, ParameterMap parametersMap)
	{
        Cat.logEvent("INVALID_METHOD_3", "com.dianping.api.servlet.ApiMainServlet.parseMultiPart(javax.servlet.http.HttpServletRequest,com.dianping.api.netio.ParameterMap)");
        HashMap<String, FileItem> files = new HashMap<String , FileItem>();
		try {
			// Create a new file upload handler
			ServletFileUpload upload =  newServletFileUpload(this.getServletContext());
			// Parse the request
			@SuppressWarnings("rawtypes")
			List  items = upload.parseRequest(req);
			// Process the uploaded items
			@SuppressWarnings("rawtypes")
			Iterator iter = items.iterator();
			int i = 0;
			while (iter.hasNext()) {
			    FileItem item = (FileItem) iter.next();
				String name = item.getFieldName();

			    if (item.isFormField()) {// Process a regular form field
			    	String value = item.getString("utf-8");
			    	parametersMap.put(name.toLowerCase(), value);
			    } else {//processUploadedFile(item);now we only support one files
			    	if(!Controller.isRequestData(name)){
			    		name=Controller.KEY_UPLOAD_FILE_PREFIX+ i;
			    		i++;
			    	}
			    	files.put(name, item);
			    }
			}
			} catch (Exception e) {
				LOG.error("",e);
			}
	 return files;

	}

	private ParameterMap parseParam(HttpServletRequest req, ParameterMap parametersMap)
	{
		// 获取get参数,明文的post参数(nokia v3)
		@SuppressWarnings("unchecked")
		Enumeration<String> params = req.getParameterNames();
		while (params.hasMoreElements()) {
			String key = params.nextElement();
			parametersMap.put(key.toLowerCase(), req.getParameter(key));
		}
		return parametersMap;
	}

	/**
	 *
	 * @param inputstream
	 * @param parametersMap
	 * @param client
	 * @return
	 * @throws ApiException
	 */
	private ParameterMap parsePost(InputStream inputstream, ParameterMap parametersMap, ClientType client,boolean isPlain) throws ApiException{
		byte[] bytes = parseRequestBytes(inputstream);
		if (bytes != null && bytes.length > 0) {
			byte[] post = null;

			if(isPlain){
				post = bytes;
			}else{
				try{
					if(client.value == ClientType.WinPhone.value || client.value == ClientType.Win8Pad.value) {
						post = DPBinaryEncrypt.decrypt(bytes, DPBinaryEncrypt.PKCS5);
					} else {
						post = DPBinaryEncrypt.decrypt(bytes, DPBinaryEncrypt.NOPADING); //bytes from client have not beem compressed
					}
				}catch(Exception e){
					post = bytes;//if decrypt error, the data is not encrypted; nokia v3
					throw new ApiException("decrypt error!", e);
				}
			}

			if (post != null && post.length > 0) {
				String data;
				try {
					data = new String(post, ResponseContent.ENCODING);
					String[] keyValue = data.split("&");
					for (int i = 0; i < keyValue.length; i++) {
						String[] kv = keyValue[i].split("=");
						if (kv.length == 2){
							parametersMap.put(URLDecoder.decode(kv[0], ResponseContent.ENCODING).toLowerCase(),
											  URLDecoder.decode(kv[1], ResponseContent.ENCODING));
						}
					}
				} catch (UnsupportedEncodingException e) {
					LOG.error("parse request parameter error: unsupported encoding", e);
				} catch (Exception e) {
					LOG.error("parse request parameter error", e);
				}
			}
		}

		return parametersMap;
	}


	/**
	 * get request bytes array
	 * @return
	 */
	private byte[] parseRequestBytes(InputStream inputStream) {
		ByteArrayOutputStream baos = new ByteArrayOutputStream();
		try {
			if (inputStream != null) {
				byte[] buffer = new byte[1024];//need to check the common size of the request
				int bytesRead = -1;

				while ((bytesRead = inputStream.read(buffer)) > 0) {
					baos.write(buffer,0,bytesRead);
				}
			}
		} catch (IOException e) {
			LOG.error("parse request bytes error!",e);
		}
		LOG.debug("Request size = " + baos.size());
		return baos.toByteArray();
	}

	private int responsePlain(HttpServletResponse response, Object result, Controller controller) throws Exception{
        Cat.logEvent("INVALID_METHOD_2", "com.dianping.api.servlet.ApiMainServlet.responsePlain(javax.servlet.http.HttpServletResponse,java.lang.Object,com.dianping.api.framework.Controller)");
        return responsePlain(response, new ApiResponse(result, 200), controller);
	}
    private int responsePlain(HttpServletResponse response, ApiResponse result, Controller controller) throws Exception{
        Cat.logEvent("INVALID_METHOD_2", "com.dianping.api.servlet.ApiMainServlet.responsePlain(javax.servlet.http.HttpServletResponse,com.dianping.api.framework.ApiResponse,com.dianping.api.framework.Controller)");
        //string or byte[]
		byte[]byteResult=new byte[0];
		if(result.getResult() instanceof String){
			byteResult = ((String) result.getResult()).getBytes(ResponseContent.ENCODING);
		}else if(result.getResult() instanceof byte[]){
			byteResult = (byte[])result.getResult();
		}
		response.setStatus(result.getStatus());
		response.setContentLength(byteResult.length);
		if(controller.getControllerKey().equalsIgnoreCase("web.map")){
			response.setHeader("Cache-Control", "must-revalidate, no-cache, private");
			response.setContentType("text/html; charset=utf-8");
		}else{
			response.setContentType(ResponseContent.BINARYCONTENTTYPE);
		}
		response.getOutputStream().write(byteResult);
		return result.getStatus();
	}
    private int responseFormat(HttpServletResponse response, Object result, Controller controller) throws Exception{
        Cat.logEvent("INVALID_METHOD_2", "com.dianping.api.servlet.ApiMainServlet.responseFormat(javax.servlet.http.HttpServletResponse,java.lang.Object,com.dianping.api.framework.Controller)");
        return responseFormat(response,new ApiResponse(result,0),controller);
    }
	private int responseFormat(HttpServletResponse response, ApiResponse result, Controller controller) throws Exception{
		// 将结果编码写入response
		ResponseContent responseContent;
		if(result.getResult() == NULL_MSG) {
			if(controller.getClient().value == ClientType.WinPhone.value || controller.getClient().value == ClientType.Win8Pad.value) {
				responseContent = NULL_MSG_RESPONSE_WINPHONE;
			} else {
				responseContent = NULL_MSG_RESPONSE;
			}
		} else if(result.getResult() == ERROR_MSG) {
			if(controller.getClient().value == ClientType.WinPhone.value || controller.getClient().value == ClientType.Win8Pad.value) {
				responseContent = ERROR_MSG_RESPONSE_WINPHONE;
			} else {
				responseContent = ERROR_MSG_RESPONSE;
			}
		} else if(result.getResult() == AUTH_MSG) {
			if(controller.getClient().value == ClientType.WinPhone.value || controller.getClient().value == ClientType.Win8Pad.value) {
				responseContent = AUTH_MSG_RESPONSE_WINPHONE;
			} else {
				responseContent = AUTH_MSG_RESPONSE;
			}
		} else {
			if(controller.getClient().value == ClientType.WinPhone.value || controller.getClient().value == ClientType.Win8Pad.value) {
				responseContent = Formatter.responseFormatter(result.getResult(), result.getStatus(),DPBinaryEncrypt.PKCS5,new ClientInfo(controller),controller.getVersion());
			} else {
				responseContent = Formatter.responseFormatter(result.getResult(), result.getStatus(), DPBinaryEncrypt.NOPADING,new ClientInfo(controller),controller.getVersion());
			}
		}

		int statusCode = 0;
		if (responseContent != null) {
			response.setContentType(responseContent.getContentType());
			statusCode = responseContent.getStatusCode();
			response.setStatus(statusCode);// can be 200, 400, 401
			Cat.getProducer().logEvent("Response", "Status", Message.SUCCESS, Integer.toString(statusCode));
			response.setContentLength(responseContent.getContent().length);
			response.getOutputStream().write(responseContent.getContent());
		}
		return statusCode;
	}

	/**
	 * TODO: can  DiskFileItemFactory factory  be singleton???
	 */
	public static ServletFileUpload newServletFileUpload(ServletContext context) {
        Cat.logEvent("INVALID_METHOD_3", "com.dianping.api.servlet.ApiMainServlet.newServletFileUpload(javax.servlet.ServletContext)");
        // Create a factory for disk-based file items
		DiskFileItemFactory factory = new DiskFileItemFactory();
		// Set factory constraints
		factory.setSizeThreshold(5*1024*1024);//yourMaxMemorySize  TODO remove hardcode
		factory.setRepository(new File(context.getRealPath("/upload_temp"))); //yourTempDirectory
		// Create a factory for disk-based file items
		FileCleaningTracker fileCleaningTracker = FileCleanerCleanup.getFileCleaningTracker(context);
		factory.setFileCleaningTracker(fileCleaningTracker);


		ServletFileUpload upload = new ServletFileUpload(factory);
		// Set overall request size constraint
		upload.setSizeMax(5*1024*1024);//yourMaxRequestSize  TODO remove hardcode

		return upload;
	}

}
 