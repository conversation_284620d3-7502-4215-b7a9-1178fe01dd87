package com.dianping.api.servlet.interceptor;

import com.dianping.api.constans.HeaderConstants;
import com.dianping.api.util.StringUtil;
import com.dianping.mobile.framework.annotation.Interceptor;
import com.dianping.mobile.framework.datatypes.IMobileResponse;
import com.dianping.mobile.framework.datatypes.MobileContext;
import com.dianping.mobile.framework.servlet.interceptor.base.UserInterceptor;

import javax.servlet.http.HttpServletRequest;


@Interceptor
public class MiniProgramHeaderInterceptor implements UserInterceptor {
    @Override
    public boolean support(String actionKey, HttpServletRequest request) {
        return true;
    }

    @Override
    public boolean preHandle(MobileContext context) throws Exception {
        HttpServletRequest request = context.getRequest();
        if (StringUtil.isNullOrEmpty(context.getVersion()) && !StringUtil.isNullOrEmpty(request.getHeader(HeaderConstants.CLIENT_VERSION))) {
            context.setVersion(request.getHeader(HeaderConstants.CLIENT_VERSION));
        }
        return true;
    }

    @Override
    public void postHandle(MobileContext context, IMobileResponse response) throws Exception {

    }
}
