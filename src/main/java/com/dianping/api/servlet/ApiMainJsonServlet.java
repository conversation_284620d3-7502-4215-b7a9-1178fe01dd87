package com.dianping.api.servlet;

import com.dianping.api.domain.ClientInfo;
import com.dianping.api.domain.Enum.AppType;
import com.dianping.api.domain.Enum.ClientType;
import com.dianping.api.domain.SimpleMsg;
import com.dianping.api.domain.SuccessMsg;
import com.dianping.api.domain.Version;
import com.dianping.api.exception.IllegalApiParamException;
import com.dianping.api.exception.TryLaterException;
import com.dianping.api.framework.Controller;
import com.dianping.api.netio.DPJsonSerializer;
import com.dianping.api.netio.ParameterMap;
import com.dianping.api.netio.ResponseContent;
import com.dianping.api.servlet.exception.ControllerNotFoundException;
import com.dianping.api.util.DotUtils;
import com.dianping.api.util.UserUtils;
import com.dianping.avatar.log.AvatarLogger;
import com.dianping.avatar.log.AvatarLoggerFactory;
import com.dianping.avatar.tracker.ExecutionContextHolder;
import com.dianping.avatar.tracker.TrackerContext;
import com.dianping.cat.Cat;
import com.dianping.cat.message.Event;
import com.dianping.cat.message.MessageProducer;
import com.dianping.cat.message.Transaction;
import com.dianping.ops.remote.RemoteIpGetter;
import com.dianping.pay.common.enums.PayPlatform;
import org.apache.commons.lang.math.NumberUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.NoSuchBeanDefinitionException;
import org.springframework.web.context.WebApplicationContext;
import org.springframework.web.context.support.WebApplicationContextUtils;

import javax.servlet.ServletException;
import javax.servlet.http.HttpServlet;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.util.Enumeration;
import java.util.HashMap;

public class ApiMainJsonServlet extends HttpServlet {

	private static final long serialVersionUID = 8266084862532226791L;
	private AvatarLogger LOG = AvatarLoggerFactory.getLogger("JSONAPI");

	private static final SimpleMsg MSG_BIZ_ERROR = new SimpleMsg("提示", "服务器暂不可用，请稍候再试~");
	private static final SimpleMsg MSG_FRAMEWORK_ERROR = new SimpleMsg("提示", "服务器暂不可用，请稍候再试!");
	private static final SimpleMsg MSG_JSON_ERROR = new SimpleMsg("提示", "服务器暂不可用，请稍候再试。");
	
	private static final SimpleMsg MSG_401 = new SimpleMsg("提示", "验证失败");
	private static final SimpleMsg MSG_404 = new SimpleMsg("错误", "您请求的地址不存在");
    private static final String WEIXIN_UTM_COOKIE_NAME = "DP_WEIXIN_UTM";

	private WebApplicationContext context;
	
	public void init() {
		LOG.info("main json servlet initialized");
		context = WebApplicationContextUtils.getRequiredWebApplicationContext(getServletContext());
	}
	
	@Override
	protected void doGet(HttpServletRequest request, HttpServletResponse response)
			throws ServletException, IOException {
		handleRequest(request, response, false);
	}
	
	@Override
	protected void doPost(HttpServletRequest request, HttpServletResponse response)
			throws ServletException, IOException {
        Cat.logEvent("INVALID_METHOD_4", "com.dianping.api.servlet.ApiMainJsonServlet.doPost(javax.servlet.http.HttpServletRequest,javax.servlet.http.HttpServletResponse)");
        handleRequest(request, response, true);
	}
	
	protected void handleRequest(HttpServletRequest request, HttpServletResponse response,
			boolean isPost) throws ServletException, IOException {
		MessageProducer cat = Cat.getProducer();

		if (request != null) {
			DotUtils.addRefererDot(request);
		}

		String controllerKey = null;
		Controller controller = null;

		int status = -1;
		Object result = null;
		try {
			request.setCharacterEncoding("UTF-8");

			controller = parseController(request);
			controllerKey = controller.getControllerKey();

			parseUserIp(request, controller);
			parseUserAgent(request, controller);
			int platform = NumberUtils.toInt(request.getParameter("platform"));
			if (platform == PayPlatform.mt_iphone_native.getCode()) {
				controller.setClient(ClientType.MT_iPhone);
			} else if (platform == PayPlatform.mt_android_native.getCode()) {
				controller.setClient(ClientType.MT_Android);
			} else if(platform == PayPlatform.mt_weixin_api.getCode()){
				controller.setClient(ClientType.MT_Weixin_Api);
			}else{
				controller.setClient(ClientType.iPhone);
			}
            controller.setAppType(AppType.TG);
            controller.setVersion(Version.M2_VERSION);
            controller.setJsonClient(true);
            controller.setSource("appstore");
            controller.setOsVersion("7.0");
            parseParameterMap(request, controller);

            controller.setDpId(controller.getParametersMap().getString("dpid"));
            controller.setCx(controller.getParametersMap().getString("cx"));

            long userId = UserUtils.parseAuthInfo(request, controller, true);
            controller.setWeixinUtm(this.getUtm(request));
			setMasterSlave(request, userId);

			// request transaction
			Transaction t1 = cat.newTransaction("JSON Action", controllerKey);
			try {
				result = controller.doRequest();
				if (result == null) {
					status = 400;
					result = MSG_BIZ_ERROR;
				} else if (result instanceof SimpleMsg && !(result instanceof SuccessMsg)) {
					status = 400;
				} else {
					status = 200;
				}
				t1.setStatus(Transaction.SUCCESS);
			} catch (TokenNotValidException e) {
				result = MSG_401;
				status = 401;
				t1.setStatus(e);
			} catch (IllegalApiParamException e){
                LOG.warn("IllegalApiParamException:" + e.getMessage());
                status = 450;
                result = new SimpleMsg(e.getTitle(),e.getMessage(),e.getFlag());
                t1.setStatus(e.getClass().getSimpleName());
            } catch (TryLaterException e){
                LOG.warn("TryLaterException:" + e.getMessage());
                status = 450;
                result = new SimpleMsg("错误", "您尝试的次数太多了，请休息一下~~~", 0);
                t1.setStatus(e.getClass().getSimpleName());
            } catch (Exception e) {
				result = MSG_BIZ_ERROR;
				status = 400;
				t1.setStatus(e);
				cat.logError(e);
			} finally {
				t1.complete();
			}
		} catch (ControllerNotFoundException e) {
			result = MSG_404;
			status = 404;
		} catch (Exception e) {
			result = MSG_FRAMEWORK_ERROR;
			status = 400;
			cat.logError(e);
		} finally {
			cat.logEvent("JsonStatusCode", Integer.toString(status), Event.SUCCESS, null);

			// response transaction
			Transaction t2 = cat.newTransaction("Response", "JSON");
			try {
				responseJson(status, result, response, controller);
				t2.setStatus(Transaction.SUCCESS);
			} catch (Exception e) {
				t2.setStatus(e);
				cat.logError(e);
				// TODO: default response
			} finally {
				t2.complete();
			}
		}
	}
	
	private Controller parseController(HttpServletRequest request)
			throws ControllerNotFoundException {
		String uri = request.getRequestURI();
		String contextPath = request.getContextPath();
		Controller controller = null;
		if (!StringUtils.endsWith(uri, ".json") || uri.length() <= contextPath.length() + 1) {
			throw new ControllerNotFoundException("invalid request uri: " + uri);
		}

        String controllerKey = ServletUtils.lastPathOfUrl(uri.substring(contextPath.length() + 1).toLowerCase());
		int p = controllerKey.lastIndexOf('/');
		if (p > 0) {
			controllerKey = controllerKey.substring(p + 1);
		}
		String controllerBean = null;
		if (controllerKey.endsWith(".json")) {
			controllerBean = controllerKey.substring(0, controllerKey.length() - 5) + ".pay";
		} else {
			controllerBean = controllerKey;
		}
		
		try {
			controller = (Controller) context.getBean(controllerBean);
		} catch (NoSuchBeanDefinitionException e) {
			throw new ControllerNotFoundException("controller not found: " + controllerKey);
		}
		
		controller.setContext(context);
		controller.setControllerKey(controllerKey);
		return controller;
	}

	private void setMasterSlave(HttpServletRequest request, long userId) {
        Cat.logEvent("INVALID_METHOD_5", "com.dianping.api.servlet.ApiMainJsonServlet.setMasterSlave(javax.servlet.http.HttpServletRequest,long)");
        TrackerContext trackerContext = new TrackerContext();
		if (userId > 0) {
			Cat.getProducer().logEvent("User", "LogIn", Event.SUCCESS, "");
			trackerContext.setAuthenticated(true); // 登录用户 读主库
		} else {
			Cat.getProducer().logEvent("User", "NotLogIn", Event.SUCCESS, "");
			trackerContext.setAuthenticated(false); // 非登入用户 读从库
		}
		trackerContext.setExtension(new HashMap<String, Object>());
		ExecutionContextHolder.setTrackerContext(trackerContext);
	}
	
	private ParameterMap parseParameterMap(HttpServletRequest request, Controller controller) {
        Cat.logEvent("INVALID_METHOD_3", "com.dianping.api.servlet.ApiMainJsonServlet.parseParameterMap(javax.servlet.http.HttpServletRequest,com.dianping.api.framework.Controller)");
        ParameterMap parametersMap = new ParameterMap();
		parseParam(request, parametersMap);
		controller.setParametersMap(parametersMap);
		return parametersMap;
	}
	
	private String parseUserAgent(HttpServletRequest request, Controller controller) {
        Cat.logEvent("INVALID_METHOD_4", "com.dianping.api.servlet.ApiMainJsonServlet.parseUserAgent(javax.servlet.http.HttpServletRequest,com.dianping.api.framework.Controller)");
        String userAgent = request.getHeader("User-Agent");
		String pragma = request.getHeader("pragma");
		String pragmaOS = request.getHeader("pragma-os");
		
		if (StringUtils.containsIgnoreCase(pragmaOS, "mapi")) {
			userAgent = pragmaOS;
		} else if (StringUtils.containsIgnoreCase(pragma, "mapi")) {
			userAgent = pragma;
		}
		if (userAgent == null) {
			userAgent = StringUtils.EMPTY;
		}
		controller.setUseragent(userAgent);
		return userAgent;
	}
	
	private String parseUserIp(HttpServletRequest request, Controller controller) {
        Cat.logEvent("INVALID_METHOD_1", "com.dianping.api.servlet.ApiMainJsonServlet.parseUserIp(javax.servlet.http.HttpServletRequest,com.dianping.api.framework.Controller)");
        String ip = RemoteIpGetter.getFirstGlobalAddr(request);
		controller.setUserIP(ip);
		return ip;
	}
	
	private ParameterMap parseParam(HttpServletRequest request, ParameterMap parametersMap)
	{
        Cat.logEvent("INVALID_METHOD_4", "com.dianping.api.servlet.ApiMainJsonServlet.parseParam(javax.servlet.http.HttpServletRequest,com.dianping.api.netio.ParameterMap)");
        @SuppressWarnings("unchecked")
		Enumeration<String> params = request.getParameterNames();
		while (params.hasMoreElements()) {
			String key = params.nextElement();
			parametersMap.put(key.toLowerCase(), request.getParameter(key));
		}
		return parametersMap;
	}

	private static final byte[] ZERO_BYTE = new byte[0];
	private void responseJson(int status, Object result, HttpServletResponse response, Controller controller) throws IOException {
		JsonEnvelope envelope = new JsonEnvelope(status, result);
		ClientInfo clientInfo = new ClientInfo(controller);
		byte[] byteResult = ZERO_BYTE;
		/*
		try {
			ByteArrayOutputStream baos = new ByteArrayOutputStream();
			DPJsonSerializer dp = new DPJsonSerializer(baos, clientInfo);
			dp.serialize(envelope);
			dp.close();
			byteResult = baos.toByteArray();
			baos.close();
		} catch (Exception e) {
			envelope.setCode(400);
			envelope.setData(MSG_JSON_ERROR);
			LOG.error("error serializing as json", e);
			ByteArrayOutputStream baos = new ByteArrayOutputStream();
			DPJsonSerializer dp = new DPJsonSerializer(baos, clientInfo);
			dp.serialize(envelope);
			dp.close();
			byteResult = baos.toByteArray();
			baos.close();
//			byteResult = mapper.writeValueAsString(new JsonEnvelope(400, MSG_JSON_ERROR)).getBytes(ResponseContent.ENCODING);
		}
		*/
		ByteArrayOutputStream baos = null;
		DPJsonSerializer dp = null;
		try {
			baos = new ByteArrayOutputStream();
			dp = new DPJsonSerializer(baos, clientInfo);
			dp.serialize(envelope);
			dp.flush();
			byteResult = baos.toByteArray();
		} catch (Exception e) {
			envelope.setCode(400);
			envelope.setData(MSG_JSON_ERROR);
			LOG.error("error serializing as json", e);
			baos = new ByteArrayOutputStream();
			dp = new DPJsonSerializer(baos, clientInfo);
			dp.serialize(envelope);
			dp.flush();
			byteResult = baos.toByteArray();
//			byteResult = mapper.writeValueAsString(new JsonEnvelope(400, MSG_JSON_ERROR)).getBytes(ResponseContent.ENCODING);
		}finally {
			if(dp!=null){
				dp.close();
			}
			if (baos != null) {
				baos.close();
			}
		}
		if (status == 404) {
			response.setStatus(404);
		} else {
			response.setStatus(200);
		}
		response.setCharacterEncoding(ResponseContent.ENCODING);
		response.setContentType(ResponseContent.JSONCONTENTTYPE);
		response.setContentLength(byteResult.length);
		response.getOutputStream().write(byteResult);
	}
    public static String getUtm(HttpServletRequest request) {
        String utm = (String) request.getAttribute(WEIXIN_UTM_COOKIE_NAME);
        return StringUtils.isEmpty(utm) ? null : utm;
    }
}
 