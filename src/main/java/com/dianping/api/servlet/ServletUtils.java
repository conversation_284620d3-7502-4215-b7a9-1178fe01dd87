
package com.dianping.api.servlet;

import com.dianping.cat.Cat;
import org.apache.commons.lang3.StringUtils;

public class ServletUtils {

    private static final String URL_PATH_SEP = "/";

	public static String formatUtm(String utm) {
        Cat.logEvent("INVALID_METHOD_5", "com.dianping.api.servlet.ServletUtils.formatUtm(java.lang.String)");
        if (utm != null) {
			utm = utm.replaceAll("[^A-Za-z0-9]+", StringUtils.EMPTY);
			if (utm.length() > 15) {
				utm = utm.substring(0, 15);
			}
		}
		if (StringUtils.isEmpty(utm)) {
			return "NOUTM";
		} else {
			return utm;
		}
	}
	
	public static String formatUserAgent(String userAgent) {
        Cat.logEvent("INVALID_METHOD_1", "com.dianping.api.servlet.ServletUtils.formatUserAgent(java.lang.String)");
        return userAgent == null ? StringUtils.EMPTY : userAgent.toLowerCase();
	}
	
	public static boolean isMobileUser(String userAgent) {
        Cat.logEvent("INVALID_METHOD_2", "com.dianping.api.servlet.ServletUtils.isMobileUser(java.lang.String)");
        if (StringUtils.isNotEmpty(userAgent)) {
			userAgent = userAgent.toLowerCase();
			if (userAgent.indexOf("android") > -1
					|| userAgent.indexOf("sina_robot") > -1
					|| userAgent.indexOf("ipod") > -1
					|| userAgent.indexOf("iphone") > -1
					|| userAgent.indexOf("ipad") > -1
					|| userAgent.indexOf("symbianos") > -1) {
				return true;
			}
		}
		return false;
	}
	
	public static boolean isPCUser(String userAgent) {
        Cat.logEvent("INVALID_METHOD_2", "com.dianping.api.servlet.ServletUtils.isPCUser(java.lang.String)");
        return !isMobileUser(userAgent);
	}
	
	public static boolean isIOSUser(String userAgent) {
        Cat.logEvent("INVALID_METHOD_1", "com.dianping.api.servlet.ServletUtils.isIOSUser(java.lang.String)");
        return userAgent != null && (userAgent.contains("iphone") || userAgent.contains("ipod") || userAgent.contains("ipad"));
	}
	
	public static boolean isAndroidUser(String userAgent) {
        Cat.logEvent("INVALID_METHOD_5", "com.dianping.api.servlet.ServletUtils.isAndroidUser(java.lang.String)");
        return userAgent != null && userAgent.contains("android");
	}

    public static String lastPathOfUrl(String url) {
        if (url == null) {
            return "";
        }
        String[] paths = url.split(URL_PATH_SEP);
        return paths[paths.length - 1];
    }

}
