
package com.dianping.api.servlet;

import com.dianping.api.common.util.KeyValuePair;
import com.dianping.api.domain.ClientInfo;
import com.dianping.api.domain.Version;
import com.dianping.api.netio.DPEncoder;
import com.dianping.api.util.ApiUtil;
import com.google.common.collect.Lists;

import java.io.Serializable;
import java.util.List;

public class JsonEnvelope extends DPEncoder implements Serializable {
	private static final long serialVersionUID = -5440000289372238590L;

	private int code;
	private Object data;

	public static final List<KeyValuePair> LIST = Lists.newArrayList();
	static {
		LIST.add(new KeyValuePair("code", ApiUtil.getHash("Code")));
		LIST.add(new KeyValuePair("data", ApiUtil.getHash("Data")));
	}

	public JsonEnvelope(int code, Object data) {
		this.code = code;
		this.data = data;
	}

	public int getCode() {
		return code;
	}

	public void setCode(int code) {
		this.code = code;
	}

	public Object getData() {
		return data;
	}

	public void setData(Object data) {
		this.data = data;
	}

	@Override
	public int getClassId() {
		return ApiUtil.getHash(JsonEnvelope.class.getSimpleName());
	}

	@Override
	public List<KeyValuePair> getSerializeList(ClientInfo clientInfo, Version version) {
		return LIST;
	}

}
