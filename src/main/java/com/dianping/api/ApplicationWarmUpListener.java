package com.dianping.api;

import com.dianping.api.campaign.biz.CampaignRepository;
import com.dianping.api.campaign.biz.UnifiedCouponGroupRepository;
import com.dianping.gmkt.coupon.common.api.constants.CommonLionConstants;
import com.dianping.lion.client.Lion;
import com.dianping.pay.api.util.JsonUtils;
import com.dianping.pay.api.util.LionConstants;
import com.fasterxml.jackson.core.type.TypeReference;
import com.google.common.collect.Lists;
import com.sankuai.mpmkt.coupon.issue.octo.api.CampaignIssueCouponService;
import com.sankuai.mpmkt.coupon.issue.octo.api.request.CampaignIssueValidateRequest;
import com.sankuai.mpmkt.coupon.issue.octo.api.request.CampaignRecordQueryRequest;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationListener;
import org.springframework.context.event.ContextRefreshedEvent;
import org.springframework.stereotype.Component;

import java.util.Arrays;
import java.util.Map;

/**
 * @Description: 服务启动预热
 */
@Slf4j
@Component
public class ApplicationWarmUpListener implements ApplicationListener<ContextRefreshedEvent> {
    private static Map<String, String> warmUpInfo = Lion.getMap(CommonLionConstants.WARM_UP_INFO);

    private static Integer warmUpCouponGroupId;
    private static final String COUPON_GROUP_ID = "couponGroupId";
    private static Long warmUpUserId;

    private static final String CAMPAIGN_KEY = "campaignKey";

    private static String campaignKey;

    private static final String USER_ID = "userId";

    static {
        warmUpCouponGroupId = NumberUtils.toInt(warmUpInfo.get(COUPON_GROUP_ID), 294127860);
        warmUpUserId = NumberUtils.toLong(warmUpInfo.get(USER_ID), 235949312);
        campaignKey = warmUpInfo.getOrDefault(CAMPAIGN_KEY, "RUmWEn3ue8");


        Lion.addConfigListener(CommonLionConstants.WARM_UP_INFO, configEvent -> {
            log.info("Lion Key: {} changed: {}", CommonLionConstants.WARM_UP_INFO, configEvent.getValue());
            warmUpInfo = JsonUtils.toObject(configEvent.getValue(), new TypeReference<Map<String, String>>() {
            });
            warmUpCouponGroupId = NumberUtils.toInt(warmUpInfo.get(COUPON_GROUP_ID), 294127860);
            warmUpUserId = NumberUtils.toLong(warmUpInfo.get(USER_ID), 235949312);
            campaignKey = warmUpInfo.getOrDefault(CAMPAIGN_KEY, "RUmWEn3ue8");
        });
    }

    @Autowired
    private UnifiedCouponGroupRepository unifiedCouponGroupRepository;
    @Autowired
    private CampaignRepository campaignRepository;

    @Autowired
    private CampaignIssueCouponService campaignIssueCouponService;


    @Override
    public void onApplicationEvent(ContextRefreshedEvent contextRefreshedEvent) {
        try {
            if (contextRefreshedEvent.getApplicationContext().getParent() != null) {
                return;
            }

            warmUpRemoteService();
            warmUpLionConfig();
        } catch (Exception e) {
            log.error("[Service WarmUp Exception]");
        }
    }

    private void warmUpRemoteService() {
        try {
            // 批次接口预热点
            unifiedCouponGroupRepository.batchLoadCouponGroupById(Lists.newArrayList(warmUpCouponGroupId.longValue()));

            campaignRepository.batchLoadCampaign(Arrays.asList(campaignKey));


            CampaignIssueValidateRequest request = new CampaignIssueValidateRequest();
            request.setUserType(1);
            request.setIssueSource("MARKET_MAKER_PLATFORM");
            request.setUserId(warmUpUserId);
            request.setCampaignKeyList(Arrays.asList(campaignKey));
            campaignIssueCouponService.preValidateIssueCoupons(request);


            CampaignRecordQueryRequest queryRequest = new CampaignRecordQueryRequest();
            queryRequest.setExactQuery(true);
            queryRequest.setUserType(1);
            queryRequest.setUserId(warmUpUserId);
            queryRequest.setCampaignKeyList(Arrays.asList(campaignKey));
            campaignIssueCouponService.queryCampaignRecord(queryRequest);
        } catch (Exception e) {
            log.error("[warmUpRemoteService Exception]");
        }
    }

    private void warmUpLionConfig() {
        try {
            Lion.getBooleanValue(LionConstants.TRUST_WEB_USER_TEST_SWITCH, false);
        } catch (Exception e) {
            log.error("[warmUpLionConfig Exception]");
        }
    }

}
