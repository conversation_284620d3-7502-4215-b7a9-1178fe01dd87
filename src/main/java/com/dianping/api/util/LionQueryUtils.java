package com.dianping.api.util;

import com.dianping.api.constans.CommonConstants;
import com.dianping.lion.client.Lion;
import com.dianping.pay.api.biz.activity.newloader.dto.AbTestConfig;
import com.dianping.pay.api.util.LionConstants;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;

import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Created in 2024/6/7 10:37
 */
public class LionQueryUtils {

    public static Map<String, String> CROSS_DOMAIN_HEADERS_MAP = null;
    private static final Map<String, String> DEFAULT_MAP = Maps.newHashMap();
    public static Boolean DOT_POI_PAGE_SWITCH;
    public static Boolean DOT_MINI_PROGRAMS_ISSUE_SWITCH;
    public static boolean POI_ID_CRYPTO_SWITCH;
    public static boolean PREPAY_LQG_RAINBOW_DEGRADE_SWITCH;
    public static boolean PREPAY_POI_COUPON_PRODUCT_TYPE_SWITCH;
    public static boolean PREPAY_GOD_COUPON_BANNER_PUSH_SWITCH;
    public static boolean POI_RENAME_MMC_ICON_SWITCH;
    public static boolean POI_RENAME_FREE_MMC_ICON_SWITCH;
    public static String BUY_MMC_NEW_ICON_PIC_URL;
    public static Integer BUY_MMC_NEW_ICON_PIC_HEIGHT;
    public static Integer BUY_MMC_NEW_ICON_PIC_WIDTH;
    public static String FREE_MMC_NEW_ICON_PIC_URL;
    public static Integer FREE_MMC_NEW_ICON_PIC_HEIGHT;
    public static Integer FREE_MMC_NEW_ICON_PIC_WIDTH;

    public static Boolean BIN_PRODUCT_COUPON_PROMO_ANTI_CRAWLER_SWITCH; //productcouponpromo.bin 未登录直接返空开关  true- 未登录返回空 (反爬虫）
    private static Map<String, List<AbTestConfig>> AB_TEST_CONFIG_MAP;
    static {
        DEFAULT_MAP.put(CommonConstants.CROSS_FILTER_HEADER, "X-PINGOTHER, Origin, X-Requested-With, Content-Type, Accept, Access-Control-Allow-Credentials, Access-Control-Allow-Methods, Access-Control-Allow-Origin, Access-Control-Max-Age,swimlane,token,mtgsig");
        DEFAULT_MAP.put(CommonConstants.CROSS_UTILS_HEADER, "X-Requested-With");

        CROSS_DOMAIN_HEADERS_MAP = Lion.getMap(LionConstants.MERCHANT_CROSS_DOMAIN_ACCOUNT_HEADERS_CONFIG, String.class, DEFAULT_MAP);
        Lion.addConfigListener(LionConstants.MERCHANT_CROSS_DOMAIN_ACCOUNT_HEADERS_CONFIG, configEvent -> {
            CROSS_DOMAIN_HEADERS_MAP = Lion.getMap(LionConstants.MERCHANT_CROSS_DOMAIN_ACCOUNT_HEADERS_CONFIG, String.class, DEFAULT_MAP);
        });

        DOT_POI_PAGE_SWITCH = Lion.getBooleanValue(LionConstants.NEW_POI_PAGE_DOT_SWITCH, false);
        Lion.addConfigListener(LionConstants.NEW_POI_PAGE_DOT_SWITCH, configEvent -> {
            DOT_POI_PAGE_SWITCH = Lion.getBooleanValue(LionConstants.NEW_POI_PAGE_DOT_SWITCH, false);
        });

        DOT_MINI_PROGRAMS_ISSUE_SWITCH = Lion.getBooleanValue(LionConstants.MINI_PROGRAMS_SOURCE_DOT_SWITCH, false);
        Lion.addConfigListener(LionConstants.MINI_PROGRAMS_SOURCE_DOT_SWITCH, configEvent -> {
            DOT_MINI_PROGRAMS_ISSUE_SWITCH = Lion.getBooleanValue(LionConstants.MINI_PROGRAMS_SOURCE_DOT_SWITCH, false);
        });

        POI_ID_CRYPTO_SWITCH = Lion.getBooleanValue(LionConstants.POI_ID_CRYPTO_SWITCH, true);
        Lion.addConfigListener(LionConstants.POI_ID_CRYPTO_SWITCH, configEvent ->
                POI_ID_CRYPTO_SWITCH = Lion.getBooleanValue(LionConstants.POI_ID_CRYPTO_SWITCH, true)
        );

        PREPAY_LQG_RAINBOW_DEGRADE_SWITCH = Lion.getBooleanValue(LionConstants.PREPAY_LQG_RAINBOW_DEGRADE_SWITCH, false);
        Lion.addConfigListener(LionConstants.PREPAY_LQG_RAINBOW_DEGRADE_SWITCH, configEvent ->
                PREPAY_LQG_RAINBOW_DEGRADE_SWITCH = Lion.getBooleanValue(LionConstants.PREPAY_LQG_RAINBOW_DEGRADE_SWITCH, false)
        );

        PREPAY_POI_COUPON_PRODUCT_TYPE_SWITCH = Lion.getBooleanValue(LionConstants.PREPAY_POI_COUPON_PRODUCT_TYPE_SWITCH, true);
        Lion.addConfigListener(LionConstants.PREPAY_POI_COUPON_PRODUCT_TYPE_SWITCH, configEvent ->
                PREPAY_POI_COUPON_PRODUCT_TYPE_SWITCH = Lion.getBooleanValue(LionConstants.PREPAY_POI_COUPON_PRODUCT_TYPE_SWITCH, true)
        );

        PREPAY_GOD_COUPON_BANNER_PUSH_SWITCH = Lion.getBooleanValue(LionConstants.PREPAY_GOD_COUPON_BANNER_PUSH_SWITCH, false);
        Lion.addConfigListener(LionConstants.PREPAY_GOD_COUPON_BANNER_PUSH_SWITCH, configEvent ->
                PREPAY_GOD_COUPON_BANNER_PUSH_SWITCH = Lion.getBooleanValue(LionConstants.PREPAY_GOD_COUPON_BANNER_PUSH_SWITCH, false)
        );

        POI_RENAME_MMC_ICON_SWITCH = Lion.getBooleanValue(LionConstants.POI_RENAME_MMC_ICON_SWITCH, true);
        Lion.addConfigListener(LionConstants.POI_RENAME_MMC_ICON_SWITCH, configEvent ->
                POI_RENAME_MMC_ICON_SWITCH = Lion.getBooleanValue(LionConstants.POI_RENAME_MMC_ICON_SWITCH, true)
        );

        POI_RENAME_FREE_MMC_ICON_SWITCH = Lion.getBooleanValue(LionConstants.POI_RENAME_FREE_MMC_ICON_SWITCH, true);
        Lion.addConfigListener(LionConstants.POI_RENAME_FREE_MMC_ICON_SWITCH, configEvent ->
                POI_RENAME_FREE_MMC_ICON_SWITCH = Lion.getBooleanValue(LionConstants.POI_RENAME_FREE_MMC_ICON_SWITCH, true)
        );

        BUY_MMC_NEW_ICON_PIC_URL = Lion.getStringValue(LionConstants.BUY_MMC_NEW_ICON_PIC_URL, "https://p0.meituan.net/travelcube/44c58cf900cf03d703a8d451ef23393f2986.png");
        Lion.addConfigListener(LionConstants.BUY_MMC_NEW_ICON_PIC_URL, configEvent ->
                BUY_MMC_NEW_ICON_PIC_URL = Lion.getStringValue(LionConstants.BUY_MMC_NEW_ICON_PIC_URL, "https://p0.meituan.net/travelcube/44c58cf900cf03d703a8d451ef23393f2986.png")
        );

        BUY_MMC_NEW_ICON_PIC_HEIGHT = Lion.getIntValue(LionConstants.BUY_MMC_NEW_ICON_PIC_HEIGHT, 32);
        Lion.addConfigListener(LionConstants.BUY_MMC_NEW_ICON_PIC_HEIGHT, configEvent ->
                BUY_MMC_NEW_ICON_PIC_HEIGHT = Lion.getIntValue(LionConstants.BUY_MMC_NEW_ICON_PIC_HEIGHT, 32)
        );

        BUY_MMC_NEW_ICON_PIC_WIDTH = Lion.getIntValue(LionConstants.BUY_MMC_NEW_ICON_PIC_WIDTH, 84);
        Lion.addConfigListener(LionConstants.BUY_MMC_NEW_ICON_PIC_WIDTH, configEvent ->
                BUY_MMC_NEW_ICON_PIC_WIDTH = Lion.getIntValue(LionConstants.BUY_MMC_NEW_ICON_PIC_WIDTH, 84)
        );

        FREE_MMC_NEW_ICON_PIC_URL = Lion.getStringValue(LionConstants.FREE_MMC_NEW_ICON_PIC_URL, "https://p1.meituan.net/travelcube/99ae8cd2cb153c033c7831c6b54059192578.png");
        Lion.addConfigListener(LionConstants.FREE_MMC_NEW_ICON_PIC_URL, configEvent ->
                FREE_MMC_NEW_ICON_PIC_URL = Lion.getStringValue(LionConstants.FREE_MMC_NEW_ICON_PIC_URL, "https://p1.meituan.net/travelcube/99ae8cd2cb153c033c7831c6b54059192578.png")
        );

        FREE_MMC_NEW_ICON_PIC_HEIGHT = Lion.getIntValue(LionConstants.FREE_MMC_NEW_ICON_PIC_HEIGHT, 32);
        Lion.addConfigListener(LionConstants.FREE_MMC_NEW_ICON_PIC_HEIGHT, configEvent ->
                FREE_MMC_NEW_ICON_PIC_HEIGHT = Lion.getIntValue(LionConstants.FREE_MMC_NEW_ICON_PIC_HEIGHT, 32)
        );

        FREE_MMC_NEW_ICON_PIC_WIDTH = Lion.getIntValue(LionConstants.FREE_MMC_NEW_ICON_PIC_WIDTH, 66);
        Lion.addConfigListener(LionConstants.FREE_MMC_NEW_ICON_PIC_WIDTH, configEvent ->
                FREE_MMC_NEW_ICON_PIC_WIDTH = Lion.getIntValue(LionConstants.FREE_MMC_NEW_ICON_PIC_WIDTH, 66)
        );

        BIN_PRODUCT_COUPON_PROMO_ANTI_CRAWLER_SWITCH = Lion.getBooleanValue(LionConstants.PRODUCT_COUPON_PROMO_BIN_ANTI_CRAWLER_SWITCH, false);
        Lion.addConfigListener(LionConstants.PRODUCT_COUPON_PROMO_BIN_ANTI_CRAWLER_SWITCH, configEvent -> {
            BIN_PRODUCT_COUPON_PROMO_ANTI_CRAWLER_SWITCH = Lion.getBooleanValue(LionConstants.PRODUCT_COUPON_PROMO_BIN_ANTI_CRAWLER_SWITCH, false);
        });
        AB_TEST_CONFIG_MAP = initAbTestConfig();
        Lion.addConfigListener(LionConstants.AB_TEST_CONFIG, configEvent -> AB_TEST_CONFIG_MAP = initAbTestConfig());
    }

    private static Map<String, List<AbTestConfig>> initAbTestConfig() {
        List<AbTestConfig> list = Lion.getList(LionConstants.AB_TEST_CONFIG, AbTestConfig.class, Lists.newArrayList());
        return  list.stream().collect(Collectors.groupingBy(AbTestConfig::getAbCode));
    }

    public static List<AbTestConfig> getAbTestConfig(String abCode) {
        return AB_TEST_CONFIG_MAP.get(abCode);
    }
}
