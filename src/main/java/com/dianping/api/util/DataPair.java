package com.dianping.api.util;

import com.dianping.cat.Cat;

import java.io.Serializable;

public class DataPair<FIRST, SECOND> implements Serializable {

	private static final long serialVersionUID = -977631876946123945L;
	
	private final FIRST first;
	private final SECOND second;

	public DataPair(FIRST first, SECOND second) {
		this.first = first;
		this.second = second;
	}

	@Override
	public int hashCode() {
		return 17 * ((getFirst() != null) ? getFirst().hashCode() : 0)
				+ 17 * ((getSecond() != null) ? getSecond().hashCode() : 0);
	}

	@Override
	public boolean equals(Object o) {
		if (!(o instanceof DataPair<?, ?>)) {
			return false;
		}

		DataPair<?, ?> that = (DataPair<?, ?>) o;
		return isEqual(this.getFirst(), that.getFirst()) && isEqual(this.getSecond(), that.getSecond());
	}

	private static boolean isEqual(Object a, Object b) {
        Cat.logEvent("INVALID_METHOD_2", "com.dianping.api.util.DataPair.isEqual(java.lang.Object,java.lang.Object)");
        return a == b || (a != null && a.equals(b));
	}

	@Override
	public String toString() {
		return String.format("{%s, %s}", getFirst(), getSecond());
	}

	public FIRST getFirst() {
        Cat.logEvent("INVALID_METHOD_3", "com.dianping.api.util.DataPair.getFirst()");
        return first;
	}

	public SECOND getSecond() {
        Cat.logEvent("INVALID_METHOD_1", "com.dianping.api.util.DataPair.getSecond()");
        return second;
	}
}