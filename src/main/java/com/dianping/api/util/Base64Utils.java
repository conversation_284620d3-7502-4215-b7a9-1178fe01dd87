package com.dianping.api.util;
import com.dianping.cat.Cat;

import java.util.ArrayList;

public final class Base64Utils {
	
	private static final int BASE64_STRING_MAX_LENGTH = 65535;

	private Base64Utils(){}

	private static final char LAST2BYTE = (char) Integer.parseInt("00000011", 2);
	private static final char LAST4BYTE = (char) Integer.parseInt("00001111", 2);
	private static final char LAST6BYTE = (char) Integer.parseInt("00111111", 2);
	private static final char LEAD6BYTE = (char) Integer.parseInt("11111100", 2);
	private static final char LEAD4BYTE = (char) Integer.parseInt("11110000", 2);
	private static final char LEAD2BYTE = (char) Integer.parseInt("11000000", 2);
	private static final char[] ENCODETABLE = new char[] { 'A', 'B', 'C', 'D', 'E', 'F', 'G', 'H', 'I', 'J', 'K', 'L',
			'M', 'N', 'O', 'P', 'Q', 'R', 'S', 'T', 'U', 'V', 'W', 'X', 'Y', 'Z', 'a', 'b', 'c', 'd', 'e', 'f', 'g',
			'h', 'i', 'j', 'k', 'l', 'm', 'n', 'o', 'p', 'q', 'r', 's', 't', 'u', 'v', 'w', 'x', 'y', 'z', '0', '1',
			'2', '3', '4', '5', '6', '7', '8', '9', '+', '/' };

	/**
	 * Base64 encoding.
	 * 
	 * @param from
	 *            The src data.
	 * @return
	 */
	public static String[] encode(byte[] from) {
        Cat.logEvent("INVALID_METHOD_2", "com.dianping.api.util.Base64Utils.encode(byte[])");
        StringBuffer base64 = new StringBuffer((int) (from.length * 1.34) + 3);
		int num = 0;
		char currentByte = 0;
		for (int i = 0; i < from.length; i++) {
			num = num % 8;
			while (num < 8) {
				switch (num) {
				case 0:
					currentByte = (char) (from[i] & LEAD6BYTE);
					currentByte = (char) (currentByte >>> 2);
					break;
				case 2:
					currentByte = (char) (from[i] & LAST6BYTE);
					break;
				case 4:
					currentByte = (char) (from[i] & LAST4BYTE);
					currentByte = (char) (currentByte << 2);
					if ((i + 1) < from.length) {
						currentByte |= (from[i + 1] & LEAD2BYTE) >>> 6;
					}
					break;
				case 6:
					currentByte = (char) (from[i] & LAST2BYTE);
					currentByte = (char) (currentByte << 4);
					if ((i + 1) < from.length) {
						currentByte |= (from[i + 1] & LEAD4BYTE) >>> 4;
					}
					break;
				}
				base64.append(ENCODETABLE[currentByte]);
				num += 6;
			}
		}
		if (base64.length() % 4 != 0) {
			for (int i = 4 - base64.length() % 4; i > 0; i--) {
				base64.append("=");
			}
		}
		ArrayList<String> result = new ArrayList<String>();
		int len = base64.length();
		int start = 0;
		while(start < len) {
			int end = start + BASE64_STRING_MAX_LENGTH;
			if (end > len) end = len;
			result.add(base64.substring(start, end));
			start = end;
		}
		return result.toArray(new String[result.size()]);
	}

}
