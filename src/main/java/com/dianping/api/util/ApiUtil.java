package com.dianping.api.util;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.net.InetAddress;
import java.net.NetworkInterface;
import java.net.SocketException;
import java.net.UnknownHostException;
import java.util.ArrayList;
import java.util.Enumeration;
import java.util.List;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

import com.dianping.cat.Cat;
import com.dianping.mobile.framework.datatypes.IMobileContext;
import com.dianping.pay.api.enums.PaySource;
import com.dianping.pay.common.enums.PayPlatform;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;


import com.dianping.api.domain.Enum.AppType;
import com.dianping.api.domain.Enum.ClientType;
import com.dianping.api.framework.Controller;
import com.dianping.avatar.log.AvatarLogger;
import com.dianping.avatar.log.AvatarLoggerFactory;

public final class ApiUtil {

	private static AvatarLogger log = AvatarLoggerFactory.getLogger(ApiUtil.class);

	private ApiUtil() {
	}

	/**
	 * parse version and source from userAgent and set the value to controller
	 * 
	 * @param userAgent
	 * @param controller
	 * @return if no regex pattern can match useragent return ClientType.UnKnow
	 *         else return client type
	 */
	public static ClientType parseVersionSource(String userAgent,  Controller controller){
		for(ClientType clientType : ClientType.values()){
			boolean isMatch = parseVersionSource(clientType.pattern, userAgent, controller);
			if(isMatch) {
				return clientType;
			}
		}
		
		return ClientType.UnKnow;	 
	}

    public static ClientType parseNewTuanApp(String appType, ClientType client) {
        Matcher iphoneMatcher = ClientType.TUAN_iPhone.pattern.matcher(appType);
        if(iphoneMatcher.find()) {
            return ClientType.TUAN_iPhone;
        }
        Matcher androidMatcher = ClientType.TUAN_ANDROID.pattern.matcher(appType);
        if(androidMatcher.find()) {
            return ClientType.TUAN_ANDROID;
        }
        return client;
    }

	private static Pattern OS_VERSION = Pattern.compile("(iPhone|iPad|Android|WinPhone)\\s+([0-9\\.]+)", Pattern.CASE_INSENSITIVE);
	
	public static boolean parseVersionSource(Pattern pattern, String userAgent,
			Controller controller)
	{
		if (pattern == null) {
			return false;
		}
		Matcher m = pattern.matcher(userAgent);
		if (m.find() && m.groupCount() >= 2) {
			if (controller != null) {// this judge is to easy test which will pass a null value for controller
				controller.setVersion(m.group(1));
				controller.setSource(m.group(2));
				if (m.groupCount() >= 3 && m.group(3) != null) {
					Matcher m2 = OS_VERSION.matcher(m.group(3));
					if (m2.find() && m2.groupCount() == 2) {
						controller.setOsVersion(m2.group(2));
					}
				}
			}
			return true;
		} else {
			return false;
		}
	}
	
	private static String ip;
	public static String getIp(){
        Cat.logEvent("INVALID_METHOD_2", "com.dianping.api.util.ApiUtil.getIp()");
        if(ip != null) {
			return ip;	
		}
		try {
			ip = "0.0.0.0";
            Enumeration<NetworkInterface> n = NetworkInterface.getNetworkInterfaces();
            for (;n.hasMoreElements();)
            {
                    NetworkInterface e = n.nextElement();
                    Enumeration<InetAddress> a = e.getInetAddresses();
                    for (; a.hasMoreElements();)
                    {
                            InetAddress addr = a.nextElement();
                            if(addr.getHostAddress().equals("127.0.0.1") || addr.getHostAddress().startsWith("192.168"))continue;
                            ip = addr.getHostAddress();
                    }
            }

			log.info("ip = " + ip);
		} catch (SocketException e) {
			 ip = "java api ip error: 0.0.0.0";
		}
		return ip;			
	}
	
	public static String getHostName(){
        Cat.logEvent("INVALID_METHOD_3", "com.dianping.api.util.ApiUtil.getHostName()");
        try {
			return InetAddress.getLocalHost().getHostName();
		} catch (UnknownHostException e) {
			return null;
		}
	}
	
	public static Logger monitorLog = LoggerFactory.getLogger("com.dianping.api.monitor");
	
	
	/**
	 * the value will be used to 
	 * <AUTHOR>
	 *
	 */
	public enum ModuleType {		 
		campaign("campaign"),
		announcement("announcement"),
		badge("badge"),
		grouponAllTodayDeal("grouponAllTodayDeal"),
		grouponTodayDeal("grouponTodayDeal"),
		grouponTodayDealById("grouponTodayDealById"),
		grouponSearchDeal("grouponSearchDeal"),
		orderList("orderList"),
		receiptList("receiptList"),
		config("config"),
		search("search"),		
		picCenter("piccenter"),
		accountCenter("accountcenter"),
		mqConsumer("mqconsumer"),
		
		pushConnection("pushConnection"),
		location("location"),
		auditpolicy("auditpolicy"),
        advertisement("Advertisement");
				 
		public final String value;		 
		ModuleType(String key) {
			this.value = key;			 
		}
	}
	
	/**
	 * for timer task monitor
	 * @param module
	 * @param success
	 * @param msg
	 */
	public static void addTimerTaskLog(ModuleType module, boolean success , String msg){
        Cat.logEvent("INVALID_METHOD_3", "com.dianping.api.util.ApiUtil.addTimerTaskLog(com.dianping.api.util.ApiUtil$ModuleType,boolean,java.lang.String)");
        if(success) {
			ApiUtil.monitorLog.info("monitor: success TimerTask=" + module.value + " " + msg);
		}
		else {
			ApiUtil.monitorLog.error("monitor: error TimerTask=" + module.value + " " + msg);
		}
	}
	
	//*[monitor: search error other message]   
	public static void addErrorLog(ModuleType module, String msg){
        Cat.logEvent("INVALID_METHOD_4", "com.dianping.api.util.ApiUtil.addErrorLog(com.dianping.api.util.ApiUtil$ModuleType,java.lang.String)");
        ApiUtil.monitorLog.error("monitor: "+module.value+" error "   + msg);
	}
	
	public static void addLog(ModuleType module, boolean success , String msg){
        Cat.logEvent("INVALID_METHOD_2", "com.dianping.api.util.ApiUtil.addLog(com.dianping.api.util.ApiUtil$ModuleType,boolean,java.lang.String)");
        if(success) {
			ApiUtil.monitorLog.info("monitor: success module=" + module.value + " " + msg);
		}
		else {
			ApiUtil.monitorLog.error("monitor: error module=" + module.value + " " + msg);
		}
	}
	
	/**
	 * parse file to string array, the whole file will be stored in memory, so it should not be too large
	 * @param fileName
	 * @return
	 * @throws IOException
	 */
	public static List<String> parseFile(String fileName) throws IOException{
        Cat.logEvent("INVALID_METHOD_1", "com.dianping.api.util.ApiUtil.parseFile(java.lang.String)");
        ArrayList<String> result = new ArrayList<String>();
		/*
		InputStream is = ApiUtil.class.getResourceAsStream(fileName);		 
		BufferedReader bufferedReader = new BufferedReader(new InputStreamReader(is));	     
		String s;
		while ((s = bufferedReader.readLine()) != null)
		{
			result.add(s);		 
		}
		bufferedReader.close();
		return result;
		*/
		try(InputStream is = ApiUtil.class.getResourceAsStream(fileName);
			  BufferedReader bufferedReader = new BufferedReader(new InputStreamReader(is))){
			String s;
			while ((s = bufferedReader.readLine()) != null)
			{
				result.add(s);
			}
		}
		return result;
	}
	
	// 2012.09.03 security
	public static String phoneMask(String phoneNo) {
        Cat.logEvent("INVALID_METHOD_5", "com.dianping.api.util.ApiUtil.phoneMask(java.lang.String)");
        if (phoneNo == null) {
			return null;
		}
		int len = phoneNo.length();
		if (len > 4 && len <= 7) {
			phoneNo = phoneNo.substring(0, len - 4) + "****";
		} else if (len > 7) {
			phoneNo = phoneNo.substring(0, 3) + "****" + phoneNo.substring(7, len);
		}
		return phoneNo;
	}
	
	public static void main(String args[]){
        String[] names = {
            "actionList", "AlertMsg", "name", "content", "actionList", "DiscountNum", "Amount", "isOneKeyEnabled", "isLocked"
        };
        for (String name : names) {
            if (!name.isEmpty() && name.charAt(0) >= 'a' && name.charAt(0) <= 'z') {
                name = Character.toUpperCase(name.charAt(0)) + name.substring(1);
            }
            System.out.println(name + "\t\t0x" + intToHex(getHash(name)));
        }
	}
		
	public static int getHash(String str){
		if(StringUtil.isNullOrEmpty(str)) return -1;
		int i = str.hashCode();
		return (0xFFFF & i) ^ (i >>> 16);
	}

    public static int paymentPlatform(ClientType type, AppType app, PaySource paySource){
        if (paySource == PaySource.WEIXIN_PUB) {
            return PayPlatform.weixin_pub.getCode();
        }else if(paySource == PaySource.WEIXIN_BANK){
            return PayPlatform.weixin_bank.getCode();
        }else if(paySource == PaySource.WEIXIN_LOCAL){
            return PayPlatform.weixin_local.getCode();
        }else if(paySource == PaySource.MOBILEQQ_ANDROID){
            return PayPlatform.mobileQ_android_m.getCode();
        }else if(paySource == PaySource.MOBILEQQ_IOS){
            return PayPlatform.mobileQ_iphone_m.getCode();
        }else if(type == ClientType.iPhone && app == AppType.Main){
            return ********;
        }else if(type == ClientType.Android && app == AppType.Main){
            return ********;
        }else if(type == ClientType.iPhone && app == AppType.TG){
            return ********;
        }else if(type == ClientType.Android && app == AppType.TG){
            return ********;
        }else if(type == ClientType.TUAN_iPhone && app == AppType.Main){
            return ********;
        }else if(type == ClientType.TUAN_ANDROID && app == AppType.Main){
            return ********;
        }else if(type == ClientType.iPadHd){
            return 30020400;
        }else if(type == ClientType.WinPhone){
            return 20020600;
        }else {
            return -1;
        }
    }
    public static int paymentPlatform(ClientType type, AppType app){
        return paymentPlatform(type,app,PaySource.DEFAULT);
    }
    public static PaySource getPaySourceByChannel(String channel, Controller controller){
        Cat.logEvent("INVALID_METHOD_3", "com.dianping.api.util.ApiUtil.getPaySourceByChannel(java.lang.String,com.dianping.api.framework.Controller)");
        if("weixin".equals(channel)){
            String wxDetail = controller.getParametersMap().getString("utm");
            if(StringUtils.isBlank(wxDetail)){
                wxDetail = controller.getWeixinUtm();
            }

            if (StringUtils.equals(wxDetail, "dptuan")) {
                return PaySource.WEIXIN_PUB;
            } else if (StringUtils.equals(wxDetail, "card")) {
                return PaySource.WEIXIN_BANK;
            } else if (StringUtils.equals(wxDetail, "wxshare")) {
                return PaySource.WEIXIN_LOCAL;
            } else if (StringUtils.equals(wxDetail, "nearby")) {
                return PaySource.WEIXIN_LOCAL;
            } else if (StringUtils.equals(wxDetail, "appshare")) {
                return PaySource.WEIXIN_LOCAL;
            }
            return PaySource.WEIXIN_PUB;
        }else if("mobileqq_android".equals(channel)){
            return PaySource.MOBILEQQ_ANDROID;
        }else if("mobileqq_ios".equals(channel)){
            return PaySource.MOBILEQQ_IOS;
        }else{
            return PaySource.DEFAULT;
        }
    }

	
	public static char[] HEX_TABLE = {
        '0' , '1' , '2' , '3' ,
        '4' , '5' , '6' , '7' ,
        '8' , '9' , 'a' , 'b' ,
        'c' , 'd' , 'e' , 'f' ,
	};

	private static String intToHex( int num ){
        Cat.logEvent("INVALID_METHOD_1", "com.dianping.api.util.ApiUtil.intToHex(int)");
        String sResult = "";
        while( num > 0 ){
            sResult = HEX_TABLE[ num % 16 ] + sResult;
            num = num / 16;
        }
        return sResult;
	}
	
	public static String fixPToken(String pToken) {
        Cat.logEvent("INVALID_METHOD_5", "com.dianping.api.util.ApiUtil.fixPToken(java.lang.String)");
        if (pToken != null) {
	        pToken = pToken.replace(' ', '0');
	    }
	    return pToken;
	}
	
	public static String chopTitle(String text, String title) {
        Cat.logEvent("INVALID_METHOD_4", "com.dianping.api.util.ApiUtil.chopTitle(java.lang.String,java.lang.String)");
        if (text == null) {
			return null;
		}
		text = StringUtils.strip(text);
		text = StringUtils.removeStart(text, title);
		text = StringUtils.removeStart(text, '[' + title + ']');
		text = StringUtils.removeStart(text, '【' + title + '】');
		text = StringUtils.strip(text);
		return text;
	}

	/**
	 * 判断是否为MAPI请求
	 * @param iMobileContext
	 * @return
	 */
	public static boolean isMapiRequest(IMobileContext iMobileContext){
		String userAgent = StringUtils.trimToEmpty(iMobileContext.getUserAgent());
		return StringUtils.startsWithIgnoreCase(userAgent, "mapi");
	}


}