package com.dianping.api.util;

import com.dianping.pay.api.biz.activity.newloader.dto.RestUserInfo;
import com.meituan.mtrace.Tracer;
import com.sankuai.meituan.waimai.thrift.tools.cache.RegionSetConfigCache;
import com.sankuai.meituan.waimai.thrift.tools.enums.SetProxyTypeEnum;
import com.sankuai.meituan.waimai.thrift.tools.utils.MTraceRouterInfoUtil;
import com.sankuai.nibpt.unionlogger.UnionLoggerContext;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.servlet.http.HttpServletRequest;
import java.util.Map;

/**
 * 到家set化region工具类
 *
 * <AUTHOR>
 * @date 2024/05/21
 */
public class TraceUtils {

    private static final Logger log = LoggerFactory.getLogger(TraceUtils.class);

    public static void setUserInfo(HttpServletRequest servletRequest, RestUserInfo restUserInfo) {
        if (servletRequest == null || restUserInfo == null) {
            return;
        }
        // 设置日志白名单信息
        UnionLoggerContext.logUserId(String.valueOf(restUserInfo.getUserId()));

        // 设置regionId信息
        setRegion(servletRequest, restUserInfo);

        //打印日志观察trace中是否放入路由信息
        if (UnionLoggerContext.needLog()) {
            Map<String, String> allContext = Tracer.getAllContext();
            log.info("allContext = " + allContext);
        }
    }

    /**
     * 根据请求中的regionId 设置trace信息
     * https://km.sankuai.com/collabpage/2162052598
     * @param servletRequest http请求
     */
    public static void setRegion(HttpServletRequest servletRequest, RestUserInfo restUserInfo) {
        if (servletRequest == null) {
            return;
        }
        // 解析param中的regionId
        String regionId = StringUtils.trimToEmpty(servletRequest.getParameter("wtt_region_id"));
        if (StringUtils.isNotBlank(regionId)) {
            restUserInfo.setRegionId(regionId);
        }

        //客户端缓存中解析setName
        String setName = RegionSetConfigCache.getCellNameByRegionId(regionId);

        //调用公共方法，把路由信息放置到mtrace中
        MTraceRouterInfoUtil.setTraceRoutingInfo4Api(SetProxyTypeEnum.BY_USER_ID, restUserInfo.getUserId(), regionId, setName);
    }
}
