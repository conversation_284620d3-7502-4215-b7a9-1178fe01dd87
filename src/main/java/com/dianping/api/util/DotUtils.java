package com.dianping.api.util;

import com.dianping.cat.Cat;
import com.dianping.cat.message.Event;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang.Validate;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.servlet.http.HttpServletRequest;
import java.net.MalformedURLException;
import java.net.URL;

/**
 * Created by <PERSON><PERSON> on 2019/4/25.
 */
public class DotUtils {

    private static Logger logger = LoggerFactory.getLogger(DotUtils.class);

    private static String CLIENT_VERSION = "client_version";

    private static String UNKNOWN_VERAION = "unknown_client_version";

    //处理h5请求的referer
    public static void addRefererDot(HttpServletRequest request) {
        String referer = request.getHeader("referer");
        if (StringUtils.isEmpty(referer)) {
            return;
        }
        try {
            URL url = new URL(referer);
            String queryUrl = url.getProtocol() + "://" + url.getHost() + url.getPath();
            Cat.logEvent("h5_request_path", url.getPath());
            Cat.logEvent("h5_request_referer", queryUrl);
        } catch (MalformedURLException e) {
            logger.warn(String.format("invalid url: %s", referer), e);
        } catch (Exception e) {
            logger.warn(String.format("add dot failed: request= %s", request), e);
        }
    }

    public static void addVersionDot(String version, String platform, boolean isDpClient) {
        if (StringUtils.isEmpty(version)) {
            Cat.logEvent(CLIENT_VERSION, UNKNOWN_VERAION);
        }
        if (isDpClient) {
            Cat.logEvent(CLIENT_VERSION, "DP_" + platform + "_" + version);
        } else {
            Cat.logEvent(CLIENT_VERSION, "MT_" + platform + "_" + version);
        }
    }

    public static void addCouponComponentQueryResultEvent(String name, boolean isNotEmpty) {
        Cat.logEvent("CouponComponentResultEvent", name, isNotEmpty ? Event.SUCCESS : "-1", null);
    }
}
