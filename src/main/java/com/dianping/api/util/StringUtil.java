package com.dianping.api.util;

import java.util.ArrayList;
import java.util.List;

import com.dianping.cat.Cat;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;

/**
 * 
 * <AUTHOR>
 * 
 */
public final class StringUtil {
	
	private StringUtil() {
	}

	public static boolean isNullOrEmpty(String s) {
		return s == null || s.isEmpty();
	}

	public static boolean isNullOrTrimEmpty(String s) {
        Cat.logEvent("INVALID_METHOD_5", "com.dianping.api.util.StringUtil.isNullOrTrimEmpty(java.lang.String)");
        return s == null || "".equals(s.trim());
	}

	public static byte[] stringToBytesASCII(String str) {
        Cat.logEvent("INVALID_METHOD_3", "com.dianping.api.util.StringUtil.stringToBytesASCII(java.lang.String)");
        char[] buffer = str.toCharArray();
		byte[] b = new byte[buffer.length];
		for (int i = 0; i < b.length; i++) {
			b[i] = (byte) buffer[i];
		}
		return b;
	}
	
	public static List<Integer> commaSeperatedIntegersToList(String s) {
        Cat.logEvent("INVALID_METHOD_5", "com.dianping.api.util.StringUtil.commaSeperatedIntegersToList(java.lang.String)");
        List<Integer> list = new ArrayList<Integer>();
		for (String idStr : StringUtils.split(s == null ? StringUtils.EMPTY : s, ',')) {
			int id = NumberUtils.toInt(StringUtils.trim(idStr), -1);
			if (id != -1) {
				list.add(id);
			}
		}
		return list;
	}

}
