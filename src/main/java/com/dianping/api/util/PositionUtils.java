package com.dianping.api.util;

import com.alibaba.fastjson.JSON;
import com.dianping.api.constans.LionConstants;
import com.dianping.lion.client.ConfigEvent;
import com.dianping.lion.client.ConfigListener;
import com.dianping.lion.client.Lion;
import com.dianping.mobile.framework.datatypes.IMobileContext;
import com.dianping.pay.api.biz.activity.newloader.dto.PromotionRequestContext;
import com.dianping.pay.api.biz.activity.newloader.dto.RestUserInfo;
import com.dianping.zebra.util.StringUtils;
import com.google.common.collect.Maps;
import com.sankuai.mpmkt.coupon.issue.octo.api.enums.CampaignSceneEnum;
import com.sankuai.mpmkt.coupon.issue.octo.api.request.CampaignIssueRequest;
import com.sankuai.mpmkt.coupon.issue.octo.api.request.CampaignIssueValidateRequest;
import com.sankuai.nib.magic.member.base.enums.NibBizLineEnum;
import com.sankuai.nibpt.transparentvalidator.domain.TransparentValidatorParam;
import com.sankuai.nibpt.transparentvalidator.enums.PlatformEnum;
import com.sankuai.nibpt.transparentvalidator.util.TransparentValidatorUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang.math.NumberUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.HashMap;
import java.util.Map;

public class PositionUtils {

    private static Logger logger = LoggerFactory.getLogger(PositionUtils.class);

    private static String serviceName;
    private static final String ISSUE_COMPONENT_METHOD_NAME = "/pay/promo/issuecouponcomponent.bin";

    private static final String CAMPAIGN_ISSUE_METHOD_NAME = "/pay/promo/campaign/issue";

    private static final String CAMPAIGN_RENDER_METHOD_NAME = "/pay/promo/campaign/render";

    private static final String mtPoiId = "mtPoiId";

    private static final String dpPoiId = "dpPoiId";

    private static final int VIRTUAL_BIZLINE = 9000;
    private static final Long ONE_MILLION = 1000000L;

    static {
        serviceName = Lion.getString(LionConstants.APP_KEY, LionConstants.REPORT_HOST_NAME, "mapi.dianping.com");
        Lion.addConfigListener(LionConstants.APP_KEY, "default",LionConstants.REPORT_HOST_NAME, new ConfigListener() {
            @Override
            public void configChanged(ConfigEvent configEvent) {
                serviceName = Lion.getString(LionConstants.APP_KEY, LionConstants.REPORT_HOST_NAME, "mapi.dianping.com");
            }
        });
    }

    public static void reportValidateParams(PromotionRequestContext promotionRequestContext, IMobileContext context) {
        try {
            if (TransparentValidatorUtils.needReport(promotionRequestContext.getUserId())) {
                TransparentValidatorUtils.reportValidateParams(buildTransparentValidatorParam(promotionRequestContext, context));
            }
        } catch (Exception e) {
            logger.error("reportValidateParams error, promotionRequestContext:{}", JSON.toJSONString(promotionRequestContext), e);
        }
    }

    private static TransparentValidatorParam buildTransparentValidatorParam(PromotionRequestContext promotionRequestContext, IMobileContext context) {
        TransparentValidatorParam param = TransparentValidatorParam.builder()
                .serviceName(serviceName)
                .methodName(ISSUE_COMPONENT_METHOD_NAME)
                .cPosition(String.valueOf(promotionRequestContext.getCpositionOrDefault()))
                .mtRealUserId(promotionRequestContext.getMtUserId())
                .dpRealUserId(promotionRequestContext.getDpUserId())
                .bizLine(NibBizLineEnum.GENERAL.getCode())
                .uuid(promotionRequestContext.getUuid())
                .dpId(promotionRequestContext.getDpid())
                .cType(getCType(promotionRequestContext,context))
                .appVersion(StringUtils.isNotBlank(promotionRequestContext.getVersion()) ? promotionRequestContext.getVersion() : context.getVersion())
                .mtgsig(promotionRequestContext.getMtgsig())
                .mtFingerprint(promotionRequestContext.getMtFingerprint())
                .mtHomeCityId(promotionRequestContext.isMt() ? promotionRequestContext.getCityId() : null)
                .userLocalCityId(promotionRequestContext.isMt() ? promotionRequestContext.getActualCityId() : null)
                .dpUserLocalCityId(promotionRequestContext.isMt() ? null : promotionRequestContext.getActualCityId())
                .dpHomeCityId(promotionRequestContext.isMt() ? null : promotionRequestContext.getCityId())
                .ip(context.getUserIp())
                .platform(getPlatform(promotionRequestContext,context))
                .weAppVersion(promotionRequestContext.getVersion())
                .actualLatitude(promotionRequestContext.getLatitude() != null ? promotionRequestContext.getLatitude().longValue() : null)
                .actualLongitude(promotionRequestContext.getLongitude() != null ? promotionRequestContext.getLongitude().longValue() : null)
                .fingerApplets(promotionRequestContext.getCx())
                .mtPoiId(promotionRequestContext.getMtShopIdL())
                .dpPoiId(promotionRequestContext.getDpShopIdL())
                .appId(promotionRequestContext.getAppId())
                .wxOpenId(promotionRequestContext.isMiniProgram() ? promotionRequestContext.getOpenId() : null)
                .build();
        if (promotionRequestContext.getLatitude() != null) {
            param.setActualLatitude((long) (promotionRequestContext.getLatitude() * ONE_MILLION));
        }
        if (promotionRequestContext.getLongitude() != null) {
            param.setActualLongitude((long) (promotionRequestContext.getLongitude() * ONE_MILLION));
        }
        if (StringUtils.isNotBlank(promotionRequestContext.getRegionId())) {
            param.setRegionId(promotionRequestContext.getRegionId());
        }
        if (!promotionRequestContext.isMt()) {
            param.setCType(promotionRequestContext.getCPlatform() == 1 ? "dp_iphone" : "dp_android");
        }
        Map<String, Object> businessParamMap = new HashMap<>();
        businessParamMap.put(mtPoiId, promotionRequestContext.getMtShopIdL());
        businessParamMap.put(dpPoiId, promotionRequestContext.getDpShopIdL());
        param.setBusinessParamMap(businessParamMap);
        return param;
    }

    private static String getCType(PromotionRequestContext promotionRequestContext, IMobileContext context) {
        if (promotionRequestContext.isMiniProgram()) {
            return "mt_weapp";
        }
        return promotionRequestContext.getCPlatform() == 1 ? "mtiphone" : "mtandroid";
    }

    private static Integer getPlatform(PromotionRequestContext promotionRequestContext, IMobileContext context) {
        if (promotionRequestContext.isMiniProgram()) {
            return PlatformEnum.MEITUAN_APPLET.getCode();
        }
        return promotionRequestContext.isMt() ? PlatformEnum.MEITUAN.getCode() : PlatformEnum.DIANPING.getCode();
    }
    public static void reportValidateParams(CampaignIssueRequest request, RestUserInfo restUserInfo) {
        // 仅神券会场组件需要上报
        if (request == null || request.getCampaignScene() != CampaignSceneEnum.MAGICAL_MEMBER.getCode() || restUserInfo == null) {
            return;
        }
        try {
            if (TransparentValidatorUtils.needReport(restUserInfo.getUserId())) {
                TransparentValidatorUtils.reportValidateParams(buildTransparentValidatorParam(request, restUserInfo));
            }
        } catch (Exception e) {
            logger.error("reportValidateParams error, CampaignIssueRequest:{}", JSON.toJSONString(request), e);
        }
    }

    private static TransparentValidatorParam buildTransparentValidatorParam(CampaignIssueRequest request, RestUserInfo restUserInfo) {
        Map<String, String> mmcParams = request.getMmcParams();

        TransparentValidatorParam param = TransparentValidatorParam.builder()
                .serviceName(serviceName)
                .methodName(CAMPAIGN_ISSUE_METHOD_NAME)
                .mtRealUserId(restUserInfo.getUserId())
                .bizLine(VIRTUAL_BIZLINE)
                .build();
        if (MapUtils.isEmpty(mmcParams)) {
            return param;
        }

        assembleParamFromMmcParams(param, mmcParams, restUserInfo);
        Map<String, Object> businessParamMap = buildBusinessParamMap(mmcParams);
        param.setBusinessParamMap(businessParamMap);
        return param;
    }

    public static void reportValidateParams(CampaignIssueValidateRequest request, RestUserInfo restUserInfo) {
        // 仅神券会场组件需要上报
        if (request == null || request.getCampaignScene() != CampaignSceneEnum.MAGICAL_MEMBER.getCode() || restUserInfo == null) {
            return;
        }
        try {
            if (TransparentValidatorUtils.needReport(restUserInfo.getUserId())) {
                TransparentValidatorUtils.reportValidateParams(buildTransparentValidatorParam(request, restUserInfo));
            }
        } catch (Exception e) {
            logger.error("reportValidateParams error, CampaignIssueRequest:{}", JSON.toJSONString(request), e);
        }
    }

    private static TransparentValidatorParam buildTransparentValidatorParam(CampaignIssueValidateRequest request, RestUserInfo restUserInfo) {
        Map<String, String> mmcParams = request.getMmcParams();

        TransparentValidatorParam param = TransparentValidatorParam.builder()
                .serviceName(serviceName)
                .methodName(CAMPAIGN_RENDER_METHOD_NAME)
                .mtRealUserId(restUserInfo.getUserId())
                .bizLine(VIRTUAL_BIZLINE)
                .build();
        if (MapUtils.isEmpty(mmcParams)) {
            return param;
        }

        assembleParamFromMmcParams(param, mmcParams, restUserInfo);
        Map<String, Object> businessParamMap = buildBusinessParamMap(mmcParams);
        param.setBusinessParamMap(businessParamMap);
        return param;
    }

    private static void assembleParamFromMmcParams(TransparentValidatorParam param, Map<String, String> mmcParams, RestUserInfo restUserInfo) {
        if (MapUtils.isEmpty(mmcParams)) {
            return;
        }
        String inflatePosition = MapUtils.getString(mmcParams, "inflatePosition");
        if (StringUtils.isNotBlank(inflatePosition)) {
            param.setCPosition(inflatePosition);
        }

        String phone = MapUtils.getString(mmcParams, "phone");
        if (StringUtils.isNotBlank(phone)) {
            param.setPhone(phone);
        }

        String uuid = MapUtils.getString(mmcParams, "uuid");
        if (StringUtils.isNotBlank(uuid)) {
            param.setUuid(uuid);
        }

        String cType = MapUtils.getString(mmcParams, "cType");
        if (StringUtils.isNotBlank(cType)) {
            param.setCType(cType);
        }

        String appVersion = MapUtils.getString(mmcParams, "appVersion");
        if (StringUtils.isNotBlank(appVersion)) {
            param.setAppVersion(appVersion);
        }

        String ip = MapUtils.getString(mmcParams, "ip");
        if (StringUtils.isNotBlank(ip)) {
            param.setIp(ip);
        }

        String actualLatitude = MapUtils.getString(mmcParams, "actualLatitude");
        if (StringUtils.isNotBlank(actualLatitude)) {
            param.setActualLatitude(NumberUtils.toLong(actualLatitude));
        }

        String actualLongitude = MapUtils.getString(mmcParams, "actualLongitude");
        if (StringUtils.isNotBlank(actualLongitude)) {
            param.setActualLongitude(NumberUtils.toLong(actualLongitude));
        }

        String mtgsig = MapUtils.getString(mmcParams, "mtgsig");
        if (StringUtils.isNotBlank(mtgsig)) {
            param.setMtgsig(mtgsig);
        }

        String mtFingerPrint = MapUtils.getString(mmcParams, "mtFingerPrint");
        if (StringUtils.isNotBlank(mtFingerPrint)) {
            param.setMtFingerprint(mtFingerPrint);
        }

        String mtHomeCityId = MapUtils.getString(mmcParams, "mtCityId");
        if (StringUtils.isNotBlank(mtHomeCityId)) {
            param.setMtHomeCityId(NumberUtils.toInt(mtHomeCityId));
        }

        if (StringUtils.isNotBlank(restUserInfo.getRegionId())) {
            param.setRegionId(restUserInfo.getRegionId());
        }
    }

    private static Map<String, Object> buildBusinessParamMap(Map<String, String> mmcParams) {
        Map<String, Object> result = Maps.newHashMap();
        if (MapUtils.isEmpty(mmcParams)) {
            return result;
        }

        result.put("bizCategoryType", 5);
        String couponSource = MapUtils.getString(mmcParams, "couponSource");
        if (StringUtils.isNotBlank(couponSource)) {
            result.put("couponSource", couponSource);
        }

        return result;
    }
}
