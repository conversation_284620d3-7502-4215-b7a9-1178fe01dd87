package com.dianping.api.util;


import com.dianping.api.constans.CommonConstants;
import com.dianping.combiz.spring.util.LionConfigUtils;
import org.apache.commons.lang.StringUtils;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;


/**
 * @description:
 * @author: qiunian.sun
 * @date: 2018/05/23
 */
public class CrossDomainUtils {

    public static void setEnableCrossDomain(HttpServletRequest request, HttpServletResponse response) {
        String ajaxDomain = LionConfigUtils.getProperty("mapi-pay-promo-web.ajax.ajaxDomain");
        String origin = request.getHeader("Origin");
        if (StringUtils.isNotBlank(origin) && StringUtils.isNotBlank(ajaxDomain) && ajaxDomain.contains(origin)) {
            response.setHeader("Access-Control-Allow-Origin", origin);
            response.setHeader("Access-Control-Allow-Headers", LionQueryUtils.CROSS_DOMAIN_HEADERS_MAP.get(CommonConstants.CROSS_UTILS_HEADER));
            response.setHeader("Access-Control-Allow-Credentials", "true");
            response.setHeader("Access-Control-Allow-Methods", "GET,POST,OPTIONS,PUT,DELETE,HEAD");
        } else {
            response.setHeader("Access-Control-Allow-Origin", "*");
        }
        response.setContentType("text/html;charset=UTF-8");
    }

}
