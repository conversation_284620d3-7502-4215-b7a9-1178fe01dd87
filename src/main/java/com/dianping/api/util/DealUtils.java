
package com.dianping.api.util;

import com.dianping.api.domain.*;
import com.dianping.api.domain.Enum;
import com.dianping.cat.Cat;
import com.dianping.tuangou.remote.api.deal.dto.DealGroupDTO;
import com.dianping.tuangou.remote.api.deal.dto.NaviTagItemDTO;
import com.dianping.tuangou.remote.api.deal.dto.NaviTagSelectionDTO;
import com.dianping.tuangou.remote.common.constant.TagEnNameConstant;
import com.dianping.tuangou.remote.common.constant.TagItemEnNameConstant;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;

import java.util.List;

public class DealUtils {

	private static final int IDMASK_LO = 0x0000ffff;
	private static final int IDMASK_HI = 0xffff0000;

	/* navigation bar categoryId/regionId combination */
	public static final int NAVI_MAXID = 0x7fff; // sign bit lost in data transferring, don't use
	public static final int NAVI_ROOTID = 0;
	public static final int NAVI_REGIONID_DESTINATION = NAVI_MAXID;
	public static final int NAVI_REGIONID_UNLIMITEDAREA = NAVI_MAXID - 1;
	public static final int NAVI_REGIONID_HOT = NAVI_MAXID - 2;
	public static final int NAVI_CATEGORYID_CARD = NAVI_MAXID;
	
	public static boolean canCombine(int id1, int id2) {
        Cat.logEvent("INVALID_METHOD_1", "com.dianping.api.util.DealUtils.canCombine(int,int)");
        return id1 >= 0 && id1 <= NAVI_MAXID && id2 >= 0 && id2 <= NAVI_MAXID;
	}
	
	public static int combineId(int id1, int id2) {
        Cat.logEvent("INVALID_METHOD_1", "com.dianping.api.util.DealUtils.combineId(int,int)");
		/*
		if (!canCombine(id1, id2)) {
			throw new IllegalArgumentException(String.format("invalid id to combine into one: %s, %s", id1, id2));
		}
		*/
		return (id1 << 16) + id2;
	}
	
	/**
	 * The compactedId is a 32-bit integer which is a combination of two 16-bit integers
	 * 
	 * @param compactedId
	 * @return
	 */
	public static Pair<Integer, Integer> extractId(int compactedId) {
        Cat.logEvent("INVALID_METHOD_5", "com.dianping.api.util.DealUtils.extractId(int)");
        int id1 = (compactedId & IDMASK_HI) >>> 16;
		int id2 = compactedId & IDMASK_LO;
		return Pair.of(id1, id2);
	}
	
	public static String showBytes(int v) {
        Cat.logEvent("INVALID_METHOD_4", "com.dianping.api.util.DealUtils.showBytes(int)");
        StringBuilder s = new StringBuilder();
		for (int i = 31; i >= 0; i--) {
			int mask = 1 << i;
			s.append(((v & mask) == 0 ? '0' : '1'));
		}
		return s.toString();
	}
	
	public static String showLoBytes(int v) {
        Cat.logEvent("INVALID_METHOD_2", "com.dianping.api.util.DealUtils.showLoBytes(int)");
        StringBuilder s = new StringBuilder();
		for (int i = 15; i >= 0; i--) {
			int mask = 1 << i;
			s.append(((v & mask) == 0 ? '0' : '1'));
		}
		return s.toString();
	}
	
    public static boolean isMemberCardDeal(DealGroupDTO dealGroupDto) {
        Cat.logEvent("INVALID_METHOD_5", "com.dianping.api.util.DealUtils.isMemberCardDeal(com.dianping.tuangou.remote.api.deal.dto.DealGroupDTO)");
        List<NaviTagSelectionDTO> tagSelectionDTOs = dealGroupDto.getTags();
        for (NaviTagSelectionDTO tagSelectionDTO : tagSelectionDTOs) {
            if (StringUtils.equals(TagEnNameConstant.CARD, tagSelectionDTO.getTag().getTagEnName())) {
                for (NaviTagItemDTO tagItemDTO : tagSelectionDTO.getTagItems()) {
                    if (StringUtils.equals(TagItemEnNameConstant.RECHARGEABLE_CARD, tagItemDTO.getItemEnName())) {
                        return true;
                    }
                }
            }
        }
        return false;
    }

    public static boolean enableCardDeals(Enum.AppType appType, Version version) {
        Cat.logEvent("INVALID_METHOD_4", "com.dianping.api.util.DealUtils.enableCardDeals(com.dianping.api.domain.Enum$AppType,com.dianping.api.domain.Version)");
        return (appType == Enum.AppType.Main) && (Version.compareTo(version, Version.CARD_VERSION)>= 0);
    }
}
