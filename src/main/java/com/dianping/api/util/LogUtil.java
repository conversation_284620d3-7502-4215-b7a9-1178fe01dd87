package com.dianping.api.util;

import java.util.HashMap;

import com.dianping.api.statlog.entity.Result;
import com.dianping.api.statlog.entity.StatLogConstant;
import com.dianping.cat.Cat;

public final class LogUtil {
	
	private LogUtil() {
	}
	
	public static HashMap<String, String> urlEncodedLineparse(String input, Result isWrong){
        Cat.logEvent("INVALID_METHOD_1", "com.dianping.api.util.LogUtil.urlEncodedLineparse(java.lang.String,com.dianping.api.statlog.entity.Result)");
        isWrong.setValue(1);
		try{
			String[] r = input.split("\\&");
			HashMap<String, String> result = new HashMap<String, String>();
			for(String s:r){
				String[] temp = s.split("\\=");
				if(temp.length>=2){
					try{
						//temps length>2 有错误, 有可能是因为没有encode造成的，取第一个=前的部分作为key，其他的作为value
						String key = s.substring(0, s.indexOf('='));
						String value = s.substring(s.indexOf('=')+1);
						result.put(java.net.URLDecoder.decode(key, "utf-8").toLowerCase().trim(), java.net.URLDecoder.decode(value, "utf-8").toLowerCase().trim());
					} catch(Exception e){
						isWrong.setValue(StatLogConstant.WRONG_LINE_DECODE);
						if(!StringUtil.isNullOrEmpty(temp[0])&&temp[0].length()<20){
							isWrong.setContent(isWrong.getContent() + "iw " + temp[0].replace(',', ' ') + ",");
						}
					}
				} else if(temp.length==1){
					try{
						result.put(java.net.URLDecoder.decode(temp[0], "utf-8").toLowerCase().trim(), null);
					} catch(Exception e){
						isWrong.setValue(StatLogConstant.WRONG_LINE_DECODE);
						if(!StringUtil.isNullOrEmpty(temp[0])&&temp[0].length()<20){
							isWrong.setContent(isWrong.getContent() + "iw " + temp[0].replace(',', ' ') + ",");
						}
					}
				} 
			}
			return result;
		} catch(Exception e){
			isWrong.setValue(StatLogConstant.WRONG_LINE_DECODE);
			isWrong.setContent("all line wrong,");
			return new HashMap<String, String>();
		}
	}
	
	public static int parseInt(String input){
        Cat.logEvent("INVALID_METHOD_4", "com.dianping.api.util.LogUtil.parseInt(java.lang.String)");
        try{
			int result = Integer.parseInt(input);
			return result;
		} catch(Exception e){
			return -2;
		}
	}
	
	public static int clientToTrainid(int client){
        Cat.logEvent("INVALID_METHOD_1", "com.dianping.api.util.LogUtil.clientToTrainid(int)");
        switch(client){
		case 1: return 7;
		default: return -1;
		}
	}
}
