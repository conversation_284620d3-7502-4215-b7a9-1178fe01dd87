package com.dianping.api.util;

import com.dianping.deal.idmapper.api.dto.IdMapper;
import com.google.common.base.Function;

import javax.annotation.Nullable;

/**
 * <AUTHOR>
 * @date 2021/1/22
 */
public class FunctionPool {

    public static final Function<Integer, Number> INTEGER_TO_NUMBER = new Function<Integer, Number>() {
        @Override
        public Number apply(Integer input) {
            return input;
        }
    };

}
