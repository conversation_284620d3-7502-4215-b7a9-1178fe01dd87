package com.dianping.api.cat;

import com.dianping.api.framework.FeatureContingency;
import com.dianping.api.framework.FeatureContingencyConstants;
import com.dianping.cat.Cat;
import com.dianping.cat.message.Transaction;

public class TransactionWrapper {

	private Transaction t;

	public TransactionWrapper(Transaction t) {
		this.t = t;
	}

	public static TransactionWrapper newTransaction(String type, String name) {
		Cat.logEvent("INVALID_METHOD_3", "com.dianping.api.cat.TransactionWrapper.newTransaction(java.lang.String,java.lang.String)");
		boolean enabled = FeatureContingency
				.isOn(FeatureContingencyConstants.FCID_CAT);
		if (enabled) {
			Transaction t = Cat.getProducer().newTransaction(type, name);
			return new TransactionWrapper(t);
		} else {
			return new TransactionWrapper(null);
		}
	}

	public void setStatus(String status) {
		Cat.logEvent("INVALID_METHOD_2", "com.dianping.api.cat.TransactionWrapper.setStatus(java.lang.String)");
		if (t != null) {
			t.setStatus(status);
		}
	}

	public void complete() {
		Cat.logEvent("INVALID_METHOD_1", "com.dianping.api.cat.TransactionWrapper.complete()");
		if (t != null) {
			t.complete();
		}
	}

	public void setStatus(Throwable e) {
		Cat.logEvent("INVALID_METHOD_5", "com.dianping.api.cat.TransactionWrapper.setStatus(java.lang.Throwable)");
		if (t != null) {
			t.setStatus(e);
		}
	}
}
