package com.dianping.api.cat;

import com.dianping.api.framework.FeatureContingency;
import com.dianping.api.framework.FeatureContingencyConstants;
import com.dianping.cat.Cat;
import com.dianping.cat.message.MessageProducer;
import com.dianping.cat.message.Transaction;

public final class CatWrapper {

	private MessageProducer cat;

	private CatWrapper(MessageProducer cat) {
		this.cat = cat;
	}

	public static CatWrapper getCatWrapper() {
        Cat.logEvent("INVALID_METHOD_3", "com.dianping.api.cat.CatWrapper.getCatWrapper()");
        boolean enabled = FeatureContingency
				.isOn(FeatureContingencyConstants.FCID_CAT);
		if (enabled) {
			MessageProducer cat = Cat.getProducer();
			return new CatWrapper(cat);
		} else {
			return new CatWrapper(null);
		}
	}

	public TransactionWrapper newTransaction(String type, String name) {
        Cat.logEvent("INVALID_METHOD_1", "com.dianping.api.cat.CatWrapper.newTransaction(java.lang.String,java.lang.String)");
        if (cat != null) {
			Transaction t = cat.newTransaction(type, name);
			return new TransactionWrapper(t);
		} else {
			return new TransactionWrapper(null);
		}
	}

	public void logEvent(String type, String name, String status,
			String nameValuePairs) {
        Cat.logEvent("INVALID_METHOD_3", "com.dianping.api.cat.CatWrapper.logEvent(java.lang.String,java.lang.String,java.lang.String,java.lang.String)");
        if (cat != null) {
			cat.logEvent(type, name, status, nameValuePairs);
		}
	}

	public void logError(Throwable cause) {
        Cat.logEvent("INVALID_METHOD_1", "com.dianping.api.cat.CatWrapper.logError(java.lang.Throwable)");
        if (cat != null) {
			cat.logError(cause);
		}
	}
}