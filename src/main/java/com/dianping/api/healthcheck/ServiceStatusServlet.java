package com.dianping.api.healthcheck;

import java.io.IOException;
import java.io.PrintWriter;
import java.util.HashMap;
import java.util.Map;

import javax.servlet.ServletException;
import javax.servlet.http.HttpServlet;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import com.dianping.cat.Cat;
import org.apache.commons.lang3.StringEscapeUtils;
import org.apache.commons.lang3.StringUtils;

import com.dianping.api.domain.Result;
import com.dianping.api.healthcheck.dpsfwrap.ServiceInfo;
import com.dianping.api.healthcheck.dpsfwrap.ServiceMonitor;

public class ServiceStatusServlet extends HttpServlet {

	private static final long serialVersionUID = -6861741725676125143L;

	public ServiceStatusServlet() {
		super();
	}

	public void init() {
        Cat.logEvent("INVALID_METHOD_2", "com.dianping.api.healthcheck.ServiceStatusServlet.init()");
    }

	protected void doPost(HttpServletRequest request,
			HttpServletResponse response) throws ServletException, IOException {
        Cat.logEvent("INVALID_METHOD_2", "com.dianping.api.healthcheck.ServiceStatusServlet.doPost(javax.servlet.http.HttpServletRequest,javax.servlet.http.HttpServletResponse)");
        doGet(request, response);
	}

	protected void doGet(HttpServletRequest request,
			HttpServletResponse response) throws ServletException, IOException {
        Cat.logEvent("INVALID_METHOD_2", "com.dianping.api.healthcheck.ServiceStatusServlet.doGet(javax.servlet.http.HttpServletRequest,javax.servlet.http.HttpServletResponse)");
        response.setContentType("text/html");
		response.setCharacterEncoding("UTF-8");
		/*
		PrintWriter out = response.getWriter();
		out.println("<html><head><meta charset=\"UTF-8\"/><title>Am I down?</title></head><body><h1>Am I Down?</h1>");
		
		int ss = 0;
		// service monitors
		out.println("<h3>Service Status</h3>");
		out.println("<table border=\"1\"><tr><td>Service</td><td>Status</td><td>Info</td><td>Owner</td></tr>");
		for (String name : ServiceMonitor.SERVICES.keySet()) {
			ServiceInfo info = ServiceMonitor.SERVICES.get(name);
			synchronized (info) {
				int lastReqs = info.status & 7;
				int status = lastReqs == 7 ? 2 : (lastReqs > 0 ? 1 : 0);
				if (status > ss) {
					ss = status;
				}
				out.println(getServiceRow(name, status, info.status));
			}
		}
//		out.println("<tr><td>...</td><td>...</td><td>...</td><td>...</td></tr></table>");
		out.println("</table>");
		
		out.println("<h1>" + (ss == 0 ? "I'm OK." : (ss == 1 ? "I'm probably down." : "I'm down.")) + "</h1>");
		out.println("</body></html>");
		out.close();
		*/
		try(PrintWriter out = response.getWriter()){
			out.println("<html><head><meta charset=\"UTF-8\"/><title>Am I down?</title></head><body><h1>Am I Down?</h1>");
			int ss = 0;
			// service monitors
			out.println("<h3>Service Status</h3>");
			out.println("<table border=\"1\"><tr><td>Service</td><td>Status</td><td>Info</td><td>Owner</td></tr>");
			for (String name : ServiceMonitor.SERVICES.keySet()) {
				ServiceInfo info = ServiceMonitor.SERVICES.get(name);
				synchronized (info) {
					int lastReqs = info.status & 7;
					int status = lastReqs == 7 ? 2 : (lastReqs > 0 ? 1 : 0);
					if (status > ss) {
						ss = status;
					}
					out.println(getServiceRow(name, status, info.status));
				}
			}
			out.println("</table>");
			out.println("<h1>" + (ss == 0 ? "I'm OK." : (ss == 1 ? "I'm probably down." : "I'm down.")) + "</h1>");
			out.println("</body></html>");
		}
	}

	private static Map<String, String> SERVICE_OWNER = new HashMap<String, String>();
	static {
		SERVICE_OWNER.put("DealGroupRemoteService", "晨清、杜鹏、杨星");
		SERVICE_OWNER.put("DealGroupRemindService", "文伟");
		SERVICE_OWNER.put("ReceiptRemoteService", "晨清、杜鹏、杨星");
		SERVICE_OWNER.put("DeliveryRemoteService", "华慰");
		SERVICE_OWNER.put("AccountService", "一方");
		SERVICE_OWNER.put("PayOrderService", "华慰");
		SERVICE_OWNER.put("PayOrderRemoteService", "华慰");
		SERVICE_OWNER.put("UserAccountService", "高甜甜");
		SERVICE_OWNER.put("UserSMSRemoteService", "晓冬");
		SERVICE_OWNER.put("DealGroupRemindService", "文伟");
		SERVICE_OWNER.put("DealGroupRemindService", "文伟");
	}
	
	private String getServiceRow(String name, int status, int fullstatus) {
        Cat.logEvent("INVALID_METHOD_1", "com.dianping.api.healthcheck.ServiceStatusServlet.getServiceRow(java.lang.String,int,int)");
        return "<tr><td>"
				+ StringEscapeUtils.escapeHtml3(name)
				+ "</td><td>"
//				+ getStatusImgHtml(status)
				+ getFullStatusImgHtml(fullstatus)
				+ "</td><td>"
				+ (status == 0 ? "ok" : (status == 1 ? "error(s) detected" : "down"))
				+ "</td><td>"
				+ (SERVICE_OWNER.containsKey(name) ? SERVICE_OWNER.get(name) : "unknown")
				+ "</td></tr>\n";
	}
	
	private String getFullStatusImgHtml(int status) {
        Cat.logEvent("INVALID_METHOD_1", "com.dianping.api.healthcheck.ServiceStatusServlet.getFullStatusImgHtml(int)");
        StringBuilder sb = new StringBuilder();
		int mask = 1;
		for (int i=0; i<10; i++) {
			if ((status & mask) == 0) {
				sb.append("<img src=\"http://www.websitehealthcheck.com.au/images/fair/tick.png\" />");
			} else {
				sb.append("<img src=\"http://www.websitehealthcheck.com.au/images/fair/cross.png\" />");
			}
			mask = mask << 1;
		}
		return sb.toString();
	}

	abstract class HealthTester {
		private String name;
		private String owner;
		private int status = 0;
		private String info = "ok";

		public HealthTester(String name, String owner) {
			this.name = name;
			this.owner = owner;
		}

		public String getName() {
			return name;
		}

		public void setName(String name) {
			this.name = name;
		}

		public String getOwner() {
			return owner;
		}

		public void setOwner(String owner) {
			this.owner = owner;
		}

		public int getStatus() {
			return status;
		}

		public void setStatus(int status) {
			this.status = status;
		}

		public String getInfo() {
			return info;
		}

		public void setInfo(String info) {
			this.info = info;
		}

		protected abstract Result<Integer, String> doTest() throws Exception;

		public void test() {
			try {
				Result<Integer, String> result = doTest();
				if (result == null) {
					this.status = 2;
					this.info = "null returned";
				} else {
					this.status = result.getFirst();
					this.info = result.getSecond();
				}
			} catch (Exception e) {
//				StackTraceElement[] st = e.getStackTrace();
//				List<String> filteredSt = Lists.newArrayList();
//				filteredSt.add(e.getMessage());
//				for (int i=0; i<st.length; i++) {
//					if (st[i].toString().contains("ServiceStatusServlet")) {
//						break;
//					}
//					filteredSt.add(st[i].toString());
//				}
				this.status = 2;
				this.info = e.getMessage() + '\n' + StringUtils.join(e.getStackTrace(), '\n');
			}
		}
	}

}
