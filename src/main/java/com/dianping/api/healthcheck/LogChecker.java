package com.dianping.api.healthcheck;

import java.util.Date;
import java.util.List;
import java.util.regex.Pattern;

import com.dianping.cat.Cat;
import org.apache.commons.lang3.StringUtils;

public class LogChecker {

	private Pattern pattern;
	private int count;
	private String lastError;
	private Date lastUpdate;
	private String owner;

	public LogChecker(List<String> keywords) {
		this.count = -1;
		this.lastError = "ok";
		this.pattern = Pattern.compile(StringUtils.join(keywords, '|'));
	}
	
	public boolean isMatch(String s) {
        Cat.logEvent("INVALID_METHOD_1", "com.dianping.api.healthcheck.LogChecker.isMatch(java.lang.String)");
        return s == null ? false : pattern.matcher(s).find();
	}
	
	public synchronized void logSuccess() {
        Cat.logEvent("INVALID_METHOD_1", "com.dianping.api.healthcheck.LogChecker.logSuccess()");
        if (count == -1) {
			count = 1;
		} else {
			count++;
		}
		lastUpdate = new Date();
	}
	
	public synchronized void logError(String msg) {
        Cat.logEvent("INVALID_METHOD_2", "com.dianping.api.healthcheck.LogChecker.logError(java.lang.String)");
        this.lastError = msg;
		count = 0;
		lastUpdate = new Date();
	}
	
	public synchronized Pattern getPattern() {
        Cat.logEvent("INVALID_METHOD_5", "com.dianping.api.healthcheck.LogChecker.getPattern()");
        return pattern;
	}

	public synchronized int getCount() {
		return count;
	}

	public synchronized void setCount(int count) {
		this.count = count;
	}

	public synchronized String getLastError() {
		return lastError;
	}

	public synchronized void setLastError(String lastError) {
		this.lastError = lastError;
	}

	public synchronized Date getLastUpdate() {
		return lastUpdate;
	}

	public synchronized void setLastUpdate(Date lastUpdate) {
		this.lastUpdate = lastUpdate;
	}

	public String getOwner() {
		return owner;
	}

	public void setOwner(String owner) {
		this.owner = owner;
	}

}
