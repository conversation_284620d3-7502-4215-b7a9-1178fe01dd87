package com.dianping.api.healthcheck;

import java.util.Arrays;
import java.util.LinkedHashMap;
import java.util.Map;

import com.dianping.cat.Cat;
import org.apache.commons.lang3.StringUtils;
import org.apache.log4j.AppenderSkeleton;
import org.apache.log4j.Level;
import org.apache.log4j.spi.LoggingEvent;

public class LogAnalyzerAppender extends AppenderSkeleton {
	
	public static Map<String, LogChecker> MONITOR = new LinkedHashMap<String, LogChecker>();
	static {
		MONITOR.put("DealGroupRemoteService", new LogChecker(Arrays.asList("dealGroupRemoteService")));
	}
	
	@Override
	protected void append(LoggingEvent event) {
        Cat.logEvent("INVALID_METHOD_1", "com.dianping.api.healthcheck.LogAnalyzerAppender.append(org.apache.log4j.spi.LoggingEvent)");
        if (event.getMessage() == null || !(event.getMessage() instanceof String)) {
			return;
		}
		String message = (String) event.getMessage();
		if (message.isEmpty()) {
			return;
		}
		if (message.charAt(0)=='*' && (event.getLevel().toInt() == Level.INFO_INT || event.getLevel().toInt() == Level.ERROR_INT)) {
			for (LogChecker lc : MONITOR.values()) {
				if (!lc.isMatch(message)) {
					continue;
				}
				if (event.getLevel().toInt() == Level.INFO_INT) {
					lc.logSuccess();
				} else {
					lc.logError(message + '\n' + StringUtils.join(event.getThrowableStrRep(), '\n'));
				}
			}
		}
	}

	@Override
	public void close() {
	}

	@Override
	public boolean requiresLayout() {
        Cat.logEvent("INVALID_METHOD_3", "com.dianping.api.healthcheck.LogAnalyzerAppender.requiresLayout()");
        return false;
	}

}
