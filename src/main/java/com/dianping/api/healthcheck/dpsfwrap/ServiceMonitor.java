package com.dianping.api.healthcheck.dpsfwrap;

import com.dianping.cat.Cat;

import java.util.concurrent.ConcurrentHashMap;

public class ServiceMonitor {

	public static ConcurrentHashMap<String, ServiceInfo> SERVICES = new ConcurrentHashMap<String, ServiceInfo>();

	public static void registerService(String serviceName) {
        Cat.logEvent("INVALID_METHOD_1", "com.dianping.api.healthcheck.dpsfwrap.ServiceMonitor.registerService(java.lang.String)");
        SERVICES.put(serviceName, new ServiceInfo());
	}

	public static void logError(String serviceName, Throwable error) {
        Cat.logEvent("INVALID_METHOD_4", "com.dianping.api.healthcheck.dpsfwrap.ServiceMonitor.logError(java.lang.String,java.lang.Throwable)");
        ServiceInfo s = SERVICES.get(serviceName);
		if (s != null) {
			synchronized (s) {
				s.status = (s.status << 1) | 1;
			}
		}
	}

	public static void logSuccess(String serviceName) {
        Cat.logEvent("INVALID_METHOD_3", "com.dianping.api.healthcheck.dpsfwrap.ServiceMonitor.logSuccess(java.lang.String)");
        ServiceInfo s = SERVICES.get(serviceName);
		if (s != null) {
			synchronized (s) {
				s.status = s.status << 1;
			}
		}
	}

}
