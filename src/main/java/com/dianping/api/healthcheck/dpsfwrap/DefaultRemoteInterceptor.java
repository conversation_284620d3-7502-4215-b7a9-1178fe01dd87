package com.dianping.api.healthcheck.dpsfwrap;

import java.lang.reflect.Method;

import com.dianping.cat.Cat;
import org.aopalliance.intercept.MethodInvocation;

public class DefaultRemoteInterceptor extends RemoteInterceptor {

//	private static final AvatarLogger LOG = AvatarLoggerFactory.getLogger(DefaultRemoteInterceptor.class);
	
	@Override
	public Object invoke(MethodInvocation invocation) throws Throwable {
        Cat.logEvent("INVALID_METHOD_5", "com.dianping.api.healthcheck.dpsfwrap.DefaultRemoteInterceptor.invoke(org.aopalliance.intercept.MethodInvocation)");
        Method method = invocation.getMethod();
//		String iface = method.getDeclaringClass().getCanonicalName();
//		int p = iface.lastIndexOf('.');
//		String serviceName = p >= 0 ? iface.substring(p) : iface;
		
		String serviceName = method.getDeclaringClass().getSimpleName();
		
//		long begin = System.currentTimeMillis();
		try {
			Object result = invocation.proceed();
			ServiceMonitor.logSuccess(serviceName);
			return result;
		} catch (Throwable e) {
			ServiceMonitor.logError(serviceName, e);
//			StringBuilder builder = new StringBuilder(256);
//			 [调用时间] exception名称 类名.接口名 接口参数个数
//			builder.append(e.getClass().getName()).append(STAT_LOG_SEPERATOR);
//			builder.append(PRE_INTERFACE)
//					.append(method.getDeclaringClass().getSimpleName())
//					.append(".").append(method.getName())
//					.append(STAT_LOG_SEPERATOR);
//			builder.append(method.getParameterTypes().length);
//			LOG.error(builder);
			throw e;
		} finally {
//			StringBuilder builder = new StringBuilder(256);
			// [调用时间] 耗时 类名.接口名 接口参数个数
//			builder.append((System.currentTimeMillis() - begin)).append("ms")
//					.append(STAT_LOG_SEPERATOR);
//			builder.append(PRE_INTERFACE)
//					.append(method.getDeclaringClass().getSimpleName())
//					.append(".").append(method.getName())
//					.append(STAT_LOG_SEPERATOR);
//			builder.append(method.getParameterTypes().length);
//			LOG.info(builder);
		}
	}
}
