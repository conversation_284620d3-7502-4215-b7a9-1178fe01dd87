package com.dianping.api.healthcheck.dpsfwrap;

import org.aopalliance.intercept.MethodInterceptor;

public abstract class RemoteInterceptor implements MethodInterceptor {

	public static final String STAT_LOG_SEPERATOR = " ";
	public static final String PRE_INTERFACE = "mobcall:";

	protected Class<?> objType;

	public Class<?> getObjType() {
		return objType;
	}

	public void setObjType(Class<?> objType) {
		this.objType = objType;
	}

}
