package com.dianping.api.service;

import com.dianping.cat.Cat;
import com.dianping.gmm.markingflag.dto.MFlag;
import com.dianping.gmm.markingflag.enums.SourceCodeEnums;
import com.dianping.gmm.markingflag.service.MFlagService;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2020/12/9
 */
@Service
public class MFlagWrapperService {

    private static final Logger logger = LoggerFactory.getLogger(MFlagWrapperService.class);

    @Resource
    private MFlagService mFlagService;

    public String getExternalLink(String hashStr) {
        Cat.logEvent("INVALID_METHOD_5", "com.dianping.api.service.MFlagWrapperService.getExternalLink(java.lang.String)");
        try {
            if (StringUtils.isBlank(hashStr)) {
                return null;
            }
            MFlag mFlag = mFlagService.parseMFlagFromHashStr(hashStr);
            if (mFlag != null && Objects.equals(SourceCodeEnums.valueOfCodeOrNull(mFlag.getSourceCode()), SourceCodeEnums.CEL)) {
                return mFlag.getmFlagStr();
            }
            logger.info("getExternalLink fail, hashStr:{}, mFlag:{}", hashStr, mFlag);
        } catch (Exception e) {
            logger.error("getExternalLink error, hashStr:{}", hashStr, e);
        }
        return null;
    }
}
