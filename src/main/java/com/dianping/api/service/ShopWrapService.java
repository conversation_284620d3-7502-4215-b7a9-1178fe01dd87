package com.dianping.api.service;

import com.dianping.cat.Cat;
import com.dianping.pay.api.util.PoiIdUtils;
import com.dianping.poi.relation.service.api.PoiRelationService;
import com.dianping.shopremote.remote.ShopService;
import com.dianping.shopremote.remote.dto.ShopDTO;
import com.google.common.collect.Lists;
import com.sankuai.sinai.data.api.dto.DpPoiDTO;
import com.sankuai.sinai.data.api.dto.DpPoiRequest;
import com.sankuai.sinai.data.api.service.DpPoiService;
import com.sankuai.sinai.data.api.util.ConvertUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;

@Component
@Slf4j
public class ShopWrapService {

    @Autowired
    private ShopService shopService;

    @Autowired
    private PoiRelationService poiRelationCacheService;

    @Autowired
    private DpPoiService sinaiDpPoiService;

    public ShopDTO loadShopInfo(long shopId, boolean isMt) {
        try {
            if (shopId <= 0) {
                return null;
            }
            if (isMt) {
                List<Long> dpShopIds = poiRelationCacheService.queryDpByMtIdL(shopId);
                if (CollectionUtils.isEmpty(dpShopIds)) {
                    log.warn("call queryDpByMtId empty. shopId: {}", shopId);
                    return null;
                }
                shopId = dpShopIds.get(0);
            }
            List<Long> shopList = new ArrayList<>();
            shopList.add(shopId);
            DpPoiRequest dpPoiRequest = new DpPoiRequest();
            dpPoiRequest.setShopIds(shopList);
            dpPoiRequest.setFields(Lists.newArrayList("shopType","mainCategoryId"));
            List<DpPoiDTO> dpPoiDTOS = sinaiDpPoiService.findShopsByShopIds(dpPoiRequest);
            if(CollectionUtils.isEmpty(dpPoiDTOS)) {
                Cat.logEvent("POI_UPGRADE", "findShopsByShopIds_empty");
                log.warn("call findShopsByShopIds empty. shopId: {}", shopId);
                return null;
            }
            ShopDTO shopDTO = convertShop(dpPoiDTOS.get(0));
            if (shopDTO == null) {
                log.warn("call loadShop empty. shopId: {}", shopId);
            }
            return shopDTO;
        } catch (Exception e) {
            log.warn("call loadShop error. shopId: {}", shopId, e);
            return null;
        }
    }

    private ShopDTO convertShop(DpPoiDTO dpPoiDTO) {
        if(dpPoiDTO == null) {
            return null;
        }
        ShopDTO shopDTO = new ShopDTO();
        shopDTO.setMainCategoryId(dpPoiDTO.getMainCategoryId());
        shopDTO.setShopType(PoiIdUtils.convertIntegerToShort(dpPoiDTO.getShopType()));
        return shopDTO;
    }


}
