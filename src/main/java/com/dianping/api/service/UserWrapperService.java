package com.dianping.api.service;

import com.dianping.account.UserAccountService;
import com.dianping.account.dto.UserAccountDTO;
import com.sankuai.wpt.user.merge.query.thrift.message.FlattedBindRelationAggregateResp;
import com.sankuai.wpt.user.merge.query.thrift.message.UserMergeQueryService;
import com.sankuai.wpt.user.retrieve.thrift.message.RpcUserRetrieveService;
import com.sankuai.wpt.user.retrieve.thrift.message.UserFields;
import com.sankuai.wpt.user.retrieve.thrift.message.UserModel;
import com.sankuai.wpt.user.retrieve.thrift.message.UserRespMsg;
import org.apache.commons.lang3.tuple.Pair;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.apache.thrift.TException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Component
public class UserWrapperService {

    private static Logger logger = LogManager.getLogger(UserWrapperService.class);

    @Autowired
    private RpcUserRetrieveService.Iface rpcUserRetrieveService;

    @Autowired
    private UserMergeQueryService.Iface userMergeQueryService;

    @Autowired
    private UserAccountService userAccountService;

    public UserModel getUserByIdWithMsg(long mtUserId) {
        UserFields userFields = new  UserFields();
        userFields.setMobile(true);
        try {
            UserRespMsg userRespMsg = rpcUserRetrieveService.getUserByIdWithMsg(mtUserId, userFields);
            if (userRespMsg.isSuccess()) {
                return userRespMsg.getUser();
            }
        } catch (TException e) {
            logger.error("getUserByIdWithMsg error, mtUserId:" + mtUserId, e);
        }
        return null;
    }

    public Pair<Long, Long> getMtUserId(long dpUserId) {
        try {
            FlattedBindRelationAggregateResp data = userMergeQueryService.getFlattedBindAggregateByDpUserId(dpUserId);
            if (data != null && data.isSuccess()) {
                return data.getFlattedAggregateData() == null ? null : Pair.of(data.getFlattedAggregateData().getMtRealUserId(),
                        data.getFlattedAggregateData().getMtVirtualUserId());
            }else{
                logger.warn("getMtRealUserId fail,dpUserid:" + dpUserId);
            }
        } catch (Exception e) {
            logger.error("getRealBindByDpUserId error, dpUserid:" + dpUserId, e);
        }
        return null;
    }

    public UserAccountDTO getUserAccountByDpUser(long dpUserId) {
        try {
            UserAccountDTO userAccountDTO = userAccountService.loadById(dpUserId);
            if (userAccountDTO == null) {
                logger.warn("getUserAccountByDpUser fail, dpUserId:" + dpUserId);
            }
            return userAccountDTO;
        }catch (Exception e) {
            logger.error("getUserAccountByDpUser fail, dpUserId:" + dpUserId, e);
        }
        return null;
    }

}
