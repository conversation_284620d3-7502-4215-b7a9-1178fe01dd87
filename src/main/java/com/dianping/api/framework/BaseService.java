/**
 * 
 */
package com.dianping.api.framework;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Date;

import com.dianping.avatar.log.AvatarLogger;
import com.dianping.avatar.log.AvatarLoggerFactory;
import com.dianping.cat.Cat;
import com.dianping.commobile.dict.config.DictionaryConfig;

/**
 * <AUTHOR>
 *
 */
public class BaseService {
	protected AvatarLogger log = AvatarLoggerFactory.getLogger(this.getClass());
	protected static DictionaryConfig  resource = DictionaryConfig.getInstance();
	private static final long DAY = 24L * 60L * 60L * 1000L;
	
	/**
	 * 服务基于1900到现在的秒数，而JAVA能取得现在到1970年的毫秒数，需要求1900-1970年天数，1900 - 1970 年有 18个闰年
	 */
	protected static final int DISTANCE =  ( 1970-1900 ) * 365 + 18 ;
	
	/**
	 * 当前时间到 1900年的天数
	 * @return
	 */
	public static int dayId(){
        Cat.logEvent("INVALID_METHOD_2", "com.dianping.api.framework.BaseService.dayId()");
        return (int) (((System.currentTimeMillis()) / (24 * 60 * 60 * 1000)) + DISTANCE);
	}
	


}
