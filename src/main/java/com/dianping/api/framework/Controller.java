package com.dianping.api.framework;

import com.dianping.api.domain.Enum.AppType;
import com.dianping.api.domain.Enum.ClientType;
import com.dianping.api.domain.SimpleMsg;
import com.dianping.api.domain.SuccessMsg;
import com.dianping.api.domain.Version;
import com.dianping.api.netio.ParameterMap;
import com.dianping.avatar.log.AvatarLogger;
import com.dianping.avatar.log.AvatarLoggerFactory;
import com.dianping.cat.Cat;
import com.dianping.commobile.dict.config.DictionaryConfig;
import org.apache.commons.fileupload.FileItem;
import org.apache.commons.lang.builder.ToStringBuilder;
import org.springframework.context.ApplicationContext;
import org.springframework.util.MethodInvoker;

import java.io.IOException;
import java.io.InputStream;
import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;
import java.util.Map;
import java.util.concurrent.atomic.AtomicInteger;

public abstract class Controller {
	public static final String KEY_UPLOAD_FILE_PREFIX = "uploadFile";
	
	/**
	 * so far only support one request body for each connection,use this key
	 * to identify the request data in a multipart request
	 */
	private static final String KEY_UPLOAD_FILE_APP_REQUEST = "be_data";
	
	protected static final DictionaryConfig  RESOURCE = DictionaryConfig.getInstance();
	protected static final SimpleMsg MSG_ERROR_LACKOFPARAM = new SimpleMsg(RESOURCE.getItemValue("ERROR_TITLE"),
																		   RESOURCE.getItemValue("LACK_OF_PARAM"));
	protected static final SimpleMsg MSG_ERROR_BLOCKUSER = new SimpleMsg(RESOURCE.getItemValue("ERROR_TITLE"),
			 															 RESOURCE.getItemValue("BLOCKUSER"));

	protected static final SuccessMsg SUCCESS_MSG_ERROR_BLOCKUSER = new SuccessMsg(RESOURCE.getItemValue("ERROR_TITLE"),
																				   RESOURCE.getItemValue("BLOCKUSER"));

	protected static final SimpleMsg MSG_ERROR_INPUT_INVALID = new SimpleMsg(RESOURCE.getItemValue("NOTICE_TITLE"),
			                                                                 RESOURCE.getItemValue("INPUT_ERROR"));
	protected  AvatarLogger log = AvatarLoggerFactory.getLogger(this.getClass());
	
	private static final SimpleMsg MSG_ERROR_THROT = new SimpleMsg("提示","系统忙，请稍后再试！");

	/**
	 * subclass should not access it directly
	 */
	private Map<String, FileItem>files;
	
	protected ParameterMap parametersMap;
	protected String userIP;
	protected String useragent;
	protected String pragma;
	protected String pragmaOS;
	protected String source;
	protected Version version;
	protected String osVersion;
	protected ClientType client = ClientType.UnKnow;
	protected AppType appType = AppType.Unknown;
	protected String deviceId;
	protected ApplicationContext context;
    protected boolean isJsonClient;
    protected String jsonpCallback;
    protected String dpId;
    protected String mtId;
    protected String originalUA;
    protected String cx;

	/**
	 * identify which method will be called by client
	 */
	protected String method;

    protected MethodInvoker methodInvoker;

	/**
	 * identify whether need to format result, if it is false, the result will be write directly to output stream
	 */
	protected boolean needFormat = true;
	  
	/**
	 * controller name configured in spring 
	 */
	protected String controllerKey;

	protected long userID = 0L;
    private String weixinUtm;
    protected boolean printResponse;

	/**
	 * identify run which method need check blockUser
	 */
	protected boolean blockUserCheck = false;
	
	public String getControllerKey() {
		return controllerKey;
	}

	public void setControllerKey(String controllerKey) {
		this.controllerKey = controllerKey;
	}
	
	public long getUserID() {
		return userID;
	}

	public void setUserID(long userID) {
		this.userID = userID;
	}

	public boolean isNeedFormat() {
		return needFormat;                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                           
	}
	
	public void setNeedFormat(boolean needFormat) {
		this.needFormat = needFormat;
	}

	public void setFiles(Map<String, FileItem> files) {
        Cat.logEvent("INVALID_METHOD_1", "com.dianping.api.framework.Controller.setFiles(java.util.Map)");
        this.files = files;
	}
	
	/**
	 * the method only used by MainServlet
	 * @param fileName
	 * @return
	 */
	public static boolean isRequestData(String fileName){
        Cat.logEvent("INVALID_METHOD_4", "com.dianping.api.framework.Controller.isRequestData(java.lang.String)");
        return KEY_UPLOAD_FILE_APP_REQUEST.equals(fileName);
	}
	
	/**
	 * so far we only support one app request in a multi part request
	 * @return
	 */
	public InputStream getRequestInputStream(){
		return this.getFile(KEY_UPLOAD_FILE_APP_REQUEST);
	}
	
	/**
	 * so far we don't support fetching file by its name; 
	 * we don'e make the protocol based on the file's name 
	 */
	private InputStream getFile(String name){
		if(files == null || files.get(name) == null) {
			return null;
		}
		
		try {
			return files.get(name).getInputStream();
		} catch (IOException e) {
			log.error("get upload file inputstream error!", e);
			return null;
		}		
	}
	
	/**
	 * so far we only support upload one files, all subclass of controller should get the upload by this method
	 * @return
	 */
	public InputStream getFile(int i ){
        Cat.logEvent("INVALID_METHOD_2", "com.dianping.api.framework.Controller.getFile(int)");
        return getFile(KEY_UPLOAD_FILE_PREFIX + i);
	}
	
	protected byte[] getFileBytes(int i ){
        Cat.logEvent("INVALID_METHOD_2", "com.dianping.api.framework.Controller.getFileBytes(int)");
        if(files == null || !files.containsKey(KEY_UPLOAD_FILE_PREFIX + i)) {
			return null;
		}
		FileItem fileItem = files.get(KEY_UPLOAD_FILE_PREFIX + i); 	 
		return fileItem.get();
	}
	 
	public ApplicationContext getContext() {
		return context;
	}
	public void setContext(ApplicationContext context) {
		this.context = context;
	}
	public void setMethod(String method){
        Cat.logEvent("INVALID_METHOD_3", "com.dianping.api.framework.Controller.setMethod(java.lang.String)");
        this.method = method;
	}
	public ParameterMap getParametersMap() {
		return parametersMap;
	}
	public void setParametersMap(ParameterMap parametersMap) {
		this.parametersMap = parametersMap;
	}
	public String getUserIP() {
		return userIP;
	}
	public void setUserIP(String userIP) {
		this.userIP = userIP;
	}
	public String getUseragent() {
		return useragent;
	}
	public void setUseragent(String useragent) {
		this.useragent = useragent;
	}
	public String getPragma() {
		return pragma;
	}
	public void setPragma(String pragma) {
		this.pragma = pragma;
	}
	public String getPragmaOS() {
		return pragmaOS;
	}
	public void setPragmaOS(String pragmaOS) {
		this.pragmaOS = pragmaOS;
	}
	public String getSource() {
		return source;
	}
	public void setSource(String source) {
		this.source = source!=null? source.trim():"";
	}
	public Version getVersion() {
		return version;
	}
	public void setVersion(String version){
		this.version = new Version(version);		
	}
	public void setVersion(Version version) {
		this.version = version;
	}
	public String getOsVersion() {
		return osVersion;
	}
	public void setOsVersion(String osVersion) {
		this.osVersion = osVersion;
	}
	public ClientType getClient() {
		return client;
	}
	public void setClient(ClientType client) {
		this.client = client;
	}
	public String getDeviceId() {
		return deviceId;
	}
	
	public void setDeviceId(String deviceId) {
		this.deviceId = deviceId == null ? "" : deviceId;
	}
	
	public boolean isBlockUserCheck() {
		return blockUserCheck;
	}

	public void setBlockUserCheck(boolean blockUserCheck) {
		this.blockUserCheck = blockUserCheck;
	}

    public MethodInvoker getMethodInvoker() {
        return methodInvoker;
    }

    public void setMethodInvoker(MethodInvoker methodInvoker) {
        this.methodInvoker = methodInvoker;
    }

    public boolean isPrintResponse() {
        return printResponse;
    }

    public void setPrintResponse(boolean printResponse) {
        this.printResponse = printResponse;
    }

    public boolean validateParam(){
		return true;
	}
	
	public final Object doRequest() throws Exception{
		if(!validateParam()) {
			return MSG_ERROR_LACKOFPARAM;
		}
		if (isThrotEnable() && !isPassThrot()) {// fail quickly
			log.info("throt counter reached limit, drop request.");
			return MSG_ERROR_THROT;
		}
        Object result = null;
        String methodName = null;
        try {
            this.throtIncrementAndGet();
            Method m = null;
            if(this.methodInvoker!=null){
                methodName = methodInvoker.getTargetMethod();
                if(!this.methodInvoker.isPrepared()){
                    this.methodInvoker.prepare();
                }
                m = this.methodInvoker.getPreparedMethod();
            }else if(this.method!=null){
                methodName = method;
                m = this.getClass().getMethod(method);
            }else{
                doBusiness();
            }
            if(m != null){
				result = m.invoke(this);
			}
            return result;
		} catch (NoSuchMethodException e) {
			log.warn("NoSuchMethodException: the method mapping is wrong, controller="
					+ this.getClass().getName() + " method=" + methodName);
			return null;
		}catch (InvocationTargetException e) {
            if (e.getCause() != null && e.getCause() instanceof Exception) {
                throw (Exception)e.getCause();
            }
            log.error("controller=" + this.getClass().getName() + " method="
                    + methodName, e.getCause() != null ? e.getCause() : e);
            return null;
        } finally {
			this.throtDecrementAndGet();
            if(printResponse){
                log.info(methodName+" threadId="+Thread.currentThread().getId()+" res="+ToStringBuilder.reflectionToString(result));
            }
		}
	}
	
	/**
	 * this can be overwrite by subclass which will be called if not method is defined for the URI in spring configure
	 */
	public Object doBusiness(){
        Cat.logEvent("INVALID_METHOD_2", "com.dianping.api.framework.Controller.doBusiness()");
        return null;
	}
	

	public AppType getAppType() {
		return appType;
	}

	public void setAppType(AppType appType) {
		this.appType = appType;
	}

	protected final boolean isPassThrot(){
        Cat.logEvent("INVALID_METHOD_3", "com.dianping.api.framework.Controller.isPassThrot()");
        if(this.getThrotCounter()!=null) {
			return this.getThrotCounter().get() < this.getThrotLimit();
		} else {
			log.warn("enable throt but doesn't have a throt counter:" + this.controllerKey);
			return true;
		}
	}

	protected final void throtDecrementAndGet() {
		if (this.getThrotCounter() != null) {
			this.getThrotCounter().decrementAndGet();
		}
	}

	protected final void throtIncrementAndGet() {
		if (this.getThrotCounter() != null) {
			this.getThrotCounter().incrementAndGet();
		}
	}

	/**
	 * by default, turn off the throt; 
	 * each sub controller need to overwrite the method to open it
	 * @return
	 */
	protected boolean isThrotEnable(){
		return false;
	}
	
	/**
	 * by default it is not limit; 
	 * @return
	 */
	protected  int getThrotLimit(){
        Cat.logEvent("INVALID_METHOD_5", "com.dianping.api.framework.Controller.getThrotLimit()");
        return Integer.MAX_VALUE;
	}
	
	/**
	 * overwrite the method to provide controller's own counter
	 * @return
	 */
	protected AtomicInteger getThrotCounter(){
		return null;
	}

    public void setJsonClient(boolean jsonClient) {
        isJsonClient = jsonClient;
    }

    public String getJsonpCallback() {
        return jsonpCallback;
    }

    public void setJsonpCallback(String jsonpCallback) {
        this.jsonpCallback = jsonpCallback;
    }

    public String getWeixinUtm() {
        return weixinUtm;
    }

    public void setWeixinUtm(String weixinUtm) {
        this.weixinUtm = weixinUtm;
    }

    public String getDpId() {
        return dpId;
    }

    public void setDpId(String dpId) {
        this.dpId = dpId;
    }

    public String getMtId() {
        return mtId;
    }

    public void setMtId(String mtId) {
        this.mtId = mtId;
    }

    public String getOriginalUA() {
        return originalUA;
    }

    public void setOriginalUA(String originalUA) {
        this.originalUA = originalUA;
    }

    public String getCx() {
        return cx;
    }

    public void setCx(String cx) {
        this.cx = cx;
    }

    @Override
    public String toString(){
        StringBuffer buffer = new StringBuffer();
        buffer.append(super.toString()).
                append("[controllerKey=<").append(controllerKey).
                append(">,method=<").append(method).
                append(">,useragent=<").append(useragent).
                append(">,pragma=<").append(pragma).
                append(">,pragmaos=<").append(pragmaOS).
                append(">,source=<").append(source).
                append(">,version=<").append(version).
                append(">,osversion=<").append(osVersion).
                append(">,clienttype=<").append(client).
                append(">,apptype=<").append(appType).
                append(">,deviceid=<").append(deviceId).
                append(">,dpid=<").append(dpId).
                append(">,mtid=<").append(mtId).
                append(">,orignialua=<").append(originalUA).
                append(">,weixinutm=<").append(weixinUtm).
                append(">,userid=<").append(userID).
                append(">]");
        return buffer.toString();
    }
}
