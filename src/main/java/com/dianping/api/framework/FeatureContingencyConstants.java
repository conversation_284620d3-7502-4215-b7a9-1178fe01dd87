//CODE GENERATED SOURCE FILE, PLEASE DO NOT CHANGE IT MANUALLY
//USE tool com.dianping.api.tools.FeatureContingencyCodeGen to generate the source file again after change.

package com.dianping.api.framework;

public final class FeatureContingencyConstants {

	public static final int STATUS_ON = 0;
	public static final int STATUS_OFF = 1;
	public static final int STATUS_DELETED = 2;

	public static final int FCID_HAWK = 1;

	public static final int FCID_CAT = 3;

	public static final int FCID_REVIEW_MOBILE_SERVICE = 4;

	public static final int FCID_GROUPON_SERVICE = 5;

	public static final int FCID_GROUPON_THROTTLING = 6;

	public static final int FCID_IPHONE_SCORE = 7;

	public static final int FCID_LOGIN = 8;

	public static final int FCID_RULE_ENGINE = 10;

	public static final int FCID_CONFIG_PUSH = 11;

	public static final int FCID_ADDSHOP = 12;

	public static final int FCID_USER_BLOCKLIST = 13;

	public static final int FCID_PHONE_BIND_DISABLED = 14;

	public static final int FCID_BOUND_PHONE_MASKED = 15;
	
	public static final int FCID_DEAL_SUGGEST_ENABLED = 16;

	public static final int FCID_FORCE_LOGOUT = 17;
	
	public static final int FCID_MAIN_APP_NATIVE = 18;
	
	public static final int FCID_THIRD_FEED_LOG = 19;
	
	private FeatureContingencyConstants() {
	}
	
}
