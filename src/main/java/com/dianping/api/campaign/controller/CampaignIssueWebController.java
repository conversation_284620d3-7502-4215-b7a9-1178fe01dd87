package com.dianping.api.campaign.controller;

import com.dianping.api.campaign.biz.CampaignIssueWebBiz;
import com.dianping.api.campaign.biz.HttpContextBiz;
import com.dianping.api.campaign.dto.*;
import com.dianping.api.campaign.enums.CampaignResultCodeWebEnum;
import com.dianping.api.campaign.request.CampaignIssueWebRequest;
import com.dianping.api.campaign.request.CampaignQueryInflateRequest;
import com.dianping.api.campaign.request.CampaignRenderWebRequest;
import com.dianping.api.campaign.response.CampaignWebResponse;
import com.dianping.api.campaign.utils.CampaignDotUtils;
import com.dianping.api.campaign.utils.CampaignRhinoUtils;
import com.dianping.api.campaign.utils.LogUtils;
import com.dianping.api.service.UserWrapperService;
import com.dianping.api.util.TraceUtils;
import com.dianping.cat.Cat;
import com.dianping.gmkt.coupon.common.api.enums.UserTypeEnum;
import com.dianping.lion.client.ConfigEvent;
import com.dianping.lion.client.ConfigListener;
import com.dianping.lion.client.Lion;
import com.dianping.pay.api.biz.activity.newloader.dto.RestUserInfo;
import com.dianping.pay.api.constants.CatEventConstants;
import com.dianping.pay.api.util.JsonUtils;
import com.dianping.pay.api.util.LionConstants;
import com.dianping.unified.coupon.manage.api.utils.CampaignUtils;
import com.sankuai.ia.phx.utils.StringUtils;
import com.sankuai.mpmkt.coupon.issue.octo.api.enums.CampaignSceneEnum;
import com.sankuai.wpt.user.retrieve.thrift.message.UserModel;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.Validate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;


@RequestMapping( "/promo/campaign")
@RestController
@Slf4j
public class CampaignIssueWebController {


    @Autowired
    private CampaignIssueWebBiz campaignIssueWebBiz;

    @Autowired
    private HttpContextBiz httpContextBiz;

    @Autowired
    private UserWrapperService userWrapperService;


    private static boolean ALLOW_UNLOGIN;

    private static boolean MMC_ALLOW_UN_LOGIN;

    static {
        ALLOW_UNLOGIN = Lion.getBooleanValue(LionConstants.CAMPAIGN_RENDER_ALLOW_UNLOGIN, false);
        Lion.addConfigListener(LionConstants.CAMPAIGN_RENDER_ALLOW_UNLOGIN, new ConfigListener() {
            @Override
            public void configChanged(ConfigEvent configEvent) {
                ALLOW_UNLOGIN = Lion.getBooleanValue(LionConstants.CAMPAIGN_RENDER_ALLOW_UNLOGIN, false);
            }
        });
        MMC_ALLOW_UN_LOGIN = Lion.getBooleanValue(LionConstants.CAMPAIGN_MMC_ALLOW_UN_LOGIN, false);
        Lion.addConfigListener(LionConstants.CAMPAIGN_MMC_ALLOW_UN_LOGIN, new ConfigListener() {
            @Override
            public void configChanged(ConfigEvent configEvent) {
                MMC_ALLOW_UN_LOGIN = Lion.getBooleanValue(LionConstants.CAMPAIGN_MMC_ALLOW_UN_LOGIN, false);
            }
        });
    }


    @PostMapping(value = "/issue")
    public CampaignWebResponse<CampaignIssueWebResult> issueCoupons(@RequestBody CampaignIssueWebRequest request, HttpServletRequest servletRequest) {
        CampaignWebResponse<CampaignIssueWebResult> campaignResponse = null;

        try {
            Validate.notNull(request, "request cannot null");
            Validate.notNull(request.getUserInfo(), "userCode cannot null");
            Validate.notNull(request.getCampaignKeys(), "campaignKeys cannot null");
            UserTypeEnum userTypeEnum = UserTypeEnum.toUser(request.getUserInfo().getUserCode());
            Validate.notNull(userTypeEnum, "userCode illegal");

            for (String campaignKey : request.getCampaignKeys()) {
                Validate.isTrue(CampaignUtils.validCampaignKey(campaignKey), "campaign key illegal");
            }

            CampaignDotUtils.dot4IssueRequest(request);

            if (!CampaignRhinoUtils.allowCampaignIssue(request)) {
                Cat.logEvent(CatEventConstants.CAMPAIGN_ISSUE_RESULT, String.valueOf(CampaignResultCodeWebEnum.RATE_LIMIT.getCode()));
                return new CampaignWebResponse<>(CampaignResultCodeWebEnum.RATE_LIMIT);
            }

            HttpContext httpContext = httpContextBiz.buildContext(servletRequest, request);
            RestUserInfo restUserInfo = httpContext.getUserInfo();

            if (restUserInfo == null || restUserInfo.getUserId() <= 0) {
                Cat.logEvent(CatEventConstants.CAMPAIGN_ISSUE_RESULT, String.valueOf(CampaignResultCodeWebEnum.UN_LOGIN.getCode()));
                return new CampaignWebResponse<>(CampaignResultCodeWebEnum.UN_LOGIN);
            } else if (request.getCampaignScene() == CampaignSceneEnum.MAGICAL_MEMBER.getCode()) {
                assembleUserInfoWithPhone(restUserInfo);
                TraceUtils.setUserInfo(servletRequest, restUserInfo);
            }

            campaignResponse = campaignIssueWebBiz.issueCoupons(request, httpContext, restUserInfo);
        } catch (IllegalArgumentException e) {
            log.error("campaign issue illegal param, {}", JsonUtils.toJson(request), e);
            campaignResponse = new CampaignWebResponse<>(CampaignResultCodeWebEnum.ILLEGAL_PARAM);
        } catch (Exception e) {
            log.error("campaign issue exception, {}", JsonUtils.toJson(request), e);
            campaignResponse = new CampaignWebResponse<>(CampaignResultCodeWebEnum.ISSUE_FAIL);
        }

        Cat.logEvent(CatEventConstants.CAMPAIGN_ISSUE_RESULT, String.valueOf(campaignResponse.getResultCode()));


        if (LogUtils.ifSample(this.getClass().getSimpleName(), "issueCoupons")) {
            log.info("issueCoupons req:{}, resp:{}", JsonUtils.toJson(request), JsonUtils.toJson(campaignResponse));
        }

        return campaignResponse;
    }

    @PostMapping(value = "/render")
    public CampaignWebResponse<CampaignIssueValidateWebResult> render(@RequestBody CampaignRenderWebRequest request, HttpServletRequest servletRequest) {
        CampaignWebResponse<CampaignIssueValidateWebResult> renderResponse;
        try {
            Validate.notNull(request, "request cannot null");
            Validate.notNull(request.getUserInfo(), "userCode cannot null");
            Validate.notNull(request.getCampaignKeys(), "campaignKeys cannot null");
            UserTypeEnum userTypeEnum = UserTypeEnum.toUser(request.getUserInfo().getUserCode());
            Validate.notNull(userTypeEnum, "userCode illegal");

            for (String campaignKey : request.getCampaignKeys()) {
                Validate.isTrue(CampaignUtils.validCampaignKey(campaignKey), "campaign key illegal");
            }
            
            CampaignDotUtils.dot4RenderRequest(request);

            if (!CampaignRhinoUtils.allowCampaignRender(request)) {
                Cat.logEvent(CatEventConstants.CAMPAIGN_RENDER_RESULT, String.valueOf(CampaignResultCodeWebEnum.RATE_LIMIT.getCode()));
                return new CampaignWebResponse<>(CampaignResultCodeWebEnum.RATE_LIMIT);
            }

            HttpContext httpContext = httpContextBiz.buildContext(servletRequest, request);
            RestUserInfo restUserInfo = httpContext.getUserInfo();

            if (restUserInfo == null || restUserInfo.getUserId() <= 0) {
                Cat.logEvent(CatEventConstants.CAMPAIGN_RENDER_USER_INFO, "noUserIdNoToken");
                if (!ALLOW_UNLOGIN ||
                        (request.getCampaignScene() == CampaignSceneEnum.MAGICAL_MEMBER.getCode() && !MMC_ALLOW_UN_LOGIN)) {
                    Cat.logEvent(CatEventConstants.CAMPAIGN_RENDER_RESULT, String.valueOf(CampaignResultCodeWebEnum.UN_LOGIN.getCode()));
                    return new CampaignWebResponse<>(CampaignResultCodeWebEnum.UN_LOGIN);
                }
            } else if (request.getCampaignScene() == CampaignSceneEnum.MAGICAL_MEMBER.getCode()) {
                assembleUserInfoWithPhone(restUserInfo);
                TraceUtils.setUserInfo(servletRequest, restUserInfo);
            }


            renderResponse = campaignIssueWebBiz.render(request, httpContext, restUserInfo);
        } catch (IllegalArgumentException e) {
            log.error("campaign render illegal param, {}", JsonUtils.toJson(request), e);
            renderResponse = new CampaignWebResponse<>(CampaignResultCodeWebEnum.ILLEGAL_PARAM);
        } catch (Exception e) {
            log.error("campaign render validate exception, {}", JsonUtils.toJson(request), e);
            renderResponse = new CampaignWebResponse<>(CampaignResultCodeWebEnum.VALIDATE_FAIL);
        }

        Cat.logEvent(CatEventConstants.CAMPAIGN_RENDER_RESULT, String.valueOf(renderResponse.getResultCode()));

        if (LogUtils.ifSample(this.getClass().getSimpleName(), "render")) {
            log.info("render req:{}, resp:{}", JsonUtils.toJson(request), JsonUtils.toJson(renderResponse));
        }
        return renderResponse;
    }


    @GetMapping(value = "/time")
    public CampaignWebResponse<CampaignTimeResult> getTime() {
        if (!CampaignRhinoUtils.allowCampaignTime()) {
            Cat.logEvent(CatEventConstants.CAMPAIGN_TIME_RESULT, String.valueOf(CampaignResultCodeWebEnum.RATE_LIMIT.getCode()));
            return new CampaignWebResponse<>(CampaignResultCodeWebEnum.RATE_LIMIT);
        }

        CampaignTimeResult result = new CampaignTimeResult();
        result.setCurrentTimeMillis(String.valueOf(System.currentTimeMillis()));
        return new CampaignWebResponse<>(CampaignResultCodeWebEnum.SUCCESS, result);
    }

    @PostMapping(value = "/queryinflate")
    public CampaignWebResponse<CampaignInflateCouponWebResult> queryInflate(@RequestBody CampaignQueryInflateRequest request, HttpServletRequest servletRequest) {
        CampaignWebResponse<CampaignInflateCouponWebResult> inflateResponse;
        try {
            Validate.notNull(request, "request cannot null");
            Validate.notNull(request.getUserInfo(), "userCode cannot null");
            Validate.isTrue(StringUtils.isNotBlank(request.getUnifiedCouponId()), "unifiedCouponId cannot null");
            UserTypeEnum userTypeEnum = UserTypeEnum.toUser(request.getUserInfo().getUserCode());
            Validate.notNull(userTypeEnum, "userCode illegal");
            Validate.isTrue(userTypeEnum == UserTypeEnum.MT, "support mt only");

            CampaignDotUtils.dot4QueryInflateRequest(request);

            if (!CampaignRhinoUtils.allowCampaignQueryInflate(request)) {
                Cat.logEvent(CatEventConstants.CAMPAIGN_QUERY_INFLATE_RESULT, String.valueOf(CampaignResultCodeWebEnum.RATE_LIMIT.getCode()));
                return new CampaignWebResponse<>(CampaignResultCodeWebEnum.RATE_LIMIT);
            }

            HttpContext httpContext = httpContextBiz.buildContext(servletRequest, request);
            RestUserInfo restUserInfo = httpContext.getUserInfo();

            if (restUserInfo == null || restUserInfo.getUserId() <= 0) {
                Cat.logEvent(CatEventConstants.CAMPAIGN_QUERY_INFLATE_USER_INFO, "noUserIdNoToken");
                Cat.logEvent(CatEventConstants.CAMPAIGN_QUERY_INFLATE_RESULT, String.valueOf(CampaignResultCodeWebEnum.UN_LOGIN.getCode()));
                return new CampaignWebResponse<>(CampaignResultCodeWebEnum.UN_LOGIN);
            } else {
                TraceUtils.setUserInfo(servletRequest, restUserInfo);
            }

            inflateResponse = campaignIssueWebBiz.queryInflate(request, httpContext);
        } catch (IllegalArgumentException e) {
            log.error("campaign queryInflate illegal param, {}", JsonUtils.toJson(request), e);
            inflateResponse = new CampaignWebResponse<>(CampaignResultCodeWebEnum.ILLEGAL_PARAM);
        } catch (Exception e) {
            log.error("campaign queryInflate validate exception, {}", JsonUtils.toJson(request), e);
            inflateResponse = new CampaignWebResponse<>(CampaignResultCodeWebEnum.VALIDATE_FAIL);
        }

        Cat.logEvent(CatEventConstants.CAMPAIGN_RENDER_RESULT, String.valueOf(inflateResponse.getResultCode()));

        if (LogUtils.ifSample(this.getClass().getSimpleName(), "queryInflate")) {
            log.info("queryInflate req:{}, resp:{}", JsonUtils.toJson(request), JsonUtils.toJson(inflateResponse));
        }
        return inflateResponse;
    }

    private void assembleUserInfoWithPhone(RestUserInfo restUserInfo) {
        if (restUserInfo == null || restUserInfo.getUserId() < 0) {
            return;
        }
        UserModel userModel = userWrapperService.getUserByIdWithMsg(restUserInfo.getUserId());
        if (userModel == null) {
            return;
        }
        restUserInfo.setPhone(userModel.getMobile());
    }

}
