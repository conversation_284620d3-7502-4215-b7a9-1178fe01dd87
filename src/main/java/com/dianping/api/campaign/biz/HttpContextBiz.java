package com.dianping.api.campaign.biz;

import com.dianping.account.UserAccountService;
import com.dianping.account.dto.VirtualBindUserInfoDTO;
import com.dianping.account.utils.constants.CommonWebConstants;
import com.dianping.account.utils.util.HttpUtils;
import com.dianping.api.campaign.dto.HttpContext;
import com.dianping.api.campaign.dto.UserWebInfo;
import com.dianping.api.campaign.request.CampaignIssueWebRequest;
import com.dianping.api.campaign.request.CampaignQueryInflateRequest;
import com.dianping.api.campaign.request.CampaignRenderWebRequest;
import com.dianping.api.campaign.utils.LogUtils;
import com.dianping.cat.Cat;
import com.dianping.gmkt.coupon.common.api.enums.UserTypeEnum;
import com.dianping.lion.client.ConfigEvent;
import com.dianping.lion.client.ConfigListener;
import com.dianping.lion.client.Lion;
import com.dianping.pay.api.biz.activity.newloader.dto.RestUserInfo;
import com.dianping.pay.api.util.JsonUtils;
import com.dianping.pay.api.util.LionConstants;
import com.dianping.pay.promo.common.enums.User;
import com.meituan.mtrace.Tracer;
import com.sankuai.wpt.user.thrift.token.UserIdMsg;
import com.sankuai.wpt.user.thrift.token.UserValidateTokenService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.servlet.http.HttpServletRequest;

@Slf4j
@Component
public class HttpContextBiz {

    private static boolean trustWebUserSwitch;

    private static boolean trustWebUserSwitch4Test;

    @Autowired
    private UserAccountService userAccountService;
    
    @Autowired
    private UserValidateTokenService.Iface rpcTokenService;

    static {
        trustWebUserSwitch = Lion.getBooleanValue(LionConstants.TRUST_WEB_USER_SWITCH, false);
        Lion.addConfigListener(LionConstants.TRUST_WEB_USER_SWITCH, new ConfigListener() {
            @Override
            public void configChanged(ConfigEvent configEvent) {
                trustWebUserSwitch = Lion.getBooleanValue(LionConstants.TRUST_WEB_USER_SWITCH, false);
            }
        });


        trustWebUserSwitch4Test = Lion.getBooleanValue(LionConstants.TRUST_WEB_USER_TEST_SWITCH, false);
        Lion.addConfigListener(LionConstants.TRUST_WEB_USER_TEST_SWITCH, new ConfigListener() {
            @Override
            public void configChanged(ConfigEvent configEvent) {
                trustWebUserSwitch4Test = Lion.getBooleanValue(LionConstants.TRUST_WEB_USER_TEST_SWITCH, false);
            }
        });
    }


    private static String getCookieToken(HttpServletRequest request, String key) {
        String cookieToken = HttpUtils.getCookie(request, key);
        if (isValidToken(cookieToken)) {
            return cookieToken;
        }
        return null;
    }

    private static boolean isValidToken(String token) {
        return StringUtils.isNotBlank(token) && token.length() >= 10;
    }




    public RestUserInfo getUserInfo(HttpServletRequest request, UserWebInfo userInfo){
        RestUserInfo restUserInfo = new RestUserInfo();
        
        if (trustWebUserSwitch || (trustWebUserSwitch4Test && Tracer.isTest())) {
            restUserInfo.setUserId(userInfo.getUserId());
            restUserInfo.setUserCode(userInfo.getUserCode());
            restUserInfo.setToken(userInfo.getToken());
            return restUserInfo;
        }

        // 不传就按点评处理
        int userCode = userInfo.getUserCode();

        restUserInfo.setUserCode(userCode);
        String token = getToken(request, userInfo);
        if (StringUtils.isBlank(token)) {
            token = getCookieToken(request, "oops");
        }
        if(StringUtils.isBlank(token)) {
            return restUserInfo;
        }
        try {
            if (userCode == User.MT.getCode()) {
                UserIdMsg userIdByToken = rpcTokenService.getUserIdByToken(token);
                if (LogUtils.ifSample("rpcTokenService", "getUserIdByToken")) {
                    log.info("getUserIdByToken: result:{}", userIdByToken);
                }
                if (userIdByToken != null) {
                    restUserInfo.setUserId(userIdByToken.getUserId());
                }
            } else {
                VirtualBindUserInfoDTO virtualBindUserInfoDTO = userAccountService.loadUserByToken(token, HttpUtils.getUserIp(request), null);
                if (LogUtils.ifSample("userAccountService", "loadUserByToken")) {
                    log.info("loadUserByToken: result:{}", virtualBindUserInfoDTO);
                }
                if(virtualBindUserInfoDTO != null && virtualBindUserInfoDTO.getDpid() != null){
                    restUserInfo.setUserId(virtualBindUserInfoDTO.getDpid());
                }
            }
        }catch (Exception e){
            log.error(String.format("loadUserByToken exception! token:{}", token),  e);
        }

        restUserInfo.setToken(token);
        return restUserInfo;
    }


    private String getToken(HttpServletRequest request, UserWebInfo userInfo) {
        if(request == null){
            return "";
        }

        String token = "";

        token = userInfo.getToken();
        if(StringUtils.isNotBlank(token)){
            return token;
        }

        if(userInfo.getUserCode() == User.DP.getCode()){
            token = HttpUtils.getCookie(request, CommonWebConstants.COOKIE_KEY_DPER);
            if(StringUtils.isNotBlank(token)){
                return token;
            }
        }

        token = HttpUtils.getCookie(request, CommonWebConstants.COOKIE_KEY_TOKEN);
        if(StringUtils.isNotBlank(token)){
            return token;
        }

        token = request.getHeader("token");
        if(StringUtils.isNotBlank(token)){
            return token;
        }

        return "";
    }

    public HttpContext buildContext(HttpServletRequest servletRequest, CampaignIssueWebRequest request) {
        UserWebInfo userWebInfo = request.getUserInfo();

        RestUserInfo restUserInfo = getUserInfo(servletRequest, userWebInfo);

        HttpContext httpContext = new HttpContext();
        httpContext.setUserInfo(restUserInfo);
        httpContext.setIp(HttpUtils.getUserIp(servletRequest));
        httpContext.setUserAgent(HttpUtils.getUserAgent(servletRequest));
        httpContext.setReferer(HttpUtils.getReferer(servletRequest));
        httpContext.setMtgsig(getMtg(servletRequest));
        httpContext.setDeviceId(getDeviceId(servletRequest, request.getUuid(), userWebInfo.getUserCode()));

        if (request.getUserInfo().getUserId() > 0 && request.getUserInfo().getUserId() != restUserInfo.getUserId()) {
            Cat.logEvent("IllegalUserId", String.format("issue#expected#%d#act#%d", request.getUserInfo().getUserId(), restUserInfo.getUserId()));
        }

        if (StringUtils.isBlank(restUserInfo.getToken())) {
            Cat.logEvent("TokenWarn", "IssueNoToken");
            log.info("issue no token, {}", JsonUtils.toJson(request));
        }

        return httpContext;
    }

    public HttpContext buildContext(HttpServletRequest servletRequest, CampaignRenderWebRequest request) {
        UserWebInfo userWebInfo = request.getUserInfo();

        RestUserInfo restUserInfo = getUserInfo(servletRequest, userWebInfo);

        HttpContext httpContext = new HttpContext();
        httpContext.setUserInfo(restUserInfo);
        httpContext.setIp(HttpUtils.getUserIp(servletRequest));
        httpContext.setUserAgent(HttpUtils.getUserAgent(servletRequest));
        httpContext.setReferer(HttpUtils.getReferer(servletRequest));
        httpContext.setMtgsig(getMtg(servletRequest));
        httpContext.setDeviceId(getDeviceId(servletRequest, request.getUuid(), userWebInfo.getUserCode()));

        if (request.getUserInfo().getUserId() > 0 && request.getUserInfo().getUserId() != restUserInfo.getUserId()) {
            Cat.logEvent("IllegalUserId", String.format("render#expected#%d#act#%d", request.getUserInfo().getUserId(), restUserInfo.getUserId()));
        }

        if (StringUtils.isBlank(restUserInfo.getToken())) {
            Cat.logEvent("TokenWarn", "RenderNoToken");
        }

        return httpContext;
    }

    public HttpContext buildContext(HttpServletRequest servletRequest, CampaignQueryInflateRequest request) {
        UserWebInfo userWebInfo = request.getUserInfo();

        RestUserInfo restUserInfo = getUserInfo(servletRequest, userWebInfo);

        HttpContext httpContext = new HttpContext();
        httpContext.setUserInfo(restUserInfo);
        httpContext.setIp(HttpUtils.getUserIp(servletRequest));
        httpContext.setUserAgent(HttpUtils.getUserAgent(servletRequest));
        httpContext.setReferer(HttpUtils.getReferer(servletRequest));
        httpContext.setMtgsig(getMtg(servletRequest));
        httpContext.setDeviceId(getDeviceId(servletRequest, request.getUuid(), userWebInfo.getUserCode()));

        if (request.getUserInfo().getUserId() > 0 && request.getUserInfo().getUserId() != restUserInfo.getUserId()) {
            Cat.logEvent("IllegalUserId", String.format("queryinflate#expected#%d#act#%d", request.getUserInfo().getUserId(), restUserInfo.getUserId()));
        }

        if (StringUtils.isBlank(restUserInfo.getToken())) {
            Cat.logEvent("TokenWarn", "QueryInflateNoToken");
        }

        return httpContext;
    }

    private String getMtg(HttpServletRequest servletRequest) {
        String mtgsig = HttpUtils.getHeader(servletRequest, "mtgsig");
        if (StringUtils.isNotBlank(mtgsig)) {
            return mtgsig;
        }
        return HttpUtils.getParamAttribute(servletRequest, "mtgsig");
    }

    private String getDeviceId(HttpServletRequest servletRequest, String deviceId, int userCode) {
        if (StringUtils.isNotBlank(deviceId)) {
            return deviceId;
        }

        if (userCode == UserTypeEnum.DP.getCode()) {
            deviceId = HttpUtils.getCookie(servletRequest, "dpid");
            if (StringUtils.isBlank(deviceId)) {
                deviceId = HttpUtils.getCookie(servletRequest, "_lxsdk_dpid");
            }
            if (StringUtils.isBlank(deviceId)) {
                deviceId = HttpUtils.getCookie(servletRequest, "_lxsdk_cuid");
            }
        } else {
            deviceId = HttpUtils.getCookie(servletRequest, "uuid");
        }
        return deviceId;
    }
}
