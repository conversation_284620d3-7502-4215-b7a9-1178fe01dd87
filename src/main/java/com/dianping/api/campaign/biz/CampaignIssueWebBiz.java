package com.dianping.api.campaign.biz;


import com.dianping.api.campaign.dto.CampaignIssueWebResult;
import com.dianping.api.campaign.dto.*;
import com.dianping.api.campaign.enums.CampaignCouponStatusWebEnum;
import com.dianping.api.campaign.enums.CampaignResultCodeWebEnum;
import com.dianping.api.campaign.request.CampaignQueryInflateRequest;
import com.dianping.api.campaign.response.CampaignWebResponse;
import com.dianping.api.campaign.request.CampaignRenderWebRequest;
import com.dianping.api.campaign.request.CampaignIssueWebRequest;
import com.dianping.api.campaign.utils.CampaignInflateUtils;
import com.dianping.api.campaign.utils.MapperUtils;
import com.dianping.api.util.PositionUtils;
import com.dianping.cat.Cat;
import com.dianping.gmkt.coupon.common.api.dto.campaign.CampaignCGInfoDTO;
import com.dianping.gmkt.coupon.common.api.enums.CouponGroupExtraKeyEnum;
import com.dianping.gmkt.coupon.common.api.enums.InflateStatusEnum;
import com.dianping.gmkt.coupon.common.api.enums.MtCouponPlatform;
import com.dianping.gmkt.coupon.common.api.enums.campaign.CampaignStatusEnum;
import com.dianping.lion.client.ConfigEvent;
import com.dianping.lion.client.ConfigListener;
import com.dianping.lion.client.Lion;
import com.dianping.pay.api.biz.activity.newloader.dto.RestUserInfo;
import com.dianping.pay.api.util.DateUtils;
import com.dianping.pay.api.util.JsonUtils;
import com.dianping.pay.api.util.LionConstants;
import com.dianping.unified.coupon.manage.api.UnifiedCouponListService;
import com.dianping.unified.coupon.manage.api.dto.UnifiedCouponDTO;
import com.dianping.unified.coupon.manage.api.dto.UnifiedCouponGroupDTO;
import com.dianping.unified.coupon.manage.api.request.UnifiedCouponQueryInflateRequest;
import com.dianping.unified.coupon.manage.api.response.CouponQueryInflateResponse;
import com.dianping.unified.coupon.manage.api.response.UnifiedCouponManageResponse;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.meituan.service.mobile.mtthrift.degrage.ServiceDegradeException;
import com.sankuai.mpmkt.coupon.group.query.api.dto.BaseCampaignDTO;
import com.sankuai.mpmkt.coupon.group.query.api.dto.BaseCouponGroupDTO;
import com.sankuai.mpmkt.coupon.issue.octo.api.CampaignIssueCouponService;
import com.sankuai.mpmkt.coupon.issue.octo.api.dto.*;
import com.sankuai.mpmkt.coupon.issue.octo.api.enums.CampaignResultCodeEnum;
import com.sankuai.mpmkt.coupon.issue.octo.api.enums.CampaignSceneEnum;
import com.sankuai.mpmkt.coupon.issue.octo.api.request.CampaignIssueRequest;
import com.sankuai.mpmkt.coupon.issue.octo.api.request.CampaignIssueValidateRequest;
import com.sankuai.mpmkt.coupon.issue.octo.api.request.CampaignRecordQueryRequest;
import com.sankuai.mpmkt.coupon.issue.octo.api.response.CampaignIssueResponse;
import com.sankuai.mpmkt.coupon.issue.octo.api.response.CampaignIssueValidateResponse;
import com.sankuai.mpmkt.coupon.issue.octo.api.response.CampaignRecordResponse;
import com.sankuai.nib.mkt.magicDegarade.MagicControlResult;
import com.sankuai.nib.mkt.magicDegarade.MagicGrayUtil;
import com.sankuai.nib.mkt.magicGrayScale.GrayControlReq;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang.math.NumberUtils;
import org.apache.thrift.TException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Component
public class CampaignIssueWebBiz {

    private static final String ISSUE_SOURCE = "MARKET_MAKER_PLATFORM";

    private static final String CAMPAIGN_WARN = "campaignWarn";

    private static boolean MMC_CAMPAIGN_WHITE_LIST_BLOCK_SWITCH = false;

    static {
        MMC_CAMPAIGN_WHITE_LIST_BLOCK_SWITCH = Lion.getBooleanValue(LionConstants.MMC_CAMPAIGN_WHITE_LIST_BLOCK_SWITCH, false);
        Lion.addConfigListener(LionConstants.MMC_CAMPAIGN_WHITE_LIST_BLOCK_SWITCH, new ConfigListener() {
            @Override
            public void configChanged(ConfigEvent configEvent) {
                MMC_CAMPAIGN_WHITE_LIST_BLOCK_SWITCH = Lion.getBooleanValue(LionConstants.MMC_CAMPAIGN_WHITE_LIST_BLOCK_SWITCH, false);
            }
        });
    }


    @Autowired
    private CampaignIssueCouponService campaignIssueCouponService;

    @Autowired
    private CampaignRepository campaignRepository;

    @Autowired
    private UnifiedCouponGroupRepository couponGroupRepository;

    @Autowired
    private UnifiedCouponListService unifiedCouponListService;

    public CampaignWebResponse<CampaignIssueWebResult> issueCoupons(CampaignIssueWebRequest request, HttpContext context, RestUserInfo restUserInfo) {
        if (needBlockByMmcWhiteList(request.getCampaignScene(), restUserInfo.getUserId(), request.getMmcParams())) {
            Cat.logEvent(CAMPAIGN_WARN, "mmcWhiteListBlock");
            return new CampaignWebResponse<>(CampaignResultCodeWebEnum.NOT_ONLINE.getCode(), CampaignResultCodeWebEnum.NOT_ONLINE.getMessage());
        }
        CampaignIssueRequest issueRequest = buildIssueReq(request, restUserInfo, context);
        PositionUtils.reportValidateParams(issueRequest, restUserInfo);
        CampaignIssueResponse campaignIssueResponse = null;
        Integer errorCode = null;
        try {
            campaignIssueResponse = campaignIssueCouponService.issueCoupons(issueRequest);
        } catch (TException e) {
            if (e.getMessage() !=null && e.getMessage().contains(ServiceDegradeException.class.getSimpleName())){
                errorCode = CampaignResultCodeEnum.CAMPAIGN_RATE_LIMIT.getCode();
            }
            log.error("issueCoupons fail, {}", JsonUtils.toJson(request), e);
        }

        if (campaignIssueResponse == null || campaignIssueResponse.getResult() == null) {
            CampaignResultCodeWebEnum resultCodeWebEnum = map2IssueWebResultCode(campaignIssueResponse == null ?
                (errorCode == null ? CampaignResultCodeWebEnum.ISSUE_FAIL.getCode() : errorCode) : campaignIssueResponse.getResultCode());
            return new CampaignWebResponse<>(resultCodeWebEnum.getCode(), getCustomErrorMsg(campaignIssueResponse, resultCodeWebEnum));
        }

        CampaignIssueResult campaignIssueResult = campaignIssueResponse.getResult();
        CampaignIssueWebResult result = new CampaignIssueWebResult();

        result.setSuccessCount(campaignIssueResult.getIssueSuccessQuantity());
        dot4IssueDetail(campaignIssueResult.getDetailList());
        result.setDetailList(buildDetail(request, campaignIssueResult.getDetailList()));

        if (result.getSuccessCount() > 0) {
            return new CampaignWebResponse<>(CampaignResultCodeWebEnum.SUCCESS, result);
        } else if (CollectionUtils.isNotEmpty(campaignIssueResult.getDetailList())) {
            CampaignIssueDetail detail = campaignIssueResult.getDetailList().get(0);
            if (detail != null) {
                CampaignResultCodeWebEnum campaignResultCodeWebEnum = map2IssueWebResultCode(detail.getResultCode());
                return new CampaignWebResponse<>(campaignResultCodeWebEnum.getCode(), getCustomErrorMsg(campaignIssueResponse, campaignResultCodeWebEnum), result);
            }
        }

        return new CampaignWebResponse<>(CampaignResultCodeWebEnum.ISSUE_FAIL.getCode(), getCustomErrorMsg(campaignIssueResponse, CampaignResultCodeWebEnum.ISSUE_FAIL), result);
    }

    private String getCustomErrorMsg(CampaignIssueResponse campaignIssueResponse, CampaignResultCodeWebEnum resultCodeWebEnum) {
        String errorMessage = null;
        if (campaignIssueResponse != null && StringUtils.isNotBlank(campaignIssueResponse.getResultMsg())) {
            if (resultCodeWebEnum == CampaignResultCodeWebEnum.RISK_CONTROL) {
                errorMessage = campaignIssueResponse.getResultMsg();
            }
        }

        if (StringUtils.isBlank(errorMessage)) {
            errorMessage = resultCodeWebEnum.getMessage();
        }

        return errorMessage;
    }

    private void dot4IssueDetail(List<CampaignIssueDetail> detailList) {
        try{
            if (CollectionUtils.isEmpty(detailList)) {
                return;
            }

            for (CampaignIssueDetail detail : detailList) {
                Cat.logEvent("CampaignIssueDetail", detail.getCampaignKey() + "#" + detail.getResultCode());
            }
        } catch (Exception ignore) {}
    }

    private List<CampaignIssueDetailWebResult> buildDetail(CampaignIssueWebRequest request, List<CampaignIssueDetail> detailList) {
        List<CampaignIssueDetailWebResult> result = new ArrayList<>();

        if (CollectionUtils.isEmpty(detailList)) {
            return result;
        }

        Map<String, List<CampaignIssueDetail>> detailMap = detailList.stream().collect(Collectors.groupingBy(CampaignIssueDetail::getCampaignKey));

        for (Map.Entry<String, List<CampaignIssueDetail>> entry : detailMap.entrySet()) {
            CampaignIssueDetailWebResult campaignResult = new CampaignIssueDetailWebResult();
            campaignResult.setCampaignKey(entry.getKey());
            campaignResult.setCouponInfoList(new ArrayList<>());

            result.add(campaignResult);

            if (CollectionUtils.isEmpty(entry.getValue())) {
                continue;
            }

            int successCount = 0;
            for (CampaignIssueDetail detail : entry.getValue()) {
                CampaignResultCodeWebEnum campaignResultCodeWebEnum = map2IssueWebResultCode(detail.getResultCode());
                if (campaignResultCodeWebEnum == CampaignResultCodeWebEnum.SUCCESS) {
                    successCount++;
                } else {
                    continue;
                }
                CampaignIssueCouponWebInfo webInfo = new CampaignIssueCouponWebInfo();
                webInfo.setResultCode(campaignResultCodeWebEnum.getCode());
                webInfo.setResultMessage(campaignResultCodeWebEnum.getMessage());
                webInfo.setTitle(detail.getCouponGroupName());
                webInfo.setRedirectLink(buildRedirectLink(request.getIssuePlatform(), detail.getRedirectLink(), detail.getRedirectLinkDTO()));
                webInfo.setValueType(detail.getValueType());
                webInfo.setPriceLimit(detail.getPriceLimit());
                if (detail.getBeginTime() != null) {
                    webInfo.setBeginTime(detail.getBeginTime().toString());
                }
                if (detail.getEndTime() != null) {
                    webInfo.setEndTime(detail.getEndTime().toString());
                }
                webInfo.setDiscountAmount(detail.getDiscountAmount());
                webInfo.setDiscountCouponDTO(detail.getDiscountCouponDTO());
                webInfo.setFixedPriceCouponDTO(detail.getFixedPriceCouponDTO());
                webInfo.setStepPriceList(detail.getStepPriceList());

                campaignResult.getCouponInfoList().add(webInfo);
            }
            campaignResult.setSuccessCount(successCount);
        }

        return result;
    }

    private CampaignResultCodeWebEnum map2IssueWebResultCode(int campaignResultCode) {
        CampaignResultCodeWebEnum campaignResultCodeWebEnum = map2WebResultCode(campaignResultCode);
        if (campaignResultCodeWebEnum == null) {
            return CampaignResultCodeWebEnum.ISSUE_FAIL;
        }
        return campaignResultCodeWebEnum;
    }

    private CampaignResultCodeWebEnum map2ValidateWebResultCode(int campaignResultCode) {
        CampaignResultCodeWebEnum campaignResultCodeWebEnum = map2WebResultCode(campaignResultCode);
        if (campaignResultCodeWebEnum == null) {
            return CampaignResultCodeWebEnum.VALIDATE_FAIL;
        }
        return campaignResultCodeWebEnum;
    }


    private CampaignResultCodeWebEnum map2WebResultCode(int campaignResultCode) {
        if (campaignResultCode == CampaignResultCodeEnum.SUCCESS.getCode()) {
            return CampaignResultCodeWebEnum.SUCCESS;
        }


        if (campaignResultCode == CampaignResultCodeEnum.ISSUE_FAIL.getCode()) {
            return CampaignResultCodeWebEnum.ISSUE_FAIL;
        }

        if (campaignResultCode == CampaignResultCodeEnum.ILLEGAL_PARAM.getCode()) {
            return CampaignResultCodeWebEnum.ILLEGAL_PARAM;
        }

        if (campaignResultCode == CampaignResultCodeEnum.NOT_BEGIN.getCode()) {
            return CampaignResultCodeWebEnum.NOT_BEGIN;
        }

        if (campaignResultCode == CampaignResultCodeEnum.ALREADY_END.getCode()) {
            return CampaignResultCodeWebEnum.ALREADY_END;
        }

        if (campaignResultCode == CampaignResultCodeEnum.CAMPAIGN_RATE_LIMIT.getCode()) {
            return CampaignResultCodeWebEnum.RATE_LIMIT;
        }

        if (campaignResultCode == CampaignResultCodeEnum.RISK_CONTROL.getCode() ||
                campaignResultCode == CampaignResultCodeEnum.BLOCKED_BY_CREDIT.getCode()) {
            return CampaignResultCodeWebEnum.RISK_CONTROL;
        }

        if (campaignResultCode == CampaignResultCodeEnum.STOCK_NOT_ENOUGH.getCode() ||
                campaignResultCode == CampaignResultCodeEnum.DAILY_STOCK_NOT_ENOUGH.getCode() ||
                campaignResultCode == CampaignResultCodeEnum.HAS_BEEN_STOPPED.getCode() ||
                campaignResultCode == CampaignResultCodeEnum.COUPON_STOCK_NOT_ENOUGH.getCode() ||
                campaignResultCode == CampaignResultCodeEnum.COUPON_DAILY_STOCK_NOT_ENOUGH.getCode()) {
            return CampaignResultCodeWebEnum.STOCK_NOT_ENOUGH;
        }

        if (campaignResultCode == CampaignResultCodeEnum.NOT_MATCH_USER_RULE.getCode() ||
                campaignResultCode == CampaignResultCodeEnum.COUPON_NOT_MATCH_USER_RULE.getCode()) {
            return CampaignResultCodeWebEnum.NO_USER_QUALIFICATION;
        }

        if (campaignResultCode == CampaignResultCodeEnum.ISSUE_FREQ_LIMIT.getCode() ||
                campaignResultCode == CampaignResultCodeEnum.COUPON_USER_ISSUE_COUNT_OVERFLOW.getCode() ||
                campaignResultCode == CampaignResultCodeEnum.USER_ISSUE_COUNT_OVERFLOW.getCode() ||
                campaignResultCode == CampaignResultCodeEnum.USER_STOCK_NOT_ENOUGH.getCode()) {
            return CampaignResultCodeWebEnum.ISSUE_NUM_LIMIT;
        }

        return null;
    }


    private CampaignIssueRequest buildIssueReq(CampaignIssueWebRequest request, RestUserInfo restUserInfo, HttpContext context) {
        CampaignIssueRequest target = new CampaignIssueRequest();

        target.setUserId(restUserInfo.getUserId());
        target.setUserType(restUserInfo.getUserCode());
        target.setCampaignKeyList(request.getCampaignKeys());
        target.setIssueSource(ISSUE_SOURCE);

        if (StringUtils.isNotBlank(context.getDeviceId())) {
            target.setDeviceNo(context.getDeviceId());
        }

        target.setCreditContext(buildCreditContext(request, restUserInfo, context));
        target.setCampaignScene(request.getCampaignScene());
        target.setMmcParams(buildMmcParams(request, restUserInfo, context));

        return target;
    }

    private Map<String, String> buildCreditContext(CampaignIssueWebRequest request, RestUserInfo restUserInfo, HttpContext context) {
        Map<String, String> creditContext = new HashMap<>();

        if (StringUtils.isNotBlank(context.getUserAgent())) {
            creditContext.put("ua", context.getUserAgent());
        }

        if (StringUtils.isNotBlank(context.getIp())) {
            creditContext.put("ip", context.getIp());
        }

        if (StringUtils.isNotBlank(context.getReferer())) {
            creditContext.put("referUrl", context.getReferer());
        }

        if (StringUtils.isNotBlank(context.getMtgsig())) {
            creditContext.put("mtgsig", context.getMtgsig());
        }

        if (StringUtils.isNotBlank(request.getUuid())) {
            creditContext.put("uuid", request.getUuid());
        }

        if (request.getCreditInfo() != null) {
            CreditWebInfo creditInfo = request.getCreditInfo();
            if (StringUtils.isNotBlank(creditInfo.getEnv())) {
                creditContext.put("platformEnv", creditInfo.getEnv());
                creditContext.put("urlPlatformEnv", String.format("[%s]", creditInfo.getEnv()));
            }

            if (StringUtils.isNotBlank(creditInfo.getFingerprint())) {
                creditContext.put("fingerprint", creditInfo.getFingerprint());
            }

            if (StringUtils.isNotBlank(creditInfo.getH5Fingerprint())) {
                creditContext.put("h5_fingerprint", creditInfo.getH5Fingerprint());
            }

            if (StringUtils.isNotBlank(creditInfo.getWechatFingerprint())) {
                creditContext.put("wechatFingerprint", creditInfo.getWechatFingerprint());
            }

            if (StringUtils.isNotBlank(creditInfo.getWechatOpenId())) {
                creditContext.put("openid", creditInfo.getWechatOpenId());
            }

            if (StringUtils.isNotBlank(creditInfo.getWechatAppId())) {
                creditContext.put("appid", creditInfo.getWechatAppId());
            }

            if (StringUtils.isNotBlank(creditInfo.getAppVersion())) {
                creditContext.put("version", creditInfo.getAppVersion());
            }

            if (creditInfo.getPlatform() != null) {
                creditContext.put("platform", String.valueOf(creditInfo.getPlatform()));
                creditContext.put("platformOld", String.valueOf(creditInfo.getPlatform()));
            }

            if (creditInfo.getApp() != null) {
                creditContext.put("app", String.valueOf(creditInfo.getApp()));
            }

            if (creditInfo.getPartner() != null) {
                creditContext.put("partner", String.valueOf(creditInfo.getPartner()));
            }

            if (creditInfo.getLocation() != null) {
                creditContext.put("location", JsonUtils.toJson(creditInfo.getLocation()));
            }
        }

        if (StringUtils.isNotBlank(restUserInfo.getToken())) {
            creditContext.put("webToken", restUserInfo.getToken());
        }

        creditContext.put("couponsendtype", "1");
        creditContext.put("sendCouponType", "1");
        creditContext.put("userFormSourceid", "1");
        creditContext.put("userFormSourceName", "maker");

        // 神券会场组件所需特有的风控参数
        if (request.getCampaignScene() == CampaignSceneEnum.MAGICAL_MEMBER.getCode()) {
            creditContext.put("accessToken", "sendcoupon_202_1100");
            creditContext.put("versionNum", "2");
        }

        return creditContext;
    }


    public CampaignWebResponse<CampaignIssueValidateWebResult> render(CampaignRenderWebRequest request, HttpContext context, RestUserInfo restUserInfo) {
        if (needBlockByMmcWhiteList(request.getCampaignScene(), restUserInfo == null ? 0 : restUserInfo.getUserId(), request.getMmcParams())) {
            Cat.logEvent(CAMPAIGN_WARN, "mmcWhiteListBlock");
            return new CampaignWebResponse<>(CampaignResultCodeWebEnum.NOT_ONLINE.getCode(), CampaignResultCodeWebEnum.NOT_ONLINE.getMessage());
        }
        List<String> campaignKeyList = request.getCampaignKeys();

        List<CampaignIssueValidateDetailWebResult> detailWebResultList = new ArrayList<>();
        Map<String, CampaignIssueValidateDetailWebResult> detailWebResultMap = new HashMap<>();

        Map<String, BaseCampaignDTO> campaignDTOMap = campaignRepository.batchLoadCampaign(campaignKeyList);

        List<BaseCampaignDTO> campaignDTOList = new ArrayList<>(campaignDTOMap.values());

        if (CollectionUtils.isNotEmpty(campaignDTOList)) {
            campaignDTOList = campaignDTOList.stream().filter(dto -> dto.getStatus() == CampaignStatusEnum.ONLINE.getStatus()).collect(Collectors.toList());
        }

        if (CollectionUtils.isEmpty(campaignDTOList)) {
            Cat.logEvent(CAMPAIGN_WARN, "emptyCampaign");
            log.error("empty campaign list, req:{}", JsonUtils.toJson(request));
            return new CampaignWebResponse<>(CampaignResultCodeWebEnum.NOT_ONLINE);
        }

        List<String> needValidateList = campaignDTOList.stream().map(BaseCampaignDTO::getCampaignKey).collect(Collectors.toList());

        List<CampaignIssueValidateDetail> detailRpcList = new ArrayList<>();
        Set<String> issuedSet = new HashSet<>();

        Integer errorCode = CampaignResultCodeWebEnum.VALIDATE_FAIL.getCode();
        boolean exactQuery = false;
        try {
            CampaignIssueValidateRequest validateRequest = buildValidateReq(needValidateList, request, restUserInfo, context);
            PositionUtils.reportValidateParams(validateRequest, restUserInfo);
            CampaignIssueValidateResponse validateResponse = campaignIssueCouponService.preValidateIssueCoupons(validateRequest);

            if (validateResponse != null && validateResponse.isSuccess() && validateResponse.getResult() != null && CollectionUtils.isNotEmpty(validateResponse.getResult().getDetailList())) {
                detailRpcList = validateResponse.getResult().getDetailList();

                dot4ValidateDetail(detailRpcList);
                for (CampaignIssueValidateDetail detail : detailRpcList) {
                    if (detail.getResultCode() == CampaignResultCodeEnum.USER_ISSUE_COUNT_OVERFLOW.getCode() ||
                            detail.getResultCode() == CampaignResultCodeEnum.USER_STOCK_NOT_ENOUGH.getCode() ||
                            detail.getResultCode() == CampaignResultCodeEnum.COUPON_USER_ISSUE_COUNT_OVERFLOW.getCode() ||
                            detail.getResultCode() == CampaignResultCodeEnum.ISSUE_FREQ_LIMIT.getCode()) {
                        exactQuery = true;
                        issuedSet.add(detail.getCampaignKey());
                    } else if (detail.getResultCode() == CampaignResultCodeEnum.STOCK_NOT_ENOUGH.getCode() ||
                            detail.getResultCode() == CampaignResultCodeEnum.DAILY_STOCK_NOT_ENOUGH.getCode() ||
                            detail.getResultCode() == CampaignResultCodeEnum.COUPON_DAILY_STOCK_NOT_ENOUGH.getCode() ||
                            detail.getResultCode() == CampaignResultCodeEnum.COUPON_STOCK_NOT_ENOUGH.getCode()) {
                        issuedSet.add(detail.getCampaignKey());
                    }
                }
            } else {
                if (validateResponse != null && !validateResponse.isSuccess()) {
                    errorCode = validateResponse.getResultCode();
                }
            }
        } catch (TException e) {
            if (e.getMessage() !=null && e.getMessage().contains(ServiceDegradeException.class.getSimpleName())){
                errorCode = CampaignResultCodeEnum.CAMPAIGN_RATE_LIMIT.getCode();
            }
            log.error("campaign validate thrift exception, req:{}, context:{}", JsonUtils.toJson(request), JsonUtils.toJson(context), e);
        } catch (Exception e) {
            log.error("campaign validate fail, req:{}, context:{}", JsonUtils.toJson(request), JsonUtils.toJson(context), e);
        }

        //校验结果转换
        List<CampaignIssueValidateDetailWebResult> validateResult = buildValidateDetail(request, needValidateList, campaignDTOMap, detailRpcList, errorCode);
        if (CollectionUtils.isNotEmpty(validateResult)) {
            validateResult.forEach(detail -> detailWebResultMap.put(detail.getCampaignKey(), detail));
        }

        //如果不能领券, 查询已领取券
        if (CollectionUtils.isNotEmpty(issuedSet)) {
            List<CampaignIssueValidateDetailWebResult> issuedResult = processIssuedRecord(new ArrayList<>(issuedSet), request, restUserInfo, context, exactQuery);
            if (CollectionUtils.isNotEmpty(issuedResult)) {
                for (CampaignIssueValidateDetailWebResult result : issuedResult) {
                    detailWebResultMap.put(result.getCampaignKey(), result);
                }
            }
        }

        for (String campaignKey : needValidateList) {
            CampaignIssueValidateDetailWebResult validateDetailWebResult = detailWebResultMap.get(campaignKey);
            if (validateDetailWebResult == null) {
                continue;
            }
            detailWebResultList.add(validateDetailWebResult);
        }

        if (request.getCampaignScene() == CampaignSceneEnum.MAGICAL_MEMBER.getCode()) {
            detailWebResultList = filterRenderWebResultList(detailWebResultList);
        }

        CampaignIssueValidateWebResult result = new CampaignIssueValidateWebResult();
        result.setDetailList(detailWebResultList);
        result.setCurrentTimeMillis(String.valueOf(System.currentTimeMillis()));
        return new CampaignWebResponse<>(CampaignResultCodeWebEnum.SUCCESS, result);
    }

    public CampaignWebResponse<CampaignInflateCouponWebResult> queryInflate(CampaignQueryInflateRequest request, HttpContext context) {

        // 膨后券查询
        try {
            UnifiedCouponQueryInflateRequest queryInflateRequest = buildInflateQueryReq(request, context);
            UnifiedCouponManageResponse<CouponQueryInflateResponse> queryInflateResponse = unifiedCouponListService.queryInflateByUnifiedCouponId(queryInflateRequest);

            if (queryInflateResponse == null || !queryInflateResponse.isSuccess() || queryInflateResponse.getResult() == null) {
                log.error("queryInflateByUnifiedCouponId error, queryInflateRequest:{}, queryInflateResponse:{}", queryInflateRequest, queryInflateResponse);
                return new CampaignWebResponse<>(CampaignResultCodeWebEnum.QUERY_INFLATE_FAIL);
            }
            CouponQueryInflateResponse inflateResponse = queryInflateResponse.getResult();
            CampaignInflateCouponWebResult result = new CampaignInflateCouponWebResult();
            result.setUnifiedCouponId(request.getUnifiedCouponId());
            result.setCurrentTimeMillis(String.valueOf(System.currentTimeMillis()));
            if (MapUtils.isNotEmpty(inflateResponse.getInlfateInfoMap())
                    && inflateResponse.getInlfateInfoMap().containsKey(request.getUnifiedCouponId())) {
                result.setInflateInfoList(buildCampaignInflateCoupons(request, inflateResponse.getInlfateInfoMap().get(request.getUnifiedCouponId())));
            }

            return new CampaignWebResponse<>(CampaignResultCodeWebEnum.SUCCESS, result);

        } catch (Exception e) {
            log.error("queryInflateByUnifiedCouponId exception, request:{}", request, e);
        }

        return new CampaignWebResponse<>(CampaignResultCodeWebEnum.QUERY_INFLATE_FAIL);
    }

    /**
     * 是否神券会场组件，且未命中白名单，需要阻塞返回
     * @return
     */
    private boolean needBlockByMmcWhiteList(int campaignScene, long userId, Map<String, String> mmcParams) {
        if (campaignScene != CampaignSceneEnum.MAGICAL_MEMBER.getCode()) {
            return false;
        }
        if (!MMC_CAMPAIGN_WHITE_LIST_BLOCK_SWITCH) {
            return false;
        }
        String locationCityIdStr = MapUtils.getString(mmcParams, "locationCityId");
        long locationCityId = NumberUtils.toLong(locationCityIdStr);
        GrayControlReq grayControlReq = new GrayControlReq();
        grayControlReq.setGrayVersion("1.5");
        grayControlReq.setMtUserId(userId);
        grayControlReq.setLocationCityId(locationCityId);
        MagicControlResult result = MagicGrayUtil.grayControl(grayControlReq);
        return result == null || !result.isPass();
    }

    private void dot4ValidateDetail(List<CampaignIssueValidateDetail> detailRpcList) {
        try{
            if (CollectionUtils.isEmpty(detailRpcList)) {
                return;
            }
            for (CampaignIssueValidateDetail detail : detailRpcList) {
                Cat.logEvent("CampaignRenderDetail", detail.getCampaignKey() + "#" + detail.getResultCode());
            }
        } catch (Exception ignore) {}
    }

    private List<CampaignIssueValidateDetailWebResult> processIssuedRecord(List<String> campaignKeyList, CampaignRenderWebRequest request,
                                                                           RestUserInfo restUserInfo, HttpContext context, boolean exactQuery) {
        if (restUserInfo == null || restUserInfo.getUserId() <= 0) {
            return new ArrayList<>(0);
        }

        try {
            CampaignRecordQueryRequest queryRequest = buildQueryReq(campaignKeyList, request, restUserInfo, context, exactQuery);
            CampaignRecordResponse campaignRecordResponse = campaignIssueCouponService.queryCampaignRecord(queryRequest);
            if (campaignRecordResponse != null && campaignRecordResponse.isSuccess() && campaignRecordResponse.getResult() != null) {
                List<CampaignRecordDetail> recordDetailList = campaignRecordResponse.getResult().getRecordDetailList();
                if (CollectionUtils.isNotEmpty(recordDetailList)) {
                    if (request.getCampaignScene() == CampaignSceneEnum.MAGICAL_MEMBER.getCode()) {
                        return processRecordDetailMergeCampaign(request, recordDetailList);
                    }
                    return processRecordDetail(request, recordDetailList);
                }
            }
        } catch (Exception e) {
            log.error("query issued record exm req:{}, userInfo:{}", JsonUtils.toJson(request), JsonUtils.toJson(restUserInfo), e);
        }
        return new ArrayList<>(0);
    }

    private List<CampaignIssueValidateDetailWebResult> processRecordDetail(CampaignRenderWebRequest request, List<CampaignRecordDetail> recordDetailList) {
        List<CampaignIssueValidateDetailWebResult> resultList = new ArrayList<>();
        Map<String, List<CampaignRecordDetail>> recordMap = recordDetailList.stream().collect(Collectors.groupingBy(CampaignRecordDetail::getCampaignKey));

        for (Map.Entry<String, List<CampaignRecordDetail>> entry : recordMap.entrySet()) {
            if (CollectionUtils.isEmpty(entry.getValue())) {
                continue;
            }
            CampaignIssueValidateDetailWebResult result = new CampaignIssueValidateDetailWebResult();
            resultList.add(result);
            result.setCampaignKey(entry.getKey());
            result.setCouponInfoList(new ArrayList<>());

            for (CampaignRecordCouponDetail detail : entry.getValue().get(0).getCouponDetailList()) {
                CampaignIssueValidateCouponWebInfo webInfo = new CampaignIssueValidateCouponWebInfo();

                webInfo.setTitle(detail.getCouponGroupName());
                webInfo.setDiscountAmount(detail.getDiscountAmount());
                webInfo.setPriceLimit(detail.getPriceLimit());
                webInfo.setRedirectLink(buildRedirectLink(request.getIssuePlatform(), detail.getRedirectLink(), detail.getRedirectLinkDTO()));
                webInfo.setValueType(detail.getValueType());
                webInfo.setDiscountCouponDTO(detail.getDiscountCouponDTO());
                webInfo.setStepPriceList(detail.getStepPriceList());
                webInfo.setFixedPriceCouponDTO(detail.getFixedPriceCouponDTO());
                webInfo.setValidateCode(CampaignResultCodeEnum.SUCCESS.getCode());
                webInfo.setHasCoupon(true);
                if (detail.getBeginTime() != null) {
                    webInfo.setBeginTime(String.valueOf(detail.getBeginTime()));
                }
                if (detail.getEndTime() != null) {
                    webInfo.setEndTime(String.valueOf(detail.getEndTime()));
                }
                webInfo.setCouponStatus(map2Status(detail));
                webInfo.setCanInflate(detail.isCanInflate());
                if (detail.getInflateStatus() != null && detail.getInflateStatus() == InflateStatusEnum.AFTER.getStatus()) {
                    webInfo.setAfterInflate(true);
                }
                webInfo.setUnifiedCouponId(detail.getUnifiedCouponId());
                webInfo.setCouponGroupId(String.valueOf(detail.getCouponGroupId()));
                webInfo.setAssetType(CampaignInflateUtils.NIB_ASSET_TYPE);
                webInfo.setCouponInflateType(CampaignInflateUtils.getCouponInflateType(
                        webInfo.isCanInflate(), webInfo.isAfterInflate()));
                webInfo.setBizToken(CampaignInflateUtils.buildBizToken(detail));

                result.getCouponInfoList().add(webInfo);
            }
        }

        return resultList;
    }

    private List<CampaignIssueValidateDetailWebResult> processRecordDetailMergeCampaign(CampaignRenderWebRequest request, List<CampaignRecordDetail> recordDetailList) {
        List<CampaignIssueValidateDetailWebResult> webResults = new ArrayList<>();

        for (CampaignRecordDetail record : recordDetailList) {
            if (CollectionUtils.isEmpty(record.getCouponDetailList())) {
                continue;
            }

            CampaignIssueValidateDetailWebResult result = new CampaignIssueValidateDetailWebResult();
            webResults.add(result);
            result.setCampaignKey(record.getCampaignKey());
            result.setCouponInfoList(new ArrayList<>());

            for (CampaignRecordCouponDetail detail : record.getCouponDetailList()) {
                CampaignIssueValidateCouponWebInfo webInfo = new CampaignIssueValidateCouponWebInfo();

                webInfo.setTitle(detail.getCouponGroupName());
                webInfo.setDiscountAmount(detail.getDiscountAmount());
                webInfo.setPriceLimit(detail.getPriceLimit());
                webInfo.setRedirectLink(buildRedirectLink(request.getIssuePlatform(), detail.getRedirectLink(), detail.getRedirectLinkDTO()));
                webInfo.setValueType(detail.getValueType());
                webInfo.setDiscountCouponDTO(detail.getDiscountCouponDTO());
                webInfo.setStepPriceList(detail.getStepPriceList());
                webInfo.setFixedPriceCouponDTO(detail.getFixedPriceCouponDTO());
                webInfo.setValidateCode(CampaignResultCodeEnum.SUCCESS.getCode());
                webInfo.setHasCoupon(true);
                if (detail.getBeginTime() != null) {
                    webInfo.setBeginTime(String.valueOf(detail.getBeginTime()));
                }
                if (detail.getEndTime() != null) {
                    webInfo.setEndTime(String.valueOf(detail.getEndTime()));
                }
                webInfo.setCouponStatus(map2Status(detail));
                webInfo.setCanInflate(detail.isCanInflate());
                if (detail.getInflateStatus() != null && detail.getInflateStatus() == InflateStatusEnum.AFTER.getStatus()) {
                    webInfo.setAfterInflate(true);
                }
                webInfo.setUnifiedCouponId(detail.getUnifiedCouponId());
                webInfo.setCouponGroupId(String.valueOf(detail.getCouponGroupId()));
                webInfo.setAssetType(CampaignInflateUtils.NIB_ASSET_TYPE);
                webInfo.setCouponInflateType(CampaignInflateUtils.getCouponInflateType(
                        webInfo.isCanInflate(), webInfo.isAfterInflate()));
                webInfo.setBizToken(CampaignInflateUtils.buildBizToken(detail));

                result.getCouponInfoList().add(webInfo);
            }

        }

        // 合并同一活动的不同领券记录
        List<CampaignIssueValidateDetailWebResult> mergeResult = Lists.newArrayList();
        if (CollectionUtils.isEmpty(webResults)) {
            return webResults;
        }
        Map<String, List<CampaignIssueValidateCouponWebInfo>> campaignWebInfoMap = Maps.newHashMap();
        for (CampaignIssueValidateDetailWebResult webResult : webResults) {
            if (campaignWebInfoMap.containsKey(webResult.getCampaignKey())) {
                if (CollectionUtils.isNotEmpty(webResult.getCouponInfoList())) {
                    campaignWebInfoMap.get(webResult.getCampaignKey()).addAll(webResult.getCouponInfoList());
                }
            } else if (CollectionUtils.isNotEmpty(webResult.getCouponInfoList())) {
                campaignWebInfoMap.put(webResult.getCampaignKey(), webResult.getCouponInfoList());
            }
        }
        for (Map.Entry<String, List<CampaignIssueValidateCouponWebInfo>> entry : campaignWebInfoMap.entrySet()) {
            CampaignIssueValidateDetailWebResult webResult = new CampaignIssueValidateDetailWebResult();
            webResult.setCampaignKey(entry.getKey());
            webResult.setCouponInfoList(entry.getValue());
            mergeResult.add(webResult);
        }
        return mergeResult;
    }


    private CampaignRecordQueryRequest buildQueryReq(List<String> campaignKeyList, CampaignRenderWebRequest request,
                                                     RestUserInfo restUserInfo, HttpContext context, boolean exactQuery) {
        CampaignRecordQueryRequest target = new CampaignRecordQueryRequest();
        target.setUserType(restUserInfo.getUserCode());
        target.setUserId(restUserInfo.getUserId());
        target.setCampaignKeyList(campaignKeyList);
        target.setExactQuery(exactQuery);
        target.setIssueSource(ISSUE_SOURCE);
        if (request.getSessionBeginTime() != null) {
            target.setBeginTime(request.getSessionBeginTime());
        }
        if (request.getSessionEndTime() != null) {
            target.setEndTime(request.getSessionEndTime());
        }
        if (request.getCampaignScene() > 0) {
            target.setCampaignScene(request.getCampaignScene());
        }
        if (MapUtils.isNotEmpty(request.getCampaignBizMap())) {
            target.setCampaignBizMap(request.getCampaignBizMap());
        }
        if (MapUtils.isNotEmpty(request.getMmcParams())) {
            target.setMmcParams(buildMmcParams(request, restUserInfo, context));
        }

        return target;
    }

    private UnifiedCouponQueryInflateRequest buildInflateQueryReq(CampaignQueryInflateRequest request, HttpContext context) {
        UnifiedCouponQueryInflateRequest queryInflateRequest = new UnifiedCouponQueryInflateRequest();
        if (request == null || context == null || context.getUserInfo() == null) {
            return queryInflateRequest;
        }
        queryInflateRequest.setMtRealUserId(context.getUserInfo().getUserId());
        List<String> unifiedCouponIdList = Lists.newArrayList();
        if (StringUtils.isNotBlank(request.getUnifiedCouponId())) {
            unifiedCouponIdList.add(request.getUnifiedCouponId());
        }
        queryInflateRequest.setUnifiedCouponIdList(unifiedCouponIdList);
        return queryInflateRequest;
    }

    private List<CampaignIssueValidateDetailWebResult> buildValidateDetail(CampaignRenderWebRequest request, List<String> needValidateList, Map<String, BaseCampaignDTO> campaignDTOMap, List<CampaignIssueValidateDetail> detailList, Integer errorCode) {
        List<CampaignIssueValidateDetailWebResult> result = new ArrayList<>();

        if (CollectionUtils.isEmpty(needValidateList)) {
            return result;
        }

        Set<Long> couponGroupIdSet = new HashSet<>();
        for (BaseCampaignDTO campaignDTO : campaignDTOMap.values()) {
            couponGroupIdSet.addAll(campaignDTO.getCouponGroupInfos().stream().map(CampaignCGInfoDTO::getCouponGroupId).collect(Collectors.toList()));
        }

        Map<Long, BaseCouponGroupDTO> couponGroupDTOMap = couponGroupRepository.batchLoadCouponGroupById(new ArrayList<>(couponGroupIdSet));

        Map<String, List<CampaignIssueValidateDetail>> detailMap = detailList.stream().collect(Collectors.groupingBy(CampaignIssueValidateDetail::getCampaignKey));

        for (String campaignKey : needValidateList) {
            BaseCampaignDTO campaignDTO = campaignDTOMap.get(campaignKey);
            if (campaignDTO == null) {
                Cat.logEvent(CAMPAIGN_WARN, "keyNotExist");
                log.warn("campaign key not exist, key:{}", campaignKey);
                continue;
            }

            List<Long> couponGroupIds = campaignDTO.getCouponGroupInfos().stream().map(CampaignCGInfoDTO::getCouponGroupId).collect(Collectors.toList());
            result.add(buildValidateDetailSingle(campaignKey, couponGroupIds, request, couponGroupDTOMap, detailMap.get(campaignKey), errorCode));
        }

        return result;
    }

    private CampaignIssueValidateDetailWebResult buildValidateDetailSingle(String campaignKey, List<Long> couponGroupIds, CampaignRenderWebRequest request, Map<Long, BaseCouponGroupDTO> couponGroupDTOMap, List<CampaignIssueValidateDetail> detailList, Integer errorCode) {
        CampaignIssueValidateDetailWebResult result = new CampaignIssueValidateDetailWebResult();
        result.setCampaignKey(campaignKey);

        List<CampaignIssueValidateCouponWebInfo> couponInfoList = new ArrayList<>();
        result.setCouponInfoList(couponInfoList);

        Map<Long, List<CampaignIssueValidateDetail>> detailMap = new HashMap<>();

        if (CollectionUtils.isNotEmpty(detailList)) {
            detailMap = detailList.stream().collect(Collectors.groupingBy(CampaignIssueValidateDetail::getCouponGroupId));
        }

        for (Long couponGroupId : couponGroupIds) {
            BaseCouponGroupDTO couponGroupDTO = couponGroupDTOMap.get(couponGroupId);
            if (couponGroupDTO == null) {
                continue;
            }

            List<CampaignIssueValidateDetail> couponGroupDetailList = detailMap.get(couponGroupId);
            if (CollectionUtils.isEmpty(couponGroupDetailList)) {
                couponInfoList.add(buildCouponInfo(request, couponGroupDTO, errorCode));
            } else {
                couponInfoList.add(buildCouponInfo(request, couponGroupDetailList.get(0)));
                couponGroupDetailList.remove(0);
            }
        }
        return result;
    }

    private CampaignIssueValidateCouponWebInfo buildCouponInfo(CampaignRenderWebRequest request, CampaignIssueValidateDetail detail) {
        CampaignIssueValidateCouponWebInfo webInfo = new CampaignIssueValidateCouponWebInfo();
        CampaignResultCodeWebEnum campaignResultCodeWebEnum = map2ValidateWebResultCode(detail.getResultCode());
        webInfo.setValidateCode(campaignResultCodeWebEnum.getCode());
        webInfo.setTitle(detail.getCouponGroupName());
        webInfo.setRedirectLink(buildRedirectLink(request.getIssuePlatform(), detail.getRedirectLink(), detail.getRedirectLinkDTO()));
        webInfo.setValueType(detail.getValueType());
        webInfo.setPriceLimit(detail.getPriceLimit());
        webInfo.setDiscountAmount(detail.getDiscountAmount());
        webInfo.setDiscountCouponDTO(detail.getDiscountCouponDTO());
        webInfo.setFixedPriceCouponDTO(detail.getFixedPriceCouponDTO());
        webInfo.setStepPriceList(detail.getStepPriceList());
        if (detail.getBeginTime() != null) {
            webInfo.setBeginTime(String.valueOf(detail.getBeginTime()));
        }
        if (detail.getEndTime() != null) {
            webInfo.setEndTime(String.valueOf(detail.getEndTime()));
        }
        webInfo.setCanInflate(detail.isCanInflate());
        if (detail.getInflateStatus() != null && detail.getInflateStatus() == InflateStatusEnum.AFTER.getStatus()) {
            webInfo.setAfterInflate(true);
        }
        webInfo.setAssetType(CampaignInflateUtils.NIB_ASSET_TYPE);
        webInfo.setCouponInflateType(CampaignInflateUtils.getCouponInflateType(webInfo.isCanInflate(), webInfo.isAfterInflate()));

        return webInfo;
    }

    private CampaignIssueValidateCouponWebInfo buildCouponInfo(CampaignRenderWebRequest request, BaseCouponGroupDTO couponGroupDTO, Integer errorCode) {
        CampaignIssueValidateCouponWebInfo webInfo = new CampaignIssueValidateCouponWebInfo();
        webInfo.setValidateCode(map2ValidateWebResultCode(errorCode).getCode());
        webInfo.setTitle(couponGroupDTO.getCouponGroupName());
        webInfo.setRedirectLink(buildRedirectLink(request.getIssuePlatform(), couponGroupDTO.getRedirectLink(), getRedirectDTO(couponGroupDTO)));
        webInfo.setValueType(couponGroupDTO.getValueType());
        if (couponGroupDTO.getPriceLimit() != null) {
            webInfo.setPriceLimit(couponGroupDTO.getPriceLimit().stripTrailingZeros().toPlainString());
        }
        if (couponGroupDTO.getDiscountAmount() != null) {
            webInfo.setDiscountAmount(couponGroupDTO.getDiscountAmount().stripTrailingZeros().toPlainString());
        }
        webInfo.setDiscountCouponDTO(MapperUtils.map(couponGroupDTO.getDiscountCouponDTO()));
        webInfo.setFixedPriceCouponDTO(MapperUtils.map(couponGroupDTO.getFixedPriceCouponDTO()));
        webInfo.setStepPriceList(MapperUtils.mapStepPrice(couponGroupDTO.getStepPriceList()));

        return webInfo;
    }

    private RedirectLinkDTO getRedirectDTO(BaseCouponGroupDTO couponGroupDTO) {
        String redirectLinkStr = MapUtils.getString(couponGroupDTO.getExtraInfoMap(), CouponGroupExtraKeyEnum.redirectLink.name());

        if (StringUtils.isNotEmpty(redirectLinkStr)) {
            return JsonUtils.toObject(redirectLinkStr, RedirectLinkDTO.class);
        }

        return null;
    }

    private CampaignIssueValidateRequest buildValidateReq(List<String> needValidateList, CampaignRenderWebRequest request, RestUserInfo restUserInfo, HttpContext context) {
        CampaignIssueValidateRequest target = new CampaignIssueValidateRequest();

        if (restUserInfo != null && restUserInfo.getUserId() >= 0) {
            target.setUserId(restUserInfo.getUserId());
        }

        target.setUserType(request.getUserInfo().getUserCode());
        target.setCampaignKeyList(needValidateList);
        target.setIssueSource(ISSUE_SOURCE);

        if (StringUtils.isNotBlank(context.getDeviceId())) {
            target.setDeviceNo(context.getDeviceId());
        }
        target.setCampaignScene(request.getCampaignScene());

        target.setMmcParams(buildMmcParams(request, restUserInfo, context));

        return target;
    }

    private Map<String, String> buildMmcParams(CampaignRenderWebRequest request, RestUserInfo restUserInfo, HttpContext context) {
        Map<String, String> mmcParams = buildMmcParams(request.getMmcParams());
        if (StringUtils.isNotEmpty(request.getUuid())) {
            mmcParams.put("uuid", request.getUuid());
        }
        if (restUserInfo != null && StringUtils.isNotBlank(restUserInfo.getPhone())) {
            mmcParams.put("phone", restUserInfo.getPhone());
        }

        if (StringUtils.isNotBlank(request.getPageId())) {
            mmcParams.put("pageId", request.getPageId());
        }

        if (context != null && StringUtils.isNotBlank(context.getIp())) {
            mmcParams.put("ip", context.getIp());
        }
        return mmcParams;
    }

    private Map<String, String> buildMmcParams(CampaignIssueWebRequest request, RestUserInfo restUserInfo, HttpContext context) {
        Map<String, String> mmcParams = buildMmcParams(request.getMmcParams());
        if (StringUtils.isNotEmpty(request.getUuid())) {
            mmcParams.put("uuid", request.getUuid());
        }
        if (restUserInfo != null && StringUtils.isNotBlank(restUserInfo.getPhone())) {
            mmcParams.put("phone", restUserInfo.getPhone());
        }

        if (StringUtils.isNotBlank(request.getPageId())) {
            mmcParams.put("pageId", request.getPageId());
        }

        if (context != null && StringUtils.isNotBlank(context.getIp())) {
            mmcParams.put("ip", context.getIp());
        }
        return mmcParams;
    }

    private Map<String, String> buildMmcParams(Map<String, String> mmcParams) {
        Map<String, String> result = Maps.newHashMap();
        if (MapUtils.isEmpty(mmcParams)) {
            return result;
        }
        // 膨胀查询相关参数过滤，过滤原因：thrift协议不允许map值为null，否则会报npe
        for (Map.Entry<String, String> entry : mmcParams.entrySet()) {
            if (entry == null || entry.getKey() == null || entry.getValue() == null) {
                continue;
            }
            result.put(entry.getKey(), entry.getValue());
        }

        // 处理定位经纬度，上游传递的经纬度是原始数据，需要乘以10的6次方取long类型数据
        String actualLatitudeStr = result.get("actualLatitude");
        if (StringUtils.isNotBlank(actualLatitudeStr)) {
            long actualLatitude = new BigDecimal(actualLatitudeStr).scaleByPowerOfTen(6).longValue();
            if (actualLatitude > 0) {
                result.put("actualLatitude", String.valueOf(actualLatitude));
            }
        }
        String actualLongitudeStr = result.get("actualLongitude");
        if (StringUtils.isNotBlank(actualLongitudeStr)) {
            long actualLongitude = new BigDecimal(actualLongitudeStr).scaleByPowerOfTen(6).longValue();
            if (actualLongitude > 0) {
                result.put("actualLongitude", String.valueOf(actualLongitude));
            }
        }

        return result;
    }

    private int map2Status(CampaignRecordCouponDetail detail) {
        if (detail.getEndTime() < System.currentTimeMillis()) {
            return CampaignCouponStatusWebEnum.EXPIRED.getCode();
        }

        if (detail.getUsedStatus() == 0) {
            return CampaignCouponStatusWebEnum.UNUSED.getCode();
        } else {
            return CampaignCouponStatusWebEnum.USED.getCode();
        }
    }

    private int map2Status(UnifiedCouponDTO unifiedCouponDTO) {
        if (unifiedCouponDTO.getEndTime() != null && unifiedCouponDTO.getEndTime().getTime() < System.currentTimeMillis()) {
            return CampaignCouponStatusWebEnum.EXPIRED.getCode();
        }

        if (unifiedCouponDTO.isUsed()) {
            return CampaignCouponStatusWebEnum.USED.getCode();
        } else {
            return CampaignCouponStatusWebEnum.UNUSED.getCode();
        }
    }

    private String buildRedirectLink(Integer issuePlatform, String redirectLink, RedirectLinkDTO redirectLinkDTO) {
        if (issuePlatform == null) {
            return null;
        }

        MtCouponPlatform byCode = MtCouponPlatform.getByCode(issuePlatform);

        switch (byCode) {
            case APP:
                if (StringUtils.isNotBlank(redirectLink)) {
                    return redirectLink;
                }
                if (redirectLinkDTO != null && StringUtils.isNotBlank(redirectLinkDTO.getMtAppLink())) {
                    return redirectLinkDTO.getMtAppLink();
                }
                break;
            case DP_APP:
                if (redirectLinkDTO != null && StringUtils.isNotBlank(redirectLinkDTO.getDpAppLink())) {
                    return redirectLinkDTO.getDpAppLink();
                }
                break;

            case WEIXIN_CX:
                if (redirectLinkDTO != null && StringUtils.isNotBlank(redirectLinkDTO.getMtWxMiniLink())) {
                    return redirectLinkDTO.getMtWxMiniLink();
                }
                break;

            case DP_WX_APPLET:
                if (redirectLinkDTO != null && StringUtils.isNotBlank(redirectLinkDTO.getDpWxMiniLink())) {
                    return redirectLinkDTO.getDpWxMiniLink();
                }
                break;

            case WEB_PAGE:
            case I_TOUCH:
            case DP_ITOUCH:
                return redirectLink;
        }
        return null;
    }

    private List<CampaignInflateCouponWebInfo> buildCampaignInflateCoupons(CampaignQueryInflateRequest request, List<UnifiedCouponDTO> unifiedCouponDTOs) {
        List<CampaignInflateCouponWebInfo> result = Lists.newArrayList();
        if (CollectionUtils.isEmpty(unifiedCouponDTOs)) {
            return result;
        }
        for (UnifiedCouponDTO unifiedCouponDTO : unifiedCouponDTOs) {
            if (unifiedCouponDTO == null || unifiedCouponDTO.getCouponGroupDTO() == null) {
                continue;
            }
            UnifiedCouponGroupDTO couponGroupDTO = unifiedCouponDTO.getCouponGroupDTO();
            CampaignInflateCouponWebInfo couponWebInfo = new CampaignInflateCouponWebInfo();
            couponWebInfo.setHasCoupon(true);
            couponWebInfo.setCouponStatus(map2Status(unifiedCouponDTO));
            couponWebInfo.setTitle(couponGroupDTO.getCouponGroupName());
            couponWebInfo.setValueType(couponGroupDTO.getValueType());
            couponWebInfo.setRedirectLink(buildRedirectLink(request.getIssuePlatform(),
                    couponGroupDTO.getRedirectLink(), getRedirectDTO(couponGroupDTO)));
            if (unifiedCouponDTO.getBeginTime() != null) {
                couponWebInfo.setBeginTime(String.valueOf(unifiedCouponDTO.getBeginTime().getTime()));
            }
            if (unifiedCouponDTO.getEndTime() != null) {
                couponWebInfo.setEndTime(String.valueOf(unifiedCouponDTO.getEndTime().getTime()));
            }
            // 目前仅支持满减券和动态满减券
            if (couponGroupDTO.getPriceLimit() != null) {
                couponWebInfo.setPriceLimit(couponGroupDTO.getPriceLimit().stripTrailingZeros().toPlainString());
            }
            if (couponGroupDTO.getDiscountAmount() != null) {
                couponWebInfo.setDiscountAmount(couponGroupDTO.getDiscountAmount().stripTrailingZeros().toPlainString());
            }
            couponWebInfo.setCanInflate(false);
            couponWebInfo.setAfterInflate(true);
            couponWebInfo.setCouponInflateType(CampaignInflateUtils.getCouponInflateType(
                    couponWebInfo.isCanInflate(), couponWebInfo.isAfterInflate()));
            couponWebInfo.setBizToken(CampaignInflateUtils.buildBizToken(unifiedCouponDTO, couponWebInfo.isCanInflate()));
            couponWebInfo.setUnifiedCouponId(unifiedCouponDTO.getUnifiedCouponId());
            couponWebInfo.setCouponGroupId(unifiedCouponDTO.getCouponGroupDTO().getUnifiedCouponGroupId());
            couponWebInfo.setAssetType(2);
            result.add(couponWebInfo);
        }
        return result;
    }

    private static RedirectLinkDTO getRedirectDTO(UnifiedCouponGroupDTO couponGroupDTO) {
        if (couponGroupDTO == null) {
            return null;
        }

        String redirectLinkStr = MapUtils.getString(couponGroupDTO.getExtraInfoMap(), CouponGroupExtraKeyEnum.redirectLink.name());

        if (StringUtils.isNotEmpty(redirectLinkStr)) {
            return JsonUtils.toObject(redirectLinkStr, RedirectLinkDTO.class);
        }

        return null;
    }

    private List<CampaignIssueValidateDetailWebResult> filterRenderWebResultList(List<CampaignIssueValidateDetailWebResult> webResults) {
        List<CampaignIssueValidateDetailWebResult> resultList = Lists.newArrayList();

        for (CampaignIssueValidateDetailWebResult webResult : webResults) {
            if (webResult == null || CollectionUtils.isEmpty(webResult.getCouponInfoList())) {
                continue;
            }
            List<CampaignIssueValidateCouponWebInfo> filterWebInfos = Lists.newArrayList();
            for (CampaignIssueValidateCouponWebInfo webInfo : webResult.getCouponInfoList()) {
                if (webInfo == null) {
                    continue;
                }
                // 过滤已结束活动
                if (webInfo.getValidateCode() != null && webInfo.getValidateCode().equals(CampaignResultCodeWebEnum.ALREADY_END.getCode())) {
                    continue;
                }
                // 过滤已过期优惠券
                if (webInfo.isHasCoupon() && StringUtils.isNotBlank(webInfo.getEndTime())) {
                    long endTime = NumberUtils.toLong(webInfo.getEndTime());
                    if (endTime < System.currentTimeMillis()) {
                        continue;
                    }
                }
                filterWebInfos.add(webInfo);
            }
            if (CollectionUtils.isNotEmpty(filterWebInfos)) {
                webResult.setCouponInfoList(filterWebInfos);
                resultList.add(webResult);
            }
        }
        return resultList;
    }
}
