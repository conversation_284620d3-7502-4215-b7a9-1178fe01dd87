package com.dianping.api.campaign.biz;


import com.dianping.api.campaign.utils.CampaignRefreshLimiterUtil;
import com.dianping.api.campaign.utils.DeepCopyUtils;
import com.dianping.api.campaign.utils.ThreadPoolUtils;
import com.dianping.cat.Cat;
import com.dianping.cat.util.MetricHelper;
import com.dianping.gmkt.coupon.common.api.constants.CommonLionConstants;
import com.dianping.lion.client.Lion;
import com.dianping.pay.api.util.JsonUtils;
import com.github.benmanes.caffeine.cache.Cache;
import com.github.benmanes.caffeine.cache.Caffeine;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.sankuai.mpmkt.coupon.group.query.api.CampaignQueryService;
import com.sankuai.mpmkt.coupon.group.query.api.dto.BaseCampaignDTO;
import com.sankuai.mpmkt.coupon.group.query.api.dto.BaseCouponGroupDTO;
import com.sankuai.mpmkt.coupon.group.query.api.request.CampaignBatchQueryRequest;
import com.sankuai.mpmkt.coupon.group.query.api.response.CampaignBatchQueryResponse;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.Validate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import javax.annotation.PreDestroy;
import java.util.*;
import java.util.concurrent.ScheduledFuture;
import java.util.concurrent.TimeUnit;

@Slf4j
@Component
public class CampaignRepository {


    private static final String CAMPAIGN_WARN = "campaignWarn";

    private static final String FRESH_SWITCH = "mapi-pay-promo-web.autoRefresh.campaignLocalCache.switch";

    @Autowired
    private CampaignQueryService campaignQueryService;

    private final static Cache<String, BaseCampaignDTO> campaignDTOCache = Caffeine.newBuilder().maximumSize(200).expireAfterWrite(10, TimeUnit.SECONDS).build();

    /**
     * 定时刷新调度任务
     */
    private ScheduledFuture<?> scheduledFuture;

    private static List<String> refreshCampaignList;

    static {
        refreshCampaignList = Lion.getList(CommonLionConstants.REFRESH_CAMPAIGN_LOCAL_CACHE_LIST);
        Lion.addConfigListener(CommonLionConstants.REFRESH_CAMPAIGN_LOCAL_CACHE_LIST, configEvent -> {
            refreshCampaignList = Lion.getList(CommonLionConstants.REFRESH_CAMPAIGN_LOCAL_CACHE_LIST);
        });
    }


    public Map<String, BaseCampaignDTO> batchLoadCampaign(List<String> campaignKeyList) {
        if(CollectionUtils.isEmpty(campaignKeyList)){
            return Maps.newHashMap();
        }

        Map<String, BaseCampaignDTO> localCacheCampaignMap = getCampaignMapFromCache(campaignKeyList);

        //异步刷新
        asyncRefreshLocalCache(localCacheCampaignMap.keySet());

        Map<String, BaseCampaignDTO> resultGroupDTOMap = Maps.newHashMap(localCacheCampaignMap);

        Set<String> missCampaignKeySet = Sets.newHashSet(CollectionUtils.subtract(Sets.newHashSet(campaignKeyList), localCacheCampaignMap.keySet()));
        reportLocalCacheHitRate("CampaignLocalCache", missCampaignKeySet.size(), campaignKeyList.size());
        if(CollectionUtils.isEmpty(missCampaignKeySet)){
            return resultGroupDTOMap;
        }
        try {
            CampaignBatchQueryRequest request = new CampaignBatchQueryRequest();
            request.setCampaignKeyList(Lists.newArrayList(missCampaignKeySet));
            CampaignBatchQueryResponse queryResponse = campaignQueryService.batchQuery(request);
            Validate.notNull(queryResponse, "query campaign return null");
            Validate.isTrue(queryResponse.isSuccess(), "query campaign return not success");

            List<BaseCampaignDTO> campaignDTORpcList = queryResponse.getCampaignDTOList();
            if (CollectionUtils.isNotEmpty(campaignDTORpcList)) {
                for (BaseCampaignDTO campaignDTO : campaignDTORpcList) {
                    resultGroupDTOMap.put(campaignDTO.getCampaignKey(), campaignDTO);
                }
                cacheBaseCouponGroupMap(campaignDTORpcList);
            }
        } catch (Exception e) {
            log.error("queryCampaign:{} exception!", missCampaignKeySet, e);
        }

        return resultGroupDTOMap;
    }

    private void reportLocalCacheHitRate(String name, int miss, int all) {
        try {
            MetricHelper.build().name(name).tag("status", "miss").count(miss);
            MetricHelper.build().name(name).tag("status", "hit").count(all - miss);

            //本地缓存命中率
            Cat.logEvent("CampaignCacheStat", "hitRate", miss==0?"0":"-1", null);
            Cat.logEvent("CampaignCacheStat", "totSize" + campaignDTOCache.estimatedSize()/100);
        } catch (Exception e) {
            log.warn("reportLocalCacheHitRate error. cache:{}", name, e);
        }
    }


    private void cacheBaseCouponGroupMap(List<BaseCampaignDTO> campaignDTOList) {
        if (CollectionUtils.isEmpty(campaignDTOList)) {
            return;
        }

        Map<String, BaseCampaignDTO> copy = new HashMap<>();

        for (BaseCampaignDTO campaignDTO : campaignDTOList) {
            copy.put(campaignDTO.getCampaignKey(), DeepCopyUtils.doCopy(campaignDTO));
        }

        campaignDTOCache.putAll(copy);
    }


    private Map<String, BaseCampaignDTO> getCampaignMapFromCache(List<String> campaignKeyList) {
        Map<String, BaseCampaignDTO> allPresent = campaignDTOCache.getAllPresent(campaignKeyList);
        if (MapUtils.isEmpty(allPresent)) {
            return allPresent;
        }

        Map<String, BaseCampaignDTO> copy = Maps.newHashMapWithExpectedSize(allPresent.size());
        for (Map.Entry<String, BaseCampaignDTO> entry : allPresent.entrySet()) {
            copy.put(entry.getKey(), DeepCopyUtils.doCopy(entry.getValue()));
        }
        return copy;
    }


    @PostConstruct
    public void postConstruct(){
        log.info("begin init refreshCampaignLocalCache schedule task.");
        scheduledFuture = ThreadPoolUtils.scheduledExecutor.scheduleWithFixedDelay(()->{
            try {
                if(CollectionUtils.isNotEmpty(refreshCampaignList)){
                    log.info("refreshLocalCache campaignKeyList:{}", refreshCampaignList);
                    asyncRefreshLocalCache(new HashSet<>(refreshCampaignList));
                    for (String campaignKey : refreshCampaignList) {
                        Cat.logEvent("CampaignScheduleRefreshDetail", campaignKey);
                    }
                }
            } catch (Exception e) {
                Cat.logEvent(CAMPAIGN_WARN, "refreshLocalCacheEx:"+e.getMessage());
                log.error("refreshCampaignLocalCache campaignKeys:{} exception!", refreshCampaignList, e);
            }
        }, 0, 3, TimeUnit.SECONDS);
        log.info("end init refreshCampaignLocalCache schedule task.");
    }


    private void asyncRefreshLocalCache(Set<String> keySet) {
        try {
            //异步刷新开关
            if(!Lion.getBooleanValue(FRESH_SWITCH, true)){
                return;
            }

            Set<String> needRefreshCampaignKeys = CampaignRefreshLimiterUtil.filterRefreshCampaignKeys(keySet);
            if(CollectionUtils.isEmpty(needRefreshCampaignKeys)){
                return;
            }
            ThreadPoolUtils.cacheExecutor.execute(()->refreshCampaignLocalCache(keySet));
        } catch (Exception e) {
            log.error("asyncRefreshLocalCache fail, {}", JsonUtils.toJson(keySet), e);
        }
    }

    private void refreshCampaignLocalCache(Set<String> keySet) {
        Cat.logEvent("INVALID_METHOD_3", "com.dianping.api.campaign.biz.CampaignRepository.refreshCampaignLocalCache(java.util.Set)");
        try {
            if(CollectionUtils.isEmpty(keySet)){
                return;
            }
            //统计异步刷新qps
            Cat.logEvent("CampaignCacheAsyncRefresh", "cache");
            CampaignBatchQueryRequest request = new CampaignBatchQueryRequest();
            request.setExactQuery(true);
            request.setCampaignKeyList(new ArrayList<>(keySet));
            CampaignBatchQueryResponse queryResponse = campaignQueryService.batchQuery(request);
            if(queryResponse != null && queryResponse.isSuccess()){
                List<BaseCampaignDTO> campaignDTOList = queryResponse.getCampaignDTOList();
                if (CollectionUtils.isNotEmpty(campaignDTOList)) {
                    campaignDTOList.forEach(dto -> campaignDTOCache.put(dto.getCampaignKey(), dto));
                }
            }
        }catch (Exception e){
            log.error("refreshCampaignLocalCache fail, {}", JsonUtils.toJson(keySet), e);
        }
    }

    @PreDestroy
    public void destory(){
        log.info("begin destory refreshCampaignLocalCache schedule task.");
        try{
            if(scheduledFuture != null){
                scheduledFuture.cancel(true);
            }
        }catch (Exception e){
            log.error("destory refreshCampaignLocalCache schedule task exception!", e);
        }
        log.info("end destory refreshCampaignLocalCache schedule task.");
    }


}
