package com.dianping.api.campaign.biz;

import com.dianping.api.campaign.utils.DeepCopyUtils;
import com.dianping.cat.Cat;
import com.dianping.cat.util.MetricHelper;
import com.github.benmanes.caffeine.cache.Cache;
import com.github.benmanes.caffeine.cache.Caffeine;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.sankuai.mpmkt.coupon.group.query.api.CouponGroupQueryService;
import com.sankuai.mpmkt.coupon.group.query.api.dto.BaseCouponGroupDTO;
import com.sankuai.mpmkt.coupon.group.query.api.response.BaseCouponGroupInfoBatchResponse;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang.Validate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;


@Slf4j
@Repository
public class UnifiedCouponGroupRepository {
    @Autowired
    private CouponGroupQueryService baseCouponGroupQueryService;

    private final static Cache<Long, BaseCouponGroupDTO> couponGroupDTOCache = Caffeine.newBuilder().maximumSize(200).expireAfterWrite(10, TimeUnit.SECONDS).build();

    /**
     * 如果未查询到或调用失败不返回该券批次
     * @param unifiedCouponGroupIdList
     * @return
     */
    public Map<Long, BaseCouponGroupDTO> batchLoadCouponGroupById(List<Long> unifiedCouponGroupIdList) {
        if(CollectionUtils.isEmpty(unifiedCouponGroupIdList)){
            return Maps.newHashMap();
        }
        Map<Long, BaseCouponGroupDTO> localCacheGroupDTOMap = getBaseCouponGroupMapFromCache(unifiedCouponGroupIdList);


        Map<Long, BaseCouponGroupDTO> resultGroupDTOMap = Maps.newHashMap(localCacheGroupDTOMap);

        Set<Long> missCouponGroupIdSet = Sets.newHashSet(CollectionUtils.subtract(Sets.newHashSet(unifiedCouponGroupIdList), localCacheGroupDTOMap.keySet()));
        reportLocalCacheHitRate("BaseCouponGroupLocalCache", missCouponGroupIdSet.size(), unifiedCouponGroupIdList.size());
        if(CollectionUtils.isEmpty(missCouponGroupIdSet)){
            return resultGroupDTOMap;
        }
        try {
            BaseCouponGroupInfoBatchResponse queryResponse = baseCouponGroupQueryService.queryByCouponGroupIdList(missCouponGroupIdSet.stream().map(Long::intValue).collect(Collectors.toList()));
            Validate.notNull(queryResponse, "baseCouponGroupQueryService.queryByCouponGroupIdList return null");
            Validate.isTrue(queryResponse.isSuccess(), "baseCouponGroupQueryService.queryByCouponGroupIdList return not success");
            Map<Long, BaseCouponGroupDTO> couponGroupDTOMapFromRpc = queryResponse.getBaseCouponGroupLDTOMap();
            if(MapUtils.isNotEmpty(couponGroupDTOMapFromRpc)){
                resultGroupDTOMap.putAll(couponGroupDTOMapFromRpc);
                cacheBaseCouponGroupMap(couponGroupDTOMapFromRpc);
            }
        } catch (Exception e) {
            log.error("queryByCouponGroupIdList:{} exception!", unifiedCouponGroupIdList, e);
        }
        return resultGroupDTOMap;
    }

    private void reportLocalCacheHitRate(String name, int miss, int all) {
        try {
            MetricHelper.build().name(name).tag("status", "miss").count(miss);
            MetricHelper.build().name(name).tag("status", "hit").count(all - miss);

            //本地缓存命中率
            Cat.logEvent("CouponGroupCache", "hitRate", miss==0?"0":"-1", null);
            Cat.logEvent("CouponGroupCache", "totSize"+couponGroupDTOCache.estimatedSize()/100);
        } catch (Exception e) {
            log.warn("reportLocalCacheHitRate error. cache:{}", name, e);
        }
    }

    private Map<Long, BaseCouponGroupDTO> getBaseCouponGroupMapFromCache(List<Long> couponGroupIds) {
        Map<Long, BaseCouponGroupDTO> allPresent = couponGroupDTOCache.getAllPresent(couponGroupIds);
        if (MapUtils.isEmpty(allPresent)) {
            return allPresent;
        }

        Map<Long, BaseCouponGroupDTO> copy = Maps.newHashMapWithExpectedSize(allPresent.size());
        for (Map.Entry<Long, BaseCouponGroupDTO> entry : allPresent.entrySet()) {
            copy.put(entry.getKey(), DeepCopyUtils.doCopy(entry.getValue()));
        }
        return copy;
    }

    private void cacheBaseCouponGroupMap(Map<Long, BaseCouponGroupDTO> couponGroupDTOMap) {
        if (MapUtils.isEmpty(couponGroupDTOMap)) {
            return;
        }

        Map<Long, BaseCouponGroupDTO> copy = Maps.newHashMapWithExpectedSize(couponGroupDTOMap.size());
        for (Map.Entry<Long, BaseCouponGroupDTO> entry : couponGroupDTOMap.entrySet()) {
            copy.put(entry.getKey(), DeepCopyUtils.doCopy(entry.getValue()));
        }
        couponGroupDTOCache.putAll(copy);
    }

}
