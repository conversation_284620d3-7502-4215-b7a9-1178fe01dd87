
package com.dianping.api.campaign.response;


import com.dianping.api.campaign.enums.CampaignResultCodeWebEnum;
import lombok.Data;

import java.io.Serializable;

@Data
public class CampaignWebResponse<T> implements Serializable {

    private int resultCode;

    private String resultMessage;


    private T data;


    public CampaignWebResponse() {

    }

    public CampaignWebResponse(CampaignResultCodeWebEnum resultCodeEnum) {
        this.resultCode = resultCodeEnum.getCode();
        this.resultMessage = resultCodeEnum.getMessage();
    }

    public CampaignWebResponse(int resultCode, String resultMessage) {
        this.resultCode = resultCode;
        this.resultMessage = resultMessage;
    }

    public CampaignWebResponse(CampaignResultCodeWebEnum resultCodeEnum, T data) {
        this.resultCode = resultCodeEnum.getCode();
        this.resultMessage = resultCodeEnum.getMessage();
        this.data = data;
    }

    public CampaignWebResponse(int resultCode, String resultMessage, T data) {
        this.resultCode = resultCode;
        this.resultMessage = resultMessage;
        this.data = data;
    }

}
