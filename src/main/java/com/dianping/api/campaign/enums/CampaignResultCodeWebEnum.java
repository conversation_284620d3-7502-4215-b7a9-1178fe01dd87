package com.dianping.api.campaign.enums;

public enum CampaignResultCodeWebEnum {

    SUCCESS(0, "成功"),
    ILLEGAL_PARAM(101, "参数错误"),

    UN_LOGIN(102, "未登录"),

    ISSUE_FAIL(103, "发券失败"),

    NOT_BEGIN(104, "活动还没开始"),

    ALREADY_END(105, "活动已结束"),

    RISK_CONTROL(106, "抱歉，本次领券不符合活动规则"),

    STOCK_NOT_ENOUGH(107, "库存不足"),

    NO_USER_QUALIFICATION(108, "无领取资格"),

    ISSUE_NUM_LIMIT(109, "达到领取上限"),

    VALIDATE_FAIL(110, "发券预校验失败"),

    RATE_LIMIT(112, "参与人数过多"),

    NOT_ONLINE(113, "没有在线活动"),

    QUERY_INFLATE_FAIL(114, "膨胀查询失败"),
    ;


    private int code;
    private String message;


    CampaignResultCodeWebEnum(int code , String message) {
        this.code = code;
        this.message = message;
    }

    public int getCode() {
        return code;
    }

    public String getMessage() {
        return message;
    }
}
