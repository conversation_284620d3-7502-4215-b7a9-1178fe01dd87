package com.dianping.api.campaign.request;

import com.dianping.api.campaign.dto.UserWebInfo;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2024/05/13
 */
@Data
public class CampaignQueryInflateRequest implements Serializable {

    /**
     * 用户信息
     */
    private UserWebInfo userInfo;

    /**
     * 膨前券id
     */
    private String unifiedCouponId;

    /**
     * 设备号
     */
    private String uuid;

    /**
     * 前端页面Id
     */
    private String pageId;

    /**
     * 前端组件id
     */
    private String moduleId;

    /**
     * 发券平台  美团APP 8 点评APP 64 微信小程序 128
     */
    private Integer issuePlatform;

}
