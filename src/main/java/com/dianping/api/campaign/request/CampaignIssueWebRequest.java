package com.dianping.api.campaign.request;


import com.dianping.api.campaign.dto.CreditWebInfo;
import com.dianping.api.campaign.dto.UserWebInfo;
import lombok.Data;

import java.io.Serializable;
import java.util.List;
import java.util.Map;


@Data
public class CampaignIssueWebRequest implements Serializable {

    private UserWebInfo userInfo;

    private CreditWebInfo creditInfo;

    private List<String> campaignKeys;

    private String uuid;

    /**
     * 前端页面Id
     */
    private String pageId;
    /**
     * 前端组件id
     */
    private String moduleId;
    /**
     * 发券平台  美团APP 8 点评APP 64 微信小程序 128
     */
    private Integer issuePlatform;

    /**
     * 活动场景
     */
    private int campaignScene;

    /**
     * 神券参数
     */
    private Map<String, String> mmcParams;
}
