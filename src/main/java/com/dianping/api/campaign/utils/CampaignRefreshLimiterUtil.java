package com.dianping.api.campaign.utils;

import com.dianping.api.campaign.dto.CampaignRefreshLimiterBean;
import com.dianping.cat.Cat;
import com.github.benmanes.caffeine.cache.Caffeine;
import com.github.benmanes.caffeine.cache.LoadingCache;
import com.google.common.collect.Sets;
import com.google.common.util.concurrent.RateLimiter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;

import java.util.Map;
import java.util.Set;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicBoolean;

@Slf4j
public class CampaignRefreshLimiterUtil {

    /**
     * 券刷新间隔
     */
    private static final int REFRESH_INTERVAL_SECONDS = 3;

    /**
     * 刷新限流器
     * 间隔3s刷一次
     */
    private final static LoadingCache<String, CampaignRefreshLimiterBean> GROUP_REFRESH_LIMITER_CACHE = Caffeine.newBuilder()
            .expireAfterWrite(10, TimeUnit.SECONDS)
            .maximumSize(1000).build(campaignKey -> new CampaignRefreshLimiterBean(RateLimiter.create(1.0/REFRESH_INTERVAL_SECONDS)));

    /**
     * 获取需要刷新的券批次
     * 期望功能:经常被访问的数据,隔一段时间通过一次  (首次忽略)  间隔时间:3s
     * 首次忽略的原因:假设某个批次被持续访问,第一个请求调rpc设置本地缓存,第二个请求命中本地缓存(首次调用本方法,缓存数据是上一次请求设置进去的)
     * 选择刷新,会导致多调用一次rpc,选择不刷新,本地缓存的数据可能是老数据(取决于本地缓存失效时长)
     * @param campaignKeys
     * @return
     */
    public static Set<String> filterRefreshCampaignKeys(Set<String> campaignKeys) {
        Cat.logEvent("INVALID_METHOD_1", "com.dianping.api.campaign.utils.CampaignRefreshLimiterUtil.filterRefreshCampaignKeys(java.util.Set)");
        Set<String> needRefreshGroupIds = Sets.newHashSet();
        if(CollectionUtils.isEmpty(campaignKeys)){
            return needRefreshGroupIds;
        }
        Map<String, CampaignRefreshLimiterBean> rateLimiterMap = GROUP_REFRESH_LIMITER_CACHE.getAll(campaignKeys);

        for (String campaignKey : rateLimiterMap.keySet()) {
            CampaignRefreshLimiterBean campaignRefreshLimiterBean = rateLimiterMap.get(campaignKey);
            if(campaignRefreshLimiterBean == null){
                continue;
            }
            RateLimiter rateLimiter = campaignRefreshLimiterBean.getRateLimiter();
            //RateLimiter的特性是创建后,第一次调用通过,后面每隔3s通过一次,期望首次不通过
            if(rateLimiter.tryAcquire()){
                AtomicBoolean firstFlag = campaignRefreshLimiterBean.getFirstFlag();
                if(firstFlag.get()){
                    firstFlag.set(false);
                }else{
                    needRefreshGroupIds.add(campaignKey);
                }
            }
        }
        return needRefreshGroupIds;
    }

}
