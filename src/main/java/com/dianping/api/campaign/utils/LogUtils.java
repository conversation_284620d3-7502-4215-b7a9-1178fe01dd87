package com.dianping.api.campaign.utils;

import com.dianping.gmkt.coupon.common.log.utils.RemoteConfigLoader;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.math.RandomUtils;

@Slf4j
public class LogUtils {


    public static boolean ifSample(String simpleClassName, String methodName) {
        int sampleRate = RemoteConfigLoader.getSampleRate(simpleClassName, methodName);
        if (sampleRate <= 0) {
            return false;
        }
        if (sampleRate >= 10000) {
            return true;
        }
        int rand = RandomUtils.nextInt(10000);
        return rand < sampleRate;
    }


}
