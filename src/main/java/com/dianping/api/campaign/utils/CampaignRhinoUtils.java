package com.dianping.api.campaign.utils;

import com.alibaba.fastjson.JSON;
import com.dianping.api.campaign.request.CampaignIssueWebRequest;
import com.dianping.api.campaign.request.CampaignQueryInflateRequest;
import com.dianping.api.campaign.request.CampaignRenderWebRequest;
import com.dianping.rhino.Rhino;
import com.dianping.rhino.onelimiter.LimitResult;
import com.dianping.rhino.onelimiter.OneLimiter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;

import java.util.HashMap;
import java.util.Map;

@Slf4j
public class CampaignRhinoUtils {


    private static final OneLimiter oneLimiter = Rhino.newOneLimiter();


    public static boolean allowCampaignRender(CampaignRenderWebRequest request) {
        return isPass("/campaign/render", buildParam(request));
    }


    private static Map<String, String> buildParam(CampaignRenderWebRequest request) {
        Map<String, String> params = new HashMap<>();

        try {
            if (request.getUserInfo() != null && request.getUserInfo().getUserId() > 0) {
                params.put("userId", String.valueOf(request.getUserInfo().getUserId()));
            } else {
                params.put("userId", "0");
            }

            if (StringUtils.isNotBlank(request.getUuid())) {
                params.put("uuid", request.getUuid());
            }

            if (CollectionUtils.isNotEmpty(request.getCampaignKeys())) {
                params.put("campaignKeys", StringUtils.join(request.getCampaignKeys(), ","));
            }
            
            if (StringUtils.isNotBlank(request.getPageId())) {
                params.put("pageId", request.getPageId());
            }

            if (StringUtils.isNotBlank(request.getModuleId())) {
                params.put("moduleId", request.getModuleId());
            }

            if (request.getIssuePlatform() != null) {
                params.put("issuePlatform", String.valueOf(request.getIssuePlatform()));
            }
            
        } catch (Exception ignore) {
        }

        return params;
    }


    public static boolean allowCampaignIssue(CampaignIssueWebRequest request) {
        return isPass("/campaign/issue", buildParam(request));
    }

    private static Map<String, String> buildParam(CampaignIssueWebRequest request) {
        Map<String, String> params = new HashMap<>();

        try {
            if (request.getUserInfo() != null && request.getUserInfo().getUserId() > 0) {
                params.put("userId", String.valueOf(request.getUserInfo().getUserId()));
            } else {
                params.put("userId", "0");
            }

            if (StringUtils.isNotBlank(request.getUuid())) {
                params.put("uuid", request.getUuid());
            }

            if (CollectionUtils.isNotEmpty(request.getCampaignKeys())) {
                params.put("campaignKeys", StringUtils.join(request.getCampaignKeys(), ","));
            }

            if (StringUtils.isNotBlank(request.getPageId())) {
                params.put("pageId", request.getPageId());
            }

            if (StringUtils.isNotBlank(request.getModuleId())) {
                params.put("moduleId", request.getModuleId());
            }

            if (request.getIssuePlatform() != null) {
                params.put("issuePlatform", String.valueOf(request.getIssuePlatform()));
            }
        } catch (Exception ignore) {
        }

        return params;
    }


    public static boolean allowCampaignTime() {
        return isPass("/campaign/time", new HashMap<>());
    }

    private static boolean isPass(String entrance, Map<String, String> params) {
        try {
            if (StringUtils.isBlank(entrance)) {
                throw new IllegalArgumentException("entrance为空.");
            }
            LimitResult result = oneLimiter.run(entrance, params);
            return result == null || result.isPass();
        } catch (Exception e) {
            log.warn("自定义限流异常，限流结果【通过】", e);
            return true;
        }
    }

    public static boolean allowCampaignQueryInflate(CampaignQueryInflateRequest request) {
        return isPass("/campaign/issue", buildParam(request));
    }

    private static Map<String, String> buildParam(CampaignQueryInflateRequest request) {
        Map<String, String> params = new HashMap<>();

        try {
            if (request.getUserInfo() != null && request.getUserInfo().getUserId() > 0) {
                params.put("userId", String.valueOf(request.getUserInfo().getUserId()));
            } else {
                params.put("userId", "0");
            }

            if (StringUtils.isNotBlank(request.getUuid())) {
                params.put("uuid", request.getUuid());
            }

            if (StringUtils.isNotBlank(request.getUnifiedCouponId())) {
                params.put("unifiedCouponId", request.getUnifiedCouponId());
            }

            if (StringUtils.isNotBlank(request.getPageId())) {
                params.put("pageId", request.getPageId());
            }

            if (StringUtils.isNotBlank(request.getModuleId())) {
                params.put("moduleId", request.getModuleId());
            }

            if (request.getIssuePlatform() != null) {
                params.put("issuePlatform", String.valueOf(request.getIssuePlatform()));
            }
        } catch (Exception ignore) {
            log.error("buildParam exception, request:{}", JSON.toJSONString(request));
        }

        return params;
    }
}
