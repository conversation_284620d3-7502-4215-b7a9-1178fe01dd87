package com.dianping.api.campaign.utils;

import com.dianping.cat.Cat;
import com.sankuai.mpmkt.coupon.issue.octo.api.dto.DiscountCouponDTO;
import com.sankuai.mpmkt.coupon.issue.octo.api.dto.FixedPriceCouponDTO;
import com.sankuai.mpmkt.coupon.issue.octo.api.dto.StepPriceDTO;
import org.apache.commons.collections.CollectionUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

public class MapperUtils {

    public static DiscountCouponDTO map(com.dianping.unified.coupon.manage.api.dto.DiscountCouponDTO discountCouponDTO) {
        if (discountCouponDTO == null) {
            return null;
        }
        DiscountCouponDTO target = new DiscountCouponDTO();
        target.setAccuracy(discountCouponDTO.getAccuracy());
        target.setDiscount(discountCouponDTO.getDiscount());
        target.setRoundingMode(discountCouponDTO.getRoundingMode());
        if (discountCouponDTO.getDiscountMaxAmount() != null) {
            target.setDiscountMaxAmount(discountCouponDTO.getDiscountMaxAmount().stripTrailingZeros().toPlainString());
        }
        if (discountCouponDTO.getPriceLimit() != null) {
            target.setPriceLimit(discountCouponDTO.getPriceLimit().stripTrailingZeros().toPlainString());
        }
        target.setDiscountBaseType(discountCouponDTO.getDiscountBaseType());

        return target;
    }

    public static List<StepPriceDTO> mapStepPrice(List<com.sankuai.mpmkt.coupon.group.query.api.dto.StepPriceDTO> stepPriceList) {
        if (stepPriceList == null) {
            return null;
        }
        if (CollectionUtils.isEmpty(stepPriceList)) {
            return new ArrayList<>(0);
        }
        return stepPriceList.stream().map(entry->map(entry)).collect(Collectors.toList());
    }

    private static StepPriceDTO map(com.sankuai.mpmkt.coupon.group.query.api.dto.StepPriceDTO stepPriceDTO) {
        Cat.logEvent("INVALID_METHOD_2", "com.dianping.api.campaign.utils.MapperUtils.map(com.sankuai.mpmkt.coupon.group.query.api.dto.StepPriceDTO)");
        if (stepPriceDTO == null) {
            return null;
        }

        StepPriceDTO target = new StepPriceDTO();
        target.setDiscountAmount(stepPriceDTO.getDiscountAmount().stripTrailingZeros().toPlainString());
        target.setPriceLimit(stepPriceDTO.getPriceLimit().stripTrailingZeros().toPlainString());

        return target;
    }

    public static List<StepPriceDTO> mapStepPriceNew(List<com.dianping.unified.coupon.manage.api.dto.StepPriceDTO> stepPriceList) {
        if (stepPriceList == null) {
            return null;
        }
        if (CollectionUtils.isEmpty(stepPriceList)) {
            return new ArrayList<>(0);
        }
        return stepPriceList.stream().map(entry->map(entry)).collect(Collectors.toList());
    }

    private static StepPriceDTO map(com.dianping.unified.coupon.manage.api.dto.StepPriceDTO stepPriceDTO) {
        if (stepPriceDTO == null) {
            return null;
        }

        StepPriceDTO target = new StepPriceDTO();
        target.setDiscountAmount(stepPriceDTO.getDiscountAmount().stripTrailingZeros().toPlainString());
        target.setPriceLimit(stepPriceDTO.getPriceLimit().stripTrailingZeros().toPlainString());

        return target;
    }

    public static FixedPriceCouponDTO map(com.dianping.gmkt.coupon.common.api.dto.FixedPriceCouponDTO fixedPriceCouponDTO) {
        if (fixedPriceCouponDTO == null) {
            return null;
        }

        FixedPriceCouponDTO target = new FixedPriceCouponDTO();
        if (fixedPriceCouponDTO.getFixedPrice() != null) {
            target.setFixedPrice(fixedPriceCouponDTO.getFixedPrice().stripTrailingZeros().toPlainString());
        }

        if (fixedPriceCouponDTO.getMaxProductPrice() != null) {
            target.setMaxProductPrice(fixedPriceCouponDTO.getMaxProductPrice().stripTrailingZeros().toPlainString());
        }

        if (fixedPriceCouponDTO.getMaxDiscountAmount() != null) {
            target.setMaxDiscountAmount(fixedPriceCouponDTO.getMaxDiscountAmount().stripTrailingZeros().toPlainString());
        }

        return target;
    }
}
