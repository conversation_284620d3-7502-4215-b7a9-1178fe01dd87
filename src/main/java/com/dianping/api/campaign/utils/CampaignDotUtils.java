package com.dianping.api.campaign.utils;

import com.alibaba.fastjson.JSON;
import com.dianping.api.campaign.request.CampaignIssueWebRequest;
import com.dianping.api.campaign.request.CampaignQueryInflateRequest;
import com.dianping.api.campaign.request.CampaignRenderWebRequest;
import com.dianping.cat.Cat;
import lombok.extern.slf4j.Slf4j;

@Slf4j
public class CampaignDotUtils {


    public static void dot4RenderRequest(CampaignRenderWebRequest request) {
        try {
            Cat.logEvent("CampaignRenderPageInfo", request.getPageId() + "#" + request.getModuleId());
            Cat.logEvent("CampaignRenderPlatform", String.valueOf(request.getIssuePlatform()));
            if (request.getUserInfo() != null && request.getUserInfo().getUserId() <= 0) {
                Cat.logEvent("CampaignRenderUserInfo", "noUserId");
            }
        } catch (Exception ignore){

        }
    }


    public static void dot4IssueRequest(CampaignIssueWebRequest request) {
        try {
            Cat.logEvent("CampaignIssuePageInfo", request.getPageId() + "#" + request.getModuleId());
            Cat.logEvent("CampaignIssuePlatform", String.valueOf(request.getIssuePlatform()));
        } catch (Exception ignore){

        }
    }

    public static void dot4QueryInflateRequest(CampaignQueryInflateRequest request) {
        try {
            Cat.logEvent("CampaignQueryInflatePageInfo", request.getPageId() + "#" + request.getModuleId());
            Cat.logEvent("CampaignQueryInflatePlatform", String.valueOf(request.getIssuePlatform()));
            if (request.getUserInfo() != null && request.getUserInfo().getUserId() <= 0) {
                Cat.logEvent("CampaignQueryInflateUserInfo", "noUserId");
            }
        } catch (Exception ignore){
            log.error("dot4QueryInflateRequest exception, request:{}", JSON.toJSONString(request));
        }
    }
}
