package com.dianping.api.campaign.utils;

import com.dianping.unified.coupon.manage.api.dto.UnifiedCouponDTO;
import com.sankuai.mpmkt.coupon.issue.octo.api.dto.CampaignIssueDetail;
import com.sankuai.mpmkt.coupon.issue.octo.api.dto.CampaignRecordCouponDetail;
import com.sankuai.nib.mkt.common.base.util.EncryptUtil;
import com.sankuai.nib.mkt.common.base.util.KmsUtil;
import org.apache.commons.lang.StringUtils;

import java.math.BigDecimal;

/**
 * 活动膨胀工具
 *
 * <AUTHOR>
 * @date 2024/05/15
 */
public class CampaignInflateUtils {

    public static final int NIB_ASSET_TYPE = 2;

    /**
     * 膨胀类型，普通神券-膨胀前
     */
    public static final int NORMAL_CAN_INFLATE = 3;

    /**
     * 膨胀类型，普通神券-膨胀后
     */
    public static final int NORMAL_AFTER_INFLATE = 4;

    /**
     * 膨胀类型，普通神券-不可膨胀
     */
    public static final int NORMAL_NOT_INFLATE = 5;



    public static int getCouponInflateType(boolean canInflate, boolean afterInflate) {
        if (canInflate) {
            return NORMAL_CAN_INFLATE;
        }
        if (afterInflate) {
            return NORMAL_AFTER_INFLATE;
        }
        return NORMAL_NOT_INFLATE;
    }

    public static String buildBizToken(CampaignRecordCouponDetail detail) {
        if (detail == null || detail.getDiscountAmount() == null) {
            return null;
        }
        BigDecimal discountAmount = new BigDecimal(detail.getDiscountAmount());
        long discountAmountLong = discountAmount.scaleByPowerOfTen(2).longValue();
        return buildBizToken(detail.getUserId(), String.valueOf(discountAmountLong), detail.getUnifiedCouponId(), detail.isCanInflate());
    }

    public static String buildBizToken(UnifiedCouponDTO unifiedCouponDTO, boolean canInflate) {
        if (unifiedCouponDTO == null || unifiedCouponDTO.getCouponGroupDTO().getDiscountAmount() == null) {
            return null;
        }
        BigDecimal discountAmount = unifiedCouponDTO.getCouponGroupDTO().getDiscountAmount();
        long discountAmountLong = discountAmount.scaleByPowerOfTen(2).longValue();
        return buildBizToken(unifiedCouponDTO.getUserId(), String.valueOf(discountAmountLong), unifiedCouponDTO.getUnifiedCouponId(), canInflate);
    }

    public static String buildBizToken(long userId, String discountAmount, String unifiedCouponId, boolean canInflate) {
        if (userId <= 0 || StringUtils.isBlank(discountAmount) || StringUtils.isBlank(unifiedCouponId)) {
            return null;
        }
        String mmcKey = KmsUtil.getMagicMemberKey();
        // 加密前券信息 userId_券金额_券码_canInflate
        String beforeEncryptionStr = String.format("%s_%s_%s_%s",
                userId,
                discountAmount,
                unifiedCouponId,
                canInflate);
        return EncryptUtil.encryptWithCBC7(mmcKey, beforeEncryptionStr);
    }
}
