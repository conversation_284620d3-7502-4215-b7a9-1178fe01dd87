package com.dianping.api.campaign.utils;

import com.dianping.cat.thread.pool.CatScheduledExecutorServiceTraceWrapper;
import com.dianping.rhino.Rhino;
import com.dianping.rhino.threadpool.DefaultThreadPoolProperties;
import com.dianping.rhino.threadpool.ThreadPool;
import com.meituan.mtrace.thread.pool.TraceExecutors;

import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;

public class ThreadPoolUtils {


    public static ThreadPool cacheExecutor = Rhino.newThreadPool("cacheExecutor", DefaultThreadPoolProperties.Setter()
            .withCoreSize(5)
            .withMaxSize(20)
            .withKeepAliveTimeMinutes(30)
            .withKeepAliveTimeUnit(TimeUnit.SECONDS)
            .withMaxQueueSize(100)
            .with<PERSON>eject<PERSON>and<PERSON>(new ThreadPoolExecutor.CallerRunsPolicy()));


    public static ScheduledExecutorService scheduledExecutor =
            TraceExecutors.getTraceScheduledExecutorService(
                    new CatScheduledExecutorServiceTraceWrapper(Executors.newSingleThreadScheduledExecutor()));

    public static ThreadPool issueCouponPreExecutor = Rhino.newThreadPool("issueCouponPreExecutor", DefaultThreadPoolProperties.Setter()
            .withCoreSize(5)
            .withMaxSize(10)
            .withKeepAliveTimeMinutes(30)
            .withKeepAliveTimeUnit(TimeUnit.SECONDS)
            .withMaxQueueSize(100)
            .withRejectHandler(new ThreadPoolExecutor.CallerRunsPolicy()));

}
