package com.dianping.api.campaign.utils;

import com.esotericsoftware.kryo.Kryo;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * Created by cailixia on 2022/4/7.
 */
public class DeepCopyUtils {


    private static final ThreadLocal<Kryo> KRYO_THREAD_LOCAL = ThreadLocal.withInitial(
            () -> {
                Kryo kryo = new Kryo();
                kryo.setRegistrationRequired(false);
                return kryo;
            });

    public static <T> T doCopy(T target) {
        if (null == target) {
            return null;
        }
        Kryo kryoInstance = KRYO_THREAD_LOCAL.get();
        return kryoInstance.copy(target);
    }

}
