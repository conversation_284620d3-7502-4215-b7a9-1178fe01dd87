package com.dianping.api.campaign.dto;

import com.sankuai.mpmkt.coupon.issue.octo.api.dto.DiscountCouponDTO;
import com.sankuai.mpmkt.coupon.issue.octo.api.dto.FixedPriceCouponDTO;
import com.sankuai.mpmkt.coupon.issue.octo.api.dto.StepPriceDTO;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
public class CampaignIssueCouponWebInfo implements Serializable {

    private Integer resultCode;

    private String resultMessage;

    private String title;

    private Integer valueType;

    private String redirectLink;

    private String beginTime;

    private String endTime;

    private String priceLimit;

    private String discountAmount;

    private List<StepPriceDTO> stepPriceList;

    private DiscountCouponDTO discountCouponDTO;

    private FixedPriceCouponDTO fixedPriceCouponDTO;

}
