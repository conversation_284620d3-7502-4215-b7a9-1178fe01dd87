package com.dianping.api.campaign.dto;

import com.google.common.util.concurrent.RateLimiter;
import lombok.Data;

import java.util.concurrent.atomic.AtomicBoolean;

@Data
public class CampaignRefreshLimiterBean {

    private AtomicBoolean firstFlag;

    private RateLimiter rateLimiter;

    public CampaignRefreshLimiterBean(RateLimiter rateLimiter) {
        this.firstFlag = new AtomicBoolean(true);
        this.rateLimiter = rateLimiter;
    }
}
