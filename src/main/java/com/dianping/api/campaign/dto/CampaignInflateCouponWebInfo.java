package com.dianping.api.campaign.dto;

import com.sankuai.mpmkt.coupon.issue.octo.api.dto.DiscountCouponDTO;
import com.sankuai.mpmkt.coupon.issue.octo.api.dto.FixedPriceCouponDTO;
import com.sankuai.mpmkt.coupon.issue.octo.api.dto.StepPriceDTO;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 膨后券信息
 * <AUTHOR>
 * @date 2024/05/13
 */
@Data
public class CampaignInflateCouponWebInfo implements Serializable {

    /**
     * 是否已发券
     */
    private boolean hasCoupon;

    /**
     * 券状态
     */
    private int couponStatus;

    /**
     * 券标题
     */
    private String title;

    /**
     * 券优惠类型
     */
    private Integer valueType;

    /**
     * 跳转链接
     */
    private String redirectLink;

    /**
     * 开始时间
     */
    private String beginTime;

    /**
     * 结束时间
     */
    private String endTime;

    /**
     * 门槛
     */
    private String priceLimit;

    /**
     * 面额
     */
    private String discountAmount;

    /**
     * 是否可膨胀
     */
    private boolean canInflate;

    /**
     * 是否已膨胀
     */
    private boolean afterInflate;

    /**
     * 膨胀加密Token
     */
    private String bizToken;

    /**
     * 券id
     */
    private String unifiedCouponId;

    /**
     * 券批次id
     */
    private String couponGroupId;

    /**
     * 资产类型
     */
    private int assetType;

    /**
     * 券膨胀类型
     * 1.付费神券-膨胀前 2.付费神券-膨胀后 3.普通神券-膨胀前 4普通神券-膨胀后  5普通神券-不可膨胀
     */
    private int couponInflateType;

}
