package com.dianping.api.campaign.dto;

import com.sankuai.mpmkt.coupon.issue.octo.api.dto.DiscountCouponDTO;
import com.sankuai.mpmkt.coupon.issue.octo.api.dto.FixedPriceCouponDTO;
import com.sankuai.mpmkt.coupon.issue.octo.api.dto.StepPriceDTO;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
public class CampaignIssueValidateCouponWebInfo implements Serializable {

    private Integer validateCode;

    private boolean hasCoupon;

    private int couponStatus;

    private String title;

    private Integer valueType;

    private String redirectLink;

    private String beginTime;

    private String endTime;

    private String priceLimit;

    private String discountAmount;

    private List<StepPriceDTO> stepPriceList;

    private DiscountCouponDTO discountCouponDTO;

    private FixedPriceCouponDTO fixedPriceCouponDTO;

    /**
     * 是否可膨胀
     */
    private boolean canInflate;

    /**
     * 是否已膨胀
     */
    private boolean afterInflate;

    /**
     * 膨胀加密Token
     */
    private String bizToken;

    /**
     * 券id
     */
    private String unifiedCouponId;

    /**
     * 券批次id
     */
    private String couponGroupId;

    /**
     * 资产类型
     */
    private int assetType;

    /**
     * 券膨胀类型
     * 1.付费神券-膨胀前 2.付费神券-膨胀后 3.普通神券-膨胀前 4普通神券-膨胀后  5普通神券-不可膨胀
     */
    private int couponInflateType;

}
