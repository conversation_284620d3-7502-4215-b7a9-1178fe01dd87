package com.dianping.api.log.util;

import com.dianping.cat.Cat;

import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public final class ThirdFeedLogUtil {
	
	public static final int MOBILE = 0;
	public static final int MAIN = 1;
	public static final String FUNCTION_SENDMOBILEFEED = "sendMobileFeed";
	public static final String FUNCTION_SEND = "send";
	public static final String FUNCTION_FEEDLIST = "feedList";
	public static final String NAME_SEQID = "seqID";
	
	private ThirdFeedLogUtil() {
	}
	
	/**
	 * 得到int 中1的个数
	 * */
	public static int getIntOneNumber(int number){
        Cat.logEvent("INVALID_METHOD_4", "com.dianping.api.log.util.ThirdFeedLogUtil.getIntOneNumber(int)");
        int result = 0;
		int compare = 1;
		for(int i=0;i<32;i++){
			if((number & compare)>0){
				result ++;
			}
			compare = compare << 1;
		}
		return result;
	}
	
	/**
	 * 生成seqID
	 * */
	public static String generateSeqID(long userId){
        Cat.logEvent("INVALID_METHOD_1", "com.dianping.api.log.util.ThirdFeedLogUtil.generateSeqID(long)");
        Date date = new Date();
		SimpleDateFormat format = new SimpleDateFormat("yyyyMMdd_HHmmss_SSS");
		return userId + "_" + format.format(date);
	}
	
	/**
	 * 生成希望同步的个数
	 * 输入为 feedFlag, mobileList, mainList
	 * */
	public static int getFeedNumber(int feedFlag,List<Integer> mobileList, List<Integer> mainList){
        Cat.logEvent("INVALID_METHOD_1", "com.dianping.api.log.util.ThirdFeedLogUtil.getFeedNumber(int,java.util.List,java.util.List)");
        List<Integer> allList = new ArrayList<Integer>();
		for(int i:mobileList){
			allList.add(i);
		}
		for(int i:mainList){
			if(!allList.contains(i)){
				allList.add(i);
			}
		}
		int result = 0;
		for(int i:allList){
			if((i & feedFlag)>0){
				result ++;
			}
		}
		return result;	
	}
	
	/**
	 * 生成token内容
	 * 输入为 feedFlag, mobileList, mainList
	 * */
	public static String getFeedToken(int feedFlag,List<Integer> mobileList, List<Integer> mainList){
        Cat.logEvent("INVALID_METHOD_3", "com.dianping.api.log.util.ThirdFeedLogUtil.getFeedToken(int,java.util.List,java.util.List)");
        List<Integer> allList = new ArrayList<Integer>();
		for(int i:mobileList){
			allList.add(i);
		}
		for(int i:mainList){
			if(!allList.contains(i)){
				allList.add(i);
			}
		}
		StringBuilder buffer = new StringBuilder(200);
		buffer.append("mobileList: ") 
		      .append(mobileList)
		      .append(", mainList:")
		      .append(mainList)
		      .append(" ;token: ");
		for(int i:allList){
			if((i & feedFlag)>0){
				//手机有会优先用手机的
				if(mobileList.contains(i)){
					buffer.append(MOBILE + "_" + String.valueOf(i) + ","); 
				} else {
					buffer.append(MAIN + "_" + String.valueOf(i) + ","); 
				}
			}
		}
	
		return buffer.substring(0, buffer.length()-1);	
	}
	
	/**
	 * 生成token内容
	 * 输入为 type (0 moible, 1 main)
	 * List<Integer> feedList
	 * */
	public static String getFeedToken(int type, List<Integer> feedList){
        Cat.logEvent("INVALID_METHOD_3", "com.dianping.api.log.util.ThirdFeedLogUtil.getFeedToken(int,java.util.List)");
        String result = "";
		for(int feed:feedList){
			result += type + "_" + feed + ",";
		}
		if(result.length()>0){
			result = result.substring(0, result.length()-1);
		}
		return result;
	}
	
	
}
