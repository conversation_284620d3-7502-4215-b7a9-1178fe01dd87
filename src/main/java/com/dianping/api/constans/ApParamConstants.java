package com.dianping.api.constans;

import lombok.Getter;

/**
 * <AUTHOR>
 * @date 2025/1/13
 */
@Getter
public enum ApParamConstants {
    //医美ap
    CITY_ID("dz_select_city", "城市ID"),
    PLATFORM("dz_platform", "平台标识"),
    DP_ID("dpId", "DP ID"),
    UUID("uuid", "用户UUID"),
    USER_ID("userid", "用户ID"),
    SKU_ID("sku_id", "业务SKU ID"),
    ROW_ID("rowId", "行ID"),
    SHOP_ID("shop_id", "商店ID"),
    PAGE_SOURCE("page_source", "页面来源 0:发券页面 1:其它查询页"),
    ;
    private String code;
    private String desc;

    ApParamConstants(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }
}
