package com.dianping.api.constans;

import com.google.common.collect.Lists;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/8/14
 */
public class CommonConstants {

    public static final int DRAWED = 1;

    public static final int CAN_DRAW = 2;

    public static final String PERSON_TAG = "customPersonTag";

    public static final String SEND_COUPON_TYPE = "sendCouponType";
    public static final String WAIT_DRAW_IMG = "waitDrawImg";
    public static final String DRAW_SINGLE_COUPON_IMG = "drawSingleCouponImg";
    public static final String DRAW_MULTI_COUPONS_IMG = "drawMultiCouponsImg";
    public static final String SUB_TITLE_IMG = "subTitleImg";

    /**
     * 弹窗领券+领券模块
     */
    public static final String POPUP_MODULE = "popupAndCouponModule";

    /**
     * 领券模块
     */
    public static final String DRAW_MODULE = "drawCouponModule";

    public static final String BANNER_COUPON_MODULE = "bannerAndCouponModule";



    /**
     * 投放券支持的领券样式
     */
    public static final List<String> SUPPORT_SEND_TYPE = Lists.newArrayList(POPUP_MODULE, DRAW_MODULE, BANNER_COUPON_MODULE);

    /**
     * 彩虹投放定义的安卓和ios平台
     */
    public static final int RAINBOW_ANDROID = 4;
    public static final int RAINBOW_IOS = 5;

    /**
     * 跨域请求配置Key
     */
    public static final String CROSS_FILTER_HEADER = "CrossFilterHeaders";
    public static final String CROSS_UTILS_HEADER = "CrossUtilsHeaders";

    public static final String PACKAGE_SECRET_KEY = "packageSecretKey";

    /**
     * 新老poi页
     */
    public static final Integer NEW_POI_PAGE = 1;
    
    public static final String DEVICE_ID = "deviceId";
}
