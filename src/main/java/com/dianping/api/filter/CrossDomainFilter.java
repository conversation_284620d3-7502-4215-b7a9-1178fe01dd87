package com.dianping.api.filter;

import com.dianping.api.constans.CommonConstants;
import com.dianping.api.util.LionQueryUtils;
import com.dianping.combiz.spring.util.LionConfigUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.servlet.*;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;

@Component
@Slf4j
public class CrossDomainFilter implements Filter {


    @Override
    public void init(FilterConfig filterConfig) throws ServletException {

    }

    @Override
    public void doFilter(ServletRequest servletRequest, ServletResponse servletResponse, FilterChain filterChain) throws IOException, ServletException {
        HttpServletResponse resp = (HttpServletResponse) servletResponse;
        HttpServletRequest request = (HttpServletRequest) servletRequest;
        String ajaxDomain = LionConfigUtils.getProperty("mapi-pay-promo-web.ajax.ajaxDomain");

        String origin = request.getHeader("Origin");
        if (StringUtils.isNotBlank(origin) && ajaxDomain.contains(origin)) {
            resp.setHeader("Access-Control-Allow-Origin", origin);
            resp.setHeader("Access-Control-Allow-Credentials", "true");
            resp.setHeader("Access-Control-Allow-Methods", "GET,POST,OPTIONS,PUT,DELETE,HEAD");
            resp.setHeader("Access-Control-Allow-Headers", LionQueryUtils.CROSS_DOMAIN_HEADERS_MAP.get(CommonConstants.CROSS_FILTER_HEADER));
            resp.setHeader("Access-Control-Expose-Headers", "*");
            resp.addHeader("Access-Control-Max-Age", "1728000");
        }
        filterChain.doFilter(servletRequest, servletResponse);
    }

    @Override
    public void destroy() {

    }
}

