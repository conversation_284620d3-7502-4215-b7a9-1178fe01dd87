package com.dianping.api.picasso.controller;

import com.dianping.mobile.base.datatypes.bean.ClientInfoRule;
import com.dianping.mobile.framework.action.DefaultAction;
import com.dianping.mobile.framework.annotation.Action;
import com.dianping.mobile.framework.base.datatypes.SimpleMsg;
import com.dianping.mobile.framework.base.datatypes.StatusCode;
import com.dianping.mobile.framework.datatypes.CommonMobileResponse;
import com.dianping.mobile.framework.datatypes.IMobileContext;
import com.dianping.mobile.framework.datatypes.IMobileResponse;
import com.dianping.mobile.framework.enums.ReqProtocol;
import com.dianping.pay.api.biz.activity.RestUserInfoService;
import com.dianping.pay.api.biz.activity.UnifiedIssueCouponBiz;
import com.dianping.pay.api.biz.activity.newloader.CouponIssueActivityQueryService;
import com.dianping.pay.api.biz.activity.newloader.dto.CouponActivityContext;
import com.dianping.pay.api.biz.activity.newloader.dto.PromotionRequestContext;
import com.dianping.pay.api.biz.activity.newloader.dto.RestUserInfo;
import com.dianping.pay.api.biz.activity.newloader.mapper.DealPromoInfoMapper;
import com.dianping.pay.api.biz.activity.newloader.mapper.IssueActivityMapper;
import com.dianping.pay.api.entity.issuecoupon.CouponProductHttpPromoModule;
import com.dianping.pay.api.entity.issuecoupon.IssueCouponMsg;
import com.dianping.pay.api.entity.issuecoupon.ProductcouponpromoRequest;
import com.dianping.pay.api.entity.issuecoupon.PromotionDisplayActionRequest;
import com.dianping.pay.api.util.PoiIdUtils;
import com.dianping.pay.framework.utils.shopUuid.ShopUuidUtils;
import com.meituan.servicecatalog.api.annotations.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.Validate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;

import java.util.List;

/**
 * <AUTHOR>
 */

@InterfaceDoc(
        displayName = "新版团购详情页领券组件-h5",
        type = "restful",
        description = "新版团购详情页领券组件-h5",
        scenarios = "新版团购详情页领券组件-h5",
        host = "",
        authors = "xiemingyuan"
)
@Controller(value = "couponpromo.bin")
@Action(url = "couponpromo.bin", httpType = "post", protocol = {ReqProtocol.MAPI, ReqProtocol.REST})
@Slf4j
public class CouponPromoAction extends DefaultAction<ProductcouponpromoRequest> {

    @Autowired
    private CouponIssueActivityQueryService couponIssueActivityQueryService;

    @Autowired
    private UnifiedIssueCouponBiz unifiedIssueCouponBiz;

    @Autowired
    private ShopUuidUtils shopUuidUtils;

    @Autowired
    private DealPromoInfoMapper dealPromoInfoMapper;

    @Autowired
    private RestUserInfoService restUserInfoService;

    @Override
    protected IMobileResponse validate(ProductcouponpromoRequest request, IMobileContext context) {
        try {
            Validate.notNull(request, "null request");
            Long requestShopid = request.getShopIdL();
            String shopuuid = request.getShopuuid();

            if (requestShopid == null || requestShopid <= 0) {
                if (shopUuidUtils.isShopUuid(shopuuid)) {
                    Long shopId = shopUuidUtils.toShopId(shopuuid);
                    request.setShopIdL(shopId);
                    request.setShopid(PoiIdUtils.poiIdLongToInt(shopId));
                }
            } else {
                request.setShopuuid(null);
            }
            Validate.isTrue(request.getProducttype() != null, "product type is null");
            Validate.isTrue(request.getProductid() != null, "product id is null");

            //Validate.isTrue(request.getShopid() != null && request.getShopid() > 0, "invalid shopId");
            //其他参数均使用内置校验
        } catch (Exception e) {
            log.warn("validate request exception!", e);
            return new CommonMobileResponse(new IssueCouponMsg(401, e.getMessage(), e.getMessage()));
        }
        return null;
    }

    @MethodDoc(requestMethods = HttpMethod.POST,
            urls = "issueCouponComponentAction.bin",
            displayName = "新版团购详页领券组件-h5",
            description = "新版团购详页领券组件-h5",
            returnValueDescription = "团购详页新版领券组件-展示信息",
            restExampleUrl = "",
            parameters = {
                    @ParamDoc(
                            name = "request",
                            description = "productcouponpromo.bin请求参数",
                            type = PromotionDisplayActionRequest.class,
                            paramType = ParamType.REQUEST_BODY
                    ),
                    @ParamDoc(
                            name = "iMobileContext",
                            description = "mapi-shell协议上下文",
                            type = IMobileContext.class,
                            paramType = ParamType.REQUEST_BODY
                    )
            },
            responseParams = {@ParamDoc(name = "iMobileResponse", description = "通用返回数据模型")},
            extensions = {
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE",
                            content = "公开数据，无需建券"
                    ),
            }
    )
    @Override
    protected IMobileResponse execute(ProductcouponpromoRequest request, IMobileContext context) {
        try {
            RestUserInfo restUserInfo = restUserInfoService.handleMobileContext(context);
            PromotionRequestContext promotionRequestContext = IssueActivityMapper.buildHttpCouponPromoRequestContext(request, context, restUserInfo);

            log.info("H5CouponPromoAction# promotionRequestContext:{}", promotionRequestContext);
            CouponActivityContext couponActivityContext = couponIssueActivityQueryService.queryShopPromotions(promotionRequestContext, context);
            log.info("CouponPromoAction# queryShopPromotions response:{}", couponActivityContext);
            CouponProductHttpPromoModule couponProductPromoModule = dealPromoInfoMapper.buildCouponProductHttpPromoModule(couponActivityContext, context);
            log.info("H5CouponPromoAction# coupons query by promotion component:{}", couponProductPromoModule);

            return new CommonMobileResponse(couponProductPromoModule);
        } catch (Exception e) {
            log.error("H5CouponPromoAction#  exception. request: {}", request, e);
            return new CommonMobileResponse(
                    new SimpleMsg("提示", "请稍后重试~", StatusCode.ACTIONEXCEPTION));
        }
    }

    @Override
    protected List<ClientInfoRule> getRule() {
        return null;
    }
}
