package com.dianping.api.picasso.controller;

import com.alibaba.fastjson.JSON;
import com.dianping.api.util.ApiUtil;
import com.dianping.cat.Cat;
import com.dianping.mobile.base.datatypes.bean.ClientInfoRule;
import com.dianping.mobile.framework.action.DefaultAction;
import com.dianping.mobile.framework.annotation.Action;
import com.dianping.mobile.framework.datatypes.CommonMobileResponse;
import com.dianping.mobile.framework.datatypes.IMobileContext;
import com.dianping.mobile.framework.datatypes.IMobileResponse;
import com.dianping.mobile.framework.enums.ReqProtocol;
import com.dianping.pay.api.biz.activity.RestUserInfoService;
import com.dianping.pay.api.biz.activity.newloader.CouponIssueActivityQueryService;
import com.dianping.pay.api.biz.activity.newloader.dto.CouponActivityContext;
import com.dianping.pay.api.biz.activity.newloader.dto.PromotionRequestContext;
import com.dianping.pay.api.biz.activity.newloader.dto.RestUserInfo;
import com.dianping.pay.api.biz.activity.newloader.mapper.IssueActivityMapper;
import com.dianping.pay.api.biz.activity.newloader.mapper.ShopCartPromoInfoMapper;
import com.dianping.pay.api.entity.issuecoupon.*;
import com.meituan.servicecatalog.api.annotations.InterfaceDoc;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang.Validate;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2021/1/19
 */
@InterfaceDoc(
        displayName = "购物车领券组件",
        type = "restful",
        description = "新版商详页领券组件",
        scenarios = "新版商详页领券组件",
        host = "",
        authors = "xiemingyuan"
)
@Controller(value = "shopcartpromodisplay.bin")
@Action(url = "shopcartpromodisplay.bin", httpType = "post", protocol = {ReqProtocol.MAPI, ReqProtocol.REST})
public class ShopCartPromoDisplayAction extends DefaultAction<ShopCartPromoDisplayActionRequest> {

    public static final Logger log = LoggerFactory.getLogger(ShopCartPromoDisplayAction.class);

    public static final String CAT_TYPE = "ShopCartPromoDisplayAction";

    @Autowired
    private CouponIssueActivityQueryService couponIssueActivityQueryService;

    @Autowired
    private RestUserInfoService restUserInfoService;

    @Override
    protected IMobileResponse validate(ShopCartPromoDisplayActionRequest request, IMobileContext context) {
        try {
            Validate.notNull(request, "null request");
            Validate.isTrue(StringUtils.isNotBlank(request.getShopcartbrandproducts()), "invalid products");
        } catch (Exception e) {
            log.warn("validate request exception!, request:{}", request, e);
            return new CommonMobileResponse(new IssueCouponMsg(401, e.getMessage(), e.getMessage()));
        }
        return null;
    }

    @Override
    protected IMobileResponse execute(ShopCartPromoDisplayActionRequest request, IMobileContext context) {
        PromotionRequestContext promotionRequestContext;
        List<ShopCartBrandProduct> shopCartBrandProducts;
        try {
            shopCartBrandProducts = JSON.parseArray(request.getShopcartbrandproducts(), ShopCartBrandProduct.class);
            if (CollectionUtils.isEmpty(shopCartBrandProducts)) {
                Cat.logEvent(CAT_TYPE, "empty shopCartBrandProducts", "-1", JSON.toJSONString(request));
                return new CommonMobileResponse(ResultWrapper.success(null));
            }
        } catch (Exception e) {
            log.warn("validate shopCartBrandProducts exception!, request:{}", request.getShopcartbrandproducts(), e);
            Cat.logEvent(CAT_TYPE, "invalid shopCartBrandProducts", "-1", JSON.toJSONString(request));
            return new CommonMobileResponse(ResultWrapper.success(null));
        }
        // 如果是h5场景
        if (!ApiUtil.isMapiRequest(context)) {
            RestUserInfo restUserInfo = restUserInfoService.handleMobileContext(context);
            promotionRequestContext = IssueActivityMapper.buildHttpPromotionRequestContext(request, restUserInfo, shopCartBrandProducts);
        } else {
            promotionRequestContext = IssueActivityMapper.buildNativePromotionRequestContext(request, context, shopCartBrandProducts);
        }
        CouponActivityContext couponActivityContext = couponIssueActivityQueryService.queryShopPromotions(promotionRequestContext, context);
        ShopCartPromoInfo shopCartPromoInfo = ShopCartPromoInfoMapper.buildShopCartPromoInfo(couponActivityContext, shopCartBrandProducts);
        return new CommonMobileResponse(ResultWrapper.success(shopCartPromoInfo));
    }

    @Override
    protected List<ClientInfoRule> getRule() {
        return null;
    }
}
