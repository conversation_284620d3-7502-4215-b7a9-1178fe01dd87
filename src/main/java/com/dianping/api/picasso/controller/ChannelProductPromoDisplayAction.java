package com.dianping.api.picasso.controller;

import com.dianping.api.util.ApiUtil;
import com.dianping.lion.client.Lion;
import com.dianping.mobile.base.datatypes.bean.ClientInfoRule;
import com.dianping.mobile.framework.action.DefaultAction;
import com.dianping.mobile.framework.annotation.Action;
import com.dianping.mobile.framework.datatypes.CommonMobileResponse;
import com.dianping.mobile.framework.datatypes.IMobileContext;
import com.dianping.mobile.framework.datatypes.IMobileResponse;
import com.dianping.mobile.framework.enums.ReqProtocol;
import com.dianping.pay.api.biz.activity.RestUserInfoService;
import com.dianping.pay.api.biz.activity.newloader.CouponIssueActivityQueryService;
import com.dianping.pay.api.biz.activity.newloader.dto.CouponActivityContext;
import com.dianping.pay.api.biz.activity.newloader.dto.PromotionRequestContext;
import com.dianping.pay.api.biz.activity.newloader.dto.RestUserInfo;
import com.dianping.pay.api.biz.activity.newloader.mapper.IssueActivityMapper;
import com.dianping.pay.api.biz.activity.newloader.mapper.ProductPromoInfoMapper;
import com.dianping.pay.api.entity.issuecoupon.*;
import com.dianping.pay.api.enums.ChannelProductEnum;
import com.dianping.pay.api.util.LionConstants;
import com.dianping.pay.framework.utils.shopUuid.ShopUuidUtils;
import com.meituan.servicecatalog.api.annotations.InterfaceDoc;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang.Validate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;

import java.util.List;

@InterfaceDoc(
        displayName = "渠道商详页领券组件",
        type = "restful",
        description = "渠道商详页领券组件",
        scenarios = "渠道商详页领券组件",
        host = "",
        authors = "zhaojiangang02"
)
@Controller(value = "channelproductpromodisplay.bin")
@Action(url = "channelproductpromodisplay.bin", httpType = "post", protocol = {ReqProtocol.MAPI, ReqProtocol.REST})
@Slf4j
public class ChannelProductPromoDisplayAction extends DefaultAction<ChannelProductCouponPromoRequest> {

    @Autowired
    private CouponIssueActivityQueryService couponIssueActivityQueryService;

    @Autowired
    private ShopUuidUtils shopUuidUtils;

    @Autowired
    private RestUserInfoService restUserInfoService;

    @Override
    protected IMobileResponse validate(ChannelProductCouponPromoRequest request, IMobileContext context) {
        try {
            Validate.notNull(request, "null request");
            Validate.isTrue(request.getProductid() != null, "productId is null");
            Validate.isTrue(request.getChannel() != null && ChannelProductEnum.getByDesc(request.getChannel()) != null, "invalid channel");
            Long requestShopid = request.getShopid();
            String shopuuid = request.getShopuuid();

            if (requestShopid == null || requestShopid <= 0) {
                if (shopUuidUtils.isShopUuid(shopuuid)) {
                    Long shopId = shopUuidUtils.toShopId(shopuuid);
                    request.setShopid(shopId);
                }
            }
            //Validate.isTrue(request.getShopid() != null && request.getShopid() > 0, "invalid shopId");
            //其他参数均使用内置校验
        } catch (Exception e) {
            log.warn("validate request exception!", e);
            return new CommonMobileResponse(new IssueCouponMsg(401, e.getMessage(), e.getMessage()));
        }
        return null;
    }

    @Override
    protected IMobileResponse execute(ChannelProductCouponPromoRequest request, IMobileContext context) {
        PromotionRequestContext promotionRequestContext;
        try {
            if (Lion.getBooleanValue(LionConstants.CHANNEL_PRODUCT_PROMO_DISPLAY_SWITCH, false)) {
                return new CommonMobileResponse(new ProductPromoInfo());
            }
            // 如果是h5场景
            if (!ApiUtil.isMapiRequest(context)) {
                RestUserInfo restUserInfo = restUserInfoService.handleMobileContext(context);
                promotionRequestContext = IssueActivityMapper.buildHttpPromotionRequestContext(request, context, restUserInfo);
            } else {
                promotionRequestContext = IssueActivityMapper.buildNativePromotionRequestContext(request, context);
            }
            promotionRequestContext.setNeedResetUserId(true);
            CouponActivityContext couponActivityContext = couponIssueActivityQueryService.queryShopPromotions(promotionRequestContext, context);
            ProductPromoInfo productPromoInfo = ProductPromoInfoMapper.buildProductPromoInfo(couponActivityContext, context);
            if (Lion.getBooleanValue(LionConstants.CHANNEL_PRODUCT_LOG_PRINT_SWITCH, true)) {
                log.info("ChannelProductPromoDisplay# ctx: {}, response:{}", couponActivityContext, productPromoInfo);
            }
            return new CommonMobileResponse(productPromoInfo);
        } catch (Exception e) {
            log.error("ChannelProductPromoDisplay# error. request: {}", request, e);
            return new CommonMobileResponse(new ProductPromoInfo());
        }
    }

    @Override
    protected List<ClientInfoRule> getRule() {
        return null;
    }
}
