package com.dianping.api.picasso.controller;

import com.dianping.mobile.base.datatypes.bean.ClientInfoRule;
import com.dianping.mobile.framework.action.DefaultAction;
import com.dianping.mobile.framework.annotation.Action;
import com.dianping.mobile.framework.datatypes.CommonMobileResponse;
import com.dianping.mobile.framework.datatypes.IMobileContext;
import com.dianping.mobile.framework.datatypes.IMobileResponse;
import com.dianping.mobile.framework.enums.ReqProtocol;
import com.dianping.pay.api.biz.NewIssueMultiCouponBiz;
import com.dianping.pay.api.entity.issuecoupon.NewIssueMultiCouponRequest;
import com.dianping.pay.api.entity.issuecoupon.NewIssueMultiCouponResp;
import com.dianping.pay.api.entity.issuecoupon.UnifiedIssueMultiCouponResp;
import com.dianping.pay.api.entity.issuecoupon.UnifiedIssueMultiCouponRespDo;
import com.dianping.pay.api.exception.IssueCouponException;
import java.util.List;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;

@Controller("promo/newissuemulticouponaction.bin")
@Action(url = "promo/newissuemulticouponaction.bin", protocol = ReqProtocol.REST, httpType = "post")
@Slf4j
public class NewIssueMultiCouponAction extends DefaultAction<NewIssueMultiCouponRequest> {

    @Autowired
    private NewIssueMultiCouponBiz newIssueMultiCouponBiz;

    @Override protected IMobileResponse validate(NewIssueMultiCouponRequest request, IMobileContext context) {
        return newIssueMultiCouponBiz.validate(request, context);
    }

    @Override protected IMobileResponse execute(NewIssueMultiCouponRequest request, IMobileContext context) {
        try {
            return new CommonMobileResponse(newIssueMultiCouponBiz.doIssueMultiCoupon(request, context));
        } catch (IssueCouponException e) {
            log.error("NewIssueMultiCouponAction ex, request:{}", request, e);
            NewIssueMultiCouponResp newIssueMultiCouponResp = new NewIssueMultiCouponResp();
            newIssueMultiCouponResp.setBeSuccess(false);
            newIssueMultiCouponResp.setErrorMsg(e.getMessage());
            return new CommonMobileResponse(newIssueMultiCouponResp);
        }
    }

    @Override protected List<ClientInfoRule> getRule() {
        return null;
    }
}
