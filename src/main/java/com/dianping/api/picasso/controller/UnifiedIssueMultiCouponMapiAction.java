package com.dianping.api.picasso.controller;

import com.dianping.mobile.base.datatypes.bean.ClientInfoRule;
import com.dianping.mobile.framework.action.DefaultAction;
import com.dianping.mobile.framework.annotation.Action;
import com.dianping.mobile.framework.datatypes.CommonMobileResponse;
import com.dianping.mobile.framework.datatypes.IMobileContext;
import com.dianping.mobile.framework.datatypes.IMobileResponse;
import com.dianping.mobile.framework.enums.ReqProtocol;
import com.dianping.pay.api.biz.UnifiedIssueMultiCouponBiz;
import com.dianping.pay.api.entity.issuecoupon.IssueDetailResult;
import com.dianping.pay.api.entity.issuecoupon.UnifiedIssueDetailResult;
import com.dianping.pay.api.entity.issuecoupon.UnifiedIssueMultiCouponResp;
import com.dianping.pay.api.entity.issuecoupon.UnifiedIssueMultiCouponRespDo;
import com.dianping.pay.api.entity.issuecoupon.UnifiedissuemulticouponRequest;
import com.dianping.pay.api.exception.IssueCouponException;
import com.dianping.pay.api.util.DateUtils;
import com.google.common.collect.Lists;
import com.meituan.mtrace.Tracer;
import com.meituan.servicecatalog.api.annotations.InterfaceDoc;
import org.apache.commons.collections.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import com.dianping.cat.Cat;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/7/3
 */
@InterfaceDoc(
        displayName = "领券组件-用户领券",
        type = "restful",
        scenarios = "领券中心-用户领券 用户在团详页/商详页/..等领券组件中点击领券时触发的领券行为",
        description = "领券中心-用户领券 用户在团详页/商详页/..等领券组件中点击领券时触发的领券行为",
        host = "http://mapi.51ping.com/pay"
)
@org.springframework.stereotype.Controller("promo/unifiedissuemulticouponmapi.bin")
@Action(url = "promo/unifiedissuemulticouponmapi.bin", protocol = {ReqProtocol.MAPI, ReqProtocol.REST}, httpType = "post")
public class UnifiedIssueMultiCouponMapiAction extends DefaultAction<UnifiedissuemulticouponRequest> {

    public static final Logger logger = LoggerFactory.getLogger(UnifiedIssueMultiCouponMapiAction.class);

    @Autowired
    private UnifiedIssueMultiCouponBiz unifiedIssueMultiCouponBiz;

    @Override
    protected IMobileResponse validate(UnifiedissuemulticouponRequest request, IMobileContext iMobileContext) {
        // 入口逻辑兼容
        if (request.getProductid() == null || request.getProductid() == 0) {
            if (request.getLongProductId() != null && request.getLongProductId() > 0) {
                request.setProductid(request.getLongProductId().intValue());
            }
        } else if (request.getProductid() > 0 && request.getLongProductId() == null) {
            request.setLongProductId(Long.valueOf(request.getProductid()));
            //对未完成改造的客户端做打点，便于跟进进度
            Cat.logEvent("ProductIdNotUpgrated", Tracer.getRemoteAppKey() + ":promo/unifiedissuemulticouponmapi.bin");
        }
        return unifiedIssueMultiCouponBiz.validate(request, iMobileContext);
    }

    @Override
    protected IMobileResponse execute(UnifiedissuemulticouponRequest request, IMobileContext iMobileContext) {
        try {
            UnifiedIssueMultiCouponRespDo unifiedIssueMultiCouponRespDo = unifiedIssueMultiCouponBiz.doMultiIssueCoupon(request, iMobileContext);
            if (unifiedIssueMultiCouponRespDo == null) {
                return new CommonMobileResponse(null);
            }

            return new CommonMobileResponse(convertToIssueDetailResult(unifiedIssueMultiCouponRespDo));
        } catch (IssueCouponException e) {
            logger.error("UnifiedIssueMultiCouponMapiAction ex, request:{}", request, e);
            UnifiedIssueMultiCouponResp unifiedIssueMultiCouponResp = new UnifiedIssueMultiCouponResp();
            unifiedIssueMultiCouponResp.setBeSuccess(false);
            unifiedIssueMultiCouponResp.setErrorMsg(e.getMessage());
            return new CommonMobileResponse(unifiedIssueMultiCouponResp);
        }
    }

    private UnifiedIssueMultiCouponResp convertToIssueDetailResult(UnifiedIssueMultiCouponRespDo unifiedIssueMultiCouponRespDo) {
        UnifiedIssueMultiCouponResp unifiedIssueMultiCouponResp = new UnifiedIssueMultiCouponResp();
        unifiedIssueMultiCouponResp.setBeSuccess(unifiedIssueMultiCouponRespDo.isSuccess());
        unifiedIssueMultiCouponResp.setErrorMsg(unifiedIssueMultiCouponRespDo.getErrorMsg());
        if (CollectionUtils.isNotEmpty(unifiedIssueMultiCouponRespDo.getIssueDetail())) {
            List<IssueDetailResult> issueDetailResults = Lists.newArrayList();
            unifiedIssueMultiCouponResp.setIssueDetail(issueDetailResults);
            for (UnifiedIssueDetailResult issueDetailResult : unifiedIssueMultiCouponRespDo.getIssueDetail()) {
                IssueDetailResult issueDetail = new IssueDetailResult();
                issueDetail.setBeSuccess(issueDetailResult.isSuccess());
                issueDetail.setResultMessage(issueDetailResult.getResultMessage());
                issueDetail.setUnifiedCouponGroupId(issueDetailResult.getUnifiedCouponGroupId());
                issueDetail.setUnifiedCouponId(issueDetailResult.getUnifiedCouponId());
                issueDetail.setBeginTime(DateUtils.dateToStr(issueDetailResult.getBeginTime()));
                issueDetail.setEndTime(DateUtils.dateToStr(issueDetailResult.getEndTime()));
                issueDetail.setCouponGroupName(issueDetailResult.getCouponGroupName());
                issueDetail.setUseUrl(issueDetailResult.getUseUrl());
                issueDetail.setToastMsg(issueDetailResult.getToastMsg());
                issueDetailResults.add(issueDetail);
            }
        }
        return unifiedIssueMultiCouponResp;
    }

    @Override
    protected List<ClientInfoRule> getRule() {
        return null;
    }
}
