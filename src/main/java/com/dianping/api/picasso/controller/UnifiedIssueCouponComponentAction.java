package com.dianping.api.picasso.controller;

import com.dianping.api.common.enums.ProductTypeEnum;
import com.dianping.api.picasso.CouponPlatformUtils;
import com.dianping.api.util.ApiUtil;
import com.dianping.api.util.CrossDomainUtils;
import com.dianping.api.util.DotUtils;
import com.dianping.gmkt.coupon.common.api.enums.MtCouponPlatform;
import com.dianping.lion.client.Lion;
import com.dianping.mobile.base.datatypes.bean.ClientInfoRule;
import com.dianping.mobile.framework.action.DefaultAction;
import com.dianping.mobile.framework.annotation.Action;
import com.dianping.mobile.framework.datatypes.CommonMobileResponse;
import com.dianping.mobile.framework.datatypes.IMobileContext;
import com.dianping.mobile.framework.datatypes.IMobileResponse;
import com.dianping.mobile.framework.enums.ReqProtocol;
import com.dianping.pay.api.beans.IssueCouponActivity;
import com.dianping.pay.api.beans.PlatformCoupon;
import com.dianping.pay.api.biz.activity.RestUserInfoService;
import com.dianping.pay.api.biz.activity.UnifiedIssueCouponBiz;
import com.dianping.pay.api.biz.activity.newloader.CouponIssueActivityQueryService;
import com.dianping.pay.api.biz.activity.newloader.PlatformCouponQuerySerivce;
import com.dianping.pay.api.biz.activity.newloader.dto.CouponIssueActivityQueryContext;
import com.dianping.pay.api.biz.activity.newloader.dto.RestUserInfo;
import com.dianping.pay.api.biz.activity.newloader.mapper.IssueActivityMapper;
import com.dianping.pay.api.entity.issuecoupon.IssueCouponMsg;
import com.dianping.pay.api.entity.issuecoupon.UnifiedIssueCouponComponentDo;
import com.dianping.pay.api.entity.issuecoupon.UnifiedissuecouponcomponentRequest;
import com.dianping.pay.api.enums.RequestTypeConstant;
import com.dianping.pay.framework.utils.shopUuid.ShopUuidUtils;
import com.dianping.pay.promo.common.enums.User;
import com.google.common.collect.Lists;
import com.meituan.servicecatalog.api.annotations.*;
import org.apache.commons.lang.Validate;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

/**
 * Created by drintu on 18/6/6.
 */

@InterfaceDoc(displayName = "领券组件-优惠券展示",
        type = "restful",
        description = "用户在进入团详页/商详页/..等页面时，展示符合条件的优惠券信息",
        scenarios = "用户在进入团详页/商详页/..等页面时，展示符合条件的优惠券信息",
        host = "http://mapi.dianping.com/pay/promo/"
)
@org.springframework.stereotype.Controller("promo/unifiedissuecouponcomponent.bin")
@Action(url = "promo/unifiedissuecouponcomponent.bin", protocol = ReqProtocol.REST, httpType = "post")
public class UnifiedIssueCouponComponentAction extends DefaultAction<UnifiedissuecouponcomponentRequest> {
    private static final Logger LOGGER = LogManager.getLogger(UnifiedIssueCouponComponentAction.class);
    @Autowired
    private CouponIssueActivityQueryService couponIssueActivityQueryService;
    @Autowired
    private UnifiedIssueCouponBiz unifiedIssueCouponBiz;

    @Autowired
    private RestUserInfoService restUserInfoService;

    @Autowired
    private PlatformCouponQuerySerivce platformCouponQuerySerivce;

    @Resource
    private ShopUuidUtils shopUuidUtils;

    @Override
    protected IMobileResponse validate(UnifiedissuecouponcomponentRequest request, IMobileContext iMobileContext) {
        try {
            if (iMobileContext.getRequest() != null) {
                DotUtils.addRefererDot(iMobileContext.getRequest());
            }
            shopUuidUtils.prepareUuidInRequest(request, ApiUtil.isMapiRequest(iMobileContext));
            Validate.notNull(request, "null request");
            Validate.isTrue((request.getShopidL()!=null && request.getShopidL() > 0)
                    || (request.getProductid()!=null && request.getProductid() > 0)
                    , "invalid shopId/productId");
            if(request.getProductid() != null && request.getProductid() >0){
                if(request.getProducttype() == null){
                    //producttype没有设置的情况下默认是团购
                    request.setProducttype(ProductTypeEnum.DEAL.code);
                }
                Validate.isTrue(request.getProducttype() == ProductTypeEnum.DEAL.code
                        || request.getProducttype() == ProductTypeEnum.SKU.code, "producttype invalid");
            }
        } catch (IllegalArgumentException e) {
            LOGGER.warn(String.format("invalid request: %s", e.getMessage()));
            return new CommonMobileResponse(new IssueCouponMsg(401, e.getMessage(), e.getMessage()));
        }
        return null;
    }

    @MethodDoc(requestMethods = HttpMethod.POST,
            urls = "unifiedissuecouponcomponent.bin",
            displayName = "用户在进入团详页/商详页/..等页面时，展示符合条件的优惠券信息",
            description = "用户在进入团详页/商详页/..等页面时，展示符合条件的优惠券信息",
            returnValueDescription = "优惠券信息",
            restExampleUrl = "https://mapi.51ping.com/pay/promo/unifiedissuecouponcomponent.bin",
            parameters = {
                    @ParamDoc(
                            name = "request",
                            description = "unifiedissuecouponcomponent.bin请求参数",
                            type = UnifiedissuecouponcomponentRequest.class,
                            paramType = ParamType.REQUEST_BODY
                    ),
                    @ParamDoc(
                            name = "context",
                            description = "mapi-shell协议上下文",
                            type = IMobileContext.class,
                            paramType = ParamType.REQUEST_BODY
                    )
            },
            responseParams = {@ParamDoc(name = "iMobileResponse", description = "通用返回数据模型")},
            restExamplePostData = "{\n" +
                    "    \"productid\": \"101308415\",\n" +
                    "    \"cx\": \"i2HKpOmsirDPavelVfQBZKStIAhhP0jrlcWNEO0p7xzLqo3ks4DtKZUXEmFqIBCckKqww8m5UAA1ZYSIpyZjeoLCCGAGBaYGOw2DbajCci4LOm6vt4v4g1mfbhjnbo7WcGHHlR7EFR70uuxNOD%2Bi3YJXL8yt2WTp45PzvizXk1Lv36bW3ji3FltdW%2BTMlNR6xACYZZOJYHiUwbC5Iv2%2BQFZ6enSyBTJrR3cHF6XE97//MOpaaeuJlxIv%2BzELn5x9Uh7%2BbX300dloWEoX608MBaRMAhXbo/zSpynAyZuVUfcMn3b4IuspEIgPlFpKIfY4urULt/CFlPvZsPfP6mitE5KSJAd6FGxLIZz3zC452J3SNUzwLayTDubI2fPNaFcL32knqGYgsVljnymSjVQo6AJkuICcKAUw5htYBNhfhY3lWqVCiKxqBQOr5vfU5VgPDWVlyRodr35ter2UhGhRjiTskqIbBVBVbOE577T9B3OwbercnHm%2B7FC3l6QnHOWV7PU53V4AwRx9m6/sJ0jnklflp9bTYKU19WQuexvznMFv581FGqzjBYYzkd82drULp9WmaqR2GLqMP3grpgDu6hiTEipQccMm5OZ%2B2UUGE50cts1gVqSvRh71zYGC/hY20PGMoBqgQHZ7wZoiDPnBXx1zDw0lKVGY1IQzBXSMxoc3FFXAXn0GFvgCD7PmvRNfsWiDFl18OKzoQcRmyMTtUWAHF/RlVNOuM9MfikBZiHNcOa3LB0gm4GLqDRPeM%2BIAacI3F5ViRgeY0F9YMCHKfqWX41BZFY3sVTfWEIv1tQFtuf2kQhflo8xSNpWuxrV%2BXVuFnYlMVvBOISrI3TUpJTGZ/KAylUo2e2I/lB/tizqKfGFBQOAvRHW17tbkxwiuFBTsJNduEoK%2B27YaZ5rq8r7ySTYeK2optUrXp3tWsLPdsGbzxwisNMyztiFUFMxUbKCekL5aU81nqErmJe8/6dRz%2B1gDKvRCg2RZ/R5JUzWcPL1ZVe%2BKUS6Xxs46A9Jc06KGr20fAulTP837NMgtGj56IsU/7C0BALehiUh1EN/1Zt/giaCrEfAEHS9mNhLqmCpXLhJ4wxQMtFswTcFVSKAsenfxCHT2XMhEbvWZI97ccKywTlk1QB6iL9AL7tGGIh6/fjIiadJnSdz0Sy0zTvVcNkaLAU8OzpfZvxIaC6K103moyARkSzIZA5fI6gwojIBp1oj8gWqro8ByMMNpLag19Ma1w8loVk3vyP%2BwQFBB06mhHfkQQGmFURePLP68hmxuyC0d5s%2BfvOllnf7rbGVENHSLhpcIXmCkvF9LUPVrYhORiXQqisOrxnRM4TQtinoAVf8bBshsyiNI%2B9lSP7ntcWXx6lQXmnc33FCBQDgcPOtyqWLLZvQpjvSp/dWTXMHmNCEUInDfXLS9pWaNIGPLgwCS8wy24yQDQMcRPWw%3D\",\n" +
                    "    \"cityid\": \"10\",\n" +
                    "    \"producttype\": \"1\"\n" +
                    "}",
            restExampleResponseData = "{\n" +
                    "  \"unifiedIssueCouponLists\": [\n" +
                    "    {\n" +
                    "      \"title\": \"店铺抵用券\",\n" +
                    "      \"unifiedIssueCouponOption\": [\n" +
                    "        {\n" +
                    "          \"couponGroupId\": 479131492,\n" +
                    "          \"unifiedCouponGroupId\": \"479131492\",\n" +
                    "          \"desc\": \"领取后7天有效\",\n" +
                    "          \"amount\": 22,\n" +
                    "          \"priceLimit\": 0,\n" +
                    "          \"isPlatformCoupon\": false,\n" +
                    "          \"amountDesc\": \"无门槛\",\n" +
                    "          \"title\": \"限【团购】指定商品\",\n" +
                    "          \"issuedIcon\": \"https://www.dpfile.com/app/pay-promo-static-pic/js/dp-issued-3x.d0dce73c749958bd80cd9995ea1fb9ef.png\",\n" +
                    "          \"issueButtonText\": \"立即领取\",\n" +
                    "          \"status\": {\n" +
                    "            \"status\": 0,\n" +
                    "            \"comment\": \"可领\"\n" +
                    "          }\n" +
                    "        }\n" +
                    "      ]\n" +
                    "    }\n" +
                    "  ],\n" +
                    "  \"icon\": \"https://www.dpfile.com/app/pay-promo-static-pic/js/dp-coupon-3x.87e3aa8cad0ec2df08c91f3e2ecc886f.png\",\n" +
                    "  \"couponText\": \"团购无门槛减22;团购无门槛减19\",\n" +
                    "  \"entranceText\": \"去领取\"\n" +
                    "}",
            extensions = {
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE",
                            content = "数据鉴权逻辑: 无需鉴权，用户可以查询到商详页/团详页等所有券信息"
                    )
            }
    )
    @Override
    protected IMobileResponse execute(UnifiedissuecouponcomponentRequest request, IMobileContext iMobileContext) {
        CrossDomainUtils.setEnableCrossDomain(iMobileContext.getRequest(), iMobileContext.getResponse());
        CouponIssueActivityQueryContext context = IssueActivityMapper.unifiedIssueComponentRequest2QueryActivityContext(request, iMobileContext);
        processHttpUserInfoAndCouponPlatform(context, iMobileContext,request);

        //平台券查询
        List<PlatformCoupon> platformCouponList = platformCouponQuerySerivce.queryPlatformCoupons(context);

        //查询商家券
        String undesiredShopCouponBUs = Lion.getStringValue("mapi-pay-promo-web.undesired.shopCoupon.BUs", "");
        List<IssueCouponActivity> activities = new ArrayList<>();
        if (!Arrays.asList(undesiredShopCouponBUs.split(",")).contains(String.valueOf(context.getBusinessLine()))) {
            activities = couponIssueActivityQueryService.queryActivities(context);
        }
        UnifiedIssueCouponComponentDo unifiedIssueCouponComponentDo = unifiedIssueCouponBiz.toUnifiedIssueComponent(activities, platformCouponList);
        return new CommonMobileResponse(unifiedIssueCouponComponentDo);
    }

    private void processHttpUserInfoAndCouponPlatform(CouponIssueActivityQueryContext context, IMobileContext iMobileContext, UnifiedissuecouponcomponentRequest request) {
        if (!ApiUtil.isMapiRequest(iMobileContext)) {
            RestUserInfo restUserInfo = restUserInfoService.handleMobileContext(iMobileContext);
            if (restUserInfo != null) {
                DotUtils.addVersionDot(iMobileContext.getVersion(), RequestTypeConstant.H5, restUserInfo.getUserCode() != User.MT.getCode());
                context.setDpClient(restUserInfo.getUserCode() != User.MT.getCode());
                context.setUserId(restUserInfo.getUserId());

                //非mapi请求
                if (request.getCouponPlatform() != null) {
                    if (restUserInfo.getUserCode() == User.MT.getCode()) {
                        if (MtCouponPlatform.transformByPayPlatform(request.getCouponPlatform()) != null) {
                            context.setCouponPlatforms(Lists.newArrayList(MtCouponPlatform.transformByPayPlatform(request.getCouponPlatform()).getCode()));
                        }
                    } else {
                        context.setCouponPlatforms(Lists.newArrayList(request.getCouponPlatform()));
                    }
                }
            }
        } else {
            //mapi请求
            DotUtils.addVersionDot(iMobileContext.getVersion(), RequestTypeConstant.NATIVE, iMobileContext.isMtClient());
            context.setCouponPlatforms(Lists.newArrayList(CouponPlatformUtils.getCouponPlatform(iMobileContext)));
        }
    }

    @Override
    protected List<ClientInfoRule> getRule() {
        return null;
    }
}
