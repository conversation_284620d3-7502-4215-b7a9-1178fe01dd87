package com.dianping.api.picasso.controller;

import com.dianping.api.util.ApiUtil;
import com.dianping.api.util.DotUtils;
import com.dianping.cat.Cat;
import com.dianping.combiz.spring.util.PropertiesLoaderSupportUtils;
import com.dianping.mobile.base.datatypes.bean.ClientInfoRule;
import com.dianping.mobile.framework.action.DefaultAction;
import com.dianping.mobile.framework.annotation.Action;
import com.dianping.mobile.framework.base.datatypes.SimpleMsg;
import com.dianping.mobile.framework.base.datatypes.StatusCode;
import com.dianping.mobile.framework.datatypes.CommonMobileResponse;
import com.dianping.mobile.framework.datatypes.IMobileContext;
import com.dianping.mobile.framework.datatypes.IMobileResponse;
import com.dianping.mobile.framework.enums.ReqProtocol;
import com.dianping.pay.api.beans.IssueCouponActivity;
import com.dianping.pay.api.biz.activity.IssueCouponBiz;
import com.dianping.pay.api.biz.activity.RestUserInfoService;
import com.dianping.pay.api.biz.activity.newloader.CouponIssueActivityQueryService;
import com.dianping.pay.api.biz.activity.newloader.dto.RestUserInfo;
import com.dianping.pay.api.entity.issuecoupon.BeautyIssueCouponComponent;
import com.dianping.pay.api.entity.issuecoupon.IssueCouponRequest;
import com.dianping.pay.api.enums.IssueCouponOptionType;
import com.dianping.pay.api.enums.RequestTypeConstant;
import com.dianping.pay.framework.utils.shopUuid.ShopUuidUtils;
import com.dianping.pay.promo.common.enums.User;
import com.dianping.violet.enums.CouponRequestTypeEnum;
import com.google.common.base.Predicate;
import com.google.common.collect.Collections2;
import com.google.common.collect.Lists;
import com.meituan.mtrace.Tracer;
import com.meituan.servicecatalog.api.annotations.ExtensionDoc;
import com.meituan.servicecatalog.api.annotations.HttpMethod;
import com.meituan.servicecatalog.api.annotations.InterfaceDoc;
import com.meituan.servicecatalog.api.annotations.MethodDoc;
import com.meituan.servicecatalog.api.annotations.ParamDoc;
import com.meituan.servicecatalog.api.annotations.ParamType;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang.Validate;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;

import javax.annotation.Resource;
import java.util.List;

/**
 * Created by zhaochen on 2018/4/17.
 */

@InterfaceDoc(displayName = "丽人领券组件展示接口",
        type = "restful",
        description = "用户在进入丽人团详页/商详页/..等页面时，展示商家或是平台运营配置的优惠券信息",
        scenarios = "用户在进入丽人团详页/商详页/..等页面时，展示商家或是平台运营优惠券信息",
        host = "http://mapi.dianping.com/pay/promo/"
)
@Controller("promo/beautyissuecouponcomponent.bin")
@Action(url = "promo/beautyissuecouponcomponent.bin", protocol = ReqProtocol.REST, httpType = "post")
public class BeautyIssueCouponPicassoAction extends DefaultAction<IssueCouponRequest> {

    private static final Logger LOGGER = LoggerFactory.getLogger(BeautyIssueCouponPicassoAction.class);

    @Autowired
    private IssueCouponBiz issueCouponBiz;

    @Autowired
    private CouponIssueActivityQueryService couponIssueActivityQueryService;

    @Resource
    private ShopUuidUtils shopUuidUtils;

    @Autowired
    private RestUserInfoService restUserInfoService;

    @Override
    protected IMobileResponse validate(IssueCouponRequest request, IMobileContext context) {
        try {
            if (context.getRequest() != null) {
                DotUtils.addRefererDot(context.getRequest());
            }
            //目前该接口只支持mapi请求，不考虑h5
            if (ApiUtil.isMapiRequest(context)) {
                DotUtils.addVersionDot(context.getVersion(), RequestTypeConstant.NATIVE, context.isMtClient());
            }
            Validate.notNull(request, "null request");
            shopUuidUtils.prepareUuidInRequest(request, ApiUtil.isMapiRequest(context));
            
            // 兼容性处理：如果productId为null或者0，但longProductId有值，则将longProductId的值赋给productId
            Integer productId = request.getProductId();
            Long longProductId = request.getLongProductId();
            // 入口逻辑兼容
            if (request.getProductId() == 0 && request.getLongProductId() != null) {
                request.setProductId((int) request.getLongProductId().longValue());
            } else if (request.getProductId() > 0 && request.getLongProductId() == null) {
                request.setLongProductId((long) request.getProductId());
                //对未完成改造的客户端做打点，便于跟进进度
                Cat.logEvent("ProductSkuIdNotUpgraded", Tracer.getRemoteAppKey() + ":beautyissuecouponcomponent.bin");
            }
            if (request.getCouponOptionId() == 0 && request.getStringCouponOptionId() != null) {
                request.setCouponOptionId(Integer.parseInt(request.getStringCouponOptionId()));
            } else if (request.getCouponOptionId() > 0 && request.getStringCouponOptionId() == null) {
                request.setStringCouponOptionId(request.getCouponOptionId() + "");
                //对未完成改造的客户端做打点，便于跟进进度
                Cat.logEvent("CouponGroupIdNotUpgraded", Tracer.getRemoteAppKey() + ":beautyissuecouponcomponent.bin");
            }
            
            Validate.isTrue(request.getShopIdL() > 0
                    || StringUtils.isNotBlank(request.getShopUuid())
                    || (productId != null && productId > 0)
                    || (longProductId != null && longProductId > 0)
                    || CollectionUtils.isNotEmpty(request.getShopIdLList()), "invalid shopId/productId/longProductId/shopIdList");
        } catch (IllegalArgumentException e) {
            LOGGER.warn(String.format("invalid request: %s", e.getMessage()));
            return new CommonMobileResponse(new SimpleMsg("出错了", "请求错误", StatusCode.PARAMERROR));
        }
        return null;
    }

    private void processHttpUserInfo(IssueCouponRequest issueCouponRequest, IMobileContext iMobileContext) {
        if (!ApiUtil.isMapiRequest(iMobileContext)) {
            RestUserInfo restUserInfo = restUserInfoService.handleMobileContext(iMobileContext);
            if (restUserInfo != null) {
                boolean isDpClient = restUserInfo.getUserCode() != User.MT.getCode();
                issueCouponRequest.setDpClient(isDpClient);
                issueCouponRequest.setUserId(restUserInfo.getUserId());
            }
        }
    }

    @MethodDoc(requestMethods = HttpMethod.POST,
            urls = "beautyissuecouponcomponent.bin",
            displayName = "丽人领券组件展示接口",
            description = "用户在进入丽人团详页/商详页/..等页面时，展示优惠券信息",
            returnValueDescription = "丽人团详页/商详页...配置活动券信息",
            restExampleUrl = "https://mapi.51ping.com/pay/promo/beautyissuecouponcomponent.bin",
            parameters = {
                    @ParamDoc(
                            name = "request",
                            description = "请求参数",
                            type = IssueCouponRequest.class,
                            paramType = ParamType.REQUEST_BODY
                    ),
                    @ParamDoc(
                            name = "context",
                            description = "mapi-shell协议上下文",
                            type = IMobileContext.class,
                            paramType = ParamType.REQUEST_BODY
                    )
            },
            responseParams = {@ParamDoc(name = "iMobileResponse", description = "通用返回数据模型")},
            restExamplePostData = "{\n" +
                    "    \"productid\": \"101294101\",\n" +
                    "    \"cx\": \"i2HKpOmsirDPavelVfQBZCAheya8XusP1SqR9nrNBjW8c5nwI8kftebo343SCYw81mqGbiKhHwzlkLOlPSSEemCZqHmkO0tQ5mEuhdpg%2BvJRWCBnhiqEgxybvQDeTVzc3xfjr4QU9PFql%2BIqDzTuYvQjTy7aGLMdw3qaSqiKHvoid8n4DWID7U9iohXIWLA64CPjhcpm73abZFuvW/nnFpmp10Shs%2BNdiI4Y6P9DyQ%2BZIznGjkRHJzlejLvn9OK/peROrhcAlWMa8ywmbDtDs5QkRFlt0UYDPj%2B9w61BvKQOLfnryE1BnVA1jrZl82rC/MknN5Oj1hbtBZM%2BxH3HC1RVLue02uIH5iL3Ha5kGc2V9utS4%2BCtGRKTiOmCxfmdBN0tFz4iasWS1ot50Vh3Rg3NDH/eqG5rM2A6WzQbjxkDqvVOCFv0BQWwpvB8sgpFdK6cEeDZFUQ8%2B0opTvjw8evQ54a5hY48f6zGHEyXddJE/p71PeCjPY0WbX4TRUOoycyLPEdGDFPeMDyNk2jt9uEC61Xw6d/53CgVV5IoXia%2BgwWrXQDNOCKZ9JpRISm2B5s9dVV/a1TqIhFMtPo8Fm1J4WKTBf7XyI3%2BXsQAFuW0tsbITkr3TJg2uldAGOPF0FkDzimnaYJJCXGsXVvb5GVKKvitpQ8RQl%2BdDsqSIvwb/qCjlzDO3fTDLXNmjDLKdSZ4cVgm/Qu2w/ZgfOqaJJIZ%2BsAjmOMMvFKPvWGCWPcXfbBf7yaFUDIH0z0Q5x0oqNsi9I9qHMnQPnpaVegjNEhXHlnzyu4HkwGs34VXLBibH%2BhSSnotJIjiC1HXOXq77fAUwnAtBn9CHS6RVeozSySULwucKR/aLs2H6WqFteJxrnr/piYG4xKprVNH3KsWEZe%2BDYmuO0WTyj4vG1PwIZOGWMcqAyLGUYfAjarazUJTZejhwQ%2B6xipHVkUSC6PPLP/5xMdRbIkF25bf9AVA4RmTmkA7H6BKkOfF%2BYE2ZLQzK/HenGjn%2BPE/D7AvpyfSSc1A6RiKimK20Ocm9HM3iNf9%2BNgXVNyNJl5uV%2Bd55%2BAz30Uy9CC931MV8TLbWEx9D2y9%2BGLYXRbSSelibVe6sIj4JQuAGPt9WyLOZpZYryg8kMCutPCm9pg0mF4UAW4wky4K5A%2BSZRBCQiEpG4HsL0E9R/KIlyncUzZ2MquCFi5CKkgT6%2B2b4cQRR/us2xgXY5Jjmq6EE%2BbOQskPXOo4t0abCWp5fAqGEWjNpDcbaWEV7PiKooEi0nXiA7Y7k91UkrM1jQvnudk49F9qcOPsaUcsHj66bsEQwnHuVXVNirwfJslTDLf7rzKLrotwPf2d0wdVh5XiwrgIXDgY%2BPuelJIk/wgefkYj8l06yaxsXiyIiexwuGE/ejuiIvYmArlNxpf81Gch4%2BdghX8/VsKj49BzSeTyRn5tq%2BxLuO09BmY%3D\",\n" +
                    "    \"cityid\": \"10\"\n" +
                    "}",
            restExampleResponseData = "{\n" +
                    "    \"BeautyShopCouponTag\": \"店铺抵用券\",\n" +
                    "    \"BeautyPlatformCouponTag\": null,\n" +
                    "    \"BeautyShopSecondTag\": \"优先抵用尾款，尾款全部抵用后，可继续抵用预付金\",\n" +
                    "    \"BeautyCouponTagList\": [\n" +
                    "        {\n" +
                    "            \"__name\": \"BeautyIssueCouponTag\",\n" +
                    "            \"icon\": \"https://p1.meituan.net/beautyimg/d29be4ad5e8b68eaa3574ac630bcaca64177.png\",\n" +
                    "            \"tag\": \"团购无门槛减16,无门槛减12,满111减18,满199减13\"\n" +
                    "        }\n" +
                    "    ],\n" +
                    "    \"BeautyShopCouponOptionList\": [\n" +
                    "        {\n" +
                    "            \"__name\": \"BeautyIssueCouponOption\",\n" +
                    "            \"ID#I\": 0,\n" +
                    "            \"Amount#D\": 16.0,\n" +
                    "            \"Tag\": \"店铺团购抵用券\",\n" +
                    "            \"Title\": \"无门槛使用\",\n" +
                    "            \"Desc\": \"领取后7天有效，限指定商品\",\n" +
                    "            \"Enable\": true,\n" +
                    "            \"Incompatible\": false,\n" +
                    "            \"IncompatibleRule\": \"\",\n" +
                    "            \"Used\": false,\n" +
                    "            \"CouponGroupId#I\": 0,\n" +
                    "            \"unifiedcoupongroupid\": \"7J3Q7G19b3UmsDM5DKjB\"\n" +
                    "        },\n" +
                    "        {\n" +
                    "            \"__name\": \"BeautyIssueCouponOption\",\n" +
                    "            \"ID#I\": 313741671,\n" +
                    "            \"Amount#D\": 12.0,\n" +
                    "            \"Tag\": \"店铺团购抵用券\",\n" +
                    "            \"Title\": \"无门槛使用\",\n" +
                    "            \"Desc\": \"领取后7天有效，限指定商品\",\n" +
                    "            \"Enable\": true,\n" +
                    "            \"Incompatible\": false,\n" +
                    "            \"IncompatibleRule\": \"\",\n" +
                    "            \"Used\": false,\n" +
                    "            \"CouponGroupId#I\": 0,\n" +
                    "            \"unifiedcoupongroupid\": \"313741671\"\n" +
                    "        },\n" +
                    "        {\n" +
                    "            \"__name\": \"BeautyIssueCouponOption\",\n" +
                    "            \"ID#I\": 0,\n" +
                    "            \"Amount#D\": 18.0,\n" +
                    "            \"Tag\": \"店铺团购抵用券\",\n" +
                    "            \"Title\": \"满111可用\",\n" +
                    "            \"Desc\": \"领取后7天有效，限指定商品\",\n" +
                    "            \"Enable\": true,\n" +
                    "            \"Incompatible\": false,\n" +
                    "            \"IncompatibleRule\": \"\",\n" +
                    "            \"Used\": false,\n" +
                    "            \"CouponGroupId#I\": 0,\n" +
                    "            \"unifiedcoupongroupid\": \"7r4M6U1752aACiPYPdpQ\"\n" +
                    "        },\n" +
                    "        {\n" +
                    "            \"__name\": \"BeautyIssueCouponOption\",\n" +
                    "            \"ID#I\": 637876713,\n" +
                    "            \"Amount#D\": 13.0,\n" +
                    "            \"Tag\": \"店铺团购抵用券\",\n" +
                    "            \"Title\": \"满199可用\",\n" +
                    "            \"Desc\": \"领取后7天有效，限指定商品\",\n" +
                    "            \"Enable\": true,\n" +
                    "            \"Incompatible\": false,\n" +
                    "            \"IncompatibleRule\": \"\",\n" +
                    "            \"Used\": false,\n" +
                    "            \"CouponGroupId#I\": 0,\n" +
                    "            \"unifiedcoupongroupid\": \"637876713\"\n" +
                    "        }\n" +
                    "    ],\n" +
                    "    \"BeautyPlatformCouponOptionList\": []\n" +
                    "}",
            extensions = {
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE",
                            content = "数据鉴权逻辑: 无需鉴权，用户可以查询到商详页/团详页等所有券信息"
                    )
            }
    )
    @Override
    protected IMobileResponse execute(IssueCouponRequest request, IMobileContext context) {
        try {
            if (!PropertiesLoaderSupportUtils.getBoolProperty("pay-api-mobile.issuecouponcomponent.switch", true)) {
                return new CommonMobileResponse(new SimpleMsg("出错了", "开关没开", StatusCode.ERROR));
            }
            IssueCouponRequest issueCouponRequest = new IssueCouponRequest(request, context, CouponRequestTypeEnum.DISPLAY.getCode());
            processHttpUserInfo(issueCouponRequest, context);

            log.info(String.format("isdpclient=%s, token=%s, userid=%s",
                    issueCouponRequest.isDpClient(),
                    context.getHeader().getToken(),
                    issueCouponRequest.getUserId()));
            List<IssueCouponActivity> activities = Lists.newArrayList(Collections2.filter(
                    couponIssueActivityQueryService.queryActivities(issueCouponRequest),
                    new Predicate<IssueCouponActivity>() {
                        @Override
                        public boolean apply(IssueCouponActivity activity) {
                            return activity.getType() != IssueCouponOptionType.DP_PLATFORM_EVENT.getCode()
                                    && activity.getType() != IssueCouponOptionType.DP_SHANHUI_EVENT.getCode();
                        }
                    })
            );
            BeautyIssueCouponComponent beautyIssueCouponComponent = issueCouponBiz.toBeautyIssueCouponComponent(activities);
            DotUtils.addCouponComponentQueryResultEvent("beautyissuecouponcomponent", couponListIsNotEmpty(beautyIssueCouponComponent));
            return new CommonMobileResponse(beautyIssueCouponComponent);
        } catch (Exception e) {
            return new CommonMobileResponse(new SimpleMsg("出错了", e.getMessage(), StatusCode.PARAMERROR));
        }
    }


    @Override
    protected List<ClientInfoRule> getRule() {
        return null;
    }

    public boolean couponListIsNotEmpty(BeautyIssueCouponComponent beautyIssueCouponComponent) {
        if(beautyIssueCouponComponent != null
            && (CollectionUtils.isNotEmpty(beautyIssueCouponComponent.getBeautyShopCouponOptionList())
            || CollectionUtils.isNotEmpty(beautyIssueCouponComponent.getBeautyPlatformCouponOptionList()))) {
            return true;
        }
        return false;
    }
}
