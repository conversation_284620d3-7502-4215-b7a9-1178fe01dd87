package com.dianping.api.picasso.controller;

import com.dianping.api.util.ApiUtil;
import com.dianping.mobile.base.datatypes.bean.ClientInfoRule;
import com.dianping.mobile.framework.action.DefaultAction;
import com.dianping.mobile.framework.annotation.Action;
import com.dianping.mobile.framework.datatypes.CommonMobileResponse;
import com.dianping.mobile.framework.datatypes.IMobileContext;
import com.dianping.mobile.framework.datatypes.IMobileResponse;
import com.dianping.mobile.framework.enums.ReqProtocol;
import com.dianping.pay.api.biz.activity.RestUserInfoService;
import com.dianping.pay.api.biz.activity.UnifiedIssueCouponBiz;
import com.dianping.pay.api.biz.activity.newloader.CouponIssueActivityQueryService;
import com.dianping.pay.api.biz.activity.newloader.dto.CouponActivityContext;
import com.dianping.pay.api.biz.activity.newloader.dto.CouponIssueActivityQueryContext;
import com.dianping.pay.api.biz.activity.newloader.dto.PromotionRequestContext;
import com.dianping.pay.api.biz.activity.newloader.dto.RestUserInfo;
import com.dianping.pay.api.biz.activity.newloader.mapper.IssueActivityMapper;
import com.dianping.pay.api.biz.activity.newloader.mapper.ProductPromoInfoMapper;
import com.dianping.pay.api.entity.issuecoupon.*;
import com.dianping.pay.api.exception.IssueCouponException;
import com.dianping.pay.api.util.PoiIdUtils;
import com.dianping.pay.framework.utils.shopUuid.ShopUuidUtils;
import com.dianping.pay.promo.common.enums.User;
import com.meituan.servicecatalog.api.annotations.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.Validate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;

import java.util.List;

/**
 * <AUTHOR>
 */

@InterfaceDoc(
        displayName = "新版商详页领券组件",
        type = "restful",
        description = "新版商详页领券组件",
        scenarios = "新版商详页领券组件",
        host = "",
        authors = "xiemingyuan"
)
@Controller(value = "productpromodisplay.bin")
@Action(url = "productpromodisplay.bin", httpType = "post", protocol = {ReqProtocol.MAPI, ReqProtocol.REST})
@Slf4j
public class ProductPromoDisplayAction extends DefaultAction<ProductcouponpromoRequest> {

    @Autowired
    private CouponIssueActivityQueryService couponIssueActivityQueryService;

    @Autowired
    private UnifiedIssueCouponBiz unifiedIssueCouponBiz;

    @Autowired
    private ShopUuidUtils shopUuidUtils;

    @Autowired
    private RestUserInfoService restUserInfoService;

    @Override
    protected IMobileResponse validate(ProductcouponpromoRequest request, IMobileContext context) {
        try {
            Validate.notNull(request, "null request");
            Validate.isTrue(request.getProductid() != null, "productId is null");
            Long requestShopid = request.getShopIdL();
            String shopuuid = request.getShopuuid();

            if (requestShopid == null || requestShopid <= 0) {
                if (shopUuidUtils.isShopUuid(shopuuid)) {
                    Long shopId = shopUuidUtils.toShopId(shopuuid);
                    request.setShopIdL(shopId);
                    request.setShopid(PoiIdUtils.poiIdLongToInt(shopId));
                }
            }
            //Validate.isTrue(request.getShopid() != null && request.getShopid() > 0, "invalid shopId");
            //其他参数均使用内置校验
        } catch (Exception e) {
            log.warn("validate request exception!", e);
            return new CommonMobileResponse(new IssueCouponMsg(401, e.getMessage(), e.getMessage()));
        }
        return null;
    }

    @Override
    protected IMobileResponse execute(ProductcouponpromoRequest request, IMobileContext context) {
        PromotionRequestContext promotionRequestContext;
        try {
            // 如果是h5场景
            if (!ApiUtil.isMapiRequest(context)) {
                RestUserInfo restUserInfo = restUserInfoService.handleMobileContext(context);
                promotionRequestContext = IssueActivityMapper.buildHttpPromotionRequestContext(request, context, restUserInfo);
            } else {
                promotionRequestContext = IssueActivityMapper.buildNativePromotionRequestContext(request, context);
            }
            CouponActivityContext couponActivityContext = couponIssueActivityQueryService.queryShopPromotions(promotionRequestContext, context);
            ProductPromoInfo productPromoInfo = ProductPromoInfoMapper.buildProductPromoInfo(couponActivityContext, context);
            log.info("ProductPromoDisplayAction# ctx: {}, response:{}", couponActivityContext, productPromoInfo);
            return new CommonMobileResponse(ResultWrapper.success(productPromoInfo));
        } catch (Exception e) {
            log.error("productpromodisplay# error. request: {}", request, e);
            return new CommonMobileResponse(ResultWrapper.success(new ProductPromoInfo()));
        }

    }

    @Override
    protected List<ClientInfoRule> getRule() {
        return null;
    }
}
