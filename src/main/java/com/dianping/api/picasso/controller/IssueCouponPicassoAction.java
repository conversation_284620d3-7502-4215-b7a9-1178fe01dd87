package com.dianping.api.picasso.controller;

import com.dianping.api.util.ApiUtil;
import com.dianping.api.util.DotUtils;
import com.dianping.cat.Cat;
import com.dianping.gmkt.coupon.common.api.enums.IssueDetailSourceEnum;
import com.dianping.mobile.base.datatypes.bean.ClientInfoRule;
import com.dianping.mobile.framework.action.DefaultAction;
import com.dianping.mobile.framework.annotation.Action;
import com.dianping.mobile.framework.datatypes.CommonMobileResponse;
import com.dianping.mobile.framework.datatypes.IMobileContext;
import com.dianping.mobile.framework.datatypes.IMobileResponse;
import com.dianping.pay.api.biz.activity.IssueCouponBiz;
import com.dianping.pay.api.biz.discount.CouponBiz;
import com.dianping.pay.api.entity.issuecoupon.IssueCouponMsg;
import com.dianping.pay.api.entity.issuecoupon.IssueCouponRequest;
import com.dianping.pay.api.exception.IssueCouponException;
import com.dianping.pay.framework.utils.shopUuid.ShopUuidUtils;
import com.dianping.violet.enums.CouponRequestTypeEnum;
import com.dianping.zebra.util.StringUtils;
import com.meituan.mtrace.Tracer;
import com.meituan.servicecatalog.api.annotations.ExtensionDoc;
import com.meituan.servicecatalog.api.annotations.HttpMethod;
import com.meituan.servicecatalog.api.annotations.InterfaceDoc;
import com.meituan.servicecatalog.api.annotations.MethodDoc;
import com.meituan.servicecatalog.api.annotations.ParamDoc;
import com.meituan.servicecatalog.api.annotations.ParamType;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.Validate;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;

import javax.annotation.Resource;
import java.util.List;

@InterfaceDoc(displayName = "丽人：领券组件-用户领券",
        type = "restful",
        description = "用户在丽人团详页/商详页/..等领券组件中点击领券时触发的领券动作",
        scenarios = "用户在丽人团详页/商详页/..等领券组件中点击领券时触发的领券动作",
        host = "http://mapi.dianping.com/pay/promo/"
)
@Controller("promo/issuecoupon.bin")
@Action(url = "promo/issuecoupon.bin", httpType = "post", isCheckToken = true)
public class IssueCouponPicassoAction extends DefaultAction<IssueCouponRequest> {

    private static final Logger LOGGER = LoggerFactory.getLogger(IssueCouponPicassoAction.class);

    @Autowired
    private IssueCouponBiz issueCouponBiz;
    @Autowired
    private CouponBiz couponBiz;
    @Resource
    private ShopUuidUtils shopUuidUtils;

    @Override
    protected IMobileResponse validate(IssueCouponRequest request, IMobileContext context) {
        Cat.logEvent("INVALID_METHOD_1", "com.dianping.api.picasso.controller.IssueCouponPicassoAction.validate(com.dianping.pay.api.entity.issuecoupon.IssueCouponRequest,com.dianping.mobile.framework.datatypes.IMobileContext)");
        try {
            // 入口逻辑兼容
            if (request.getProductId() == 0 && request.getLongProductId() != null) {
                request.setProductId(request.getLongProductId().intValue());
            } else if (request.getProductId() > 0 && request.getLongProductId() == null) {
                request.setLongProductId((long) request.getProductId());
                //对未完成改造的客户端做打点，便于跟进进度
                Cat.logEvent("ProductSkuIdNotUpgraded", Tracer.getRemoteAppKey() + ":promo/issuecoupon.bin");
            }
            if (request.getCouponOptionId() == 0 && request.getStringCouponOptionId() != null) {
                request.setCouponOptionId(Integer.parseInt(request.getStringCouponOptionId()));
            } else if (request.getCouponOptionId() > 0 && request.getStringCouponOptionId() == null) {
                request.setStringCouponOptionId(request.getCouponOptionId() + "");
                //对未完成改造的客户端做打点，便于跟进进度
                Cat.logEvent("CouponGroupIdNotUpgraded", Tracer.getRemoteAppKey() + ":beautyissuecouponcomponent.bin");
            }
            if (context.getRequest() != null) {
                DotUtils.addRefererDot(context.getRequest());
            }
            Validate.notNull(request, "null request");
            shopUuidUtils.prepareUuidInRequest(request, ApiUtil.isMapiRequest(context));
            Validate.isTrue(context.getUserId() > 0 || (context.isMtClient() && context.getUserStatus() != null && context.getUserStatus().getMtUserId() > 0), "用户未登录");
            Validate.isTrue(request.getShopIdL() > 0
                            || request.getProductId() > 0
                            || request.getCategoryId() > 0
                            || CollectionUtils.isNotEmpty(request.getShopIdLList())
                            || StringUtils.isNotBlank(request.getShopUuid())
                            || CollectionUtils.isNotEmpty(request.getShopUuids())
                    , "invalid shopId/productId/categoryId");
            Validate.isTrue(request.getCouponOptionId() > 0, "invalid couponOptionId");
            if (request.getIssueDetailSourceCode() != null) {
                Validate.isTrue(IssueDetailSourceEnum.getByCode(request.getIssueDetailSourceCode()) != null, "issuedetailsourcecode invalid");
            }
            request.validate();
        } catch (IllegalArgumentException e) {
            LOGGER.warn(String.format("invalid request: %s", e.getMessage()));
            return new CommonMobileResponse(new IssueCouponMsg(1, e.getMessage(), e.getMessage()));
        }
        return null;
    }

    @MethodDoc(requestMethods = HttpMethod.POST,
            urls = "issuecoupon.bin",
            displayName = "用户在丽人团详页/商详页/..等领券组件中点击领券时触发的领券动作",
            description = "用户在丽人团详页/商详页/..等领券组件中点击领券时触发的领券动作",
            returnValueDescription = "领券结果",
            restExampleUrl = "https://mapi.51ping.com/pay/promo/issuecoupon.bin",
            parameters = {
                    @ParamDoc(
                            name = "request",
                            description = "请求参数",
                            type = IssueCouponRequest.class,
                            paramType = ParamType.REQUEST_BODY
                    ),
                    @ParamDoc(
                            name = "context",
                            description = "mapi-shell协议上下文",
                            type = IMobileContext.class,
                            paramType = ParamType.REQUEST_BODY
                    )
            },
            responseParams = {@ParamDoc(name = "iMobileResponse", description = "通用返回数据模型")},
            restExamplePostData = "{\n" +
                    "  \"productid\": \"200025690\",\n" +
                    "  \"cityid\": \"1\",\n" +
                    "  \"couponoptionid\": \"814846070\"\n" +
                    "}",
            restExampleResponseData = "{\n" +
                    "  \"Message\": \"领取成功\",\n" +
                    "  \"ErrorCode\": 0,\n" +
                    "  \"ErrorMsg\": null,\n" +
                    "  \"Content\": [\n" +
                    "    [\n" +
                    "      {\n" +
                    "        \"text\": \"券种类: \",\n" +
                    "        \"textcolor\": \"#999999\"\n" +
                    "      },\n" +
                    "      {\n" +
                    "        \"text\": \"12\",\n" +
                    "        \"textcolor\": \"#E06639\"\n" +
                    "      },\n" +
                    "      {\n" +
                    "        \"text\": \"元抵用券\",\n" +
                    "        \"textcolor\": \"#2C2C2C\"\n" +
                    "      }\n" +
                    "    ],\n" +
                    "    [\n" +
                    "      {\n" +
                    "        \"text\": \"使用规则: \",\n" +
                    "        \"textcolor\": \"#999999\"\n" +
                    "      },\n" +
                    "      {\n" +
                    "        \"text\": \"满123元可用\",\n" +
                    "        \"textcolor\": \"#2C2C2C\"\n" +
                    "      }\n" +
                    "    ],\n" +
                    "    [\n" +
                    "      {\n" +
                    "        \"text\": \"有效期: \",\n" +
                    "        \"textcolor\": \"#999999\"\n" +
                    "      },\n" +
                    "      {\n" +
                    "        \"text\": \"领取后7天有效，限指定商品\",\n" +
                    "        \"textcolor\": \"#2C2C2C\"\n" +
                    "      }\n" +
                    "    ]\n" +
                    "  ]\n" +
                    "}",
            extensions = {
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE",
                            content = "数据鉴权逻辑: 通过门店、渠道、券id校验领券合法性"
                    )
            }
    )
    @Override
    protected IMobileResponse execute(IssueCouponRequest request, IMobileContext context) {
        Cat.logEvent("INVALID_METHOD_2", "com.dianping.api.picasso.controller.IssueCouponPicassoAction.execute(com.dianping.pay.api.entity.issuecoupon.IssueCouponRequest,com.dianping.mobile.framework.datatypes.IMobileContext)");
        try {
            IssueCouponRequest issueCouponRequest = new IssueCouponRequest(request, context, CouponRequestTypeEnum.ISSUE.getCode());

            log.info(String.format("isdpclient=%s, token=%s, userid=%s",
                    issueCouponRequest.isDpClient(),
                    context.getHeader().getToken(),
                    issueCouponRequest.getUserId()));
            if (issueCouponRequest.getUserId() <= 0) {
                Cat.logEvent("InvalidUserId", "issuecoupon.bin");
            }
            int couponGroupId = issueCouponBiz.doIssueCoupon(issueCouponRequest);
            return new CommonMobileResponse(new IssueCouponMsg(0, null, "领取成功", couponBiz.getIssueCouponMsgSuccessContent(couponGroupId)));
        } catch (IssueCouponException e) {
            return new CommonMobileResponse(new IssueCouponMsg(1, e.getMessage(), e.getMessage()));
        }
    }

    @Override
    protected List<ClientInfoRule> getRule() {
        Cat.logEvent("INVALID_METHOD_3", "com.dianping.api.picasso.controller.IssueCouponPicassoAction.getRule()");
        return null;
    }
}
