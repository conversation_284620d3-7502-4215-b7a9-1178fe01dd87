package com.dianping.api.picasso.controller;

import com.dianping.api.util.CrossDomainUtils;
import com.dianping.mobile.base.datatypes.bean.ClientInfoRule;
import com.dianping.mobile.framework.action.DefaultAction;
import com.dianping.mobile.framework.annotation.Action;
import com.dianping.mobile.framework.datatypes.CommonMobileResponse;
import com.dianping.mobile.framework.datatypes.IMobileContext;
import com.dianping.mobile.framework.datatypes.IMobileResponse;
import com.dianping.mobile.framework.enums.ReqProtocol;
import com.dianping.pay.api.biz.ShopCartIssueMultiCouponBiz;
import com.dianping.pay.api.entity.issuecoupon.*;
import com.dianping.pay.api.exception.IssueCouponException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2021/1/21
 */
@Controller("promo/shopcartissuemulticouponaction.bin")
@Action(url = "promo/shopcartissuemulticouponaction.bin", protocol = ReqProtocol.REST, httpType = "post")
public class ShopCartIssueMultiCouponAction extends DefaultAction<ShopCartIssueMultiCouponRequest> {

    public static final Logger log = LoggerFactory.getLogger(ShopCartIssueMultiCouponAction.class);

    @Autowired
    private ShopCartIssueMultiCouponBiz shopCartIssueMultiCouponBiz;

    @Override
    protected IMobileResponse validate(ShopCartIssueMultiCouponRequest request, IMobileContext iMobileContext) {
        return shopCartIssueMultiCouponBiz.validate(request, iMobileContext);
    }

    @Override
    protected IMobileResponse execute(ShopCartIssueMultiCouponRequest request, IMobileContext iMobileContext) {
        try {
            return new CommonMobileResponse(shopCartIssueMultiCouponBiz.doIssueShopCartCoupon(request, iMobileContext));
        } catch (IssueCouponException e) {
            UnifiedIssueMultiCouponRespDo unifiedIssueCouponRespDo = new UnifiedIssueMultiCouponRespDo();
            unifiedIssueCouponRespDo.setSuccess(false);
            unifiedIssueCouponRespDo.setErrorMsg(e.getMessage());
            return new CommonMobileResponse(unifiedIssueCouponRespDo);
        }
    }

    @Override
    protected List<ClientInfoRule> getRule() {
        return null;
    }
}
