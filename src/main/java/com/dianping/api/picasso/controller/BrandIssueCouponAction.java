package com.dianping.api.picasso.controller;

import com.dianping.api.util.ApiUtil;
import com.dianping.cat.Cat;
import com.dianping.mobile.base.datatypes.bean.ClientInfoRule;
import com.dianping.mobile.framework.action.DefaultAction;
import com.dianping.mobile.framework.annotation.Action;
import com.dianping.mobile.framework.datatypes.CommonMobileResponse;
import com.dianping.mobile.framework.datatypes.IMobileContext;
import com.dianping.mobile.framework.datatypes.IMobileResponse;
import com.dianping.mobile.framework.enums.ReqProtocol;
import com.dianping.pay.api.biz.activity.RestUserInfoService;
import com.dianping.pay.api.biz.activity.newloader.dto.RestUserInfo;
import com.dianping.pay.api.biz.activity.newloader.promo.BrandActivityPromotionProcessor;
import com.dianping.pay.api.entity.issuecoupon.*;
import com.dianping.pay.api.util.LogUtils;
import com.meituan.servicecatalog.api.annotations.InterfaceDoc;
import org.apache.commons.lang.Validate;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Controller;

import javax.annotation.Resource;
import java.util.List;

@InterfaceDoc(
        displayName = "品牌详情页领券组件领券接口",
        type = "restful",
        description = "品牌详情页领券组件领券接口",
        scenarios = "品牌详情页领券组件领券接口",
        host = "",
        authors = "xiemingyuan"
)
@Controller(value = "brandissuecouponaction.bin")
@Action(url = "brandissuecouponaction.bin", httpType = "post", protocol = {ReqProtocol.MAPI, ReqProtocol.REST})
public class BrandIssueCouponAction extends DefaultAction<BrandActivityIssueCouponRequest> {

    public static final Logger log = LoggerFactory.getLogger(BrandIssueCouponAction.class);

    @Resource
    private RestUserInfoService restUserInfoService;

    @Resource
    private BrandActivityPromotionProcessor brandActivityPromotionProcessor;

    @Resource
    private LogUtils logUtils;

    @Override
    protected IMobileResponse validate(BrandActivityIssueCouponRequest request, IMobileContext context) {
        Cat.logEvent("INVALID_METHOD_4", "com.dianping.api.picasso.controller.BrandIssueCouponAction.validate(com.dianping.pay.api.entity.issuecoupon.BrandActivityIssueCouponRequest,com.dianping.mobile.framework.datatypes.IMobileContext)");
        try {
            Validate.notNull(request, "null request");
            Validate.isTrue(request.getBrandId() != null && request.getBrandId() > 0, "invalid brandId");
            Validate.isTrue(request.getBActivityId() != null && request.getBActivityId() > 0, "invalid bactivityid");
        } catch (Exception e) {
            log.warn("validate request exception!, request:{}", request, e);
            return new CommonMobileResponse(ResultWrapper.fail(e.getMessage()));
        }
        return null;
    }

    @Override
    protected IMobileResponse execute(BrandActivityIssueCouponRequest request, IMobileContext context) {
        Cat.logEvent("INVALID_METHOD_1", "com.dianping.api.picasso.controller.BrandIssueCouponAction.execute(com.dianping.pay.api.entity.issuecoupon.BrandActivityIssueCouponRequest,com.dianping.mobile.framework.datatypes.IMobileContext)");
        try {
            // 如果是h5场景
            if (!ApiUtil.isMapiRequest(context)) {
                RestUserInfo restUserInfo = restUserInfoService.handleMobileContext(context);
                request.setUserId(restUserInfo.getUserId());
                request.setUsercode(restUserInfo.getUserCode());
            }
            if (request.getUserId() == null || request.getUserId() <= 0) {
                return new CommonMobileResponse(ResultWrapper.fail(HttpStatus.UNAUTHORIZED.value(), "用户未登录"));
            }
            UnifiedIssueDetailResult issueResult = brandActivityPromotionProcessor.doIssueCoupon(request, context);
            logUtils.logInfo("BrandIssueCouponAction# request:{}, result:{}", request, issueResult);
            return new CommonMobileResponse(ResultWrapper.success(issueResult));
        } catch (Exception e) {
            log.error("BrandIssueCouponAction# error. request:{}", request, e);
            return new CommonMobileResponse(ResultWrapper.fail("未知错误"));
        }
    }

    @Override
    protected List<ClientInfoRule> getRule() {
        Cat.logEvent("INVALID_METHOD_1", "com.dianping.api.picasso.controller.BrandIssueCouponAction.getRule()");
        return null;
    }

}
