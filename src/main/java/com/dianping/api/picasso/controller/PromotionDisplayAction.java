package com.dianping.api.picasso.controller;

import com.dianping.api.util.CrossDomainUtils;
import com.dianping.mobile.base.datatypes.bean.ClientInfoRule;
import com.dianping.mobile.framework.action.DefaultAction;
import com.dianping.mobile.framework.annotation.Action;
import com.dianping.mobile.framework.datatypes.CommonMobileResponse;
import com.dianping.mobile.framework.datatypes.IMobileContext;
import com.dianping.mobile.framework.datatypes.IMobileResponse;
import com.dianping.mobile.framework.enums.ReqProtocol;
import com.dianping.pay.api.biz.activity.UnifiedIssueCouponBiz;
import com.dianping.pay.api.biz.activity.newloader.CouponIssueActivityQueryService;
import com.dianping.pay.api.biz.activity.newloader.dto.CouponActivityContext;
import com.dianping.pay.api.biz.activity.newloader.dto.PromotionRequestContext;
import com.dianping.pay.api.biz.activity.newloader.mapper.IssueActivityMapper;
import com.dianping.pay.api.entity.issuecoupon.IssueCouponMsg;
import com.dianping.pay.api.entity.issuecoupon.PromotionDisplayActionRequest;
import com.dianping.pay.api.entity.issuecoupon.PromotionDisplayResponse;
import com.dianping.pay.api.util.PoiIdUtils;
import com.dianping.pay.framework.utils.shopUuid.ShopUuidUtils;
import com.meituan.servicecatalog.api.annotations.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.Validate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;

import java.util.List;


/**
 * <AUTHOR>
 * @date 08/04/2020
 * @time 18:27
 * 移动之家链接：https://mobile.sankuai.com/studio/api/16073/index
 */

@InterfaceDoc(
        displayName = "新版商详页领券组件",
        type = "restful",
        description = "新版商详页领券组件",
        scenarios = "新版商详页领券组件",
        host = "",
        authors = "chensheng05"
)
@Controller(value = "promotiondisplayaction.bin")
@Action(url = "promotiondisplayaction.bin", httpType = "post", protocol = {ReqProtocol.MAPI, ReqProtocol.REST})
@Slf4j
public class PromotionDisplayAction extends DefaultAction<PromotionDisplayActionRequest> {


    @Autowired
    private CouponIssueActivityQueryService couponIssueActivityQueryService;
    @Autowired
    private UnifiedIssueCouponBiz unifiedIssueCouponBiz;

    @Autowired
    private ShopUuidUtils shopUuidUtils;

    @Override
    protected IMobileResponse validate(PromotionDisplayActionRequest request, IMobileContext iMobileContext) {
        try {
            Validate.notNull(request, "null request");

            Long requestShopid = request.getShopIdL();
            String shopuuid = request.getShopuuid();

            if(requestShopid == null || requestShopid<=0){
                if(shopUuidUtils.isShopUuid(shopuuid)){
                    Long shopId = shopUuidUtils.toShopId(shopuuid);
                    request.setShopidL(shopId);
                    request.setShopid(PoiIdUtils.poiIdLongToInt(shopId));
                }
            }
            Validate.isTrue(request.getShopIdL() != null && request.getShopIdL()>0, "invalid shopId");
            //其他参数均使用内置校验
        } catch (Exception e) {
            log.warn("validate request exception!", e);
            return new CommonMobileResponse(new IssueCouponMsg(401, e.getMessage(), e.getMessage()));
        }
        return null;
    }

    @MethodDoc(requestMethods = HttpMethod.POST,
            urls = "promotiondisplayaction.bin",
            displayName = "新版商详页领券组件",
            description = "新版商详页领券组件",
            returnValueDescription = "商详页新版领券组件-展示信息",
            restExampleUrl = "",
            parameters = {
                    @ParamDoc(
                            name = "request",
                            description = "接口promotiondisplayaction.bin的请求参数",
                            type = PromotionDisplayActionRequest.class,
                            paramType = ParamType.REQUEST_BODY
                    ),
                    @ParamDoc(
                            name = "iMobileContext",
                            description = "mapi-shell协议上下文",
                            type = IMobileContext.class,
                            paramType = ParamType.REQUEST_BODY
                    )
            },
            responseParams = {@ParamDoc(name = "iMobileResponse", description = "通用返回数据模型")},
            extensions = {
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE",
                            content = "{鉴权逻辑，请自行填写} "
                    ),
            }
    )
    @Override
    protected IMobileResponse execute(PromotionDisplayActionRequest request, IMobileContext iMobileContext) {
        CrossDomainUtils.setEnableCrossDomain(iMobileContext.getRequest(), iMobileContext.getResponse());

        PromotionRequestContext promotionRequestContext = IssueActivityMapper.buildPromotionRequestContext(request, iMobileContext);
        log.info("queryShopPromotions promotionRequestContext:{}", promotionRequestContext);
        CouponActivityContext couponActivityContext = couponIssueActivityQueryService.queryShopPromotions(promotionRequestContext, iMobileContext);
        log.info("queryShopPromotions response:{}", couponActivityContext);
        if(CollectionUtils.isEmpty(couponActivityContext.getIssueCouponActivities()) && couponActivityContext.getReturnPromotionDisplayDto() == null){
            return new CommonMobileResponse(null);
        }

        PromotionDisplayResponse promotionDisplayResponse = unifiedIssueCouponBiz.toPromotionDisplayResponse(couponActivityContext, promotionRequestContext);
        log.info("coupons query by promotion component:{}", promotionDisplayResponse);
        return new CommonMobileResponse(promotionDisplayResponse);
    }

    @Override
    protected List<ClientInfoRule> getRule() {
        return null;
    }
}
