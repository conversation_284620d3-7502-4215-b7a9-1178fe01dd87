package com.dianping.api.picasso.controller;

import com.dianping.cat.Cat;
import com.dianping.mobile.base.datatypes.bean.ClientInfoRule;
import com.dianping.mobile.framework.action.DefaultAction;
import com.dianping.mobile.framework.annotation.Action;
import com.dianping.mobile.framework.datatypes.CommonMobileResponse;
import com.dianping.mobile.framework.datatypes.IMobileContext;
import com.dianping.mobile.framework.datatypes.IMobileResponse;
import com.dianping.mobile.framework.enums.ReqProtocol;
import com.dianping.pay.api.biz.UnifiedIssueMultiCouponBiz;
import com.dianping.pay.api.entity.issuecoupon.UnifiedIssueCouponRespDo;
import com.dianping.pay.api.entity.issuecoupon.UnifiedIssueMultiCouponRespDo;
import com.dianping.pay.api.entity.issuecoupon.UnifiedissuemulticouponRequest;
import com.dianping.pay.api.exception.IssueCouponException;
import com.meituan.mtrace.Tracer;
import com.meituan.servicecatalog.api.annotations.ExtensionDoc;
import com.meituan.servicecatalog.api.annotations.InterfaceDoc;
import com.meituan.servicecatalog.api.annotations.MethodDoc;
import com.meituan.servicecatalog.api.annotations.ParamDoc;
import com.meituan.servicecatalog.api.annotations.ParamType;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;

@InterfaceDoc(
        displayName = "领券组件-用户领券",
        type = "restful",
        scenarios = "领券中心-用户领券 用户在团详页/商详页/..等领券组件中点击领券时触发的领券行为",
        description = "领券中心-用户领券 用户在团详页/商详页/..等领券组件中点击领券时触发的领券行为",
        host = "http://mapi.51ping.com/pay"
)
@org.springframework.stereotype.Controller("promo/unifiedissuemulticoupon.bin")
@Action(url = "promo/unifiedissuemulticoupon.bin", protocol = ReqProtocol.REST, httpType = "post")
public class UnifiedIssueMultiCouponAction extends DefaultAction<UnifiedissuemulticouponRequest> {
    private static final Logger LOGGER = LogManager.getLogger(UnifiedIssueMultiCouponAction.class);

    @Autowired
    private UnifiedIssueMultiCouponBiz unifiedIssueMultiCouponBiz;

    @Override
    protected IMobileResponse validate(UnifiedissuemulticouponRequest request, IMobileContext iMobileContext) {
        // 入口逻辑兼容
        if (request.getProductid() == null || request.getProductid() == 0) {
            if (request.getLongProductId() != null && request.getLongProductId() > 0) {
                request.setProductid(request.getLongProductId().intValue());
            }
        } else if (request.getProductid() > 0 && request.getLongProductId() == null) {
            request.setLongProductId(Long.valueOf(request.getProductid()));
            //对未完成改造的客户端做打点，便于跟进进度
            Cat.logEvent("ProductIdNotUpgrated", Tracer.getRemoteAppKey() + ":promo/unifiedissuemulticoupon.bin");
        }
        return unifiedIssueMultiCouponBiz.validate(request, iMobileContext);
    }

    @MethodDoc(
            displayName = "用户领券",
            description = "用户在团详页/商详页/..等领券组件中点击领券时触发的领券动作",
            parameters = {
                    @ParamDoc(
                            name = "request",
                            description = "用户领券参数",
                            paramType = ParamType.REQUEST_BODY,
                            requiredness = Requiredness.REQUIRED
                    )
            },
            responseParams = {
                    @ParamDoc(name = "返回参数",
                            type = UnifiedIssueCouponRespDo.class,
                            description = "领券返回参数"
                    ),
            },
            returnValueDescription = "领券结果",
            restExampleUrl = "https://mapi.51ping.com/pay/promo/unifiedissuecoupon.bin",
            restExamplePostData = "shopid=1794790&cityid=1&token=407c8fbae616a3eab47b443db00562b645bee12e9c0d24d9411da312a1cb75647c22bbd1f0f7bb2878fd33a777b9c5e4747a1b65eb4658ee71ee8e2f9f27b8f0&coupongroupid=256403890&usercode=0",
            restExampleResponseData = "{\n" +
                    "    \"isSuccess\": true,\n" +
                    "    \"errorMsg\": null,\n" +
                    "    \"toUseUrl\": \"dianping://web?url=https%3a%2f%2fg.51ping" +
                    ".com%2fav%2frainbow%2f1304698%2findex.html%3fcouponid%3d154259873014453578562501\"\n" +
                    "}",
            extensions = {
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE",
                            content = "数据鉴权逻辑：通过门店、渠道、券id校验领券合法性"
                    )
            }

    )
    @Override
    protected IMobileResponse execute(UnifiedissuemulticouponRequest request, IMobileContext iMobileContext) {
        try {
            return new CommonMobileResponse(unifiedIssueMultiCouponBiz.doMultiIssueCoupon(request, iMobileContext));
        } catch (IssueCouponException e) {
            UnifiedIssueMultiCouponRespDo unifiedIssueCouponRespDo = new UnifiedIssueMultiCouponRespDo();
            unifiedIssueCouponRespDo.setSuccess(false);
            unifiedIssueCouponRespDo.setErrorMsg(e.getMessage());
            return new CommonMobileResponse(unifiedIssueCouponRespDo);
        }
    }

    @Override
    protected List<ClientInfoRule> getRule() {
        return null;
    }
}
