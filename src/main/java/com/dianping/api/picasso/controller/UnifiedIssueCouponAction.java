package com.dianping.api.picasso.controller;

import com.dianping.api.common.enums.ProductTypeEnum;
import com.dianping.api.domain.ClientInfo;
import com.dianping.api.picasso.UrlUtils;
import com.dianping.api.util.ApiUtil;
import com.dianping.api.util.CrossDomainUtils;
import com.dianping.api.util.DotUtils;
import com.dianping.cat.Cat;
import com.dianping.cat.util.StringUtils;
import com.dianping.gmkt.coupon.common.api.enums.IssueDetailSourceEnum;
import com.dianping.gmkt.wave.api.enums.ActivityChannelEnum;
import com.dianping.mobile.base.datatypes.bean.ClientInfoRule;
import com.dianping.mobile.framework.action.DefaultAction;
import com.dianping.mobile.framework.annotation.Action;
import com.dianping.mobile.framework.datatypes.CommonMobileResponse;
import com.dianping.mobile.framework.datatypes.IMobileContext;
import com.dianping.mobile.framework.datatypes.IMobileResponse;
import com.dianping.mobile.framework.enums.ReqProtocol;
import com.dianping.pay.api.biz.activity.CouponInfoBiz;
import com.dianping.pay.api.biz.activity.IssueCouponBiz;
import com.dianping.pay.api.biz.activity.RestUserInfoService;
import com.dianping.pay.api.biz.activity.newloader.CouponIssueActivityQueryService;
import com.dianping.pay.api.biz.activity.newloader.dto.CouponIssueActivityQueryContext;
import com.dianping.pay.api.biz.activity.newloader.dto.RestUserInfo;
import com.dianping.pay.api.biz.activity.newloader.promo.FinancialConsumePromotionProcessor;
import com.dianping.pay.api.biz.activity.newloader.promo.ResourcePromotionProcessor;
import com.dianping.pay.api.biz.discount.CouponBiz;
import com.dianping.pay.api.entity.issuecoupon.*;
import com.dianping.pay.api.enums.ShopProductType;
import com.dianping.pay.api.exception.IssueCouponException;
import com.dianping.pay.api.util.JsonUtils;
import com.dianping.pay.framework.utils.IdCryptoUtils;
import com.dianping.pay.framework.utils.shopUuid.ShopUuidUtils;
import com.dianping.pay.promo.common.enums.User;
import com.dianping.unified.coupon.manage.api.dto.UnifiedCouponGroupDTO;
import com.dianping.violet.enums.CouponRequestTypeEnum;
import com.meituan.mtrace.Tracer;
import com.meituan.servicecatalog.api.annotations.*;
import org.apache.commons.lang.Validate;
import org.apache.commons.lang.math.NumberUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

@InterfaceDoc(
        displayName = "领券组件-用户领券",
        type = "restful",
        scenarios = "领券中心-用户领券 用户在团详页/商详页/..等领券组件中点击领券时触发的领券行为",
        description = "领券中心-用户领券 用户在团详页/商详页/..等领券组件中点击领券时触发的领券行为",
        host = "http://mapi.51ping.com/pay"
)
@org.springframework.stereotype.Controller("promo/unifiedissuecoupon.bin")
@Action(url = "promo/unifiedissuecoupon.bin", protocol = ReqProtocol.REST, httpType = "post")
public class UnifiedIssueCouponAction extends DefaultAction<UnifiedissuecouponRequest> {
    private static final Logger LOGGER = LogManager.getLogger(UnifiedIssueCouponAction.class);
    @Autowired
    private CouponIssueActivityQueryService couponIssueActivityQueryService;
    @Autowired
    private IssueCouponBiz issueCouponBiz;
    @Autowired
    private RestUserInfoService restUserInfoService;
    @Autowired
    private CouponBiz couponBiz;
    @Autowired
    private CouponInfoBiz couponInfoBiz;
    @Resource
    private ShopUuidUtils shopUuidUtils;
    @Resource
    private ResourcePromotionProcessor resourcePromotionProcessor;
    @Resource
    private FinancialConsumePromotionProcessor financialConsumePromotionProcessor;


    @Override
    protected IMobileResponse validate(UnifiedissuecouponRequest request, IMobileContext iMobileContext) {
        try {
            if (iMobileContext.getRequest() != null) {
                DotUtils.addRefererDot(iMobileContext.getRequest());
            }
            
            // 入口逻辑兼容
            if ((request.getProductid() == null || request.getProductid() == 0) && request.getLongProductId() != null && request.getLongProductId() > 0) {
                request.setProductid(request.getLongProductId().intValue());
            } else if (request.getProductid() != null && request.getProductid() > 0 && request.getLongProductId() == null) {
                request.setLongProductId(Long.valueOf(request.getProductid()));
                //对未完成改造的客户端做打点，便于跟进进度
                Cat.logEvent("ProductIdNotUpgrated", Tracer.getRemoteAppKey() + ":promo/unifiedissuecoupon.bin");
            }
            Validate.notNull(request, "null request");
            IdCryptoUtils.decryptByRequest(request);
            shopUuidUtils.prepareUuidInRequest(request, ApiUtil.isMapiRequest(iMobileContext));

            if (ApiUtil.isMapiRequest(iMobileContext)) {
                Validate.isTrue(iMobileContext.getUserId() > 0 || (iMobileContext.isMtClient() && iMobileContext.getUserStatus() != null && iMobileContext.getUserStatus().getMtUserId() > 0), "用户未登录");
            }
            if (request.getCoupontype() == null || request.getCoupontype() == 0) {
                Validate.isTrue((request.getShopidL() != null && request.getShopidL() > 0)
                                || (request.getProductid() != null && request.getProductid() > 0)
                                || (request.getLongProductId() != null && request.getLongProductId() > 0)
                                || (StringUtils.isNotEmpty(request.getShopuuid()))
                        , "invalid shopId/productId");
                Validate.isTrue(request.getCoupongroupid() != null || request.getUnifiedCouponGroupId() != null, " 券 id 非法");
                if (request.getProductid() != null && request.getProductid() > 0) {
                    if (request.getProducttype() == null) {
                        request.setProducttype(ProductTypeEnum.DEAL.code);
                    }
                    Validate.isTrue(request.getProducttype() == ProductTypeEnum.DEAL.code
                            || request.getProducttype() == ProductTypeEnum.SKU.code || request.getProducttype() == ProductTypeEnum.SPU.code,
                            "producttype invalid");
                }
            }
            if (request.getIssueDetailSourceCode() != null) {
                Validate.isTrue(IssueDetailSourceEnum.getByCode(request.getIssueDetailSourceCode()) != null, "issuedetailsourcecode invalid");
            }
        } catch (IllegalArgumentException e) {
            LOGGER.warn(String.format("invalid request: %s", e.getMessage()));
            return new CommonMobileResponse(new IssueCouponMsg(401, e.getMessage(), e.getMessage(), e.getMessage()));
        }
        return null;
    }

    @MethodDoc(
            displayName = "用户领券",
            description = "用户在团详页/商详页/..等领券组件中点击领券时触发的领券动作",
            parameters = {
                    @ParamDoc(
                            name = "request",
                            description = "用户领券参数",
                            paramType = ParamType.REQUEST_BODY,
                            requiredness = Requiredness.REQUIRED
                    )
            },
            responseParams = {
                    @ParamDoc(name = "返回参数",
                            type = UnifiedIssueCouponRespDo.class,
                            description = "领券返回参数"
                    ),
            },
            returnValueDescription = "领券结果",
            restExampleUrl = "https://mapi.51ping.com/pay/promo/unifiedissuecoupon.bin",
            restExamplePostData = "shopid=1794790&cityid=1&token=407c8fbae616a3eab47b443db00562b645bee12e9c0d24d9411da312a1cb75647c22bbd1f0f7bb2878fd33a777b9c5e4747a1b65eb4658ee71ee8e2f9f27b8f0&coupongroupid=256403890&usercode=0",
            restExampleResponseData = "{\n" +
                    "    \"isSuccess\": true,\n" +
                    "    \"errorMsg\": null,\n" +
                    "    \"toUseUrl\": \"dianping://web?url=https%3a%2f%2fg.51ping" +
                    ".com%2fav%2frainbow%2f1304698%2findex.html%3fcouponid%3d154259873014453578562501\"\n" +
                    "}",
            extensions = {
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE",
                            content = "数据鉴权逻辑：通过门店、渠道、券id校验领券合法性"
                    )
            }

    )
    @Override
    protected IMobileResponse execute(UnifiedissuecouponRequest request, IMobileContext iMobileContext) {
        CrossDomainUtils.setEnableCrossDomain(iMobileContext.getRequest(), iMobileContext.getResponse());
        try {
            IssueCouponRequest issueCouponRequest = issueCouponRequestAdapter(request, iMobileContext, CouponRequestTypeEnum.ISSUE.getCode());
            LOGGER.info("unifiedissuecoupon issue request：{}", JsonUtils.toJson(request));
            CouponIssueActivityQueryContext context = issueRequest2QueryActivityContext(request, iMobileContext);

            processHttpUserInfo(issueCouponRequest, iMobileContext, context);
            UnifiedIssueResult issueResult = null;
            String toUseUrl = "";

            if(request.getCoupontype() != null && request.getCoupontype() == 2) {
                //发金融政府消费券
                issueResult = financialConsumePromotionProcessor.doIssueGovConsumeCoupon(issueCouponRequest, context);
            }else if (request.getCoupontype() != null && request.getCoupontype() == 1) {
                issueResult = resourcePromotionProcessor.doIssueCoupon(issueCouponRequest, iMobileContext);
            } else {
                boolean isShopCoupon = couponInfoBiz.isShopCoupon(context);
                if (isShopCoupon) {
                    //领券商家券
                    if(issueCouponRequest.getUserId() <= 0 ){
                        Cat.logEvent("InvalidUserId", "unifiedissuecoupon.bin");
                    }
                    issueResult = issueCouponBiz.doIssueCoupon(issueCouponRequest, context);
                    toUseUrl = getUseUrl(issueCouponRequest.isDpClient(), issueResult);
                } else {
                    //领平台券
                    issueResult = issueCouponBiz.issuePlatformCoupon(context,issueCouponRequest);
                    toUseUrl = "";
                }
            }

            UnifiedIssueCouponRespDo unifiedIssueCouponRespDo = new UnifiedIssueCouponRespDo(true, toUseUrl);
            unifiedIssueCouponRespDo.setBeginTime(issueResult.getBeginTime());
            unifiedIssueCouponRespDo.setEndTime(issueResult.getEndTime());
            if (issueResult.getBeginTime() != null) {
                unifiedIssueCouponRespDo.setUseBeginTime(issueResult.getBeginTime().getTime());
            }
            if (issueResult.getEndTime() != null) {
                unifiedIssueCouponRespDo.setUseEndTime(issueResult.getEndTime().getTime());
            }
            unifiedIssueCouponRespDo.setMessage("领取成功");
            unifiedIssueCouponRespDo.setToastMsg("领取成功");
            if (StringUtils.isNotEmpty(issueResult.getToastMsg())) {
                unifiedIssueCouponRespDo.setMessage(issueResult.getToastMsg());
                unifiedIssueCouponRespDo.setToastMsg(issueResult.getToastMsg());
            }


            unifiedIssueCouponRespDo.setCouponGroupName(issueResult.getCouponGroupName());
            // 前端认为时间判断后端处理会更加安全
            if (issueResult.getBeginTime() != null && !new Date().before(issueResult.getBeginTime())) {
                unifiedIssueCouponRespDo.setCurrentCanUse(true);
            }
            unifiedIssueCouponRespDo.setUnifiedCouponId(issueResult.getUnifiedCouponId());
            LOGGER.info("unifiedissuecoupon issue response：{}", JsonUtils.toJson(unifiedIssueCouponRespDo));
            return new CommonMobileResponse(unifiedIssueCouponRespDo);
        } catch (IssueCouponException e) {
            LOGGER.info("unifiedissuecoupon issue request：{}", JsonUtils.toJson(request), e);
            UnifiedIssueCouponRespDo unifiedIssueCouponRespDo = new UnifiedIssueCouponRespDo(new IssueCouponErrorDo(e.getCode(), e.getMessage()), e.getMessage());
            return new CommonMobileResponse(unifiedIssueCouponRespDo);
        }
    }

    private String getUseUrl(boolean dpClient, UnifiedIssueResult issueResult) {
        if (issueResult == null) {
            return "";
        }
        String toUseUrl = "";
        int couponGroupId = issueResult.getCouponGroupId();
        if (couponGroupId <= 0) {
            return "";
        }

        UnifiedCouponGroupDTO unifiedCouponGroupDTO = couponBiz.queryCouponGroup(couponGroupId);
        if (unifiedCouponGroupDTO == null) {
            return "";
        }
        return UrlUtils.getCouponGroupToUseUrl(unifiedCouponGroupDTO, issueResult.getUnifiedCouponId(), dpClient);
    }

    private void processHttpUserInfo(IssueCouponRequest issueCouponRequest, IMobileContext iMobileContext,
                                     CouponIssueActivityQueryContext context) {
        if (!ApiUtil.isMapiRequest(iMobileContext)) {
            RestUserInfo restUserInfo = restUserInfoService.handleMobileContext(iMobileContext);
            if (restUserInfo == null || restUserInfo.getUserId() <= 0) {
                LOGGER.warn(String.format("invalid request: %s", "rest用户未登录"));
                throw new IssueCouponException("用户未登录", 401);
            }
            boolean isDpClient = restUserInfo.getUserCode() != User.MT.getCode();
            issueCouponRequest.setDpClient(isDpClient);
            issueCouponRequest.setUserId(restUserInfo.getUserId());

            context.setDpClient(isDpClient);
            context.setUserId(restUserInfo.getUserId());
        }
    }


    private CouponIssueActivityQueryContext issueRequest2QueryActivityContext(UnifiedissuecouponRequest request, IMobileContext iMobileContext) {
        CouponIssueActivityQueryContext context = new CouponIssueActivityQueryContext();
        context.setSerialno(request.getSerialno());
        context.setShopIdL(request.getShopidL() == null ? 0 : request.getShopidL());
        context.setUserId(iMobileContext.getUserId());
        context.setDpClient(!iMobileContext.isMtClient());
        context.setProductId(request.getProductid() == null ? 0 : request.getProductid());
        context.setProductType(request.getProducttype() == null ? 0 : request.getProducttype());
        if (request.getUnifiedCouponGroupId() != null) {
            context.setUnifiedCouponGroupId(request.getUnifiedCouponGroupId());
        } else {
            context.setCouponGroupId(request.getCoupongroupid() == null ? 0 : request.getCoupongroupid());
        }
        if (ApiUtil.isMapiRequest(iMobileContext)) {
            //native查询平台券时，设置为通用领券组件
            context.setIssueChannel(ActivityChannelEnum.COMMON.getCode());
        } else {
            context.setIssueChannel(request.getIssueChannel() == null ? 0 : request.getIssueChannel());
        }
        context.setCx(request.getCx());
        return context;
    }

    private IssueCouponRequest issueCouponRequestAdapter(UnifiedissuecouponRequest request, IMobileContext iMobileContext, int code) {
        IssueCouponRequest issueCouponRequest = new IssueCouponRequest();

        issueCouponRequest.setShopIdL(request.getShopidL() == null ? 0 : request.getShopidL());
        issueCouponRequest.setShopUuid(request.getShopuuid());
        issueCouponRequest.setProductId(request.getProductid() == null ? 0 : request.getProductid());
        issueCouponRequest.setLongProductId(request.getLongProductId());
        if (request.getCityid() != null) {
            issueCouponRequest.setCityId(request.getCityid());
        }
        if (request.getLatitude() != null) {
            issueCouponRequest.setLatitude(request.getLatitude());
        }
        if (request.getLongitude() != null) {
            issueCouponRequest.setLongitude(request.getLongitude());
        }
        if (request.getProducttype() == null) {
            issueCouponRequest.setShopProductType(0);
        } else if (request.getProducttype() == ProductTypeEnum.DEAL.code) {
            issueCouponRequest.setShopProductType(ShopProductType.DEFAULT.getValue());
        } else if (request.getProducttype() == ProductTypeEnum.SKU.code) {
            issueCouponRequest.setShopProductType(ShopProductType.GENERAL_TRADE.getValue());
        } else if (request.getProducttype() == ProductTypeEnum.SPU.code) {
            issueCouponRequest.setShopProductType(ShopProductType.GENERAL_TRADE.getValue());
        }
        issueCouponRequest.setCouponOptionId(request.getCoupongroupid() == null ? 0 : request.getCoupongroupid());
        issueCouponRequest.setStringCouponOptionId(request.getCoupongroupid() == null ? null : request.getCoupongroupid() + "");
        //TODO 兼容字符型券批次
        if(issueCouponRequest.getCouponOptionId() == 0){
            issueCouponRequest.setCouponOptionId(NumberUtils.toInt(request.getUnifiedCouponGroupId()));
            issueCouponRequest.setStringCouponOptionId(request.getUnifiedCouponGroupId());
        }

        ClientInfo clientInfo = new ClientInfo();
        clientInfo.setCx(request.getCx());
        issueCouponRequest.setClientInfo(clientInfo);
        if (!iMobileContext.isMtClient() && iMobileContext.getUserId() > 0) {
            issueCouponRequest.setUserId(iMobileContext.getUserId());
        } else if (iMobileContext.isMtClient() && iMobileContext.getUserStatus() != null && iMobileContext.getUserStatus().getMtUserId() > 0) {
            issueCouponRequest.setUserId(iMobileContext.getUserStatus().getMtUserId());
        }
        issueCouponRequest.setDpClient(!iMobileContext.isMtClient());
        issueCouponRequest.setRequestType(code);
        issueCouponRequest.setIssueDetailSourceCode(request.getIssueDetailSourceCode());

        // 投放券参数填充
        issueCouponRequest.setActivityid(request.getActivityid());
        issueCouponRequest.setMaterialid(request.getMaterialid());
        issueCouponRequest.setResourceactivityid(request.getResourceactivityid());
        issueCouponRequest.setFlowid(request.getFlowid());
        issueCouponRequest.setRowkey(request.getRowkey());

        if (StringUtils.isNotEmpty(request.getUuid())) {
            issueCouponRequest.setUuid(request.getUuid());
        } else {
            if (ApiUtil.isMapiRequest(iMobileContext)) {
                if (issueCouponRequest.isDpClient()) {
                    issueCouponRequest.setUuid(iMobileContext.getHeader().getDpid());
                } else {
                    issueCouponRequest.setUuid(iMobileContext.getHeader().getUuid());
                }
            }else{
                issueCouponRequest.setUuid(iMobileContext.getRequest().getHeader("openId"));
            }
        }
        issueCouponRequest.setCouponPageSource(request.getCouponPageSource());
        issueCouponRequest.setCPlatform(request.getCplatform());
        issueCouponRequest.setMtFingerprint(request.getMtfingerprint());
        issueCouponRequest.setActualCityId(request.getActualcityid());
        issueCouponRequest.setPackagesecretkey(request.getPackagesecretkey());
        if (iMobileContext.getRequest() != null) {
            issueCouponRequest.setRegionId(iMobileContext.getRequest().getParameter("wtt_region_id"));
        }
        return issueCouponRequest;
    }

    @Override
    protected List<ClientInfoRule> getRule() {
        return null;
    }
}
