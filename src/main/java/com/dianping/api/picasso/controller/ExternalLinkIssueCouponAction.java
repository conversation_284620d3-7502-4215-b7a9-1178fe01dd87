package com.dianping.api.picasso.controller;

import com.dianping.api.service.MFlagWrapperService;
import com.dianping.api.util.ApiUtil;
import com.dianping.cat.Cat;
import com.dianping.mobile.base.datatypes.bean.ClientInfoRule;
import com.dianping.mobile.framework.action.DefaultAction;
import com.dianping.mobile.framework.annotation.Action;
import com.dianping.mobile.framework.datatypes.CommonMobileResponse;
import com.dianping.mobile.framework.datatypes.IMobileContext;
import com.dianping.mobile.framework.datatypes.IMobileResponse;
import com.dianping.mobile.framework.enums.ReqProtocol;
import com.dianping.pay.api.biz.activity.IssueCouponBiz;
import com.dianping.pay.api.biz.activity.RestUserInfoService;
import com.dianping.pay.api.biz.activity.newloader.dto.RestUserInfo;
import com.dianping.pay.api.biz.activity.newloader.mapper.IssueActivityMapper;
import com.dianping.pay.api.entity.issuecoupon.*;
import com.dianping.pay.api.exception.IssueCouponException;
import com.dianping.tgc.dto.CouponExternalLink;
import com.dianping.violet.enums.CouponRequestTypeEnum;
import com.google.common.collect.Maps;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang.Validate;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2020/12/9
 */
@Controller("promo/externallinkissuecoupon.bin")
@Action(url = "promo/externallinkissuecoupon.bin", protocol = ReqProtocol.REST, httpType = "post")
public class ExternalLinkIssueCouponAction extends DefaultAction<ExternalLinkIssueCouponRequest> {

    public static final Logger logger = LoggerFactory.getLogger(ExternalLinkIssueCouponAction.class);

    @Autowired
    private MFlagWrapperService mFlagWrapperService;

    @Autowired
    private IssueCouponBiz issueCouponBiz;

    @Autowired
    private RestUserInfoService restUserInfoService;

    @Override
    protected IMobileResponse validate(ExternalLinkIssueCouponRequest request, IMobileContext iMobileContext) {
        Cat.logEvent("INVALID_METHOD_2", "com.dianping.api.picasso.controller.ExternalLinkIssueCouponAction.validate(ExternalLinkIssueCouponRequest,IMobileContext)");
        try {
            Validate.notNull(request, "null request");
            Validate.isTrue(StringUtils.isNotBlank(request.getCouponExternalLink()), "invalid couponExternalLink");
            //其他参数均使用内置校验
        } catch (Exception e) {
            log.warn("validate request exception!", e);
            return new CommonMobileResponse(ExternalLinkCouponResponse.errorResult(e.getMessage()));
        }
        return null;
    }

    @Override
    protected IMobileResponse execute(ExternalLinkIssueCouponRequest request, IMobileContext context) {
        Cat.logEvent("INVALID_METHOD_1", "com.dianping.api.picasso.controller.ExternalLinkIssueCouponAction.execute(ExternalLinkIssueCouponRequest,IMobileContext)");
        try {
            String couponExternalLinkStr = mFlagWrapperService.getExternalLink(request.getCouponExternalLink());
            if (StringUtils.isBlank(couponExternalLinkStr)) {
                return new CommonMobileResponse(ExternalLinkCouponResponse.errorResult("券不存在"));
            }
            IssueCouponRequest issueCouponRequest;
            CouponExternalLink couponExternalLink = CouponExternalLink.buildByStr(couponExternalLinkStr);
            // 如果是h5场景
            if (!ApiUtil.isMapiRequest(context)) {
                RestUserInfo restUserInfo = restUserInfoService.handleMobileContext(context);
                issueCouponRequest =  new IssueCouponRequest(request, restUserInfo, Integer.parseInt(couponExternalLink.getCouponGroupId()), CouponRequestTypeEnum.ISSUE.getCode());
            } else {
                issueCouponRequest =  new IssueCouponRequest(request, context, Integer.parseInt(couponExternalLink.getCouponGroupId()), CouponRequestTypeEnum.ISSUE.getCode());
            }
            logger.info("ExternalLinkIssueCouponAction# issueCouponRequest:{}, request:{}", issueCouponRequest, request);
            if (issueCouponRequest.getUserId() == null || issueCouponRequest.getUserId() <= 0) {
                return new CommonMobileResponse(ExternalLinkCouponResponse.errorResult("用户未登录"));
            }
            Map<String, String> riskMap = Maps.newHashMap();
            riskMap.put("cx", request.getCx());
            riskMap.put("dpid", request.getDpid());
            riskMap.put("useragent", context.getUserAgent());
            riskMap.put("ip", context.getUserIp());
            riskMap.put("env", "app");
            riskMap.put("event", "mapi-pay-promo-web");
            riskMap.put("envArray", "[app]");
            if (null != request.getDevicePlatform()) {
                riskMap.put("platform", String.valueOf(request.getDevicePlatform()));
            }
            UnifiedIssueResult unifiedIssueResult = issueCouponBiz.issueExternalCoupon(issueCouponRequest, riskMap);
            UnifiedIssueCouponRespDo unifiedIssueCouponRespDo = new UnifiedIssueCouponRespDo(true, "");
            unifiedIssueCouponRespDo.setBeginTime(unifiedIssueResult.getBeginTime());
            unifiedIssueCouponRespDo.setEndTime(unifiedIssueResult.getEndTime());
            unifiedIssueCouponRespDo.setCouponGroupName(unifiedIssueResult.getCouponGroupName());
            unifiedIssueCouponRespDo.setUnifiedCouponId(unifiedIssueResult.getUnifiedCouponId());
            logger.info("ExternalLinkIssueCouponAction# external link issueCoupon result:{}", unifiedIssueResult);
            return new CommonMobileResponse(ExternalLinkCouponResponse.successResult(unifiedIssueCouponRespDo));
        } catch (IssueCouponException e) {
            logger.error("ExternalLinkIssueCouponAction# external link issueCoupon exception, request:{}", request, e);
            UnifiedIssueCouponRespDo unifiedIssueCouponRespDo = new UnifiedIssueCouponRespDo(new IssueCouponErrorDo(e.getCode(), e.getMessage()));
            return new CommonMobileResponse(ExternalLinkCouponResponse.successResult(unifiedIssueCouponRespDo));
        }
    }

    @Override
    protected List<ClientInfoRule> getRule() {
        Cat.logEvent("INVALID_METHOD_2", "com.dianping.api.picasso.controller.ExternalLinkIssueCouponAction.getRule()");
        return null;
    }
}
