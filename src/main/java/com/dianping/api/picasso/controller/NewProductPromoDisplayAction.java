package com.dianping.api.picasso.controller;

import com.dianping.api.util.ApiUtil;
import com.dianping.api.util.DotUtils;
import com.dianping.lion.client.Lion;
import com.dianping.mobile.base.datatypes.bean.ClientInfoRule;
import com.dianping.mobile.framework.action.DefaultAction;
import com.dianping.mobile.framework.annotation.Action;
import com.dianping.mobile.framework.datatypes.CommonMobileResponse;
import com.dianping.mobile.framework.datatypes.IMobileContext;
import com.dianping.mobile.framework.datatypes.IMobileResponse;
import com.dianping.mobile.framework.enums.ReqProtocol;
import com.dianping.pay.api.biz.activity.RestUserInfoService;
import com.dianping.pay.api.biz.activity.UnifiedIssueCouponBiz;
import com.dianping.pay.api.biz.activity.newloader.CouponIssueActivityQueryService;
import com.dianping.pay.api.biz.activity.newloader.dto.CouponActivityContext;
import com.dianping.pay.api.biz.activity.newloader.dto.PromotionRequestContext;
import com.dianping.pay.api.biz.activity.newloader.dto.RestUserInfo;
import com.dianping.pay.api.biz.activity.newloader.mapper.IssueActivityMapper;
import com.dianping.pay.api.biz.activity.newloader.mapper.ProductPromoInfoMapper;
import com.dianping.pay.api.entity.issuecoupon.IssueCouponMsg;
import com.dianping.pay.api.entity.issuecoupon.ProductPromoInfo;
import com.dianping.pay.api.entity.issuecoupon.ProductcouponpromoRequest;
import com.dianping.pay.api.util.LionConstants;
import com.dianping.pay.api.util.PoiIdUtils;
import com.dianping.pay.framework.utils.IdCryptoUtils;
import com.dianping.pay.framework.utils.shopUuid.ShopUuidUtils;
import java.util.List;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.Validate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;

@Controller(value = "newproductpromodisplay.bin")
@Action(url = "newproductpromodisplay.bin", httpType = "post", protocol = {ReqProtocol.MAPI, ReqProtocol.REST})
@Slf4j
public class NewProductPromoDisplayAction extends DefaultAction<ProductcouponpromoRequest> {

    @Autowired
    private CouponIssueActivityQueryService couponIssueActivityQueryService;

    @Autowired
    private UnifiedIssueCouponBiz unifiedIssueCouponBiz;

    @Autowired
    private ShopUuidUtils shopUuidUtils;

    @Autowired
    private RestUserInfoService restUserInfoService;

    @Override
    protected IMobileResponse validate(ProductcouponpromoRequest request, IMobileContext context) {
        try {
            Validate.notNull(request, "null request");
            IdCryptoUtils.decryptByRequest(request);
            Validate.isTrue(request.getProductid() != null, "productId is null");
            Long requestShopid = request.getShopIdL();
            String shopuuid = request.getShopuuid();
            if (requestShopid == null || requestShopid <= 0) {
                if (shopUuidUtils.isShopUuid(shopuuid)) {
                    Long shopId = shopUuidUtils.toShopId(shopuuid);
                    request.setShopIdL(shopId);
                    request.setShopid(PoiIdUtils.poiIdLongToInt(shopId));
                }
            }
        } catch (Exception e) {
            log.warn("NewProductPromoDisplayAction validate request exception!", e);
            return new CommonMobileResponse(new IssueCouponMsg(401, e.getMessage(), e.getMessage()));
        }
        return null;
    }

    @Override
    protected IMobileResponse execute(ProductcouponpromoRequest request, IMobileContext context) {
        PromotionRequestContext promotionRequestContext;
        try {
            //降级开关
            if(Lion.getBooleanValue(LionConstants.NEW_PRODUCT_PROMOTION_ACTION_DEGRADE_SWITCH, false)) {
                return new CommonMobileResponse(new ProductPromoInfo());
            }
            // 如果是h5场景
            if (!ApiUtil.isMapiRequest(context)) {
                RestUserInfo restUserInfo = restUserInfoService.handleMobileContext(context);
                promotionRequestContext = IssueActivityMapper.buildHttpPromotionRequestContext(request, context, restUserInfo);
            } else {
                promotionRequestContext = IssueActivityMapper.buildNativePromotionRequestContext(request, context);
            }
            CouponActivityContext couponActivityContext = couponIssueActivityQueryService.queryShopPromotions(promotionRequestContext, context);
            ProductPromoInfo productPromoInfo = ProductPromoInfoMapper.buildProductPromoInfo(couponActivityContext, context);
            if(Lion.getBooleanValue(LionConstants.NEW_PRODUCT_PROMOTION_ACTION_LOG_SWITCH, false)) {
                log.info("NewProductPromoDisplayAction ProductPromoDisplayAction# request:{}, ctx: {}, response:{}", request, couponActivityContext, productPromoInfo);
            }
            DotUtils.addCouponComponentQueryResultEvent("newproductpromodisplay", couponListIsNotEmpty(productPromoInfo));
            return new CommonMobileResponse(productPromoInfo);
        } catch (Exception e) {
            log.error("NewProductPromoDisplayAction productpromodisplay# error. request: {}", request, e);
            return new CommonMobileResponse(new ProductPromoInfo());
        }

    }

    @Override
    protected List<ClientInfoRule> getRule() {
        return null;
    }

    public boolean couponListIsNotEmpty(ProductPromoInfo productPromoInfo) {
        if(productPromoInfo != null
            && productPromoInfo.getProductPromoConciseInfo() != null) {
            return true;
        }
        return false;
    }
}
