package com.dianping.api.picasso.controller;

import com.dianping.api.util.ApiUtil;
import com.dianping.mobile.base.datatypes.bean.ClientInfoRule;
import com.dianping.mobile.framework.action.DefaultAction;
import com.dianping.mobile.framework.annotation.Action;
import com.dianping.mobile.framework.datatypes.CommonMobileResponse;
import com.dianping.mobile.framework.datatypes.IMobileContext;
import com.dianping.mobile.framework.datatypes.IMobileResponse;
import com.dianping.mobile.framework.enums.ReqProtocol;
import com.dianping.pay.api.biz.activity.RestUserInfoService;
import com.dianping.pay.api.biz.activity.newloader.CouponIssueActivityQueryService;
import com.dianping.pay.api.biz.activity.newloader.dto.CouponActivityContext;
import com.dianping.pay.api.biz.activity.newloader.dto.PromotionRequestContext;
import com.dianping.pay.api.biz.activity.newloader.dto.RestUserInfo;
import com.dianping.pay.api.biz.activity.newloader.mapper.BrandPromoInfoMapper;
import com.dianping.pay.api.biz.activity.newloader.mapper.IssueActivityMapper;
import com.dianping.pay.api.entity.issuecoupon.BrandCouponInfo;
import com.dianping.pay.api.entity.issuecoupon.BrandPromoDisplayActionRequest;
import com.dianping.pay.api.entity.issuecoupon.ResultWrapper;
import com.dianping.pay.api.util.LogUtils;
import com.google.common.collect.Lists;
import com.meituan.servicecatalog.api.annotations.InterfaceDoc;
import org.apache.commons.lang.Validate;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Controller;

import javax.annotation.Resource;
import java.util.List;

@InterfaceDoc(
        displayName = "品牌详情页领券组件",
        type = "restful",
        description = "品牌详情页领券组件",
        scenarios = "品牌详情页领券组件",
        host = "",
        authors = "xiemingyuan"
)
@Controller(value = "brandpromodisplay.bin")
@Action(url = "brandpromodisplay.bin", httpType = "post", protocol = {ReqProtocol.MAPI, ReqProtocol.REST})
public class BrandIssueCouponComponentAction extends DefaultAction<BrandPromoDisplayActionRequest> {

    public static final Logger log = LoggerFactory.getLogger(BrandIssueCouponComponentAction.class);

    @Resource
    private RestUserInfoService restUserInfoService;

    @Resource
    private CouponIssueActivityQueryService couponIssueActivityQueryService;

    @Resource
    private LogUtils logUtils;

    @Override
    protected IMobileResponse validate(BrandPromoDisplayActionRequest request, IMobileContext context) {
        try {
            Validate.notNull(request, "null request");
            Validate.isTrue(request.getBrandId() != null && request.getBrandId() > 0, "invalid brandId");
        } catch (Exception e) {
            log.warn("validate request exception!, request:{}", request, e);
            return new CommonMobileResponse(Lists.newArrayList());
        }
        return null;
    }

    @Override
    protected IMobileResponse execute(BrandPromoDisplayActionRequest request, IMobileContext context) {
        try {
            PromotionRequestContext promotionRequestContext;
            List<BrandCouponInfo> brandCouponInfos;
            // 如果是h5场景
            if (!ApiUtil.isMapiRequest(context)) {
                RestUserInfo restUserInfo = restUserInfoService.handleMobileContext(context);
                promotionRequestContext = IssueActivityMapper.buildHttpPromotionRequestContext(request, restUserInfo);
            } else {
                promotionRequestContext = IssueActivityMapper.buildNativePromotionRequestContext(request, context);
            }
            CouponActivityContext couponActivityContext = couponIssueActivityQueryService.queryShopPromotions(promotionRequestContext, context);
            brandCouponInfos = BrandPromoInfoMapper.buildBrandInfo(couponActivityContext, context);
            if (brandCouponInfos == null) {
                brandCouponInfos = Lists.newArrayList();
            }
            logUtils.logInfo("BrandIssueCouponComponentAction#. request: {}, response: {}", request, brandCouponInfos);
            return new CommonMobileResponse(ResultWrapper.success(brandCouponInfos));
        } catch (Exception e) {
            log.error("BrandIssueCouponComponentAction# error. request: {}", request);
            return new CommonMobileResponse(ResultWrapper.fail("内部错误"));

        }
    }

    @Override
    protected List<ClientInfoRule> getRule() {
        return null;
    }
}

