package com.dianping.api.picasso.controller;

import com.dianping.api.util.ApiUtil;
import com.dianping.api.util.PositionUtils;
import com.dianping.cat.Cat;
import com.dianping.cat.message.Transaction;
import com.dianping.mobile.base.datatypes.bean.ClientInfoRule;
import com.dianping.mobile.framework.action.DefaultAction;
import com.dianping.mobile.framework.annotation.Action;
import com.dianping.mobile.framework.datatypes.CommonMobileResponse;
import com.dianping.mobile.framework.datatypes.IMobileContext;
import com.dianping.mobile.framework.datatypes.IMobileResponse;
import com.dianping.mobile.framework.enums.ReqProtocol;
import com.dianping.pay.api.biz.activity.RestUserInfoService;
import com.dianping.pay.api.biz.activity.UnifiedIssueCouponBiz;
import com.dianping.pay.api.biz.activity.newloader.CouponIssueActivityQueryService;
import com.dianping.pay.api.biz.activity.newloader.dto.CouponActivityContext;
import com.dianping.pay.api.biz.activity.newloader.dto.PromotionRequestContext;
import com.dianping.pay.api.biz.activity.newloader.dto.RestUserInfo;
import com.dianping.pay.api.biz.activity.newloader.mapper.IssueActivityMapper;
import com.dianping.pay.api.entity.issuecoupon.*;
import com.dianping.pay.api.util.CatUtils;
import com.dianping.pay.api.util.PoiIdUtils;
import com.dianping.pay.framework.utils.IdCryptoUtils;
import com.dianping.pay.framework.utils.shopUuid.ShopUuidUtils;
import com.meituan.servicecatalog.api.annotations.*;
import com.sankuai.nib.mkt.common.base.util.JsonUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.Validate;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;

import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * 移动之家链接：https://mobile.sankuai.com/studio/api/17088/index
 */

@InterfaceDoc(
        displayName = "新版商户详情页领券组件（医美）",
        type = "restful",
        description = "新版商户详情页领券组件（医美）",
        scenarios = "新版商户详情页领券组件",
        host = "",
        authors = "xiemingyuan"
)
@Controller(value = "issuecouponcomponent.bin")
@Action(url = "issuecouponcomponent.bin", httpType = "post", protocol = {ReqProtocol.MAPI, ReqProtocol.REST})
@Slf4j
public class IssueCouponComponentAction extends DefaultAction<IssuecouponcomponentRequest> {

    @Autowired
    private CouponIssueActivityQueryService couponIssueActivityQueryService;

    @Autowired
    private UnifiedIssueCouponBiz unifiedIssueCouponBiz;

    @Autowired
    private ShopUuidUtils shopUuidUtils;

    @Autowired
    private RestUserInfoService restUserInfoService;

    @Override
    protected IMobileResponse validate(IssuecouponcomponentRequest request, IMobileContext context) {
        try {
            Validate.notNull(request, "null request");
            IdCryptoUtils.decryptByRequest(request);
            Long requestShopid = request.getShopIdL();
            String shopuuid = request.getShopuuid();

            if (requestShopid == null || requestShopid <= 0) {
                if (shopUuidUtils.isShopUuid(shopuuid)) {
                    Long shopId = shopUuidUtils.toShopId(shopuuid);
                    request.setShopidL(shopId);
                    request.setShopid(PoiIdUtils.poiIdLongToInt(shopId));
                }
            }
            Validate.isTrue(request.getShopIdL() != null && request.getShopIdL() > 0, "invalid shopId");
            //其他参数均使用内置校验
        } catch (Exception e) {
            log.warn("validate request exception!", e);
            return new CommonMobileResponse(new IssueCouponMsg(401, e.getMessage(), e.getMessage()));
        }
        return null;
    }

    @MethodDoc(requestMethods = HttpMethod.POST,
            urls = "issueCouponComponentAction.bin",
            displayName = "新版商户详页领券组件（医美）",
            description = "新版商户详页领券组件",
            returnValueDescription = "商户详页新版领券组件-展示信息",
            restExampleUrl = "",
            parameters = {
                    @ParamDoc(
                            name = "request",
                            description = "issuecouponcomponent.bin的请求参数",
                            type = PromotionDisplayActionRequest.class,
                            paramType = ParamType.REQUEST_BODY
                    ),
                    @ParamDoc(
                            name = "iMobileContext",
                            description = "mapi-shell协议上下文",
                            type = IMobileContext.class,
                            paramType = ParamType.REQUEST_BODY
                    )
            },
            responseParams = {@ParamDoc(name = "iMobileResponse", description = "通用返回数据模型")},
            extensions = {
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE",
                            content = "公开数据，无需建券"
                    ),
            }
    )
    @Override
    protected IMobileResponse execute(IssuecouponcomponentRequest request, IMobileContext context) {
        Transaction t;
        if (request != null && Objects.equals(request.getNeedshopresourcespromotion(), 1)) {
            // 休娱流量
            t = Cat.newTransaction("issueCouponComponentAction", "resourcePromo");
        } else if (request != null && Objects.equals(request.getNeedshopresourcespromotion(), 0)) {
            // 丽人+LE流量
            t = Cat.newTransaction("issueCouponComponentAction", "noResourcePromo");
        } else {
            t = Cat.newTransaction("issueCouponComponentAction", "other");
        }
        IMobileResponse response;
        try {
            log.info("IssueCouponComponentAction# IssuecouponcomponentRequest:{}", JsonUtils.toJSONString(request));
            PromotionRequestContext promotionRequestContext;
            if (!ApiUtil.isMapiRequest(context)) {
                RestUserInfo restUserInfo = restUserInfoService.handleMobileContext(context);
                promotionRequestContext = IssueActivityMapper.buildHttpPromotionRequestContext(request, restUserInfo, context);
            } else {
                promotionRequestContext = IssueActivityMapper.buildPromotionRequestContext(request, context);
            }
            
            if (request != null && StringUtils.isNotEmpty(request.getMagicMemberComponentVersion())) {
                promotionRequestContext.setMagicMemberComponentVersion(request.getMagicMemberComponentVersion());
            }

            // 上报校验点位参数
            PositionUtils.reportValidateParams(promotionRequestContext, context);
            CouponActivityContext couponActivityContext = couponIssueActivityQueryService.queryShopPromotions(promotionRequestContext, context);
            IssueCouponComponentDTO issueCouponComponentResp = unifiedIssueCouponBiz.toIssueCouponComponentResponse(couponActivityContext,
                    promotionRequestContext, context);
            log.info("IssueCouponComponentAction# resp promotionRequestContext:{}, issueCouponComponentResp:{}", JsonUtils.toJSONString(promotionRequestContext), JsonUtils.toJSONString(issueCouponComponentResp));
            CatUtils.catCouponCount(promotionRequestContext, issueCouponComponentResp);
            response = new CommonMobileResponse(issueCouponComponentResp);
            t.setStatus(Transaction.SUCCESS);
        } catch (Exception e) {
            log.error("IssueCouponComponentAction# error. request: {}", request, e);
            response = new CommonMobileResponse(new IssueCouponComponentDTO());
            t.setStatus(e);
        } finally {
            t.complete();
        }
        return response;
    }

    @Override
    protected List<ClientInfoRule> getRule() {
        return null;
    }
}
