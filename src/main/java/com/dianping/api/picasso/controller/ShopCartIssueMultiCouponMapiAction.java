package com.dianping.api.picasso.controller;

import com.dianping.api.util.CrossDomainUtils;
import com.dianping.mobile.base.datatypes.bean.ClientInfoRule;
import com.dianping.mobile.framework.action.DefaultAction;
import com.dianping.mobile.framework.annotation.Action;
import com.dianping.mobile.framework.datatypes.CommonMobileResponse;
import com.dianping.mobile.framework.datatypes.IMobileContext;
import com.dianping.mobile.framework.datatypes.IMobileResponse;
import com.dianping.mobile.framework.enums.ReqProtocol;
import com.dianping.pay.api.biz.ShopCartIssueMultiCouponBiz;
import com.dianping.pay.api.entity.issuecoupon.*;
import com.dianping.pay.api.exception.IssueCouponException;
import com.dianping.pay.api.util.DateUtils;
import com.google.common.collect.Lists;
import org.apache.commons.collections.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2021/1/21
 */
@Controller("promo/shopcartissuemulticouponmapiaction.bin")
@Action(url = "promo/shopcartissuemulticouponmapiaction.bin", protocol = ReqProtocol.REST, httpType = "post")
public class ShopCartIssueMultiCouponMapiAction extends DefaultAction<ShopCartIssueMultiCouponRequest> {

    public static final Logger log = LoggerFactory.getLogger(ShopCartIssueMultiCouponAction.class);

    @Autowired
    private ShopCartIssueMultiCouponBiz shopCartIssueMultiCouponBiz;

    @Override
    protected IMobileResponse validate(ShopCartIssueMultiCouponRequest request, IMobileContext iMobileContext) {
        return shopCartIssueMultiCouponBiz.validate(request, iMobileContext);
    }

    @Override
    protected IMobileResponse execute(ShopCartIssueMultiCouponRequest request, IMobileContext iMobileContext) {
        try {
            UnifiedIssueMultiCouponRespDo unifiedIssueMultiCouponRespDo = shopCartIssueMultiCouponBiz.doIssueShopCartCoupon(request, iMobileContext);
            if (unifiedIssueMultiCouponRespDo == null) {
                return new CommonMobileResponse(null);
            }

            return new CommonMobileResponse(convertToIssueDetailResult(unifiedIssueMultiCouponRespDo));
        } catch (IssueCouponException e) {
            log.error("ShopCartIssueMultiCouponMapiAction ex, request:{}", request, e);
            UnifiedIssueMultiCouponResp unifiedIssueCouponRespDo = new UnifiedIssueMultiCouponResp();
            unifiedIssueCouponRespDo.setBeSuccess(false);
            unifiedIssueCouponRespDo.setErrorMsg(e.getMessage());
            return new CommonMobileResponse(unifiedIssueCouponRespDo);
        }
    }

    private UnifiedIssueMultiCouponResp convertToIssueDetailResult(UnifiedIssueMultiCouponRespDo unifiedIssueMultiCouponRespDo) {
        UnifiedIssueMultiCouponResp unifiedIssueMultiCouponResp = new UnifiedIssueMultiCouponResp();
        unifiedIssueMultiCouponResp.setBeSuccess(unifiedIssueMultiCouponRespDo.isSuccess());
        unifiedIssueMultiCouponResp.setErrorMsg(unifiedIssueMultiCouponRespDo.getErrorMsg());
        if (CollectionUtils.isNotEmpty(unifiedIssueMultiCouponRespDo.getIssueDetail())) {
            List<IssueDetailResult> issueDetailResults = Lists.newArrayList();
            unifiedIssueMultiCouponResp.setIssueDetail(issueDetailResults);
            for (UnifiedIssueDetailResult issueDetailResult : unifiedIssueMultiCouponRespDo.getIssueDetail()) {
                IssueDetailResult issueDetail = new IssueDetailResult();
                issueDetail.setBeSuccess(issueDetailResult.isSuccess());
                issueDetail.setResultMessage(issueDetailResult.getResultMessage());
                issueDetail.setUnifiedCouponGroupId(issueDetailResult.getUnifiedCouponGroupId());
                issueDetail.setUnifiedCouponId(issueDetailResult.getUnifiedCouponId());
                issueDetail.setBeginTime(DateUtils.dateToStr(issueDetailResult.getBeginTime()));
                issueDetail.setEndTime(DateUtils.dateToStr(issueDetailResult.getEndTime()));
                issueDetail.setCouponGroupName(issueDetailResult.getCouponGroupName());
                issueDetail.setUseUrl(issueDetailResult.getUseUrl());
                issueDetail.setToastMsg(issueDetailResult.getToastMsg());
                issueDetailResults.add(issueDetail);
            }
        }
        return unifiedIssueMultiCouponResp;
    }

    @Override
    protected List<ClientInfoRule> getRule() {
        return null;
    }
}
