package com.dianping.api.picasso.controller;

import com.dianping.api.util.ApiUtil;
import com.dianping.api.util.LionQueryUtils;
import com.dianping.cat.Cat;
import com.dianping.mobile.base.datatypes.bean.ClientInfoRule;
import com.dianping.mobile.framework.action.DefaultAction;
import com.dianping.mobile.framework.annotation.Action;
import com.dianping.mobile.framework.base.datatypes.SimpleMsg;
import com.dianping.mobile.framework.base.datatypes.StatusCode;
import com.dianping.mobile.framework.datatypes.CommonMobileResponse;
import com.dianping.mobile.framework.datatypes.IMobileContext;
import com.dianping.mobile.framework.datatypes.IMobileResponse;
import com.dianping.mobile.framework.enums.ReqProtocol;
import com.dianping.pay.api.biz.activity.RestUserInfoService;
import com.dianping.pay.api.biz.activity.UnifiedIssueCouponBiz;
import com.dianping.pay.api.biz.activity.newloader.CouponIssueActivityQueryService;
import com.dianping.pay.api.biz.activity.newloader.dto.CouponActivityContext;
import com.dianping.pay.api.biz.activity.newloader.dto.PromotionRequestContext;
import com.dianping.pay.api.biz.activity.newloader.dto.RestUserInfo;
import com.dianping.pay.api.biz.activity.newloader.mapper.DealPromoInfoMapper;
import com.dianping.pay.api.biz.activity.newloader.mapper.IssueActivityMapper;
import com.dianping.pay.api.entity.issuecoupon.*;
import com.dianping.pay.api.util.JsonUtils;
import com.dianping.pay.api.util.PoiIdUtils;
import com.dianping.pay.framework.utils.IdCryptoUtils;
import com.dianping.pay.framework.utils.shopUuid.ShopUuidUtils;
import com.meituan.servicecatalog.api.annotations.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.Validate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;

import java.util.List;

/**
 * <AUTHOR>
 * 移动之家链接：https://mobile.sankuai.com/studio/api/17509/index
 */

@InterfaceDoc(
        displayName = "新版团购详情页领券组件",
        type = "restful",
        description = "新版团购详情页领券组件",
        scenarios = "新版团购详情页领券组件",
        host = "",
        authors = "xiemingyuan"
)
@Controller(value = "productcouponpromo.bin")
@Action(url = "productcouponpromo.bin", httpType = "post", protocol = {ReqProtocol.MAPI, ReqProtocol.REST})
@Slf4j
public class ProductCouponPromoAction extends DefaultAction<ProductcouponpromoRequest> {

    @Autowired
    private CouponIssueActivityQueryService couponIssueActivityQueryService;

    @Autowired
    private UnifiedIssueCouponBiz unifiedIssueCouponBiz;

    @Autowired
    private ShopUuidUtils shopUuidUtils;

    @Autowired
    private DealPromoInfoMapper dealPromoInfoMapper;

    @Autowired
    private RestUserInfoService restUserInfoService;

    @Override
    protected IMobileResponse validate(ProductcouponpromoRequest request, IMobileContext context) {
        try {
            Validate.notNull(request, "null request");
            IdCryptoUtils.decryptByRequest(request);
            Long requestShopid = request.getShopIdL();
            String shopuuid = request.getShopuuid();

            if (requestShopid == null || requestShopid <= 0) {
                if (shopUuidUtils.isShopUuid(shopuuid)) {
                    Long shopId = shopUuidUtils.toShopId(shopuuid);
                    request.setShopIdL(shopId);
                    request.setShopid(PoiIdUtils.poiIdLongToInt(shopId));
                }
            } else {
                request.setShopuuid(null);
            }
            Validate.isTrue(request.getProducttype() != null, "product type is null");
            Validate.isTrue(request.getProductid() != null, "product id is null");

            //Validate.isTrue(request.getShopid() != null && request.getShopid() > 0, "invalid shopId");
            //其他参数均使用内置校验
        } catch (Exception e) {
            log.warn("validate request exception!", e);
            return new CommonMobileResponse(new IssueCouponMsg(401, e.getMessage(), e.getMessage()));
        }
        return null;
    }

    @MethodDoc(requestMethods = HttpMethod.POST,
            urls = "issueCouponComponentAction.bin",
            displayName = "新版团购详页领券组件",
            description = "新版团购详页领券组件",
            returnValueDescription = "团购详页新版领券组件-展示信息",
            restExampleUrl = "",
            parameters = {
                    @ParamDoc(
                            name = "request",
                            description = "productcouponpromo.bin请求参数",
                            type = PromotionDisplayActionRequest.class,
                            paramType = ParamType.REQUEST_BODY
                    ),
                    @ParamDoc(
                            name = "iMobileContext",
                            description = "mapi-shell协议上下文",
                            type = IMobileContext.class,
                            paramType = ParamType.REQUEST_BODY
                    )
            },
            responseParams = {@ParamDoc(name = "iMobileResponse", description = "通用返回数据模型")},
            extensions = {
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE",
                            content = "公开数据，无需建券"
                    ),
            }
    )
    @Override
    protected IMobileResponse execute(ProductcouponpromoRequest request, IMobileContext context) {
        try {
            PromotionRequestContext promotionRequestContext;
            if (!ApiUtil.isMapiRequest(context)) {
                RestUserInfo restUserInfo = restUserInfoService.handleMobileContext(context);
                promotionRequestContext = IssueActivityMapper.buildHttpPromotionRequestContext(request, context, restUserInfo);
            } else {
                promotionRequestContext = IssueActivityMapper.buildNativePromotionRequestContext(request, context);
            }

            log.info("ProductCouponPromoAction# promotionRequestContext:{}", JsonUtils.toJson(promotionRequestContext));
            if(LionQueryUtils.BIN_PRODUCT_COUPON_PROMO_ANTI_CRAWLER_SWITCH){ //反爬开关
                if(promotionRequestContext.getUserId() < 1  && context.getUserId() < 1){ //用户未登录
                    log.info("ProductCouponPromoAction# UserId is empty");
                    Cat.logEvent("antiCrawler",ApiUtil.isMapiRequest(context) ? "ProductCouponPromoAction_MAPI":"ProductCouponPromoAction_H5");
                    return new CommonMobileResponse(new CouponProductPromoModule());
                }
            }
            CouponActivityContext couponActivityContext = couponIssueActivityQueryService.queryShopPromotions(promotionRequestContext, context);
            log.info("ProductCouponPromoAction# queryShopPromotions response:{}", couponActivityContext);
            CouponProductPromoModule couponProductPromoModule = dealPromoInfoMapper.buildCouponProductPromoModule(couponActivityContext, context);
            log.info("ProductCouponPromoAction# coupons query by promotion component:{}", JsonUtils.toJson(couponProductPromoModule));

            boolean noResult = noResult(couponProductPromoModule);
            if (noResult && ApiUtil.isMapiRequest(context)) {
                Cat.logEvent("Productcouponpromo", "empty");
                return new CommonMobileResponse(
                        new SimpleMsg("提示", "请稍后重试~", StatusCode.ACTIONEXCEPTION));
            }
            return new CommonMobileResponse(couponProductPromoModule);
        } catch (Exception e) {
            log.error("ProductCouponPromoAction#  exception. request: {}", request, e);
            return new CommonMobileResponse(
                    new SimpleMsg("提示", "请稍后重试~", StatusCode.ACTIONEXCEPTION));
        }
    }

    private boolean noResult(CouponProductPromoModule dealPromoModule) {
        if (dealPromoModule == null) {
            return true;
        }
        CouponConcisePromoInfo concisePromoInfo = dealPromoModule.getConcisePromoInfo();
        if (concisePromoInfo != null && CollectionUtils.isNotEmpty(concisePromoInfo.getPromoInfoItems())) {
            return false;
        }
        return true;
    }

    @Override
    protected List<ClientInfoRule> getRule() {
        return null;
    }
}
