package com.dianping.api.picasso;

import com.dianping.cat.Cat;
import com.dianping.mobile.base.datatypes.enums.ClientType;
import com.dianping.mobile.base.datatypes.enums.Platform;
import com.dianping.mobile.framework.datatypes.IMobileContext;
import com.dianping.pay.common.enums.PayPlatform;
import com.google.common.collect.Lists;

import java.util.List;

public class PayPlatformUtils {

    private static List<Integer> MT_PLATFORMS = Lists.newArrayList(
            PayPlatform.mt_pc.getCode(),
            PayPlatform.mt_wap_m.getCode(),
            PayPlatform.mt_iphone_native.getCode(),
            PayPlatform.mt_android_native.getCode(),
            PayPlatform.mt_weixin_api.getCode()
    );

    private static List<Integer> DP_APP_PLATFORMS = Lists.newArrayList(
            PayPlatform.dp_iphone_native.getCode(),
            PayPlatform.dp_android_native.getCode()
    );

    /**
     * convert to PayPlayform.code, return zero if the conversion fails.
     *
     * @see PayPlatform
     */
    public static int getPayPlatform(IMobileContext context) {
        Cat.logEvent("INVALID_METHOD_3", "com.dianping.api.picasso.PayPlatformUtils.getPayPlatform(com.dianping.mobile.framework.datatypes.IMobileContext)");
        if (context.getClient() == ClientType.MAINAPP_IPHONE) {
            return PayPlatform.dp_iphone_native.getCode();
        } else if (context.getClient() == ClientType.MAINAPP_ANDROID) {
            return PayPlatform.dp_android_native.getCode();
        } else if (context.isMtClient() && context.getClient().getPlatform() == Platform.iPhone) {
            return PayPlatform.mt_iphone_native.getCode();
        } else if (context.isMtClient() && context.getClient().getPlatform() == Platform.Android) {
            return PayPlatform.mt_android_native.getCode();
        } else {
            return 0;
        }
    }

    public static boolean isMtClient(int payPlatform) {
        Cat.logEvent("INVALID_METHOD_4", "com.dianping.api.picasso.PayPlatformUtils.isMtClient(int)");
        return MT_PLATFORMS.contains(payPlatform);
    }

    public static boolean isMtClient(IMobileContext mobileContext) {
        Cat.logEvent("INVALID_METHOD_1", "com.dianping.api.picasso.PayPlatformUtils.isMtClient(com.dianping.mobile.framework.datatypes.IMobileContext)");
        return isMtClient(getPayPlatform(mobileContext));
    }

    public static boolean isDpApp(int payPlatform) {
        Cat.logEvent("INVALID_METHOD_2", "com.dianping.api.picasso.PayPlatformUtils.isDpApp(int)");
        return DP_APP_PLATFORMS.contains(payPlatform);
    }
}
