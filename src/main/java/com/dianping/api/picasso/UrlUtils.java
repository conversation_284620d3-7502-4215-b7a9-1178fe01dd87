package com.dianping.api.picasso;


import com.dianping.cat.Cat;
import com.dianping.combiz.spring.util.PropertiesLoaderSupportUtils;
import com.dianping.gmkt.coupon.common.api.enums.CouponBusiness;
import com.dianping.gmkt.coupon.common.api.utils.TgCouponBusinessUtils;
import com.dianping.unified.coupon.manage.api.dto.UnifiedCouponGroupDTO;
import org.apache.commons.collections.CollectionUtils;

import java.util.List;

/**
 * Created by drin<PERSON> on 18/6/12.
 */

public class UrlUtils {
    private static final String urlTemplate = PropertiesLoaderSupportUtils.getProperty("mapi-pay-promo-web.unified.issue.coupon.component.pic.base.url","https://www.dpfile.com/app/pay-promo-static-pic/js/%s");

    public static String getDpIssuedIcon() {
        String picName = PropertiesLoaderSupportUtils.getProperty("mapi-pay-promo-web.unified.issue.coupon.component.issued.dp.icon","dp-issued-3x.d0dce73c749958bd80cd9995ea1fb9ef.png");
        return String.format(urlTemplate,picName);
    }

    public static String getMtIssuedIcon(){
        Cat.logEvent("INVALID_METHOD_5", "com.dianping.api.picasso.UrlUtils.getMtIssuedIcon()");
        String picName = PropertiesLoaderSupportUtils.getProperty("mapi-pay-promo-web.unified.issue.coupon.component.issued.mt.icon","mt-issued-3x.5460fdc903094d67281d7be1052243b7.png");
        return String.format(urlTemplate,picName);
    }

    public static String getDpCouponIcon() {
        String picName = PropertiesLoaderSupportUtils.getProperty("mapi-pay-promo-web.unified.issue.coupon.component.coupon.dp.icon","dp-coupon-3x.87e3aa8cad0ec2df08c91f3e2ecc886f.png");
        return String.format(urlTemplate,picName);
    }

    public static String getMtCouponIcon() {
        Cat.logEvent("INVALID_METHOD_2", "com.dianping.api.picasso.UrlUtils.getMtCouponIcon()");
        String picName = PropertiesLoaderSupportUtils.getProperty("mapi-pay-promo-web.unified.issue.coupon.component.mt.coupon.icon","mt-coupon-3x.cefea1637a54f5663314da3e8ad51614.png");
        return String.format(urlTemplate,picName);
    }


    public static String getDpToUseUrl(String unifiedCouponId) {
        String baseUrl = PropertiesLoaderSupportUtils.getProperty("mapi-pay-promo-web.unified.issue.coupon.component.dp.toUseUrl",
               "dianping://web?url=https%3a%2f%2fg.dianping.com%2fav%2frainbow%2f1737308%2findex.html%3fcouponid%3d");
        return String.format("%s%s",baseUrl,unifiedCouponId);

    }

    public static String getMtToUseUrl(String unifiedCouponId) {
        String baseUrl = PropertiesLoaderSupportUtils.getProperty("mapi-pay-promo-web.unified.issue.coupon.component.mt.toUseUrl",
                "imeituan://www.meituan.com/web?url=https%3a%2f%2fg.meituan.com%2fav%2frainbow%2f1737308%2findex.html%3fcouponid%3d");
        return String.format("%s%s",baseUrl,unifiedCouponId);

    }

    public static String getDpUsedIcon() {
        Cat.logEvent("INVALID_METHOD_5", "com.dianping.api.picasso.UrlUtils.getDpUsedIcon()");
        return PropertiesLoaderSupportUtils.getProperty("mapi-pay-promo-web.unified.issue.coupon.component.used.dp.url", "https://p1.meituan.net/dprainbow/a5fe07357ba1fc165b94777a4d01173e65764.png");
    }

    public static String getMtUsedIcon() {
        Cat.logEvent("INVALID_METHOD_1", "com.dianping.api.picasso.UrlUtils.getMtUsedIcon()");
        return PropertiesLoaderSupportUtils.getProperty("mapi-pay-promo-web.unified.issue.coupon.component.used.mt.url", "https://p1.meituan.net/dprainbow/cb6a89596cb6ac7eeda4c86099b9fa8469158.png");
    }

    public static String getCouponGroupToUseUrl(UnifiedCouponGroupDTO unifiedCouponGroupDTO, String unifiedCouponId, boolean dpClient) {
        String toUseUrl = "";
        if (unifiedCouponGroupDTO == null) {
            return toUseUrl;
        }

        boolean containsTuangou = false;
        boolean couponProductLimit = CollectionUtils.isNotEmpty(unifiedCouponGroupDTO.getCouponProductList());
        List<Integer> productCodeList = unifiedCouponGroupDTO.getProductCodeList();
        if (CollectionUtils.isNotEmpty(productCodeList)) {
            for (Integer productCode : productCodeList) {
                CouponBusiness couponBusiness = CouponBusiness.getByCode(productCode);
                if (couponBusiness == null) {
                    Cat.logEvent("UNKOWN_COUPON_BUSINESS", String.valueOf(productCode));
                    continue;
                }
                if (TgCouponBusinessUtils.isDzTuangouCouponBusiness(productCode)) {
                    containsTuangou = true;
                    break;
                }
            }
        }

        //仅在团购且有团单限制时才返回链接
        if (containsTuangou && couponProductLimit) {
            if (dpClient) {
                toUseUrl = getDpToUseUrl(unifiedCouponId);
            } else {
                toUseUrl = getMtToUseUrl(unifiedCouponId);
            }
        }
        return toUseUrl;
    }
}
