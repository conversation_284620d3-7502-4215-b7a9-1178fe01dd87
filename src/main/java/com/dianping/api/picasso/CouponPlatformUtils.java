package com.dianping.api.picasso;

import com.dianping.gmkt.coupon.common.api.enums.MtCouponPlatform;
import com.dianping.mobile.base.datatypes.enums.ClientType;
import com.dianping.mobile.base.datatypes.enums.Platform;
import com.dianping.mobile.framework.datatypes.IMobileContext;
import com.dianping.pay.common.enums.PayPlatform;

/**
 * Created by ivan on 2019/3/2.
 */
public class CouponPlatformUtils {
    public static int getCouponPlatform(IMobileContext context) {
        if (context.getClient() == ClientType.MAINAPP_IPHONE) {
            return PayPlatform.dp_iphone_native.getCode();
        } else if (context.getClient() == ClientType.MAINAPP_ANDROID) {
            return PayPlatform.dp_android_native.getCode();
        } else if (context.isMtClient() && context.getClient().getPlatform() == Platform.iPhone) {
            return MtCouponPlatform.APP.getCode();
        } else if (context.isMtClient() && context.getClient().getPlatform() == Platform.Android) {
            return MtCouponPlatform.APP.getCode();
        } else {
            return 0;
        }
    }

    public static int getPayPlatform(IMobileContext context) {
        if (context.getClient() == ClientType.MAINAPP_IPHONE) {
            return PayPlatform.dp_iphone_native.getCode();
        } else if (context.getClient() == ClientType.MAINAPP_ANDROID) {
            return PayPlatform.dp_android_native.getCode();
        } else if (context.isMtClient() && context.getClient().getPlatform() == Platform.iPhone) {
            return PayPlatform.mt_iphone_native.getCode();
        } else if (context.isMtClient() && context.getClient().getPlatform() == Platform.Android) {
            return PayPlatform.mt_android_native.getCode();
        } else {
            return 0;
        }
    }
}
