package com.dianping.api.exception;

/**
 * Created by huawei.li on 14/11/13.
 */
public class IllegalApiParamException extends IllegalArgumentException {
    private int flag;
    private String title;

    public IllegalApiParamException(String msg, String code, int flag){
        super(msg);
        this.flag = flag;
        this.title = code;
    }
    public IllegalApiParamException(String msg, int flag){
        this(msg,null,flag);
    }

    public int getFlag() {
        return flag;
    }

    public void setFlag(int flag) {
        this.flag = flag;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

}
