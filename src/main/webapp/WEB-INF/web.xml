<!DOCTYPE web-app PUBLIC
 "-//Sun Microsystems, Inc.//DTD Web Application 2.3//EN"
 "http://java.sun.com/dtd/web-app_2_3.dtd" >

<web-app>
	<display-name>pay api server</display-name>

	<context-param>
		<param-name>contextConfigLocation</param-name>
		<param-value>
			classpath*:config/spring/common/appcontext-*.xml
			classpath*:config/spring/local/appcontext-*.xml
			classpath*:config/spring/appcontext-shell.xml
		</param-value>
	</context-param>

	<filter>
		<filter-name>cross-domain-filter</filter-name>
		<filter-class>com.dianping.api.filter.CrossDomainFilter</filter-class>
	</filter>
	<filter-mapping>
		<filter-name>cross-domain-filter</filter-name>
		<url-pattern>/*</url-pattern>
		<dispatcher>REQUEST</dispatcher>
		<dispatcher>FORWARD</dispatcher>
	</filter-mapping>

    <filter>
        <filter-name>biz-context-filter</filter-name>
        <filter-class>com.dianping.validationbiz.filter.AccountValidationFilter</filter-class>
    </filter>
	<filter>
		<filter-name>MtraceFilter</filter-name>
		<filter-class>com.meituan.mtrace.http.TraceFilter</filter-class>
	</filter>
    <filter-mapping>
        <filter-name>biz-context-filter</filter-name>
        <url-pattern>/*</url-pattern>
        <dispatcher>REQUEST</dispatcher>
        <dispatcher>FORWARD</dispatcher>
    </filter-mapping>

	<filter-mapping>
		<filter-name>MtraceFilter</filter-name>
		<url-pattern>/*</url-pattern>
	</filter-mapping>

	<filter>
		<filter-name>cat-filter</filter-name>
		<filter-class>com.dianping.cat.servlet.CatFilter</filter-class>
	</filter>

	<filter>
		<filter-name>InfFilter</filter-name>
		<filter-class>com.sankuai.oceanus.http.filter.InfFilter</filter-class>
		<init-param>
			<param-name>limit</param-name>
			<param-value>true</param-value>
		</init-param>
	</filter>

	<filter-mapping>
		<filter-name>cat-filter</filter-name>
		<url-pattern>/*</url-pattern>
		<dispatcher>REQUEST</dispatcher>
		<dispatcher>FORWARD</dispatcher>
	</filter-mapping>
	<filter-mapping>
		<filter-name>InfFilter</filter-name>
		<url-pattern>/*</url-pattern>
	</filter-mapping>

	<listener>
		<listener-class>com.dianping.cat.servlet.CatListener</listener-class>
	</listener>
	<listener>
		<listener-class>org.springframework.web.context.ContextLoaderListener</listener-class>
	</listener>
	<listener>
		<listener-class>org.apache.commons.fileupload.servlet.FileCleanerCleanup</listener-class>
	</listener>

	<servlet>
		<servlet-name>MainServlet</servlet-name>
		<display-name>MainServlet</display-name>
		<description>single entry</description>
		<servlet-class>com.dianping.api.servlet.ApiMainServlet</servlet-class>
	</servlet>
    <servlet>
        <servlet-name>MainJsonServlet</servlet-name>
        <display-name>MainJsonServlet</display-name>
        <description>json api</description>
        <servlet-class>com.dianping.api.servlet.ApiMainJsonServlet</servlet-class>
    </servlet>
    <servlet>
        <servlet-name>MainJsonpServlet</servlet-name>
        <display-name>MainJsonpServlet</display-name>
        <description>json api</description>
        <servlet-class>com.dianping.api.servlet.ApiMainJsonpServlet</servlet-class>
    </servlet>
	<servlet>
		<servlet-name>MonitorServlet</servlet-name>
		<display-name>MonitorServlet</display-name>
		<description>monitor servlet for F5 load balance heath check</description>
		<servlet-class>com.dianping.api.servlet.MonitorServlet</servlet-class>
	</servlet>
    <servlet>
        <servlet-name>HealthCheckServlet</servlet-name>
        <display-name>HealthCheckServlet</display-name>
        <servlet-class>com.dianping.pay.framework.servlet.HealthCheckServlet</servlet-class>
    </servlet>
	<servlet>
		<servlet-name>PicassoMainServlet</servlet-name>
		<display-name>PicassoMainServlet</display-name>
		<servlet-class>com.dianping.mobile.framework.servlet.MainServlet</servlet-class>
	</servlet>
	<servlet>
		<servlet-name>DispatcherServlet</servlet-name>
		<display-name>DispatcherServlet</display-name>
		<servlet-class>org.springframework.web.servlet.DispatcherServlet</servlet-class>
	</servlet>


	<servlet-mapping>
		<servlet-name>MainServlet</servlet-name>
		<url-pattern>*.pay</url-pattern>
	</servlet-mapping>
    <servlet-mapping>
        <servlet-name>MainJsonServlet</servlet-name>
        <url-pattern>*.json</url-pattern>
    </servlet-mapping>
    <servlet-mapping>
        <servlet-name>MainJsonpServlet</servlet-name>
        <url-pattern>*.jsonp</url-pattern>
    </servlet-mapping>
	<servlet-mapping>
		<servlet-name>PicassoMainServlet</servlet-name>
		<url-pattern>*.bin</url-pattern>
	</servlet-mapping>
    <servlet-mapping>
		<servlet-name>MonitorServlet</servlet-name>
		<url-pattern>/monitorservlet</url-pattern>
	</servlet-mapping>
    <servlet-mapping>
        <servlet-name>HealthCheckServlet</servlet-name>
        <url-pattern>/promo/healthcheck</url-pattern>
    </servlet-mapping>
	<servlet-mapping>
		<servlet-name>DispatcherServlet</servlet-name>
		<url-pattern>/promo/campaign/time</url-pattern>
	</servlet-mapping>

	<servlet-mapping>
		<servlet-name>DispatcherServlet</servlet-name>
		<url-pattern>/promo/campaign/render</url-pattern>
	</servlet-mapping>

	<servlet-mapping>
		<servlet-name>DispatcherServlet</servlet-name>
		<url-pattern>/promo/campaign/issue</url-pattern>
	</servlet-mapping>
	<servlet-mapping>
		<servlet-name>DispatcherServlet</servlet-name>
		<url-pattern>/promo/campaign/queryinflate</url-pattern>
	</servlet-mapping>

	<!-- Welcome file lists -->
	<welcome-file-list>
		<welcome-file>index.html</welcome-file>
	</welcome-file-list>

</web-app>
