<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xmlns="http://www.springframework.org/schema/beans"
       xsi:schemaLocation="http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans-3.2.xsd">

    <bean id="redisClient" class="com.dianping.squirrel.client.impl.redis.spring.RedisClientBeanFactory">
        <!-- 集群名称,必填 -->
        <property name="clusterName" value="redis-coupon"></property>
        <!--读取超时时间,缓存业务建议改成100，存储业务建议改成1000，默认值为1000。选填-->
        <property name="readTimeout" value="80"></property>
        <!--路由策略,默认值是master-only表示只从主节点读取。slave-only表示只读从节点,master-slave表示主从都可以读。选填-->
        <property name="routerType" value="master-slave"></property>
        <!--连接redis节点的连接池配置，选填-->
        <property name="poolMaxIdle" value="30"></property>
        <property name="poolMaxTotal" value="100"></property>
        <property name="poolWaitMillis" value="50"></property>
    </bean>

    <bean id="newRedisClient" class="com.dianping.squirrel.client.impl.redis.spring.RedisClientBeanFactory">
        <!-- 集群名称,必填 -->
        <property name="clusterName" value="redis-optcoupon-sh"></property>
        <!--读取超时时间,缓存业务建议改成100，存储业务建议改成1000，默认值为1000。选填-->
        <property name="readTimeout" value="80"></property>
        <!--路由策略,默认值是master-only表示只从主节点读取。slave-only表示只读从节点,master-slave表示主从都可以读。选填-->
        <property name="routerType" value="master-slave"></property>
        <!--连接redis节点的连接池配置，选填-->
        <property name="poolMaxIdle" value="30"></property>
        <property name="poolMaxTotal" value="100"></property>
        <property name="poolWaitMillis" value="50"></property>
    </bean>

</beans>