<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
       xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xsi:schemaLocation="http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans-2.5.xsd">

	<bean id="defaultRemoteInterceptor" class="com.dianping.api.healthcheck.dpsfwrap.DefaultRemoteInterceptor" />

    <bean id="discountQueryService" class="com.dianping.dpsf.spring.ProxyBeanFactory" init-method="init">
        <property name="serviceName" value="http://service.dianping.com/discountService/discountQueryService_1.0.0"/>
        <property name="iface" value="com.dianping.pay.discount.service.DiscountQueryService"/>
        <property name="serialize" value="hessian"/>
        <property name="callMethod" value="sync"/>
        <property name="timeout" value="1000"/>
    </bean>
    <bean id="privateAccountQueryService" class="com.dianping.dpsf.spring.ProxyBeanFactory" init-method="init">
        <property name="serviceName" value="http://service.dianping.com/accountQueryService/privateAccountQueryRemoteService_1.0.0"/>
        <property name="iface" value="com.dianping.pay.account.service.personal.PrivateAccountQueryService"/>
        <property name="serialize" value="hessian"/>
        <property name="callMethod" value="sync"/>
        <property name="timeout" value="5000"/>
    </bean>
    <bean id="dealGroupBaseService" class="com.dianping.dpsf.spring.ProxyBeanFactory" init-method="init">
        <property name="serviceName" value="http://service.dianping.com/tuangou/dealService/dealGroupBaseService_1.0.0"/>
        <property name="iface" value="com.dianping.deal.base.DealGroupBaseService"/>
        <property name="serialize" value="hessian"/>
        <property name="callMethod" value="sync"/>
        <property name="timeout" value="1000"/>
    </bean>

    <bean id="dealBaseService" class="com.dianping.dpsf.spring.ProxyBeanFactory" init-method="init">
        <property name="serviceName" value="http://service.dianping.com/tuangou/dealService/dealBaseService_1.0.0"/>
        <property name="iface" value="com.dianping.deal.base.DealBaseService"/>
        <property name="serialize" value="hessian"/>
        <property name="callMethod" value="sync"/>
        <property name="timeout" value="1000"/>
    </bean>
    <bean id="promoBizPaymentService" class="com.dianping.dpsf.spring.ProxyBeanFactory" init-method="init">
        <property name="serviceName" value="http://service.dianping.com/TpPromoService/IPromoBizPaymentService_1.0.0" />
        <property name="iface" value="com.dianping.tp.promo.api.service.IPromoBizPaymentService" />
        <property name="serialize" value="hessian" />
        <property name="callMethod" value="sync" />
        <property name="timeout" value="1000" />
    </bean>
    <bean id="promoDisplayService" class="com.dianping.dpsf.spring.ProxyBeanFactory" init-method="init">
        <property name="serviceName" value="http://service.dianping.com/payPromoDisplayService/PromoDisplayService_1.0.0" />
        <property name="iface" value="com.dianping.pay.promo.display.api.PromoDisplayService" />
        <property name="serialize" value="hessian" />
        <property name="callMethod" value="sync" />
        <property name="timeout" value="1000" />
    </bean>
	<bean id="payPromoExecuteQueryService" class="com.dianping.dpsf.spring.ProxyBeanFactory" init-method="init">
        <property name="serviceName" value="http://service.dianping.com/payPromoExecuteRemoteService/PayPromoExecuteQueryService_1.0.0"/>
        <property name="iface" value="com.dianping.pay.promo.execute.service.PayPromoExecuteQueryService"/>
        <property name="serialize" value="hessian"/>
        <property name="callMethod" value="sync"/>
        <property name="timeout" value="5000"/>
    </bean>
	<bean id="payPromoDeskQueryService" class="com.dianping.dpsf.spring.ProxyBeanFactory" init-method="init">
        <property name="serviceName" value="http://service.dianping.com/payPromoExecuteRemoteService/PayPromoDeskQueryService_1.0.0"/>
        <property name="iface" value="com.dianping.pay.promo.execute.service.PayPromoDeskQueryService"/>
        <property name="serialize" value="hessian"/>
        <property name="callMethod" value="sync"/>
        <property name="timeout" value="100"/>
    </bean>
    <bean id="unifiedCouponInfoService" class="com.dianping.dpsf.spring.ProxyBeanFactory" init-method="init">
        <property name="serviceName" value="http://service.dianping.com/unifiedCouponInfoQueryRemoteService/UnifiedCouponInfoService_1.0.0"/>
        <property name="iface" value="com.dianping.unified.coupon.manage.api.UnifiedCouponInfoService"/>
        <property name="serialize" value="hessian"/>
        <property name="callMethod" value="sync"/>
        <property name="timeout" value="1000"/>
    </bean>
    <bean id="unifiedCouponListService" class="com.dianping.dpsf.spring.ProxyBeanFactory" init-method="init">
        <property name="serviceName" value="http://service.sankuai.com/mpmkt/coupon/execute/UnifiedCouponListService_1.0.0"/>
        <property name="iface" value="com.dianping.unified.coupon.manage.api.UnifiedCouponListService"/>
        <property name="serialize" value="hessian"/>
        <property name="callMethod" value="sync"/>
        <property name="timeout" value="2000"/>
    </bean>
    <bean id="unifiedCouponGroupQueryService" class="com.dianping.dpsf.spring.ProxyBeanFactory" init-method="init">
        <property name="serviceName" value="http://service.dianping.com/unifiedCouponGroupQueryRemoteService/UnifiedCouponGroupQueryService_1.0.0"/>
        <property name="iface" value="com.dianping.unified.coupon.manage.api.UnifiedCouponGroupQueryService"/>
        <property name="serialize" value="hessian"/>
        <property name="callMethod" value="sync"/>
        <property name="timeout" value="2000"/>
    </bean>
    <bean id="unifiedCouponIssueTrustService" class="com.dianping.dpsf.spring.ProxyBeanFactory" init-method="init">
        <property name="serviceName" value="http://service.dianping.com/UnifiedCouponIssueTrustRemoteService/UnifiedCouponIssueTrustService_1.0.0"/>
        <property name="iface" value="com.dianping.unified.coupon.issue.api.UnifiedCouponIssueTrustService"/>
        <property name="serialize" value="hessian"/>
        <property name="callMethod" value="sync"/>
        <property name="timeout" value="2000"/>
    </bean>
    <bean id="unifiedCouponExtendService" class="com.dianping.dpsf.spring.ProxyBeanFactory" init-method="init">
        <property name="serviceName" value="http://service.dianping.com/unifiedCouponExtendRemoteService/UnifiedCouponExtendService_1.0.0"/>
        <property name="iface" value="com.dianping.unified.coupon.manage.api.UnifiedCouponExtendService"/>
        <property name="serialize" value="hessian"/>
        <property name="callMethod" value="sync"/>
        <property name="timeout" value="1000"/>
    </bean>
    <bean id="adFetchService" class="com.dianping.dpsf.spring.ProxyBeanFactory" init-method="init">
		<property name="serviceName" value="http://service.dianping.com/mktAdvService/IAdFetchService_1.0.0"/>
		<property name="iface" value="com.dianping.mkt.adv.api.service.IAdFetchService"/>
		<property name="serialize" value="hessian"/>
		<property name="callMethod" value="sync"/>
		<property name="timeout" value="2000"/>
	</bean>
    <bean id="shopActivityQueryRemoteService" class="com.dianping.dpsf.spring.ProxyBeanFactory" init-method="init">
        <property name="serviceName" value="http://service.dianping.com/tgcOpenService/shopActivityQueryRemoteService_1.0.0" />
        <property name="iface" value="com.dianping.tgc.open.ShopActivityQueryRemoteService" />
        <property name="serialize" value="hessian" />
        <property name="callMethod" value="sync" />
        <property name="timeout" value="2000" />
    </bean>
    <bean id="dealGroupActivityQueryRemoteService" class="com.dianping.dpsf.spring.ProxyBeanFactory" init-method="init">
        <property name="serviceName" value="http://service.dianping.com/tgcOpenService/dealGroupActivityQueryRemoteService_1.0.0" />
        <property name="iface" value="com.dianping.tgc.open.DealGroupActivityQueryRemoteService" />
        <property name="serialize" value="hessian" />
        <property name="callMethod" value="sync" />
        <property name="timeout" value="2000" />
    </bean>
    <bean id="merchantCouponIssueService" class="com.dianping.dpsf.spring.ProxyBeanFactory" init-method="init">
        <property name="serviceName" value="http://service.dianping.com/tgcProcessService/merchantCouponIssueService_1.0.0" />
        <property name="iface" value="com.dianping.tgc.process.MerchantCouponIssueService" />
        <property name="serialize" value="hessian" />
        <property name="callMethod" value="sync" />
        <property name="timeout" value="3000" />
    </bean>
    <bean id="dealIdMapperService" class="com.dianping.dpsf.spring.ProxyBeanFactory" init-method="init">
        <property name="serviceName" value="http://service.dianping.com/dealIdMapperService/dealIdMapperService_1.0.0"/>
        <property name="iface" value="com.dianping.deal.idmapper.api.DealIdMapperService"/>
        <property name="serialize" value="hessian"/>
        <property name="callMethod" value="sync"/>
        <property name="timeout" value="1000"/>
    </bean>
    <bean id="beautyCouponService" class="com.dianping.dpsf.spring.ProxyBeanFactory" init-method="init">
        <property name="serviceName" value="beauty.violet.service.beautyCouponService"/>
        <property name="iface" value="com.dianping.violet.service.BeautyCouponService"/>
        <property name="serialize" value="hessian"/>
        <property name="callMethod" value="sync"/>
        <property name="timeout" value="3000"/>
    </bean>

	<bean id="mtCampaignQueryService" class="com.dianping.dpsf.spring.ProxyBeanFactory" init-method="init">
		<property name="serviceName" value="http://service.dianping.com/payPromoExecuteRemoteService/MtCampaignQueryService_1.0.0"/>
		<property name="iface" value="com.dianping.pay.promo.execute.service.MtCampaignQueryService"/>
		<property name="serialize" value="hessian"/>
		<property name="callMethod" value="sync"/>
		<property name="timeout" value="1000"/>
	</bean>
    <bean id="spuRelationRemoteService" class="com.dianping.dpsf.spring.ProxyBeanFactory" init-method="init">
        <property name="serviceName" value="http://service.dianping.com/tgcOpenService/spuActivityQueryRemoteService_1.0.0" />
        <property name="iface" value="com.dianping.tgc.open.SpuActivityQueryRemoteService" />
        <property name="serialize" value="hessian" />
        <property name="callMethod" value="sync" />
        <property name="timeout" value="1000" />
    </bean>
    <bean id="poiRelationCacheService" class="com.dianping.pigeon.remoting.invoker.config.spring.ReferenceBean"
          init-method="init">
        <property name="url"
                  value="com.dianping.poi.relation.service.api.PoiRelationService"/>
        <property name="interfaceName" value="com.dianping.poi.relation.service.api.PoiRelationService"/>
        <property name="timeout" value="3000"/>
        <property name="timeoutRetry" value="true" />
    </bean>
    <bean id="skuActivityQueryRemoteService" class="com.dianping.dpsf.spring.ProxyBeanFactory" init-method="init">
        <property name="serviceName" value="http://service.dianping.com/tgcOpenService/skuActivityQueryRemoteService_1.0.0"/>
        <property name="iface" value="com.dianping.tgc.open.SkuActivityQueryRemoteService"/>
        <property name="serialize" value="hessian"/>
        <property name="callMethod" value="sync"/>
        <property name="timeout" value="2000"/>
    </bean>
    <bean id="accountValidationService" class="com.dianping.dpsf.spring.ProxyBeanFactory" init-method="init">
        <property name="serviceName" value="http://service.dianping.com/accountValidationService/accountValidationService_2.0.0"/>
        <property name="iface" value="com.dianping.account.validation.AccountValidationService"/>
        <property name="serialize" value="hessian"/>
        <property name="callMethod" value="sync"/>
        <property name="timeout" value="1000"/>
    </bean>
    <bean id="userAccountService" class="com.dianping.dpsf.spring.ProxyBeanFactory" init-method="init">
        <property name="serviceName" value="http://service.dianping.com/userAccountService/userAccountService_2.0.0"/>
        <property name="iface" value="com.dianping.account.UserAccountService"/>
        <property name="serialize" value="hessian"/>
        <property name="callMethod" value="sync"/>
        <property name="timeout" value="1000"/>
    </bean>

    <bean id="couponCenterService" class="com.dianping.pigeon.remoting.invoker.config.spring.ReferenceBean"
          init-method="init">
        <property name="url" value="http://service.dianping.com/gmkt_wave_service/couponCenterService_1.0.0"/>
        <property name="interfaceName" value="com.dianping.gmkt.wave.api.service.CouponCenterService"/>
        <property name="timeout" value="1000"/>
        <property name="serialize" value="hessian"/>
    </bean>

    <!--领券中心平台券查询-->
    <bean id="platformCouponService" class="com.dianping.pigeon.remoting.invoker.config.spring.ReferenceBean"
          init-method="init">
        <property name="url" value="com.dianping.gmkt.data.base.api.service.PlatformCouponService"/>
        <property name="interfaceName" value="com.dianping.gmkt.data.base.api.service.PlatformCouponService"/>
        <property name="timeout" value="1000"/>
        <property name="serialize" value="hessian"/>
    </bean>

    <bean id="shopUuidService" class="com.dianping.dpsf.spring.ProxyBeanFactory" init-method="init">
        <property name="serviceName" value="http://service.dianping.com/shopService/shopUuidService_1.0.0"/>
        <property name="iface" value="com.dianping.shopremote.remote.ShopUuidService"/>
        <property name="serialize" value="hessian"/>
        <property name="callMethod" value="sync"/>
        <property name="timeout" value="1000"/>
    </bean>

    <bean id="sinaiDpPoiService" class="com.meituan.service.mobile.mtthrift.proxy.ThriftClientProxy" destroy-method="destroy">
        <property name="serviceInterface" value="com.sankuai.sinai.data.api.service.DpPoiService"/> <!-- 接口名 -->
        <property name="remoteAppkey" value="com.sankuai.sinai.data.query"/>  <!-- 目标Server Appkey  -->
        <property name="nettyIO" value="true"/>
        <property name="filterByServiceName" value="true"/>  <!-- 通过服务名称进行服务发现 -->
    </bean>

    <bean id="mtPoiService" class="com.meituan.service.mobile.mtthrift.proxy.ThriftClientProxy" destroy-method="destroy">
        <property name="serviceInterface" value="com.sankuai.sinai.data.api.service.MtPoiService"/> <!-- 接口名 -->
        <property name="remoteAppkey" value="com.sankuai.sinai.data.query"/>  <!-- 目标Server Appkey  -->
        <property name="nettyIO" value="true"/>
        <property name="filterByServiceName" value="true"/>  <!-- 通过服务名称进行服务发现 -->
    </bean>


    <bean id="shopService" class="com.dianping.pigeon.remoting.invoker.config.spring.ReferenceBean" init-method="init">
        <property name="url" value="http://service.dianping.com/shopService/shopService_2.0.0"/>
        <property name="interfaceName" value="com.dianping.shopremote.remote.ShopService"/>
        <property name="serialize" value="hessian"/>
        <property name="callType" value="sync"/>
        <property name="timeout" value="1000"/>
    </bean>



    <bean id="tgcGetCouponComponentQueryService" class="com.dianping.dpsf.spring.ProxyBeanFactory" init-method="init">
        <property name="serviceName" value="http://service.dianping.com/tgcOpenService/v2/tgcGetCouponComponentQueryService_1.0.0" />
        <property name="iface" value="com.dianping.tgc.open.v2.TGCGetCouponComponentQueryService" />
        <property name="serialize" value="hessian" />
        <property name="callMethod" value="sync" />
        <property name="timeout" value="3000" />
    </bean>

    <bean id="bonusExposureQueryService" class="com.dianping.dpsf.spring.ProxyBeanFactory" init-method="init">
        <property name="serviceName" value="http://service.dianping.com/bonusExposureService/BonusExposureService_1.0.0" />
        <property name="iface" value=" com.dianping.gm.bonus.exposure.api.service.BonusExposureQueryService" />
        <property name="serialize" value="hessian" />
        <property name="callMethod" value="sync" />
        <property name="timeout" value="2000" />
    </bean>

    <bean id="unifiedCouponExecuteValidateService" class="com.dianping.pigeon.remoting.invoker.config.spring.ReferenceBean"
          init-method="init">
        <property name="interfaceName" value="com.sankuai.mpmkt.coupon.execute.api.UnifiedCouponExecuteValidateService" />
        <property name="url" value="http://service.sankuai.com/mpmkt/coupon/execute/UnifiedCouponExecuteValidateService_1.0.0_pigeontest" />
        <property name="serialize" value="hessian"/>
        <property name="callType" value="sync"/>
        <property name="timeout" value="1000"/>
    </bean>

    <bean id="merchantShareCouponActivityCService" class="com.dianping.pigeon.remoting.invoker.config.spring.ReferenceBean"
          init-method="init">
        <property name="interfaceName" value="com.sankuai.mkt.activity.api.merchantsharecoupon.service.MerchantShareCouponActivityCService" />
        <property name="url" value="com.sankuai.mkt.activity.api.merchantsharecoupon.service.MerchantShareCouponActivityCService" />
        <property name="serialize" value="hessian"/>
        <property name="callType" value="sync"/>
        <property name="timeout" value="2000"/>
    </bean>

    <bean id="mFlagService" class="com.dianping.pigeon.remoting.invoker.config.spring.ReferenceBean" init-method="init">
        <property name="url" value="http://service.dianping.com/gmm-marking-flag/MFlagService_1.0.0"/>
        <property name="interfaceName" value="com.dianping.gmm.markingflag.service.MFlagService"/>
        <property name="serialize" value="hessian"/>
        <property name="callType" value="sync"/>
        <property name="timeout" value="200"/>
    </bean>

    <!-- 结算价服务 -->
    <bean id="dealGroupVoucherQueryService" class="com.dianping.pigeon.remoting.invoker.config.spring.ReferenceBean" init-method="init">
        <property name="url" value="com.dianping.deal.voucher.query.api.DealGroupVoucherQueryService"/>
        <property name="interfaceName" value="com.dianping.deal.voucher.query.api.DealGroupVoucherQueryService"/>
        <property name="serialize" value="hessian"/>
        <property name="callType" value="sync"/>
        <property name="timeout" value="500"/>
    </bean>

    <bean id="dealGroupPublishCategoryQueryService"
          class="com.dianping.pigeon.remoting.invoker.config.spring.ReferenceBean" init-method="init">
        <property name="url"
                  value="http://service.dianping.com/deal-publish-category-service/dealGroupPublishCategoryQueryService_1.0.0"/>
        <property name="interfaceName" value="com.dianping.deal.publishcategory.DealGroupPublishCategoryQueryService"/>
        <property name="serialize" value="hessian"/>
        <property name="callType" value="sync"/>
        <property name="timeout" value="500"/>
    </bean>


    <bean id="promotionProxyService" class="com.meituan.service.mobile.mtthrift.proxy.ThriftClientProxy"
          destroy-method="destroy">
        <property name="serviceInterface" value="com.sankuai.nibmktproxy.queryclient.proxy.PromotionProxyService"/>
        <property name="appKey" value="mapi-pay-promo-web"/>
        <property name="remoteAppkey" value="com.sankuai.mpmktproxy.query"/>
        <property name="timeout" value="2000"/>
        <property name="nettyIO" value="true"/>
    </bean>

    <bean id="resourcesExposureService"
          class="com.dianping.pigeon.remoting.invoker.config.spring.ReferenceBean" init-method="init">
        <property name="url"
                  value="com.dianping.gmkt.scene.api.delivery.service.ResourcesExposureService"/>
        <property name="interfaceName" value="com.dianping.gmkt.scene.api.delivery.service.ResourcesExposureService"/>
        <property name="serialize" value="hessian"/>
        <property name="callType" value="sync"/>
        <property name="timeout" value="1000"/>
    </bean>

    <bean id="resourcesActionService"
          class="com.dianping.pigeon.remoting.invoker.config.spring.ReferenceBean" init-method="init">
        <property name="url"
                  value="com.dianping.gmkt.scene.api.delivery.service.ResourcesActionService"/>
        <property name="interfaceName" value="com.dianping.gmkt.scene.api.delivery.service.ResourcesActionService"/>
        <property name="serialize" value="hessian"/>
        <property name="callType" value="sync"/>
        <property name="timeout" value="1000"/>
    </bean>

    <!-- 点评类目接口 -->
    <bean id="poiShopCategoryQueryService" class="com.dianping.pigeon.remoting.invoker.config.spring.ReferenceBean" init-method="init">
        <property name="url" value="http://service.dianping.com/poi-shopcateprop-service/poiShopCategoryQueryService_1.0.0" />
        <property name="interfaceName" value="com.dianping.poi.shopcateprop.api.service.PoiShopCategoryQueryService" />
        <property name="serialize" value="hessian" />
        <property name="timeout" value="1000" />
    </bean>

    <bean id="productDetailService" class="com.dianping.dpsf.spring.ProxyBeanFactory" init-method="init">
        <property name="serviceName" value="http://service.dianping.com/tpfunService/productdetailservice_1.0.0"/>
        <property name="iface" value="com.dianping.tpfun.product.api.sku.aggregate.ProductDetailService"/>
        <property name="serialize" value="hessian"/>
        <property name="callMethod" value="sync"/>
        <property name="timeout" value="2000"/>
    </bean>


    <bean id="skuProductService" class="com.dianping.dpsf.spring.ProxyBeanFactory" init-method="init">
        <property name="serviceName" value="http://service.dianping.com/tpfunService/skuProductService_1.0.0"/>
        <property name="iface" value="com.dianping.tpfun.product.api.sku.ProductService"/>
        <property name="serialize" value="hessian"/>
        <property name="callMethod" value="sync"/>
        <property name="timeout" value="2000"/>
    </bean>

    <bean id="itemSettleService" class="com.dianping.dpsf.spring.ProxyBeanFactory" init-method="init">
        <property name="serviceName" value="http://service.dianping.com/tpfunService/itemsettleservice_1.0.0"/>
        <property name="iface" value="com.dianping.tpfun.product.api.sku.aggregate.ItemSettleService"/>
        <property name="serialize" value="hessian"/>
        <property name="callMethod" value="sync"/>
        <property name="timeout" value="3000"/>
    </bean>


    <bean id="priceDisplayService" class="com.dianping.dpsf.spring.ProxyBeanFactory" init-method="init">
        <property name="serviceName" value="com.sankuai.dealuser.price.display.api.PriceDisplayService"/>
        <property name="iface" value="com.sankuai.dealuser.price.display.api.PriceDisplayService"/>
        <property name="serialize" value="hessian"/>
        <property name="callMethod" value="sync"/>
        <property name="timeout" value="1000"/>
    </bean>


    <bean id="issueValidateService" class="com.dianping.pigeon.remoting.invoker.config.spring.ReferenceBean" init-method="init">
        <property name="url" value="http://service.dianping.com/CoreCouponIssueValidateService/coreCouponIssueValidateService_1.0.0_issuemerge"/>
        <property name="interfaceName" value="com.dianping.pay.unified.coupon.issue.core.api.CoreCouponIssueValidateService"/>
        <property name="serialize" value="hessian"/>
        <property name="callType" value="sync"/>
        <property name="timeout" value="1000"/>
    </bean>

    <bean id="issueCouponService" class="com.dianping.pigeon.remoting.invoker.config.spring.ReferenceBean" init-method="init">
        <property name="url" value="http://service.sankuai.com/mpmkt/coupon/issue/IssueCouponService_1.0.0"/>
        <property name="interfaceName" value="com.sankuai.mpmkt.coupon.issue.api.IssueCouponService"/>
        <property name="serialize" value="hessian"/>
        <property name="callType" value="sync"/>
        <property name="timeout" value="1000"/>
    </bean>


    <bean id="unifiedCouponInfoQueryService" class="com.dianping.pigeon.remoting.invoker.config.spring.ReferenceBean" init-method="init">
        <property name="url" value="http://service.sankuai.com/mpmkt/coupon/user-instance/UnifiedCouponInfoQueryService_1.0.0_pigeontest"/>
        <property name="interfaceName" value="com.sankuai.mpmkt.coupon.userinstance.api.UnifiedCouponInfoQueryService"/>
        <property name="serialize" value="hessian"/>
        <property name="callType" value="sync"/>
        <property name="timeout" value="1000"/>
    </bean>

    <bean id="brandThriftService" class="com.meituan.service.mobile.mtthrift.proxy.ThriftClientProxy"
          destroy-method="destroy">
        <property name="serviceInterface" value="com.sankuai.mpmctbrand.brandc.api.brand.BrandThriftService"/>
        <property name="appKey" value="mapi-pay-promo-web"/>
        <property name="remoteAppkey" value="com.sankuai.mpmctbrand.brandc"/>
        <property name="remoteServerPort" value="9001"/>
        <property name="timeout" value="2000"/>
        <property name="nettyIO" value="true"/>
    </bean>

    <bean id="couponActivityQueryService" class="com.dianping.pigeon.remoting.invoker.config.spring.ReferenceBean" init-method="init">
        <property name="url" value="http://service.dianping.com/tgcOpenService/couponActivityQueryService_1.0.0"/>
        <property name="interfaceName" value="com.dianping.tgc.open.CouponActivityQueryService"/>
        <property name="serialize" value="hessian"/>
        <property name="callType" value="sync"/>
        <property name="timeout" value="1000"/>
    </bean>

    <bean id="tgcActivityBizQueryService" class="com.dianping.dpsf.spring.ProxyBeanFactory" init-method="init">
        <property name="serviceName" value="http://service.dianping.com/tgcOpenService/v2/tgcActivityBizQueryService_1.0.0"/>
        <property name="iface" value="com.dianping.tgc.open.v2.TGCActivityBizQueryService"/>
        <property name="serialize" value="hessian"/>
        <property name="callMethod" value="sync"/>
        <property name="timeout" value="1000"/>
    </bean>

    <bean id="tgcActivityShopQueryService" class="com.dianping.pigeon.remoting.invoker.config.spring.ReferenceBean" init-method="init">
        <property name="url" value="http://service.dianping.com/tgcOpenService/v2/tgcActivityShopQueryService_1.0.0"/>
        <property name="interfaceName" value="com.dianping.tgc.open.v2.TGCActivityShopQueryService"/>
        <property name="serialize" value="hessian"/>
        <property name="callType" value="sync"/>
        <property name="timeout" value="1000"/>
    </bean>

    <bean id="unifiedCommissionQueryService" class="com.meituan.service.mobile.mtthrift.proxy.ThriftClientProxy"
          destroy-method="destroy">
        <property name="serviceInterface" value="com.dianping.zsts.commission.unify.api.UnifiedCommissionQueryService"/>
        <property name="timeout" value="2000"/>
        <property name="remoteAppkey" value="zsts-commission-service"/>
<!--  注释掉端口号
        <property name="remoteServerPort" value = "8090"/>
-->
        <property name="filterByServiceName" value="true"/> <!-- 必须加上！！ -->
    </bean>

    <bean id="userMergeQueryService" class="com.meituan.service.mobile.mtthrift.proxy.ThriftClientProxy"
          destroy-method="destroy" lazy-init="false">
        <property name="serviceInterface" value="com.sankuai.wpt.user.merge.query.thrift.message.UserMergeQueryService"/>
        <property name="remoteAppkey" value="com.sankuai.mtusercenter.merge.query"/>
        <property name="timeout" value="1000"/>
        <property name="nettyIO" value="true"/>
    </bean>

    <bean id="preDisplayClient" class="com.meituan.service.mobile.mtthrift.proxy.ThriftClientProxy" destroy-method="destroy">
        <property name="nettyIO" value="true"/>
        <property name="timeout" value="1000"/>
        <property name="retryRequest" value="false"/>
        <property name="appKey" value="mapi-pay-promo-web"/>
        <property name="remoteAppkey" value="com.sankuai.pay.access.easytrade"/>
        <property name="remoteServerPort" value="9010"/>
        <property name="serviceInterface" value="com.sankuai.pay.access.client.predisplay.IPreDisplayThrift"/>
    </bean>

    <!--TODO 待金融确认-->
    <bean id="payCouponExternalClient" class="com.meituan.service.mobile.mtthrift.proxy.ThriftClientProxy"
          destroy-method="destroy">
        <property name="nettyIO" value="true"/>
        <property name="timeout" value="1000"/>
        <property name="retryRequest" value="false"/>
        <property name="clusterManager" value="OCTO"/>
        <property name="appKey" value="mapi-pay-promo-web"/>
        <property name="remoteAppkey" value="com.sankuai.pay.paycoupon"/>
        <property name="remoteServerPort" value="3391"/>
        <property name="serviceInterface" value="com.meituan.pay.paycoupon.sdk.PayCouponExternalService"/>
    </bean>

    <bean id="dealGroupQueryService" class="com.meituan.service.mobile.mtthrift.proxy.ThriftClientProxy" destroy-method="destroy">
        <property name="serviceInterface" value="com.sankuai.general.product.query.center.client.service.DealGroupQueryService"/>
        <property name="appKey" value="com.sankuai.mpmkt.coupon.execute"/>
        <property name="remoteAppkey" value="com.sankuai.productuser.query.center"/>
        <property name="timeout" value="500"/>
        <property name="nettyIO" value="true"/>
        <property name="filterByServiceName" value="true"/>
        <property name="enableBackup" value="true"/>
        <property name="backupDelay" value="100"/>
    </bean>

    <bean id="idGen" class="com.meituan.service.mobile.mtthrift.proxy.ThriftClientProxy" destroy-method="destroy">
        <property name="serviceInterface" value="com.sankuai.inf.leaf.thrift.IDGen"/> <!--leaf的 接口名 -->
        <property name="appKey" value="mapi-pay-promo-web"/> <!-- 本地appkey，请自行配置 -->
        <property name="remoteAppkey" value="com.sankuai.leaf.service.common"/>  <!-- leaf Appkey  -->
        <property name="timeout" value="500"/> <!--超时时间为500ms-->
        <property name="nettyIO" value="true"/> <!--开启Netty IO-->
    </bean>

    <bean id="searchCouponService" class="com.dianping.pigeon.remoting.invoker.config.spring.ReferenceBean" init-method="init">
        <property name="url" value="http://service.sankuai.com/mpmkt/coupon/search/SearchCouponService_1.0.0"/>
        <property name="interfaceName" value="com.sankuai.mpmkt.coupon.search.api.SearchCouponService"/>
        <property name="serialize" value="hessian"/>
        <property name="callType" value="sync"/>
        <property name="timeout" value="${mapi-pay-promo-web.searchCoupon.timeout}"/>
    </bean>

    <bean id="topAdThriftService" class="com.meituan.service.mobile.mtthrift.proxy.ThriftClientProxy" destroy-method="destroy">
        <property name="serviceInterface" value="com.meituan.service.hotel.noah.thrift.ad.TopAdThriftService"/>
        <property name="timeout" value="400"/>
        <property name="remoteAppkey" value="com.sankuai.travel.osg.topad"/>
        <property name="remoteServerPort" value="6670"/>
        <property name="appKey" value="mapi-pay-promo-web"/>
        <property name="nettyIO" value="true"/>
    </bean>

    <bean id="areaCommonService" class="com.dianping.pigeon.remoting.invoker.config.spring.ReferenceBean"
          init-method="init">
        <property name="interfaceName" value="com.dianping.poi.areacommon.AreaCommonService"/>
        <property name="url" value="http://service.dianping.com/com.dianping.poi.areacommon.AreaCommonService_1.0.0"/>
        <property name="serialize" value="hessian"/>
        <property name="callType" value="sync"/>
        <property name="timeout" value="200"/>
    </bean>


    <bean id="campaignIssueCouponService" class="com.meituan.service.mobile.mtthrift.proxy.ThriftClientProxy"
          destroy-method="destroy">
        <property name="serviceInterface" value="com.sankuai.mpmkt.coupon.issue.octo.api.CampaignIssueCouponService"/>
        <property name="appKey" value="mapi-pay-promo-web"/>
        <property name="remoteAppkey" value="com.sankuai.mpmkt.coupon.issueocto"/>
        <property name="filterByServiceName" value="true"/>
        <property name="timeout" value="1000"/>
        <property name="nettyIO" value="true"/>
    </bean>

    <bean id="forceBeijingRegionPolicy" class="com.dianping.pigeon.remoting.invoker.route.region.ForceRegionPolicy">
        <property name="forceRegPrefer">
            <list>
                <value>Beijing</value>
                <value>Shanghai</value>
            </list>
        </property>
    </bean>

    <bean id="campaignQueryService" class="com.dianping.pigeon.remoting.invoker.config.spring.ReferenceBean" init-method="init">
        <property name="url" value="http://service.sankuai.com/mpmkt/coupon/group/campaignQueryService_1.0.0"/>
        <property name="interfaceName" value="com.sankuai.mpmkt.coupon.group.query.api.CampaignQueryService"/>
        <property name="serialize" value="hessian"/>
        <property name="callType" value="sync"/>
        <property name="timeout" value="500"/>
        <property name="routePolicyObj" ref="forceBeijingRegionPolicy"/>
    </bean>


    <bean id="couponGroupQueryService" class="com.dianping.pigeon.remoting.invoker.config.spring.ReferenceBean" init-method="init">
        <property name="url" value="http://service.sankuai.com/mpmkt/coupon/group/CouponGroupQueryService_1.0.0_pigeontest"/>
        <property name="interfaceName" value="com.sankuai.mpmkt.coupon.group.query.api.CouponGroupQueryService"/>
        <property name="serialize" value="hessian"/>
        <property name="callType" value="sync"/>
        <property name="timeout" value="500"/>
    </bean>

    <bean id="rpcTokenService" class="com.meituan.service.mobile.mtthrift.proxy.ThriftClientProxy"
          destroy-method="destroy">
        <property name="serviceInterface" value="com.sankuai.wpt.user.thrift.token.UserValidateTokenService"/>
        <property name="remoteAppkey" value="com.sankuai.wpt.user.token"/>
        <property name="filterByServiceName" value="true"/>
        <property name="timeout" value="300"/>
        <property name="nettyIO" value="true"/>
    </bean>

    <bean id="promotionQueryService" class="com.meituan.service.mobile.mtthrift.proxy.ThriftClientProxy" destroy-method="destroy">
        <property name="serviceInterface" value="com.sankuai.nibmkt.promotion.api.service.PromotionQueryService"/> <!-- 接口名 -->
        <property name="appKey" value="mapi-pay-promo-web"/>
        <property name="remoteAppkey" value="com.sankuai.mpmktproxy.query"/>  <!-- 目标Server Appkey  -->
        <property name="nettyIO" value="true"/>
        <property name="filterByServiceName" value="true"/>  <!-- 通过服务名称进行服务发现 -->
        <property name="timeout" value="${mapi-pay-promo-web.promotionQuery.timeout}"/>
    </bean>

    <bean id="rpcUserRetrieveService" class="com.meituan.service.mobile.mtthrift.proxy.ThriftClientProxy"
          destroy-method="destroy" lazy-init="false">
        <property name="serviceInterface" value="com.sankuai.wpt.user.retrieve.thrift.message.RpcUserRetrieveService"/>
        <property name="appKey" value="mapi-pay-promo-web"/>
        <property name="remoteAppkey" value="com.sankuai.wpt.user.retrieve"/>
        <property name="remoteServerPort" value="6489"/>
        <property name="timeout" value="200"/>
        <property name="nettyIO" value="true"/>
    </bean>
</beans>
