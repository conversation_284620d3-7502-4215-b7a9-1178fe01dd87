<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
	xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:context="http://www.springframework.org/schema/context"
	xsi:schemaLocation="
	http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans-2.5.xsd
	http://www.springframework.org/schema/context http://www.springframework.org/schema/context/spring-context-2.5.xsd">

	<context:component-scan base-package="com.dianping.api" />
    <context:component-scan base-package="com.dianping.pay"/>

	<context:annotation-config />

    <bean class="com.dianping.pay.framework.utils.SpringLocator"/>

	
	<!-- validation captcha -->
	<bean id="captchaUrl" class="java.lang.String">
		<constructor-arg value="${mobile-api-web.validation.captchaUrl}"/>
	</bean>
    <bean id="updateTimer" class="java.lang.String"/>
</beans>
