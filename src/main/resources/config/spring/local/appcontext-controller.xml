<beans xmlns="http://www.springframework.org/schema/beans"
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
    xmlns:context="http://www.springframework.org/schema/context"
    xsi:schemaLocation="http://www.springframework.org/schema/beans
        http://www.springframework.org/schema/beans/spring-beans-2.5.xsd
        http://www.springframework.org/schema/context
        http://www.springframework.org/schema/context/spring-context-2.5.xsd">

    <context:annotation-config />

    <!-- 获取优惠列表begin -->
    <bean id = "discountListMethod" class="org.springframework.util.MethodInvoker" init-method="prepare">
        <property name="targetClass" value="com.dianping.pay.api.controller.CouponController" />
        <property name="targetMethod" value="getCouponList" />
    </bean>
	<bean id="discountlist.pay" class="com.dianping.pay.api.controller.CouponController" scope="prototype">
        <property name="methodInvoker" ref="discountListMethod"/>
	</bean>
    <!-- 获取优惠列表end -->

    <!-- 验证优惠代码begin -->
    <bean id = "verifyDiscountCodeMethod" class="org.springframework.util.MethodInvoker" init-method="prepare">
        <property name="targetClass" value="com.dianping.pay.api.controller.CouponController" />
        <property name="targetMethod" value="verifyCouponCode" />
    </bean>
	<bean id="verifydiscountcode.pay" class="com.dianping.pay.api.controller.CouponController" scope="prototype">
        <property name="methodInvoker" ref="verifyDiscountCodeMethod"/>
        <property name="printResponse" value="true"/>
	</bean>
    <!-- 验证优惠代码end -->

    <!-- 获取用户抵用券begin -->
    <bean id = "getUserDiscountMethod" class="org.springframework.util.MethodInvoker" init-method="prepare">
        <property name="targetClass" value="com.dianping.pay.api.controller.CouponController" />
        <property name="targetMethod" value="getUserDiscount" />
    </bean>
    <bean id="getuserdiscount.pay" class="com.dianping.pay.api.controller.CouponController" scope="prototype">
        <property name="methodInvoker" ref="getUserDiscountMethod"/>
    </bean>
    <!-- 获取用户抵用券end -->

    <!-- 获取用户钱包begin -->
    <bean id = "getUserWallectMethod" class="org.springframework.util.MethodInvoker" init-method="prepare">
        <property name="targetClass" value="com.dianping.pay.api.controller.OrderController" />
        <property name="targetMethod" value="getUserWallet" />
    </bean>
    <bean id="getuserwallet.pay" class="com.dianping.pay.api.controller.OrderController" scope="prototype">
        <property name="methodInvoker" ref="getUserWallectMethod"/>
    </bean>
    <!-- 获取用户钱包end -->

    <!-- 获取红包信息begin -->
    <bean id = "getUserRedEnvelopeMethod" class="org.springframework.util.MethodInvoker" init-method="prepare">
        <property name="targetClass" value="com.dianping.pay.api.controller.CouponController" />
        <property name="targetMethod" value="getUserRedenvelope" />
    </bean>
    <bean id="getuserredenvelope.pay" class="com.dianping.pay.api.controller.CouponController" scope="prototype">
        <property name="methodInvoker" ref="getUserRedEnvelopeMethod"/>
    </bean>
    <!-- 获取红包信息end -->

    <!-- 获取优惠信息begin -->
    <bean id = "getDiscountMethod" class="org.springframework.util.MethodInvoker" init-method="prepare">
        <property name="targetClass" value="com.dianping.pay.api.controller.OrderController" />
        <property name="targetMethod" value="getDiscount" />
    </bean>
    <bean id="getdiscount.pay" class="com.dianping.pay.api.controller.OrderController" scope="prototype">
        <property name="methodInvoker" ref="getDiscountMethod" />
    </bean>
    <!-- 获取优惠信息end -->

    <!-- 获取优惠信息begin -->
    <bean id = "checkoutDiscountMethod" class="org.springframework.util.MethodInvoker" init-method="prepare">
        <property name="targetClass" value="com.dianping.pay.api.controller.CheckoutOrderController" />
        <property name="targetMethod" value="checkoutDiscount" />
    </bean>
    <bean id="checkoutdiscount.pay" class="com.dianping.pay.api.controller.CheckoutOrderController" scope="prototype">
        <property name="methodInvoker" ref="checkoutDiscountMethod"/>
        <property name="printResponse" value="true"/>
    </bean>
    <!-- 获取优惠信息end -->

    <!-- 获取用户抵用券begin -->
    <bean id = "getUserCouponMethod" class="org.springframework.util.MethodInvoker" init-method="prepare">
        <property name="targetClass" value="com.dianping.pay.api.controller.CouponController" />
        <property name="targetMethod" value="getUserCouponList" />
    </bean>
    <bean id="getusercouponlist.pay" class="com.dianping.pay.api.controller.CouponController" scope="prototype">
        <property name="methodInvoker" ref="getUserCouponMethod"/>
    </bean>
    <!-- 获取用户抵用券end -->

    <!-- 验证优惠代码begin -->
    <bean id = "verifyCouponCodeMethod" class="org.springframework.util.MethodInvoker" init-method="prepare">
        <property name="targetClass" value="com.dianping.pay.api.controller.CouponController" />
        <property name="targetMethod" value="verifyUserCouponCode" />
    </bean>
    <bean id="verifyusercouponcode.pay" class="com.dianping.pay.api.controller.CouponController" scope="prototype">
        <property name="methodInvoker" ref="verifyCouponCodeMethod"/>
        <property name="printResponse" value="true"/>
    </bean>
    <!-- 验证优惠代码end -->

    <!-- 获取优惠台begin -->
    <bean id = "getPromoDeskMethod" class="org.springframework.util.MethodInvoker" init-method="prepare">
        <property name="targetClass" value="com.dianping.pay.api.controller.PromoDeskController" />
        <property name="targetMethod" value="getPromoDesk" />
    </bean>
    <bean id="getpromodesk.pay" class="com.dianping.pay.api.controller.PromoDeskController" scope="prototype">
        <property name="methodInvoker" ref="getPromoDeskMethod"/>
        <property name="printResponse" value="true"/>
    </bean>
    <!-- 获取优惠台end -->

    <!-- 获取优惠台抵用券列表begin -->
    <bean id = "getPromoDeskCouponMethod" class="org.springframework.util.MethodInvoker" init-method="prepare">
        <property name="targetClass" value="com.dianping.pay.api.controller.PromoDeskCouponController" />
        <property name="targetMethod" value="getCouponList" />
    </bean>
    <bean id="getpromodeskcoupon.pay" class="com.dianping.pay.api.controller.PromoDeskCouponController" scope="prototype">
        <property name="methodInvoker" ref="getPromoDeskCouponMethod"/>
        <property name="printResponse" value="true"/>
    </bean>
    <!-- 获取优惠台抵用券列表end -->

    <!-- 优惠台校验优惠代码begin -->
    <bean id = "verifyPromoDeskCodeMethod" class="org.springframework.util.MethodInvoker" init-method="prepare">
        <property name="targetClass" value="com.dianping.pay.api.controller.PromoDeskCouponController" />
        <property name="targetMethod" value="verifyCouponCode" />
    </bean>
    <bean id="verifypromodeskcode.pay" class="com.dianping.pay.api.controller.PromoDeskCouponController" scope="prototype">
        <property name="methodInvoker" ref="verifyPromoDeskCodeMethod"/>
        <property name="printResponse" value="true"/>
    </bean>
    <!-- 优惠台校验优惠代码end -->

    <!-- 优惠台获取最优解begin -->
    <bean id = "getPromoDeskOptimalChoiceMethod" class="org.springframework.util.MethodInvoker" init-method="prepare">
        <property name="targetClass" value="com.dianping.pay.api.controller.PromoDeskOptimalController" />
        <property name="targetMethod" value="getOptimalChoice" />
    </bean>
    <bean id="getpromodeskoptimalchoice.pay" class="com.dianping.pay.api.controller.PromoDeskOptimalController" scope="prototype">
        <property name="methodInvoker" ref="getPromoDeskOptimalChoiceMethod"/>
        <property name="printResponse" value="true"/>
    </bean>
    <!-- 优惠台获取最优解end -->

    <!-- 加载领券组件begin -->
    <bean id = "issueCouponComponentMethod" class="org.springframework.util.MethodInvoker" init-method="prepare">
        <property name="targetClass" value="com.dianping.pay.api.controller.IssueCouponController" />
        <property name="targetMethod" value="issueCouponComponent" />
    </bean>
    <bean id="issuecouponcomponent.pay" class="com.dianping.pay.api.controller.IssueCouponController" scope="prototype">
        <property name="methodInvoker" ref="issueCouponComponentMethod"/>
        <property name="printResponse" value="true"/>
    </bean>
    <!-- 加载领券组件end -->

    <!-- 加载丽人领券组件begin -->
    <bean id = "beautyIssueCouponComponentMethod" class="org.springframework.util.MethodInvoker" init-method="prepare">
        <property name="targetClass" value="com.dianping.pay.api.controller.IssueCouponController" />
        <property name="targetMethod" value="beautyIssueCouponComponent" />
    </bean>
    <bean id="beautyissuecouponcomponent.pay" class="com.dianping.pay.api.controller.IssueCouponController" scope="prototype">
        <property name="methodInvoker" ref="beautyIssueCouponComponentMethod"/>
        <property name="printResponse" value="true"/>
    </bean>
    <!-- 加载领券组件end -->

    <!-- 领券组件触发领券begin -->
    <bean id = "issueCouponMethod" class="org.springframework.util.MethodInvoker" init-method="prepare">
        <property name="targetClass" value="com.dianping.pay.api.controller.IssueCouponController" />
        <property name="targetMethod" value="issueCoupon" />
    </bean>
    <bean id="issuecoupon.pay" class="com.dianping.pay.api.controller.IssueCouponController" scope="prototype">
        <property name="methodInvoker" ref="issueCouponMethod"/>
        <property name="printResponse" value="true"/>
    </bean>
    <!-- 领券组件触发领券end -->
</beans>
