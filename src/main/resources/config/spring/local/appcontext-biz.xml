<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
	xmlns:context="http://www.springframework.org/schema/context"
	xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:aop="http://www.springframework.org/schema/aop"
	xmlns:tx="http://www.springframework.org/schema/tx"
	xsi:schemaLocation="
    http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans-2.5.xsd
    http://www.springframework.org/schema/context http://www.springframework.org/schema/context/spring-context-2.5.xsd
	http://www.springframework.org/schema/aop http://www.springframework.org/schema/aop/spring-aop-2.5.xsd
	http://www.springframework.org/schema/tx http://www.springframework.org/schema/tx/spring-tx-2.5.xsd">

	<bean id="couponBiz" class="com.dianping.pay.api.biz.discount.CouponBiz"/>
	<bean id="reductionBiz" class="com.dianping.pay.api.biz.discount.ReductionBiz"/>
	<bean id="productBiz" class="com.dianping.pay.api.biz.product.ProductBiz"/>
	<bean id="productJudgeBiz" class="com.dianping.pay.api.biz.product.ProductJudgeBiz"/>
	<bean id="userAccountBiz" class="com.dianping.pay.api.biz.user.UserAccountBiz"/>
	<bean id="discountPromoQuery" class="com.dianping.pay.api.biz.discount.DiscountPromoQuery"/>
	<bean id="mtDiscountPromoQuery" class="com.dianping.pay.api.biz.discount.MTDiscountPromoQuery"/>

    <bean id="promoDeskStrategyFactory" class="com.dianping.pay.api.biz.promodesk.PromoDeskStrategyFactory" />
    <bean id="commonPromoDeskStrategy" class="com.dianping.pay.api.biz.promodesk.CommonPromoDeskStrategy" />
    <bean id="tuangouPromoDeskStrategy" class="com.dianping.pay.api.biz.promodesk.TuangouPromoDeskStrategy" />
    <bean id="tuangouPcPromoDeskStrategy" class="com.dianping.pay.api.biz.promodesk.TuangouPcPromoDeskStrategy" />
    <bean id="shanhuiPromoDeskStrategy" class="com.dianping.pay.api.biz.promodesk.ShanhuiPromoDeskStrategy" />
    <bean id="ktvPromoDeskStrategy" class="com.dianping.pay.api.biz.promodesk.KtvPromoDeskStrategy" />
    <bean id="mtKtvPromoDeskStrategy" class="com.dianping.pay.api.biz.promodesk.MtKtvPromoDeskStrategy" />
    <bean id="dishPromoDeskStrategy" class="com.dianping.pay.api.biz.promodesk.DishPromoDeskStrategy" />

    <bean id="promoDeskCouponStrategyFactory" class="com.dianping.pay.api.biz.promodeskcoupon.PromoDeskCouponStrategyFactory" />
    <bean id="commonPromoDeskCouponStrategy" class="com.dianping.pay.api.biz.promodeskcoupon.CommonPromoDeskCouponStrategy" />
    <bean id="ktvPromoDeskCouponStrategy" class="com.dianping.pay.api.biz.promodeskcoupon.KtvPromoDeskCouponStrategy" />
    <bean id="shanhuiPromoDeskCouponStrategy" class="com.dianping.pay.api.biz.promodeskcoupon.ShanhuiPromoDeskCouponStrategy" />
    <bean id="tuangouPromoDeskCouponStrategy" class="com.dianping.pay.api.biz.promodeskcoupon.TuangouPromoDeskCouponStrategy" />

    <bean id="couponIssueActivityQueryService" class="com.dianping.pay.api.biz.activity.newloader.CouponIssueActivityQueryService">
        <constructor-arg value="${pay-api-mobile.issuecoupon.processThread.coreSize}" />
        <constructor-arg value="${pay-api-mobile.issuecoupon.processThread.maxSize}" />
        <constructor-arg value="${pay-api-mobile.issuecoupon.processThread.idleTime}" />
        <constructor-arg value="${pay-api-mobile.issuecoupon.processThread.queueSize}" />
    </bean>
    <bean id="couponIssueActivityQueryExecutorFactory" class="com.dianping.pay.api.biz.activity.newloader.CouponIssueActivityQueryExecutorFactory" />


    <bean id="issueCouponBiz" class="com.dianping.pay.api.biz.activity.IssueCouponBiz" />

    <!-- 激活注解处理 -->
    <context:annotation-config/>

    <!-- 激活 union logger -->
    <bean class="com.sankuai.nibpt.unionlogger.EnableUnionLoggerConfiguration"/>

    <!-- 激活 SDK -->
    <bean class="com.sankuai.nibpt.transparentvalidator.EnableTransparentValidatorConfiguration">
        <property name="needWhitelistConfig" value="true"/>
    </bean>

    <bean class="com.sankuai.nib.mkt.magicMccConfig.MagicControlCenterMccConfig"/>

    <!--斗斛客户端初始化 start -->
    <bean id="douHuConfig" class="com.sankuai.douhu.absdk.bean.DouHuConfig">
        <constructor-arg value="${mapi-pay-promo-web.douhu.config.expIds}"/>
    </bean>

    <bean id="douHuClient" class="com.sankuai.douhu.absdk.client.DouHuClient">
        <constructor-arg ref="douHuConfig"/>
    </bean>
    <!--斗斛客户端初始化 end -->

</beans>
