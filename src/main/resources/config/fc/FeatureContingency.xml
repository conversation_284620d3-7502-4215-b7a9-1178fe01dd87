<?xml version="1.0" encoding="UTF-8"?>
<FeatureContingencySet>
	<!--
		Feature Contingency contains following attributes: 
		1. id 				this is the unique identifier of the fc 
		2. name 			this is the display name make fc more readable 
		3. status 	this is the status. 
							Currently we have 3 status:
								0 :ON
								1 :OFF
								2 :DELETED 
		4. description		this attribute is optional
	-->
	<FeatureContingency id="0" name="SampleFC" status="1" description="This is a sample configuration" />
	<FeatureContingency id="1" name="Hawk" status="0" description="For hawk" />
	<FeatureContingency id="3" name="Cat" status="0" description="For Cat" />
	<FeatureContingency id="4" name="ReviewMobileService" status="0" description="For ReviewMobileService" />
	<FeatureContingency id="5" name="GrouponService" status="0" description="For GrouponService" />
	<FeatureContingency id="6" name="GrouponThrottling" status="0" description="" />
    <FeatureContingency id="7" name="IphoneScore" status="0" description="pop up a dialogue screen ask user to give comment on appstore" />
    <FeatureContingency id="8" name="Login" status="0" description="For LoginService, judge is call remoteService" />
	<FeatureContingency id="10" name="RuleEngine" status="0" description="For Rule Engine" />
	<FeatureContingency id="11" name="ConfigPush" status="0" description="For config.bin push " />
	<FeatureContingency id="12" name="AddShop" status="0" description="For AddShop RemoteService" />
	<FeatureContingency id="13" name="UserBlocklist" status="0" description="For accquire user block list" />
	<FeatureContingency id="14" name="PhoneBindDisabled" status="0" description="disable mobilephone rebind on tuangou app" />
	<FeatureContingency id="15" name="BoundPhoneMasked" status="0" description="mask bound phone number as 139****0000" />
	<FeatureContingency id="16" name="DealSuggestEnabled" status="0" description="for deal search suggest" />
	<FeatureContingency id="17" name="ForceLogout" status="0" description="enable force logout" />
	<FeatureContingency id="18" name="MainAppNative" status="0" description="enable main app native UI" />
	<FeatureContingency id="19" name="ThirdFeedLog" status="0" description="For third feed log" />
</FeatureContingencySet>
