package com.dianping.unified.coupon.biz;

import com.alibaba.fastjson.JSONObject;
import com.dianping.api.picasso.controller.ShopCartPromoDisplayMapiAction;
import com.dianping.mobile.base.datatypes.enums.ClientType;
import com.dianping.mobile.base.datatypes.enums.DeviceType;
import com.dianping.mobile.framework.cache.CacheContext;
import com.dianping.mobile.framework.datatypes.AppType;
import com.dianping.mobile.framework.datatypes.IMobileContext;
import com.dianping.mobile.framework.datatypes.IMobileResponse;
import com.dianping.mobile.framework.datatypes.MobileHeader;
import com.dianping.mobile.framework.datatypes.client.ClientConfig;
import com.dianping.mobile.framework.datatypes.crawl.CrawlMark;
import com.dianping.mobile.framework.datatypes.token.UserStatusResult;
import com.dianping.pay.api.entity.issuecoupon.ShopCartPromoDisplayActionRequest;
import org.apache.commons.fileupload.FileItem;
import org.junit.Assert;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import javax.servlet.*;
import javax.servlet.http.*;
import java.io.BufferedReader;
import java.io.IOException;
import java.io.PrintWriter;
import java.io.UnsupportedEncodingException;
import java.lang.reflect.Method;
import java.security.Principal;
import java.util.*;

/**
 * <AUTHOR>
 * @date 2023/5/30
 */
public class ShopCartPromoDisplayMapiActionTest extends TestBase {

    @Autowired
    private ShopCartPromoDisplayMapiAction shopCartPromoDisplayMapiAction;

    @Test
    public void testNewProductPromoActionExecute() {
        ShopCartPromoDisplayActionRequest request = buildShopCartPromoDisplayActionRequest();
        IMobileContext context = buildMTIMobileContext();
        boolean result = true;
        try {
            Method validateMethod = shopCartPromoDisplayMapiAction.getClass().getDeclaredMethod("validate", ShopCartPromoDisplayActionRequest.class, IMobileContext.class);
            validateMethod.setAccessible(true);
            IMobileResponse response = (IMobileResponse)validateMethod.invoke(shopCartPromoDisplayMapiAction, request, context);
            Method method = shopCartPromoDisplayMapiAction.getClass().getDeclaredMethod("execute", ShopCartPromoDisplayActionRequest.class, IMobileContext.class);
            method.setAccessible(true);
            response = (IMobileResponse)method.invoke(shopCartPromoDisplayMapiAction, request, context);
            System.out.println("response:" + JSONObject.toJSONString(response));
        } catch (Exception ex){
            result = false;
        }
        Assert.assertTrue(result);
    }

    private ShopCartPromoDisplayActionRequest buildShopCartPromoDisplayActionRequest() {
        ShopCartPromoDisplayActionRequest shopCartPromoDisplayActionRequest = new ShopCartPromoDisplayActionRequest();
        shopCartPromoDisplayActionRequest.setShopcartbrandproducts("[{\"brandId\":\"123\",\"shopcartproducts\":[{\"producttype\":0,\"productid\": 419690854}]}]");
        return shopCartPromoDisplayActionRequest;
    }

    private IMobileContext buildIMobileContext() {
        IMobileContext context = new IMobileContext() {
            @Override
            public Set<String> getParameterKeys() {
                return null;
            }

            @Override
            public String getParameter(String s) {
                return null;
            }

            @Override
            public MobileHeader getHeader() {
                MobileHeader header = new MobileHeader();
                header.setDpid("112233");
                return header;
            }

            @Override
            public String getUserIp() {
                return null;
            }

            @Override
            public ClientType getClient() {
                return ClientType.MAINAPP_IPHONE;
            }

            @Override
            public String getVersion() {
                return null;
            }

            @Override
            public String getSource() {
                return null;
            }

            @Override
            public String getCellPhoneType() {
                return null;
            }

            @Override
            public String getUserAgent() {
                return "mapi-Test-natice";
            }

            @Override
            public long getUserId() {
                return 0;
            }

            @Override
            public Map<String, FileItem> getMultiParamMap() {
                return null;
            }

            @Override
            public String getOs() {
                return null;
            }

            @Override
            public String getProtocolVersion() {
                return null;
            }

            @Override
            public boolean isPost() {
                return false;
            }

            @Override
            public ClientConfig getConfig() {
                return null;
            }

            @Override
            public String getRequestId() {
                return null;
            }

            @Override
            public String getRequestIdReffer() {
                return null;
            }

            @Override
            public String getActionKey() {
                return null;
            }

            @Override
            public HttpServletRequest getRequest() {
                HttpServletRequest httpServletRequest = new HttpServletRequest() {
                    @Override
                    public String getAuthType() {
                        return null;
                    }

                    @Override
                    public Cookie[] getCookies() {
                        return new Cookie[0];
                    }

                    @Override
                    public long getDateHeader(String s) {
                        return 0;
                    }

                    @Override
                    public String getHeader(String s) {
                        return null;
                    }

                    @Override
                    public Enumeration getHeaders(String s) {
                        return null;
                    }

                    @Override
                    public Enumeration getHeaderNames() {
                        return null;
                    }

                    @Override
                    public int getIntHeader(String s) {
                        return 0;
                    }

                    @Override
                    public String getMethod() {
                        return null;
                    }

                    @Override
                    public String getPathInfo() {
                        return null;
                    }

                    @Override
                    public String getPathTranslated() {
                        return null;
                    }

                    @Override
                    public String getContextPath() {
                        return null;
                    }

                    @Override
                    public String getQueryString() {
                        return null;
                    }

                    @Override
                    public String getRemoteUser() {
                        return null;
                    }

                    @Override
                    public boolean isUserInRole(String s) {
                        return false;
                    }

                    @Override
                    public Principal getUserPrincipal() {
                        return null;
                    }

                    @Override
                    public String getRequestedSessionId() {
                        return null;
                    }

                    @Override
                    public String getRequestURI() {
                        return null;
                    }

                    @Override
                    public StringBuffer getRequestURL() {
                        return null;
                    }

                    @Override
                    public String getServletPath() {
                        return null;
                    }

                    @Override
                    public HttpSession getSession(boolean b) {
                        return null;
                    }

                    @Override
                    public HttpSession getSession() {
                        return null;
                    }

                    @Override
                    public String changeSessionId() {
                        return null;
                    }

                    @Override
                    public boolean isRequestedSessionIdValid() {
                        return false;
                    }

                    @Override
                    public boolean isRequestedSessionIdFromCookie() {
                        return false;
                    }

                    @Override
                    public boolean isRequestedSessionIdFromURL() {
                        return false;
                    }

                    @Override
                    public boolean isRequestedSessionIdFromUrl() {
                        return false;
                    }

                    @Override
                    public boolean authenticate(HttpServletResponse httpServletResponse) throws IOException, ServletException {
                        return false;
                    }

                    @Override
                    public void login(String s, String s1) throws ServletException {

                    }

                    @Override
                    public void logout() throws ServletException {

                    }

                    @Override
                    public Collection<Part> getParts() throws IOException, ServletException {
                        return null;
                    }

                    @Override
                    public Part getPart(String s) throws IOException, ServletException {
                        return null;
                    }

                    @Override
                    public <T extends HttpUpgradeHandler> T upgrade(Class<T> aClass) throws IOException, ServletException {
                        return null;
                    }

                    @Override
                    public Object getAttribute(String s) {
                        return null;
                    }

                    @Override
                    public Enumeration getAttributeNames() {
                        return null;
                    }

                    @Override
                    public String getCharacterEncoding() {
                        return null;
                    }

                    @Override
                    public void setCharacterEncoding(String s) throws UnsupportedEncodingException {

                    }

                    @Override
                    public int getContentLength() {
                        return 0;
                    }

                    @Override
                    public long getContentLengthLong() {
                        return 0;
                    }

                    @Override
                    public String getContentType() {
                        return null;
                    }

                    @Override
                    public ServletInputStream getInputStream() throws IOException {
                        return null;
                    }

                    @Override
                    public String getParameter(String s) {
                        return null;
                    }

                    @Override
                    public Enumeration getParameterNames() {
                        return null;
                    }

                    @Override
                    public String[] getParameterValues(String s) {
                        return new String[0];
                    }

                    @Override
                    public Map getParameterMap() {
                        return null;
                    }

                    @Override
                    public String getProtocol() {
                        return null;
                    }

                    @Override
                    public String getScheme() {
                        return null;
                    }

                    @Override
                    public String getServerName() {
                        return null;
                    }

                    @Override
                    public int getServerPort() {
                        return 0;
                    }

                    @Override
                    public BufferedReader getReader() throws IOException {
                        return null;
                    }

                    @Override
                    public String getRemoteAddr() {
                        return null;
                    }

                    @Override
                    public String getRemoteHost() {
                        return null;
                    }

                    @Override
                    public void setAttribute(String s, Object o) {

                    }

                    @Override
                    public void removeAttribute(String s) {

                    }

                    @Override
                    public Locale getLocale() {
                        return null;
                    }

                    @Override
                    public Enumeration getLocales() {
                        return null;
                    }

                    @Override
                    public boolean isSecure() {
                        return false;
                    }

                    @Override
                    public RequestDispatcher getRequestDispatcher(String s) {
                        return null;
                    }

                    @Override
                    public String getRealPath(String s) {
                        return null;
                    }

                    @Override
                    public int getRemotePort() {
                        return 0;
                    }

                    @Override
                    public String getLocalName() {
                        return null;
                    }

                    @Override
                    public String getLocalAddr() {
                        return null;
                    }

                    @Override
                    public int getLocalPort() {
                        return 0;
                    }

                    @Override
                    public ServletContext getServletContext() {
                        return null;
                    }

                    @Override
                    public AsyncContext startAsync() throws IllegalStateException {
                        return null;
                    }

                    @Override
                    public AsyncContext startAsync(ServletRequest servletRequest, ServletResponse servletResponse) throws IllegalStateException {
                        return null;
                    }

                    @Override
                    public boolean isAsyncStarted() {
                        return false;
                    }

                    @Override
                    public boolean isAsyncSupported() {
                        return false;
                    }

                    @Override
                    public AsyncContext getAsyncContext() {
                        return null;
                    }

                    @Override
                    public DispatcherType getDispatcherType() {
                        return null;
                    }
                };
                return httpServletRequest;
            }

            @Override
            public HttpServletResponse getResponse() {
                HttpServletResponse httpServletResponse = new HttpServletResponse() {
                    @Override
                    public void addCookie(Cookie cookie) {

                    }

                    @Override
                    public boolean containsHeader(String s) {
                        return false;
                    }

                    @Override
                    public String encodeURL(String s) {
                        return null;
                    }

                    @Override
                    public String encodeRedirectURL(String s) {
                        return null;
                    }

                    @Override
                    public String encodeUrl(String s) {
                        return null;
                    }

                    @Override
                    public String encodeRedirectUrl(String s) {
                        return null;
                    }

                    @Override
                    public void sendError(int i, String s) throws IOException {

                    }

                    @Override
                    public void sendError(int i) throws IOException {

                    }

                    @Override
                    public void sendRedirect(String s) throws IOException {

                    }

                    @Override
                    public void setDateHeader(String s, long l) {

                    }

                    @Override
                    public void addDateHeader(String s, long l) {

                    }

                    @Override
                    public void setHeader(String s, String s1) {

                    }

                    @Override
                    public void addHeader(String s, String s1) {

                    }

                    @Override
                    public void setIntHeader(String s, int i) {

                    }

                    @Override
                    public void addIntHeader(String s, int i) {

                    }

                    @Override
                    public void setStatus(int i) {

                    }

                    @Override
                    public void setStatus(int i, String s) {

                    }

                    @Override
                    public int getStatus() {
                        return 0;
                    }

                    @Override
                    public String getHeader(String s) {
                        return null;
                    }

                    @Override
                    public Collection<String> getHeaders(String s) {
                        return null;
                    }

                    @Override
                    public Collection<String> getHeaderNames() {
                        return null;
                    }

                    @Override
                    public String getCharacterEncoding() {
                        return null;
                    }

                    @Override
                    public String getContentType() {
                        return null;
                    }

                    @Override
                    public ServletOutputStream getOutputStream() throws IOException {
                        return null;
                    }

                    @Override
                    public PrintWriter getWriter() throws IOException {
                        return null;
                    }

                    @Override
                    public void setCharacterEncoding(String s) {

                    }

                    @Override
                    public void setContentLength(int i) {

                    }

                    @Override
                    public void setContentLengthLong(long l) {

                    }

                    @Override
                    public void setContentType(String s) {

                    }

                    @Override
                    public void setBufferSize(int i) {

                    }

                    @Override
                    public int getBufferSize() {
                        return 0;
                    }

                    @Override
                    public void flushBuffer() throws IOException {

                    }

                    @Override
                    public void resetBuffer() {

                    }

                    @Override
                    public boolean isCommitted() {
                        return false;
                    }

                    @Override
                    public void reset() {

                    }

                    @Override
                    public void setLocale(Locale locale) {

                    }

                    @Override
                    public Locale getLocale() {
                        return null;
                    }
                };
                return httpServletResponse;
            }

            @Override
            public byte[] getPicFile() {
                return new byte[0];
            }

            @Override
            public CacheContext getCacheContext() {
                return null;
            }

            @Override
            public AppType getAppType() {
                return null;
            }

            @Override
            public boolean isMtClient() {
                return false;
            }

            @Override
            public DeviceType getDeviceType() {
                return null;
            }

            @Override
            public UserStatusResult getUserStatus() {
                UserStatusResult userStatusResult = new UserStatusResult();
                userStatusResult.setUserId(859798856L);
                return userStatusResult;
            }

            @Override
            public void setUserStatus(UserStatusResult result) {

            }

            @Override
            public CrawlMark getCrawlMark() {
                return null;
            }

            @Override
            public int getAppId() {
                return 0;
            }
        };
        return context;
    }

    private IMobileContext buildMTIMobileContext() {
        IMobileContext context = new IMobileContext() {
            @Override
            public Set<String> getParameterKeys() {
                return null;
            }

            @Override
            public String getParameter(String s) {
                return null;
            }

            @Override
            public MobileHeader getHeader() {
                MobileHeader header = new MobileHeader();
                header.setDpid("112233");
                return header;
            }

            @Override
            public String getUserIp() {
                return null;
            }

            @Override
            public ClientType getClient() {
                return ClientType.MTMAIN_APP_IOS;
            }

            @Override
            public String getVersion() {
                return null;
            }

            @Override
            public String getSource() {
                return null;
            }

            @Override
            public String getCellPhoneType() {
                return null;
            }

            @Override
            public String getUserAgent() {
                return "mapi-Test-natice";
            }

            @Override
            public long getUserId() {
                return 0;
            }

            @Override
            public Map<String, FileItem> getMultiParamMap() {
                return null;
            }

            @Override
            public String getOs() {
                return null;
            }

            @Override
            public String getProtocolVersion() {
                return null;
            }

            @Override
            public boolean isPost() {
                return false;
            }

            @Override
            public ClientConfig getConfig() {
                return null;
            }

            @Override
            public String getRequestId() {
                return null;
            }

            @Override
            public String getRequestIdReffer() {
                return null;
            }

            @Override
            public String getActionKey() {
                return null;
            }

            @Override
            public HttpServletRequest getRequest() {
                HttpServletRequest httpServletRequest = new HttpServletRequest() {
                    @Override
                    public String getAuthType() {
                        return null;
                    }

                    @Override
                    public Cookie[] getCookies() {
                        return new Cookie[0];
                    }

                    @Override
                    public long getDateHeader(String s) {
                        return 0;
                    }

                    @Override
                    public String getHeader(String s) {
                        return null;
                    }

                    @Override
                    public Enumeration getHeaders(String s) {
                        return null;
                    }

                    @Override
                    public Enumeration getHeaderNames() {
                        return null;
                    }

                    @Override
                    public int getIntHeader(String s) {
                        return 0;
                    }

                    @Override
                    public String getMethod() {
                        return null;
                    }

                    @Override
                    public String getPathInfo() {
                        return null;
                    }

                    @Override
                    public String getPathTranslated() {
                        return null;
                    }

                    @Override
                    public String getContextPath() {
                        return null;
                    }

                    @Override
                    public String getQueryString() {
                        return null;
                    }

                    @Override
                    public String getRemoteUser() {
                        return null;
                    }

                    @Override
                    public boolean isUserInRole(String s) {
                        return false;
                    }

                    @Override
                    public Principal getUserPrincipal() {
                        return null;
                    }

                    @Override
                    public String getRequestedSessionId() {
                        return null;
                    }

                    @Override
                    public String getRequestURI() {
                        return null;
                    }

                    @Override
                    public StringBuffer getRequestURL() {
                        return null;
                    }

                    @Override
                    public String getServletPath() {
                        return null;
                    }

                    @Override
                    public HttpSession getSession(boolean b) {
                        return null;
                    }

                    @Override
                    public HttpSession getSession() {
                        return null;
                    }

                    @Override
                    public String changeSessionId() {
                        return null;
                    }

                    @Override
                    public boolean isRequestedSessionIdValid() {
                        return false;
                    }

                    @Override
                    public boolean isRequestedSessionIdFromCookie() {
                        return false;
                    }

                    @Override
                    public boolean isRequestedSessionIdFromURL() {
                        return false;
                    }

                    @Override
                    public boolean isRequestedSessionIdFromUrl() {
                        return false;
                    }

                    @Override
                    public boolean authenticate(HttpServletResponse httpServletResponse) throws IOException, ServletException {
                        return false;
                    }

                    @Override
                    public void login(String s, String s1) throws ServletException {

                    }

                    @Override
                    public void logout() throws ServletException {

                    }

                    @Override
                    public Collection<Part> getParts() throws IOException, ServletException {
                        return null;
                    }

                    @Override
                    public Part getPart(String s) throws IOException, ServletException {
                        return null;
                    }

                    @Override
                    public <T extends HttpUpgradeHandler> T upgrade(Class<T> aClass) throws IOException, ServletException {
                        return null;
                    }

                    @Override
                    public Object getAttribute(String s) {
                        return null;
                    }

                    @Override
                    public Enumeration getAttributeNames() {
                        return null;
                    }

                    @Override
                    public String getCharacterEncoding() {
                        return null;
                    }

                    @Override
                    public void setCharacterEncoding(String s) throws UnsupportedEncodingException {

                    }

                    @Override
                    public int getContentLength() {
                        return 0;
                    }

                    @Override
                    public long getContentLengthLong() {
                        return 0;
                    }

                    @Override
                    public String getContentType() {
                        return null;
                    }

                    @Override
                    public ServletInputStream getInputStream() throws IOException {
                        return null;
                    }

                    @Override
                    public String getParameter(String s) {
                        return null;
                    }

                    @Override
                    public Enumeration getParameterNames() {
                        return null;
                    }

                    @Override
                    public String[] getParameterValues(String s) {
                        return new String[0];
                    }

                    @Override
                    public Map getParameterMap() {
                        return null;
                    }

                    @Override
                    public String getProtocol() {
                        return null;
                    }

                    @Override
                    public String getScheme() {
                        return null;
                    }

                    @Override
                    public String getServerName() {
                        return null;
                    }

                    @Override
                    public int getServerPort() {
                        return 0;
                    }

                    @Override
                    public BufferedReader getReader() throws IOException {
                        return null;
                    }

                    @Override
                    public String getRemoteAddr() {
                        return null;
                    }

                    @Override
                    public String getRemoteHost() {
                        return null;
                    }

                    @Override
                    public void setAttribute(String s, Object o) {

                    }

                    @Override
                    public void removeAttribute(String s) {

                    }

                    @Override
                    public Locale getLocale() {
                        return null;
                    }

                    @Override
                    public Enumeration getLocales() {
                        return null;
                    }

                    @Override
                    public boolean isSecure() {
                        return false;
                    }

                    @Override
                    public RequestDispatcher getRequestDispatcher(String s) {
                        return null;
                    }

                    @Override
                    public String getRealPath(String s) {
                        return null;
                    }

                    @Override
                    public int getRemotePort() {
                        return 0;
                    }

                    @Override
                    public String getLocalName() {
                        return null;
                    }

                    @Override
                    public String getLocalAddr() {
                        return null;
                    }

                    @Override
                    public int getLocalPort() {
                        return 0;
                    }

                    @Override
                    public ServletContext getServletContext() {
                        return null;
                    }

                    @Override
                    public AsyncContext startAsync() throws IllegalStateException {
                        return null;
                    }

                    @Override
                    public AsyncContext startAsync(ServletRequest servletRequest, ServletResponse servletResponse) throws IllegalStateException {
                        return null;
                    }

                    @Override
                    public boolean isAsyncStarted() {
                        return false;
                    }

                    @Override
                    public boolean isAsyncSupported() {
                        return false;
                    }

                    @Override
                    public AsyncContext getAsyncContext() {
                        return null;
                    }

                    @Override
                    public DispatcherType getDispatcherType() {
                        return null;
                    }
                };
                return httpServletRequest;
            }

            @Override
            public HttpServletResponse getResponse() {
                HttpServletResponse httpServletResponse = new HttpServletResponse() {
                    @Override
                    public void addCookie(Cookie cookie) {

                    }

                    @Override
                    public boolean containsHeader(String s) {
                        return false;
                    }

                    @Override
                    public String encodeURL(String s) {
                        return null;
                    }

                    @Override
                    public String encodeRedirectURL(String s) {
                        return null;
                    }

                    @Override
                    public String encodeUrl(String s) {
                        return null;
                    }

                    @Override
                    public String encodeRedirectUrl(String s) {
                        return null;
                    }

                    @Override
                    public void sendError(int i, String s) throws IOException {

                    }

                    @Override
                    public void sendError(int i) throws IOException {

                    }

                    @Override
                    public void sendRedirect(String s) throws IOException {

                    }

                    @Override
                    public void setDateHeader(String s, long l) {

                    }

                    @Override
                    public void addDateHeader(String s, long l) {

                    }

                    @Override
                    public void setHeader(String s, String s1) {

                    }

                    @Override
                    public void addHeader(String s, String s1) {

                    }

                    @Override
                    public void setIntHeader(String s, int i) {

                    }

                    @Override
                    public void addIntHeader(String s, int i) {

                    }

                    @Override
                    public void setStatus(int i) {

                    }

                    @Override
                    public void setStatus(int i, String s) {

                    }

                    @Override
                    public int getStatus() {
                        return 0;
                    }

                    @Override
                    public String getHeader(String s) {
                        return null;
                    }

                    @Override
                    public Collection<String> getHeaders(String s) {
                        return null;
                    }

                    @Override
                    public Collection<String> getHeaderNames() {
                        return null;
                    }

                    @Override
                    public String getCharacterEncoding() {
                        return null;
                    }

                    @Override
                    public String getContentType() {
                        return null;
                    }

                    @Override
                    public ServletOutputStream getOutputStream() throws IOException {
                        return null;
                    }

                    @Override
                    public PrintWriter getWriter() throws IOException {
                        return null;
                    }

                    @Override
                    public void setCharacterEncoding(String s) {

                    }

                    @Override
                    public void setContentLength(int i) {

                    }

                    @Override
                    public void setContentLengthLong(long l) {

                    }

                    @Override
                    public void setContentType(String s) {

                    }

                    @Override
                    public void setBufferSize(int i) {

                    }

                    @Override
                    public int getBufferSize() {
                        return 0;
                    }

                    @Override
                    public void flushBuffer() throws IOException {

                    }

                    @Override
                    public void resetBuffer() {

                    }

                    @Override
                    public boolean isCommitted() {
                        return false;
                    }

                    @Override
                    public void reset() {

                    }

                    @Override
                    public void setLocale(Locale locale) {

                    }

                    @Override
                    public Locale getLocale() {
                        return null;
                    }
                };
                return httpServletResponse;
            }

            @Override
            public byte[] getPicFile() {
                return new byte[0];
            }

            @Override
            public CacheContext getCacheContext() {
                return null;
            }

            @Override
            public AppType getAppType() {
                return null;
            }

            @Override
            public boolean isMtClient() {
                return true;
            }

            @Override
            public DeviceType getDeviceType() {
                return null;
            }

            @Override
            public UserStatusResult getUserStatus() {
                UserStatusResult userStatusResult = new UserStatusResult();
                userStatusResult.setMtUserId(112233L);
                return userStatusResult;
            }

            @Override
            public void setUserStatus(UserStatusResult result) {

            }

            @Override
            public CrawlMark getCrawlMark() {
                return null;
            }

            @Override
            public int getAppId() {
                return 0;
            }
        };
        return context;
    }
}
