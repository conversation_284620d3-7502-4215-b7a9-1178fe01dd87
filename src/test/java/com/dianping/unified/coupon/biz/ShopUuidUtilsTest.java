package com.dianping.unified.coupon.biz;

import com.dianping.pay.framework.utils.shopUuid.ShopUuidUtils;
import com.dianping.squirrel.common.util.CollectionUtils;
import org.junit.Assert;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/04/22
 */
public class ShopUuidUtilsTest extends TestBase {

    @Autowired
    private ShopUuidUtils shopUuidUtils;

    @Test
    public void testUuidToShopId() {
        String uuid  = "l4UFPyyZ96Cq4u7n";
        Assert.assertTrue(shopUuidUtils.toShopId(uuid) > 0);
    }

    @Test
    public void testUuidToShopIdLong() {
        String uuid  = "96728810";
        Assert.assertTrue(shopUuidUtils.toShopId(uuid) > 0);
    }

    @Test
    public void testGetShopUuidDTOListByUUIdList() {
        List<String> uuidList = new ArrayList();
        uuidList.add("l4UFPyyZ96Cq4u7n");
        Assert.assertTrue(CollectionUtils.isNotEmpty(shopUuidUtils.getShopUuidDTOListByUUIdList(uuidList)));
    }
}
