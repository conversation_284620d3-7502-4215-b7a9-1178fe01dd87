package com.dianping.unified.coupon.biz;

import com.dianping.gmkt.coupon.common.api.enums.MtCouponPlatform;
import com.dianping.mobile.framework.datatypes.IMobileContext;
import com.dianping.mobile.framework.datatypes.MobileContext;
import com.dianping.mobile.framework.datatypes.MobileHeader;
import com.dianping.pay.api.biz.activity.RestUserInfoService;
import com.dianping.pay.api.biz.activity.newloader.dto.RestUserInfo;
import com.dianping.pay.api.biz.activity.newloader.mapper.IssueActivityMapper;
import com.dianping.pay.api.entity.issuecoupon.ProductcouponpromoRequest;
import com.dianping.pay.api.enums.CouponClientTypeEnum;
import com.dianping.pay.api.enums.CouponPlatformTypeEnum;
import com.dianping.pay.api.util.ClientTypeUtils;
import com.dianping.pay.common.enums.PayPlatform;
import org.junit.Assert;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.mock.web.MockHttpServletRequest;

public class RestInfoServiceTest extends TestBase{


    @Autowired
    private RestUserInfoService restInfoService;

    @Test
    public void testParseUserInfo() {
        IMobileContext mobileContext = buildContext();
        RestUserInfo restUserInfo = restInfoService.handleMobileContext(mobileContext);
        Assert.assertTrue(restUserInfo != null);


        ProductcouponpromoRequest request = new ProductcouponpromoRequest();
        Assert.assertTrue(IssueActivityMapper.parsePlatform(mobileContext, request) == CouponPlatformTypeEnum.ANDROID.getCode());
        Assert.assertTrue(IssueActivityMapper.parseClientType(mobileContext, request) == CouponClientTypeEnum.NATIVE.getCode());


        mobileContext = buildContext1();
        Assert.assertTrue(IssueActivityMapper.parsePlatform(mobileContext, request) == CouponPlatformTypeEnum.IPHONE.getCode());
        Assert.assertTrue(IssueActivityMapper.parseClientType(mobileContext, request) == CouponClientTypeEnum.MINIPROGRAM.getCode());

        Assert.assertTrue(ClientTypeUtils.isMainWeb(MtCouponPlatform.WEB_PAGE.getCode(), true));
        Assert.assertTrue(ClientTypeUtils.isMainWeb(PayPlatform.tg_pc.getCode(), false));
        Assert.assertTrue(ClientTypeUtils.isMainWX(MtCouponPlatform.WEIXIN_CX.getCode(), true));
        Assert.assertTrue(ClientTypeUtils.isMainWX(PayPlatform.weixin_api.getCode(), false));
    }

    private IMobileContext buildContext() {
        MockHttpServletRequest request = new MockHttpServletRequest();
        request.addHeader("appName", "meituan");
        MobileContext context = new MobileContext(request, null);
        MobileHeader header = new MobileHeader();
        context.setHeader(header);
        return context;

    }

    private IMobileContext buildContext1() {
        MockHttpServletRequest request = new MockHttpServletRequest();
        request.addHeader("appName", "meituan");
        request.addHeader("platform", "iPhone");
        request.addHeader("mpSource", "wechat");
        return new MobileContext(request, null);
    }

}
