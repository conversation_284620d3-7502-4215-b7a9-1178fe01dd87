package com.dianping.unified.coupon.biz;

import com.alibaba.fastjson.JSON;
import com.dianping.gmkt.coupon.common.api.dto.DzTimeLimitRule;
import com.dianping.gmkt.coupon.common.api.enums.CouponGroupExtraKeyEnum;
import com.dianping.pay.api.util.IssueCouponUtils;
import com.dianping.unified.coupon.manage.api.dto.UnifiedCouponGroupDTO;
import org.junit.Assert;
import org.junit.Test;

import java.util.*;

public class IssueCouponUtilsTest extends TestBase{
    @Test
    public void testFormatDzTimeLimitRule() {
        UnifiedCouponGroupDTO unifiedCouponGroupDTO = new UnifiedCouponGroupDTO();
        Map<String, String> extraInfoMap = new HashMap<>();
        DzTimeLimitRule dzTimeLimitRule = new DzTimeLimitRule();
        Set<Integer> limitWeeks = new HashSet<>();
        limitWeeks.add(1);
//        limitWeeks.add(2);
        limitWeeks.add(3);
//        limitWeeks.add(4);
        limitWeeks.add(5);
//        limitWeeks.add(6);
//        limitWeeks.add(7);
        dzTimeLimitRule.setLimitWeeks(limitWeeks);
        extraInfoMap.put(CouponGroupExtraKeyEnum.dzTimeLimitRule.name(), JSON.toJSONString(dzTimeLimitRule));
        unifiedCouponGroupDTO.setExtraInfoMap(extraInfoMap);
        Assert.assertTrue(IssueCouponUtils.isDzTimeLimitRule(unifiedCouponGroupDTO));
        Assert.assertEquals(IssueCouponUtils.formatDzTimeLimitRule(unifiedCouponGroupDTO), "限周一、周三、周五的场次可用");
    }
}
