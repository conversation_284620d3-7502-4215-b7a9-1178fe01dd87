package com.dianping.unified.coupon.biz;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.dianping.api.picasso.controller.IssueCouponComponentAction;
import com.dianping.mobile.base.datatypes.enums.ClientType;
import com.dianping.mobile.base.datatypes.enums.DeviceType;
import com.dianping.mobile.framework.cache.CacheContext;
import com.dianping.mobile.framework.datatypes.AppType;
import com.dianping.mobile.framework.datatypes.IMobileContext;
import com.dianping.mobile.framework.datatypes.IMobileResponse;
import com.dianping.mobile.framework.datatypes.MobileHeader;
import com.dianping.mobile.framework.datatypes.client.ClientConfig;
import com.dianping.mobile.framework.datatypes.crawl.CrawlMark;
import com.dianping.mobile.framework.datatypes.token.UserStatusResult;
import com.dianping.pay.api.beans.TopAdResourcePromotionDO;
import com.dianping.pay.api.biz.activity.RestUserInfoService;
import com.dianping.pay.api.biz.activity.UnifiedIssueCouponBiz;
import com.dianping.pay.api.biz.activity.newloader.CouponIssueActivityQueryService;
import com.dianping.pay.api.biz.activity.newloader.dto.CouponActivityContext;
import com.dianping.pay.api.biz.activity.newloader.dto.PromotionRequestContext;
import com.dianping.pay.api.entity.issuecoupon.IssueCouponComponentDTO;
import com.dianping.pay.api.entity.issuecoupon.IssueCouponDetail;
import com.dianping.pay.api.entity.issuecoupon.IssueCouponListDetail;
import com.dianping.pay.api.entity.issuecoupon.IssueCouponSimpleDetail;
import com.dianping.pay.api.entity.issuecoupon.IssuecouponcomponentRequest;
import com.dianping.pay.api.entity.issuecoupon.ShopCartIssueMultiCouponRequest;
import com.dianping.pay.api.util.JsonUtils;
import com.dianping.pay.common.enums.PayPlatform;
import com.dianping.pay.framework.utils.shopUuid.ShopUuidUtils;

import java.util.*;

import org.apache.commons.fileupload.FileItem;
import org.junit.Assert;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import javax.servlet.*;
import javax.servlet.http.*;
import java.io.BufferedReader;
import java.io.IOException;
import java.io.PrintWriter;
import java.io.UnsupportedEncodingException;
import java.lang.reflect.Method;
import java.security.Principal;

public class IssueCouponCompnentTest extends TestBase {
    @Autowired
    private CouponIssueActivityQueryService couponIssueActivityQueryService;

    @Autowired
    private UnifiedIssueCouponBiz unifiedIssueCouponBiz;

    @Autowired
    private ShopUuidUtils shopUuidUtils;

    @Autowired
    private RestUserInfoService restUserInfoService;

    @Test
    public void testIssueCouponCompnent() throws NoSuchMethodException {
        IMobileContext iMobileContext = buildMTIMobileContext();
        String contextJsonStr = "{\"latitude\":29.83104027777794,\"longitude\":121.5978966877173,\"dpid\":\"c81ea60b0bdd462f9cdb632cec018073a155638914227200416\",\"cityId\":11,\"dpUserId\":********,\"mtUserId\":0,\"dpShopId\":73559560,\"mtShopId\":0,\"couponPlatform\":20020400,\"payPlatform\":20020400,\"userType\":\"DP\",\"needReturnPromotion\":true,\"needShareCouponPromotion\":false,\"needReductionPromotion\":true,\"needResourcesPromotion\":true,\"needUserPromotion\":false,\"needBrandPromotion\":false,\"needMemberPromotion\":true,\"needFinalcialCouponPromotion\":true,\"needQueryPricePromotion\":false,\"needShopResourcesPromotion\":false,\"skuId\":0,\"spuGroupId\":0,\"dpDealGroupId\":*********,\"mtDealGroupId\":0,\"queryShopCouponType\":1,\"productType\":0,\"brandId\":0,\"beautyDealNewType\":false,\"needDiscountCoupon\":true,\"userId\":********,\"shopId\":**********,\"mt\":false}";
        PromotionRequestContext context = JSON.parseObject(contextJsonStr, PromotionRequestContext.class);
        context.setNeedMerchantAccountPromotion(true);
        CouponActivityContext couponActivityContext = couponIssueActivityQueryService.queryShopPromotions(context, buildMTIMobileContext());
        IssueCouponComponentDTO issueCouponComponentResp = unifiedIssueCouponBiz.toIssueCouponComponentResponse(couponActivityContext,
                context, iMobileContext);
        System.out.println("response:" + JSONObject.toJSONString(issueCouponComponentResp));
        Assert.assertNotNull(issueCouponComponentResp);
    }

    private IMobileContext buildMTIMobileContext() {
        IMobileContext context = new IMobileContext() {
            @Override
            public Set<String> getParameterKeys() {
                return null;
            }

            @Override
            public String getParameter(String s) {
                return null;
            }

            @Override
            public MobileHeader getHeader() {
                MobileHeader header = new MobileHeader();
                header.setDpid("112233");
                return header;
            }

            @Override
            public String getUserIp() {
                return null;
            }

            @Override
            public ClientType getClient() {
                return ClientType.DP_POSWED_ANDROID;
            }

            @Override
            public String getVersion() {
                return null;
            }

            @Override
            public String getSource() {
                return null;
            }

            @Override
            public String getCellPhoneType() {
                return null;
            }

            @Override
            public String getUserAgent() {
                return "mapi-Test-natice";
            }

            @Override
            public long getUserId() {
                return ********;
            }

            @Override
            public Map<String, FileItem> getMultiParamMap() {
                return null;
            }

            @Override
            public String getOs() {
                return null;
            }

            @Override
            public String getProtocolVersion() {
                return null;
            }

            @Override
            public boolean isPost() {
                return false;
            }

            @Override
            public ClientConfig getConfig() {
                return null;
            }

            @Override
            public String getRequestId() {
                return null;
            }

            @Override
            public String getRequestIdReffer() {
                return null;
            }

            @Override
            public String getActionKey() {
                return null;
            }

            @Override
            public HttpServletRequest getRequest() {
                HttpServletRequest httpServletRequest = new HttpServletRequest() {
                    @Override
                    public String getAuthType() {
                        return null;
                    }

                    @Override
                    public Cookie[] getCookies() {
                        return new Cookie[0];
                    }

                    @Override
                    public long getDateHeader(String s) {
                        return 0;
                    }

                    @Override
                    public String getHeader(String s) {
                        return null;
                    }

                    @Override
                    public Enumeration getHeaders(String s) {
                        return null;
                    }

                    @Override
                    public Enumeration getHeaderNames() {
                        return null;
                    }

                    @Override
                    public int getIntHeader(String s) {
                        return 0;
                    }

                    @Override
                    public String getMethod() {
                        return null;
                    }

                    @Override
                    public String getPathInfo() {
                        return null;
                    }

                    @Override
                    public String getPathTranslated() {
                        return null;
                    }

                    @Override
                    public String getContextPath() {
                        return null;
                    }

                    @Override
                    public String getQueryString() {
                        return null;
                    }

                    @Override
                    public String getRemoteUser() {
                        return null;
                    }

                    @Override
                    public boolean isUserInRole(String s) {
                        return false;
                    }

                    @Override
                    public Principal getUserPrincipal() {
                        return null;
                    }

                    @Override
                    public String getRequestedSessionId() {
                        return null;
                    }

                    @Override
                    public String getRequestURI() {
                        return null;
                    }

                    @Override
                    public StringBuffer getRequestURL() {
                        return null;
                    }

                    @Override
                    public String getServletPath() {
                        return null;
                    }

                    @Override
                    public HttpSession getSession(boolean b) {
                        return null;
                    }

                    @Override
                    public HttpSession getSession() {
                        return null;
                    }

                    @Override
                    public String changeSessionId() {
                        return null;
                    }

                    @Override
                    public boolean isRequestedSessionIdValid() {
                        return false;
                    }

                    @Override
                    public boolean isRequestedSessionIdFromCookie() {
                        return false;
                    }

                    @Override
                    public boolean isRequestedSessionIdFromURL() {
                        return false;
                    }

                    @Override
                    public boolean isRequestedSessionIdFromUrl() {
                        return false;
                    }

                    @Override
                    public boolean authenticate(HttpServletResponse httpServletResponse) throws IOException, ServletException {
                        return false;
                    }

                    @Override
                    public void login(String s, String s1) throws ServletException {

                    }

                    @Override
                    public void logout() throws ServletException {

                    }

                    @Override
                    public Collection<Part> getParts() throws IOException, ServletException {
                        return null;
                    }

                    @Override
                    public Part getPart(String s) throws IOException, ServletException {
                        return null;
                    }

                    @Override
                    public <T extends HttpUpgradeHandler> T upgrade(Class<T> aClass) throws IOException, ServletException {
                        return null;
                    }

                    @Override
                    public Object getAttribute(String s) {
                        return null;
                    }

                    @Override
                    public Enumeration getAttributeNames() {
                        return null;
                    }

                    @Override
                    public String getCharacterEncoding() {
                        return null;
                    }

                    @Override
                    public void setCharacterEncoding(String s) throws UnsupportedEncodingException {

                    }

                    @Override
                    public int getContentLength() {
                        return 0;
                    }

                    @Override
                    public long getContentLengthLong() {
                        return 0;
                    }

                    @Override
                    public String getContentType() {
                        return null;
                    }

                    @Override
                    public ServletInputStream getInputStream() throws IOException {
                        return null;
                    }

                    @Override
                    public String getParameter(String s) {
                        return null;
                    }

                    @Override
                    public Enumeration getParameterNames() {
                        return null;
                    }

                    @Override
                    public String[] getParameterValues(String s) {
                        return new String[0];
                    }

                    @Override
                    public Map getParameterMap() {
                        return null;
                    }

                    @Override
                    public String getProtocol() {
                        return null;
                    }

                    @Override
                    public String getScheme() {
                        return null;
                    }

                    @Override
                    public String getServerName() {
                        return null;
                    }

                    @Override
                    public int getServerPort() {
                        return 0;
                    }

                    @Override
                    public BufferedReader getReader() throws IOException {
                        return null;
                    }

                    @Override
                    public String getRemoteAddr() {
                        return null;
                    }

                    @Override
                    public String getRemoteHost() {
                        return null;
                    }

                    @Override
                    public void setAttribute(String s, Object o) {

                    }

                    @Override
                    public void removeAttribute(String s) {

                    }

                    @Override
                    public Locale getLocale() {
                        return null;
                    }

                    @Override
                    public Enumeration getLocales() {
                        return null;
                    }

                    @Override
                    public boolean isSecure() {
                        return false;
                    }

                    @Override
                    public RequestDispatcher getRequestDispatcher(String s) {
                        return null;
                    }

                    @Override
                    public String getRealPath(String s) {
                        return null;
                    }

                    @Override
                    public int getRemotePort() {
                        return 0;
                    }

                    @Override
                    public String getLocalName() {
                        return null;
                    }

                    @Override
                    public String getLocalAddr() {
                        return null;
                    }

                    @Override
                    public int getLocalPort() {
                        return 0;
                    }

                    @Override
                    public ServletContext getServletContext() {
                        return null;
                    }

                    @Override
                    public AsyncContext startAsync() throws IllegalStateException {
                        return null;
                    }

                    @Override
                    public AsyncContext startAsync(ServletRequest servletRequest, ServletResponse servletResponse) throws IllegalStateException {
                        return null;
                    }

                    @Override
                    public boolean isAsyncStarted() {
                        return false;
                    }

                    @Override
                    public boolean isAsyncSupported() {
                        return false;
                    }

                    @Override
                    public AsyncContext getAsyncContext() {
                        return null;
                    }

                    @Override
                    public DispatcherType getDispatcherType() {
                        return null;
                    }
                };
                return httpServletRequest;
            }

            @Override
            public HttpServletResponse getResponse() {
                HttpServletResponse httpServletResponse = new HttpServletResponse() {
                    @Override
                    public void addCookie(Cookie cookie) {

                    }

                    @Override
                    public boolean containsHeader(String s) {
                        return false;
                    }

                    @Override
                    public String encodeURL(String s) {
                        return null;
                    }

                    @Override
                    public String encodeRedirectURL(String s) {
                        return null;
                    }

                    @Override
                    public String encodeUrl(String s) {
                        return null;
                    }

                    @Override
                    public String encodeRedirectUrl(String s) {
                        return null;
                    }

                    @Override
                    public void sendError(int i, String s) throws IOException {

                    }

                    @Override
                    public void sendError(int i) throws IOException {

                    }

                    @Override
                    public void sendRedirect(String s) throws IOException {

                    }

                    @Override
                    public void setDateHeader(String s, long l) {

                    }

                    @Override
                    public void addDateHeader(String s, long l) {

                    }

                    @Override
                    public void setHeader(String s, String s1) {

                    }

                    @Override
                    public void addHeader(String s, String s1) {

                    }

                    @Override
                    public void setIntHeader(String s, int i) {

                    }

                    @Override
                    public void addIntHeader(String s, int i) {

                    }

                    @Override
                    public void setStatus(int i) {

                    }

                    @Override
                    public void setStatus(int i, String s) {

                    }

                    @Override
                    public int getStatus() {
                        return 0;
                    }

                    @Override
                    public String getHeader(String s) {
                        return null;
                    }

                    @Override
                    public Collection<String> getHeaders(String s) {
                        return null;
                    }

                    @Override
                    public Collection<String> getHeaderNames() {
                        return null;
                    }

                    @Override
                    public String getCharacterEncoding() {
                        return null;
                    }

                    @Override
                    public String getContentType() {
                        return null;
                    }

                    @Override
                    public ServletOutputStream getOutputStream() throws IOException {
                        return null;
                    }

                    @Override
                    public PrintWriter getWriter() throws IOException {
                        return null;
                    }

                    @Override
                    public void setCharacterEncoding(String s) {

                    }

                    @Override
                    public void setContentLength(int i) {

                    }

                    @Override
                    public void setContentLengthLong(long l) {

                    }

                    @Override
                    public void setContentType(String s) {

                    }

                    @Override
                    public void setBufferSize(int i) {

                    }

                    @Override
                    public int getBufferSize() {
                        return 0;
                    }

                    @Override
                    public void flushBuffer() throws IOException {

                    }

                    @Override
                    public void resetBuffer() {

                    }

                    @Override
                    public boolean isCommitted() {
                        return false;
                    }

                    @Override
                    public void reset() {

                    }

                    @Override
                    public void setLocale(Locale locale) {

                    }

                    @Override
                    public Locale getLocale() {
                        return null;
                    }
                };
                return httpServletResponse;
            }

            @Override
            public byte[] getPicFile() {
                return new byte[0];
            }

            @Override
            public CacheContext getCacheContext() {
                return null;
            }

            @Override
            public AppType getAppType() {
                return null;
            }

            @Override
            public boolean isMtClient() {
                return true;
            }

            @Override
            public DeviceType getDeviceType() {
                return null;
            }

            @Override
            public UserStatusResult getUserStatus() {
                UserStatusResult userStatusResult = new UserStatusResult();
                userStatusResult.setUserId(********);
                return userStatusResult;
            }

            @Override
            public void setUserStatus(UserStatusResult result) {

            }

            @Override
            public CrawlMark getCrawlMark() {
                return null;
            }

            @Override
            public int getAppId() {
                return 0;
            }
        };
        return context;
    }

    @Test
    public void buildTopAdResourceCoupon() {
        String str = "{\"adId\":25898,\"voucherAutoType\":0,\"voucherDetailDOList\":[{\"discount\":0,\"issueStatus\":1,\"priceAmount\":5,\"priceLimit\":59,\"title\":\"变美神券测试11.21\",\"useBeginTime\":0,\"useEndTime\":0,\"voucherValueType\":0}]}";
        TopAdResourcePromotionDO topAdResourcePromotionDO = JsonUtils.toObject(str, TopAdResourcePromotionDO.class);
        IssueCouponListDetail response1 = unifiedIssueCouponBiz.buildTopAdResourceCoupon(topAdResourcePromotionDO, null);
        IssueCouponDetail response2= unifiedIssueCouponBiz.buildIssueCouponDetailFromCouponDesc(topAdResourcePromotionDO.getVoucherDetailDOList().get(0));
        List<IssueCouponSimpleDetail>  response3 = unifiedIssueCouponBiz.buildTopAdResourcePromotionList(topAdResourcePromotionDO.getVoucherDetailDOList(), null);
        Assert.assertNotNull(response1);
        Assert.assertNotNull(response2);
        Assert.assertNotNull(response3);
    }
}
