package com.dianping.unified.coupon.biz;

import com.dianping.api.service.ShopWrapService;
import com.dianping.pay.api.util.JsonUtils;
import com.dianping.shopremote.remote.dto.ShopDTO;
import org.junit.Assert;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * <AUTHOR>
 * @date 2022/04/22
 */
public class ShopWrapServiceTest extends TestBase {
    @Autowired
    private ShopWrapService shopWrapService;

    @Test
    public void testLoadShopInfoMt() {
        ShopDTO shopDTO = shopWrapService.loadShopInfo(1798321, true);
        System.out.println(JsonUtils.toJson(shopDTO));
        Assert.assertTrue(shopDTO != null);
    }

    @Test
    public void testLoadShopInfoDp() {
        ShopDTO shopDTO = shopWrapService.loadShopInfo(1798321, false);
        System.out.println(JsonUtils.toJson(shopDTO));
        Assert.assertTrue(shopDTO != null);
    }
}
