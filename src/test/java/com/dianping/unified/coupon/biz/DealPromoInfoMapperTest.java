package com.dianping.unified.coupon.biz;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.dianping.pay.api.biz.activity.newloader.dto.CouponActivityContext;
import com.dianping.pay.api.biz.activity.newloader.mapper.DealPromoInfoMapper;
import com.dianping.pay.api.entity.issuecoupon.CouponProxyPromoInfo;
import com.dianping.pay.api.entity.issuecoupon.NewBatchIssueCouponRequest;
import com.dianping.pay.api.entity.issuecoupon.NewIssueCouponUnit;
import com.dianping.pay.api.enums.QueryShopCouponTypeEnum;
import com.google.common.collect.Lists;
import com.sankuai.nibmktproxy.queryclient.proxy.AssignedStatusEnum;
import com.sankuai.nibmktproxy.queryclient.proxy.FinancialCouponCategoryEnum;
import com.sankuai.nibmktproxy.queryclient.proxy.FinancialCouponDTO;
import com.sankuai.nibmktproxy.queryclient.proxy.FinancialCouponSubTypeEnum;
import com.sankuai.nibmktproxy.queryclient.proxy.GetPromotionDTO;
import com.sankuai.nibmktproxy.queryclient.proxy.PromotionDTO;
import com.sankuai.nibmktproxy.queryclient.proxy.PromotionDTOResult;
import com.sankuai.nibmktproxy.queryclient.proxy.PromotionType;
import java.util.List;
import org.junit.Assert;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

public class DealPromoInfoMapperTest extends TestBase{

    @Autowired
    private DealPromoInfoMapper dealPromoInfoMapper;

    @Test
    public void buildPromoAggInfoTest() {
        CouponActivityContext context = new CouponActivityContext();
        PromotionDTOResult promotionDTOResult = new PromotionDTOResult();
        context.setPromotionDTOResult(promotionDTOResult);
        context.setQueryShopCouponType(QueryShopCouponTypeEnum.BY_DEAL.getCode());
        context.setDealPromoProxy(true);
        CouponProxyPromoInfo couponProxyPromoInfo = dealPromoInfoMapper.buildPromoAggInfo(context);
        Assert.assertTrue(couponProxyPromoInfo == null);
    }

    @Test
    public void buildFinancialGovConsumeCouponTagTest() {
        CouponActivityContext couponActivityContext = new CouponActivityContext();
        PromotionDTOResult promotionDTOResult = new PromotionDTOResult();
        couponActivityContext.setPromotionDTOResult(promotionDTOResult);
        List<GetPromotionDTO> promos = Lists.newArrayList();
        promotionDTOResult.setGetPromotionDTO(promos);
        GetPromotionDTO getPromotionDTO = new GetPromotionDTO();
        promos.add(getPromotionDTO);
        PromotionDTO promotionDTO = new PromotionDTO();
        getPromotionDTO.setPromotionDTO(promotionDTO);
        FinancialCouponDTO consumeCouponDTO = new FinancialCouponDTO();
        promotionDTO.setFinancialCouponDTO(consumeCouponDTO);
        getPromotionDTO.setPromotionType(PromotionType.FINANCIAL_COUPON);
        consumeCouponDTO.setAssignedStatus(AssignedStatusEnum.ALREADY_ASSIGNED);
        consumeCouponDTO.setAvailable(true);
        consumeCouponDTO.setFinancialCouponCategoryEnum(FinancialCouponCategoryEnum.GOVERNMENT_CONSUME_COUPON);
        consumeCouponDTO.setFinancialCouponSubTypeEnum(FinancialCouponSubTypeEnum.INSTANT_DISCOUNT_COUPON);
        consumeCouponDTO.setCouponValue("1024.6");
        List<String> govConsumeCouponTagList = dealPromoInfoMapper.buildFinancialGovConsumeCouponTag(couponActivityContext);
        Assert.assertTrue(govConsumeCouponTagList.get(0).equals("10.25元无门槛消费券"));
        consumeCouponDTO.setFinancialCouponSubTypeEnum(FinancialCouponSubTypeEnum.FULL_DISCOUNT_COUPON);
        consumeCouponDTO.setMinConsumption("50000");
        govConsumeCouponTagList = dealPromoInfoMapper.buildFinancialGovConsumeCouponTag(couponActivityContext);
        Assert.assertTrue(govConsumeCouponTagList.get(0).equals("满500减10.25消费券"));
    }

    @Test
    public void buildFinancialPromoAggInfoTest() {
        CouponActivityContext couponActivityContext = new CouponActivityContext();
        PromotionDTOResult promotionDTOResult = new PromotionDTOResult();
        couponActivityContext.setPromotionDTOResult(promotionDTOResult);
        List<GetPromotionDTO> promos = Lists.newArrayList();
        promotionDTOResult.setGetPromotionDTO(promos);
        GetPromotionDTO getPromotionDTO = buildDiscountPromotion();
        GetPromotionDTO getPromotionDTO1 = buildFullDiscountPromotion();
        promos.add(getPromotionDTO);
        promos.add(getPromotionDTO1);
        CouponProxyPromoInfo result = dealPromoInfoMapper.buildPromoAggInfo(couponActivityContext);
        Assert.assertNotNull(result);
    }

    private GetPromotionDTO buildDiscountPromotion() {
        GetPromotionDTO getPromotionDTO = new GetPromotionDTO();
        PromotionDTO promotionDTO = new PromotionDTO();
        getPromotionDTO.setPromotionDTO(promotionDTO);
        FinancialCouponDTO consumeCouponDTO = new FinancialCouponDTO();
        promotionDTO.setFinancialCouponDTO(consumeCouponDTO);
        getPromotionDTO.setPromotionType(PromotionType.FINANCIAL_COUPON);
        consumeCouponDTO.setAssignedStatus(AssignedStatusEnum.ALREADY_ASSIGNED);
        consumeCouponDTO.setAvailable(true);
        consumeCouponDTO.setFinancialCouponCategoryEnum(FinancialCouponCategoryEnum.GOVERNMENT_CONSUME_COUPON);
        consumeCouponDTO.setFinancialCouponSubTypeEnum(FinancialCouponSubTypeEnum.INSTANT_DISCOUNT_COUPON);
        consumeCouponDTO.setCouponValue("1024.6");
        return getPromotionDTO;
    }

    private GetPromotionDTO buildFullDiscountPromotion() {
        GetPromotionDTO getPromotionDTO1 = new GetPromotionDTO();
        PromotionDTO promotionDTO1 = new PromotionDTO();
        getPromotionDTO1.setPromotionDTO(promotionDTO1);
        FinancialCouponDTO consumeCouponDTO1 = new FinancialCouponDTO();
        promotionDTO1.setFinancialCouponDTO(consumeCouponDTO1);
        getPromotionDTO1.setPromotionType(PromotionType.FINANCIAL_COUPON);
        consumeCouponDTO1.setAssignedStatus(AssignedStatusEnum.ALREADY_ASSIGNED);
        consumeCouponDTO1.setAvailable(true);
        consumeCouponDTO1.setFinancialCouponCategoryEnum(FinancialCouponCategoryEnum.GOVERNMENT_CONSUME_COUPON);
        consumeCouponDTO1.setFinancialCouponSubTypeEnum(FinancialCouponSubTypeEnum.FULL_DISCOUNT_COUPON);
        consumeCouponDTO1.setCouponValue("1000");
        consumeCouponDTO1.setMinConsumption("5000");
        return getPromotionDTO1;
    }

    @Test
    public void buildNewBatchIssueCouponRequest() {
        NewBatchIssueCouponRequest newBatchIssueCouponRequest = new NewBatchIssueCouponRequest();
        newBatchIssueCouponRequest.setIssueDetailSourceCode(3);
        newBatchIssueCouponRequest.setProductType(0);
        NewIssueCouponUnit newIssueCouponUnit1 = new NewIssueCouponUnit();
        newIssueCouponUnit1.setUnitId("1");
        newIssueCouponUnit1.setProductId(123L);
        newIssueCouponUnit1.setCouponType(0);
        newIssueCouponUnit1.setUnifiedCouponGroupId("111");

        NewIssueCouponUnit newIssueCouponUnit2 = new NewIssueCouponUnit();
        newIssueCouponUnit2.setUnitId("2");
        newIssueCouponUnit2.setProductId(456L);
        newIssueCouponUnit2.setCouponType(0);
        newIssueCouponUnit2.setUnifiedCouponGroupId("111");
        newBatchIssueCouponRequest.setNewIssueCouponUnitList(Lists.newArrayList(newIssueCouponUnit1, newIssueCouponUnit2));
        System.out.println("newBatchIssueCouponRequest:" + JSONObject.toJSONString(newBatchIssueCouponRequest));


    }
}
