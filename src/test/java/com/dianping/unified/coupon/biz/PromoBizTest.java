package com.dianping.unified.coupon.biz;

import com.alibaba.fastjson.JSONObject;
import com.dianping.api.constans.CommonConstants;
import com.dianping.cat.Cat;
import com.dianping.gmkt.coupon.common.api.enums.CouponBusiness;
import com.dianping.gmkt.coupon.common.api.enums.CouponPayPlatform;
import com.dianping.gmkt.coupon.common.api.enums.MtCouponPlatform;
import com.dianping.gmkt.coupon.common.api.utils.TgCouponBusinessUtils;
import com.dianping.gmkt.scene.api.delivery.dto.res.CouponDetailDTO;
import com.dianping.gmkt.scene.api.delivery.dto.res.LocationMaterialsDTO;
import com.dianping.gmkt.scene.api.delivery.enums.RecallTypeEnum;
import com.dianping.mobile.base.datatypes.enums.ClientType;
import com.dianping.mobile.base.datatypes.enums.DeviceType;
import com.dianping.mobile.framework.cache.CacheContext;
import com.dianping.mobile.framework.datatypes.AppType;
import com.dianping.mobile.framework.datatypes.IMobileContext;
import com.dianping.mobile.framework.datatypes.MobileHeader;
import com.dianping.mobile.framework.datatypes.client.ClientConfig;
import com.dianping.mobile.framework.datatypes.crawl.CrawlMark;
import com.dianping.mobile.framework.datatypes.token.UserStatusResult;
import com.dianping.pay.api.beans.IssueCouponActivity;
import com.dianping.pay.api.beans.ResourceMaterialsDO;
import com.dianping.pay.api.beans.ResourcePromotionDo;
import com.dianping.pay.api.beans.ShopResourcePromotionDO;
import com.dianping.pay.api.biz.activity.IssueCouponBiz;
import com.dianping.pay.api.biz.activity.UnifiedIssueCouponBiz;
import com.dianping.pay.api.biz.activity.newloader.CouponIssueActivityQueryService;
import com.dianping.pay.api.biz.activity.newloader.dto.CouponActivityContext;
import com.dianping.pay.api.biz.activity.newloader.dto.PromotionRequestContext;
import com.dianping.pay.api.biz.activity.newloader.mapper.DealPromoInfoMapper;
import com.dianping.pay.api.biz.activity.newloader.mapper.IssueActivityMapper;
import com.dianping.pay.api.biz.activity.newloader.mapper.ProductPromoInfoMapper;
import com.dianping.pay.api.biz.activity.newloader.mapper.ShopCartPromoInfoMapper;
import com.dianping.pay.api.biz.activity.newloader.promo.*;
import com.dianping.pay.api.entity.issuecoupon.*;
import com.dianping.pay.api.enums.QueryShopCouponTypeEnum;
import com.dianping.pay.api.service.impl.ProductService;
import com.dianping.pay.api.util.*;
import com.dianping.pay.common.enums.PayPlatform;
import com.dianping.pay.promo.common.enums.User;
import com.dianping.unified.coupon.manage.api.dto.UnifiedCouponGroupDTO;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.meituan.mtrace.Tracer;
import com.sankuai.nibmkt.promotion.api.common.enums.DiscountProviderEnum;
import com.sankuai.mkt.activity.api.merchantsharecoupon.model.MerchantShareCouponExposeDTO;
import com.sankuai.nibmktproxy.queryclient.proxy.CouponDTO;
import com.sankuai.nibmktproxy.queryclient.proxy.PromotionDTOResult;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.fileupload.FileItem;
import org.apache.commons.lang.math.RandomUtils;
import org.apache.commons.lang.time.DateUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.junit.Assert;
import org.junit.Ignore;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import javax.annotation.Resource;
import javax.servlet.*;
import javax.servlet.http.*;
import java.io.BufferedReader;
import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.math.BigDecimal;
import java.security.Principal;
import java.util.*;

public class PromoBizTest extends TestBase{

    @Autowired
    private CouponIssueActivityQueryService queryService;

    @Resource
    private CouponIssueActivityQueryService couponIssueActivityQueryService;

    @Resource
    private DealPromoInfoMapper dealPromoInfoMapper;

    @Resource
    private ReductionPromotionProcessor reductionPromotionProcessor;

    @Resource
    private TgcCouponProcessor tgcCouponProcessor;

    @Resource
    private IssueCouponBiz issueCouponBiz;

    @Resource
    private UnifiedIssueCouponBiz unifiedIssueCouponBiz;

    @Resource
    private ProxyCouponProcessor proxyCouponProcessor;

    @Resource
    private ProductService productService;

    @Resource
    private TgcBatchCouponProcessor tgcBatchCouponProcessor;

    @Resource
    private ShareCouponProcessor shareCouponProcessor;


    @Test
    public void testReductionQuery() {
        CouponActivityContext context = new CouponActivityContext();
        context.setDpid("0000000000000E5B8552D0CFE4911990E13D75A3FF925A156202862888980881");
        context.setCityId(1);
        context.setDpShopIdL(1798321);
        context.setMtShopIdL(1798321);
        context.setDpDealGroupId(44210832);
        context.setMtDealGroupId(80607126);
        context.setNeedReductionPromotion(true);
        context.setQueryShopCouponType(QueryShopCouponTypeEnum.BY_DEAL.getCode());
        context.setUserType(User.MT);
        reductionPromotionProcessor.prePare(context, null);
        Assert.assertNotNull(context);
    }

    @Test
    public void test() {
        CouponDTO couponDTO = new CouponDTO();
        couponDTO.setCategoryLimitText("玻尿酸，水光针");
        couponDTO.setCouponValue("10");
        CouponDetailInfo result = ProxyPromoInfoHelper.genCouponDetailItem(couponDTO, null, null);
        Assert.assertNotNull(result);
    }

    @Test
    public void testBrandQuery() {
        PromotionRequestContext promotionRequestContext = new PromotionRequestContext();
        promotionRequestContext.setBrandId(9756);
        promotionRequestContext.setNeedBrandPromotion(true);
        promotionRequestContext.setUserType(User.DP);
        CouponActivityContext couponActivityContext = couponIssueActivityQueryService.queryShopPromotions(promotionRequestContext, buildContext());
        Assert.assertTrue(couponActivityContext != null);

    }

    @Test
    public void testQueryPromo() {
        IMobileContext iMobileContext = buildContext();
        PromotionRequestContext context = buildPromotionRequestContext(false, 405203424, false);
        CouponActivityContext couponActivityContext = queryService.queryShopPromotions(context, iMobileContext);
        System.out.println(couponActivityContext);
        Assert.assertNotNull(couponActivityContext);
    }

    @Test
    public void testQueryDiscountPromoByShop() {
        IMobileContext iMobileContext = buildContext();
        IssuecouponcomponentRequest request = new IssuecouponcomponentRequest();
        request.setShopid(4741217);
        request.setUsertype(0);
        PromotionRequestContext promotionRequestContext = IssueActivityMapper.buildPromotionRequestContext(request, iMobileContext);
        CouponActivityContext couponActivityContext = queryService.queryShopPromotions(promotionRequestContext, iMobileContext);
        System.out.println(couponActivityContext);
        Assert.assertNotNull(couponActivityContext);
    }

    @Test
    public void testQueryDiscountPromoByShopString() {
        IMobileContext iMobileContext = buildContext();
        IssuecouponcomponentRequest request = new IssuecouponcomponentRequest();
        request.setShopidL(4741217L);
        request.setUsertype(0);
        PromotionRequestContext promotionRequestContext = IssueActivityMapper.buildPromotionRequestContext(request, iMobileContext);
        CouponActivityContext couponActivityContext = queryService.queryShopPromotions(promotionRequestContext, iMobileContext);
        System.out.println(couponActivityContext);
        Assert.assertNotNull(couponActivityContext);
    }


    @Test
    public void testBlackFilter() {
        CouponActivityContext context = new CouponActivityContext();
        context.setUserType(User.DP);
        boolean filterResult = tgcCouponProcessor.filterActivity("49133", context);
        Assert.assertTrue(filterResult);

        filterResult = tgcCouponProcessor.filterActivity("49134", context);
        Assert.assertFalse(filterResult);

    }


    @Test
    public void testQuerySKuPromo() {
        IMobileContext iMobileContext = buildContext();
        PromotionRequestContext context = buildDpSkuPromotionRequestContext();
        CouponActivityContext couponActivityContext = queryService.queryShopPromotions(context, iMobileContext);
        ProductPromoInfo productPromoInfo = ProductPromoInfoMapper.buildProductPromoInfo(couponActivityContext, iMobileContext);
        System.out.println(productPromoInfo);
        Assert.assertNotNull(productPromoInfo);
    }

    @Test
    public void testQuerySPUPromo() {
        IMobileContext iMobileContext = buildContext();
        PromotionRequestContext context = buildSPUPromotionRequestContext();
        CouponActivityContext couponActivityContext = queryService.queryShopPromotions(context, iMobileContext);
        System.out.println(couponActivityContext);
        Assert.assertNotNull(couponActivityContext);
        ProductPromoInfo productPromoInfo = ProductPromoInfoMapper.buildProductPromoInfo(couponActivityContext, iMobileContext);
        Assert.assertNotNull(productPromoInfo);
    }

    @Test
    public void testQueryShopPromo() {
        IMobileContext iMobileContext = buildContext();
        PromotionRequestContext context = buildShopPromotionRequestContext(23927003);
        CouponActivityContext couponActivityContext = queryService.queryShopPromotions(context, iMobileContext);
        System.out.println(couponActivityContext);
        Assert.assertNotNull(couponActivityContext);
        IssueCouponComponentDTO issueCouponComponentResp = unifiedIssueCouponBiz.toIssueCouponComponentResponse(couponActivityContext, context, iMobileContext);
        Assert.assertNotNull(issueCouponComponentResp);
    }

    @Test
    public void testQueryShopDiscountPromo() {
        IMobileContext iMobileContext = buildContext();
        PromotionRequestContext context = buildShopPromotionRequestContext(4741217);
        CouponActivityContext couponActivityContext = queryService.queryShopPromotions(context, iMobileContext);
        System.out.println(couponActivityContext);
        Assert.assertNotNull(couponActivityContext);
        IssueCouponComponentDTO issueCouponComponentResp = unifiedIssueCouponBiz.toIssueCouponComponentResponse(couponActivityContext, context, iMobileContext);
        Assert.assertNotNull(issueCouponComponentResp);
    }

    private PromotionRequestContext buildShopPromotionRequestContext(int shopId) {
        PromotionRequestContext context = new PromotionRequestContext();
        context.setUserType(User.DP);
        context.setMtUserId(37750678);
        context.setQueryShopCouponType(QueryShopCouponTypeEnum.BY_SHOP.getCode());
        context.setDpShopIdL(shopId);
        return context;
    }

    @Test
    public void testQueryBeautyNewPromo() {
        IMobileContext iMobileContext = buildContext();
        PromotionRequestContext context = buildPromotionRequestContext(true, 405203424, false);
        CouponActivityContext couponActivityContext = queryService.queryShopPromotions(context, iMobileContext);
        System.out.println(couponActivityContext);
        Assert.assertNotNull(couponActivityContext);
        CouponProductPromoModule couponProductPromoModule = dealPromoInfoMapper.buildCouponProductPromoModule(couponActivityContext, iMobileContext);
        System.out.println(couponProductPromoModule);
    }

    @Test
    public void testQueryH5() {
        IMobileContext iMobileContext = buildContext();
        PromotionRequestContext context = buildPromotionRequestContext(true, 405203424, false);
        CouponActivityContext couponActivityContext = queryService.queryShopPromotions(context, iMobileContext);
        System.out.println(couponActivityContext);
        Assert.assertNotNull(couponActivityContext);
        CouponProductHttpPromoModule couponProductPromoModule = dealPromoInfoMapper.buildCouponProductHttpPromoModule(couponActivityContext, iMobileContext);
        System.out.println(couponProductPromoModule);
    }

    @Test
    public void testQueryMemberPromo() {
        IMobileContext iMobileContext = buildContext();
        PromotionRequestContext context = buildPromotionRequestContext(false, 405543256, true);
        CouponActivityContext couponActivityContext = queryService.queryShopPromotions(context, iMobileContext);
        System.out.println(couponActivityContext);
        Assert.assertNotNull(couponActivityContext);
        CouponProductPromoModule couponProductPromoModule = dealPromoInfoMapper.buildCouponProductPromoModule(couponActivityContext, iMobileContext);
        System.out.println(couponProductPromoModule);
    }


    @Test
    public void testQueryDiscountPromo() {
        IMobileContext iMobileContext = buildContext();
        PromotionRequestContext context = buildPromotionRequestContext(false, 414146434, true);
        CouponActivityContext couponActivityContext = queryService.queryShopPromotions(context, iMobileContext);
        System.out.println(couponActivityContext);
        Assert.assertNotNull(couponActivityContext);
        CouponProductPromoModule couponProductPromoModule = dealPromoInfoMapper.buildCouponProductPromoModule(couponActivityContext, iMobileContext);
        System.out.println(couponProductPromoModule);
    }

    @Test
    public void testQueryDiscountPromoWithOld() {
        IMobileContext iMobileContext = buildContext();
        PromotionRequestContext context = buildPromotionRequestContext(false, 414146434, true, false);
        CouponActivityContext couponActivityContext = queryService.queryShopPromotions(context, iMobileContext);
        System.out.println(couponActivityContext);
        Assert.assertNotNull(couponActivityContext);
        CouponProductPromoModule couponProductPromoModule = dealPromoInfoMapper.buildCouponProductPromoModule(couponActivityContext, iMobileContext);
        System.out.println(couponProductPromoModule);
    }

    @Test
    public void testQueryJuhuasuan() {
        Tracer.setSwimlane("1938-awylx");
        IMobileContext iMobileContext = buildContext();
        PromotionRequestContext context = buildMtPromotionRequestContext(false, 414351484, true, false);
        CouponActivityContext couponActivityContext = queryService.queryShopPromotions(context, iMobileContext);
        System.out.println(couponActivityContext);
        Assert.assertNotNull(couponActivityContext);
        CouponProductPromoModule couponProductPromoModule = dealPromoInfoMapper.buildCouponProductPromoModule(couponActivityContext, iMobileContext);
        System.out.println(JsonUtils.toJson(couponProductPromoModule));
    }

    @Test
    public void testQueryResourcePromo() {
        IMobileContext iMobileContext = buildContext();
        PromotionRequestContext context = buildPromotionRequestContext(false, 414146434, true, false);
        CouponActivityContext couponActivityContext = queryService.queryShopPromotions(context, iMobileContext);
        ResourcePromotionDo exposureResponseDTO = new  ResourcePromotionDo();
        exposureResponseDTO.setFlowId(123L);
        exposureResponseDTO.setActivityId(123L);
        exposureResponseDTO.setMaterialId("123");
        exposureResponseDTO.setRowKey("123");
        exposureResponseDTO.setDrawStatus(1);


        CouponDetailDTO detailDTO = new CouponDetailDTO();
        detailDTO.setUseEndTime(DateUtils.addDays(new Date(), 5).getTime());
        detailDTO.setUseBeginTime(new Date().getTime());
        detailDTO.setRandomExpandMaxPrice("36");
        Map<String, String> materialMap = Maps.newHashMap();
        exposureResponseDTO.setStaticMaterialContext(materialMap);
        materialMap.put("prizeTitle", "满300可用");
        materialMap.put("prizeInfo", "全店通用");
        exposureResponseDTO.setDetailDTO(detailDTO);

        couponActivityContext.setResourceExposure(exposureResponseDTO);

        System.out.println(couponActivityContext);
        Assert.assertNotNull(couponActivityContext);
        CouponProductPromoModule couponProductPromoModule = dealPromoInfoMapper.buildCouponProductPromoModule(couponActivityContext, iMobileContext);
        System.out.println(couponProductPromoModule);
    }

    @Test
    public void testCouponBusiness() {
        int productCode = CouponBusinessUtils.getDzTuangouProductCode();
        System.out.println(productCode);
        Assert.assertTrue(productCode == CouponBusiness.TUANGOU.getCode());
    }

    @Test
    public void testQueryDiscountPromoWithProxy() {
        IMobileContext iMobileContext = buildContext();
        PromotionRequestContext context = buildPromotionRequestContext(false, 410622084, true);
        CouponActivityContext couponActivityContext = queryService.queryShopPromotions(context, iMobileContext);
        System.out.println(couponActivityContext);
        Assert.assertNotNull(couponActivityContext);
        CouponProductPromoModule couponProductPromoModule = dealPromoInfoMapper.buildCouponProductPromoModule(couponActivityContext, iMobileContext);
        System.out.println(JsonUtils.toJson(couponProductPromoModule));
    }


    @Test
    public void testQueryShopCartPromo() {
        IMobileContext iMobileContext = buildContext();
        PromotionRequestContext context = buildCartPromotionRequestContext();
        CouponActivityContext couponActivityContext = queryService.queryShopPromotions(context, iMobileContext);
        System.out.println(couponActivityContext);
        Assert.assertNotNull(couponActivityContext);
        List<ShopCartBrandProduct> shopCartBrandProducts = Lists.newArrayList();
        ShopCartBrandProduct shopCartBrandProduct = new ShopCartBrandProduct();
        ShopCartProduct shopCartProduct = new ShopCartProduct();
        shopCartProduct.setProductid(404581734);
        shopCartProduct.setProducttype(1);
        shopCartBrandProduct.setBrandid("1234");
        shopCartBrandProduct.setShopcartproducts(Lists.newArrayList(shopCartProduct));
        shopCartBrandProducts.add(shopCartBrandProduct);
        ShopCartPromoInfo shopCartPromoInfo = ShopCartPromoInfoMapper.buildShopCartPromoInfo(couponActivityContext, shopCartBrandProducts);
        System.out.println(shopCartPromoInfo);
    }

    @Test
    public void testQueryShopCartPromo1() {
        IMobileContext iMobileContext = buildContext();
        PromotionRequestContext context = buildDealCartPromotionRequestContext();
        CouponActivityContext couponActivityContext = queryService.queryShopPromotions(context, iMobileContext);
        System.out.println(couponActivityContext);
        Assert.assertNotNull(couponActivityContext);
        List<ShopCartBrandProduct> shopCartBrandProducts = Lists.newArrayList();
        ShopCartBrandProduct shopCartBrandProduct = new ShopCartBrandProduct();
        ShopCartProduct shopCartProduct = new ShopCartProduct();
        shopCartProduct.setProductid(417360660);
        shopCartProduct.setProducttype(0);
        ShopCartProduct shopCartProduct1 = new ShopCartProduct();
        shopCartProduct1.setProductid(409406106);
        shopCartProduct1.setProducttype(0);
        shopCartBrandProduct.setBrandid("1234");
        shopCartBrandProduct.setShopcartproducts(Lists.newArrayList(shopCartProduct, shopCartProduct1));
        shopCartBrandProducts.add(shopCartBrandProduct);
        ShopCartPromoDisplayInfo shopCartPromoInfo = ShopCartPromoInfoMapper.buildShopCartPromoDisplayInfo(couponActivityContext, shopCartBrandProducts);
        System.out.println(shopCartPromoInfo);
    }

    @Test
    public void testQueryFinancialPromo() {
        Tracer.setSwimlane("1944-zidub");
        IMobileContext iMobileContext = buildContext();
        PromotionRequestContext context = buildFinancialPromoContext();
        CouponActivityContext couponActivityContext = queryService.queryShopPromotions(context, iMobileContext);
        System.out.println(couponActivityContext);
        Assert.assertNotNull(couponActivityContext);
        CouponProductPromoModule couponProductPromoModule = dealPromoInfoMapper.buildCouponProductPromoModule(couponActivityContext, iMobileContext);
        System.out.println(couponProductPromoModule);
    }

    private PromotionRequestContext buildFinancialPromoContext() {
        PromotionRequestContext context = new PromotionRequestContext();
        context.setUserType(User.MT);
        context.setMtUserId(5058625494l);
        context.setCouponPlatform(MtCouponPlatform.APP.getCode());
        context.setPayPlatform(CouponPayPlatform.mt_iphone_native.getCode());
        context.setSkuIds(Lists.newArrayList(404581734));
        context.setNeedFinalcialCouponPromotion(true);
        context.setDpid("1231313131231");
        return context;
    }


    @Test
    public void testQueryFullDiscountPromo1() {
        Tracer.setSwimlane("36864-seqpa");
        IMobileContext iMobileContext = buildContext();
        PromotionRequestContext context = buildMtPromotionRequestContext(false, 415172621, false, false);
        CouponActivityContext couponActivityContext = queryService.queryShopPromotions(context, iMobileContext);
        System.out.println(couponActivityContext);
        Assert.assertNotNull(couponActivityContext);
        CouponProductPromoModule couponProductPromoModule = dealPromoInfoMapper.buildCouponProductPromoModule(couponActivityContext, iMobileContext);
        System.out.println(JsonUtils.toJson(couponProductPromoModule));
    }

    @Test
    public void testQueryFullDiscountPromo2() {
        Tracer.setSwimlane("36864-seqpa");
        IMobileContext iMobileContext = buildContext();
        PromotionRequestContext context = buildMtPromotionRequestContext(false, 415172621, true, false);
        CouponActivityContext couponActivityContext = queryService.queryShopPromotions(context, iMobileContext);
        System.out.println(couponActivityContext);
        Assert.assertNotNull(couponActivityContext);
        CouponProductPromoModule couponProductPromoModule = dealPromoInfoMapper.buildCouponProductPromoModule(couponActivityContext, iMobileContext);
        System.out.println(JsonUtils.toJson(couponProductPromoModule));
    }
    @Test
    public void testQueryFullDiscountPromo3() {
        Tracer.setSwimlane("36864-seqpa");
        IMobileContext iMobileContext = buildContext();
        PromotionRequestContext context = buildMtPromotionRequestContext(true, 415172621, false, false);
        CouponActivityContext couponActivityContext = queryService.queryShopPromotions(context, iMobileContext);
        System.out.println(couponActivityContext);
        Assert.assertNotNull(couponActivityContext);
        CouponProductPromoModule couponProductPromoModule = dealPromoInfoMapper.buildCouponProductPromoModule(couponActivityContext, iMobileContext);
        System.out.println(JsonUtils.toJson(couponProductPromoModule));
    }

    @Test
    public void testQueryFullDiscountPromo4() {
        Tracer.setSwimlane("36864-seqpa");
        IMobileContext iMobileContext = buildContext();
        PromotionRequestContext context = buildMtPromotionRequestContext(true, 415172621, true, false);
        CouponActivityContext couponActivityContext = queryService.queryShopPromotions(context, iMobileContext);
        System.out.println(couponActivityContext);
        Assert.assertNotNull(couponActivityContext);
        CouponProductPromoModule couponProductPromoModule = dealPromoInfoMapper.buildCouponProductPromoModule(couponActivityContext, iMobileContext);
        System.out.println(JsonUtils.toJson(couponProductPromoModule));
    }

    @Test
    public void testQueryFullDiscountPromo5() {
        Tracer.setSwimlane("36864-seqpa");
        IMobileContext iMobileContext = buildContext();
        PromotionRequestContext context = buildMtPromotionRequestContext(false, 415172621, false, false);
        CouponActivityContext couponActivityContext = queryService.queryShopPromotions(context, iMobileContext);
        System.out.println(couponActivityContext);
        Assert.assertNotNull(couponActivityContext);
        if (couponActivityContext.getPromotionDTOResult() != null) {
             couponActivityContext.getPromotionDTOResult().setDiscountProvider(DiscountProviderEnum.COST_EFFECTIVE.getCode());
        }
        CouponProductPromoModule couponProductPromoModule = dealPromoInfoMapper.buildCouponProductPromoModule(couponActivityContext, iMobileContext);
        System.out.println(JsonUtils.toJson(couponProductPromoModule));
    }

    @Test
    public void testIssueCouponComponActionTest() {
        IMobileContext iMobileContext = buildContext();
        PromotionRequestContext context = buildIssueCouponComponRequestContext(410277758L, true);
        CouponActivityContext couponActivityContext = queryService.queryShopPromotions(context, iMobileContext);
        System.out.println(couponActivityContext);
        Assert.assertNotNull(couponActivityContext);
        IssueCouponComponentDTO issueCouponComponentDTO = unifiedIssueCouponBiz.toIssueCouponComponentResponse(couponActivityContext, context, iMobileContext);
        System.out.println(JsonUtils.toJson(issueCouponComponentDTO));
    }

    @Test
    public void testbuildShopResourceSubTitleImgResult() {
        IMobileContext iMobileContext = buildContext();
        ShopResourcePromotionDO shopResourcePromotionDO = new ShopResourcePromotionDO();
        shopResourcePromotionDO.setActivityId(1L);
        shopResourcePromotionDO.setResourceLocationId(1L);
        shopResourcePromotionDO.setFlowId(1L);
        shopResourcePromotionDO.setRowKey("1");
        Map<String, String> staticMaterialContext = Maps.newHashMap();
        staticMaterialContext.put(CommonConstants.SUB_TITLE_IMG, "testImg");
        shopResourcePromotionDO.setStaticMaterialContext(staticMaterialContext);
        ResourceMaterialsDO resourceMaterialsDO = new ResourceMaterialsDO();
        CouponDetailDTO couponDetailDTO = new CouponDetailDTO();
        couponDetailDTO.setAmountPrice("50");
        couponDetailDTO.setLimitPrice("100");
        resourceMaterialsDO.setCouponDetailDTOS(Lists.newArrayList(couponDetailDTO));
        resourceMaterialsDO.setMaterialId("11");
        //resourceMaterialsDO.setMaterialType(materialsDTO.getMaterialType());
        //resourceMaterialsDO.setPlatform(materialsDTO.getPlatform());
        //resourceMaterialsDO.setRecallType(materialsDTO.getRecallType());
        resourceMaterialsDO.setPrizeCouponAmount("50");
        shopResourcePromotionDO.setResourceMaterialsDOS(Lists.newArrayList(resourceMaterialsDO));
        shopResourcePromotionDO.setDrawStatus(CommonConstants.DRAWED);
        CouponActivityContext couponActivityContext = new CouponActivityContext();
        couponActivityContext.setShopResourcePromotionDO(shopResourcePromotionDO);
        PromotionRequestContext promotionRequestContext = new PromotionRequestContext();
        IssueCouponComponentDTO issueCouponComponentDTO = unifiedIssueCouponBiz.toIssueCouponComponentResponse(couponActivityContext, promotionRequestContext, iMobileContext);
        Assert.assertTrue(issueCouponComponentDTO.getCouponViewType().getSimpleCouponListInfo().getCouponSimpleList().get(0).getSubTitleImg().equals("testImg"));
    }

    @Test
    public void testCouponAggrAndMMCIconRename() {
        IMobileContext iMobileContext = buildContext();
        PromotionRequestContext context = new PromotionRequestContext();
        context.setUserType(User.MT);
        context.setMtUserId(5135630894L);
        context.setQueryShopCouponType(QueryShopCouponTypeEnum.BY_SHOP.getCode());
        context.setMtShopIdL(307547973L);
        CouponActivityContext couponActivityContext = queryService.queryShopPromotions(context, iMobileContext);
        System.out.println(couponActivityContext);
        Assert.assertNotNull(couponActivityContext);
        IssueCouponComponentDTO issueCouponComponentResp = unifiedIssueCouponBiz.toIssueCouponComponentResponse(couponActivityContext, context, iMobileContext);
        Assert.assertNotNull(issueCouponComponentResp);
    }

    private PromotionRequestContext buildIssueCouponComponRequestContext(long dpShopId, boolean needShopResourcesPromotion) {
        PromotionRequestContext context = new PromotionRequestContext();
        context.setUserType(User.DP);
        context.setDpUserId(37750678);
        context.setCityId(10);
        context.setLongitude(0.0);
        context.setLatitude(0.0);
        context.setQueryShopCouponType(QueryShopCouponTypeEnum.BY_SHOP.getCode());
        context.setDpShopIdL(dpShopId);
        context.setNeedShopResourcesPromotion(needShopResourcesPromotion);
        return context;
    }

    private IMobileContext buildContext() {
        return new IMobileContext() {
            @Override
            public Set<String> getParameterKeys() {
                return null;
            }

            @Override
            public String getParameter(String paramName) {
                return null;
            }

            @Override
            public MobileHeader getHeader() {
                MobileHeader header = new MobileHeader();
                header.setDpid("112233");
                return header;
            }

            @Override
            public String getUserIp() {
                return null;
            }

            @Override
            public ClientType getClient() {
                return null;
            }

            @Override
            public String getVersion() {
                return "12.7.200";
            }

            @Override
            public String getSource() {
                return null;
            }

            @Override
            public String getCellPhoneType() {
                return null;
            }

            @Override
            public String getUserAgent() {
                return null;
            }

            @Override
            public long getUserId() {
                return 0;
            }

            @Override
            public Map<String, FileItem> getMultiParamMap() {
                return null;
            }

            @Override
            public String getOs() {
                return null;
            }

            @Override
            public String getProtocolVersion() {
                return null;
            }

            @Override
            public boolean isPost() {
                return false;
            }

            @Override
            public ClientConfig getConfig() {
                return null;
            }

            @Override
            public String getRequestId() {
                return null;
            }

            @Override
            public String getRequestIdReffer() {
                return null;
            }

            @Override
            public String getActionKey() {
                return null;
            }

            @Override
            public HttpServletRequest getRequest() {
                return new HttpServletRequest() {
                    @Override
                    public String getAuthType() {
                        return null;
                    }

                    @Override
                    public Cookie[] getCookies() {
                        return new Cookie[0];
                    }

                    @Override
                    public long getDateHeader(String s) {
                        return 0;
                    }

                    @Override
                    public String getHeader(String s) {
                        return null;
                    }

                    @Override
                    public Enumeration getHeaders(String s) {
                        return null;
                    }

                    @Override
                    public Enumeration getHeaderNames() {
                        return null;
                    }

                    @Override
                    public int getIntHeader(String s) {
                        return 0;
                    }

                    @Override
                    public String getMethod() {
                        return null;
                    }

                    @Override
                    public String getPathInfo() {
                        return null;
                    }

                    @Override
                    public String getPathTranslated() {
                        return null;
                    }

                    @Override
                    public String getContextPath() {
                        return null;
                    }

                    @Override
                    public String getQueryString() {
                        return null;
                    }

                    @Override
                    public String getRemoteUser() {
                        return null;
                    }

                    @Override
                    public boolean isUserInRole(String s) {
                        return false;
                    }

                    @Override
                    public Principal getUserPrincipal() {
                        return null;
                    }

                    @Override
                    public String getRequestedSessionId() {
                        return null;
                    }

                    @Override
                    public String getRequestURI() {
                        return null;
                    }

                    @Override
                    public StringBuffer getRequestURL() {
                        return null;
                    }

                    @Override
                    public String getServletPath() {
                        return null;
                    }

                    @Override
                    public HttpSession getSession(boolean b) {
                        return null;
                    }

                    @Override
                    public HttpSession getSession() {
                        return null;
                    }

                    @Override
                    public String changeSessionId() {
                        return null;
                    }

                    @Override
                    public boolean isRequestedSessionIdValid() {
                        return false;
                    }

                    @Override
                    public boolean isRequestedSessionIdFromCookie() {
                        return false;
                    }

                    @Override
                    public boolean isRequestedSessionIdFromURL() {
                        return false;
                    }

                    @Override
                    public boolean isRequestedSessionIdFromUrl() {
                        return false;
                    }

                    @Override
                    public boolean authenticate(HttpServletResponse httpServletResponse) throws IOException, ServletException {
                        return false;
                    }

                    @Override
                    public void login(String s, String s1) throws ServletException {

                    }

                    @Override
                    public void logout() throws ServletException {

                    }

                    @Override
                    public Collection<Part> getParts() throws IOException, ServletException {
                        return null;
                    }

                    @Override
                    public Part getPart(String s) throws IOException, ServletException {
                        return null;
                    }

                    @Override
                    public <T extends HttpUpgradeHandler> T upgrade(Class<T> aClass) throws IOException, ServletException {
                        return null;
                    }

                    @Override
                    public Object getAttribute(String s) {
                        return null;
                    }

                    @Override
                    public Enumeration getAttributeNames() {
                        return null;
                    }

                    @Override
                    public String getCharacterEncoding() {
                        return null;
                    }

                    @Override
                    public void setCharacterEncoding(String s) throws UnsupportedEncodingException {

                    }

                    @Override
                    public int getContentLength() {
                        return 0;
                    }

                    @Override
                    public long getContentLengthLong() {
                        return 0;
                    }

                    @Override
                    public String getContentType() {
                        return null;
                    }

                    @Override
                    public ServletInputStream getInputStream() throws IOException {
                        return null;
                    }

                    @Override
                    public String getParameter(String s) {
                        return null;
                    }

                    @Override
                    public Enumeration getParameterNames() {
                        return null;
                    }

                    @Override
                    public String[] getParameterValues(String s) {
                        return new String[0];
                    }

                    @Override
                    public Map getParameterMap() {
                        return null;
                    }

                    @Override
                    public String getProtocol() {
                        return null;
                    }

                    @Override
                    public String getScheme() {
                        return null;
                    }

                    @Override
                    public String getServerName() {
                        return null;
                    }

                    @Override
                    public int getServerPort() {
                        return 0;
                    }

                    @Override
                    public BufferedReader getReader() throws IOException {
                        return null;
                    }

                    @Override
                    public String getRemoteAddr() {
                        return null;
                    }

                    @Override
                    public String getRemoteHost() {
                        return null;
                    }

                    @Override
                    public void setAttribute(String s, Object o) {

                    }

                    @Override
                    public void removeAttribute(String s) {

                    }

                    @Override
                    public Locale getLocale() {
                        return null;
                    }

                    @Override
                    public Enumeration getLocales() {
                        return null;
                    }

                    @Override
                    public boolean isSecure() {
                        return false;
                    }

                    @Override
                    public RequestDispatcher getRequestDispatcher(String s) {
                        return null;
                    }

                    @Override
                    public String getRealPath(String s) {
                        return null;
                    }

                    @Override
                    public int getRemotePort() {
                        return 0;
                    }

                    @Override
                    public String getLocalName() {
                        return null;
                    }

                    @Override
                    public String getLocalAddr() {
                        return null;
                    }

                    @Override
                    public int getLocalPort() {
                        return 0;
                    }

                    @Override
                    public ServletContext getServletContext() {
                        return null;
                    }

                    @Override
                    public AsyncContext startAsync() throws IllegalStateException {
                        return null;
                    }

                    @Override
                    public AsyncContext startAsync(ServletRequest servletRequest, ServletResponse servletResponse) throws IllegalStateException {
                        return null;
                    }

                    @Override
                    public boolean isAsyncStarted() {
                        return false;
                    }

                    @Override
                    public boolean isAsyncSupported() {
                        return false;
                    }

                    @Override
                    public AsyncContext getAsyncContext() {
                        return null;
                    }

                    @Override
                    public DispatcherType getDispatcherType() {
                        return null;
                    }
                };
            }

            @Override
            public HttpServletResponse getResponse() {
                return null;
            }

            @Override
            public byte[] getPicFile() {
                return new byte[0];
            }

            @Override
            public CacheContext getCacheContext() {
                return null;
            }

            @Override
            public AppType getAppType() {
                return null;
            }

            @Override
            public boolean isMtClient() {
                return false;
            }

            @Override
            public DeviceType getDeviceType() {
                return null;
            }

            @Override
            public UserStatusResult getUserStatus() {
                return null;
            }

            @Override
            public void setUserStatus(UserStatusResult userStatus) {

            }

            @Override
            public CrawlMark getCrawlMark() {
                return null;
            }

            @Override
            public int getAppId() {
                return 0;
            }
        };

    }

    public static PromotionRequestContext buildMtPromotionRequestContext(boolean newBeautyType, int dealGroupId, boolean needMemberPromotion, boolean needDiscountCoupon) {
        PromotionRequestContext context = new PromotionRequestContext();
        context.setUserType(User.MT);
        context.setMtUserId(37376244);
        context.setMtShopIdL(3326527);
        context.setQueryShopCouponType(1);
        context.setCityId(1);
        context.setMtDealGroupId(dealGroupId);
        context.setPayPlatform(PayPlatform.mt_android_native.getCode());

        context.setNeedReductionPromotion(true);
        context.setNeedResourcesPromotion(true);
        context.setNeedReturnPromotion(true);
        context.setNeedMemberPromotion(needMemberPromotion);
        context.setBeautyDealNewType(newBeautyType);
        context.setPageSource("test");
        context.setNeedDiscountCoupon(needDiscountCoupon);
        return context;
    }

    public static PromotionRequestContext buildPromotionRequestContext(boolean newBeautyType, int dealGroupId, boolean needMemberPromotion, boolean needDiscountCoupon) {
        PromotionRequestContext context = new PromotionRequestContext();
        context.setUserType(User.DP);
        context.setDpShopIdL(3326527);
        context.setDpUserId(9000000000015919591L);
        context.setQueryShopCouponType(1);
        context.setCityId(1);
        context.setDpDealGroupId(dealGroupId);

        context.setNeedReductionPromotion(true);
        context.setNeedResourcesPromotion(true);
        context.setNeedReturnPromotion(true);
        context.setNeedMemberPromotion(needMemberPromotion);
        context.setBeautyDealNewType(newBeautyType);
        context.setPageSource("test");
        context.setNeedDiscountCoupon(needDiscountCoupon);
        return context;
    }


    public static PromotionRequestContext buildPromotionRequestContext(boolean newBeautyType, int dealGroupId, boolean needMemberPromotion) {
        return buildPromotionRequestContext(newBeautyType, dealGroupId, needMemberPromotion, true);
    }

    public static PromotionRequestContext buildDpSkuPromotionRequestContext( ) {
        PromotionRequestContext context = new PromotionRequestContext();
        context.setUserType(User.DP);
        context.setDpShopIdL(1798321);
        context.setDpUserId(12345L);
        context.setQueryShopCouponType(2);
        context.setCityId(1);
        context.setSkuId(3922325);
        context.setNeedReductionPromotion(true);
        context.setPayPlatform(PayPlatform.dp_android_native.getCode());
        return context;
    }

    private PromotionRequestContext buildSPUPromotionRequestContext() {
        PromotionRequestContext context = new PromotionRequestContext();
        context.setUserType(User.MT);
        context.setMtUserId(37750678);
        context.setQueryShopCouponType(6);
        context.setSpuGroupId(684011566);
        return context;
    }

    private PromotionRequestContext buildCartPromotionRequestContext() {
        PromotionRequestContext context = new PromotionRequestContext();
        context.setUserType(User.MT);
        context.setMtUserId(37750678);
        context.setQueryShopCouponType(4);
        context.setSkuIds(Lists.newArrayList(404581734));
        context.setNeedUserPromotion(true);
        return context;
    }

    private PromotionRequestContext buildDealCartPromotionRequestContext() {
        PromotionRequestContext context = new PromotionRequestContext();
        context.setUserType(User.MT);
        context.setMtUserId(37750678);
        context.setQueryShopCouponType(QueryShopCouponTypeEnum.BY_DEALS.getCode());
        context.setMtDealGroupIds(Lists.newArrayList(417360660,409406106));
        context.setNeedUserPromotion(true);
        return context;
    }

    @Test
    @Ignore
    public void testIssueActivity(){
        CouponActivityContext context = new CouponActivityContext();
        context.setDpid("0000000000000E5B8552D0CFE4911990E13D75A3FF925A156202862888980881");
        context.setCityId(1);
        context.setDpShopIdL(1798321);
        context.setMtShopIdL(1798321);
        context.setDpDealGroupId(405038042);
        context.setMtDealGroupId(405038042);
        context.setQueryShopCouponType(QueryShopCouponTypeEnum.BY_DEAL.getCode());
        context.setUserType(User.MT);
        context.setUserId(1234L);
        context.setMtUserId(1234L);

        tgcCouponProcessor.prePare(context, null);
        tgcCouponProcessor.loadPromo(context);
        List<IssueCouponActivity> issueCouponActivities = context.getIssueCouponActivities();
        System.out.println(issueCouponActivities);

        Assert.assertTrue(CollectionUtils.isNotEmpty(issueCouponActivities));

        IssueCouponActivity activity = null;
        for (IssueCouponActivity issueCouponActivity : issueCouponActivities) {
            if (CollectionUtils.isNotEmpty(issueCouponActivity.getIssuedUnUseUnExpireCoupon())) {
                activity = issueCouponActivity;
            }
        }
        Assert.assertNotNull(activity);

        Assert.assertTrue(IssueCouponTypeUtils.isIssued(activity));
        Assert.assertTrue(!IssueCouponTypeUtils.allowGetCoupon(activity));
        Assert.assertTrue(IssueCouponTypeUtils.canUse(activity));
        Assert.assertTrue(!IssueCouponTypeUtils.isUsed(activity));
        Assert.assertNotNull(IssueCouponTypeUtils.getIssuedUnifiedCouponDto(activity));
        Assert.assertTrue(!IssueCouponTypeUtils.allowGetCoupon(activity.getIssuedCoupon()));

        CouponProductPromoModule couponProductPromoModule = dealPromoInfoMapper.buildCouponProductPromoModule(context, null);
        System.out.println(JsonUtils.toJson(couponProductPromoModule));
        Assert.assertTrue(couponProductPromoModule != null && couponProductPromoModule.getDetailPromoInfo() != null);

        // 凑覆盖率的
        Collections.sort(issueCouponActivities, new IssueCouponActivityComparator());
        Assert.assertNotNull(IssueCouponTypeUtils.getIssueCouponOption(activity));
        Assert.assertNotNull(IssueCouponTypeUtils.getBeautyIssueCouponOption(activity));
        CouponDetailInfo detailInfo = ShopCartPromoInfoHelper.genCouponDetailItem(activity);
        System.out.println(JsonUtils.toJson(detailInfo));
        System.out.println(IssueCouponUtils.formatSubtitle(activity));
        UnifiedIssueCouponComponentDo unifiedIssueCouponComponentDo = unifiedIssueCouponBiz.toUnifiedIssueComponent(issueCouponActivities, Lists.newArrayList());
        System.out.println(JsonUtils.toJson(unifiedIssueCouponComponentDo));
    }

    @Test
    @Ignore
    public void testIssueActivityV2(){
        CouponActivityContext context = new CouponActivityContext();
        context.setDpid("0000000000000E5B8552D0CFE4911990E13D75A3FF925A156202862888980881");
        context.setCityId(1);
        context.setDpShopIdL(1798321);
        context.setMtShopIdL(1798321);
        context.setDpDealGroupId(405038042);
        context.setMtDealGroupId(405038042);
        context.setQueryShopCouponType(QueryShopCouponTypeEnum.BY_DEAL.getCode());
        context.setUserType(User.MT);

        tgcCouponProcessor.prePare(context, null);
        tgcCouponProcessor.loadPromo(context);
        List<IssueCouponActivity> issueCouponActivities = context.getIssueCouponActivities();
        System.out.println(JsonUtils.toJson(issueCouponActivities));
        Assert.assertTrue(CollectionUtils.isNotEmpty(issueCouponActivities));

        IssueCouponActivity activity = issueCouponActivities.get(0);
        Assert.assertTrue(!IssueCouponTypeUtils.isIssued(activity));
        Assert.assertTrue(IssueCouponTypeUtils.allowGetCoupon(activity));
        Assert.assertTrue(!IssueCouponTypeUtils.canUse(activity));
        Assert.assertNull(IssueCouponTypeUtils.getIssuedUnifiedCouponDto(activity));
        Assert.assertTrue(IssueCouponTypeUtils.allowGetCoupon(activity.getIssuedCoupon()));

        System.out.println(IssueCouponUtils.formatSubtitle(activity));

        CouponProductPromoModule couponProductPromoModule = dealPromoInfoMapper.buildCouponProductPromoModule(context, null);
        System.out.println(JsonUtils.toJson(couponProductPromoModule));
    }


    @Test
    public void testDealNoPriceLimitPromoQuery() {
        CouponActivityContext context = new CouponActivityContext();
        context.setDpid("0000000000000E5B8552D0CFE4911990E13D75A3FF925A156202862888980881");
        context.setCityId(1);
        context.setDpShopIdL(1798321);
        context.setMtShopIdL(1798321);
        context.setDpDealGroupId(405991328);
        context.setMtDealGroupId(405991328);
        context.setQueryShopCouponType(QueryShopCouponTypeEnum.BY_DEAL.getCode());
        context.setUserType(User.MT);
        context.setUserId(123);
        context.setMtUserId(123);
        context.setDpUserId(123);
        context.setPayPlatform(MtCouponPlatform.APP.getCode());


        tgcCouponProcessor.prePare(context, null);

        try {
            Thread.sleep(3000);
        } catch (InterruptedException e) {
            e.printStackTrace();
        }
        tgcCouponProcessor.loadPromo(context);

        CouponProductPromoModule couponProductPromoModule = dealPromoInfoMapper.buildCouponProductPromoModule(context, null);
        System.out.println(JsonUtils.toJson(couponProductPromoModule));
        Assert.assertTrue(couponProductPromoModule != null);

        context.setPayPlatform(MtCouponPlatform.WEIXIN_CX.getCode());
        couponProductPromoModule = dealPromoInfoMapper.buildCouponProductPromoModule(context, null);
        System.out.println(JsonUtils.toJson(couponProductPromoModule));
        Assert.assertTrue(couponProductPromoModule != null);
    }

    @Test
    public void testFastJson() {
        Assert.assertTrue(com.alibaba.fastjson.parser.ParserConfig.SAFE_MODE);
    }

    @Test
    public void testFilter() {

        CouponActivityContext context = new CouponActivityContext();
        context.setDpid("0000000000000E5B8552D0CFE4911990E13D75A3FF925A156202862888980881");
        context.setCityId(1);
        context.setDpShopIdL(17983222);
        context.setMtShopIdL(17983222);
        context.setDpDealGroupId(44210832);
        context.setMtDealGroupId(80607126);
        context.setNeedReductionPromotion(true);
        context.setQueryShopCouponType(QueryShopCouponTypeEnum.BY_DEAL.getCode());
        context.setUserType(User.MT);
        context.setUserId(12345L);

        boolean result = proxyCouponProcessor.filterActivity("123", context);
        Assert.assertTrue(result);

        context.setMtShopIdL(17983223);
        result = proxyCouponProcessor.filterActivity("123", context);
        Assert.assertTrue(!result);
    }


    @Test
    @Ignore
    public void skuProxyTest() {
        CouponActivityContext context = new CouponActivityContext();
        context.setDpid("0000000000000E5B8552D0CFE4911990E13D75A3FF925A156202862888980881");
        context.setCityId(1);
        context.setDpShopIdL(1798321);
        context.setMtShopIdL(1798321);
        context.setSkuId(401958563);
        context.setQueryShopCouponType(QueryShopCouponTypeEnum.BY_SKU.getCode());
        context.setUserType(User.MT);
        context.setUserId(123);
        context.setMtUserId(123);
        context.setDpUserId(123);
        context.setProductDetailDO(productService.loadProductDetail(401958563));
        context.setPayPlatform(MtCouponPlatform.APP.getCode());


        proxyCouponProcessor.prePare(context, null);
        proxyCouponProcessor.loadPromo(context);

        PromotionDTOResult result = context.getPromotionDTOResult();
        System.out.println(JsonUtils.toJson(result));
        Assert.assertTrue(result != null);
    }

    @Test
    public void tgcBatchTest() {
        CouponActivityContext context = new CouponActivityContext();
        context.setSkuIds(Lists.newArrayList(404917783,423341638));
        context.setQueryShopCouponType(QueryShopCouponTypeEnum.BY_SKUS.getCode());
        context.setUserType(User.MT);
        context.setMtUserId(5041099455L);
        context.setPayPlatform(MtCouponPlatform.APP.getCode());


        tgcBatchCouponProcessor.prePare(context, null);
        tgcBatchCouponProcessor.loadPromo(context);

        Pair<Map<Integer, Map<String, List<Integer>>>, List<IssueCouponActivity>> batchIssueCouponActivitiesPair = context.getBatchIssueCouponActivitiesPair();
        System.out.println(JsonUtils.toJson(batchIssueCouponActivitiesPair));
        Assert.assertNotNull(batchIssueCouponActivitiesPair);
    }

    @Test
    public void tgcProcessTest() {
        CouponActivityContext context = new CouponActivityContext();
        context.setDpDealGroupId(401227110);
        context.setMtDealGroupId(401227110);
        context.setQueryShopCouponType(QueryShopCouponTypeEnum.BY_DEAL.getCode());
//        context.setMtShopId(1799914);
//        context.setDpShopId(1799914);
//        context.setQueryShopCouponType(QueryShopCouponTypeEnum.BY_DEAL.getCode());
        context.setUserType(User.MT);
//        context.setMtUserId(5041099455L);
        context.setPayPlatform(MtCouponPlatform.APP.getCode());


        tgcCouponProcessor.prePare(context, null);
        try {
            Thread.sleep(3000);
        } catch (InterruptedException e) {
            e.printStackTrace();
        }
        tgcCouponProcessor.loadPromo(context);

        List<IssueCouponActivity> issueCouponActivities = context.getIssueCouponActivities();
        System.out.println(JsonUtils.toJson(issueCouponActivities));
        Assert.assertNotNull(issueCouponActivities);
    }

    @Test
    public void activitySortTest() {
        IssueCouponActivity dzTuangouAcitvity = buildIssueCouponActivity(Lists.newArrayList(CouponBusiness.DZ_TUANGOU.getCode()));
        IssueCouponActivity tuangouAcitvity = buildIssueCouponActivity(Lists.newArrayList(CouponBusiness.TUANGOU.getCode()));
        IssueCouponActivity ktvAcitvity = buildIssueCouponActivity(Lists.newArrayList(CouponBusiness.KTV.getCode()));
        IssueCouponActivity eduAcitvity = buildIssueCouponActivity(Lists.newArrayList(CouponBusiness.EDU.getCode()));
        IssueCouponActivity shanhuiAcitvity = buildIssueCouponActivity(Lists.newArrayList(CouponBusiness.MO2O2PAY.getCode()));
        List<IssueCouponActivity> issueCouponActivities = Lists.newArrayList(shanhuiAcitvity, eduAcitvity, ktvAcitvity, tuangouAcitvity, dzTuangouAcitvity);
        Collections.sort(issueCouponActivities, new IssueCouponActivityComparator());
        Assert.assertTrue(TgCouponBusinessUtils.isDzTuangouCouponBusiness(issueCouponActivities.get(0).getCouponGroup().getProductCodeList().get(0)));
    }

    @Test
    public void shareCouponTest() {
        Tracer.setSwimlane("3792-tsiyi");
        CouponActivityContext context = new CouponActivityContext();
        context.setNeedShareCouponPromotion(true);
        context.setUserType(User.DP);
        context.setDpUserId(9000000000019761026L);
        context.setDpShopIdL(3326527L);
        context.setQueryShopCouponType(QueryShopCouponTypeEnum.BY_SHOP.getCode());
        context.setPayPlatform(MtCouponPlatform.APP.getCode());


        shareCouponProcessor.prePare(context, null);
        try {
            Thread.sleep(3000);
        } catch (InterruptedException e) {
            e.printStackTrace();
        }
        shareCouponProcessor.loadPromo(context);

        List<MerchantShareCouponExposeDTO> shareCouponExposeDTOS = context.getCouponExposeDTOS();
        System.out.println(JsonUtils.toJson(shareCouponExposeDTOS));
        Assert.assertNotNull(shareCouponExposeDTOS);
    }

    @Test
    public void shareCouponMtTest() {
        Tracer.setSwimlane("3792-tsiyi");
        CouponActivityContext context = new CouponActivityContext();
        context.setNeedShareCouponPromotion(true);

        context.setUserType(User.MT);
        context.setMtUserId(5035886370L);
        context.setMtShopIdL(78141377L);

        context.setQueryShopCouponType(QueryShopCouponTypeEnum.BY_SHOP.getCode());
        context.setPayPlatform(MtCouponPlatform.APP.getCode());


        shareCouponProcessor.prePare(context, null);
        try {
            Thread.sleep(3000);
        } catch (InterruptedException e) {
            e.printStackTrace();
        }
        shareCouponProcessor.loadPromo(context);

        List<MerchantShareCouponExposeDTO> shareCouponExposeDTOS = context.getCouponExposeDTOS();
        System.out.println(JsonUtils.toJson(shareCouponExposeDTOS));
        Assert.assertNotNull(shareCouponExposeDTOS);
    }

    private IssueCouponActivity buildIssueCouponActivity(List<Integer> productCodes) {
        IssueCouponActivity activity1 = new IssueCouponActivity(RandomUtils.nextInt(), 1, 1);
        UnifiedCouponGroupDTO couponGroupDTO = new UnifiedCouponGroupDTO();
        couponGroupDTO.setProductCodeList(productCodes);
        couponGroupDTO.setDiscountAmount(new BigDecimal(10));
        activity1.setCouponGroup(couponGroupDTO);
        return activity1;
    }


}
