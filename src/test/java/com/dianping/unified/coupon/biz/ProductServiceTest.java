package com.dianping.unified.coupon.biz;

import com.alibaba.fastjson.JSONObject;
import com.dianping.pay.api.beans.DealDetailDO;
import com.dianping.pay.api.service.impl.ProductService;
import com.meituan.mtrace.Tracer;
import org.apache.commons.lang3.StringUtils;
import org.junit.Assert;
import org.junit.Test;

import javax.annotation.Resource;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2022/10/19
 */
public class ProductServiceTest extends TestBase {

    @Resource
    private ProductService productService;

    @Test
    public void testDealGroupCommission() {
        Tracer.setSwimlane("qinhuayu-egktm");
        BigDecimal commissionRate = productService.getDealGroupCommissionRate(406809457, true);
        BigDecimal commissionRate1 = productService.getDealGroupCommissionRate(406809457, false);
        Assert.assertNotNull(commissionRate);
    }

    @Test
    public void testSkuCommission() {
        Tracer.setSwimlane("qinhuayu-egktm");
        BigDecimal commissionRate = productService.getSkuCommissionRate(407698641, true);
        BigDecimal commissionRate1 = productService.getSkuCommissionRate(407698641, false);
        Assert.assertNotNull(commissionRate);
    }

    @Test
    public void testGetDealGroupDetailInfo() {
        DealDetailDO dealDetailDO = productService.getDealGroupDetailInfo(415172621);
        Assert.assertTrue("501".equals(dealDetailDO.getDealCategoryId()));
    }
}
