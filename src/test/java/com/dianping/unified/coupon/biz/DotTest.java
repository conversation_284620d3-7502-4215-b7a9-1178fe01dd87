package com.dianping.unified.coupon.biz;

import com.dianping.api.constans.CommonConstants;
import com.dianping.cat.Cat;
import com.dianping.gmkt.scene.api.delivery.enums.RecallTypeEnum;
import com.dianping.pay.api.beans.ResourceMaterialsDO;
import com.dianping.pay.api.beans.ShopResourcePromotionDO;
import com.dianping.pay.api.beans.TopAdResourcePromotionDO;
import com.dianping.pay.api.beans.VoucherDetailDO;
import com.dianping.pay.api.biz.activity.newloader.dto.CouponActivityContext;
import com.dianping.pay.api.biz.activity.newloader.dto.PromotionRequestContext;
import com.dianping.pay.api.util.DotUtils;
import org.junit.Assert;
import org.junit.Test;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class DotTest extends TestBase {

    /**
     * 测试已领取彩虹券的场景
     */
    @Test
    public void testDotForPoiRainbowCouponReceivedCoupon() {
        ShopResourcePromotionDO shopResourcePromotionDO = new ShopResourcePromotionDO();
        List<ResourceMaterialsDO> resourceMaterialsDOS = new ArrayList<>();
        ResourceMaterialsDO resourceMaterialsDO = new ResourceMaterialsDO();
        resourceMaterialsDO.setRecallType(RecallTypeEnum.ALREADY_DRAW_COUPON.getCode());
        resourceMaterialsDOS.add(resourceMaterialsDO);
        shopResourcePromotionDO.setDrawStatus(CommonConstants.DRAWED);
        shopResourcePromotionDO.setResourceMaterialsDOS(resourceMaterialsDOS);
        DotUtils.dotForPoiRainbowCoupon(shopResourcePromotionDO);
    }

    /**
     * 测试待领取彩虹券的场景
     */
    @Test
    public void testDotForPoiRainbowCouponGetCoupon() {
        ShopResourcePromotionDO shopResourcePromotionDO = new ShopResourcePromotionDO();
        List<ResourceMaterialsDO> resourceMaterialsDOS = new ArrayList<>();
        ResourceMaterialsDO resourceMaterialsDO = new ResourceMaterialsDO();
        resourceMaterialsDO.setRecallType(RecallTypeEnum.RECALL_DRAW_COUPON.getCode());
        resourceMaterialsDOS.add(resourceMaterialsDO);
        shopResourcePromotionDO.setDrawStatus(CommonConstants.CAN_DRAW);
        shopResourcePromotionDO.setResourceMaterialsDOS(resourceMaterialsDOS);
        DotUtils.dotForPoiRainbowCoupon(shopResourcePromotionDO);
    }

    /**
     * 测试新塞彩虹券的场景
     */
    @Test
    public void testDotForPoiRainbowCouponNewPushCoupon() {
        ShopResourcePromotionDO shopResourcePromotionDO = new ShopResourcePromotionDO();
        List<ResourceMaterialsDO> resourceMaterialsDOS = new ArrayList<>();
        ResourceMaterialsDO resourceMaterialsDO = new ResourceMaterialsDO();
        resourceMaterialsDO.setRecallType(RecallTypeEnum.RECALL_DIRECT_DRAW_COUPON.getCode());
        resourceMaterialsDOS.add(resourceMaterialsDO);
        shopResourcePromotionDO.setDrawStatus(CommonConstants.DRAWED);
        shopResourcePromotionDO.setResourceMaterialsDOS(resourceMaterialsDOS);
        DotUtils.dotForPoiRainbowCoupon(shopResourcePromotionDO);
    }

    /**
     * 测试
     */
    @Test
    public void dotForPoiConsistentIssueCoupon() {
        CouponActivityContext couponActivityContext = new CouponActivityContext();
        TopAdResourcePromotionDO topAdResourcePromotionDO = new TopAdResourcePromotionDO();
        couponActivityContext.setTopAdResourcePromotionDO(topAdResourcePromotionDO);
        List<VoucherDetailDO> voucherDetailDOList = new ArrayList<>();
        topAdResourcePromotionDO.setVoucherDetailDOList(voucherDetailDOList);
        voucherDetailDOList.add(new VoucherDetailDO());
        voucherDetailDOList.add(new VoucherDetailDO());
        voucherDetailDOList.add(new VoucherDetailDO());

        PromotionRequestContext prContext = new PromotionRequestContext();
        prContext.setPoiPageSource(1);
        DotUtils.dotForPoiConsistentIssueCoupon(couponActivityContext, prContext);
    }

    @Test
    public void dotForPromoProcessor() {
        boolean dotResult = true;
        try {
            CouponActivityContext context = new CouponActivityContext();
            DotUtils.dotForPromoProcessor(context, "brandActivity");
            CouponActivityContext context1 = new CouponActivityContext();
            context1.setMiniProgramFlag(0);
            DotUtils.dotForPromoProcessor(context1, "platformCoupon");
            CouponActivityContext context2 = new CouponActivityContext();
            context2.setMiniProgramFlag(1);
            DotUtils.dotForPromoProcessor(context2, "magicalMemberCouponProcessor");
        } catch (Exception e) {
            dotResult = false;
        }
        Assert.assertTrue(dotResult);
    }

}
