package com.dianping.unified.coupon.biz;

import com.dianping.gmkt.coupon.common.api.enums.MtCouponPlatform;
import com.dianping.mobile.base.datatypes.enums.ClientType;
import com.dianping.mobile.base.datatypes.enums.DeviceType;
import com.dianping.mobile.framework.cache.CacheContext;
import com.dianping.mobile.framework.datatypes.AppType;
import com.dianping.mobile.framework.datatypes.IMobileContext;
import com.dianping.mobile.framework.datatypes.MobileHeader;
import com.dianping.mobile.framework.datatypes.client.ClientConfig;
import com.dianping.mobile.framework.datatypes.crawl.CrawlMark;
import com.dianping.mobile.framework.datatypes.token.UserStatusResult;
import com.dianping.pay.api.biz.activity.newloader.dto.CouponActivityContext;
import com.dianping.pay.api.biz.activity.newloader.promo.TopAdResourcePromotionProcessor;
import com.dianping.pay.common.enums.PayPlatform;
import com.dianping.pay.promo.common.enums.User;
import java.util.Map;
import java.util.Set;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import org.apache.commons.fileupload.FileItem;
import org.junit.Assert;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

public class TopAdResourcePromotionProcessorTest extends TestBase {

    @Autowired
    private TopAdResourcePromotionProcessor topAdResourcePromotionProcessor;

    @Test
    public void prepare() {
        boolean result = true;
        try {
            CouponActivityContext promoCtx = buildCouponActivityContext();
            IMobileContext iMobileContext = buildIMobileContext();
            topAdResourcePromotionProcessor.prePare(promoCtx, iMobileContext);
        } catch(Exception e) {
            result = false;
        }
        Assert.assertTrue(result);
    }

    private CouponActivityContext buildCouponActivityContext() {
        CouponActivityContext promoCtx = new CouponActivityContext();
        promoCtx.setNeedTopAdResourcePromotion(true);
        promoCtx.setUserType(User.MT);
        promoCtx.setMtUserId(29060335L);
        promoCtx.setCouponPlatform(MtCouponPlatform.APP.getCode());
        promoCtx.setPayPlatform(PayPlatform.mt_iphone_native.getCode());
        promoCtx.setCityId(1);
        promoCtx.setDpid("12345");
        promoCtx.setMtShopIdL(604557219L);
        return promoCtx;
    }

    private IMobileContext buildIMobileContext() {
        IMobileContext iMobileContext = new IMobileContext() {
            @Override public Set<String> getParameterKeys() {
                return null;
            }

            @Override public String getParameter(String s) {
                return null;
            }

            @Override public MobileHeader getHeader() {
                return null;
            }

            @Override public String getUserIp() {
                return null;
            }

            @Override public ClientType getClient() {
                return null;
            }

            @Override public String getVersion() {
                return "10.1.200";
            }

            @Override public String getSource() {
                return null;
            }

            @Override public String getCellPhoneType() {
                return null;
            }

            @Override public String getUserAgent() {
                return null;
            }

            @Override public long getUserId() {
                return 0;
            }

            @Override public Map<String, FileItem> getMultiParamMap() {
                return null;
            }

            @Override public String getOs() {
                return null;
            }

            @Override public String getProtocolVersion() {
                return null;
            }

            @Override public boolean isPost() {
                return false;
            }

            @Override public ClientConfig getConfig() {
                return null;
            }

            @Override public String getRequestId() {
                return null;
            }

            @Override public String getRequestIdReffer() {
                return null;
            }

            @Override public String getActionKey() {
                return null;
            }

            @Override public HttpServletRequest getRequest() {
                return null;
            }

            @Override public HttpServletResponse getResponse() {
                return null;
            }

            @Override public byte[] getPicFile() {
                return new byte[0];
            }

            @Override public CacheContext getCacheContext() {
                return null;
            }

            @Override public AppType getAppType() {
                return null;
            }

            @Override public boolean isMtClient() {
                return false;
            }

            @Override public DeviceType getDeviceType() {
                return null;
            }

            @Override public UserStatusResult getUserStatus() {
                return null;
            }

            @Override public void setUserStatus(UserStatusResult result) {

            }

            @Override public CrawlMark getCrawlMark() {
                return null;
            }

            @Override public int getAppId() {
                return 0;
            }
        };
        return iMobileContext;
    }
}
