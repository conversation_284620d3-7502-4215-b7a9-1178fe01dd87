package com.dianping.unified.coupon.biz;

import com.dianping.pay.api.util.HighPriceControlUtils;
import org.junit.Assert;
import org.junit.Test;

public class HighPriceControlUtilsTest {

    @Test
    public void dpDegradeTest() {
        boolean needDegrade = HighPriceControlUtils.needDegradeOfDp("medical_prepay", null);
        Assert.assertFalse(needDegrade);
        needDegrade = HighPriceControlUtils.needDegradeOfDp("medical_prepay", 3);
        Assert.assertTrue(needDegrade);
    }

    @Test
    public void mtDegradeTest() {
        boolean needDegrade = HighPriceControlUtils.needDegradeOfMt("medical_prepay", 1);
        Assert.assertFalse(needDegrade);
    }
}
