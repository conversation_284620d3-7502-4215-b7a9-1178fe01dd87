package com.dianping.unified.coupon.biz;

import com.dianping.gmkt.coupon.common.api.enums.MtCouponPlatform;
import com.dianping.mobile.framework.datatypes.MobileContext;
import com.dianping.mobile.framework.datatypes.token.UserStatusResult;
import com.dianping.pay.api.biz.activity.newloader.CouponIssueActivityQueryService;
import com.dianping.pay.api.biz.activity.newloader.dto.CouponActivityContext;
import com.dianping.pay.api.biz.activity.newloader.dto.PromotionRequestContext;
import com.dianping.pay.api.biz.activity.newloader.mapper.ProductPromoInfoMapper;
import com.dianping.pay.api.constants.ExtParamKeyConstant;
import com.dianping.pay.api.entity.issuecoupon.ProductPromoInfo;
import com.dianping.pay.common.enums.PayPlatform;
import com.dianping.pay.promo.common.enums.User;
import org.junit.Ignore;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.Assert;

import java.util.HashMap;
import java.util.Map;

public class ChannelProductPromoActionTest extends TestBase{

    @Autowired
    private CouponIssueActivityQueryService couponIssueActivityQueryService;

    @Test
    @Ignore
    public void testChannelProductPromo() {
        PromotionRequestContext context = buildNativePromotionRequestContext();
        MobileContext mobileContext = new MobileContext();
        UserStatusResult userStatusResult = new UserStatusResult();
        userStatusResult.setMtUserId(5060019351l);
        userStatusResult.setUserId(5060019351l);
        mobileContext.setUserStatus(userStatusResult);
        CouponActivityContext couponActivityContext = couponIssueActivityQueryService.queryShopPromotions(context, mobileContext);
        ProductPromoInfo productPromoInfo = ProductPromoInfoMapper.buildProductPromoInfo(couponActivityContext, mobileContext);
        Assert.notNull(productPromoInfo.getProductPromoConciseInfo());
    }

    @Test
    public void testChannelProductPromo01() {
        PromotionRequestContext context = buildPromotionRequestContext();
        MobileContext mobileContext = new MobileContext();
        UserStatusResult userStatusResult = new UserStatusResult();
        userStatusResult.setUserId(5060019351l);
        mobileContext.setUserStatus(userStatusResult);
        CouponActivityContext couponActivityContext = couponIssueActivityQueryService.queryShopPromotions(context, mobileContext);
        ProductPromoInfo productPromoInfo = ProductPromoInfoMapper.buildProductPromoInfo(couponActivityContext, mobileContext);
        Assert.notNull(productPromoInfo);
    }

    @Test
    public void testChannelProductPromo02() {
        PromotionRequestContext context = buildRequestContext();
        MobileContext mobileContext = new MobileContext();
        UserStatusResult userStatusResult = new UserStatusResult();
        userStatusResult.setUserId(5060019351l);
        mobileContext.setUserStatus(userStatusResult);
        CouponActivityContext couponActivityContext = couponIssueActivityQueryService.queryShopPromotions(context, mobileContext);
        ProductPromoInfo productPromoInfo = ProductPromoInfoMapper.buildProductPromoInfo(couponActivityContext, mobileContext);
        Assert.notNull(productPromoInfo);
    }

    public static PromotionRequestContext buildNativePromotionRequestContext() {
        PromotionRequestContext context = new PromotionRequestContext();
        context.setCx("");
        context.setCityId(1);
        context.setUserType(User.MT);
        context.setMtShopIdL(1641507L);
        context.setDpid("");
        context.setCouponPlatform(MtCouponPlatform.APP.getCode());
        context.setPayPlatform(PayPlatform.mt_android_native.getCode());
        context.setQueryShopCouponType(-1);
        context.setProductType(0);
        context.setMtDealGroupId(102179612);
        context.setChannel("odp");
        Map<String, String> dealExtParam = new HashMap<>();
        dealExtParam.put(ExtParamKeyConstant.odpFloorId, "210005");
        dealExtParam.put(ExtParamKeyConstant.odpLaunchId, "210006");
        context.setExtParam(dealExtParam);
        context.setNeedQueryPricePromotion(true);
        return context;
    }

    public static PromotionRequestContext buildPromotionRequestContext() {
        PromotionRequestContext context = new PromotionRequestContext();
        context.setCx("");
        context.setCityId(1);
        context.setUserType(User.DP);
        context.setDpShopIdL(5384038L);
        context.setDpid("");
        context.setCouponPlatform(MtCouponPlatform.DP_APP.getCode());
        context.setPayPlatform(PayPlatform.dp_android_native.getCode());
        context.setQueryShopCouponType(-1);
        context.setProductType(0);
        context.setDpDealGroupId(416567963);
        context.setChannel("odp");
        Map<String, String> dealExtParam = new HashMap<>();
        dealExtParam.put(ExtParamKeyConstant.odpLaunchId, "220005");
        context.setExtParam(dealExtParam);
        context.setNeedQueryPricePromotion(true);
        return context;
    }

    public static PromotionRequestContext buildRequestContext() {
        PromotionRequestContext context = new PromotionRequestContext();
        context.setCx("");
        context.setCityId(1);
        context.setUserType(User.DP);
        context.setDpShopIdL(5384038);
        context.setDpid("");
        context.setCouponPlatform(MtCouponPlatform.DP_APP.getCode());
        context.setPayPlatform(PayPlatform.dp_android_native.getCode());
        context.setQueryShopCouponType(-1);
        context.setProductType(0);
        context.setDpDealGroupId(200176747);
        context.setChannel("odp");
        Map<String, String> dealExtParam = new HashMap<>();
        dealExtParam.put(ExtParamKeyConstant.odpLaunchId, "210006");
        dealExtParam.put(ExtParamKeyConstant.odpFloorId, "210005");
        context.setExtParam(dealExtParam);
        context.setNeedQueryPricePromotion(true);
        return context;
    }
}
