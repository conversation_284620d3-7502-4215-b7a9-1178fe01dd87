package com.dianping.unified.coupon.biz;

import com.alibaba.fastjson.JSONObject;
import com.dianping.gmkt.coupon.common.api.enums.IssueDetailSourceEnum;
import com.dianping.pay.api.biz.NewIssueMultiCouponBiz;
import com.dianping.pay.api.entity.issuecoupon.NewBatchIssueCouponRequest;
import com.dianping.pay.api.entity.issuecoupon.NewIssueCouponUnit;
import com.dianping.pay.api.entity.issuecoupon.NewIssueMultiCouponContext;
import com.dianping.pay.api.util.JsonUtils;
import com.dianping.pay.promo.common.enums.User;
import com.google.common.collect.Lists;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

public class NewIssueMultiCouponBizTest extends TestBase {
    @Autowired
    private NewIssueMultiCouponBiz newIssueMultiCouponBiz;

    @Test
    public void m() {
        NewBatchIssueCouponRequest newBatchIssueCouponRequest = JsonUtils.toObject("{\"issueDetailSourceCode\":3,\"productType\":0,\"newIssueCouponUnitList\":[{\"couponType\":0,\"productId\":421959593,\"unifiedCouponGroupId\":\"116466256\",\"unitId\":1694073884391}]}", NewBatchIssueCouponRequest.class);
        System.out.println("newBatchIssueCouponRequest:" + JSONObject.toJSONString(newBatchIssueCouponRequest));
    }

    @Test
    public void doIssueDpOptCoupon() {
        NewIssueMultiCouponContext context = buildDpIssueCouponContext();
        newIssueMultiCouponBiz.doIssueOptCoupon(context);
        System.out.println("context:" + JSONObject.toJSONString(context));
    }

    @Test
    public void doIssueMtOptCoupon() {
        NewIssueMultiCouponContext context = buildMtIssueCouponContext();
        newIssueMultiCouponBiz.doIssueOptCoupon(context);
        System.out.println("context:" + JSONObject.toJSONString(context));
    }

    private NewIssueMultiCouponContext buildDpIssueCouponContext() {
        NewIssueMultiCouponContext context = new NewIssueMultiCouponContext();
        context.setUserType(User.DP);
        context.setDpUserId(859798856L);
        context.setIssueDetailSourceCode(IssueDetailSourceEnum.PRODUCT_PAGE_AUTO_ISSUE.getCode());
        NewIssueCouponUnit newIssueCouponUnit1 = buildNewIssueCouponUnit("1", 421535689L, "854751135");
        NewIssueCouponUnit newIssueCouponUnit2 = buildNewIssueCouponUnit("2", 421537629L, "854751135");
        NewIssueCouponUnit newIssueCouponUnit3 = buildNewIssueCouponUnit("3", 420638164L, "347157087");
        context.setNewIssueCouponUnitList(Lists.newArrayList(newIssueCouponUnit1, newIssueCouponUnit2, newIssueCouponUnit3));
        return context;
    }

    private NewIssueMultiCouponContext buildMtIssueCouponContext() {
        NewIssueMultiCouponContext context = new NewIssueMultiCouponContext();
        context.setUserType(User.MT);
        context.setMtUserId(29060335L);
        context.setIssueDetailSourceCode(IssueDetailSourceEnum.PRODUCT_PAGE_AUTO_ISSUE.getCode());
        NewIssueCouponUnit newIssueCouponUnit1 = buildNewIssueCouponUnit("1", 421535689L, "143053702");
        NewIssueCouponUnit newIssueCouponUnit2 = buildNewIssueCouponUnit("2", 421537629L, "143053702");
        context.setNewIssueCouponUnitList(Lists.newArrayList(newIssueCouponUnit1, newIssueCouponUnit2));
        return context;
    }

    private NewIssueCouponUnit buildNewIssueCouponUnit(String unitId, Long productId, String unifiedCouponGroupId) {
        NewIssueCouponUnit newIssueCouponUnit = new NewIssueCouponUnit();
        newIssueCouponUnit.setUnitId(unitId);
        newIssueCouponUnit.setUnifiedCouponGroupId(unifiedCouponGroupId);
        newIssueCouponUnit.setProductId(productId);
        newIssueCouponUnit.setCouponType(0);
        return newIssueCouponUnit;
    }
}
