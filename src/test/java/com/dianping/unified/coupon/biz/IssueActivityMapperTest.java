package com.dianping.unified.coupon.biz;

import com.dianping.api.constans.HeaderConstants;
import com.dianping.mobile.base.datatypes.enums.ClientType;
import com.dianping.mobile.base.datatypes.enums.DeviceType;
import com.dianping.mobile.framework.cache.CacheContext;
import com.dianping.mobile.framework.datatypes.AppType;
import com.dianping.mobile.framework.datatypes.IMobileContext;
import com.dianping.mobile.framework.datatypes.MobileHeader;
import com.dianping.mobile.framework.datatypes.client.ClientConfig;
import com.dianping.mobile.framework.datatypes.crawl.CrawlMark;
import com.dianping.mobile.framework.datatypes.token.UserStatusResult;
import com.dianping.pay.api.biz.activity.newloader.dto.PromotionRequestContext;
import com.dianping.pay.api.biz.activity.newloader.dto.RestUserInfo;
import com.dianping.pay.api.biz.activity.newloader.mapper.IssueActivityMapper;
import com.dianping.pay.api.entity.issuecoupon.IssuecouponcomponentRequest;
import com.dianping.pay.api.enums.GPSCoordEnum;
import com.dianping.pay.api.util.JsonUtils;
import com.dianping.pay.promo.common.enums.User;
import com.fasterxml.jackson.core.type.TypeReference;
import org.apache.commons.fileupload.FileItem;
import org.apache.commons.lang3.tuple.Pair;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.mockito.Mockito;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.UnsupportedEncodingException;
import java.net.URLDecoder;
import java.util.Map;
import java.util.Set;

public class IssueActivityMapperTest  {

    private static final String UUID = "x1234";
    private static final String CLIENT_VERSION = "10.1.200";
    private static final String OPEN_ID = "124214";
    private static final String APP_ID = "1234";
    private static final String MTGSIG = "1234adwt32";

    private RestUserInfo restUserInfo;

    @Before
    public void init() {
        restUserInfo = new RestUserInfo();
        restUserInfo.setUserCode(User.DP.getCode());
        restUserInfo.setUserId(1l);
    }

    @Test
    public void testBuildHttpPromotionRequestContext() {
        IssuecouponcomponentRequest request = new IssuecouponcomponentRequest();
        request.setShopid(1);
        PromotionRequestContext context = IssueActivityMapper.buildHttpPromotionRequestContext(request, restUserInfo, buildIMobileContext());
        Assert.assertNotNull(context);
    }

    @Test
    public void testHeaders() {
        IssuecouponcomponentRequest request = new IssuecouponcomponentRequest();
        request.setMiniProgramFlag(1);
        request.setShopidL(1L);
        request.setAppversion(CLIENT_VERSION);
        IMobileContext iMobileContext = buildIMobileContext();
        PromotionRequestContext context = IssueActivityMapper.buildHttpPromotionRequestContext(request, restUserInfo, iMobileContext);
        Assert.assertEquals(context.getUuid(), UUID);
        Assert.assertEquals(context.getVersion(), CLIENT_VERSION);
        Assert.assertEquals(context.getOpenId(), OPEN_ID);
        Assert.assertEquals(context.getAppId(), APP_ID);
        Assert.assertEquals(context.getMtgsig(), MTGSIG);


    }

    @Test
    public void convertLocation() {

        Double lat = 31.2;
        Double lng = 121.3;
        Double precision = 0.001;
        Pair<Double, Double> pair = IssueActivityMapper.convertLocation(GPSCoordEnum.MARS.getCode(), 31.2, 121.3);
        Assert.assertNotNull(pair);
        Assert.assertTrue(pair.getLeft() - lat <= precision);
        Assert.assertTrue(pair.getRight() - lng <= precision);

        pair = IssueActivityMapper.convertLocation(GPSCoordEnum.GPS.getCode(), 31.2, 121.3);
        Assert.assertNotNull(pair);
        Assert.assertTrue(Math.abs(pair.getLeft() - lat) > precision);
        Assert.assertTrue(Math.abs(pair.getRight() - lng) > precision);

    }

    private IMobileContext buildIMobileContext() {
        IMobileContext iMobileContext = new IMobileContext() {
            @Override public Set<String> getParameterKeys() {
                return null;
            }

            @Override public String getParameter(String s) {
                return null;
            }

            @Override public MobileHeader getHeader() {
                return new MobileHeader();
            }

            @Override public String getUserIp() {
                return null;
            }

            @Override public ClientType getClient() {
                return null;
            }

            @Override public String getVersion() {
                return "10.1.200";
            }

            @Override public String getSource() {
                return null;
            }

            @Override public String getCellPhoneType() {
                return null;
            }

            @Override public String getUserAgent() {
                return null;
            }

            @Override public long getUserId() {
                return 0;
            }

            @Override public Map<String, FileItem> getMultiParamMap() {
                return null;
            }

            @Override public String getOs() {
                return null;
            }

            @Override public String getProtocolVersion() {
                return null;
            }

            @Override public boolean isPost() {
                return false;
            }

            @Override public ClientConfig getConfig() {
                return null;
            }

            @Override public String getRequestId() {
                return null;
            }

            @Override public String getRequestIdReffer() {
                return null;
            }

            @Override public String getActionKey() {
                return null;
            }

            @Override public HttpServletRequest getRequest() {
                HttpServletRequest request = Mockito.mock(HttpServletRequest.class);
                Mockito.when(request.getHeader(Mockito.eq(HeaderConstants.UUID))).thenReturn(UUID);
                Mockito.when(request.getHeader(Mockito.eq(HeaderConstants.CLIENT_VERSION))).thenReturn(UUID);
                Mockito.when(request.getHeader(Mockito.eq(HeaderConstants.OPEN_ID))).thenReturn(OPEN_ID);
                Mockito.when(request.getHeader(Mockito.eq(HeaderConstants.APP_ID))).thenReturn(APP_ID);
                Mockito.when(request.getHeader(Mockito.eq(HeaderConstants.MTGSIG))).thenReturn(MTGSIG);
                return request;
            }

            @Override public HttpServletResponse getResponse() {
                return null;
            }

            @Override public byte[] getPicFile() {
                return new byte[0];
            }

            @Override public CacheContext getCacheContext() {
                return null;
            }

            @Override public AppType getAppType() {
                return null;
            }

            @Override public boolean isMtClient() {
                return false;
            }

            @Override public DeviceType getDeviceType() {
                return null;
            }

            @Override public UserStatusResult getUserStatus() {
                return null;
            }

            @Override public void setUserStatus(UserStatusResult result) {

            }

            @Override public CrawlMark getCrawlMark() {
                return null;
            }

            @Override public int getAppId() {
                return 0;
            }
        };
        return iMobileContext;
    }


    @Test
    public void testUrlDecode() {
        try {
            String dealextparam = URLDecoder.decode("%7B%22odpflowinfo%22%3A%22075093CCFABBA05CEF7ECB0F8EAB76DA6B7A724F8DA7D6827D4C510ACC144E4C48EF9E9D1C2BBF0ADB25B7161096750F79B2EAB78D9CC1A8D28731A2D83629FF669E3954EA6DC402A16FD43B3914318B08D13F581CB0CED75DD25A30AD55F840CC2EE60993B4AD0FB6D6A8064763324C1F14AB9FAEBD781E84A04ED8CC3570C250FB23E9EF1F67737D2C2D6589779A7CBE9F133850F8FA31DBEF75994CE3547CC95415FD92597A5DA601BEBDD0DA84796F748E643121F5F11208FA45115039FD%22%2C%22odpLaunchId%22%3A%22lLMk9gjP%22%2C%22odpFloorId%22%3A%22%22%2C%22source%22%3A%22odp%22%2C%22odpRequestId%22%3A%226dea243f-b6c9-4374-8fc5-6865e7caf827_lLMk9gjP%22%2C%22odpChannelType%22%3A3%7D", "utf-8");
            Map<String, String> map = JsonUtils.toObject(dealextparam, new TypeReference<Map<String, String>>(){});
            Assert.assertNotNull(map);
            String decode = URLDecoder.decode(dealextparam, "utf-8");
            Assert.assertTrue(decode.equals(dealextparam));
            Assert.assertNotNull(JsonUtils.toObject(decode, Map.class));
            Assert.assertTrue(decode.equals(URLDecoder.decode(decode, "utf-8")));
        } catch (UnsupportedEncodingException e) {
            Assert.assertTrue(false);
        }
    }
}
