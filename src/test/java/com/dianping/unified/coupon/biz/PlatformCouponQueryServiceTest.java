package com.dianping.unified.coupon.biz;

import com.dianping.pay.api.beans.PlatformCoupon;
import com.dianping.pay.api.biz.activity.newloader.PlatformCouponQuerySerivce;
import com.dianping.pay.api.biz.activity.newloader.dto.CouponIssueActivityQueryContext;
import com.dianping.pay.api.util.JsonUtils;
import com.google.common.collect.Lists;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/09/06
 */
public class PlatformCouponQueryServiceTest extends TestBase {

    @Autowired
    private PlatformCouponQuerySerivce platformCouponQuerySerivce;


    @Test
    public void testQueryPlatformCoupons() {
        CouponIssueActivityQueryContext queryContext = new CouponIssueActivityQueryContext();
        queryContext.setDpClient(false);
        queryContext.setShopIdL(78534837L);
        queryContext.setUserId(5087794086L);
        queryContext.setCouponPlatforms(Lists.newArrayList(8));
        queryContext.setUuid("0000000000000998206842B4843CBB47821023FE36C0BA168416483104797960");
        List<PlatformCoupon> result = platformCouponQuerySerivce.queryPlatformCoupons(queryContext);
        System.out.println(JsonUtils.toJson(result));
    }

}
