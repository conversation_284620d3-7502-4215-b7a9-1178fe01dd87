package com.dianping.unified.coupon.biz;

import com.dianping.pay.api.repository.LeafRepository;
import java.util.List;
import org.junit.Assert;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

public class LeafRepositoryTest extends TestBase {
    @Autowired
    private LeafRepository leafRepository;

    @Test
    public void nextIdTest() {
        Long leafId = leafRepository.nextFinancialConsumeSerialId();
        List<Long> leafIds = leafRepository.batchGenFinancialConsumeSerialId(3);
        Assert.assertTrue(leafId > 0);
    }
}
