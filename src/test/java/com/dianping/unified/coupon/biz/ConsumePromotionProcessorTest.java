package com.dianping.unified.coupon.biz;

import com.alibaba.fastjson.JSONObject;
import com.dianping.pay.api.biz.activity.newloader.dto.CouponIssueActivityQueryContext;
import com.dianping.pay.api.biz.activity.newloader.promo.FinancialConsumePromotionProcessor;
import com.dianping.pay.api.entity.issuecoupon.IssueCouponRequest;
import com.dianping.pay.api.entity.issuecoupon.UnifiedIssueResult;
import org.junit.Assert;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

public class ConsumePromotionProcessorTest extends TestBase{
    @Autowired
    private FinancialConsumePromotionProcessor financialConsumePromotionProcessor;

    @Test
    public void doIssueGovConsumeCouponTest() {
        try {
            IssueCouponRequest issueCouponRequest = new IssueCouponRequest();
            CouponIssueActivityQueryContext context = new CouponIssueActivityQueryContext();
            context.setSerialno(String.valueOf(System.currentTimeMillis()));
            context.setUnifiedCouponGroupId("63682");
            context.setDpClient(false);
            issueCouponRequest.setUserId(29060335L);
            issueCouponRequest.setPackagesecretkey("SK123123");
            UnifiedIssueResult result = financialConsumePromotionProcessor.doIssueGovConsumeCoupon(issueCouponRequest, context);
        } catch(Exception e) {
            Assert.assertNotNull(e);
        }
    }
}
