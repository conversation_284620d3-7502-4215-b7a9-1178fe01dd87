package com.dianping.unified.coupon.biz;

import com.dianping.api.picasso.UrlUtils;
import com.dianping.gmkt.coupon.common.api.enums.CouponBusiness;
import com.dianping.pay.api.beans.IssueCouponActivity;
import com.dianping.pay.api.biz.activity.newloader.CouponIssueActivityQueryService;
import com.dianping.pay.api.biz.activity.newloader.dto.CouponIssueActivityQueryContext;
import com.dianping.unified.coupon.manage.api.dto.UnifiedCouponGroupDTO;
import com.dianping.unified.coupon.manage.api.dto.UnifiedCouponProductDTO;
import com.google.common.collect.Lists;
import com.meituan.mtrace.Tracer;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.junit.Assert;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/7/12
 */
public class IssueTest extends TestBase {

    @Autowired
    private CouponIssueActivityQueryService couponIssueActivityQueryService;

    @Test
    public void test() {
        CouponIssueActivityQueryContext couponIssueActivityQueryContext = new CouponIssueActivityQueryContext();
        couponIssueActivityQueryContext.setProductType(4);
        couponIssueActivityQueryContext.setProductId(684011566);
        couponIssueActivityQueryContext.setDpClient(true);
        List<IssueCouponActivity> issueCouponActivities = couponIssueActivityQueryService.queryActivities(couponIssueActivityQueryContext);
        Assert.assertNotNull(issueCouponActivities);
    }

    @Test
    public void issueUrlTest() {
        UnifiedCouponGroupDTO unifiedCouponGroupDTO = new UnifiedCouponGroupDTO();
        unifiedCouponGroupDTO.setProductCodeList(Lists.newArrayList(CouponBusiness.DZ_TUANGOU.getCode()));
        unifiedCouponGroupDTO.setCouponProductList(Lists.newArrayList(new UnifiedCouponProductDTO()));

        String dpUrl = UrlUtils.getCouponGroupToUseUrl(unifiedCouponGroupDTO, "123", false);
        String mtUrl = UrlUtils.getCouponGroupToUseUrl(unifiedCouponGroupDTO, "123", false);
        Assert.assertTrue(StringUtils.isNotBlank(dpUrl));
        Assert.assertTrue(StringUtils.isNotBlank(mtUrl));
        System.out.println(dpUrl);
        System.out.println(mtUrl);

    }
}
