package com.dianping.unified.coupon.biz;

import com.dianping.api.common.enums.TrafficSourceEnum;
import com.dianping.gmkt.coupon.common.api.enums.MtCouponPlatform;
import com.dianping.mobile.base.datatypes.enums.ClientType;
import com.dianping.mobile.base.datatypes.enums.DeviceType;
import com.dianping.mobile.framework.cache.CacheContext;
import com.dianping.mobile.framework.datatypes.AppType;
import com.dianping.mobile.framework.datatypes.IMobileContext;
import com.dianping.mobile.framework.datatypes.MobileHeader;
import com.dianping.mobile.framework.datatypes.client.ClientConfig;
import com.dianping.mobile.framework.datatypes.crawl.CrawlMark;
import com.dianping.mobile.framework.datatypes.token.UserStatusResult;
import com.dianping.pay.api.biz.activity.newloader.dto.CouponActivityContext;
import com.dianping.pay.api.biz.activity.newloader.promo.MagicalMemberCouponProcessor;
import com.dianping.pay.api.service.impl.ShopWrapperService;
import com.dianping.pay.common.enums.PayPlatform;
import com.dianping.pay.promo.common.enums.User;
import com.meituan.mtrace.Tracer;
import com.sankuai.nib.magic.member.base.enums.BizEnum;
import com.sankuai.nib.mkt.common.base.enums.RealGpsCoordTypeEnum;
import com.sankuai.nib.mkt.common.base.enums.RulePropertyTypeEnum;
import com.sankuai.nib.mkt.common.base.enums.propertyValueType.ClientTypeEnum;
import com.sankuai.nibmkt.promotion.api.query.model.magicalmember.request.QueryMagicalMemberTagRequest;
import com.sankuai.nibmkt.promotion.api.query.model.magicalmember.response.QueryMagicalMemberTagResponse;
import com.sankuai.nibmkt.promotion.api.service.PromotionQueryService;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.fileupload.FileItem;
import org.apache.commons.lang3.tuple.Pair;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.Spy;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.HashMap;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.atomic.AtomicReference;

public class MagicalMemberCouponProcessorTest extends TestBase {

    private static final String UUID = "192e659d752c8-233a32e48c5cec-0-0-192e659d752c8";
    private static final String CLIENT_VERSION = "5.22.83";
    private static final String OPEN_ID = "oJVP50CKM_pf6LU3xtQcJ6hRrPXo";
    private static final String APP_ID = "wxde8ac0a21135c07d";
    private static final String MTGSIG = "{\"a1\":\"1.2\",...,\"a7\":\"wxde8ac0a21135c07d\"}";
    private static final String MT_FINGERPRINT = "WX__ver1.2.0_CCCC_dfwOgF35EQNYzVh8i27kXL04k6XTiM6UIbLC34PDQwBs4ozo/V8vQDjBpXC9zCevMBauXt8Gups86GaQMtR1TfjWKSCfwL+/";

    @Spy
    @InjectMocks
    private MagicalMemberCouponProcessor magicalMemberCouponProcessor;

    @Mock
    private PromotionQueryService promotionQueryService;

    @Spy
    private ShopWrapperService shopWrapperService;

    @Before
    public void init() {
        super.init();
    }

    @Test
    public void prepare() {
        Tracer.setSwimlane("selftest-240309-112825-892");
        boolean result = true;
        try {
            CouponActivityContext promoCtx = buildCouponActivityContext();
            IMobileContext iMobileContext = buildIMobileContext();
            magicalMemberCouponProcessor.prePare(promoCtx, iMobileContext);
        } catch(Exception e) {
            result = false;
        }
        Assert.assertTrue(result);
    }

    @Test
    public void testBuildQueryMagicalMemberTagRequest() {
        CouponActivityContext promoCtx = buildCouponActivityContext();
        promoCtx.setMiniProgramFlag(1);
        promoCtx.setUuid(UUID);
        promoCtx.setVersion(CLIENT_VERSION);
        promoCtx.setOpenId(OPEN_ID);
        promoCtx.setAppId(APP_ID);
        promoCtx.setMtgsig(MTGSIG);
        promoCtx.setCx(MT_FINGERPRINT);
        promoCtx.setTrafficSource(TrafficSourceEnum.NewYouHuiMa.code);
        promoCtx.setMmcBuy("1");
        promoCtx.setMmcFree("1");
        promoCtx.setMmcUse("1");
        promoCtx.setMmcInflate("1");
        promoCtx.setMagicMemberComponentVersion("1.0.0");

        IMobileContext iMobileContext = buildIMobileContext();

        Mockito.when(shopWrapperService.loadMtShopFirstCategory(promoCtx.getMtShopIdL())).thenReturn(Pair.of(1, 2));

        QueryMagicalMemberTagRequest request = magicalMemberCouponProcessor.buildQueryMagicalMemberTagRequest(promoCtx, iMobileContext);
        Assert.assertEquals( request.getNibBiz(), BizEnum.GENERAL_GROUPBUY.name());
        Assert.assertEquals( request.getUserInfoDTO().getDeviceId(), UUID);
        AtomicReference<Integer> matchCount = new AtomicReference<>(0);
        request.getCommonProperties().forEach(property -> {
            if (property.getCode().equals(RulePropertyTypeEnum.clientTp.getCode()))  {
                matchCount.getAndSet(matchCount.get() + 1);
                Assert.assertEquals(property.getValue(), ClientTypeEnum.MT_WE_CHAT_APPLET.getValue());
            } else if (property.getCode().equals(RulePropertyTypeEnum.wxVersion.getCode()))  {
                matchCount.getAndSet(matchCount.get() + 1);
                Assert.assertEquals(property.getValue(), CLIENT_VERSION);
            } else if (property.getCode().equals(RulePropertyTypeEnum.position.getCode()))  {
                matchCount.getAndSet(matchCount.get() + 1);
                Assert.assertEquals(property.getValue(), String.valueOf(MagicalMemberCouponProcessor.MINI_PROGRAM_POI_POSITION));
            } else if (property.getCode().equals(RulePropertyTypeEnum.realGpsCoordType.getCode()))  {
                matchCount.getAndSet(matchCount.get() + 1);
                Assert.assertEquals(property.getValue(), RealGpsCoordTypeEnum.GCJ02.name());
            } else if (property.getCode().equals(RulePropertyTypeEnum.wxAppId.getCode()))  {
                matchCount.getAndSet(matchCount.get() + 1);
                Assert.assertEquals(property.getValue(), APP_ID);
            } else if (property.getCode().equals(RulePropertyTypeEnum.wxOpenId.getCode())) {
                matchCount.getAndSet(matchCount.get() + 1);
                Assert.assertEquals(property.getValue(), OPEN_ID);
            } else if (property.getCode().equals(RulePropertyTypeEnum.mtgsig.getCode())) {
                matchCount.getAndSet(matchCount.get() + 1);
                Assert.assertEquals(property.getValue(), MTGSIG);
            } else if (property.getCode().equals(RulePropertyTypeEnum.fingerprint.getCode())) {
                matchCount.getAndSet(matchCount.get() + 1);
                Assert.assertEquals(MT_FINGERPRINT, property.getValue());
            } else if (property.getCode().equals(RulePropertyTypeEnum.magicMemberComponentVersion.getCode())) {
                matchCount.getAndSet(matchCount.get() + 1);
                Assert.assertEquals(property.getValue(), "1.0.0");
            }
        });
        Assert.assertEquals(9, matchCount.get().intValue());
    }

    @Test
    public void testMiniProgramQueryMagicalMemberTag() {
        Tracer.init();
        CouponActivityContext promoCtx = buildMiniProgramCouponActivityContext();
        IMobileContext iMobileContext = buildIMobileContext();

        Mockito.when(shopWrapperService.loadMtShopFirstCategory(promoCtx.getMtShopIdL())).thenReturn(Pair.of(1, 2));

        magicalMemberCouponProcessor.prePare(promoCtx, iMobileContext);
        magicalMemberCouponProcessor.loadPromo(promoCtx);
        Assert.assertNotNull(promoCtx.getMagicalMemberTagRawDOFuture());
//        Assert.assertNotNull(promoCtx.getMagicalMemberTagRawDO());
    }

    @Test
    public void testBuildMagicalMemberTagDOWithExtendedFields() {
        // 准备测试数据
        QueryMagicalMemberTagResponse response = new QueryMagicalMemberTagResponse();
        Long poiId = 307955586L;

        // 构造扩展字段数据
        Map<Long, Map<String, String>> poiId2ExtendFieldMap = new HashMap<>();
        Map<String, String> extendedFieldsMap = new HashMap<>();
        extendedFieldsMap.put("key1", "value1");
        extendedFieldsMap.put("key2", "value2");
        poiId2ExtendFieldMap.put(poiId, extendedFieldsMap);
        response.setPoiId2ExtendFieldMap(poiId2ExtendFieldMap);

        // 调用被测试方法
        Mockito.when(promotionQueryService.queryMagicalMemberTag(Mockito.any())).thenReturn(response);

        CouponActivityContext promoCtx = buildMiniProgramCouponActivityContext();
        IMobileContext iMobileContext = buildIMobileContext();

        // 使用反射调用私有方法buildMagicalMemberTagDO
        try {
            java.lang.reflect.Method method = MagicalMemberCouponProcessor.class.getDeclaredMethod("buildMagicalMemberTagDO", QueryMagicalMemberTagResponse.class, Long.class);
            method.setAccessible(true);
            Object result = method.invoke(magicalMemberCouponProcessor, response, poiId);

            // 验证结果
            Assert.assertNotNull(result);
            Assert.assertTrue(result instanceof com.dianping.pay.api.beans.MagicalMemberTagRawDO);
            com.dianping.pay.api.beans.MagicalMemberTagRawDO magicalMemberTagRawDO = (com.dianping.pay.api.beans.MagicalMemberTagRawDO) result;

            // 验证扩展字段是否正确设置
            Assert.assertNotNull(magicalMemberTagRawDO.getExtendedFieldsMap());
            Assert.assertEquals(2, magicalMemberTagRawDO.getExtendedFieldsMap().size());
            Assert.assertEquals("value1", magicalMemberTagRawDO.getExtendedFieldsMap().get("key1"));
            Assert.assertEquals("value2", magicalMemberTagRawDO.getExtendedFieldsMap().get("key2"));
        } catch (Exception e) {
            Assert.fail("测试失败: " + e.getMessage());
        }
    }

    @Test
    public void testComponentVersionInRequest() {
        // 准备测试数据
        CouponActivityContext promoCtx = buildCouponActivityContext();
        String testVersion = "2.0.0";
        promoCtx.setMagicMemberComponentVersion(testVersion);

        IMobileContext iMobileContext = buildIMobileContext();
        Mockito.when(shopWrapperService.loadMtShopFirstCategory(promoCtx.getMtShopIdL())).thenReturn(Pair.of(1, 2));

        // 调用被测试方法
        QueryMagicalMemberTagRequest request = magicalMemberCouponProcessor.buildQueryMagicalMemberTagRequest(promoCtx, iMobileContext);

        // 验证组件版本是否正确设置
        boolean found = false;
        for (com.sankuai.nib.mkt.common.base.model.Property property : request.getCommonProperties()) {
            if (property.getCode().equals(RulePropertyTypeEnum.magicMemberComponentVersion.getCode())) {
                found = true;
                Assert.assertEquals(testVersion, property.getValue());
                break;
            }
        }
        Assert.assertTrue("未找到magicMemberComponentVersion属性", found);
    }

    private CouponActivityContext buildMiniProgramCouponActivityContext() {
        CouponActivityContext promoCtx = new CouponActivityContext();
        promoCtx.setMiniProgramFlag(1);
        promoCtx.setNeedMagicalMemberCoupon(true);
        promoCtx.setUserType(User.MT);
        promoCtx.setMtUserId(5034293558L);
        promoCtx.setMtShopIdL(307955586L);
        promoCtx.setCx(MT_FINGERPRINT);
        promoCtx.setMtCityId(10);
        promoCtx.setMtActualCityId(10);
        promoCtx.setMtgsig(MTGSIG);
        promoCtx.setVersion(CLIENT_VERSION);
        promoCtx.setCposition(1102);
        promoCtx.setLatitude(31.25956);
        promoCtx.setLongitude(121.52609);
        promoCtx.setUuid(UUID);
        promoCtx.setOpenId(OPEN_ID);
        promoCtx.setAppId(APP_ID);
        promoCtx.setMagicMemberComponentVersion("1.0.0");
        return promoCtx;
    }

    private CouponActivityContext buildCouponActivityContext() {
        CouponActivityContext promoCtx = new CouponActivityContext();
        promoCtx.setNeedMagicalMemberCoupon(true);
        promoCtx.setUserType(User.MT);
        promoCtx.setMtUserId(5034293558L);
        promoCtx.setCouponPlatform(MtCouponPlatform.APP.getCode());
        promoCtx.setPayPlatform(PayPlatform.mt_iphone_native.getCode());
        promoCtx.setCPlatform(1);
        promoCtx.setCityId(1);
        promoCtx.setDpid("12345");
        promoCtx.setMtShopIdL(1927078209L);
        promoCtx.setDpid("12345");
        return promoCtx;
    }


    private IMobileContext buildIMobileContext() {
        IMobileContext iMobileContext = new IMobileContext() {
            @Override public Set<String> getParameterKeys() {
                return null;
            }

            @Override public String getParameter(String s) {
                return null;
            }

            @Override public MobileHeader getHeader() {
                return null;
            }

            @Override public String getUserIp() {
                return null;
            }

            @Override public ClientType getClient() {
                return null;
            }

            @Override public String getVersion() {
                return "10.1.200";
            }

            @Override public String getSource() {
                return null;
            }

            @Override public String getCellPhoneType() {
                return null;
            }

            @Override public String getUserAgent() {
                return null;
            }

            @Override public long getUserId() {
                return 0;
            }

            @Override public Map<String, FileItem> getMultiParamMap() {
                return null;
            }

            @Override public String getOs() {
                return null;
            }

            @Override public String getProtocolVersion() {
                return null;
            }

            @Override public boolean isPost() {
                return false;
            }

            @Override public ClientConfig getConfig() {
                return null;
            }

            @Override public String getRequestId() {
                return null;
            }

            @Override public String getRequestIdReffer() {
                return null;
            }

            @Override public String getActionKey() {
                return null;
            }

            @Override public HttpServletRequest getRequest() {
                return null;
            }

            @Override public HttpServletResponse getResponse() {
                return null;
            }

            @Override public byte[] getPicFile() {
                return new byte[0];
            }

            @Override public CacheContext getCacheContext() {
                return null;
            }

            @Override public AppType getAppType() {
                return null;
            }

            @Override public boolean isMtClient() {
                return false;
            }

            @Override public DeviceType getDeviceType() {
                return null;
            }

            @Override public UserStatusResult getUserStatus() {
                return null;
            }

            @Override public void setUserStatus(UserStatusResult result) {

            }

            @Override public CrawlMark getCrawlMark() {
                return null;
            }

            @Override public int getAppId() {
                return 0;
            }
        };
        return iMobileContext;
    }
}
