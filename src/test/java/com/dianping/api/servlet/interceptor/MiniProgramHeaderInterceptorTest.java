package com.dianping.api.servlet.interceptor;

import com.dianping.mobile.framework.datatypes.MobileContext;
import com.dianping.mobile.framework.servlet.interceptor.base.UserInterceptor;
import org.junit.Assert;
import org.junit.Test;
import org.mockito.Mockito;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

public class MiniProgramHeaderInterceptorTest {

    @Test
    public void testHeaderInterceptor() throws Exception {UserInterceptor headInterceptor = new MiniProgramHeaderInterceptor();
                Assert.assertNotNull(headInterceptor);
        HttpServletRequest request = Mockito.mock(HttpServletRequest.class);
        HttpServletResponse response = Mockito.mock(HttpServletResponse.class);
        Mockito.when(request.getHeader("clientversion")).thenReturn( "1.1");
        // 使用模拟的request和response对象创建MobileContext实例
        MobileContext context = new MobileContext(request, response);
        Assert.assertNull(context.getVersion());
        headInterceptor.preHandle(context);
        Assert.assertEquals(context.getVersion(), "1.1");
    }
}