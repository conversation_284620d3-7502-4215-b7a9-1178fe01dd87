package com.dianping.api.picasso.controller;

import com.dianping.mobile.framework.datatypes.IMobileContext;
import com.dianping.mobile.framework.datatypes.IMobileResponse;
import com.dianping.pay.api.biz.UnifiedIssueMultiCouponBiz;
import com.dianping.pay.api.entity.issuecoupon.UnifiedissuemulticouponRequest;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import static org.junit.Assert.assertEquals;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class UnifiedIssueMultiCouponActionTest {

    @InjectMocks
    private UnifiedIssueMultiCouponAction action;

    @Mock
    private UnifiedIssueMultiCouponBiz unifiedIssueMultiCouponBiz;

    @Mock
    private IMobileContext mockContext;

    @Mock
    private IMobileResponse mockResponse;

    @Before
    public void setUp() {
        when(unifiedIssueMultiCouponBiz.validate(any(), any())).thenReturn(mockResponse);
    }

    @Test
    public void testValidate_WhenLongProductIdProvided() {
        // Given
        UnifiedissuemulticouponRequest request = new UnifiedissuemulticouponRequest();
        request.setProductid(0);
        request.setLongProductId(123L);

        // When
        IMobileResponse response = action.validate(request, mockContext);

        // Then
        assertEquals(123, request.getProductid().intValue());
        assertEquals(mockResponse, response);
        verify(unifiedIssueMultiCouponBiz).validate(request, mockContext);
    }

    @Test
    public void testValidate_WhenProductIdProvided() {
        // Given
        UnifiedissuemulticouponRequest request = new UnifiedissuemulticouponRequest();
        request.setProductid(123);
        request.setLongProductId(null);

        // When
        IMobileResponse response = action.validate(request, mockContext);

        // Then
        assertEquals(Long.valueOf(123), request.getLongProductId());
        assertEquals(mockResponse, response);
        verify(unifiedIssueMultiCouponBiz).validate(request, mockContext);
    }

    @Test
    public void testValidate_WhenBothProductIdsProvided() {
        // Given
        UnifiedissuemulticouponRequest request = new UnifiedissuemulticouponRequest();
        request.setProductid(123);
        request.setLongProductId(456L);

        // When
        IMobileResponse response = action.validate(request, mockContext);

        // Then
        assertEquals(123, request.getProductid().intValue());
        assertEquals(Long.valueOf(456), request.getLongProductId());
        assertEquals(mockResponse, response);
        verify(unifiedIssueMultiCouponBiz).validate(request, mockContext);
    }

    @Test
    public void testValidate_WhenNoProductIdsProvided() {
        // Given
        UnifiedissuemulticouponRequest request = new UnifiedissuemulticouponRequest();
        request.setProductid(0);
        request.setLongProductId(null);

        // When
        IMobileResponse response = action.validate(request, mockContext);

        // Then
        assertEquals(0, request.getProductid().intValue());
        assertEquals(null, request.getLongProductId());
        assertEquals(mockResponse, response);
        verify(unifiedIssueMultiCouponBiz).validate(request, mockContext);
    }
}