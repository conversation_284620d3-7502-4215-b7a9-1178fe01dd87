package com.dianping.api.picasso.controller;

import com.dianping.mobile.framework.datatypes.CommonMobileResponse;
import com.dianping.mobile.framework.datatypes.IMobileContext;
import com.dianping.pay.api.biz.UnifiedIssueMultiCouponBiz;
import com.dianping.pay.api.entity.issuecoupon.UnifiedissuemulticouponRequest;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

public class UnifiedIssueMultiCouponMapiActionTest {

    @InjectMocks
    private UnifiedIssueMultiCouponMapiAction action;

    @Mock
    private UnifiedIssueMultiCouponBiz unifiedIssueMultiCouponBiz;

    @Mock
    private IMobileContext mobileContext;

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
        when(unifiedIssueMultiCouponBiz.validate(any(), any())).thenReturn(new CommonMobileResponse(null));
    }

    @Test
    public void test_validate_whenProductIdIsNull_andLongProductIdExists() {
        // 准备测试数据
        UnifiedissuemulticouponRequest request = new UnifiedissuemulticouponRequest();
        request.setProductid(null);
        request.setLongProductId(123L);

        // 执行测试
        action.validate(request, mobileContext);

        // 验证结果
        verify(unifiedIssueMultiCouponBiz).validate(argThat(req ->
                req.getProductid() == 123 && req.getLongProductId() == 123L
        ), eq(mobileContext));
    }

    @Test
    public void test_validate_whenProductIdExists_andLongProductIdIsNull() {
        // 准备测试数据
        UnifiedissuemulticouponRequest request = new UnifiedissuemulticouponRequest();
        request.setProductid(456);
        request.setLongProductId(null);

        // 执行测试
        action.validate(request, mobileContext);

        // 验证结果
        verify(unifiedIssueMultiCouponBiz).validate(argThat(req ->
                req.getProductid() == 456 && req.getLongProductId() == 456L
        ), eq(mobileContext));
    }

    @Test
    public void test_validate_whenBothFieldsAreNull() {
        // 准备测试数据
        UnifiedissuemulticouponRequest request = new UnifiedissuemulticouponRequest();
        request.setProductid(null);
        request.setLongProductId(null);

        // 执行测试
        action.validate(request, mobileContext);

        // 验证结果
        verify(unifiedIssueMultiCouponBiz).validate(argThat(req ->
                req.getProductid() == null && req.getLongProductId() == null
        ), eq(mobileContext));
    }
}