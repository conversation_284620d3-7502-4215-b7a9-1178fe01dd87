package com.dianping.api.picasso.controller;

import com.dianping.mobile.framework.datatypes.CommonMobileResponse;
import com.dianping.mobile.framework.datatypes.IMobileContext;
import com.dianping.mobile.framework.datatypes.IMobileResponse;
import com.dianping.pay.api.entity.issuecoupon.IssueCouponMsg;
import com.dianping.pay.api.entity.issuecoupon.IssueCouponRequest;
import com.dianping.pay.framework.utils.shopUuid.ShopUuidUtils;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertNotNull;
import static org.junit.Assert.assertNull;
import static org.junit.Assert.assertTrue;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class IssueCouponPicassoActionTest {

    @InjectMocks
    private IssueCouponPicassoAction action;

    @Mock
    private IMobileContext context;
    @Mock
    private ShopUuidUtils shopUuidUtils;

    /**
     * 测试正常场景
     */
    @Test
    public void testValidateNormalScenario() throws Throwable {
        // arrange
        IssueCouponRequest request = new IssueCouponRequest();
        request.setProductId(1);
        request.setCouponOptionId(1);
        when(context.getUserId()).thenReturn(1L);

        // act
        IMobileResponse response = action.validate(request, context);

        // assert
        assertNull(response);
        assertEquals(request.getProductId(), 1);
        assertEquals(request.getLongProductId().longValue(), 1L);
        assertEquals(request.getCouponOptionId(), 1);
        assertEquals(request.getStringCouponOptionId(), "1");
    }

    /**
     * 测试request为null时抛出NullPointerException
     */
    @Test(expected = NullPointerException.class)
    public void testValidateRequestIsNull() throws Throwable {
        // arrange
        IssueCouponRequest request = null;

        // act
        IMobileResponse response = action.validate(request, context);

        // assert
        // Expected exception
    }

    /**
     * 测试用户未登录时抛出IllegalArgumentException
     */
    @Test
    public void testValidateUserNotLoggedIn() throws Throwable {
        // arrange
        IssueCouponRequest request = new IssueCouponRequest();
        when(context.getUserId()).thenReturn(0L);

        // act
        IMobileResponse response = action.validate(request, context);

        // assert
        assertNotNull(response);
        assertTrue(response instanceof CommonMobileResponse);
        assertEquals(1, ((IssueCouponMsg) response.getData()).getErrorCode());
    }

    /**
     * 测试无效的shopId时抛出IllegalArgumentException
     */
    @Test
    public void testValidateInvalidShopId() throws Throwable {
        // arrange
        IssueCouponRequest request = new IssueCouponRequest();
        request.setShopIdL(0L);
        when(context.getUserId()).thenReturn(1L);

        // act
        IMobileResponse response = action.validate(request, context);

        // assert
        assertNotNull(response);
        assertTrue(response instanceof CommonMobileResponse);
        assertEquals(1, ((IssueCouponMsg) response.getData()).getErrorCode());
    }

    /**
     * 测试无效的couponOptionId时抛出IllegalArgumentException
     */
    @Test
    public void testValidateInvalidCouponOptionId() throws Throwable {
        // arrange
        IssueCouponRequest request = new IssueCouponRequest();
        request.setCouponOptionId(0);
        when(context.getUserId()).thenReturn(1L);

        // act
        IMobileResponse response = action.validate(request, context);

        // assert
        assertNotNull(response);
        assertTrue(response instanceof CommonMobileResponse);
        assertEquals(1, ((IssueCouponMsg) response.getData()).getErrorCode());
    }

    /**
     * 测试issueDetailSourceCode无效时抛出IllegalArgumentException
     */
    @Test
    public void testValidateInvalidIssueDetailSourceCode() throws Throwable {
        // arrange
        IssueCouponRequest request = new IssueCouponRequest();
        request.setIssueDetailSourceCode(0);
        when(context.getUserId()).thenReturn(1L);

        // act
        IMobileResponse response = action.validate(request, context);

        // assert
        // assert
        assertNotNull(response);
        assertTrue(response instanceof CommonMobileResponse);
        assertEquals(1, ((IssueCouponMsg) response.getData()).getErrorCode());
    }

    /**
     * 测试productId和longProductId互转的场景
     */
    @Test
    public void testValidateProductIdAndLongProductIdConversion() throws Throwable {
        // arrange
        IssueCouponRequest request = new IssueCouponRequest();
        request.setLongProductId(123L);
        request.setCouponOptionId(1);
        when(context.getUserId()).thenReturn(1L);

        // act
        IMobileResponse response = action.validate(request, context);

        // assert
        assertEquals(request.getProductId(), 123);
        assertNull(response);
    }

    /**
     * 测试couponOptionId和stringCouponOptionId互转的场景
     */
    @Test
    public void testValidateCouponOptionIdAndStringCouponOptionIdConversion() throws Throwable {
        // arrange
        IssueCouponRequest request = new IssueCouponRequest();
        request.setStringCouponOptionId("456");
        request.setProductId(1);
        when(context.getUserId()).thenReturn(1L);

        // act
        IMobileResponse response = action.validate(request, context);

        // assert
        assertEquals(request.getCouponOptionId(), 456);
        assertNull(response);
    }
}