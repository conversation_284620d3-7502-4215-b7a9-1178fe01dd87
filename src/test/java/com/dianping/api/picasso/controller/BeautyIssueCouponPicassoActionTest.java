package com.dianping.api.picasso.controller;

import com.dianping.mobile.framework.base.datatypes.StatusCode;
import com.dianping.mobile.framework.datatypes.CommonMobileResponse;
import com.dianping.mobile.framework.datatypes.IMobileContext;
import com.dianping.mobile.framework.datatypes.IMobileResponse;
import com.dianping.pay.api.entity.issuecoupon.IssueCouponRequest;
import com.dianping.pay.framework.utils.shopUuid.ShopUuidUtils;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import javax.servlet.http.HttpServletRequest;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertNotNull;
import static org.junit.Assert.assertNull;
import static org.junit.Assert.assertTrue;
import static org.mockito.Mockito.when;

/**
 * <AUTHOR> , 2025/4/2
 */
public class BeautyIssueCouponPicassoActionTest {

    @Mock
    private IMobileContext context;
    @Mock
    private HttpServletRequest request;
    @Mock
    private ShopUuidUtils shopUuidUtils;
    @InjectMocks
    private BeautyIssueCouponPicassoAction action;



    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
        when(context.getRequest()).thenReturn(request);
    }

    /**
     * 测试正常场景
     */
    @Test
    public void testValidateNormalCase() throws Throwable {
        // arrange
        IssueCouponRequest request = new IssueCouponRequest();
        request.setShopIdL(1L);
        // act
        IMobileResponse response = action.validate(request, context);

        // assert
        assertNull(response);
        assertEquals(request.getProductId(), 0);
    }

    /**
     * 测试参数校验失败场景
     */
    @Test
    public void testValidateIllegalArgumentException() throws Throwable {
        // arrange
        IssueCouponRequest request = new IssueCouponRequest();

        // act
        IMobileResponse response = action.validate(request, context);

        // assert
        assertNotNull(response);
        assertTrue(response instanceof CommonMobileResponse);
        assertEquals(StatusCode.PARAMERROR, response.getStatusCode());
    }

    /**
     * 测试productId为0，longProductId有值的场景
     */
    @Test
    public void testValidateProductIdZeroLongProductIdNotNull() throws Throwable {
        // arrange
        IssueCouponRequest request = new IssueCouponRequest();
        request.setProductId(0);
        request.setLongProductId(100L);
        request.setShopIdL(1L); // 确保其他参数有效

        // act
        IMobileResponse response = action.validate(request, context);

        // assert
        assertNull(response);
        assertEquals(request.getProductId(), 100);
        assertEquals(request.getLongProductId().longValue(), 100L);
    }

    /**
     * 测试productId有值，longProductId为null的场景
     */
    @Test
    public void testValidateProductIdNotNullLongProductIdNull() throws Throwable {
        // arrange
        IssueCouponRequest request = new IssueCouponRequest();
        request.setProductId(100);
        request.setLongProductId(null);
        request.setShopIdL(1L); // 确保其他参数有效

        // act
        IMobileResponse response = action.validate(request, context);

        // assert
        assertNull(response);
        assertEquals(request.getProductId(), 100);
        assertEquals(request.getLongProductId().longValue(), 100L);

    }

    /**
     * 测试couponOptionId为0，stringCouponOptionId有值的场景
     */
    @Test
    public void testValidateCouponOptionIdZeroStringCouponOptionIdNotNull() throws Throwable {
        // arrange
        IssueCouponRequest request = new IssueCouponRequest();
        request.setCouponOptionId(0);
        request.setStringCouponOptionId("100");
        request.setShopIdL(1L); // 确保其他参数有效

        // act
        IMobileResponse response = action.validate(request, context);

        // assert
        assertNull(response);
        assertEquals(request.getCouponOptionId(), 100);
        assertEquals(request.getStringCouponOptionId(), "100");
    }

    /**
     * 测试couponOptionId有值，stringCouponOptionId为null的场景
     */
    @Test
    public void testValidateCouponOptionIdNotNullStringCouponOptionIdNull() throws Throwable {
        // arrange
        IssueCouponRequest request = new IssueCouponRequest();
        request.setCouponOptionId(100);
        request.setStringCouponOptionId(null);
        request.setShopIdL(1L); // 确保其他参数有效

        // act
        IMobileResponse response = action.validate(request, context);

        // assert
        assertNull(response);
        assertEquals(request.getCouponOptionId(), 100);
        assertEquals(request.getStringCouponOptionId(), "100");
    }

    /**
     * 测试所有参数无效的场景
     */
    @Test
    public void testValidateAllParamsInvalid() throws Throwable {
        // arrange
        IssueCouponRequest request = new IssueCouponRequest();
        // 设置所有参数为无效值
        request.setShopIdL(0L);
        request.setShopUuid("");
        request.setProductId(0);
        request.setLongProductId(0L);
        request.setShopIdLList(null);

        // act
        IMobileResponse response = action.validate(request, context);

        // assert
        assertNotNull(response);
        assertTrue(response instanceof CommonMobileResponse);
        assertEquals(StatusCode.PARAMERROR, response.getStatusCode());
    }
}
