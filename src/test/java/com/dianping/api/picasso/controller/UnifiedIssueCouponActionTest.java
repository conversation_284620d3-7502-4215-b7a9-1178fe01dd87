package com.dianping.api.picasso.controller;

import com.dianping.api.common.enums.ProductTypeEnum;
import com.dianping.mobile.framework.datatypes.CommonMobileResponse;
import com.dianping.mobile.framework.datatypes.IMobileContext;
import com.dianping.mobile.framework.datatypes.IMobileResponse;
import com.dianping.pay.api.entity.issuecoupon.IssueCouponMsg;
import com.dianping.pay.api.entity.issuecoupon.UnifiedissuecouponRequest;
import com.dianping.pay.framework.utils.shopUuid.ShopUuidUtils;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.test.util.ReflectionTestUtils;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertNotNull;
import static org.junit.Assert.assertNull;
import static org.junit.Assert.assertTrue;
import static org.mockito.Mockito.anyBoolean;
import static org.mockito.Mockito.eq;
import static org.mockito.Mockito.verify;

@RunWith(MockitoJUnitRunner.class)
public class UnifiedIssueCouponActionTest {

    @InjectMocks
    private UnifiedIssueCouponAction unifiedIssueCouponAction;

    @Mock
    private IMobileContext mockContext;

    @Mock
    private HttpServletRequest mockRequest;

    @Mock
    private HttpServletResponse mockResponse;

    @Mock
    private ShopUuidUtils shopUuidUtils;

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
        ReflectionTestUtils.setField(unifiedIssueCouponAction, "shopUuidUtils", shopUuidUtils);
    }

    @Test(expected = NullPointerException.class)
    public void testValidate_NullRequest() {
        unifiedIssueCouponAction.validate(null, mockContext);
    }

    @Test
    public void testValidate_InvalidShopIdOrProductId() {
        UnifiedissuecouponRequest request = new UnifiedissuecouponRequest();

        IMobileResponse response = unifiedIssueCouponAction.validate(request, mockContext);

        assertNotNull(response);
        assertTrue(response instanceof CommonMobileResponse);
        CommonMobileResponse commonResponse = (CommonMobileResponse) response;
        assertTrue(commonResponse.getData() instanceof IssueCouponMsg);
        IssueCouponMsg msg = (IssueCouponMsg) commonResponse.getData();
        assertEquals(401, msg.getErrorCode());
        assertEquals("invalid shopId/productId", msg.getMessage());
    }

    @Test
    public void testValidate_MissingShopIdAndProductId() {
        UnifiedissuecouponRequest request = new UnifiedissuecouponRequest();
        request.setCoupontype(0);

        IMobileResponse response = unifiedIssueCouponAction.validate(request, mockContext);

        assertNotNull(response);
        assertTrue(response instanceof CommonMobileResponse);
        CommonMobileResponse commonResponse = (CommonMobileResponse) response;
        assertTrue(commonResponse.getData() instanceof IssueCouponMsg);
        IssueCouponMsg msg = (IssueCouponMsg) commonResponse.getData();
        assertEquals(401, msg.getErrorCode());
        assertEquals("invalid shopId/productId", msg.getMessage());
    }

    @Test
    public void testValidate_MissingCouponGroupId() {
        UnifiedissuecouponRequest request = new UnifiedissuecouponRequest();
        request.setCoupontype(0);
        request.setShopidL(123L);

        IMobileResponse response = unifiedIssueCouponAction.validate(request, mockContext);

        assertNotNull(response);
        assertTrue(response instanceof CommonMobileResponse);
        CommonMobileResponse commonResponse = (CommonMobileResponse) response;
        assertTrue(commonResponse.getData() instanceof IssueCouponMsg);
        IssueCouponMsg msg = (IssueCouponMsg) commonResponse.getData();
        assertEquals(401, msg.getErrorCode());
        assertEquals(" 券 id 非法", msg.getMessage());
    }

    @Test
    public void testValidate_InvalidProductType() {
        UnifiedissuecouponRequest request = new UnifiedissuecouponRequest();
        request.setCoupontype(0);
        request.setShopidL(123L);
        request.setCoupongroupid(456);
        request.setProductid(789);
        request.setProducttype(99); // 无效的产品类型

        IMobileResponse response = unifiedIssueCouponAction.validate(request, mockContext);

        assertNotNull(response);
        assertTrue(response instanceof CommonMobileResponse);
        CommonMobileResponse commonResponse = (CommonMobileResponse) response;
        assertTrue(commonResponse.getData() instanceof IssueCouponMsg);
        IssueCouponMsg msg = (IssueCouponMsg) commonResponse.getData();
        assertEquals(401, msg.getErrorCode());
        assertEquals("producttype invalid", msg.getMessage());
    }

    @Test
    public void testValidate_InvalidIssueDetailSourceCode() {
        UnifiedissuecouponRequest request = new UnifiedissuecouponRequest();
        request.setCoupontype(0);
        request.setShopidL(123L);
        request.setCoupongroupid(456);
        request.setProductid(789);
        request.setProducttype(ProductTypeEnum.DEAL.code);
        request.setIssueDetailSourceCode(999); // 无效的来源代码

        IMobileResponse response = unifiedIssueCouponAction.validate(request, mockContext);

        assertNotNull(response);
        assertTrue(response instanceof CommonMobileResponse);
        CommonMobileResponse commonResponse = (CommonMobileResponse) response;
        assertTrue(commonResponse.getData() instanceof IssueCouponMsg);
        IssueCouponMsg msg = (IssueCouponMsg) commonResponse.getData();
        assertEquals(401, msg.getErrorCode());
        assertEquals("issuedetailsourcecode invalid", msg.getMessage());
    }

    @Test
    public void testValidate_ValidRequest() {
        UnifiedissuecouponRequest request = new UnifiedissuecouponRequest();
        request.setCoupontype(0);
        request.setShopidL(123L);
        request.setCoupongroupid(456);
        IMobileResponse response = unifiedIssueCouponAction.validate(request, mockContext);

        assertNull(response); // 验证通过时返回null
        verify(shopUuidUtils).prepareUuidInRequest(eq(request), anyBoolean());
    }

    @Test
    public void testValidate_ProductIdUpgrade() {
        UnifiedissuecouponRequest request = new UnifiedissuecouponRequest();
        request.setCoupontype(0);
        request.setShopidL(123L);
        request.setCoupongroupid(456);
        request.setProductid(789);
        request.setLongProductId(null);

        IMobileResponse response = unifiedIssueCouponAction.validate(request, mockContext);

        assertNull(response);
        assertEquals(Long.valueOf(789), request.getLongProductId());
    }

    @Test
    public void testValidate_LongProductIdDowngrade() {
        UnifiedissuecouponRequest request = new UnifiedissuecouponRequest();
        request.setCoupontype(0);
        request.setShopidL(123L);
        request.setCoupongroupid(456);
        request.setProductid(0);
        request.setLongProductId(789L);

        IMobileResponse response = unifiedIssueCouponAction.validate(request, mockContext);

        assertNull(response);
        assertEquals(Integer.valueOf(789), request.getProductid());
    }
}