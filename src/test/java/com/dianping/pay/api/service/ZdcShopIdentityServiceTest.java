package com.dianping.pay.api.service;

import com.dianping.pay.api.entity.issuecoupon.GovernmentSubsidyInfo;
import com.dianping.pay.api.service.impl.ZdcShopIdentityServiceImpl;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.junit.MockitoJUnitRunner;

import static org.junit.Assert.*;

/**
 * ZDC门店标识服务测试
 * <AUTHOR>
 */
@RunWith(MockitoJUnitRunner.class)
public class ZdcShopIdentityServiceTest {

    @InjectMocks
    private ZdcShopIdentityServiceImpl zdcShopIdentityService;

    @Before
    public void setUp() {
        // 初始化测试数据
    }

    @Test
    public void testHasGovernmentSubsidyIdentity() {
        // 测试门店是否有国补标识
        Long shopId = 123456L;
        boolean isMt = true;
        
        boolean result = zdcShopIdentityService.hasGovernmentSubsidyIdentity(shopId, isMt);
        
        // 由于是模拟数据，这里应该返回true
        assertTrue(result);
    }

    @Test
    public void testGetGovernmentSubsidyInfo() {
        // 测试获取国补信息
        Long shopId = 123456L;
        Long userId = 789L;
        boolean isMt = true;
        
        GovernmentSubsidyInfo subsidyInfo = zdcShopIdentityService.getGovernmentSubsidyInfo(shopId, userId, isMt);
        
        // 验证返回结果
        assertNotNull(subsidyInfo);
        assertTrue(subsidyInfo.isShowGovernmentSubsidy());
        assertNotNull(subsidyInfo.getSubsidyTag());
        assertNotNull(subsidyInfo.getSubsidyModule());
        assertEquals("MT_MERCHANT_DETAILS_BANNER", subsidyInfo.getBoothId());
    }

    @Test
    public void testGetGovernmentSubsidyInfoWithInvalidShop() {
        // 测试无效门店ID
        Long shopId = 0L;
        Long userId = 789L;
        boolean isMt = true;
        
        GovernmentSubsidyInfo subsidyInfo = zdcShopIdentityService.getGovernmentSubsidyInfo(shopId, userId, isMt);
        
        // 应该返回null
        assertNull(subsidyInfo);
    }
}
