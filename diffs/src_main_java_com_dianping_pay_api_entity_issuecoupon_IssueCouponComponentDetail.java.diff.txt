diff --git a/src/main/java/com/dianping/pay/api/entity/issuecoupon/IssueCouponComponentDetail.java b/src/main/java/com/dianping/pay/api/entity/issuecoupon/IssueCouponComponentDetail.java
index 29191a30..5b6ac23d 100644
--- a/src/main/java/com/dianping/pay/api/entity/issuecoupon/IssueCouponComponentDetail.java
+++ b/src/main/java/com/dianping/pay/api/entity/issuecoupon/IssueCouponComponentDetail.java
@@ -50,4 +50,10 @@ public class IssueCouponComponentDetail implements Serializable {
     @MobileDo.MobileField(key = 0x2354)
     private List<Integer> inflateExperimentId;
 
+    /**
+     * 国补资格领取信息
+     */
+    @MobileDo.MobileField(key = 0x2fd8)
+    private List<NationalSubsidyInfo> nationalSubsidyInfo;
+
 }
