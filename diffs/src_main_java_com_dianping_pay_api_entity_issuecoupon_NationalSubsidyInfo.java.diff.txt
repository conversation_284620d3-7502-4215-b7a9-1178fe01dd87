diff --git a/src/main/java/com/dianping/pay/api/entity/issuecoupon/NationalSubsidyInfo.java b/src/main/java/com/dianping/pay/api/entity/issuecoupon/NationalSubsidyInfo.java
new file mode 100644
index 00000000..47bc38c4
--- /dev/null
+++ b/src/main/java/com/dianping/pay/api/entity/issuecoupon/NationalSubsidyInfo.java
@@ -0,0 +1,57 @@
+package com.dianping.pay.api.entity.issuecoupon;
+
+import com.dianping.mobile.framework.annotation.MobileDo;
+import com.dianping.mobile.framework.annotation.MobileDo.*;
+import lombok.Data;
+
+import java.io.Serializable;
+
+/**
+ * 国补资格领取信息
+ */
+@MobileDo(id = 0xb934)
+@Data
+public class NationalSubsidyInfo implements Serializable {
+
+    /**
+     * 按钮跳链
+     */
+    @MobileField(key = 0x774e)
+    private String jumpUrl;
+
+    /**
+     * 按钮图片
+     */
+    @MobileField(key = 0x6c48)
+    private String buttonPic;
+
+    /**
+     * 国补优惠比例
+     */
+    @MobileField(key = 0xcf50)
+    private String promo;
+
+    /**
+     * 副标题
+     */
+    @MobileField(key = 0xd894)
+    private String subTitle;
+
+    /**
+     * 标题
+     */
+    @MobileField(key = 0x24cc)
+    private String title;
+
+    /**
+     * 角标
+     */
+    @MobileField(key = 0x3c48)
+    private String icon;
+
+    /**
+     * 领取状态 0未领取，1已领取
+     */
+    @MobileField(key = 0xc851)
+    private int issueStatus;
+}
