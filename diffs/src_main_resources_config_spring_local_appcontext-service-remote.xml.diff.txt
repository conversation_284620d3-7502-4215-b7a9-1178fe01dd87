diff --git a/src/main/resources/config/spring/local/appcontext-service-remote.xml b/src/main/resources/config/spring/local/appcontext-service-remote.xml
index 7e9d7282..b3bfc7e9 100644
--- a/src/main/resources/config/spring/local/appcontext-service-remote.xml
+++ b/src/main/resources/config/spring/local/appcontext-service-remote.xml
@@ -579,4 +579,29 @@
         <property name="timeout" value="200"/>
         <property name="nettyIO" value="true"/>
     </bean>
+
+    <bean id="poiTagDisplayRPCService" class="com.dianping.pigeon.remoting.invoker.config.spring.ReferenceBean" init-method="init">
+        <property name="url" value="http://service.dianping.com/ZDCTagApplyService/poiTagDisplayRPCService_1.0.0" />
+        <property name="interfaceName" value="com.sankuai.zdc.tag.apply.api.PoiTagDisplayRPCService" />
+        <property name="timeout" value="300" />
+        <property name="serialize" value="hessian" />
+        <property name="callType" value="sync" />
+    </bean>
+
+    <bean id="stateSubsidiesOpenService" class="com.meituan.service.mobile.mtthrift.proxy.ThriftClientProxy"
+          destroy-method="destroy">
+        <property name="serviceInterface" value="com.sankuai.statesubsidies.c.thrift.service.StateSubsidiesOpenService"/>
+        <property name="remoteAppkey" value="com.sankuai.statesubsidies.c"/>
+        <property name="filterByServiceName" value="true"/>
+        <property name="timeout" value="300"/>
+        <property name="nettyIO" value="true"/>
+    </bean>
+
+    <bean id="govSubsidyInfoQueryService" class="com.dianping.dpsf.spring.ProxyBeanFactory" init-method="init">
+        <property name="serviceName" value="com.dianping.tpfun.product.api.govsubsidy.service.GovSubsidyInfoQueryService"/>
+        <property name="iface" value="com.dianping.tpfun.product.api.govsubsidy.service.GovSubsidyInfoQueryService"/>
+        <property name="serialize" value="hessian"/>
+        <property name="callMethod" value="sync"/>
+        <property name="timeout" value="3000"/>
+    </bean>
 </beans>
