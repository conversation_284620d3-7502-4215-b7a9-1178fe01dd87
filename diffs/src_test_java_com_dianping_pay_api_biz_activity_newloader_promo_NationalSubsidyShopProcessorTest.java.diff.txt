diff --git a/src/test/java/com/dianping/pay/api/biz/activity/newloader/promo/NationalSubsidyShopProcessorTest.java b/src/test/java/com/dianping/pay/api/biz/activity/newloader/promo/NationalSubsidyShopProcessorTest.java
new file mode 100644
index 00000000..e81d107f
--- /dev/null
+++ b/src/test/java/com/dianping/pay/api/biz/activity/newloader/promo/NationalSubsidyShopProcessorTest.java
@@ -0,0 +1,514 @@
+package com.dianping.pay.api.biz.activity.newloader.promo;
+
+import static org.junit.Assert.*;
+import static org.mockito.Mockito.*;
+import com.sankuai.statesubsidies.c.thrift.constant.ProductCategoryEnum;
+import com.sankuai.statesubsidies.c.thrift.constant.QualificationStatusEnum;
+import com.sankuai.statesubsidies.c.thrift.dto.QualificationInfo;
+import com.sankuai.statesubsidies.c.thrift.response.GetUserQualificationOpenResponse;
+import java.lang.reflect.Method;
+import java.util.ArrayList;
+import java.util.List;
+import java.util.Map;
+
+import org.apache.commons.collections.MapUtils;
+import org.junit.*;
+import org.junit.runner.RunWith;
+import org.junit.runner.RunWith.*;
+import org.mockito.InjectMocks;
+import org.mockito.Mock;
+import org.mockito.junit.*;
+import com.google.common.collect.Lists;
+import com.sankuai.statesubsidies.c.thrift.service.StateSubsidiesOpenService;
+import com.sankuai.zdc.tag.apply.api.PoiTagDisplayRPCService;
+import com.sankuai.zdc.tag.apply.dto.FindSceneDisplayTagRequest;
+import com.dianping.pay.api.biz.activity.newloader.dto.CouponActivityContext;
+import com.sankuai.statesubsidies.c.thrift.constant.BizLineEnum;
+import com.sankuai.statesubsidies.c.thrift.request.CommonRequest;
+import com.sankuai.statesubsidies.c.thrift.request.GetUserQualificationOpenRequest;
+import org.mockito.junit.MockitoJUnitRunner;
+import java.util.HashMap;
+import org.mockito.MockitoAnnotations;
+import static org.mockito.ArgumentMatchers.any;
+import static org.mockito.ArgumentMatchers.anyString;
+import static org.mockito.ArgumentMatchers.eq;
+import com.dianping.lion.client.Lion;
+import com.dianping.pay.api.beans.NationalSubsidyInfoDO;
+import com.dianping.pay.api.util.LionConstants;
+import java.util.concurrent.Future;
+import java.util.concurrent.TimeUnit;
+import java.util.concurrent.TimeoutException;
+import org.slf4j.Logger;
+import com.dianping.mobile.framework.datatypes.IMobileContext;
+import java.lang.reflect.Field;
+import java.util.concurrent.Callable;
+import java.util.concurrent.ExecutorService;
+
+@RunWith(MockitoJUnitRunner.class)
+public class NationalSubsidyShopProcessorTest {
+
+    @InjectMocks
+    private NationalSubsidyShopProcessor processor;
+
+    @Mock
+    private PoiTagDisplayRPCService poiTagDisplayRPCService;
+
+    @Mock
+    private StateSubsidiesOpenService.Iface stateSubsidiesOpenService;
+
+    @Mock
+    private Logger log;
+
+    @Mock
+    private Future<NationalSubsidyInfoDO> future;
+
+    private Method getConvert2UserQualMapMethod() throws Exception {
+        Method method = NationalSubsidyShopProcessor.class.getDeclaredMethod("convert2UserQualMap", GetUserQualificationOpenResponse.class);
+        method.setAccessible(true);
+        return method;
+    }
+
+    /**
+     * Test case for null response
+     */
+    @Test
+    public void testConvert2UserQualMap_NullResponse() throws Throwable {
+        // arrange
+        GetUserQualificationOpenResponse response = null;
+        // act
+        Map<Integer, Integer> result = (Map<Integer, Integer>) getConvert2UserQualMapMethod().invoke(processor, response);
+        // assert
+        assertTrue(MapUtils.isEmpty(result));
+    }
+
+    /**
+     * Test case for non-zero response code
+     */
+    @Test
+    public void testConvert2UserQualMap_NonZeroCode() throws Throwable {
+        // arrange
+        GetUserQualificationOpenResponse response = new GetUserQualificationOpenResponse();
+        response.setCode(1);
+        // act
+        Map<Integer, Integer> result = (Map<Integer, Integer>) getConvert2UserQualMapMethod().invoke(processor, response);
+        // assert
+        assertTrue(MapUtils.isEmpty(result));
+    }
+
+    /**
+     * Test case for empty qualification info list
+     */
+    @Test
+    public void testConvert2UserQualMap_EmptyQualificationInfo() throws Throwable {
+        // arrange
+        GetUserQualificationOpenResponse response = new GetUserQualificationOpenResponse();
+        response.setCode(0);
+        response.setQualificationInfo(new ArrayList<>());
+        // act
+        Map<Integer, Integer> result = (Map<Integer, Integer>) getConvert2UserQualMapMethod().invoke(processor, response);
+        // assert
+        assertTrue(MapUtils.isEmpty(result));
+    }
+
+    /**
+     * Test case for null qualification info list
+     */
+    @Test
+    public void testConvert2UserQualMap_NullQualificationInfo() throws Throwable {
+        // arrange
+        GetUserQualificationOpenResponse response = new GetUserQualificationOpenResponse();
+        response.setCode(0);
+        response.setQualificationInfo(null);
+        // act
+        Map<Integer, Integer> result = (Map<Integer, Integer>) getConvert2UserQualMapMethod().invoke(processor, response);
+        // assert
+        assertTrue(MapUtils.isEmpty(result));
+    }
+
+    /**
+     * Test case for valid qualification info
+     */
+    @Test
+    public void testConvert2UserQualMap_ValidQualificationInfo() throws Throwable {
+        // arrange
+        GetUserQualificationOpenResponse response = new GetUserQualificationOpenResponse();
+        response.setCode(0);
+        ArrayList<QualificationInfo> qualInfoList = new ArrayList<>();
+        QualificationInfo info = new QualificationInfo();
+        info.setProductCategory(ProductCategoryEnum.TV);
+        info.setStatus(QualificationStatusEnum.BIND);
+        qualInfoList.add(info);
+        response.setQualificationInfo(qualInfoList);
+        // act
+        Map<Integer, Integer> result = (Map<Integer, Integer>) getConvert2UserQualMapMethod().invoke(processor, response);
+        // assert
+        assertNotNull(result);
+        assertEquals(1, result.size());
+        assertEquals(Integer.valueOf(QualificationStatusEnum.BIND.getValue()), result.get(ProductCategoryEnum.TV.getValue()));
+    }
+
+    /**
+     * Test case for qualification info list containing null elements
+     */
+    @Test
+    public void testConvert2UserQualMap_NullElementInQualificationInfo() throws Throwable {
+        // arrange
+        GetUserQualificationOpenResponse response = new GetUserQualificationOpenResponse();
+        response.setCode(0);
+        ArrayList<QualificationInfo> qualInfoList = new ArrayList<>();
+        qualInfoList.add(null);
+        QualificationInfo info = new QualificationInfo();
+        info.setProductCategory(ProductCategoryEnum.TV);
+        info.setStatus(QualificationStatusEnum.BIND);
+        qualInfoList.add(info);
+        response.setQualificationInfo(qualInfoList);
+        // act
+        Map<Integer, Integer> result = (Map<Integer, Integer>) getConvert2UserQualMapMethod().invoke(processor, response);
+        // assert
+        assertNotNull(result);
+        assertEquals(1, result.size());
+        assertEquals(Integer.valueOf(QualificationStatusEnum.BIND.getValue()), result.get(ProductCategoryEnum.TV.getValue()));
+    }
+
+    private FindSceneDisplayTagRequest invokeBuildZDCTagRequest(long mtShopIdL) throws Exception {
+        Method method = NationalSubsidyShopProcessor.class.getDeclaredMethod("buildZDCTagRequest", long.class);
+        method.setAccessible(true);
+        return (FindSceneDisplayTagRequest) method.invoke(processor, mtShopIdL);
+    }
+
+    @Test
+    public void testBuildZDCTagRequest_WithPositiveShopId() throws Throwable {
+        // arrange
+        long mtShopIdL = 123456789L;
+        // act
+        FindSceneDisplayTagRequest result = invokeBuildZDCTagRequest(mtShopIdL);
+        // assert
+        assertNotNull("Request should not be null", result);
+        assertEquals("BizCode should match expected value", "MT_MERCHANT_DETAILS_BANNER", result.getBizCode());
+        assertNotNull("ShopIds list should not be null", result.getShopIds());
+        assertEquals("ShopIds list should contain exactly one element", 1, result.getShopIds().size());
+        assertEquals("ShopId should match input value", mtShopIdL, result.getShopIds().get(0).longValue());
+    }
+
+    @Test
+    public void testBuildZDCTagRequest_WithZeroShopId() throws Throwable {
+        // arrange
+        long mtShopIdL = 0L;
+        // act
+        FindSceneDisplayTagRequest result = invokeBuildZDCTagRequest(mtShopIdL);
+        // assert
+        assertNotNull("Request should not be null", result);
+        assertEquals("BizCode should match expected value", "MT_MERCHANT_DETAILS_BANNER", result.getBizCode());
+        assertNotNull("ShopIds list should not be null", result.getShopIds());
+        assertEquals("ShopIds list should contain exactly one element", 1, result.getShopIds().size());
+        assertEquals("ShopId should be zero", 0L, result.getShopIds().get(0).longValue());
+    }
+
+    @Test
+    public void testBuildZDCTagRequest_WithNegativeShopId() throws Throwable {
+        // arrange
+        long mtShopIdL = -123L;
+        // act
+        FindSceneDisplayTagRequest result = invokeBuildZDCTagRequest(mtShopIdL);
+        // assert
+        assertNotNull("Request should not be null", result);
+        assertEquals("BizCode should match expected value", "MT_MERCHANT_DETAILS_BANNER", result.getBizCode());
+        assertNotNull("ShopIds list should not be null", result.getShopIds());
+        assertEquals("ShopIds list should contain exactly one element", 1, result.getShopIds().size());
+        assertEquals("ShopId should match input negative value", -123L, result.getShopIds().get(0).longValue());
+    }
+
+    @Test
+    public void testBuildZDCTagRequest_WithMaxLongValue() throws Throwable {
+        // arrange
+        long mtShopIdL = Long.MAX_VALUE;
+        // act
+        FindSceneDisplayTagRequest result = invokeBuildZDCTagRequest(mtShopIdL);
+        // assert
+        assertNotNull("Request should not be null", result);
+        assertEquals("BizCode should match expected value", "MT_MERCHANT_DETAILS_BANNER", result.getBizCode());
+        assertNotNull("ShopIds list should not be null", result.getShopIds());
+        assertEquals("ShopIds list should contain exactly one element", 1, result.getShopIds().size());
+        assertEquals("ShopId should match maximum long value", Long.MAX_VALUE, result.getShopIds().get(0).longValue());
+    }
+
+    private GetUserQualificationOpenRequest invokeBuildUserQualRequest(CouponActivityContext promoCtx) throws Exception {
+        Method method = NationalSubsidyShopProcessor.class.getDeclaredMethod("buildUserQualRequest", CouponActivityContext.class);
+        method.setAccessible(true);
+        try {
+            return (GetUserQualificationOpenRequest) method.invoke(processor, promoCtx);
+        } catch (Exception e) {
+            if (e.getCause() != null) {
+                throw (Exception) e.getCause();
+            }
+            throw e;
+        }
+    }
+
+    @Test
+    public void testBuildUserQualRequest_WithAllFields() throws Throwable {
+        // arrange
+        CouponActivityContext promoCtx = mock(CouponActivityContext.class);
+        when(promoCtx.getMtUserId()).thenReturn(123L);
+        when(promoCtx.getDpid()).thenReturn("test-dpid");
+        when(promoCtx.getMtCityId()).thenReturn(1);
+        // act
+        GetUserQualificationOpenRequest result = invokeBuildUserQualRequest(promoCtx);
+        // assert
+        assertNotNull(result);
+        assertEquals(BizLineEnum.FU_WU_LING_SHOU, result.getBizLine());
+        CommonRequest commonRequest = result.getCommonRequest();
+        assertNotNull(commonRequest);
+        assertEquals(123L, commonRequest.getUserId());
+        assertEquals("test-dpid", commonRequest.getUuid());
+        assertEquals(1, commonRequest.getMtCityId());
+    }
+
+    @Test(expected = NullPointerException.class)
+    public void testBuildUserQualRequest_NullPromoCtx() throws Throwable {
+        // act
+        invokeBuildUserQualRequest(null);
+    }
+
+    @Test
+    public void testBuildUserQualRequest_NullMtUserId() throws Throwable {
+        // arrange
+        CouponActivityContext promoCtx = mock(CouponActivityContext.class);
+        when(promoCtx.getMtUserId()).thenReturn(0L);
+        when(promoCtx.getDpid()).thenReturn("test-dpid");
+        when(promoCtx.getMtCityId()).thenReturn(1);
+        // act
+        GetUserQualificationOpenRequest result = invokeBuildUserQualRequest(promoCtx);
+        // assert
+        assertNotNull(result);
+        CommonRequest commonRequest = result.getCommonRequest();
+        assertEquals(0L, commonRequest.getUserId());
+        assertEquals("test-dpid", commonRequest.getUuid());
+        assertEquals(1, commonRequest.getMtCityId());
+    }
+
+    @Test
+    public void testBuildUserQualRequest_NullDpid() throws Throwable {
+        // arrange
+        CouponActivityContext promoCtx = mock(CouponActivityContext.class);
+        when(promoCtx.getMtUserId()).thenReturn(123L);
+        when(promoCtx.getDpid()).thenReturn(null);
+        when(promoCtx.getMtCityId()).thenReturn(1);
+        // act
+        GetUserQualificationOpenRequest result = invokeBuildUserQualRequest(promoCtx);
+        // assert
+        assertNotNull(result);
+        CommonRequest commonRequest = result.getCommonRequest();
+        assertEquals(123L, commonRequest.getUserId());
+        assertNull(commonRequest.getUuid());
+        assertEquals(1, commonRequest.getMtCityId());
+    }
+
+    @Test
+    public void testBuildUserQualRequest_ZeroMtCityId() throws Throwable {
+        // arrange
+        CouponActivityContext promoCtx = mock(CouponActivityContext.class);
+        when(promoCtx.getMtUserId()).thenReturn(123L);
+        when(promoCtx.getDpid()).thenReturn("test-dpid");
+        when(promoCtx.getMtCityId()).thenReturn(0);
+        // act
+        GetUserQualificationOpenRequest result = invokeBuildUserQualRequest(promoCtx);
+        // assert
+        assertNotNull(result);
+        CommonRequest commonRequest = result.getCommonRequest();
+        assertEquals(123L, commonRequest.getUserId());
+        assertEquals("test-dpid", commonRequest.getUuid());
+        assertEquals(0, commonRequest.getMtCityId());
+    }
+
+    @Before
+    public void setUp() {
+        MockitoAnnotations.initMocks(this);
+    }
+
+    private Map<Integer, Integer> invokePrivateMethod(String methodName, List<String> userQualCats, Map<String, Integer> input) throws Exception {
+        Method method = NationalSubsidyShopProcessor.class.getDeclaredMethod(methodName, List.class, Map.class);
+        method.setAccessible(true);
+        return (Map<Integer, Integer>) method.invoke(processor, userQualCats, input);
+    }
+
+    @Test
+    public void testConvert2Category2promoMap_NullInput() throws Throwable {
+        Map<String, Integer> input = null;
+        Map<Integer, Integer> result = invokePrivateMethod("convert2Category2promoMap", Lists.newArrayList(), input);
+        assertTrue(MapUtils.isEmpty(result));
+        verifyNoMoreInteractions(poiTagDisplayRPCService);
+        verifyNoMoreInteractions(stateSubsidiesOpenService);
+    }
+
+    @Test
+    public void testConvert2Category2promoMap_EmptyInput() throws Throwable {
+        Map<String, Integer> input = new HashMap<>();
+        Map<Integer, Integer> result = invokePrivateMethod("convert2Category2promoMap", Lists.newArrayList(), input);
+        assertTrue(MapUtils.isEmpty(result));
+        verifyNoMoreInteractions(poiTagDisplayRPCService);
+        verifyNoMoreInteractions(stateSubsidiesOpenService);
+    }
+
+    @Test
+    public void testConvert2Category2promoMap_ValidInput() throws Throwable {
+        Map<String, Integer> input = new HashMap<>();
+        input.put("123", 456);
+        input.put("789", 101);
+        Map<Integer, Integer> result = invokePrivateMethod("convert2Category2promoMap", Lists.newArrayList("123", "789"), input);
+        assertNotNull(result);
+        assertEquals(2, result.size());
+        assertEquals(Integer.valueOf(456), result.get(123));
+        assertEquals(Integer.valueOf(101), result.get(789));
+        verifyNoMoreInteractions(poiTagDisplayRPCService);
+        verifyNoMoreInteractions(stateSubsidiesOpenService);
+    }
+
+    @Test
+    public void testLoadPromo_WhenFutureReturnsSuccessfully() throws Throwable {
+        // arrange
+        CouponActivityContext promoCtx = new CouponActivityContext();
+        NationalSubsidyInfoDO expectedResult = new NationalSubsidyInfoDO();
+        when(future.get(1500, TimeUnit.MILLISECONDS)).thenReturn(expectedResult);
+        promoCtx.setNationalSubsidyInfoDOFuture(future);
+        // act
+        processor.loadPromo(promoCtx);
+        // assert
+        verify(future).get(1500, TimeUnit.MILLISECONDS);
+        assertEquals(expectedResult, promoCtx.getNationalSubsidyInfoDO());
+    }
+
+    @Test
+    public void testLoadPromo_WhenFutureTimesOut() throws Throwable {
+        // arrange
+        CouponActivityContext promoCtx = new CouponActivityContext();
+        when(future.get(1500, TimeUnit.MILLISECONDS)).thenThrow(new TimeoutException());
+        promoCtx.setNationalSubsidyInfoDOFuture(future);
+        // act
+        processor.loadPromo(promoCtx);
+        // assert
+        verify(future).get(1500, TimeUnit.MILLISECONDS);
+        assertNotNull(promoCtx.getNationalSubsidyInfoDO());
+        assertTrue(promoCtx.getNationalSubsidyInfoDO() instanceof NationalSubsidyInfoDO);
+    }
+
+    @Test
+    public void testLoadPromo_WhenFutureThrowsException() throws Throwable {
+        // arrange
+        CouponActivityContext promoCtx = new CouponActivityContext();
+        when(future.get(1500, TimeUnit.MILLISECONDS)).thenThrow(new RuntimeException("Test exception"));
+        promoCtx.setNationalSubsidyInfoDOFuture(future);
+        // act
+        processor.loadPromo(promoCtx);
+        // assert
+        verify(future).get(1500, TimeUnit.MILLISECONDS);
+        assertNotNull(promoCtx.getNationalSubsidyInfoDO());
+        assertTrue(promoCtx.getNationalSubsidyInfoDO() instanceof NationalSubsidyInfoDO);
+    }
+
+    @Test
+    public void testLoadPromo_WhenFutureIsNull() throws Throwable {
+        // arrange
+        CouponActivityContext promoCtx = new CouponActivityContext();
+        promoCtx.setNationalSubsidyInfoDOFuture(null);
+        // act
+        processor.loadPromo(promoCtx);
+        // assert
+        verifyZeroInteractions(future);
+        assertNull(promoCtx.getNationalSubsidyInfoDO());
+    }
+
+    @Test
+    public void testPrePare_WhenLionConfigFalseAndNotMt() throws Throwable {
+        // arrange
+        CouponActivityContext promoCtx = mock(CouponActivityContext.class);
+        when(promoCtx.isNeedNationalSubsidy()).thenReturn(true);
+        when(promoCtx.isMt()).thenReturn(false);
+        // act
+        processor.prePare(promoCtx, mock(IMobileContext.class));
+        // assert
+        verify(promoCtx, never()).setNationalSubsidyInfoDOFuture(any());
+    }
+
+    @Test
+    public void testPrePare_WhenMtShopIdLInvalid() throws Throwable {
+        // arrange
+        CouponActivityContext promoCtx = mock(CouponActivityContext.class);
+        when(promoCtx.isNeedNationalSubsidy()).thenReturn(true);
+        when(promoCtx.getMtShopIdL()).thenReturn(0L);
+        when(promoCtx.isMt()).thenReturn(true);
+        // act
+        processor.prePare(promoCtx, mock(IMobileContext.class));
+        // assert
+        verify(promoCtx, never()).setNationalSubsidyInfoDOFuture(any());
+    }
+
+    @Test
+    public void testPrePare_WhenMtUserIdInvalid() throws Throwable {
+        // arrange
+        CouponActivityContext promoCtx = mock(CouponActivityContext.class);
+        when(promoCtx.isNeedNationalSubsidy()).thenReturn(true);
+        when(promoCtx.getMtShopIdL()).thenReturn(1L);
+        when(promoCtx.getMtUserId()).thenReturn(0L);
+        when(promoCtx.isMt()).thenReturn(true);
+        // act
+        processor.prePare(promoCtx, mock(IMobileContext.class));
+        // assert
+        verify(promoCtx, never()).setNationalSubsidyInfoDOFuture(any());
+    }
+
+    @Test
+    public void testPrePare_WhenMtCityIdInvalid() throws Throwable {
+        // arrange
+        CouponActivityContext promoCtx = mock(CouponActivityContext.class);
+        when(promoCtx.isNeedNationalSubsidy()).thenReturn(true);
+        when(promoCtx.getMtShopIdL()).thenReturn(1L);
+        when(promoCtx.getMtUserId()).thenReturn(1L);
+        when(promoCtx.getMtCityId()).thenReturn(0);
+        when(promoCtx.isMt()).thenReturn(true);
+        // act
+        processor.prePare(promoCtx, mock(IMobileContext.class));
+        // assert
+        verify(promoCtx, never()).setNationalSubsidyInfoDOFuture(any());
+    }
+
+    @Test
+    @SuppressWarnings("unchecked")
+    public void testPrePare_Success() throws Throwable {
+        // arrange
+        CouponActivityContext promoCtx = mock(CouponActivityContext.class);
+        when(promoCtx.isNeedNationalSubsidy()).thenReturn(true);
+        when(promoCtx.getMtShopIdL()).thenReturn(1L);
+        when(promoCtx.getMtUserId()).thenReturn(1L);
+        when(promoCtx.getMtCityId()).thenReturn(1);
+        when(promoCtx.isMt()).thenReturn(true);
+        // Create a mock Future that will be returned by the ExecutorService
+        Future<NationalSubsidyInfoDO> mockFuture = mock(Future.class);
+        // We can't mock the static ExecutorService directly, but we can verify the result
+        // was set on the context
+        doNothing().when(promoCtx).setNationalSubsidyInfoDOFuture(any());
+        // act
+        processor.prePare(promoCtx, mock(IMobileContext.class));
+        // assert
+        verify(promoCtx).setNationalSubsidyInfoDOFuture(any());
+    }
+
+    @Test
+    public void testPrePare_WhenException() throws Throwable {
+        // arrange
+        CouponActivityContext promoCtx = mock(CouponActivityContext.class);
+        when(promoCtx.isNeedNationalSubsidy()).thenReturn(true);
+        when(promoCtx.getMtShopIdL()).thenReturn(1L);
+        when(promoCtx.getMtUserId()).thenReturn(1L);
+        when(promoCtx.getMtCityId()).thenReturn(1);
+        when(promoCtx.isMt()).thenReturn(true);
+        // Simulate an exception
+        RuntimeException testException = new RuntimeException("Test Exception");
+        doThrow(testException).when(promoCtx).setNationalSubsidyInfoDOFuture(any());
+        // act & assert
+        // The method should not throw exception even when exception occurs internally
+        processor.prePare(promoCtx, mock(IMobileContext.class));
+        // Verify the method was called and exception was handled
+        verify(promoCtx).setNationalSubsidyInfoDOFuture(any());
+    }
+}
\ No newline at end of file
