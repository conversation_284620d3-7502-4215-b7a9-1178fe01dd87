diff --git a/src/main/java/com/dianping/pay/api/biz/activity/newloader/dto/CouponActivityContext.java b/src/main/java/com/dianping/pay/api/biz/activity/newloader/dto/CouponActivityContext.java
index 2b3b4776..bc9ce836 100644
--- a/src/main/java/com/dianping/pay/api/biz/activity/newloader/dto/CouponActivityContext.java
+++ b/src/main/java/com/dianping/pay/api/biz/activity/newloader/dto/CouponActivityContext.java
@@ -117,6 +117,8 @@ public class CouponActivityContext {
     private boolean needTopAdResourcePromotion;
     //是否返回神会员券
     private boolean needMagicalMemberCoupon;
+    //是否返回国家补贴
+    private boolean needNationalSubsidy;
 
     //点评神劵灰度结果
     private boolean dpMmcGrayControlPaas;
@@ -147,6 +149,7 @@ public class CouponActivityContext {
     private Future<MagicalMemberTagRawDO> magicalMemberTagRawDOFuture;
     //left:未领，right：已领
     private Future<Pair<List<CouponDesc>, List<CouponDesc>>> merchantAccountCouponPromotionFuture;
+    private Future<NationalSubsidyInfoDO> nationalSubsidyInfoDOFuture;
 
 
     //存放future get后的信息
@@ -169,6 +172,7 @@ public class CouponActivityContext {
     private PriceDisplayDTO priceDisplayDTO;
     private MagicalMemberTagRawDO magicalMemberTagRawDO;
     private Pair<List<CouponDesc>, List<CouponDesc>> merchantAccountCouponPromotion;
+    private NationalSubsidyInfoDO nationalSubsidyInfoDO;
 
     //价格力优化--三个优惠请求
     private PromotionDTOResult proxyReducePromotionResult;
