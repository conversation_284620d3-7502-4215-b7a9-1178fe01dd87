diff --git a/src/main/java/com/dianping/pay/api/beans/NationalSubsidyInfoDO.java b/src/main/java/com/dianping/pay/api/beans/NationalSubsidyInfoDO.java
new file mode 100644
index 00000000..79a08c9e
--- /dev/null
+++ b/src/main/java/com/dianping/pay/api/beans/NationalSubsidyInfoDO.java
@@ -0,0 +1,21 @@
+package com.dianping.pay.api.beans;
+
+import com.meituan.servicecatalog.api.annotations.FieldDoc;
+import lombok.Data;
+
+import java.io.Serializable;
+import java.util.Map;
+
+@Data
+public class NationalSubsidyInfoDO implements Serializable {
+
+    @FieldDoc(
+            description = "用户国补资格信息映射"
+    )
+    private Map<Integer, Integer> userQualMap;
+
+    @FieldDoc(
+            description = "闪购类目到补贴比例映射"
+    )
+    private Map<Integer, Integer> category2promoMap;
+}
