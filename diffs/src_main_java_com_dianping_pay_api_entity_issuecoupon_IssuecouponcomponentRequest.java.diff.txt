diff --git a/src/main/java/com/dianping/pay/api/entity/issuecoupon/IssuecouponcomponentRequest.java b/src/main/java/com/dianping/pay/api/entity/issuecoupon/IssuecouponcomponentRequest.java
index 91f0282d..bd1b4910 100644
--- a/src/main/java/com/dianping/pay/api/entity/issuecoupon/IssuecouponcomponentRequest.java
+++ b/src/main/java/com/dianping/pay/api/entity/issuecoupon/IssuecouponcomponentRequest.java
@@ -99,6 +99,12 @@ public class IssuecouponcomponentRequest implements IMobileRequest {
     @MobileRequest.Param(name = "needmagicalmembercoupon")
     private Integer needmagicalmembercoupon;
 
+    /**
+     * 是否需要国家补贴，1查，0&其他不查
+     */
+    @MobileRequest.Param(name = "neednationalsubsidy")
+    private Integer neednationalsubsidy;
+
     /**
      * 是否要分享券，休娱不要，传0
      */
