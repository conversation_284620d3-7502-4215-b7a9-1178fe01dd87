diff --git a/src/main/java/com/dianping/pay/api/util/LionConstants.java b/src/main/java/com/dianping/pay/api/util/LionConstants.java
index 23808922..15fe9102 100644
--- a/src/main/java/com/dianping/pay/api/util/LionConstants.java
+++ b/src/main/java/com/dianping/pay/api/util/LionConstants.java
@@ -409,4 +409,44 @@ public class LionConstants {
 
     public static final String AB_TEST_CONFIG = "mapi-pay-promo-web.ab.test.config";
 
+    /**
+     * 是否支持点评侧查询全国补贴，默认不支持，false
+     */
+    public static final String NATIONAL_SUBSIDY_IS_SUPPORT_DP = "mapi-pay-promo-web.nationalSubsidy.isSupportDp";
+
+    /**
+     * queryNationalSubsidyInfo超时时间
+     */
+    public static final String QUERY_NATIONAL_SUBSIDY_INFO_TIMEOUT = "mapi-pay-promo-web.queryNationalSubsidyInfo.timeout";
+
+    /**
+     * 国补城市列表
+     */
+    public static final String NATIONAL_SUBSIDY_CITYID_LIST = "mapi-pay-promo-web.nationalSubsidy.cityIdList";
+
+    /**
+     * 国补补贴比例查询切供应链开关
+     */
+    public static final String NATIONAL_SUBSIDY_PROMO_SWITCH_SUPPLY_CHAIN = "mapi-pay-promo-web.nationalSubsidy.switchSupplyChain";
+
+    /**
+     * 闪购商品类目映射补贴比例
+     */
+    public static final String NATIONAL_SUBSIDY_SHANGOU_PRODUCT_CAT_2_PROMO_MAP = "mapi-pay-promo-web.nationalSubsidy.shangouProductCat2PromoMap";
+
+    /**
+     * 国补会场跳转链接，区分平台
+     */
+    public static final String NATIONAL_SUBSIDY_JUMP_URL_MAP = "mapi-pay-promo-web.nationalSubsidy.jumpUrlMap";
+
+    /**
+     * 国补卡片副标题
+     */
+    public static final String NATIONAL_SUBSIDY_CARD_SUBTITLE = "mapi-pay-promo-web.nationalSubsidy.cardSubtitle";
+
+    /**
+     * 国补卡片按钮图片
+     */
+    public static final String NATIONAL_SUBSIDY_CARD_BUTTON_PIC_MAP = "mapi-pay-promo-web.nationalSubsidy.cardButtonPicMap";
+
 }
