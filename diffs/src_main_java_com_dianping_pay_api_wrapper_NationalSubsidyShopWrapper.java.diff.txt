diff --git a/src/main/java/com/dianping/pay/api/wrapper/NationalSubsidyShopWrapper.java b/src/main/java/com/dianping/pay/api/wrapper/NationalSubsidyShopWrapper.java
new file mode 100644
index 00000000..82f212ea
--- /dev/null
+++ b/src/main/java/com/dianping/pay/api/wrapper/NationalSubsidyShopWrapper.java
@@ -0,0 +1,76 @@
+package com.dianping.pay.api.wrapper;
+
+import com.dianping.cat.Cat;
+import com.dianping.pay.api.biz.activity.newloader.dto.CouponActivityContext;
+import com.dianping.pay.api.util.JsonUtils;
+import com.dianping.tpfun.product.api.common.IResponse;
+import com.dianping.tpfun.product.api.govsubsidy.model.GovSubsidyInfo;
+import com.dianping.tpfun.product.api.govsubsidy.request.QueryGovSubsidyInfoRequest;
+import com.dianping.tpfun.product.api.govsubsidy.service.GovSubsidyInfoQueryService;
+import com.sankuai.athena.biz.Response;
+import com.sankuai.statesubsidies.c.thrift.request.GetUserQualificationOpenRequest;
+import com.sankuai.statesubsidies.c.thrift.response.GetUserQualificationOpenResponse;
+import com.sankuai.statesubsidies.c.thrift.service.StateSubsidiesOpenService;
+import com.sankuai.zdc.tag.apply.api.PoiTagDisplayRPCService;
+import com.sankuai.zdc.tag.apply.dto.DisplayTagDto;
+import com.sankuai.zdc.tag.apply.dto.FindSceneDisplayTagRequest;
+import lombok.extern.slf4j.Slf4j;
+import org.springframework.beans.factory.annotation.Autowired;
+import org.springframework.stereotype.Service;
+
+import java.util.List;
+import java.util.Map;
+
+@Service
+@Slf4j
+public class NationalSubsidyShopWrapper {
+
+    @Autowired
+    private PoiTagDisplayRPCService poiTagDisplayRPCService;
+
+    @Autowired
+    private StateSubsidiesOpenService.Iface stateSubsidiesOpenService;
+
+    @Autowired
+    private GovSubsidyInfoQueryService govSubsidyInfoQueryService;
+
+    public Response<Map<Long, List<DisplayTagDto>>> queryZDC(CouponActivityContext promoCtx, FindSceneDisplayTagRequest zdcTagRequest, String processorName) {
+        try {
+            // 请求ZDC流量打点
+            Cat.logEvent(processorName, "queryZDC");
+            log.info("{}#queryZDC request: {}", processorName, JsonUtils.toJson(zdcTagRequest));
+            Response<Map<Long, List<DisplayTagDto>>> shopId2TagsRes = poiTagDisplayRPCService.findSceneDisplayTagOfMt(zdcTagRequest);
+            log.info("{}#queryZDC request: {}, response: {}", processorName, JsonUtils.toJson(zdcTagRequest), JsonUtils.toJson(shopId2TagsRes));
+            return shopId2TagsRes;
+        } catch (Exception e) {
+            log.error("{}#queryNationalSubsidyInfo error, ctx: {}", processorName, promoCtx, e);
+            return null;
+        }
+    }
+
+    public GetUserQualificationOpenResponse queryShangou(CouponActivityContext promoCtx, GetUserQualificationOpenRequest getUserQualificationOpenRequest, String processorName) {
+        try {
+            // 请求闪购流量打点
+            Cat.logEvent(processorName, "queryShangou");
+            log.info("{}#queryShangou request: {}", processorName, JsonUtils.toJson(getUserQualificationOpenRequest));
+            GetUserQualificationOpenResponse userQualRes = stateSubsidiesOpenService.getUserQualificationOpen(getUserQualificationOpenRequest);
+            log.info("{}#queryShangou request: {}, response: {}", processorName, JsonUtils.toJson(getUserQualificationOpenRequest), JsonUtils.toJson(userQualRes));
+            return userQualRes;
+        } catch (Exception e) {
+            log.error("{}#queryNationalSubsidyInfo error, ctx: {}", processorName, promoCtx, e);
+            return null;
+        }
+    }
+
+    public IResponse<Map<String, GovSubsidyInfo>> querySupplyChain(CouponActivityContext promoCtx, QueryGovSubsidyInfoRequest govSubsidyInfoQueryRequest, String processorName) {
+        try {
+            // 请求供给链流量打点
+            Cat.logEvent(processorName, "querySupplyChain");
+            log.info("{}#querySupplyChain request: {}", processorName, JsonUtils.toJson(govSubsidyInfoQueryRequest));
+            return govSubsidyInfoQueryService.batchQueryGovSubsidyInfoByInstashoppingCategory(govSubsidyInfoQueryRequest);
+        } catch (Exception e) {
+            log.error("{}#querySupplyChain error, ctx: {}", processorName, promoCtx, e);
+            return null;
+        }
+    }
+}
