diff --git a/src/main/java/com/dianping/pay/api/biz/activity/newloader/promo/NationalSubsidyShopProcessor.java b/src/main/java/com/dianping/pay/api/biz/activity/newloader/promo/NationalSubsidyShopProcessor.java
new file mode 100644
index 00000000..67312d8c
--- /dev/null
+++ b/src/main/java/com/dianping/pay/api/biz/activity/newloader/promo/NationalSubsidyShopProcessor.java
@@ -0,0 +1,190 @@
+package com.dianping.pay.api.biz.activity.newloader.promo;
+
+import com.dianping.mobile.framework.datatypes.IMobileContext;
+import com.dianping.pay.api.beans.NationalSubsidyInfoDO;
+import com.dianping.pay.api.biz.activity.newloader.dto.CouponActivityContext;
+import com.dianping.pay.api.util.JsonUtils;
+import com.dianping.pay.api.util.LionConstants;
+import com.dianping.pay.api.wrapper.NationalSubsidyShopWrapper;
+import com.dianping.tpfun.product.api.common.IResponse;
+import com.dianping.tpfun.product.api.govsubsidy.model.GovSubsidyInfo;
+import com.dianping.tpfun.product.api.govsubsidy.request.QueryGovSubsidyInfoRequest;
+import com.google.common.collect.Lists;
+import com.google.common.collect.Maps;
+import com.sankuai.athena.biz.Response;
+import com.sankuai.statesubsidies.c.thrift.constant.BizLineEnum;
+import com.sankuai.statesubsidies.c.thrift.request.CommonRequest;
+import com.sankuai.statesubsidies.c.thrift.request.GetUserQualificationOpenRequest;
+import com.sankuai.statesubsidies.c.thrift.response.GetUserQualificationOpenResponse;
+import com.sankuai.zdc.tag.apply.dto.DisplayTagDto;
+import com.sankuai.zdc.tag.apply.dto.FindSceneDisplayTagRequest;
+import lombok.extern.slf4j.Slf4j;
+import org.apache.commons.lang3.StringUtils;
+import org.springframework.beans.factory.annotation.Autowired;
+import org.springframework.stereotype.Component;
+import com.dianping.lion.client.Lion;
+import org.apache.commons.collections.MapUtils;
+import org.apache.commons.collections.CollectionUtils;
+import com.dianping.cat.Cat;
+
+import java.util.List;
+import java.util.Map;
+import java.util.Objects;
+import java.util.concurrent.Callable;
+import java.util.concurrent.Future;
+import java.util.concurrent.TimeUnit;
+import java.util.stream.Collectors;
+
+@Component("nationalSubsidyShopProcessor")
+@Slf4j
+public class NationalSubsidyShopProcessor extends AbstractPromoProcessor {
+
+    private static final long NATIONAL_SUBSIDY_TAG_ID = 21857;
+
+    @Autowired
+    private NationalSubsidyShopWrapper nationalSubsidyShopWrapper;
+
+    @Override
+    public void prePare(CouponActivityContext promoCtx, IMobileContext iMobileContext) {
+        try {
+            // 快搭模版卡控
+            if (!promoCtx.isNeedNationalSubsidy()) {
+                return;
+            }
+            // 默认国补只有美团侧
+            if (!Lion.getBoolean(LionConstants.APP_KEY, LionConstants.NATIONAL_SUBSIDY_IS_SUPPORT_DP, false) && !promoCtx.isMt()) {
+                return;
+            }
+            if (promoCtx.getMtShopIdL() <= 0) {
+                return;
+            }
+            if (promoCtx.getMtUserId() <= 0) {
+                return;
+            }
+            if (promoCtx.getMtCityId() <= 0) {
+                return;
+            }
+            Future<NationalSubsidyInfoDO> nationalSubsidyInfoDOFuture = ExecutorService.submit(new Callable<NationalSubsidyInfoDO>() {
+                @Override
+                public NationalSubsidyInfoDO call() throws Exception {
+                    return queryNationalSubsidyInfo(promoCtx);
+                }
+            });
+            promoCtx.setNationalSubsidyInfoDOFuture(nationalSubsidyInfoDOFuture);
+        } catch (Exception e) {
+            log.error("NationalSubsidyShopProcessor# prepare error. ctx: {}", promoCtx, e);
+        }
+    }
+
+    @Override
+    public void loadPromo(CouponActivityContext promoCtx) {
+        try {
+            Future<NationalSubsidyInfoDO> nationalSubsidyInfoDOFuture = promoCtx.getNationalSubsidyInfoDOFuture();
+            if (Objects.nonNull(nationalSubsidyInfoDOFuture)) {
+                int timeout = Lion.getInt(LionConstants.APP_KEY, LionConstants.QUERY_NATIONAL_SUBSIDY_INFO_TIMEOUT, 500);
+                promoCtx.setNationalSubsidyInfoDO(nationalSubsidyInfoDOFuture.get(timeout, TimeUnit.MILLISECONDS));
+            }
+        } catch (Exception e) {
+            log.error("NationalSubsidyShopProcessor# loadPromo error. ctx: {}", promoCtx, e);
+            promoCtx.setNationalSubsidyInfoDO(new NationalSubsidyInfoDO());
+        }
+    }
+
+    @Override
+    public String getPromoName() {
+        return "nationalSubsidyShopProcessor";
+    }
+
+    private NationalSubsidyInfoDO queryNationalSubsidyInfo(CouponActivityContext promoCtx) {
+        try {
+            long mtShopIdL = promoCtx.getMtShopIdL();
+            FindSceneDisplayTagRequest zdcTagRequest = buildZDCTagRequest(mtShopIdL);
+            Response<Map<Long, List<DisplayTagDto>>> shopId2TagsRes = nationalSubsidyShopWrapper.queryZDC(promoCtx, zdcTagRequest, this.getClass().getSimpleName());
+            if (Objects.isNull(shopId2TagsRes) || MapUtils.isEmpty(shopId2TagsRes.getData()) || !shopId2TagsRes.getData().containsKey(mtShopIdL)
+                    || CollectionUtils.isEmpty(shopId2TagsRes.getData().get(mtShopIdL))) {
+                return null;
+            }
+            boolean isNationalSubsidyShop = shopId2TagsRes.getData().get(mtShopIdL).stream().filter(Objects::nonNull)
+                    .anyMatch(displayTagDto -> displayTagDto.getTagId() == NATIONAL_SUBSIDY_TAG_ID);
+            if (!isNationalSubsidyShop) {
+                return null;
+            }
+            NationalSubsidyInfoDO nationalSubsidyInfoDO = new NationalSubsidyInfoDO();
+            GetUserQualificationOpenRequest getUserQualificationOpenRequest = buildUserQualRequest(promoCtx);
+            GetUserQualificationOpenResponse userQualRes = nationalSubsidyShopWrapper.queryShangou(promoCtx, getUserQualificationOpenRequest, this.getClass().getSimpleName());
+            Map<Integer, Integer> userQualMap = convert2UserQualMap(userQualRes);
+            log.info("NationalSubsidyShopProcessor#queryShangou userQualMap: {}", JsonUtils.toJson(userQualMap));
+            nationalSubsidyInfoDO.setUserQualMap(userQualMap);
+            // 国补补贴比例查询，查Lion配置或者供应链
+            List<String> userQualCats = userQualMap.keySet().stream().map(String::valueOf).collect(Collectors.toList());
+            if (!Lion.getBoolean(LionConstants.APP_KEY, LionConstants.NATIONAL_SUBSIDY_PROMO_SWITCH_SUPPLY_CHAIN, false)) {
+                nationalSubsidyInfoDO.setCategory2promoMap(convert2Category2promoMap(userQualCats, Lion.getMap(LionConstants.APP_KEY, LionConstants.NATIONAL_SUBSIDY_SHANGOU_PRODUCT_CAT_2_PROMO_MAP, Integer.class, Maps.newHashMap())));
+            } else {
+                QueryGovSubsidyInfoRequest govSubsidyInfoQueryRequest = buildQueryGovSubsidyInfoQueryRequest(userQualCats);
+                IResponse<Map<String, GovSubsidyInfo>> supplyChainRes = nationalSubsidyShopWrapper.querySupplyChain(promoCtx, govSubsidyInfoQueryRequest, this.getClass().getSimpleName());
+                nationalSubsidyInfoDO.setCategory2promoMap(convert2Category2promoMap(govSubsidyInfoQueryRequest, supplyChainRes));
+            }
+            return nationalSubsidyInfoDO;
+        } catch (Exception e) {
+            log.error("NationalSubsidyShopProcessor#queryNationalSubsidyInfo error, ctx: {}", promoCtx, e);
+            return new NationalSubsidyInfoDO();
+        }
+    }
+
+    private QueryGovSubsidyInfoRequest buildQueryGovSubsidyInfoQueryRequest(List<String> shangouCats) {
+        QueryGovSubsidyInfoRequest queryGovSubsidyInfoRequest = new QueryGovSubsidyInfoRequest();
+        queryGovSubsidyInfoRequest.setInstashoppingCategories(shangouCats);
+        return queryGovSubsidyInfoRequest;
+    }
+
+    private Map<Integer, Integer> convert2Category2promoMap(QueryGovSubsidyInfoRequest govSubsidyInfoQueryRequest, IResponse<Map<String, GovSubsidyInfo>> supplyChainRes) {
+        if (Objects.isNull(supplyChainRes) || !supplyChainRes.isSuccess() || Objects.isNull(supplyChainRes.getResult())) {
+            return Maps.newHashMap();
+        }
+        log.info("NationalSubsidyShopProcessor#querySupplyChain request: {}, response: {}", JsonUtils.toJson(govSubsidyInfoQueryRequest), JsonUtils.toJson(supplyChainRes.getResult()));
+        return supplyChainRes.getResult().entrySet().stream().filter(Objects::nonNull)
+                .filter(entry -> Objects.nonNull(entry.getValue()))
+                .filter(entry -> StringUtils.isNumeric(entry.getKey()))
+                .filter(entry -> StringUtils.isNumeric(entry.getValue().getSubsidyRatio()))
+                .collect(Collectors.toMap(entry -> Integer.parseInt(entry.getKey()), entry -> Integer.parseInt(entry.getValue().getSubsidyRatio())));
+    }
+
+    private Map<Integer, Integer> convert2Category2promoMap(List<String> userQualCats, Map<String, Integer> rawCategory2promoMap) {
+        if (MapUtils.isEmpty(rawCategory2promoMap)) {
+            return Maps.newHashMap();
+        }
+        return rawCategory2promoMap.entrySet()
+                .stream()
+                .filter(entry -> Objects.nonNull(entry) && StringUtils.isNumeric(entry.getKey()) && Objects.nonNull(entry.getValue()))
+                .filter(entry -> userQualCats.contains(entry.getKey()))
+                .collect(Collectors.toMap(entry -> Integer.parseInt(entry.getKey()), Map.Entry::getValue));
+    }
+
+    private Map<Integer, Integer> convert2UserQualMap(GetUserQualificationOpenResponse userQualRes) {
+        if (Objects.isNull(userQualRes) || userQualRes.getCode() != 0 || CollectionUtils.isEmpty(userQualRes.getQualificationInfo())) {
+            return Maps.newHashMap();
+        }
+        return userQualRes.getQualificationInfo()
+                .stream()
+                .filter(Objects::nonNull)
+                .collect(Collectors.toMap(qualInfo -> qualInfo.getProductCategory().getValue(), qualInfo -> qualInfo.getStatus().getValue()));
+    }
+
+    private GetUserQualificationOpenRequest buildUserQualRequest(CouponActivityContext promoCtx) {
+        GetUserQualificationOpenRequest userQualRequest = new GetUserQualificationOpenRequest();
+        userQualRequest.setBizLine(BizLineEnum.FU_WU_LING_SHOU);
+        CommonRequest commonRequest = new CommonRequest();
+        commonRequest.setUserId(promoCtx.getMtUserId());
+        commonRequest.setUuid(promoCtx.getDpid());
+        commonRequest.setMtCityId(promoCtx.getMtCityId());
+        userQualRequest.setCommonRequest(commonRequest);
+        return userQualRequest;
+    }
+
+    private FindSceneDisplayTagRequest buildZDCTagRequest(long mtShopIdL) {
+        FindSceneDisplayTagRequest request = new FindSceneDisplayTagRequest();
+        request.setBizCode("MT_MERCHANT_DETAILS_BANNER");
+        request.setShopIds(Lists.newArrayList(mtShopIdL));
+        return request;
+    }
+}
