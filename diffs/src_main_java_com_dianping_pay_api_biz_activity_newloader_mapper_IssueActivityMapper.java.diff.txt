diff --git a/src/main/java/com/dianping/pay/api/biz/activity/newloader/mapper/IssueActivityMapper.java b/src/main/java/com/dianping/pay/api/biz/activity/newloader/mapper/IssueActivityMapper.java
index 0eea51cb..a6fd348e 100644
--- a/src/main/java/com/dianping/pay/api/biz/activity/newloader/mapper/IssueActivityMapper.java
+++ b/src/main/java/com/dianping/pay/api/biz/activity/newloader/mapper/IssueActivityMapper.java
@@ -171,6 +171,7 @@ public class IssueActivityMapper {
         context.setNeedMerchantAccountPromotion(request.getNeedmerchantaccountcoupon() != null && request.getNeedmerchantaccountcoupon() == 1);
         context.setNeedTopAdResourcePromotion(request.getNeedtopadresourcepromotion() != null && request.getNeedtopadresourcepromotion() == 1);
         context.setNeedMagicalMemberCoupon(request.getNeedmagicalmembercoupon() != null && request.getNeedmagicalmembercoupon() == 1);
+        context.setNeedNationalSubsidy(request.getNeednationalsubsidy() != null && request.getNeednationalsubsidy() == 1);
         context.setCx(request.getCx());
         context.setPageSource(request.getPagesource());
         context.setCouponPageSource(request.getCouponPageSource());
@@ -797,6 +798,7 @@ public class IssueActivityMapper {
         context.setNeedMerchantAccountPromotion(request.getNeedmerchantaccountcoupon() != null && request.getNeedmerchantaccountcoupon() == 1);
         context.setNeedTopAdResourcePromotion(request.getNeedtopadresourcepromotion() != null && request.getNeedtopadresourcepromotion() == 1);
         context.setNeedMagicalMemberCoupon(request.getNeedmagicalmembercoupon() != null && request.getNeedmagicalmembercoupon() == 1);
+        context.setNeedNationalSubsidy(request.getNeednationalsubsidy() != null && request.getNeednationalsubsidy() == 1);
         context.setCx(request.getCx());
         context.setPageSource(request.getPagesource());
         context.setCouponPageSource(request.getCouponPageSource());
