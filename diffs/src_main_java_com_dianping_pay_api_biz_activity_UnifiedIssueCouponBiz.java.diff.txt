diff --git a/src/main/java/com/dianping/pay/api/biz/activity/UnifiedIssueCouponBiz.java b/src/main/java/com/dianping/pay/api/biz/activity/UnifiedIssueCouponBiz.java
index 9aab5490..c0bf2773 100644
--- a/src/main/java/com/dianping/pay/api/biz/activity/UnifiedIssueCouponBiz.java
+++ b/src/main/java/com/dianping/pay/api/biz/activity/UnifiedIssueCouponBiz.java
@@ -689,9 +689,11 @@ public class UnifiedIssueCouponBiz {
         org.apache.commons.lang3.tuple.Pair<List<CouponDesc>, List<CouponDesc>> merchantAccountCouponPromotion = couponActivityContext.getMerchantAccountCouponPromotion();
         TopAdResourcePromotionDO topAdResourcePromotionDO = couponActivityContext.getTopAdResourcePromotionDO();
         MagicalMemberTagRawDO magicalMemberTagDO = couponActivityContext.getMagicalMemberTagRawDO();
+        NationalSubsidyInfoDO nationalSubsidyInfoDO = couponActivityContext.getNationalSubsidyInfoDO();
         List<String> magicalMemberCouponIds = buildUserHoldMagicalMemberCouponIds(magicalMemberTagDO);   // 用户已有神券用户券id列表
+
         if (CollectionUtils.isEmpty(issueCouponActivitieList) && CollectionUtils.isEmpty(couponExposeDTOS)
-                && shopResourcePromotionDO == null && merchantAccountCouponPromotion == null && magicalMemberTagDO == null) {
+                && shopResourcePromotionDO == null && merchantAccountCouponPromotion == null && magicalMemberTagDO == null && nationalSubsidyInfoDO == null) {
             return null;
         }
         IssueCouponFullComponentDetail fullComponentDetail = new IssueCouponFullComponentDetail();
@@ -754,7 +756,8 @@ public class UnifiedIssueCouponBiz {
                 && (shopResourcePromotionDO == null || CollectionUtils.isEmpty(shopResourcePromotionDO.getResourceMaterialsDOS()))
                 && CollectionUtils.isEmpty(noIssuedMerchantCoupons) && CollectionUtils.isEmpty(issuedMerchantCoupons)
                 && (topAdResourcePromotionDO == null || CollectionUtils.isEmpty(topAdResourcePromotionDO.getVoucherDetailDOList()))
-                && (magicalMemberTagDO == null || (magicalMemberTagDO.getMagicalMemberTagText() == null && CollectionUtils.isEmpty(magicalMemberTagDO.getMagicalMemberCouponPackageIds()) && CollectionUtils.isEmpty(magicalMemberTagDO.getMagicMemberCouponInfoList())))) {
+                && (magicalMemberTagDO == null || (magicalMemberTagDO.getMagicalMemberTagText() == null && CollectionUtils.isEmpty(magicalMemberTagDO.getMagicalMemberCouponPackageIds()) && CollectionUtils.isEmpty(magicalMemberTagDO.getMagicMemberCouponInfoList())))
+                && nationalSubsidyInfoDO == null) {
             return null;
         }
 
@@ -939,6 +942,38 @@ public class UnifiedIssueCouponBiz {
             }
         }
 
+        // 国补资格入口层标签封装
+        // 国补资格尚领取状态，false-尚未领取，true-已领取
+        boolean isNationalSubsidyQualBound = false;
+        // 国补补贴比例
+        Integer nationalSubsidyMaxPromo = 0;
+        if (Objects.nonNull(nationalSubsidyInfoDO)) {
+            if (MapUtils.isNotEmpty(nationalSubsidyInfoDO.getUserQualMap()) && MapUtils.isNotEmpty(nationalSubsidyInfoDO.getCategory2promoMap())) {
+                Map<Integer, Integer> userQualMap = nationalSubsidyInfoDO.getUserQualMap();
+                Map<Integer, Integer> category2promoMap = nationalSubsidyInfoDO.getCategory2promoMap();
+                // 未领取资格的类目列表
+                List<Integer> unboundQualCategoryList = userQualMap.entrySet().stream().filter(entry -> Objects.equals(entry.getValue(), 0))
+                        .map(Map.Entry::getKey)
+                        .collect(Collectors.toList());
+                // a.尚有类目未领取资格
+                if (CollectionUtils.isNotEmpty(unboundQualCategoryList)) {
+                    nationalSubsidyMaxPromo = category2promoMap.entrySet().stream()
+                            .filter(entry -> unboundQualCategoryList.contains(entry.getKey()))
+                            .map(Map.Entry::getValue)
+                            .max(Comparator.naturalOrder())
+                            .orElse(0);
+                }
+                // b.全部类目资格已领取
+                else {
+                    isNationalSubsidyQualBound = true;
+                    nationalSubsidyMaxPromo = category2promoMap.values().stream()
+                            .max(Comparator.naturalOrder())
+                            .orElse(0);
+                }
+            }
+            maxCount++;
+            couponSimpleListResult.add(0, buildNationalSubsidyQualTag(isNationalSubsidyQualBound, nationalSubsidyMaxPromo));
+        }
 
         simpleCouponListInfo.setCouponSimpleList(couponSimpleListResult.subList(0, Math.min(maxCount, couponSimpleListResult.size())));
         fullComponentDetail.setSimpleCouponListInfo(simpleCouponListInfo);
@@ -1038,6 +1073,11 @@ public class UnifiedIssueCouponBiz {
             couponListInfo.setInflateExperimentId(buildInflateExperimentId(magicalMemberTagDO.getExtendedFieldsMap()));
         }
         couponListInfo.setCouponList(issueCouponList);
+        // 拼装国补资格浮层卡片信息
+        if (Objects.nonNull(nationalSubsidyInfoDO)) {
+            NationalSubsidyInfo nationalSubsidyInfo = buildNationalSubsidyInfo(couponActivityContext.isMt(), isNationalSubsidyQualBound, nationalSubsidyMaxPromo);
+            couponListInfo.setNationalSubsidyInfo(Lists.newArrayList(nationalSubsidyInfo));
+        }
         fullComponentDetail.setCouponListInfo(couponListInfo);
         //处理ab实验结果
         fillAbResult(fullComponentDetail, couponActivityContext);
@@ -1045,6 +1085,28 @@ public class UnifiedIssueCouponBiz {
         return fullComponentDetail;
     }
 
+    private NationalSubsidyInfo buildNationalSubsidyInfo(boolean isMt, boolean isNationalSubsidyQualBound, Integer nationalSubsidyMaxPromo) {
+        NationalSubsidyInfo nationalSubsidyInfo = new NationalSubsidyInfo();
+        nationalSubsidyInfo.setTitle("国家补贴");
+        nationalSubsidyInfo.setSubTitle(Lion.getString(LionConstants.APP_KEY, LionConstants.NATIONAL_SUBSIDY_CARD_SUBTITLE, "领后使用云闪付可享"));
+        nationalSubsidyInfo.setIcon("https://p0.meituan.net/ingee/bb5c80f042994ae27c669942e26ae7ee6947.png");
+        nationalSubsidyInfo.setPromo(Integer.valueOf(0).equals(nationalSubsidyMaxPromo) ? 20 + "" : String.valueOf(nationalSubsidyMaxPromo));
+        nationalSubsidyInfo.setIssueStatus(isNationalSubsidyQualBound ? 1 : 0);
+        nationalSubsidyInfo.setButtonPic(Lion.getMap(LionConstants.APP_KEY, LionConstants.NATIONAL_SUBSIDY_CARD_BUTTON_PIC_MAP, String.class, Maps.newHashMap()).get(String.valueOf(isNationalSubsidyQualBound)));
+        String jumpUrl = Lion.getMap(LionConstants.APP_KEY, LionConstants.NATIONAL_SUBSIDY_JUMP_URL_MAP, String.class, Maps.newHashMap()).get(isMt ? "mt" : "dp");
+        nationalSubsidyInfo.setJumpUrl(jumpUrl);
+        return nationalSubsidyInfo;
+    }
+
+    private IssueCouponSimpleDetail buildNationalSubsidyQualTag(boolean isNationalSubsidyQualBound, Integer maxPromo) {
+        IssueCouponSimpleDetail simpleDetail = new IssueCouponSimpleDetail();
+        simpleDetail.setType(12);
+        simpleDetail.setTitle("最高补贴");
+        simpleDetail.setIssueStatus(isNationalSubsidyQualBound ? 1 : 0);
+        simpleDetail.setTotalAmount(Integer.valueOf(0).equals(maxPromo) ? "20%" : maxPromo + "%");
+        return simpleDetail;
+    }
+
     private List<Integer> buildInflateExperimentId(Map<String, String> extendedFieldsMap) {
         if (MapUtils.isEmpty(extendedFieldsMap) || !extendedFieldsMap.containsKey(PromotionPropertyEnum.COMMON_EXPERIMENT_ID_SET.getValue())) {
             return Lists.newArrayList();
