diff --git a/src/main/java/com/dianping/pay/api/biz/activity/newloader/CouponIssueActivityQueryService.java b/src/main/java/com/dianping/pay/api/biz/activity/newloader/CouponIssueActivityQueryService.java
index fb3b2615..e33ce1db 100644
--- a/src/main/java/com/dianping/pay/api/biz/activity/newloader/CouponIssueActivityQueryService.java
+++ b/src/main/java/com/dianping/pay/api/biz/activity/newloader/CouponIssueActivityQueryService.java
@@ -114,6 +114,8 @@ public class CouponIssueActivityQueryService {
     private TopAdResourcePromotionProcessor topAdResourcePromotionProcessor;
     @Resource(name = "magicalMemberCouponProcessor")
     private MagicalMemberCouponProcessor magicalMemberCouponProcessor;
+    @Resource(name = "nationalSubsidyShopProcessor")
+    private NationalSubsidyShopProcessor nationalSubsidyShopProcessor;
 
 
     @Autowired
@@ -147,7 +149,7 @@ public class CouponIssueActivityQueryService {
         return Lists.newArrayList(tgcCouponProcessor, returnPromotionProcessor, shareCouponProcessor, reductionPromotionProcessor,
                 proxyCouponProcessor, resourcePromotionProcessor, userCouponProcessor, tgcBatchCouponProcessor, brandActivityPromotionProcessor,
                 proxyReduceProcessor, proxyMemberCardProcessor, proxyJoyCardProcessor, financialCouponProcessor, priceDisplayProcessor, shopResourcePromotionProcessor,
-                merchantAccountCouponProcessor, topAdResourcePromotionProcessor, magicalMemberCouponProcessor);
+                merchantAccountCouponProcessor, topAdResourcePromotionProcessor, magicalMemberCouponProcessor, nationalSubsidyShopProcessor);
     }
 
     public CouponIssueActivityQueryService(int coreSize, int maxSize, long threadTimeOut, int blockQueueSize) {
@@ -287,6 +289,7 @@ public class CouponIssueActivityQueryService {
         promoCtx.setNeedMerchantAccountPromotion(request.isNeedMerchantAccountPromotion());
         promoCtx.setNeedTopAdResourcePromotion(request.isNeedTopAdResourcePromotion());
         promoCtx.setNeedMagicalMemberCoupon(request.isNeedMagicalMemberCoupon());
+        promoCtx.setNeedNationalSubsidy(request.isNeedNationalSubsidy());
         promoCtx.setActualCityId(request.getActualCityId());
         promoCtx.setUuid(request.getUuid());
         promoCtx.setCPlatform(request.getCPlatform());
