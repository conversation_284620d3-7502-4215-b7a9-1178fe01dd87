diff --git a/src/test/java/com/dianping/pay/api/biz/activity/UnifiedIssueCouponBizTest.java b/src/test/java/com/dianping/pay/api/biz/activity/UnifiedIssueCouponBizTest.java
new file mode 100644
index 00000000..689cd162
--- /dev/null
+++ b/src/test/java/com/dianping/pay/api/biz/activity/UnifiedIssueCouponBizTest.java
@@ -0,0 +1,124 @@
+package com.dianping.pay.api.biz.activity;
+
+import static org.junit.Assert.*;
+import static org.mockito.Mockito.*;
+
+import com.dianping.mobile.framework.datatypes.IMobileContext;
+import com.dianping.pay.api.beans.NationalSubsidyInfoDO;
+import com.dianping.pay.api.biz.activity.UnifiedIssueCouponBiz;
+import com.dianping.pay.api.biz.activity.newloader.dto.CouponActivityContext;
+import com.dianping.pay.api.biz.activity.newloader.dto.PromotionRequestContext;
+import com.dianping.pay.api.entity.issuecoupon.IssueCouponFullComponentDetail;
+import com.dianping.pay.api.entity.issuecoupon.IssueCouponSimpleDetail;
+import java.lang.reflect.Method;
+import java.util.HashMap;
+
+import org.junit.*;
+import org.junit.runner.RunWith;
+import org.junit.runner.RunWith.*;
+import org.mockito.InjectMocks;
+import org.mockito.Mock;
+import org.mockito.junit.*;
+
+@RunWith(MockitoJUnitRunner.class)
+public class UnifiedIssueCouponBizTest {
+
+    private UnifiedIssueCouponBiz unifiedIssueCouponBiz = new UnifiedIssueCouponBiz();
+
+    /**
+     * 测试isNationalSubsidyQualBound为true，maxPromo为任意值的情况
+     */
+    @Test
+    public void testBuildNationalSubsidyQualTagTrue() throws Throwable {
+        // arrange
+        boolean isNationalSubsidyQualBound = true;
+        Integer maxPromo = 100;
+        // Use reflection to access the private method
+        Method method = UnifiedIssueCouponBiz.class.getDeclaredMethod("buildNationalSubsidyQualTag", boolean.class, Integer.class);
+        method.setAccessible(true);
+        // act
+        IssueCouponSimpleDetail result = (IssueCouponSimpleDetail) method.invoke(unifiedIssueCouponBiz, isNationalSubsidyQualBound, maxPromo);
+        // assert
+        assertEquals(1, result.getIssueStatus());
+        assertEquals("100%", result.getTotalAmount());
+    }
+
+    /**
+     * 测试isNationalSubsidyQualBound为false，maxPromo为任意值的情况
+     */
+    @Test
+    public void testBuildNationalSubsidyQualTagFalse() throws Throwable {
+        // arrange
+        boolean isNationalSubsidyQualBound = false;
+        Integer maxPromo = 100;
+        // Use reflection to access the private method
+        Method method = UnifiedIssueCouponBiz.class.getDeclaredMethod("buildNationalSubsidyQualTag", boolean.class, Integer.class);
+        method.setAccessible(true);
+        // act
+        IssueCouponSimpleDetail result = (IssueCouponSimpleDetail) method.invoke(unifiedIssueCouponBiz, isNationalSubsidyQualBound, maxPromo);
+        // assert
+        assertEquals(0, result.getIssueStatus());
+        assertEquals("100%", result.getTotalAmount());
+    }
+
+    /**
+     * 测试isNationalSubsidyQualBound为true，maxPromo为0的情况
+     */
+    @Test
+    public void testBuildNationalSubsidyQualTagTrueZero() throws Throwable {
+        // arrange
+        boolean isNationalSubsidyQualBound = true;
+        Integer maxPromo = 0;
+        // Use reflection to access the private method
+        Method method = UnifiedIssueCouponBiz.class.getDeclaredMethod("buildNationalSubsidyQualTag", boolean.class, Integer.class);
+        method.setAccessible(true);
+        // act
+        IssueCouponSimpleDetail result = (IssueCouponSimpleDetail) method.invoke(unifiedIssueCouponBiz, isNationalSubsidyQualBound, maxPromo);
+        // assert
+        assertEquals(1, result.getIssueStatus());
+        assertEquals("20%", result.getTotalAmount());
+    }
+
+    /**
+     * 测试isNationalSubsidyQualBound为false，maxPromo为0的情况
+     */
+    @Test
+    public void testBuildNationalSubsidyQualTagFalseZero() throws Throwable {
+        // arrange
+        boolean isNationalSubsidyQualBound = false;
+        Integer maxPromo = 0;
+        // Use reflection to access the private method
+        Method method = UnifiedIssueCouponBiz.class.getDeclaredMethod("buildNationalSubsidyQualTag", boolean.class, Integer.class);
+        method.setAccessible(true);
+        // act
+        IssueCouponSimpleDetail result = (IssueCouponSimpleDetail) method.invoke(unifiedIssueCouponBiz, isNationalSubsidyQualBound, maxPromo);
+        // assert
+        assertEquals(0, result.getIssueStatus());
+        assertEquals("20%", result.getTotalAmount());
+    }
+
+    @Test
+    public void testBuildIssueCouponFullComponentDetail() throws Throwable {
+        // arrange
+        CouponActivityContext couponActivityContext = mock(CouponActivityContext.class);
+        PromotionRequestContext promotionRequestContext = mock(PromotionRequestContext.class);
+        IMobileContext iMobileContext = mock(IMobileContext.class);
+        NationalSubsidyInfoDO nationalSubsidyInfoDO = new NationalSubsidyInfoDO();
+        HashMap<Integer, Integer> category2promoMap = new HashMap<>();
+        category2promoMap.put(123, 456);
+        category2promoMap.put(789, 101);
+        nationalSubsidyInfoDO.setCategory2promoMap(category2promoMap);
+        nationalSubsidyInfoDO.setCategory2promoMap(category2promoMap);
+        HashMap<Integer, Integer> userQualMap = new HashMap<>();
+        userQualMap.put(1, 100);
+        nationalSubsidyInfoDO.setUserQualMap(userQualMap);
+        when(couponActivityContext.getNationalSubsidyInfoDO()).thenReturn(nationalSubsidyInfoDO);
+        // Use reflection to access the private method
+        Method method = UnifiedIssueCouponBiz.class.getDeclaredMethod("buildIssueCouponFullComponentDetail", CouponActivityContext.class, PromotionRequestContext.class, IMobileContext.class, boolean.class);
+        method.setAccessible(true);
+        // act
+        IssueCouponFullComponentDetail result = (IssueCouponFullComponentDetail) method.invoke(unifiedIssueCouponBiz, couponActivityContext, promotionRequestContext, iMobileContext, true);
+        // assert
+        assertNotNull(result);
+    }
+}
\ No newline at end of file
