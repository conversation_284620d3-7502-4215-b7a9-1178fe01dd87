<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/maven-v4_0_0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.dianping</groupId>
        <artifactId>dianping-parent</artifactId>
        <version>5.1.0</version>
    </parent>
    <groupId>com.dianping.pay</groupId>
    <artifactId>mapi-pay-promo-web</artifactId>
    <version>0.1.0</version>
    <packaging>war</packaging>
    <name>mapi-pay-promo-web</name>
    <properties>
        <env>qa</env>
        <restlet.version>2.0.15</restlet.version>
        <springframework-version>5.0.11.RELEASE</springframework-version>
        <netty-all.version>4.1.45.Final</netty-all.version>
        <mtthrift.version>2.5.10</mtthrift.version>
        <promotion-api.version>2.0.43</promotion-api.version>
        <promotion-common-base.version>2.14.70</promotion-common-base.version>
        <promotion-query-client.version>1.6.6</promotion-query-client.version>
        <jackson-datatype-guava.version>2.10.3</jackson-datatype-guava.version>
    </properties>
    <dependencyManagement>
        <dependencies>
            <dependency>
                <groupId>com.dianping</groupId>
                <artifactId>account-common-validation</artifactId>
                <version>2.0.5</version>
                <exclusions>
                    <exclusion>
                        <groupId>org.slf4j</groupId>
                        <artifactId>slf4j-log4j12</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>com.dianping</groupId>
                <artifactId>avatar-cache</artifactId>
                <version>2.8.0</version>
                <exclusions>
                    <exclusion>
                        <groupId>org.slf4j</groupId>
                        <artifactId>slf4j-log4j12</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>com.dianping</groupId>
                <artifactId>avatar-mobile</artifactId>
                <version>1.0</version>
                <exclusions>
                    <exclusion>
                        <groupId>org.slf4j</groupId>
                        <artifactId>slf4j-log4j12</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>com.dianping</groupId>
                <artifactId>dp-common-util</artifactId>
                <version>0.1.0</version>
                <exclusions>
                    <exclusion>
                        <groupId>org.slf4j</groupId>
                        <artifactId>slf4j-log4j12</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>com.dianping.review.profession</groupId>
                <artifactId>review-profession-api</artifactId>
                <version>1.0.6</version>
                <exclusions>
                    <exclusion>
                        <groupId>org.slf4j</groupId>
                        <artifactId>slf4j-log4j12</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>com.dianping.sales</groupId>
                <artifactId>mkt-adv-api</artifactId>
                <version>2.2</version>
                <exclusions>
                    <exclusion>
                        <groupId>org.slf4j</groupId>
                        <artifactId>slf4j-log4j12</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>com.dianping.swallow</groupId>
                <artifactId>swallow-client</artifactId>
                <version>0.5.4</version>
                <exclusions>
                    <exclusion>
                        <groupId>org.slf4j</groupId>
                        <artifactId>slf4j-log4j12</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>com.sankuai</groupId>
                <artifactId>inf-bom</artifactId>
                <version>1.13.1</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
            <dependency>
                <groupId>com.dianping.swallow</groupId>
                <artifactId>swallow-consumerclient</artifactId>
                <version>0.8.11.9</version>
            </dependency>
            <dependency>
                <groupId>com.dianping.swallow</groupId>
                <artifactId>swallow-producerclient</artifactId>
                <version>0.8.11.9</version>
            </dependency>
            <dependency>
                <groupId>com.dianping.tuangou</groupId>
                <artifactId>tuangou-pay-api</artifactId>
                <version>0.2.2</version>
                <exclusions>
                    <exclusion>
                        <groupId>org.slf4j</groupId>
                        <artifactId>slf4j-log4j12</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>com.dianping.zebra</groupId>
                <artifactId>zebra-calcite</artifactId>
                <version>3.3.1</version>
            </dependency>
            <dependency>
                <groupId>com.dianping.zebra</groupId>
                <artifactId>zebra-tool</artifactId>
                <version>3.3.1</version>
            </dependency>
            <dependency>
                <groupId>com.sankuai.meituan</groupId>
                <artifactId>mt-security</artifactId>
                <version>1.0.0</version>
                <exclusions>
                    <exclusion>
                        <groupId>org.slf4j</groupId>
                        <artifactId>slf4j-log4j12</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>io.netty</groupId>
                <artifactId>netty-all</artifactId>
                <version>${netty-all.version}</version>
            </dependency>
            <dependency>
                <groupId>mysql</groupId>
                <artifactId>mysql-connector-java</artifactId>
                <version>5.1.49</version>
            </dependency>
            <dependency>
                <groupId>org.mockito</groupId>
                <artifactId>mockito-core</artifactId>
                <version>2.7.22</version>
            </dependency>
            <dependency>
                <groupId>com.fasterxml.jackson.datatype</groupId>
                <artifactId>jackson-datatype-guava</artifactId>
                <version>${jackson-datatype-guava.version}</version>
            </dependency>
        </dependencies>
    </dependencyManagement>
    <dependencies>
        <dependency>
            <groupId>com.meituan.service.mobile</groupId>
            <artifactId>mtthrift</artifactId>
        </dependency>
        <dependency>
            <groupId>com.meituan.mtrace</groupId>
            <artifactId>mtrace-http</artifactId>
        </dependency>
        <dependency>
            <groupId>org.slf4j</groupId>
            <artifactId>jcl-over-slf4j</artifactId>
            <version>1.7.5</version>
        </dependency>
        <dependency>
            <groupId>org.slf4j</groupId>
            <artifactId>slf4j-api</artifactId>
            <version>1.7.5</version>
        </dependency>
        <dependency>
            <groupId>com.dianping.mobile</groupId>
            <artifactId>mapi-shell</artifactId>
            <version>4.2.5</version>
            <exclusions>
                <exclusion>
                    <groupId>com.dianping.dpsf</groupId>
                    <artifactId>dpsf-net</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.xerial.snappy</groupId>
                    <artifactId>snappy-java</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.apache.httpcomponents</groupId>
                    <artifactId>httpcore-nio</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>javax.servlet</groupId>
                    <artifactId>servlet-api</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.dianping.appkit</groupId>
            <artifactId>appkit-client</artifactId>
            <version>2.6.6</version>
            <exclusions>
                <exclusion>
                    <groupId>javax.servlet</groupId>
                    <artifactId>servlet-api</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.dianping</groupId>
            <artifactId>avatar-dao</artifactId>
            <version>2.2.5</version>
        </dependency>
        <dependency>
            <groupId>commons-codec</groupId>
            <artifactId>commons-codec</artifactId>
            <version>1.9</version>
        </dependency>
        <dependency>
            <groupId>commons-fileupload</groupId>
            <artifactId>commons-fileupload</artifactId>
            <version>1.3.3</version>
        </dependency>
        <dependency>
            <groupId>commons-io</groupId>
            <artifactId>commons-io</artifactId>
            <version>2.0.1</version>
        </dependency>
        <dependency>
            <groupId>com.dianping.search</groupId>
            <artifactId>dp-search-interface</artifactId>
            <version>1.4.7.8</version>
            <exclusions>
                <exclusion>
                    <groupId>org.apache.thrift</groupId>
                    <artifactId>libthrift</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.dianping.search</groupId>
            <artifactId>dp-search-base</artifactId>
            <version>1.3.8</version>
            <exclusions>
                <exclusion>
                    <groupId>com.dianping</groupId>
                    <artifactId>avatar-cache</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.caucho</groupId>
                    <artifactId>hessian-dp</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.dianping</groupId>
            <artifactId>avatar-cache</artifactId>
        </dependency>
        <dependency>
            <groupId>net.sf.ehcache</groupId>
            <artifactId>ehcache-core</artifactId>
            <version>2.5.2</version>
        </dependency>
        <dependency>
            <groupId>javax.servlet</groupId>
            <artifactId>javax.servlet-api</artifactId>
            <version>3.1.0</version>
        </dependency>
        <dependency>
            <groupId>com.googlecode.json-simple</groupId>
            <artifactId>json-simple</artifactId>
            <version>1.1.1</version>
        </dependency>
        <dependency>
            <groupId>javax.axis</groupId>
            <artifactId>axis-adb</artifactId>
            <version>1.4.1</version>
            <exclusions>
                <exclusion>
                    <groupId>org.apache.geronimo.specs</groupId>
                    <artifactId>geronimo-javamail_1.4_spec</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>javax.axis</groupId>
            <artifactId>axis2-kernel</artifactId>
            <version>1.4.1</version>
            <exclusions>
                <exclusion>
                    <groupId>org.apache.geronimo.specs</groupId>
                    <artifactId>geronimo-javamail_1.4_spec</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.apache.httpcomponents</groupId>
                    <artifactId>httpcore-nio</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>javax.servlet</groupId>
                    <artifactId>servlet-api</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>javax.axis</groupId>
            <artifactId>wsdl4j</artifactId>
            <version>1.6.2</version>
        </dependency>
        <dependency>
            <groupId>javax.axis</groupId>
            <artifactId>XmlSchema</artifactId>
            <version>1.4.2</version>
        </dependency>
        <dependency>
            <groupId>javax.axis</groupId>
            <artifactId>neethi</artifactId>
            <version>2.0.4</version>
        </dependency>
        <dependency>
            <groupId>javax.axis</groupId>
            <artifactId>axiom-api</artifactId>
            <version>1.2.7</version>
            <exclusions>
                <exclusion>
                    <groupId>org.apache.geronimo.specs</groupId>
                    <artifactId>geronimo-javamail_1.4_spec</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>javax.axis</groupId>
            <artifactId>axiom-dom</artifactId>
            <version>1.2.7</version>
            <exclusions>
                <exclusion>
                    <groupId>org.apache.geronimo.specs</groupId>
                    <artifactId>geronimo-javamail_1.4_spec</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>javax.axis</groupId>
            <artifactId>axiom-impl</artifactId>
            <version>1.2.7</version>
            <exclusions>
                <exclusion>
                    <groupId>org.apache.geronimo.specs</groupId>
                    <artifactId>geronimo-javamail_1.4_spec</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.dianping.data.warehouse</groupId>
            <artifactId>dp-log</artifactId>
            <version>1.0.6</version>
        </dependency>
        <dependency>
            <groupId>com.fasterxml.jackson.core</groupId>
            <artifactId>jackson-core</artifactId>
            <version>2.9.3</version>
        </dependency>
        <dependency>
            <groupId>com.fasterxml.jackson.core</groupId>
            <artifactId>jackson-annotations</artifactId>
            <version>2.9.3</version>
        </dependency>
        <dependency>
            <groupId>com.dianping.to</groupId>
            <artifactId>to-event-api</artifactId>
            <version>2.0.8</version>
            <exclusions>
                <exclusion>
                    <groupId>com.dianping.mobile</groupId>
                    <artifactId>mobile-base-datatypes</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>junit</groupId>
            <artifactId>junit</artifactId>
            <version>4.12</version>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>com.dianping</groupId>
            <artifactId>avatar-mobile</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>com.dianping</groupId>
                    <artifactId>avatar-cache</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.springframework</groupId>
                    <artifactId>spring-jms</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>commons-httpclient</groupId>
            <artifactId>commons-httpclient</artifactId>
            <version>3.1</version>
        </dependency>
        <dependency>
            <groupId>dom4j</groupId>
            <artifactId>dom4j</artifactId>
            <version>1.6.1</version>
            <scope>compile</scope>
        </dependency>
        <dependency>
            <groupId>com.dianping</groupId>
            <artifactId>shoppic-remote</artifactId>
            <version>3.2.1</version>
        </dependency>
        <dependency>
            <groupId>com.dianping</groupId>
            <artifactId>feed-api</artifactId>
            <version>1.0.4</version>
        </dependency>
        <dependency>
            <groupId>com.dianping</groupId>
            <artifactId>feedback-remote</artifactId>
            <version>2.0.8</version>
        </dependency>
        <dependency>
            <groupId>com.dianping</groupId>
            <artifactId>avatar-tracker</artifactId>
            <version>2.2.5</version>
        </dependency>
        <dependency>
            <groupId>com.dianping</groupId>
            <artifactId>avatar-counter</artifactId>
            <version>2.2.5</version>
        </dependency>
        <dependency>
            <groupId>com.google.zxing</groupId>
            <artifactId>javase</artifactId>
            <version>1.6</version>
        </dependency>
        <dependency>
            <groupId>com.dianping</groupId>
            <artifactId>pay-account-service</artifactId>
            <version>0.3.6</version>
        </dependency>
        <dependency>
            <groupId>com.sankuai.meituan</groupId>
            <artifactId>mt-security</artifactId>
        </dependency>
        <dependency>
            <groupId>com.dianping</groupId>
            <artifactId>thirdpartyconnect</artifactId>
            <version>1.0.1</version>
        </dependency>
        <dependency>
            <groupId>com.dianping</groupId>
            <artifactId>group-remote</artifactId>
            <version>1.0.6</version>
        </dependency>
        <dependency>
            <groupId>com.dianping</groupId>
            <artifactId>promo-api</artifactId>
            <version>1.5.2</version>
            <scope>compile</scope>
        </dependency>
        <dependency>
            <groupId>com.dianping</groupId>
            <artifactId>user-remote</artifactId>
            <version>1.1.3</version>
        </dependency>
        <dependency>
            <groupId>dianping</groupId>
            <artifactId>recom-service</artifactId>
            <version>1.1</version>
        </dependency>
        <dependency>
            <groupId>com.dianping.tuangou</groupId>
            <artifactId>tuangou-coupon-api</artifactId>
            <version>0.2.2</version>
        </dependency>
        <dependency>
            <groupId>com.dianping.tuangou</groupId>
            <artifactId>tuangou-user-api</artifactId>
            <version>0.2.1</version>
        </dependency>
        <dependency>
            <groupId>com.dianping.tuangou</groupId>
            <artifactId>tuangou-pay-api</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>org.springframework</groupId>
                    <artifactId>spring</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.dianping.tuangou</groupId>
            <artifactId>tuangou-deal-api</artifactId>
            <version>1.1.28</version>
        </dependency>
        <dependency>
            <groupId>com.dianping.tuangou</groupId>
            <artifactId>tuangou-remote-common</artifactId>
            <version>0.4.13</version>
            <exclusions>
                <exclusion>
                    <groupId>com.dianping.swallow</groupId>
                    <artifactId>swallow-common</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.springframework</groupId>
                    <artifactId>spring</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.dianping.tuangou</groupId>
            <artifactId>tuangou-deal-client</artifactId>
            <version>1.0.6</version>
        </dependency>
        <dependency>
            <groupId>com.dianping.tuangou</groupId>
            <artifactId>tuangou-order-api</artifactId>
            <version>0.2.6</version>
        </dependency>
        <dependency>
            <groupId>com.dianping.tuangou</groupId>
            <artifactId>tuangou-receipt-api</artifactId>
            <version>0.4.9</version>
        </dependency>
        <dependency>
            <groupId>com.dianping</groupId>
            <artifactId>pay-common</artifactId>
            <version>1.1.9</version>
        </dependency>
        <dependency>
            <groupId>com.dianping.pay</groupId>
            <artifactId>pay-promo-common</artifactId>
            <version>1.1.83</version>
        </dependency>
        <dependency>
            <groupId>com.dianping.pay</groupId>
            <artifactId>pay-promo-rule-api</artifactId>
            <version>0.1.9</version>
            <exclusions>
                <exclusion>
                    <groupId>javassist</groupId>
                    <artifactId>javassist</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.dianping</groupId>
            <artifactId>pay-channel-service</artifactId>
            <version>0.3.3</version>
            <exclusions>
                <exclusion>
                    <groupId>org.springframework</groupId>
                    <artifactId>spring</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.dianping.pay</groupId>
            <artifactId>pay-order-service</artifactId>
            <version>0.7.7</version>
        </dependency>
        <dependency>
            <groupId>com.dianping</groupId>
            <artifactId>pay-unipay-service</artifactId>
            <version>0.3.4</version>
            <exclusions>
                <exclusion>
                    <groupId>org.apache.struts</groupId>
                    <artifactId>struts2-spring-plugin</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.dianping</groupId>
            <artifactId>piccenter-remote</artifactId>
            <version>1.2.1</version>
        </dependency>
        <dependency>
            <groupId>org.quartz-scheduler</groupId>
            <artifactId>quartz</artifactId>
            <version>1.8.5</version>
        </dependency>
        <dependency>
            <groupId>com.dianping</groupId>
            <artifactId>account-common-validation</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>org.apache.struts</groupId>
                    <artifactId>struts2-core</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.apache.logging.log4j</groupId>
                    <artifactId>log4j-api</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.apache.logging.log4j</groupId>
                    <artifactId>log4j-core</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.dianping</groupId>
            <artifactId>account-api</artifactId>
            <version>4.0.0</version>
        </dependency>
        <dependency>
            <groupId>com.dianping</groupId>
            <artifactId>account-validation-api</artifactId>
            <version>3.0.0</version>
        </dependency>
        <dependency>
            <groupId>com.dianping</groupId>
            <artifactId>dp-common-util</artifactId>
        </dependency>
        <dependency>
            <groupId>com.dianping.pay</groupId>
            <artifactId>pay-promo-log</artifactId>
            <version>0.1.0</version>
            <exclusions>
                <exclusion>
                    <groupId>org.apache.logging.log4j</groupId>
                    <artifactId>log4j-api</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.apache.logging.log4j</groupId>
                    <artifactId>log4j-core</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>org.apache.commons</groupId>
            <artifactId>commons-email</artifactId>
            <version>1.5</version>
        </dependency>
        <dependency>
            <groupId>org.restlet.jee</groupId>
            <artifactId>org.restlet</artifactId>
            <version>${restlet.version}</version>
        </dependency>
        <dependency>
            <groupId>org.restlet.jee</groupId>
            <artifactId>org.restlet.ext.ssl</artifactId>
            <version>${restlet.version}</version>
        </dependency>
        <dependency>
            <groupId>org.restlet.jee</groupId>
            <artifactId>org.restlet.ext.spring</artifactId>
            <version>${restlet.version}</version>
            <exclusions>
                <exclusion>
                    <groupId>org.springframework</groupId>
                    <artifactId>spring-asm</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.google.guava</groupId>
            <artifactId>guava</artifactId>
            <version>20.0</version>
        </dependency>
        <dependency>
            <groupId>org.apache.commons</groupId>
            <artifactId>commons-lang3</artifactId>
            <version>3.1</version>
        </dependency>
        <!--apple push-->
        <dependency>
            <groupId>com.notnoop.apns</groupId>
            <artifactId>apns</artifactId>
            <version>0.1.6</version>
        </dependency>
        <dependency>
            <groupId>org.codehaus.jackson</groupId>
            <artifactId>jackson-core-asl</artifactId>
            <version>1.9.9</version>
        </dependency>
        <dependency>
            <groupId>org.codehaus.jackson</groupId>
            <artifactId>jackson-mapper-asl</artifactId>
            <version>1.9.9</version>
        </dependency>
        <dependency>
            <groupId>org.json</groupId>
            <artifactId>json</artifactId>
            <version>20090211</version>
        </dependency>
        <dependency>
            <groupId>org.mockito</groupId>
            <artifactId>mockito-core</artifactId>
        </dependency>
        <dependency>
            <groupId>com.fasterxml.jackson.core</groupId>
            <artifactId>jackson-databind</artifactId>
            <version>2.9.3</version>
        </dependency>
        <dependency>
            <groupId>com.dianping</groupId>
            <artifactId>review-api</artifactId>
            <version>2.0.41.0</version>
        </dependency>
        <dependency>
            <groupId>com.dianping</groupId>
            <artifactId>sms-api</artifactId>
            <version>1.0.3</version>
        </dependency>
        <dependency>
            <groupId>com.dianping.swallow</groupId>
            <artifactId>swallow-consumerclient</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>com.dianping.swallow</groupId>
                    <artifactId>swallow-common</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.apache.logging.log4j</groupId>
                    <artifactId>log4j-api</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.apache.logging.log4j</groupId>
                    <artifactId>log4j-core</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.xerial.snappy</groupId>
                    <artifactId>snappy-java</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.meituan.mafka</groupId>
                    <artifactId>mafka-client_2.9</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.meituan.mafka</groupId>
                    <artifactId>mafka-kafka_2.9.2</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.dianping.gmkt</groupId>
            <artifactId>gmkt-wave-api</artifactId>
            <version>1.2.13</version>
        </dependency>
        <dependency>
            <groupId>com.dianping.swallow</groupId>
            <artifactId>swallow-producerclient</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>com.dianping.swallow</groupId>
                    <artifactId>swallow-common</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.apache.logging.log4j</groupId>
                    <artifactId>log4j-api</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.apache.logging.log4j</groupId>
                    <artifactId>log4j-core</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.meituan.mafka</groupId>
                    <artifactId>mafka-client_2.9</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.dianping.swallow</groupId>
            <artifactId>swallow-client</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>org.springframework</groupId>
                    <artifactId>spring</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.dianping</groupId>
            <artifactId>file-queue</artifactId>
            <version>1.1.1</version>
        </dependency>
        <dependency>
            <groupId>com.dianping</groupId>
            <artifactId>geoinfo-api</artifactId>
            <version>1.0.6</version>
            <scope>compile</scope>
        </dependency>
        <dependency>
            <groupId>com.dianping.review.profession</groupId>
            <artifactId>review-profession-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.dianping</groupId>
            <artifactId>pay-cashier-service</artifactId>
            <version>0.5.9</version>
            <exclusions>
                <exclusion>
                    <groupId>com.dianping.pay</groupId>
                    <artifactId>pay-point-api</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.dianping.lion</groupId>
                    <artifactId>lion-dev</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.springframework</groupId>
                    <artifactId>spring</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.thoughtworks.xstream</groupId>
                    <artifactId>xstream</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.dianping</groupId>
            <artifactId>pay-coupon-service</artifactId>
            <version>0.7.4</version>
        </dependency>
        <dependency>
            <groupId>com.sankuai.mpmkt.coupon</groupId>
            <artifactId>mkt-coupon-manage-api</artifactId>
            <version>0.1.1</version>
        </dependency>
        <dependency>
            <groupId>com.sankuai.mpmkt.coupon</groupId>
            <artifactId>mkt-coupon-manage-common-api</artifactId>
            <version>0.3.20</version>
        </dependency>
        <dependency>
            <groupId>com.sankuai.mpmkt.coupon</groupId>
            <artifactId>mkt-coupon-issue-api</artifactId>
            <version>0.2.7</version>
        </dependency>
        <dependency>
            <groupId>com.dianping.columbus</groupId>
            <artifactId>rs-open-remote</artifactId>
            <version>0.3.0</version>
        </dependency>
        <!--会员卡中心-->
        <dependency>
            <groupId>com.dianping</groupId>
            <artifactId>mobile-membercard-main-api</artifactId>
            <version>2.4.5</version>
        </dependency>
        <dependency>
            <groupId>com.dianping.pay</groupId>
            <artifactId>pay-discount-api</artifactId>
            <version>1.0.6</version>
        </dependency>
        <dependency>
            <groupId>com.dianping.tp</groupId>
            <artifactId>tp-promo-api</artifactId>
            <version>1.1.7</version>
            <exclusions>
                <exclusion>
                    <groupId>org.springframework</groupId>
                    <artifactId>spring</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.dianping.pay</groupId>
            <artifactId>pay-promo-execute-api</artifactId>
            <version>0.2.29</version>
        </dependency>
        <dependency>
            <groupId>com.dianping</groupId>
            <artifactId>ops-remote-host</artifactId>
            <version>1.1.0-IPV6</version>
        </dependency>
        <dependency>
            <groupId>com.dianping</groupId>
            <artifactId>deal-stock-query-api</artifactId>
            <version>1.0.1</version>
        </dependency>
        <dependency>
            <groupId>com.dianping.deal</groupId>
            <artifactId>deal-attribute-api</artifactId>
            <version>1.1.9</version>
        </dependency>
        <dependency>
            <groupId>com.dianping.tgc</groupId>
            <artifactId>tgc-open-remote</artifactId>
            <version>0.1.58</version>
            <exclusions>
                <exclusion>
                    <groupId>com.dianping.swallow</groupId>
                    <artifactId>swallow-producerclient</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.dianping.swallow</groupId>
                    <artifactId>swallow-consumerclient</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.dianping.swallow</groupId>
                    <artifactId>swallow-common</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.dianping.spurs</groupId>
                    <artifactId>spurs-common</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.dianping.deal</groupId>
            <artifactId>deal-idmapper-api</artifactId>
            <version>1.0.5</version>
        </dependency>
        <dependency>
            <groupId>com.dianping.tgc</groupId>
            <artifactId>tgc-process-remote</artifactId>
            <version>0.1.25</version>
            <exclusions>
                <exclusion>
                    <groupId>com.dianping.spurs</groupId>
                    <artifactId>spurs-common</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.dianping.beauty</groupId>
            <artifactId>violet-biz-api</artifactId>
            <version>2.1.28</version>
            <exclusions>
                <exclusion>
                    <groupId>org.xerial.snappy</groupId>
                    <artifactId>snappy-java</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.apache.httpcomponents</groupId>
                    <artifactId>httpcore</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.google.code.gson</groupId>
            <artifactId>gson</artifactId>
            <version>2.8.9</version>
        </dependency>
        <dependency>
            <groupId>com.dianping.poi.flow</groupId>
            <artifactId>poi-main-api</artifactId>
            <version>0.2.10</version>
        </dependency>
        <dependency>
            <groupId>com.dianping.pay</groupId>
            <artifactId>point-api</artifactId>
            <version>0.2.7</version>
        </dependency>
        <dependency>
            <groupId>com.dianping.sales</groupId>
            <artifactId>mkt-adv-api</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>com.dianping.mobile</groupId>
                    <artifactId>mobile-base-datatypes</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.dianping</groupId>
            <artifactId>deal-base-api</artifactId>
            <version>2.0.1</version>
        </dependency>
        <dependency>
            <groupId>org.apache.thrift</groupId>
            <artifactId>libthrift</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>javax.servlet</groupId>
                    <artifactId>servlet-api</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-aop</artifactId>
            <version>${springframework-version}</version>
        </dependency>
        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-aspects</artifactId>
            <version>${springframework-version}</version>
        </dependency>
        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-beans</artifactId>
            <version>${springframework-version}</version>
        </dependency>
        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-context</artifactId>
            <version>${springframework-version}</version>
        </dependency>
        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-expression</artifactId>
            <version>${springframework-version}</version>
        </dependency>
        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-context-support</artifactId>
            <version>${springframework-version}</version>
        </dependency>
        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-core</artifactId>
            <version>${springframework-version}</version>
        </dependency>
        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-jdbc</artifactId>
            <version>${springframework-version}</version>
        </dependency>
        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-orm</artifactId>
            <version>${springframework-version}</version>
        </dependency>
        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-test</artifactId>
            <version>${springframework-version}</version>
        </dependency>
        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-tx</artifactId>
            <version>${springframework-version}</version>
        </dependency>
        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-web</artifactId>
            <version>${springframework-version}</version>
        </dependency>
        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-webmvc</artifactId>
            <version>${springframework-version}</version>
        </dependency>
        <dependency>
            <groupId>com.dianping.pay</groupId>
            <artifactId>pay-promo-display-api</artifactId>
            <version>0.2.24</version>
        </dependency>
        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>fastjson</artifactId>
            <version>1.2.83_noneautotype</version>
        </dependency>
        <dependency>
            <groupId>com.dianping.poi</groupId>
            <artifactId>poi-relation-service-api</artifactId>
            <version>1.1.10</version>
        </dependency>
        <!--日志中心-->
        <dependency>
            <groupId>com.meituan.log</groupId>
            <artifactId>scribe-log4j2</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>org.slf4j</groupId>
                    <artifactId>slf4j-log4j12</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.meituan.inf</groupId>
            <artifactId>xmd-common-log4j2</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>org.apache.logging.log4j</groupId>
                    <artifactId>log4j-api</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.apache.logging.log4j</groupId>
                    <artifactId>log4j-1.2-api</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>org.apache.httpcomponents</groupId>
            <artifactId>httpclient</artifactId>
            <version>4.5.6</version>
        </dependency>
        <dependency>
            <groupId>org.apache.httpcomponents</groupId>
            <artifactId>httpcore</artifactId>
            <version>4.4.10</version>
        </dependency>
        <dependency>
            <groupId>com.meituan.servicecatalog</groupId>
            <artifactId>api-annotations</artifactId>
            <version>1.0.7</version>
        </dependency>
        <dependency>
            <groupId>com.dianping.gmkt</groupId>
            <artifactId>gmkt-data-base-api</artifactId>
            <version>1.2.15</version>
            <exclusions>
                <exclusion>
                    <groupId>com.thoughtworks.xstream</groupId>
                    <artifactId>xstream</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.dianping.gmkt</groupId>
            <artifactId>gmkt-base-api</artifactId>
            <version>1.3.1</version>
        </dependency>
        <dependency>
            <groupId>com.dianping.gmkt</groupId>
            <artifactId>gmkt-common</artifactId>
            <version>1.0.7</version>
        </dependency>
        <dependency>
            <groupId>com.dianping</groupId>
            <artifactId>gmkt-coupon-common-api</artifactId>
            <version>1.5.51</version>
        </dependency>
        <dependency>
            <groupId>com.dianping</groupId>
            <artifactId>shop-api</artifactId>
            <version>0.4.4</version>
        </dependency>
        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
            <version>1.16.22</version>
            <scope>provided</scope>
        </dependency>
        <dependency>
            <groupId>com.dianping.dp</groupId>
            <artifactId>gm-bonus-exposure-api</artifactId>
            <version>0.1.12</version>
        </dependency>
        <dependency>
            <groupId>com.sankuai.oceanus.http</groupId>
            <artifactId>oceanus-http</artifactId>
        </dependency>
        <dependency>
            <groupId>com.sankuai.mpmkt.coupon</groupId>
            <artifactId>mkt-coupon-execute-api</artifactId>
            <version>0.0.68</version>
        </dependency>
        <dependency>
            <groupId>com.sankuai.mpmkt.activity</groupId>
            <artifactId>mkt-activity-api</artifactId>
            <version>0.1.23</version>
            <classifier>jdk17</classifier>
            <exclusions>
                <exclusion>
                    <groupId>*</groupId>
                    <artifactId>*</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.dianping.tgc</groupId>
            <artifactId>tgc-entity</artifactId>
            <version>0.2.95</version>
        </dependency>
        <dependency>
            <groupId>com.dianping.gmm</groupId>
            <artifactId>gmm-marking-flag-api</artifactId>
            <version>1.0.3</version>
        </dependency>
        <dependency>
            <groupId>com.dianping.dpsf</groupId>
            <artifactId>dpsf-net</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>org.xerial.snappy</groupId>
                    <artifactId>snappy-java</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.dianping.lion</groupId>
                    <artifactId>lion-common</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.javassist</groupId>
                    <artifactId>javassist</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>javax.servlet</groupId>
                    <artifactId>servlet-api</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.fasterxml.jackson.module</groupId>
                    <artifactId>jackson-module-jsonSchema</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.sankuai.nibmktproxy</groupId>
            <artifactId>query-client</artifactId>
            <version>${promotion-query-client.version}</version>
            <exclusions>
                <exclusion>
                    <groupId>com.meituan.mdp.boot</groupId>
                    <artifactId>mdp-boot-starter</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.dianping.deal</groupId>
            <artifactId>deal-voucher-query-api</artifactId>
            <version>1.0.5</version>
        </dependency>
        <dependency>
            <groupId>com.dianping</groupId>
            <artifactId>ts-settle-common-api</artifactId>
            <version>0.1.3</version>
            <exclusions>
                <exclusion>
                    <groupId>com.dianping.swallow</groupId>
                    <artifactId>swallow-common</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.dianping.deal</groupId>
            <artifactId>deal-publish-category-api</artifactId>
            <version>1.1.3</version>
        </dependency>
        <dependency>
            <groupId>com.dianping.gmkt</groupId>
            <artifactId>gmkt-scene-engine-api</artifactId>
            <version>2.0.8</version>
        </dependency>
        <dependency>
            <groupId>com.dianping.poi</groupId>
            <artifactId>poi-shopcateprop-api</artifactId>
            <version>0.4.1</version>
        </dependency>
        <dependency>
            <groupId>org.aspectj</groupId>
            <artifactId>aspectjweaver</artifactId>
            <version>1.8.10</version>
        </dependency>
        <dependency>
            <groupId>org.aspectj</groupId>
            <artifactId>aspectjrt</artifactId>
            <version>1.8.10</version>
        </dependency>
        <dependency>
            <groupId>com.dianping.tpfun</groupId>
            <artifactId>sku-aggregate-api</artifactId>
            <version>1.8.18</version>
            <exclusions>
                <exclusion>
                    <groupId>com.dianping.piccentercloud</groupId>
                    <artifactId>piccenter-display-api</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.alibaba</groupId>
                    <artifactId>fastjson</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.dianping.zebra</groupId>
            <artifactId>zebra-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.dianping.zebra</groupId>
            <artifactId>zebra-ds-monitor-client</artifactId>
        </dependency>
        <dependency>
            <groupId>com.sankuai.dealuser</groupId>
            <artifactId>price-display-api</artifactId>
            <version>0.0.32</version>
            <exclusions>
                <exclusion>
                    <groupId>com.meituan.mdp.boot</groupId>
                    <artifactId>mdp-boot-starter</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.sankuai.mpmkt.coupon</groupId>
            <artifactId>mkt-coupon-issue-core-api</artifactId>
            <version>0.1.8</version>
        </dependency>
        <dependency>
            <groupId>com.sankuai.mpmctbrand</groupId>
            <artifactId>mpmctbrand-brandc-thrift</artifactId>
            <version>0.0.34</version>
        </dependency>
        <dependency>
            <groupId>com.sankuai.mpmctmvacommon</groupId>
            <artifactId>mpmctmvacommon-resource</artifactId>
            <version>0.0.1</version>
        </dependency>
        <dependency>
            <groupId>com.meituan.mdp.boot</groupId>
            <artifactId>mdp-boot-starter-thrift</artifactId>
            <version>1.7.13</version>
            <exclusions>
                <exclusion>
                    <groupId>com.meituan.service.mobile</groupId>
                    <artifactId>mtthrift</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.sankuai.sinai</groupId>
            <artifactId>sinai-api</artifactId>
            <version>1.0.6-jdk7</version>
        </dependency>
        <dependency>
            <groupId>com.sankuai.meituan</groupId>
            <artifactId>mtconfig-client</artifactId>
        </dependency>
        <dependency>
            <groupId>com.dianping.lion</groupId>
            <artifactId>lion-client</artifactId>
        </dependency>
        <dependency>
            <groupId>com.dianping.tssettle</groupId>
            <artifactId>zsts-commission-api</artifactId>
            <version>2.2.5</version>
            <exclusions>
                <exclusion>
                    <groupId>org.javassist</groupId>
                    <artifactId>javassist</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.hibernate.validator</groupId>
                    <artifactId>hibernate-validator</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.sankuai.pay</groupId>
            <artifactId>pay-access-client</artifactId>
            <version>1.3.59</version>
        </dependency>
        <dependency>
            <groupId>com.sankuai.wpt.user.merge</groupId>
            <artifactId>user-merge-query-api</artifactId>
            <version>1.0.1</version>
        </dependency>
        <dependency>
            <groupId>com.meituan.pay</groupId>
            <artifactId>payCouponSdk</artifactId>
            <version>1.12.1-RELEASE</version>
        </dependency>
        <dependency>
            <groupId>com.sankuai.general.product</groupId>
            <artifactId>client</artifactId>
            <version>1.0.1</version>
        </dependency>
        <dependency>
            <groupId>com.sankuai.inf.leaf</groupId>
            <artifactId>leaf-idl</artifactId>
        </dependency>
        <dependency>
            <groupId>com.sankuai.mpmkt.coupon</groupId>
            <artifactId>mkt-coupon-search-api</artifactId>
            <version>0.0.81</version>
        </dependency>
        <dependency>
            <groupId>org.javassist</groupId>
            <artifactId>javassist</artifactId>
            <version>3.20.0-GA</version>
        </dependency>
        <dependency>
            <groupId>io.netty</groupId>
            <artifactId>netty-all</artifactId>
        </dependency>
        <dependency>
            <groupId>com.meituan.travel.pandora.api</groupId>
            <artifactId>noah-thrift</artifactId>
            <version>2.2.6</version>
            <exclusions>
                <exclusion>
                    <groupId>*</groupId>
                    <artifactId>*</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.dianping.poi</groupId>
            <artifactId>poi-gis-api</artifactId>
            <version>0.4.27</version>
            <exclusions>
                <exclusion>
                    <groupId>*</groupId>
                    <artifactId>*</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.sankuai.mpmkt.coupon</groupId>
            <artifactId>mkt-coupon-issue-octo-api</artifactId>
            <version>0.1.0</version>
        </dependency>
        <dependency>
            <groupId>com.sankuai.mpmkt.coupon</groupId>
            <artifactId>mkt-coupon-group-api</artifactId>
            <version>0.0.62</version>
        </dependency>
        <dependency>
            <groupId>com.github.ben-manes.caffeine</groupId>
            <artifactId>caffeine</artifactId>
            <version>2.9.3</version>
        </dependency>
        <dependency>
            <groupId>com.esotericsoftware</groupId>
            <artifactId>kryo</artifactId>
            <version>5.3.0</version>
        </dependency>
        <dependency>
            <groupId>com.dianping</groupId>
            <artifactId>gmkt-coupon-log</artifactId>
            <version>0.0.23</version>
        </dependency>
        <dependency>
            <groupId>com.meituan.service.user</groupId>
            <artifactId>validate-token-api</artifactId>
            <version>1.0.6</version>
        </dependency>
        <dependency>
            <groupId>com.sankuai.nib.mkt</groupId>
            <artifactId>promotion-api</artifactId>
            <version>${promotion-api.version}</version>
        </dependency>
        <dependency>
            <groupId>com.sankuai.nib.mkt</groupId>
            <artifactId>magic-member-base</artifactId>
            <version>1.0.4</version>
        </dependency>
        <dependency>
            <groupId>com.sankuai.ia</groupId>
            <artifactId>phx-utils</artifactId>
            <version>3.3.12</version>
            <exclusions>
                <exclusion>
                    <groupId>com.amazonaws</groupId>
                    <artifactId>mss-java-sdk-s3</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.jruby.joni</groupId>
                    <artifactId>joni</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.sankuai.wpt.user.retrieve</groupId>
            <artifactId>retrieve-api</artifactId>
            <version>1.2.23</version>
        </dependency>
        <dependency>
            <groupId>com.sankuai.order.route</groupId>
            <artifactId>stenv-routing</artifactId>
            <version>1.0.3</version>
        </dependency>
        <dependency>
            <groupId>com.sankuai.mpmkt.coupon</groupId>
            <artifactId>mkt-coupon-query-api</artifactId>
            <version>0.1.8</version>
            <exclusions>
                <exclusion>
                    <groupId>com.alibaba</groupId>
                    <artifactId>fastjson</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.sankuai.nib.mkt</groupId>
            <artifactId>common-base</artifactId>
            <version>${promotion-common-base.version}</version>
            <exclusions>
                <exclusion>
                    <groupId>com.sankuai.nib.mkt</groupId>
                    <artifactId>config-standardize-api</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.sankuai.meituan.waimai</groupId>
            <artifactId>waimai-thrift-tools</artifactId>
            <version>1.9.6.0.9</version>
        </dependency>
        <dependency>
            <groupId>com.sankuai.nibpt</groupId>
            <artifactId>nibpt-union-log</artifactId>
            <version>0.0.3</version>
        </dependency>
        <dependency>
            <groupId>com.sankuai.nibpt</groupId>
            <artifactId>nibpt-transparent-validator</artifactId>
            <version>1.0.7</version>
        </dependency>
        <dependency>
            <groupId>com.sankuai.nib.mkt</groupId>
            <artifactId>magic-member-degrade</artifactId>
            <version>1.0.8</version>
        </dependency>
        <dependency>
            <groupId>com.sankuai.octo</groupId>
            <artifactId>mns-invoker</artifactId>
        </dependency>
        <dependency>
            <groupId>com.sankuai.octo</groupId>
            <artifactId>idl-common</artifactId>
        </dependency>
        <dependency>
            <groupId>com.sankuai.octo</groupId>
            <artifactId>idl-sgagent</artifactId>
        </dependency>
        <dependency>
            <groupId>com.sankuai.guardian</groupId>
            <artifactId>sig-botdefender-core</artifactId>
            <version>1.1.20</version>
        </dependency>
        <dependency>
            <groupId>com.meituan.mars.framework</groupId>
            <artifactId>mars-common-domain</artifactId>
            <version>3.25.2</version>
            <exclusions>
                <exclusion>
                    <groupId>org.hibernate.validator</groupId>
                    <artifactId>hibernate-validator</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.fasterxml.jackson.datatype</groupId>
            <artifactId>jackson-datatype-guava</artifactId>
        </dependency>
        <dependency>
            <groupId>com.sankuai.douhu</groupId>
            <artifactId>douhu-absdk</artifactId>
            <version>2.10.5</version>
        </dependency>
    </dependencies>
    <build>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <configuration>
                    <source>8</source>
                    <target>8</target>
                </configuration>
            </plugin>
        </plugins>
    </build>
</project>
